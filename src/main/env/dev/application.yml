server:
  port: 18087
  servlet:
    context-path: /lake

spring:
  resources:
    static-locations: classpath:/
  datasource:
    url: ************************************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: Mysql@1234
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      keep-alive: true
      time-between-eviction-runs-millis: 10000
      validation-query-timeout: 3
      min-evictable-idle-time-millis: 30000
      max-evictable-idle-time-millis: 7200000
      validation-query: select 1
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      stat-view-servlet:
        enabled: false
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  quartz:
    job-store-type: MEMORY
    properties:
      org:
        quartz:
          scheduler:
            instanceName: dataScanScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.simpl.RAMJobStore
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            #线程数 一个任务使用一个线程
            threadCount: 25
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  redis:
    # 用第0号数据库
    database: 0
    host: ***********
    #ipv6配置
    #host: 2002:a0a:112:c:250:56ff:fe98:6428
    port: 6379
    password: redis@1234
    # 连接超时时长（毫秒）
    timeout: 10000
    #读取数据超时时间，单位毫秒
    readTimeout: 3000
    #redis情报信息缓存限量
    infoLimit: 10000
    pool:
      # 连接池最大连接数（使用负值表示没有限制）
      max-active: 1000
      # 连接池最大阻塞等待时间（使用负值表示没有限制）
      max-wait: -1
      # 连接池中的最大空闲连接
      max-idle: 10
      # 连接池中的最小空闲连接
      min-idle: 5

clickhouse:
  cluster_nx_copy: cluster_nx_copy
  cluster_1x_copy: cluster_1x_copy
  cluster_2x_copy: cluster_2x_copy
  shard_for_nx_copy: shard_for_nx_copy
  shard_for_1x_copy: shard_for_1x_copy
  shard_for_2x_copy: shard_for_2x_copy

# zeppelin服务器IP和端口地址
zeppelin-server: http://***********:8081
# zeppelin服务器IP和端口地址(用于服务器之间调用)
zeppelin-internal-server: http://***********:8081
# 本地jar目录
zeppelin-local-jar-path: /home/<USER>/zeppelin/zeppelin-0.10.1-bin-all/local-jar

ueba:
  gpl-url: http://***********
  base-url: http://***********:18086
  etl-url: http://***********:18085
  ueba-url: http://***********:18080

data:
  collect:
    #[D] 将以下多个连接地址的ip端口都改成实际的 data-mete-collector 服务地址
    url: http://localhost:8004/meta-collector

upload:
  kbs:
    # 上传KBS认证路径
    #    filePath: /home/<USER>/upload-file/
    #[E] 修改实际的保存认证文件的路基 与 data-mete-collector 配置的 es.kbs.authPath保持一致
    filePath: /Users/<USER>/upload/

#分页pageHelper
pagehelper:
  #配置使用哪种数据库语言，不配置的话pageHelper也会自动检测
  helper-dialect: mysql
  #配置分页参数合理化功能，默认是false。 启用合理化时，如果pageNum<1会查询第一页，如果pageNum>总页数会查询最后一页； 禁用合理化时，如果pageNum<1或pageNum>总页数会返回空数据。
  reasonable: true
  #支持通过Mapper接口参数来传递分页参数，默认值false，分页插件会从查询方法的参数值中，自动根据上面 params 配置的字段中取值，查找到合适的值时就会自动分页。
  support-methods-arguments: true

# 首页统计任务执行配置
index-task-schedule: 0 0 1 * * ?
monitor:
  delete: 0 0/1 * * * ?

file:
  uploadPath: /home/<USER>/api/data-lake-webapi-1.0.0/upload

# MA python
ma:
  python-path: /Users/<USER>/anaconda3/envs/trade/bin/python
  schedule: 0 30 2 * * ?

# AI配置,python3和run.py脚本路径
ai:
  download-url: http://************:8082/lake/ai/version/download/
  python3-path: /Users/<USER>/anaconda3/envs/trade/bin/python
  script-path:  /home/<USER>/bin/services/notebook/run.py
  project-create: /project/create
  project-delete: /project/delete
  project-status: /project/status
  project-update: /project/update
  notebook-create: /notebook/create
  notebook-delete: /notebook/delete
  notebook-status: /notebook/status
  serving-create: /serving/create
  serving-delete: /serving/delete
  serving-status: /serving/status
  serving-log: /download/pod/log
  serving-restart: /deployment/restart
  serving-url: http://************:8081/

# 元数据表维护分计算统计
maintenance-score-schedule: 0 0 9 * * ?
# 元数据表数据量和存储计算统计
table-count-storage-schedule: 0 0 6,13,22 * * ?

# 数澜数据源
shulan:
  jdbc-url: ****************************************************************************************************************************************
  username: root
  password: 3hd2eV-^MNSrtQ06