/**
 * <p><strong>李茂杰 2020-10-19 数据探索新增实时查询功能</strong></p>
 */
package com.idss.datalake.etl.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.util.DateUtil;
import com.idss.datalake.etl.entity.EtlTaskDayStatistics;
import com.idss.datalake.etl.entity.ExploreQueryHistory;
import com.idss.datalake.etl.service.IDataexploreService;
import com.idss.datalake.etl.service.IEtlTaskDayStatisticsService;
import com.idss.datalake.etl.service.IExploreQueryHistoryService;
import com.idss.radar.datasource.DatasourceType;
import com.idss.radar.util.UmsUtils;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>数据湖相关接口</p>
 * @see
 * @since 2022-11-08
 */
@RestController
@RequestMapping("/api/lake")
public class LakeController {

    /**
     * 时间过滤字段
     */
    private static final String DEFAULT_ERROR_TYPE_FIELD = "异常类型";

    /**
     * 原始日存放字段
     */
    private static final String DEFAULT_ERROR_EXCEPTION_FIELD = "异常原因";

    private static final Logger LOGGER = LoggerFactory.getLogger(LakeController.class);

    @Autowired
    private IDataexploreService dataexploreService;

    @Autowired
    private IExploreQueryHistoryService exploreQueryHistoryService;
    @Resource
    private IEtlTaskDayStatisticsService taskDayStatisticsService;

    @ApiOperation(value = "租户首页统计-可搜索资源数&最近使用时间")
    @GetMapping("/explore/statistic")
    public ResultBean chtables() {
        ResultBean resultBean = new ResultBean();
        Map<String, Object> resultMap = new HashMap<>();
        try {
            Map<String, Object> params = new HashMap<>();
            // 查询总数量(ch + es)
            Map<String, Object> map = dataexploreService.queryCHTables(params, 0, 0);
            List<Map<String, String>> chNames = (List<Map<String, String>>) map.get("names");
            Map<String, Object> esIndexs = dataexploreService.queryESIndexs(params, 0, 0);
            List<Map<String, String>> esNames = (List<Map<String, String>>) esIndexs.get("names");
            resultMap.put("exploreCount", chNames.size() + esNames.size());

            // 最近使用时间
            int tenantId = UmsUtils.getUVO().getTenantId();
            DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenantId);
            ExploreQueryHistory queryHistory = exploreQueryHistoryService.getOne(new QueryWrapper<ExploreQueryHistory>()
                    .eq("history_type", "explore").orderByDesc("create_time").last("limit 1"));
            String exploreLastTime = "";
            if (queryHistory != null) {
                Date date = DateUtil.LocalDateTime2Date(queryHistory.getCreateTime());
                exploreLastTime = DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss");
            }
            resultMap.put("exploreLastTime", exploreLastTime);
            resultBean.setMessage("查询成功");
            resultBean.setContent(resultMap);
        } catch (Exception e) {
            LOGGER.error("数据湖租户首页统计-可搜索资源数&最近使用时间失败：{}", e.getMessage(), e);
            resultBean.setStatus(Constant.STATUS_FAIL);
            resultBean.setMessage("查询失败");
        }
        return resultBean;
    }

    @ApiOperation(value = "租户首页统计-在线采集任务数")
    @GetMapping("/onlineTask")
    public ResultBean onlineTask() {
        ResultBean resultBean = new ResultBean();
        try {
            int tenantId = UmsUtils.getUVO().getTenantId();
            DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenantId);
            List<EtlTaskDayStatistics> list = taskDayStatisticsService.list(new QueryWrapper<EtlTaskDayStatistics>().eq("flag", 1)
                    .orderByAsc("create_time"));
            resultBean.setMessage("查询成功");
            resultBean.setContent(list);
        } catch (Exception e) {
            LOGGER.error("数据湖租户首页统计-在线采集任务数失败：{}", e.getMessage(), e);
            resultBean.setStatus(Constant.STATUS_FAIL);
            resultBean.setMessage("查询失败");
        }
        return resultBean;
    }

    @ApiOperation(value = "租户首页统计-用户使用情况")
    @PostMapping("/userUseInfoPage")
    public ResultBean userUseInfoPage(@RequestBody Map<String,Object> param) {
        ResultBean resultBean = new ResultBean();
        try {
            Integer pageNum = Integer.parseInt(param.get("pageNum").toString());
            Integer pageSize = Integer.parseInt(param.get("pageSize").toString());
            resultBean.setMessage("查询成功");
            resultBean.setContent(this.taskDayStatisticsService.getUserUseList(pageNum,pageSize));
        } catch (Exception e) {
            LOGGER.error("租户首页统计-用户使用情况失败：{}", e.getMessage(), e);
            resultBean.setStatus(Constant.STATUS_FAIL);
            resultBean.setMessage("查询失败");
        }
        return resultBean;
    }

    @ApiOperation(value = "租户首页统计-采集失败日志")
    @PostMapping("/flowErrorLog")
    public ResultBean flowErrorLog(@RequestBody Map<String,Object> param) {
        ResultBean resultBean = new ResultBean();
        try {
            String flowId = param.get("flowId").toString();
            Integer pageNum = Integer.parseInt(param.get("pageNum").toString());
            Integer pageSize = Integer.parseInt(param.get("pageSize").toString());
            resultBean.setMessage("查询成功");
            resultBean.setContent(this.taskDayStatisticsService.getFlowErrorList(flowId,pageNum,pageSize));
        } catch (Exception e) {
            LOGGER.error("租户首页统计-用户使用情况失败：{}", e.getMessage(), e);
            resultBean.setStatus(Constant.STATUS_FAIL);
            if(e.getMessage() != null && e.getMessage().contains("logmodule_flow_errlog")){
                resultBean.setMessage("采集错误日志任务未创建!");
            }else {
                resultBean.setMessage("查询失败");
            }
        }
        return resultBean;
    }

    @ApiOperation(value = "租户首页统计-采集失败日志下载", httpMethod = "POST")
    @GetMapping("/downloadFlowErrorLog/{flowId}")
    public void downloadFlowErrorLog(@PathVariable("flowId") String flowId, HttpServletRequest request, HttpServletResponse response) {
        this.taskDayStatisticsService.downloadErrorLog(flowId,request,response);
    }

}
