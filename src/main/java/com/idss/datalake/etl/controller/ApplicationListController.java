/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-09-04
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-09-04
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.etl.controller;


import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.etl.dto.ApplicationListDTO;
import com.idss.datalake.etl.manager.ApplicationListManager;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;

import static com.idss.datalake.common.constants.NConst.CONF_PATH;

/**
 * 应用列表
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/application/application-list")
public class ApplicationListController {
    private static final Logger logger = LoggerFactory.getLogger(ApplicationListController.class);
    @Autowired
    private ApplicationListManager applicationListManager;

    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody ApplicationListDTO dto) {
        try {
            applicationListManager.create(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public ResultBean delete(@RequestBody ApplicationListDTO dto) {
        try {
            applicationListManager.delete(dto.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Long id) {
        try {
            return ResultBean.success(applicationListManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "编辑")
    @PostMapping(value = "/edit")
    public ResultBean edit(@RequestBody ApplicationListDTO dto) {
        try {
            applicationListManager.edit(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询列表")
    @PostMapping(value = "/page")
    public ResultBean page(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(applicationListManager.page(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "导入")
    @PostMapping(value = "/importData")
    public ResultBean importData(@RequestBody ApplicationListDTO dto) {
        try {
            applicationListManager.importData(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @GetMapping("/download/template")
    public ResponseEntity<Resource> downloadTemplate() {
        String fileName = "dim_app_list.xlsx";
        Path path = Paths.get(CONF_PATH + "template").resolve(fileName);
        Resource resource;
        try {
            logger.info("Downloading template file: {}", path.toUri().getPath());
            resource = new UrlResource(path.toUri());
        } catch (MalformedURLException e) {
            logger.error("Failed to download template file: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
        if (resource.exists() || resource.isReadable()) {
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + resource.getFilename())
                    .body(resource);
        } else {
            logger.error("Failed to download template file: {}", path.toUri().getPath());
            return ResponseEntity.notFound().build();
        }
    }
}
