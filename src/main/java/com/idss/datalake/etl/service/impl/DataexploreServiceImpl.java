package com.idss.datalake.etl.service.impl;

import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.dialect.mysql.parser.MySqlStatementParser;
import com.alibaba.druid.sql.dialect.mysql.visitor.MySqlSchemaStatVisitor;
import com.alibaba.druid.stat.TableStat;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.etl.entity.UmsSysRoleChEs;
import com.idss.datalake.etl.mapper.DataexploreMapper;
import com.idss.datalake.etl.service.IDataexploreService;
import com.idss.datalake.etl.service.IUmsSysDatasourceConfigService;
import com.idss.datalake.etl.service.IUmsSysRoleChEsService;
import com.idss.radar.datasource.DatasourceType;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Copyright 2020 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2020/5/13 9:17
 * 类说明
 */
@Service
public class DataexploreServiceImpl implements IDataexploreService {

    @Autowired
    private DataexploreMapper dataexploreMapper;

    @Autowired
    private IUmsSysRoleChEsService sysRoleChEsService;

    @Autowired
    private IUmsSysDatasourceConfigService datasourceConfigService;

    @Override
    public Map<String, Object> queryCHTables(Map<String, Object> params, int pageNum, int pageSize) throws Exception {
        int tenantId = UmsUtils.getUVO().getTenantId();
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenantId);
        // 查询实时数仓表信息
        Map<String, Object> contentMap = new HashMap<String, Object>();
        List<Map<String, String>> names = new ArrayList<>();

        // 获取用户所属角色
        String roleId = getUserRole();
        // 获取角色对应的表权限
        List<UmsSysRoleChEs> roleCH = new ArrayList<>();
        if(StringUtils.isNotBlank(roleId)) {
            roleCH = sysRoleChEsService.list(new QueryWrapper<UmsSysRoleChEs>()
                    .eq("role_id", roleId)
                    .eq("type", "ch")
                    .eq("flag", Constant.STATUS_ENABLE));
        } else {
            roleCH = sysRoleChEsService.list(new QueryWrapper<UmsSysRoleChEs>()
                    .eq("type", "ch")
                    .eq("flag", Constant.STATUS_ENABLE));
        }
        if(!CollectionUtils.isEmpty(roleCH)) {
            /**
             * add by zhangpl v3.0.9 2021-04-27 从系统表再过滤一遍被删除的表
             */
            // 2021/6/15 modify by 李茂杰 数据库名无需通过配置获取
            String dataBase = datasourceConfigService.getSchemalName();
            if (dataBase.startsWith("ueba_")) {
                dataBase = dataBase.replace("ueba_", "");
            }
            DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE, tenantId);
            List<String> systemTables = dataexploreMapper.querySystemTables(dataBase);
            for (UmsSysRoleChEs chTable : roleCH) {
                if(systemTables.contains(chTable.getTableName())) {
                    Map<String, String> chTableMap = new HashMap<>();
                    chTableMap.put("name", chTable.getTableName());
                    names.add(chTableMap);
                }
            }
            //对一些表特殊处理
            if (systemTables.contains("security_log_alarm_history")) {
                Map<String, String> chTableMap = new HashMap<>();
                chTableMap.put("name", "security_log_alarm_history");
                names.add(chTableMap);
            }
        }
        contentMap.put("names", names);

        return contentMap;
    }

    @Override
    public Map<String, Object> queryESIndexs(Map<String, Object> params, int pageNum, int pageSize) {
        int tenantId = UmsUtils.getUVO().getTenantId();
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenantId);
        // 查询索引库索引信息
        Map<String, Object> contentMap = new HashMap<String, Object>();
        List<Map<String, String>> names = new ArrayList<>();

        // 获取用户所属角色
        String roleId = getUserRole();
        // 获取角色对应的表权限
        List<UmsSysRoleChEs> roleES = new ArrayList<>();
        if(StringUtils.isNotBlank(roleId)) {
            roleES = sysRoleChEsService.list(new QueryWrapper<UmsSysRoleChEs>()
                    .eq("role_id", roleId)
                    .eq("type", "es")
                    .eq("flag", Constant.STATUS_ENABLE));
        }
        if(!CollectionUtils.isEmpty(roleES)) {
            for (UmsSysRoleChEs esIndex : roleES) {
                Map<String, String> chTableMap = new HashMap<>();
                chTableMap.put("name", esIndex.getTableName());

                names.add(chTableMap);
            }
        }
        contentMap.put("names", names);

        return contentMap;
    }

    private String getUserRole() {
        UserValueObject userValueObject = UmsUtils.getEmptyUVO();
        String roleId = "";
        if(userValueObject != null) {
            roleId = userValueObject.getRoleId();
        }
        //roleId = "ff9c5c2046c14d87ac3a1204b7facff4";
        return roleId;
    }

}
