package com.idss.datalake.etl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.etl.dto.AssetDiskDTO;
import com.idss.datalake.etl.dto.AssetResourceDTO;
import com.idss.datalake.etl.dto.AssetTotalDTO;
import com.idss.datalake.etl.entity.EtlSource;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-10
 */
public interface IEtlSourceService extends IService<EtlSource> {


    /**
     * 查询资产目录
     *
     * @param dto
     * @return
     */
    Map<String, Object> queryAssetResource(AssetResourceDTO dto);

    /**
     * 查询总数据量
     *
     * @return
     */
    long queryDatasourceTotalAll(Map<String, Object> params, TbTenant tenant);

    /**
     * 查询总大小
     *
     * @return
     */
    long queryDatasourceLengthAll(Map<String, Object> params, TbTenant tenant);

    /**
     * 查询资产数据量
     *
     * @param params
     * @return
     */
    List<AssetTotalDTO> queryAssetTotal(Map<String, Object> params, TbTenant tenant);

    /**
     * 查询资产磁盘空间
     *
     * @return
     */
    List<AssetDiskDTO> queryAssetDiskTotal(TbTenant tenant);

}
