package com.idss.datalake.etl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.etl.dto.AssetPurposeAggDTO;
import com.idss.datalake.etl.dto.AssetResourceDTO;
import com.idss.datalake.etl.dto.DeviceTypeAggDTO;
import com.idss.datalake.etl.dto.TableAddDto;
import com.idss.datalake.etl.dto.TableFieldDto;
import com.idss.datalake.etl.entity.EtlAssetResource;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 资产目录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-01
 */
public interface IEtlAssetResourceService extends IService<EtlAssetResource> {

    AssetResourceDTO queryAssetResourceDetail(Long etlSourceId);

    List<DeviceTypeAggDTO> deviceTypeAgg(Map<String, Object> params);
    List<DeviceTypeAggDTO> deviceTypeAgg(Map<String, Object> params, TbTenant tenant);

    List<AssetPurposeAggDTO> assetPurposeAgg(TbTenant tenant);



    List<TableFieldDto> tableFieldDetail(String database,String tableName);


    List<TableAddDto> tableAddByDay(Long flowId);


    Long tableYesterdayAddRecord(Long flowId);


    Long tableYesterdayAddSize(Long flowId);


    Long tableYesterdaySize(Long flowId);


    Long tableYesterdayRecord(Long flowId);


    Long tableTotalSize(Long flowId);


    Long tableTotalRecord(Long flowId);


    String getTableNameByFlowId(Long flowId);
}
