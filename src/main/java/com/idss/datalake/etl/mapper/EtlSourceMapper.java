package com.idss.datalake.etl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.etl.dto.AssetDiskDTO;
import com.idss.datalake.etl.dto.AssetTotalDTO;
import com.idss.datalake.etl.entity.EtlSource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据源配置Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-10
 */
@Mapper
public interface EtlSourceMapper extends BaseMapper<EtlSource> {

    /**
     * 查询资产目录
     *
     * @param params
     * @return
     */
    List<Map<String, Object>> queryAssetResource(Map<String, Object> params);

    int queryTotalAssetResource(Map<String, Object> params);

    /**
     * 查询采集条数和占用空间
     *
     * @param params
     * @return
     */
    List<Map<String, Object>> queryDatasourceTotal(Map<String, Object> params);

    /**
     * 查询总数据量
     *
     * @return
     */
    Long queryDatasourceTotalAll(Map<String, Object> params);

    /**
     * 查询资产数据量
     *
     * @param params
     * @return
     */
    List<AssetTotalDTO> queryAssetTotal(Map<String, Object> params);

    /**
     * 查询资产磁盘空间
     *
     * @return
     */
    List<AssetDiskDTO> queryAssetDiskTotal();

    long queryDatasourceLengthAll(Map<String, Object> params);

}
