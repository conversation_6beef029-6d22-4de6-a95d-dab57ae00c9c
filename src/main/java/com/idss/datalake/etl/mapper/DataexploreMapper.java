package com.idss.datalake.etl.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Copyright 2020 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2020/5/13 9:49
 * 类说明
 */
@Mapper
public interface DataexploreMapper {
    List<String> querySystemTables(@Param("database") String database);


}
