/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-09-04
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-09-04
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.etl.dto;

import com.idss.datalake.etl.entity.ApplicationList;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @description <p>应用列表 dto类</p>
 * @since 2024-09-04
 */
@Data
public class ApplicationListDTO extends ApplicationList {
    private List<Long> ids;

    /**
     * 上传的excel文件位置
     */
    private String filePath;

}