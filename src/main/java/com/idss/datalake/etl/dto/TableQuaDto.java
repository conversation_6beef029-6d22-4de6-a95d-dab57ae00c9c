/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/9/15
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/9/15
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.etl.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorResult;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/9/15
 */
@Data
public class TableQuaDto {
    private String score;
    private String yesterdayScore;
    private List<String> ruleTypes;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkTime;
    private List<QuaMonitorResult> resultList;
}
