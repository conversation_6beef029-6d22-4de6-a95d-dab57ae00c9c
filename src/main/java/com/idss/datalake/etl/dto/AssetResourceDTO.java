package com.idss.datalake.etl.dto;

import com.idss.datalake.etl.entity.EtlAssetResource;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description : <p>资产目录</p>
 * @see：
 * @since 2022/11/1
 */
@Data
public class AssetResourceDTO extends EtlAssetResource {

    private Integer pageNum;
    private Integer pageSize;

    /**
     * 采集名称(资产名称)
     */
    private String sourceName;

    /**
     * 采集ids
     */
    private List<Long> etlSourceIds;

    /**
     * 资产类型名称，即原设备类型名称
     */
    private String deviceType;
    /**
     * 资产类型编码，即原设备类型编码
     */
    private String deviceCode;

    /**
     * 资产用途名称
     */
    private String assetPurpose;
}
