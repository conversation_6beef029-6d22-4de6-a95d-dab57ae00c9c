package com.idss.datalake.etl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* <p>
    * 
    * </p>
*
* <AUTHOR>
* @since 2020-06-30
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    public class UmsSysRoleChEs implements Serializable {

    private static final long serialVersionUID = 1L;

            /**
            * id
            */
            @TableId(value = "id", type = IdType.AUTO)
    private Long id;

            /**
            * 类型：es,ch
            */
    private String type;

            /**
            * 角色id
            */
    private String roleId;

            /**
            * 表名
            */
    private String tableName;

            /**
            * 查询条件参数
            */
    private String queryCondition;

            /**
            * 创建者
            */
    private String createUser;

            /**
            * 创建日期
            */
    private LocalDateTime createDate;

            /**
            * 更新者
            */
    private String updateUser;

            /**
            * 更新日期
            */
    private LocalDateTime updateDate;

            /**
            * 有效标识
            */
    private String flag;


}
