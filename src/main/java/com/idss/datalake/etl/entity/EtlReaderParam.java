package com.idss.datalake.etl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 数据源配置数据实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-10
 */
@Data
@Accessors(chain = true)
@TableName("etl_reader_param")
@NoArgsConstructor
@AllArgsConstructor
public class EtlReaderParam {

    private static final long serialVersionUID = 1L;

    /**
     * 数据源配置ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据源ID
     */
    private String sourceId;

    /**
     * 数据源配置项key
     */
    private String configKey;

    /**
     * 数据源配置项值
     */
    private String configValue;

    /**
     * 创建者
     */
    private String createUser;

    /**
     * 创建日期
     */
    private LocalDateTime createDate;

    /**
     * 更新者
     */
    private String updateUser;

    /**
     * 更新日期
     */
    private LocalDateTime updateDate;


}
