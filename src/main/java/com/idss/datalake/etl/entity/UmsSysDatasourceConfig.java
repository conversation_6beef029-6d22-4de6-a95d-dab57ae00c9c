package com.idss.datalake.etl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 数据源配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ums_sys_datasource_config")
public class UmsSysDatasourceConfig implements Serializable {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	/**
	 * 配置模块 dashboard-仪表盘 schedule-模型调度
	 */
	private String moudleName;

	/**
	 * 数据源类型
	 */
	private String datasourceType;

	/**
	 * IP地址
	 */
	private String ip;

	/**
	 * 数据库名
	 */
	private String dbName;

	/**
	 * 端口
	 */
	private String port;

	/**
	 * 用户名
	 */
	private String userName;

	/**
	 * 密码
	 */
	private String password;

	/**
	 * 展示名
	 */
	private String showName;

	/**
	 * 超时时间
	 */
	private Integer timeOut;

	/**
	 * 是否加密 1-加密 0-不加密
	 */
	private String encrypt;

	private String status;

	private Integer copyCnt;

	/**
	 * 创建账号
	 */
	private String createUser;

	private String dbUrl;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

	/**
	 * 更新账号
	 */
	private String updateUser;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

}
