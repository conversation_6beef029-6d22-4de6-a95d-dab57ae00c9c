package com.idss.datalake.etl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2020/9/2
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ExploreQueryHistory {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String tableName;
    private String quickTime;
    private String queryCondition;
    private String writeType;
    private String startTime;
    private String endTime;
    private String extraCondition;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String queryHash;

    /**
     * explore,gpl
     */
    private String historyType;

    /**
     * 查询人员
     */
    private String createUser;
}
