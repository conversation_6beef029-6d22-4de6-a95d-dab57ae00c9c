package com.idss.datalake.etl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
* <p>
    * 
    * </p>
*
* <AUTHOR>
* @since 2020-03-26
*/
    @Data
    @Accessors(chain = true)
    @TableName("etl_writer_es_config")
    public class EtlWriterEsConfig {

    private static final long serialVersionUID = 1L;

            /**
            * 主键
            */
            @TableId(value = "id", type = IdType.AUTO)
    private Long id;

            /**
            * 数据源ID
            */
    private Long sourceId;

            /**
            * 正则表达式
            */
    private String regValue;

            /**
            * 时间格式
            */
    private String timeFormat;

            /**
            * 索引名
            */
    private String indexName;

            /**
            * 集群名称
            */
    private String clusterDisplayName;

            /**
            * 分片数
            */
    private Integer shardNum;

            /**
            * 副本数
            */
    private Integer replicaNum;

            /**
            * 最大容量
            */
    private Integer maxCapacity;

            /**
            * 最大保留天数
            */
    private Integer maxKeepDays;

            /**
            * 映射对应策略
            */
    private String strategy;

            /**
            * 最后一次备份到的索引名称
            */
    private String lastBackupIndexName;

            /**
            * 归档失败的索引名称
            */
    private String failedIndexName;

            /**
            * 缩容周期
            */
    private Integer cycleScop;

            /**
            * 状态（0 表示禁用，1表示启用）
            */
    private Integer status;


}
