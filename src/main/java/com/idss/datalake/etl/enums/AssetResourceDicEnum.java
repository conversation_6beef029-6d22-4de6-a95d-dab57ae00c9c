package com.idss.datalake.etl.enums;

/**
 * <AUTHOR>
 * @description : <p>资产目录-字段字典枚举类</p>
 * @see：
 * @since 2022/11/1
 */
public enum AssetResourceDicEnum {
    ASSET_PURPOSE("ASSET_RESOURCE_PURPOSE", "资产用途"),
    CLASSIFICATION("ASSET_RESOURCE_CLASSIFICATION", "敏感分类"),
    ASSET_LEVEL("ASSET_RESOURCE_LEVEL", "敏感分级");

    private String type;
    private String name;

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    AssetResourceDicEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getName(String type) {
        for (AssetResourceDicEnum dicEnum : AssetResourceDicEnum.values()) {
            if (dicEnum.getType().equals(type)) {
                return dicEnum.name;
            }
        }
        return "";
    }
}
