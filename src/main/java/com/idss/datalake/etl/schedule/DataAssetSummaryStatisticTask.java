package com.idss.datalake.etl.schedule;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.datashare.tenant.entity.TenantDTO;
import com.idss.datalake.datashare.tenant.service.ITbTenantService;
import com.idss.datalake.etl.dto.AssetDiskDTO;
import com.idss.datalake.etl.dto.AssetPurposeAggDTO;
import com.idss.datalake.etl.dto.AssetTotalDTO;
import com.idss.datalake.etl.dto.DeviceTypeAggDTO;
import com.idss.datalake.etl.entity.EtlAssetSummary;
import com.idss.datalake.etl.entity.EtlSource;
import com.idss.datalake.etl.service.IEtlAssetResourceService;
import com.idss.datalake.etl.service.IEtlAssetSummaryService;
import com.idss.datalake.etl.service.IEtlSourceService;
import com.idss.datalake.etl.service.IUmsSysDatasourceConfigService;
import com.idss.radar.datasource.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @description <p>安全数据管理-数据资产汇总统计任务</p>
 * @see
 * @since 2022-11-03
 */
@Component
@Slf4j
public class DataAssetSummaryStatisticTask {
    @Autowired
    private IEtlAssetResourceService iEtlAssetResourceService;
    @Autowired
    private IEtlSourceService etlSourceService;
    @Autowired
    private ITbTenantService tenantService;
    @Autowired
    private IUmsSysDatasourceConfigService datasourceConfigService;
    @Autowired
    private IEtlAssetSummaryService summaryService;


    /**
     * 定时任务数据统计
     */
    @Scheduled(initialDelay = 5000, fixedDelay = 3600000)
    public void fixedDelayJob() {
        log.info("======= 开始数据资产汇总统计任务 =======");
        try {
            // 按照租户轮询统计
            DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
            List<TbTenant> tenants = tenantService.list(new QueryWrapper<TbTenant>()
                    .eq("DEL_FLAG", "0")
                    .eq("ACCOUNT_TYPE", "1")
                    .eq("RESOURCE_STATUS", "2"));
            for (TbTenant tenant : tenants) {
                //datasourceConfigService.initDatasource(tenant.getTenantId().intValue());
                doStatistics(tenant);
            }
            log.info("======= 结束数据资产汇总统计任务 =======");
        } catch (Exception e) {
            log.error("首页数据统计任务失败: {}", e.getMessage(), e);
        } finally {
            DatasourceType.clearDataBaseType();
        }
    }

    /**
     * 执行统计
     *
     * @param tenant
     */
    private void doStatistics(TbTenant tenant) {
        try {
            DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenant.getTenantId().intValue());
            // 资产总数
            int assetTotal = etlSourceService.count(new QueryWrapper<EtlSource>().eq("status", "1"));

            // 资产用途占比
            List<AssetPurposeAggDTO> assetPurposeAggDTOS = iEtlAssetResourceService.assetPurposeAgg(tenant);
            if (CollectionUtils.isNotEmpty(assetPurposeAggDTOS)) {
                long total = assetPurposeAggDTOS.stream().mapToLong(x -> x.getTotal()).sum();
                for (AssetPurposeAggDTO dto : assetPurposeAggDTOS) {
                    dto.setPercentage(calcPercent(dto.getTotal(), total));
                }
            }

            // 资产类型占比
            List<DeviceTypeAggDTO> deviceTypeAggDTOS = iEtlAssetResourceService.deviceTypeAgg(new HashMap<>(),tenant);
            if (CollectionUtils.isNotEmpty(deviceTypeAggDTOS)) {
                long total = deviceTypeAggDTOS.stream().mapToLong(x -> x.getTotal()).sum();
                for (DeviceTypeAggDTO dto : deviceTypeAggDTOS) {
                    dto.setPercentage(calcPercent(dto.getTotal(), total));
                }
            }

            Map<String, Object> params = new HashMap<>();
            // 数据累计总数量
            long dataTotalAll = etlSourceService.queryDatasourceTotalAll(params, tenant);

            // 占用磁盘总空间
            long diskAll = etlSourceService.queryDatasourceLengthAll(params, tenant);

            // 近7日新增数据量
            String dayBefore = DateFormatUtils.format(DateUtils.addDays(new Date(), -7), "yyyy-MM-dd 00:00:00");
            params.put("createDate", dayBefore);
            long last7DaysDataTotal = etlSourceService.queryDatasourceTotalAll(params, tenant);

            //  近7日新增使用磁盘空间
            long last7DaysDisk = etlSourceService.queryDatasourceLengthAll(params, tenant);

            // 资产数据量
            params.clear();
            List<AssetTotalDTO> assetList = etlSourceService.queryAssetTotal(params, tenant);

            // 近7日新增资产数量排行
            params.put("createDate", dayBefore);
            params.put("orderField", "b.in_count");
            params.put("orderType", "DESC");
            List<AssetTotalDTO> last7DaysAsset = etlSourceService.queryAssetTotal(params, tenant);

            // 资产磁盘空间占比
            List<AssetDiskDTO> diskTotalList = etlSourceService.queryAssetDiskTotal(tenant);
            if (CollectionUtils.isNotEmpty(diskTotalList)) {
                long total = diskTotalList.stream().mapToLong(x -> x.getDiskTotal()).sum();
                for (AssetDiskDTO dto : diskTotalList) {
                    dto.setPercentage(calcPercent(dto.getDiskTotal(), total));
                }
            }

            EtlAssetSummary summary = new EtlAssetSummary();
            summary.setAssetTotal(assetTotal);
            summary.setAssetPurposeJson(JSON.toJSONString(assetPurposeAggDTOS));
            summary.setDeviceTypeJson(JSON.toJSONString(deviceTypeAggDTOS));
            summary.setDataTotalAll(dataTotalAll);
            summary.setDiskAll(diskAll);
            summary.setLast7daysDataTotal(last7DaysDataTotal);
            summary.setLast7daysDisk(last7DaysDisk);
            summary.setAssetListJson(JSON.toJSONString(assetList));
            summary.setLast7daysAssetJson(JSON.toJSONString(last7DaysAsset));
            summary.setDiskTotalListJson(JSON.toJSONString(diskTotalList));
            summary.setCreateTime(LocalDateTime.now());
            DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenant.getTenantId().intValue());
            summaryService.update(new UpdateWrapper<EtlAssetSummary>().set("del_flag", 1).eq("del_flag", 0));
            summaryService.save(summary);
            log.info("保存租户[{}]资产汇总统计", tenant.getAccountName());
        } catch (Exception e) {
            log.error("租户[{}]资产汇总统计异常，{}", tenant.getAccountName(), e.getMessage(), e);
        }
    }

    /**
     * 计算百分比
     *
     * @param x
     * @param y
     * @return
     */
    private String calcPercent(long x, long y) {
        double d1 = x * 1.0;
        double d2 = y * 1.0;
        NumberFormat percentInstance = NumberFormat.getPercentInstance();
        // 保留两位小数
        percentInstance.setMinimumFractionDigits(2);
        return percentInstance.format(d1 / d2).replace("%", "");
    }

}

