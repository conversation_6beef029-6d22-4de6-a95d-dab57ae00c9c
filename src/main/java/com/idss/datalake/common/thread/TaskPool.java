package com.idss.datalake.common.thread;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Copyright 2020 IDSS
 * <p> 线程池
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2020/11/12 13:52
 * 类说明
 */
@Configuration
@EnableAsync
public class TaskPool {

    @Value("${thread.coreSize:4}")
    private String coreSize;

    @Bean("threadPool")
    public ThreadPoolExecutor threadPoolExecutor() {
        int corePoolSize = Integer.valueOf(coreSize);
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(corePoolSize, corePoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(500));
        return threadPoolExecutor;
    }
}
