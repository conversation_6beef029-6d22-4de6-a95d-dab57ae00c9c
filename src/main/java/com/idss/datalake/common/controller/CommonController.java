package com.idss.datalake.common.controller;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Encoder;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 上传功能接口
 * 需要确认是否用到该功能
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/common")
public class CommonController {

    @Autowired
    private Environment env;

    private static Logger logger = LoggerFactory.getLogger(CommonController.class);

    @ApiOperation(value = "上传文件")
    @PostMapping(value = "/upload")
    public ResultBean uploadFile(@RequestParam(value = "file", required = false) MultipartFile file) {
        return upload(file);
    }

    public ResultBean upload(MultipartFile file) {
        ResultBean resultBean = new ResultBean();

        if (file == null || file.isEmpty()) {
            resultBean.setStatus(Constant.STATUS_FAIL);
            resultBean.setMessage("请选择需上传的文件");
            return resultBean;
        }

        // 限定文件后缀
        String originalFilename = file.getOriginalFilename();
        if (originalFilename.endsWith(".jsp") || originalFilename.endsWith(".html") || originalFilename.endsWith(".php") || originalFilename.endsWith(".asp")
                || originalFilename.endsWith(".dll") || originalFilename.endsWith(".bat") || originalFilename.endsWith(".exe") ||originalFilename.endsWith(".js")
                || originalFilename.endsWith(".sh")) {
            resultBean.setStatus(Constant.STATUS_FAIL);
            resultBean.setMessage("上传的日志文件格式不正确");
            return resultBean;
        }

        // 获取文件大小，如果文件大于10MB，禁止上传
        long size = file.getSize();
        size = size / 1024 / 1024;
        if (size > 10) {
            resultBean.setStatus(Constant.STATUS_FAIL);
            resultBean.setMessage("上传文件不能大于10MB");
            return resultBean;
        }

        // 判断上传的文件是否合法
        String fileName = file.getOriginalFilename();
        String fileNamePrefix = fileName.substring(0, fileName.lastIndexOf("."));
        String fileNameSuffix = fileName.substring(fileName.lastIndexOf("."));
        String newFileName = fileNamePrefix + System.currentTimeMillis() + fileNameSuffix;
        // 如果未指定上传路径，则使用默认上传路径
        // 默认上传路径
        String uploadPath = env.getProperty("file.uploadPath");

        // 如果路径不存在则生成路径
        File uploadFile = new File(uploadPath);
        if (!uploadFile.exists()) {
            uploadFile.mkdirs();
        }

        // 上传文件
        try {
            uploadFile = new File(uploadPath, newFileName);
            byte[] bytes = file.getBytes();
            Path path = Paths.get(uploadFile.getAbsolutePath());
            Files.write(path, bytes);
            resultBean.setMessage("上传成功");
            BASE64Encoder encoder = new BASE64Encoder();
            Map<String, String> map = new HashMap<String, String>();
            map.put("url", encoder.encode(uploadFile.getAbsolutePath().getBytes(StandardCharsets.UTF_8)));
            map.put("fileName", newFileName);
            resultBean.setContent(map);
        } catch (Exception e) {
            e.printStackTrace();
            resultBean.setStatus(Constant.STATUS_FAIL);
            resultBean.setMessage("上传失败");
        }
        return resultBean;
    }

    public void deleteFile(String path) {
        File file = new File(path);
        if (file.exists()) {
            file.delete();
        }
    }
}
