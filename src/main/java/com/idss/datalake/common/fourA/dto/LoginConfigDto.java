package com.idss.datalake.common.fourA.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 登录配置请求传输对象
 *
 * <AUTHOR>
 * @date 2022-05-18
 */
@Data
public class LoginConfigDto {

    @ApiModelProperty("登录配置标识")
    private Integer id;

    @ApiModelProperty("认证类型, 0-系统，1-oauth2，2-4A")
    private Integer authType;

    @ApiModelProperty("启用状态，0-未启用，1-启用")
    private Integer enableStatus;

    @ApiModelProperty("默认状态，0-非默认，1-默认")
    private Integer defaultStatus = 0;

    @ApiModelProperty("显示名称")
    private String displayName;

    @ApiModelProperty("同步状态")
    private Integer syncStatus = 0;

    @ApiModelProperty("是否创建新用户，0-否，1-是")
    private Integer enableCreateUser;

    @ApiModelProperty("用户角色ID")
    private String roleId;

    @ApiModelProperty("用户角色名称")
    private String roleName;

    @ApiModelProperty("授权类型，0-授权式，1-隐藏式")
    private Integer grantType;

    @ApiModelProperty("基础字段信息")
    private OauthBaseFieldInfo oauthBaseFieldInfo;

    @ApiModelProperty("请求code地址")
    private String oauthCodeRequestUrl;

    @ApiModelProperty("请求code方式，get/post")
    private String oauthCodeRequestWay;

    @ApiModelProperty("响应code字段")
    private String oauthCodeRespField;

    @ApiModelProperty("请求code参数信息")
    private List<OauthRequestFieldInfo> oauthCodeFieldInfo;

    @ApiModelProperty("请求token地址")
    private String oauthTokenRequestUrl;

    @ApiModelProperty("请求token方式，get/post")
    private String oauthTokenRequestWay;

    @ApiModelProperty("响应token字段")
    private String oauthTokenRespField;

    @ApiModelProperty("响应token的格式，json/xml")
    private String oauthTokenRespFormat;

    @ApiModelProperty("请求token参数信息")
    private List<OauthRequestFieldInfo> oauthTokenFieldInfo;

    @ApiModelProperty("请求用户地址")
    private String oauthUserRequestUrl;

    @ApiModelProperty("请求user方式，get/post")
    private String oauthUserRequestWay;

    @ApiModelProperty("响应user字段")
    private String oauthUserRespField;

    @ApiModelProperty("响应user的格式，json/xml")
    private String oauthUserRespFormat;

    @ApiModelProperty("请求user参数信息")
    private List<OauthRequestFieldInfo> oauthUserFieldInfo;

    @ApiModelProperty("应用标识")
    private String faAppField;

    @ApiModelProperty("登录地址")
    private String faLoginUrl;

    @ApiModelProperty("认证地址")
    private String faAuthUrl;

    @ApiModelProperty("响应用户字段")
    private String faUserRespField;

    @ApiModelProperty("请求协议")
    private String faRequestProtocol;

    @ApiModelProperty("webservice方法名")
    private String faMethodName;

    @ApiModelProperty("http请求方式，get/post")
    private String faRequestWay;

    @ApiModelProperty("登录参数信息")
    private List<FaLoginFieldInfo> faLoginFieldInfo;

    @ApiModelProperty("认证参数信息")
    private List<FaCheckFieldInfo> faCheckFieldInfo;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("4A请求参数模板-复杂参数配置")
    private String faRequestXmlTemplate;

    private String faVendor;
}
