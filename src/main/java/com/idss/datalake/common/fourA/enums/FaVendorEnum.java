package com.idss.datalake.common.fourA.enums;

/**
 * Copyright 2023 IDSS
 * <p> 4A厂商
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2023/3/9 16:25
 */
public enum FaVendorEnum {

    ASIAINFOSEC("asiaInfoSec", "亚信安全"),
    ASIAINFOTECH("asiaInfoTech", "亚信科技"),
    TONGTECH("tongTech", "东方通"),
    VENUSTECH("venusTech", "启明星辰"),
    VENUSTECHGZ("venusTechGZ", "启明星辰(贵州)"),
    ULTRAPOWER("ultraPower", "神州泰岳"),
    STANDARD("standard", "标准");

    private String code;

    private String name;

    FaVendorEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static FaVendorEnum getVendor(String code){
        for(FaVendorEnum enums:FaVendorEnum.values()){
            if(enums.getCode().equals(code)){
                return enums;
            }
        }
        return null;
    }
}
