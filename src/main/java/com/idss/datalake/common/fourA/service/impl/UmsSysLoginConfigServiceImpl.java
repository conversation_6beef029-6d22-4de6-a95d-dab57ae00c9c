package com.idss.datalake.common.fourA.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.common.fourA.dto.FaCheckFieldInfo;
import com.idss.datalake.common.fourA.dto.FaLoginFieldInfo;
import com.idss.datalake.common.fourA.dto.LoginConfigDto;
import com.idss.datalake.common.fourA.dto.OauthBaseFieldInfo;
import com.idss.datalake.common.fourA.dto.OauthRequestFieldInfo;
import com.idss.datalake.common.fourA.entity.UmsSysLoginConfig;
import com.idss.datalake.common.fourA.enums.AuthTypeEnum;
import com.idss.datalake.common.fourA.enums.OauthGrantTypeEnum;
import com.idss.datalake.common.fourA.mapper.UmsSysLoginConfigMapper;
import com.idss.datalake.common.fourA.service.IUmsSysLoginConfigService;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.radar.util.UmsUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 登录配置服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Service
public class UmsSysLoginConfigServiceImpl extends ServiceImpl<UmsSysLoginConfigMapper, UmsSysLoginConfig> implements IUmsSysLoginConfigService {

    // @Autowired
    // private RoleMapper roleMapper;

    // @Autowired
    // private LoginAuthManager loginAuthManager;

    private static Pattern regex = Pattern.compile("\\$\\{([^}]*)\\}");

    @Override
    public void save(LoginConfigDto dto) {
        UmsSysLoginConfig umsSysLoginConfig = new UmsSysLoginConfig();
        BeanUtils.copyProperties(dto, umsSysLoginConfig);
        if (dto.getOauthBaseFieldInfo() != null) {
            umsSysLoginConfig.setOauthBaseFieldInfo(JSON.toJSONString(dto.getOauthBaseFieldInfo()));
        }
        if (!CollectionUtils.isEmpty(dto.getOauthCodeFieldInfo())) {
            umsSysLoginConfig.setOauthCodeFieldInfo(JSON.toJSONString(dto.getOauthCodeFieldInfo()));
        }
        if (!CollectionUtils.isEmpty(dto.getOauthTokenFieldInfo())) {
            umsSysLoginConfig.setOauthTokenFieldInfo(JSON.toJSONString(dto.getOauthTokenFieldInfo()));
        }
        if (!CollectionUtils.isEmpty(dto.getOauthUserFieldInfo())) {
            umsSysLoginConfig.setOauthUserFieldInfo(JSON.toJSONString(dto.getOauthUserFieldInfo()));
        }
        if (!CollectionUtils.isEmpty(dto.getFaLoginFieldInfo())) {
            umsSysLoginConfig.setFaLoginFieldInfo(JSON.toJSONString(dto.getFaLoginFieldInfo()));
        }
        if (!CollectionUtils.isEmpty(dto.getFaCheckFieldInfo())) {
            umsSysLoginConfig.setFaCheckFieldInfo(JSON.toJSONString(dto.getFaCheckFieldInfo()));
        }

        Integer id = dto.getId();
        LocalDateTime date = LocalDateTime.now();
        String userName = UmsUtils.getEmptyUVO().getUserName();
        if (id != null) {
            // 修改
            umsSysLoginConfig.setId(id);
            umsSysLoginConfig.setUpdateTime(date);
            umsSysLoginConfig.setUpdateUser(userName);
        } else {
            // 新增
            umsSysLoginConfig.setCreateUser(userName);
            umsSysLoginConfig.setCreateTime(date);
            umsSysLoginConfig.setUpdateTime(date);
        }
        this.saveOrUpdate(umsSysLoginConfig);
    }

    @Override
    public Map<String, Object> list(Map<String, Object> param) {
        Map<String, Object> data = new HashMap<>();

        int pageNum = (int) param.get("pageNum");
        int pageSize = (int) param.get("pageSize");

        Page<UmsSysLoginConfig> page = new Page<>(pageNum, pageSize);
        QueryWrapper<UmsSysLoginConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id,display_name,enable_status,auth_type,sync_status,update_time,default_status");
        queryWrapper.orderBy(true, "asc".equalsIgnoreCase((String) param.get("orderType")),
                CamelToUnderUtil.underField((String) param.get("orderField")));

        IPage<UmsSysLoginConfig> pageResult = this.page(page, queryWrapper);

        data.put("pageNum", pageResult.getCurrent());
        data.put("pageSize", pageResult.getSize());
        data.put("total", pageResult.getTotal());
        data.put("data", pageResult.getRecords());
        return data;
    }

    @Override
    public LoginConfigDto getLoginConfigById(Integer id) {
        UmsSysLoginConfig umsSysLoginConfig = this.getById(id);
        LoginConfigDto dto = new LoginConfigDto();
        BeanUtils.copyProperties(umsSysLoginConfig, dto);
        if (AuthTypeEnum.OAUTH2.getIndex() == umsSysLoginConfig.getAuthType()) {
            // oauth2请求参数信息
            dto.setOauthBaseFieldInfo(JSON.parseObject(umsSysLoginConfig.getOauthBaseFieldInfo(), new TypeReference<OauthBaseFieldInfo>() {
            }));
            dto.setOauthCodeFieldInfo(JSON.parseObject(umsSysLoginConfig.getOauthCodeFieldInfo(), new TypeReference<List<OauthRequestFieldInfo>>() {
            }));
            dto.setOauthTokenFieldInfo(JSON.parseObject(umsSysLoginConfig.getOauthTokenFieldInfo(), new TypeReference<List<OauthRequestFieldInfo>>() {
            }));
            dto.setOauthUserFieldInfo(JSON.parseObject(umsSysLoginConfig.getOauthUserFieldInfo(), new TypeReference<List<OauthRequestFieldInfo>>() {
            }));
        }
        if (AuthTypeEnum.FA.getIndex() == umsSysLoginConfig.getAuthType()) {
            // 4A请求参数信息
            dto.setFaLoginFieldInfo(JSON.parseObject(umsSysLoginConfig.getFaLoginFieldInfo(), new TypeReference<List<FaLoginFieldInfo>>() {
            }));
            dto.setFaCheckFieldInfo(JSON.parseObject(umsSysLoginConfig.getFaCheckFieldInfo(), new TypeReference<List<FaCheckFieldInfo>>() {
            }));
        }

        // 角色在配置时选择，此处角色已弃用
        // RolePermissionBean role = roleMapper.getRole(dto.getRoleId());
        // if (role != null) {
        //     dto.setRoleName(role.getRoleName());
        // }

        return dto;
    }

    @Override
    public void enable(Integer id) {
        UmsSysLoginConfig loginConfig = this.getById(id);
        if (loginConfig.getEnableStatus() == 1) {
            loginConfig.setEnableStatus(0);
        } else {
            loginConfig.setEnableStatus(1);
        }
        this.updateById(loginConfig);

    }

    @Override
    public void modifyDefault(Integer id, Integer defaultStatus) {
        if (defaultStatus == 1) {
            // 默认对接只有一个，其他的要修改成非默认
            this.update(new UpdateWrapper<UmsSysLoginConfig>().set("default_status", 0).eq("default_status", 1));
        }
        this.update(new UpdateWrapper<UmsSysLoginConfig>().set("default_status", defaultStatus).eq("id", id));
    }

    @Override
    public Boolean checkNameRepeat(String name, Integer id) {
        Boolean repeatFlag;
        if (id == null) {
            repeatFlag = this.count(new QueryWrapper<UmsSysLoginConfig>().eq("display_name", name)) > 0;
        } else {
            repeatFlag = this.count(new QueryWrapper<UmsSysLoginConfig>().eq("display_name", name).ne("id", id)) > 0;
        }
        return repeatFlag;
    }

    @Override
    public List<JSONObject> getAuths() {
        QueryWrapper<UmsSysLoginConfig> queryWrapper = new QueryWrapper<>();
        List<UmsSysLoginConfig> configList = this.list(queryWrapper
                .eq("enable_status", 1)
                .orderByDesc("default_status")
                .orderByDesc("update_time"));
        List<JSONObject> ret = new ArrayList<>();
        configList.forEach(x -> {
            JSONObject json = new JSONObject();
            json.put("id", x.getId());
            json.put("authType", x.getAuthType());
            json.put("defaultStatus", x.getDefaultStatus());
            json.put("displayName", x.getDisplayName());

            if (AuthTypeEnum.OAUTH2.getIndex() == x.getAuthType()) {
                Map<String, String> baseFieldMap = buildBaseFieldMap(x);
                List<OauthRequestFieldInfo> fieldInfoList = null;
                if (OauthGrantTypeEnum.AUTHORIZATION_CODE.getIndex() == x.getGrantType()) {
                    json.put("loginUrl", x.getOauthCodeRequestUrl());
                    fieldInfoList = JSON.parseObject(x.getOauthCodeFieldInfo(), new TypeReference<List<OauthRequestFieldInfo>>() {
                    });
                    json.put("requestWay", x.getOauthCodeRequestWay());
                    json.put("respFieldName", x.getOauthCodeRespField());
                } else {
                    json.put("loginUrl", x.getOauthTokenRequestUrl());
                    fieldInfoList = JSON.parseObject(x.getOauthTokenFieldInfo(), new TypeReference<List<OauthRequestFieldInfo>>() {
                    });
                    json.put("requestWay", x.getOauthTokenRequestWay());
                    json.put("respFieldName", x.getOauthTokenRespField());
                }
                if (!CollectionUtils.isEmpty(fieldInfoList)) {
                    for (OauthRequestFieldInfo fieldInfo : fieldInfoList) {
                        String realValue = getFieldRealValue(fieldInfo.getValue(), baseFieldMap);
                        fieldInfo.setValue(realValue);
                    }
                    json.put("fieldInfo", fieldInfoList);
                }

            } else {
                json.put("loginUrl", x.getFaLoginUrl());
                json.put("fieldInfo", JSON.parseObject(x.getFaLoginFieldInfo(), new TypeReference<List<OauthRequestFieldInfo>>() {
                }));
                json.put("requestWay", x.getFaRequestWay());
                json.put("respFieldName", x.getFaAppField());
            }
            ret.add(json);
        });
        return ret;
    }

    /**
     * 查询默认登录（排除原生）
     *
     * @return
     */
    @Override
    public UmsSysLoginConfig getDefaultConfig() {
        QueryWrapper<UmsSysLoginConfig> queryWrapper = new QueryWrapper<>();
        List<UmsSysLoginConfig> configList = this.list(queryWrapper
                .eq("enable_status", 1)
                .eq("default_status", 1)
                .ne("auth_type", 0));
        if (CollectionUtils.isEmpty(configList)) {
            return null;
        }
        return configList.get(0);
    }

    /**
     * 字段值替换，把${key}换成真实的值
     *
     * @param tempValue
     * @param fieldMap
     * @return
     */
    public String getFieldRealValue(String tempValue, Map<String, String> fieldMap) {
        if (MapUtils.isEmpty(fieldMap)) {
            return tempValue;
        }
        String key = getKeyFromPattern(tempValue);
        if (StringUtils.isNotEmpty(key)) {
            // 说明是${}格式，替换成真正的值（真实数据来源：配置基础数据、请求参数、接口响应数据）
            if (fieldMap.get(key) != null) {
                return fieldMap.get(key);
            }
        }
        return tempValue;
    }

    /**
     * 解析${}中的字段，不满足返回空串
     *
     * @param content
     * @return
     */
    private String getKeyFromPattern(String content) {
        content = content == null ? "" : content;
        Matcher matcher = regex.matcher(content);
        StringBuilder sql = new StringBuilder();
        while (matcher.find()) {
            sql.append(matcher.group(1));
            break;
        }
        return sql.toString();
    }

    /**
     * 基础参数信息，第三方key和value
     *
     * @param config
     * @return
     */
    public Map<String, String> buildBaseFieldMap(UmsSysLoginConfig config) {
        // 获取基础参数
        OauthBaseFieldInfo baseFieldInfo = JSON.parseObject(config.getOauthBaseFieldInfo(), new TypeReference<OauthBaseFieldInfo>() {
        });
        Map<String, String> baseFieldMap = new HashMap<>();
        baseFieldMap.put(baseFieldInfo.getClient_id().getThirdPartyFieldName(), baseFieldInfo.getClient_id().getValue());
        baseFieldMap.put(baseFieldInfo.getClient_secret().getThirdPartyFieldName(), baseFieldInfo.getClient_secret().getValue());
        baseFieldMap.put(baseFieldInfo.getRedirect_uri().getThirdPartyFieldName(), baseFieldInfo.getRedirect_uri().getValue());
        return baseFieldMap;
    }
}
