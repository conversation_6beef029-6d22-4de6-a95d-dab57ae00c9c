package com.idss.datalake.common.redis.connection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisPool;

public class ConnectionFactory {
    private static Logger logger = LoggerFactory.getLogger(ConnectionFactory.class);
    private static JedisPool jPool;



    /**
     *
     * @return jedis 连接池
     */
    public static JedisPool getJedisPool () {
        logger.info("获取redis实例");
        initJPoolInstance();
        if (jPool == null) {
            logger.error("========== 警告：获取 Redis 连接资源失败，请排查原因！！！ ==========");
        }
        return ConnectionFactory.jPool;
    }

    /***
     *
     */
    public static void initJPoolInstance () {
        if (jPool == null) {
            synchronized (ConnectionFactory.class) {
                if (jPool == null) {
                    jPool = StandaloneRedisResource.getResourcePool();
                    ShutDownHook hook = new ShutDownHook();
                    hook.safetyShutdown();
                }
            }
        }
    }
//    private static class build {
//    static {
//        jPool = StandaloneRedisResource.getResourcePool();
//        if (jPool == null) {
//            logger.error("========== 警告：获取 Redis 连接资源失败，请排查原因！！！ ==========");
//        }
//        ShutDownHook hook = new ShutDownHook();
//        hook.safetyShutdown();
//    }
//        private static final ConnectionFactory factory = new ConnectionFactory();
//    }
}
