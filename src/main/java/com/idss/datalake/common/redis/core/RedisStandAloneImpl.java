package com.idss.datalake.common.redis.core;

import com.idss.datalake.common.redis.connection.RedisConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class RedisStandAloneImpl extends abstractStandAloneOpreate {
    private static Logger logger = LoggerFactory.getLogger(RedisStandAloneImpl.class);
    private final static String TEMP_LIST_PREFIX = "radar-redisLimitListInfo-temp:";
    int infoLimit = RedisConfig.getInstance().infoLimit;

    @Override
    public boolean redisIsAlive() {
        Jedis jedis = getJedis();
        if (jedis != null) {
            boolean isConn = jedis.isConnected();
            if ("pong".equals(jedis.ping().toLowerCase()) && isConn) {
                return true;
            } else {
                logger.error("ERR: Jedis 连接池创建失败，无法获取有效客户端连接！");
                return false;
            }
        } else {
            logger.error("ERR: Jedis 连接池创建失败，无法获取客户端！");
            return false;
        }
    }

    @Override
    public boolean keyExist(String key) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            boolean isExist = jedis.exists(key);
            rebackJedis(jedis);
            return isExist;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return false;
        }
    }

    @Override
    public long setExpireForKey(String key, int expireSeconds) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            long flag = jedis.expire(key, expireSeconds);
            rebackJedis(jedis);
            return flag;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return 0;
        }
    }

    @Override
    public String getValueTypeByKey(String key) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            String type = jedis.type(key);
            rebackJedis(jedis);
            return type;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return "none";
        }
    }

    @Override
    public long persistentKey(String key) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            long code = jedis.persist(key);
            rebackJedis(jedis);
            return code;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return 0;
        }
    }

    @Override
    public long deleteKey(String... key) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            long delNum = jedis.del(key);
            rebackJedis(jedis);
            return delNum;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return 0;
        }
    }

    @Override
    public String[] getAllKeys(String pattern) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            Set<String> keySet = jedis.keys(pattern);
            String[] keysArray = keySet.toArray(new String[keySet.size()]);
            rebackJedis(jedis);
            return keysArray;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return null;
        }
    }

    @Override
    public String setStringValueForCombKey(String combKey, String value) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            String isOk = jedis.set(combKey, value);
            rebackJedis(jedis);
            return isOk;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return "Failure";
        }
    }

    @Override
    public String setLPush(String combKey, String value) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            jedis.lrem(combKey, 0, value);
            Long lpush = jedis.lpush(combKey, value);
            rebackJedis(jedis);
            return "success";
        } else {
            logger.error("ERR: Jedis Client is null!");
            return "Failure";
        }
    }

    @Override
    public List<String> getLPush(String combKey) {
        Jedis jedis = getJedis();
        List<String> list = new ArrayList();
        if (jedis != null) {
            for (int i = 0; i < 5; i++) {
                if (!"".equals(jedis.lindex(combKey, i))) {
                    list.add(jedis.lindex(combKey, i));
                } else {
                    break;
                }
            }
            jedis.ltrim(combKey, 0, 4);
            rebackJedis(jedis);
            return list;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return list;
        }
    }

    @Override
    public String lPop(String combKey) {
        Jedis jedis = getJedis();
        if (jedis != null) {
            String value = jedis.lpop(combKey);
            rebackJedis(jedis);
            return value;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return null;
        }
    }

    @Override
    public String setStringValueForCombKey(String combKey, String value, int expireSeconds) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            try {
                String isOk = jedis.setex(combKey, expireSeconds, value);
                rebackJedis(jedis);
                return isOk;
            } catch (JedisException jex) {
                logger.error("( 142 )ERR: " + jex.fillInStackTrace().toString().concat(System.lineSeparator().concat(jex.getCause().getMessage())));
                return "Failure";
            }
        } else {
            logger.error("ERR: Jedis Client is null!");
            return "Failure";
        }
    }

    @Override
    public String getStringValueByCombKey(String combKey) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            try {
                String returnValue = jedis.get(combKey);
                rebackJedis(jedis);
                return returnValue;
            } catch (JedisException jex) {
                logger.error("( 161 )ERR: " + jex.fillInStackTrace().toString().concat(System.lineSeparator().concat(jex.getCause().getMessage())));
                return null;
            }
        } else {
            logger.error("ERR: Jedis Client is null!");
            return null;
        }
    }

    @Override
    public long setSetValueForKey(String key, String... values) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            long count = jedis.sadd(key, values);
            rebackJedis(jedis);
            return count;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return 0;
        }
    }

    @Override
    public long deleteSetValueForKey(String key, String... values) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            long remNum = jedis.srem(key, values);
            rebackJedis(jedis);
            return remNum;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return 0;
        }
    }

    @Override
    public boolean isSetMember(String key, String values) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            boolean ret = jedis.sismember(key, values);
            rebackJedis(jedis);
            return ret;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return false;
        }
    }

    @Override
    public long deleteSetMember(String key, String... values) {
        return deleteSetValueForKey(key, values);
    }

    @Override
    public Set<String> getAllSetValues(String key) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            Set<String> setValues = jedis.smembers(key);
            rebackJedis(jedis);
            return setValues;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return null;
        }
    }

    @Override
    public boolean hashFieldExist(String key, String field) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            boolean isExist = jedis.hexists(key, field);
            rebackJedis(jedis);
            return isExist;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return false;
        }
    }

    @Override
    public boolean hashFieldExist(byte[] key, byte[] field) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            boolean isExist = jedis.hexists(key, field);
            rebackJedis(jedis);
            return isExist;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return false;
        }
    }

    @Override
    public long getHashFieldsSize(String key) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            long hashLen = jedis.hlen(key);
            rebackJedis(jedis);
            return hashLen;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return 0;
        }
    }

    @Override
    public long getHashFieldsSize(byte[] key) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            long hashLen = jedis.hlen(key);
            rebackJedis(jedis);
            return hashLen;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return 0;
        }
    }

    @Override
    public long deleteHashField(String key, String... field) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            long delNum = jedis.hdel(key, field);
            rebackJedis(jedis);
            return delNum;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return 0;
        }
    }

    @Override
    public long deleteHashField(byte[] key, byte[]... field) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            long delNum = jedis.hdel(key, field);
            rebackJedis(jedis);
            return delNum;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return 0;
        }
    }

    @Override
    public long setHashValue(String key, String field, String value) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            long code = jedis.hset(key, field, value);
            rebackJedis(jedis);
            return code;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return 0;
        }
    }

    @Override
    public long setHashValue(byte[] key, byte[] field, byte[] value) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            long code = jedis.hset(key, field, value);
            rebackJedis(jedis);
            return code;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return 0;
        }
    }

    @Override
    public String setHashValues(String key, Map<String, String> keyValueMap) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            try {
                String code = jedis.hmset(key, keyValueMap);
                rebackJedis(jedis);
                return code;
            } catch (JedisException jex) {
                logger.error("( 353 )ERR: " + jex.fillInStackTrace().toString().concat(System.lineSeparator().concat(jex.getCause().getMessage())));
                return "Failure";
            }
        } else {
            logger.error("ERR: Jedis Client is null!");
            return "Failure";
        }
    }

    @Override
    public String setHashValues(byte[] key, Map<byte[], byte[]> keyValueMap) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            try {
                String code = jedis.hmset(key, keyValueMap);
                rebackJedis(jedis);
                return code;
            } catch (JedisException jex) {
                logger.error("( 372 )ERR: " + jex.fillInStackTrace().toString().concat(System.lineSeparator().concat(jex.getCause().getMessage())));
                return "Failure";
            }
        } else {
            logger.error("ERR: Jedis Client is null!");
            return "Failure";
        }
    }

    @Override
    public String getHashValue(String key, String field) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            String value = jedis.hget(key, field);
            rebackJedis(jedis);
            return value;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return null;
        }
    }

    @Override
    public byte[] getHashValue(byte[] key, byte[] field) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            byte[] value = jedis.hget(key, field);
            rebackJedis(jedis);
            return value;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return null;
        }
    }

    @Override
    public List<String> getHashValues(String key, String... field) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            List<String> values = jedis.hmget(key, field);
            rebackJedis(jedis);
            return values;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return null;
        }
    }

    @Override
    public List<byte[]> getHashValues(byte[] key, byte[]... field) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            List<byte[]> values = jedis.hmget(key, field);
            rebackJedis(jedis);
            return values;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return null;
        }
    }

    @Override
    public Map<String, String> getAllHashTermsByKey(String key) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            Map<String, String> kvMap = jedis.hgetAll(key);
            rebackJedis(jedis);
            return kvMap;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return null;
        }
    }

    @Override
    public Map<byte[], byte[]> getAllHashTermsByKey(byte[] key) {
        Jedis jedis = getJedis();

        if (jedis != null) {
            Map<byte[], byte[]> kvMap = jedis.hgetAll(key);
            rebackJedis(jedis);
            return kvMap;
        } else {
            logger.error("ERR: Jedis Client is null!");
            return null;
        }
    }

    @Override
    public synchronized void setLimitHashValue(String key, String field, String value) {
        String tempListName = TEMP_LIST_PREFIX + key;
        Jedis jedis = getJedis();

        if (jedis != null) {
            if (!"list".equals(getValueTypeByKey(tempListName))) {
                Set<String> setKeys = jedis.hkeys(key);
                jedis.del(tempListName);
                if (setKeys.size() != 0) {
                    String[] arrKeys = setKeys.toArray(new String[setKeys.size()]);
                    if (arrKeys.length <= infoLimit) {
                        jedis.lpush(tempListName, arrKeys);
                    } else {
                        String[] subKeys = Arrays.copyOfRange(arrKeys, 0, infoLimit - 1);
                        jedis.lpush(tempListName, subKeys);
                    }
                }
            }

            if (jedis.hexists(key, field)) {
                jedis.hset(key, field, value);
            } else {
                long tmpListLen = jedis.llen(tempListName);
                if (tmpListLen < infoLimit) {
                    jedis.lpush(tempListName, field);
                    jedis.hset(key, field, value);
                } else if (tmpListLen == infoLimit) {
                    String delField = jedis.rpop(tempListName);
                    jedis.hdel(key, delField);

                    jedis.lpush(tempListName, field);
                    jedis.hset(key, field, value);
                } else {
                    long loopCount = tmpListLen - infoLimit - 1;
                    List<String> buff = new ArrayList<>();
                    for (int i = 0; i < loopCount; i++) {
                        String delKey = jedis.rpop(tempListName);
                        buff.add(delKey);
                    }
                    jedis.hdel(key, buff.toArray(new String[buff.size()]));

                    jedis.lpush(tempListName, field);
                    jedis.hset(key, field, value);
                }
            }
            rebackJedis(jedis);
        }
    }

    @Override
    public synchronized void setLimitHashValues(String key, Map<String, String> keyValueMap) {
        String tempListName = TEMP_LIST_PREFIX + key;
        Jedis jedis = getJedis();
        if (jedis != null) {
            Set<String> setKeys = jedis.hkeys(key);
            Set<String> paramKeys = keyValueMap.keySet();

            if (!"list".equals(getValueTypeByKey(tempListName))) {
                jedis.del(tempListName);
                if (setKeys.size() != 0) {
                    String[] arrKeys = setKeys.toArray(new String[setKeys.size()]);
                    if (arrKeys.length <= infoLimit) {
                        jedis.lpush(tempListName, arrKeys);
                    } else {
                        String[] subKeys = Arrays.copyOfRange(arrKeys, 0, infoLimit - 1);
                        jedis.lpush(tempListName, subKeys);
                    }
                }
            }

            Set<String> intersectionKeys = new HashSet<>(); //交集
            intersectionKeys.addAll(setKeys);
            intersectionKeys.retainAll(paramKeys);

            if (intersectionKeys.isEmpty()) {
                long temListLen = jedis.llen(tempListName);
                jedis.lpush(tempListName, paramKeys.toArray(new String[paramKeys.size()]));
                jedis.hmset(key, keyValueMap);

                if (paramKeys.size() + temListLen > infoLimit) {
                    List<String> buff = new ArrayList<>();
                    long loopSize = paramKeys.size() + temListLen - infoLimit;
                    for (int i = 0; i < loopSize; i++) {
                        String temField = jedis.rpop(tempListName);
                        buff.add(temField);
                    }
                    jedis.hdel(key, buff.toArray(new String[buff.size()]));
                }
            } else {
                long temListLen = jedis.llen(tempListName);
                Set<String> tmpSset = new HashSet<>();
                tmpSset.addAll(paramKeys);
                tmpSset.removeAll(intersectionKeys);
                if (tmpSset.size() != 0) {
                    jedis.lpush(tempListName, tmpSset.toArray(new String[tmpSset.size()]));
                }
                jedis.hmset(key, keyValueMap);

                if (tmpSset.size() + temListLen > infoLimit) {
                    List<String> buff = new ArrayList<>();
                    long loopSize = tmpSset.size() + temListLen - infoLimit;
                    for (int i = 0; i < loopSize; i++) {
                        String temField = jedis.rpop(tempListName);
                        buff.add(temField);
                    }
                    if (buff.size() != 0) {
                        jedis.hdel(key, buff.toArray(new String[buff.size()]));
                    }
                }
            }
            rebackJedis(jedis);
        }
    }
}
