package com.idss.datalake.common.redis.connection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.exceptions.JedisException;

class StandaloneRedisResource {
    private StandaloneRedisResource(){}
    private static Logger logger = LoggerFactory.getLogger(StandaloneRedisResource.class);
    private static JedisPool pool = null;

    static{
        JedisPoolConfig jpConfig = new JedisPoolConfig();

        jpConfig.setMaxIdle(RedisConfig.getInstance().maxIdle);
        jpConfig.setMinIdle(RedisConfig.getInstance().minIdle);
        jpConfig.setMaxTotal(RedisConfig.getInstance().maxActive);
        jpConfig.setTestOnBorrow(true);
        jpConfig.setTestOnCreate(true);
        jpConfig.setTestWhileIdle(true);
        jpConfig.setTestOnReturn(true);
        jpConfig.setMaxWaitMillis(RedisConfig.getInstance().maxWaitMillis);
        jpConfig.setTimeBetweenEvictionRunsMillis(30000L);
        jpConfig.setLifo(true);

        try {
            if (RedisConfig.getInstance().host != null) {
                pool = new JedisPool(jpConfig,
                        RedisConfig.getInstance().host,
                        RedisConfig.getInstance().port,
                        RedisConfig.getInstance().connectionTimeOut,
                        RedisConfig.getInstance().readTimeout,
                        RedisConfig.getInstance().password,
                        RedisConfig.getInstance().database,
                        null, false, null, null, null);
            } else {
                logger.error("ERR: 无法获取配置文件中的 redis.host 配置项！");
            }
        } catch (JedisException jex) {
            logger.error("( 42 )ERR: " + jex.fillInStackTrace().toString().concat(System.lineSeparator().concat(jex.getCause().getMessage())));
        }
    }
     static JedisPool getResourcePool(){
        try{
            if (pool != null) {
                return pool;
            } else {
                return null;
            }
        } catch (JedisException jex) {
            logger.error("( 53 )ERR: " + jex.fillInStackTrace().toString().concat(System.lineSeparator().concat(jex.getCause().getMessage())));
            return null;
        }
    }
     static void closeResourcePool(){
        try {
            pool.close();
            pool = null;
        } catch (JedisException jex) {
            logger.error("( 62 )ERR: " + jex.fillInStackTrace().toString().concat(System.lineSeparator().concat(jex.getCause().getMessage())));
        }
    }
}
