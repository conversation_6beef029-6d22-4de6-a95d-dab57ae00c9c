package com.idss.datalake.common.redis.connection;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * Date: 2018/12/20 09:53
 * Description:
 */
@Slf4j
public class ClusterPoolFactory extends BasePooledObjectFactory<JedisCluster> {
    @Override
    public JedisCluster create() throws Exception {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(RedisConfig.getInstance().maxActive);
        poolConfig.setMaxIdle(RedisConfig.getInstance().maxIdle);
        poolConfig.setMinIdle(RedisConfig.getInstance().minIdle);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setMaxWaitMillis(RedisConfig.getInstance().maxWaitMillis);
        poolConfig.setTimeBetweenEvictionRunsMillis(60000L);
        if (RedisConfig.clusterNodes != null && !RedisConfig.clusterNodes.isEmpty()) {
            Set<HostAndPort> hostAndPorts = new HashSet<>();
            String[] clusterNodes = RedisConfig.clusterNodes.split(",");
            for (String str : clusterNodes) {
                String[] str1 = str.split(":");
                HostAndPort hostAndPort = new HostAndPort(str1[0], Integer.valueOf(str1[1]));
                hostAndPorts.add(hostAndPort);
            }
            return new JedisCluster(hostAndPorts, RedisConfig.connectionTimeOut, RedisConfig.readTimeout, 6, RedisConfig.password, poolConfig);
        }
        return null;
    }

    @Override
    public PooledObject<JedisCluster> wrap(JedisCluster jedisCluster) {
        PooledObject<JedisCluster> pooledObject = new DefaultPooledObject<>(jedisCluster);
        return pooledObject;
    }

    @Override
    public void activateObject(PooledObject<JedisCluster> p) throws Exception {
        super.activateObject(p);
    }

    @Override
    public void destroyObject(PooledObject<JedisCluster> p) throws Exception {
        JedisCluster cluster = p.getObject();
        cluster.close();
        super.destroyObject(p);
    }
}

