package com.idss.datalake.common.redis.api;

import com.idss.datalake.common.redis.connection.RedisConfig;
import com.idss.datalake.common.redis.core.RedisClusterImpl;
import com.idss.datalake.common.redis.core.RedisOperation;
import com.idss.datalake.common.redis.core.RedisStandAloneImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RedisFactory {
    private static Logger logger = LoggerFactory.getLogger(RedisFactory.class);
    private RedisFactory(){}

    public static RedisOperation toProcess() {
        return build.reidsOpt;
    }

    private static final class build {
        private static volatile RedisOperation opt = null;

        static {
            if (opt == null) {
               synchronized (RedisFactory.class){
                   if (opt == null) {
                       if (RedisConfig.isStandAlone()) {
                           opt = new RedisStandAloneImpl();
                       } else {
                           opt = new RedisClusterImpl();
                       }
                   }
               }
            }
        }

        private static final RedisOperation reidsOpt = opt;
    }
}
