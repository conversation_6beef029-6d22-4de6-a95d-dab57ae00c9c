
/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V3.0.11,idss,2021-06-24
 * create 注释
 *-----------------------------------------------------------------------------*
 * V3.0.11,idss,2021-06-24
 * M  {modifyComment}
 * -  {delComment}
 * +  {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.common.syslog.entity;


/**
 * <AUTHOR>
 * date 2020/10/23
 */
public class OptModule {
    /**
     * DOCUMENT ME!
     */
    public static final String INDEX = "index"; //首页

    /**
     * DOCUMENT ME!
     */
    public static final String EXCEPTION = "exception"; //异常洞察

    /**
     * DOCUMENT ME!
     */
    public static final String THREAT = "threat"; //威胁洞察

    /**
     * DOCUMENT ME!
     */
    public static final String INSIGHT_USER = "insight-user"; //用户分析

    /**
     * DOCUMENT ME!
     */
    public static final String INSIGHT_DEVICE = "insight-device"; //设备分析

    /**
     * DOCUMENT ME!
     */
    public static final String INSIGHT_APPLICATION = "insight-application"; //应用分析

    /**
     * DOCUMENT ME!
     */
    public static final String INSIGHT_DATA = "insight-data"; //数据分析

    /**
     * DOCUMENT ME!
     */
    public static final String INSIGHT_PROGRAM = "insight-program"; //程序分析

    /**
     * DOCUMENT ME!
     */
    public static final String VISUAL_RISK_OVERVIEW = "visual-risk-overview"; //风险总览

    /**
     * DOCUMENT ME!
     */
    public static final String AI_BI_DASHBOARD = "ai_bi_dashboard"; //自定义仪表盘

    /**
     * DOCUMENT ME!
     */
    public static final String DATA_EXPLORE = "dataExplore"; //数据探索

    /**
     * DOCUMENT ME!
     */
    public static final String DATA_ACCESS = "data-access"; //数据采集

    /**
     * DOCUMENT ME!
     */
    public static final String DATA_MART = "dataMart"; //特征挖掘

    /**
     * DOCUMENT ME!
     */
    public static final String BASE_INFO = "baseInfo"; //基础信息

    /**
     * DOCUMENT ME!
     */
    public static final String WATCHLIST = "watchList"; //观察列表

    /**
     * DOCUMENT ME!
     */
    public static final String MODEL_EXPLORE = "model-explore"; //模型探索

    /**
     * DOCUMENT ME!
     */
    public static final String EXCEPTION_MODEL = "exception-config-model"; //异常模型

    /**
     * DOCUMENT ME!
     */
    public static final String THREAT_MODEL = "threat-config-model"; //威胁模型

    /**
     * DOCUMENT ME!
     */
    public static final String SCHEDULER_CONFIGURATION = "scheduler-configuration"; //模型调度

    /**
     * DOCUMENT ME!
     */
    public static final String NOTEBOOK = "notebook"; //notebook

    /**
     * DOCUMENT ME!
     */
    public static final String ACCOUNT_RIGHT = "account-right"; //账号权限

    /**
     * DOCUMENT ME!
     */
    public static final String DATA_DICTIONARY = "data-dictionary"; //数据字典

    /**
     * DOCUMENT ME!
     */
    public static final String LIST_MANAGE = "list-manage"; //名单管理

    /**
     * DOCUMENT ME!
     */
    public static final String CONFIG_MANAGE = "config-manage"; //配置管理

    /**
     * DOCUMENT ME!
     */
    public static final String RESOURCE_MANAGE = "resource-manage"; //资源管理

    /**
     * DOCUMENT ME!
     */
    public static final String WARN = "warn"; //告警管理

    /**
     * DOCUMENT ME!
     */
    public static final String AUDIT = "audit"; //基础库管理

    /**
     * DOCUMENT ME!
     */
    public static final String AUTH_CONFIG_MANAGE = "auth-config-manage"; //认证配置管理

    public static final String REPORT_MANAGE = "report-manage"; //自定义报表

    /**
     * 租户管理
     */
    public static final String TENANT_MANAGE = "tenant-manage";
    /**
     * 资源管理
     */
    public static final String CLUSTER_RESOURCE_MANAGE = "cluster-resource-manage";
    /**
     * 授权管理
     */
    public static final String LICENSE_MANAGE = "license-manage";
    /**
     * 元数据管理
     */
    public static final String DATA_LAKE_GOVERNANCE_META = "data-lake-governance-meta";
    /**
     * 数据标准管理
     */
    public static final String DATA_LAKE_GOVERNANCE_STANDARD = "data-lake-governance-standard";
    /**
     * 质量监测模型
     */
    public static final String DATA_LAKE_GOVERNANCE_QUALITY_MODEL = "data-lake-governance-quality-model";
    /**
     * 质量监测规则
     */
    public static final String DATA_LAKE_GOVERNANCE_QUALITY_RULE = "data-lake-governance-quality-rule";
    /**
     * 质量监测任务
     */
    public static final String DATA_LAKE_GOVERNANCE_QUALITY_TASK = "data-lake-governance-quality-task";
    /**
     * 主数据模型
     */
    public static final String DATA_LAKE_GOVERNANCE_MASTERDATA_MODEL = "data-lake-governance-masterData-model";
    /**
     * 主数据维护
     */
    public static final String DATA_LAKE_GOVERNANCE_MASTERDATA_MAINTAIN = "data-lake-governance-masterData-maintain";
    /**
     * 数据业务类型
     */
    public static final String DATA_LAKE_GOVERNANCE_CONFIG_BUSINESS = "data-lake-governance-config-business";
    /**
     * 业务数据类型分组
     */
    public static final String DATA_LAKE_GOVERNANCE_CONFIG_GROUPING = "data-lake-governance-config-grouping";
    /**
     * 主数据类型
     */
    public static final String DATA_LAKE_GOVERNANCE_CONFIG_DATA = "data-lake-governance-config-data";
    /**
     * 数据源管理
     */
    public static final String ZEPPELIN_INTERPRETER = "zeppelin-interpreter";
    /**
     * notebook
     */
    public static final String ZEPPELIN_NOTEBOOK = "zeppelin-notebook";
}
