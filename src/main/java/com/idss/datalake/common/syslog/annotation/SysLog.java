
/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V3.0.11,idss,2021-06-24
 * create 注释
 *-----------------------------------------------------------------------------*
 * V3.0.11,idss,2021-06-24
 * M  {modifyComment}
 * -  {delComment}
 * +  {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.common.syslog.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @description <p>功能:系统日志注解</p>
 * @see
 * @since 2022-10-24
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Documented
public @interface SysLog {
    /**
     * 操作内容
     *
     * @return
     */
    String logName();

    /**
     * 操作类型
     *
     * @return
     */
    String optType() default "";

    /**
     * 操作模块
     *
     * @return
     */
    String optModule() default "";

    /**
     * 是否插入数据库
     *
     * @return
     */
    boolean switchStatus() default false;

    /**
     * 是否存入redis
     *
     * @return
     */
    boolean switchRedisStatus() default false;

    /**
     * 此字段值用来判断是否是新增或者编辑，用于新增和编辑在同一个controller接口中，一般是主键id
     *
     * @return
     */
    String checkAddField() default "";
}
