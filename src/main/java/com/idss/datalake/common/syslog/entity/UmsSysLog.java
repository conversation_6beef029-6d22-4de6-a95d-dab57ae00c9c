
/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
** Product VERSION,UPDATED BY,UPDATE DATE                                     *
*   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* create 注释
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* M  {modifyComment}
* -  {delComment}
* +  {addCommnet}
\*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.common.syslog.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 日志审计表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class UmsSysLog implements Serializable {
  /**
   * DOCUMENT ME!
   */
  private static final long serialVersionUID = 1L;

  /**
   * DOCUMENT ME!
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id;

  /**
   * 用户名
   */
  private String userName;

  /**
   * 真实姓名
   */
  private String realName;

  /**
   * 登录ip
   */
  private String loginIp;

  /**
   * 请求路径
   */
  private String requestPath;

  /**
   * 操作名称
   */
  private String logName;

  /**
   * 操作结果
   */
  private String logResult;

  /***
   * 操作类型
   *
   */
  private String optType;

  /**
   * 请求方式
   */
  private String httpMethod;

  /**
   * DOCUMENT ME!
   */
  private String userAgent;

  /**
   * DOCUMENT ME!
   */
  private String optModule;

  /**
   * 创建时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime createTime;
}
