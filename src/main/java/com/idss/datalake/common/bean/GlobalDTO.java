package com.idss.datalake.common.bean;

/**
 * <AUTHOR>
 * @description : <p></p>
 * @see：
 * @since 2022/7/28
 */
public class GlobalDTO {
    private Integer pageNum = 1;
    private Integer pageSize = 10;
    private String orderType;
    private String orderField;

    public GlobalDTO() {
    }

    public Integer getPageNum() {
        return this.pageNum;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public String getOrderType() {
        return this.orderType;
    }

    public String getOrderField() {
        return this.orderField;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public void setOrderField(String orderField) {
        this.orderField = orderField;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof GlobalDTO)) {
            return false;
        } else {
            GlobalDTO other = (GlobalDTO) o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label59:
                {
                    Object this$pageNum = this.getPageNum();
                    Object other$pageNum = other.getPageNum();
                    if (this$pageNum == null) {
                        if (other$pageNum == null) {
                            break label59;
                        }
                    } else if (this$pageNum.equals(other$pageNum)) {
                        break label59;
                    }

                    return false;
                }

                Object this$pageSize = this.getPageSize();
                Object other$pageSize = other.getPageSize();
                if (this$pageSize == null) {
                    if (other$pageSize != null) {
                        return false;
                    }
                } else if (!this$pageSize.equals(other$pageSize)) {
                    return false;
                }

                Object this$orderType = this.getOrderType();
                Object other$orderType = other.getOrderType();
                if (this$orderType == null) {
                    if (other$orderType != null) {
                        return false;
                    }
                } else if (!this$orderType.equals(other$orderType)) {
                    return false;
                }

                Object this$orderField = this.getOrderField();
                Object other$orderField = other.getOrderField();
                if (this$orderField == null) {
                    if (other$orderField != null) {
                        return false;
                    }
                } else if (!this$orderField.equals(other$orderField)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof GlobalDTO;
    }

}
