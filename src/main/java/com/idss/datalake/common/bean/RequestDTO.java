package com.idss.datalake.common.bean;

/**
 * <AUTHOR>
 * @description : <p></p>
 * @see：
 * @since 2022/7/28
 */
public class RequestDTO {
    private GlobalDTO global;
    private Object param;

    public RequestDTO() {
    }

    public GlobalDTO getGlobal() {
        return this.global;
    }

    public Object getParam() {
        return this.param;
    }

    public void setGlobal(GlobalDTO global) {
        this.global = global;
    }

    public void setParam(Object param) {
        this.param = param;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof RequestDTO)) {
            return false;
        } else {
            RequestDTO other = (RequestDTO) o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$global = this.getGlobal();
                Object other$global = other.getGlobal();
                if (this$global == null) {
                    if (other$global != null) {
                        return false;
                    }
                } else if (!this$global.equals(other$global)) {
                    return false;
                }

                Object this$param = this.getParam();
                Object other$param = other.getParam();
                if (this$param == null) {
                    if (other$param != null) {
                        return false;
                    }
                } else if (!this$param.equals(other$param)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof RequestDTO;
    }

}
