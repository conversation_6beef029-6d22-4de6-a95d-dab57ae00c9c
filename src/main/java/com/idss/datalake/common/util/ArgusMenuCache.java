///**
// * Copyright (C), 2015-2018, 上海观安信息技术股份有限公司
// * Author:   yangpy
// * Date:     2018/8/24 11:08
// * Description:
// */
//package com.idss.datalake.common.util;
//
//import com.alibaba.fastjson.JSON;
//import com.idss.datalake.auth.entity.Menu;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.io.ByteArrayInputStream;
//import java.io.ByteArrayOutputStream;
//import java.io.FileNotFoundException;
//import java.io.IOException;
//import java.io.ObjectInputStream;
//import java.io.ObjectOutputStream;
//import java.util.List;
//
///**
// * <AUTHOR>
// * @create 2021/07/01
// */
//public class ArgusMenuCache {
//
//    private static final Logger logger = LoggerFactory.getLogger(ArgusMenuCache.class);
//
//    private static ArgusMenuCache argusMenuCache;
//
//    private static List<Menu> menus;
//
//    static {
//        try {
//            menus = JSON.parseArray(ArgusFileUtil.getFileContent("config/menu/menus.json"), Menu.class);
//        } catch (FileNotFoundException e) {
//            e.printStackTrace();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//
//    public static synchronized ArgusMenuCache getCache() {
//        synchronized (logger) {
//            if (argusMenuCache == null) {
//                argusMenuCache = new ArgusMenuCache();
//            }
//        }
//        return argusMenuCache;
//    }
//
//    public List<Menu> getMenu() {
//        List<Menu> newMenus = null;
//        try {
//            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
//            ObjectOutputStream out = new ObjectOutputStream(byteOut);
//            out.writeObject(menus);
//
//            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
//            ObjectInputStream in = new ObjectInputStream(byteIn);
//            newMenus = (List<Menu>) in.readObject();
//        } catch (Exception e) {
//            logger.error("克隆菜单对象异常:", e);
//        }
//        return newMenus;
//    }
//}
