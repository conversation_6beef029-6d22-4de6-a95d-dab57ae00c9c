package com.idss.datalake.common.util;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datashare.cluster.entity.TbCluster;
import com.idss.datalake.datashare.tenant.entity.TbDatasourceInfo;
import com.idss.datalake.datashare.tenant.entity.TbScriptHistory;
import com.idss.datalake.datashare.tenant.service.ITbDatasourceInfoService;
import com.idss.datalake.datashare.tenant.service.ITbScriptHistoryService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.EncodedResource;
import org.springframework.jdbc.datasource.init.ScriptUtils;
import ru.yandex.clickhouse.BalancedClickhouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import javax.sql.DataSource;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <p>
 * CH连接测试
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/19 0019
 */
public class ClickhouseUtil {
    private static Logger logger = LoggerFactory.getLogger(ClickhouseUtil.class);

    public static void main(String[] args) {
        String encode = Base64Encoder.encode(FileUtil.readBytes("/Users/<USER>/Documents/kbs-hive/hive.service.keytab"));
        System.out.println(encode);
        byte[] decode = Base64Decoder.decode(encode.getBytes());
        FileUtil.writeBytes(decode, "/Users/<USER>/Documents/kbs-hive/hive1.service.keytab");
        //        String url = "**********************************";
        //        //        String url = "**********************************";
        //        String username = "default";
        //        String password = "Ch@12345";
        //        //        String password = "ugOZymrL54TawiV4";
        //
        //        Connection connect = getConnect(url, username, password);
        //        System.out.println(queryDbSpaceInMB(connect, "test"));
    }

    /**
     * 查询表数据量
     *
     * @param conn
     * @param tableName
     * @return
     * @throws Exception
     */
    public static Long countTableLine(Connection conn, String tableName) throws Exception {
        Statement statement = conn.createStatement();
        // 查询表总数
        String query = "SELECT count(1) cnt FROM " + tableName;
        ResultSet resultSet = statement.executeQuery(query);
        if (resultSet.next()) {
            Long lineCount = resultSet.getLong(1);
            return lineCount;
        }
        resultSet.close();
        statement.close();
        return 0L;
    }

    public static Date queryTime(Connection conn, String dbName, String tableName, String fieldName, String type) throws Exception {
        if ("old".equals(type)) {
            Statement statement = conn.createStatement();
            String sql = "SELECT MIN(" + fieldName + ") AS latest_date FROM " + dbName + "." + tableName + " where " + fieldName + " is not null";
            ResultSet resultSet = statement.executeQuery(sql);
            if (resultSet.next()) {
                return resultSet.getDate("latest_date");
            }
            resultSet.close();
            statement.close();
        } else if ("new".equals(type)) {
            Statement statement = conn.createStatement();
            String sql = "SELECT MAX(" + fieldName + ") AS latest_date FROM " + dbName + "." + tableName + " where " + fieldName + " is not null";
            ResultSet resultSet = statement.executeQuery(sql);
            if (resultSet.next()) {
                return resultSet.getDate("latest_date");
            }
            resultSet.close();
            statement.close();
        }

        return null;
    }

    /**
     * 查询表占用空间
     *
     * @param conn
     * @param tableName
     * @return
     */
    public static BigDecimal countTableSpace(Connection conn, String dbName, String tableName) throws Exception {
        Statement statement = conn.createStatement();
        // 查询表总数
        String query = "SELECT ROUND(sum(bytes_on_disk) / (1024 * 1024), 2) cnt_mb\n" +
                "FROM system.parts\n" +
                "WHERE active\n" +
                "  AND database = '" + dbName + "'\n" +
                "  and table = '" + tableName + "' or table = '" + tableName + "_local'\n";
        ResultSet resultSet = statement.executeQuery(query);
        if (resultSet.next()) {
            return resultSet.getBigDecimal(1);
        }
        resultSet.close();
        statement.close();
        return new BigDecimal(0);
    }

    /**
     * 查询表占用空间
     *
     * @param conn
     * @param tableName
     * @return
     */
    public static Long countTableSpaceBytes(Connection conn, String dbName, String tableName) throws Exception {
        Statement statement = conn.createStatement();
        // 查询表总数
        String query = "SELECT sum(bytes_on_disk) cnt_mb\n" +
                "FROM system.parts\n" +
                "WHERE active\n" +
                "  AND database = '" + dbName + "'\n" +
                "  and table = '" + tableName + "' or table = '" + tableName + "_local'\n";
        ResultSet resultSet = statement.executeQuery(query);
        if (resultSet.next()) {
            return resultSet.getLong(1);
        }
        resultSet.close();
        statement.close();
        return 0L;
    }


    /**
     * 获取分布式表
     *
     * @param conn
     * @param dbName
     * @return
     * @throws Exception
     */
    public static List<String> queryDistributedTable(Connection conn, String dbName) throws Exception {
        List<String> tables = new ArrayList<>();
        Statement statement = conn.createStatement();
        String query = "SELECT name FROM system.tables WHERE database = '" + dbName + "' AND engine_full like 'Distributed%'";
        ResultSet resultSet = statement.executeQuery(query);

        while (resultSet.next()) {
            String tableName = resultSet.getString(1);
            tables.add(tableName);
        }

        resultSet.close();
        statement.close();
        return tables;
    }

    /**
     * 获取表DDL
     *
     * @param conn
     * @param dbName
     * @param tableName
     * @return
     * @throws Exception
     */
    public static String queryTableDDL(Connection conn, String dbName, String tableName) throws Exception {
        Statement statement = conn.createStatement();

        // 查询表的创建语句
        String query = "SELECT create_table_query FROM system.tables WHERE database = '" + dbName + "' AND name = '" + tableName + "'";
        ResultSet resultSet = statement.executeQuery(query);

        if (resultSet.next()) {
            String createTableQuery = resultSet.getString("create_table_query");
            return new String(createTableQuery.getBytes(), StandardCharsets.UTF_8);
        }
        resultSet.close();
        statement.close();
        return "";
    }

    /**
     * 获取表字段
     *
     * @param conn
     * @param tableName
     * @return
     * @throws Exception
     */
    public static List<String> queryTableField(Connection conn, String tableName) throws Exception {
        List<String> fields = new ArrayList<>();
        Statement statement = conn.createStatement();
        String query = "DESCRIBE " + tableName;
        ResultSet resultSet = statement.executeQuery(query);

        while (resultSet.next()) {
            String columnName = resultSet.getString("name");
            String dataType = resultSet.getString("type");
            fields.add(columnName);
        }

        resultSet.close();
        statement.close();
        return fields;
    }


    /**
     * 获取ClickHouse表的详细信息（字段名、字段类型、字段注释、主键信息）
     */
    public static TableInfo getTableInfo(Connection connection, String dbName, String tableName) throws Exception {
        List<ColumnInfo> columnInfoList = new ArrayList<>();
        String primaryKey = null;

        try (Statement stmt = connection.createStatement()) {
            // 查询表的字段信息
            String describeSql = "DESCRIBE TABLE " + tableName;
            ResultSet rs = stmt.executeQuery(describeSql);

            // 解析字段信息
            while (rs.next()) {
                String field = rs.getString("name");
                String type = rs.getString("type");
                String comment = rs.getString("comment");
                columnInfoList.add(new ColumnInfo(field, type, comment));
            }

            // 查询主键信息（如果有的话）
            String primaryKeySql = "SHOW CREATE TABLE " + tableName;
            ResultSet primaryKeyRs = stmt.executeQuery(primaryKeySql);
            if (primaryKeyRs.next()) {
                String createTableSql = primaryKeyRs.getString(1);
                // 解析 CREATE TABLE SQL 获取主键（如果存在）
                primaryKey = parsePrimaryKey(createTableSql);
            }

        }

        // 返回封装的表信息
        return new TableInfo(columnInfoList, primaryKey);
    }

    /**
     * 解析CREATE TABLE SQL语句，提取主键信息
     */
    private static String parsePrimaryKey(String createTableSql) {
        // 查找 PRIMARY KEY 关键字并提取其定义
        String primaryKey = null;
        int pkIndex = createTableSql.indexOf("PRIMARY KEY");
        if (pkIndex != -1) {
            int start = createTableSql.indexOf("(", pkIndex) + 1;
            int end = createTableSql.indexOf(")", pkIndex);
            if (start != -1 && end != -1) {
                primaryKey = createTableSql.substring(start, end).trim();
            }
        }
        return primaryKey;
    }

    //连接测试
    public static Connection getConnect(String url, String username, String password) {
        Connection conn = null;
        try {
            ClickHouseProperties properties = new ClickHouseProperties();
            properties.setPassword(password);
            properties.setUser(username);
            properties.setSocketTimeout(300000);
            Class.forName("ru.yandex.clickhouse.ClickHouseDriver");
            BalancedClickhouseDataSource balancedClickhouseDataSource = new BalancedClickhouseDataSource(url, properties);
            conn = balancedClickhouseDataSource.getConnection();
        } catch (Exception e) {
            logger.info("连接失败", e);
            return null;
        }
        return conn;
    }

    public static DataSource createDatasource(String url, String username, String password) {
        ClickHouseProperties properties = new ClickHouseProperties();
        properties.setUser(username);
        properties.setPassword(password);
        properties.setSocketTimeout(60000);
        BalancedClickhouseDataSource balancedClickhouseDataSource = new BalancedClickhouseDataSource(url, properties);
        balancedClickhouseDataSource.scheduleActualization(10, TimeUnit.SECONDS);
        balancedClickhouseDataSource.withConnectionsCleaning(10, TimeUnit.SECONDS);
        return balancedClickhouseDataSource;
    }

    public static void close(Connection conn) {
        try {
            conn.close();
        } catch (SQLException e) {
            logger.info("连接关闭失败");
        }
    }

    //检测库名是否重复
    public static Boolean queryDb(Connection conn, String db) {
        int flag = 0;
        Statement statement = null;
        try {
            statement = conn.createStatement();
            String queryDb = "select count(*) from system.databases where name = '%s';";
            final ResultSet resultSet = statement.executeQuery(String.format(queryDb, db));
            while (resultSet.next()) {
                flag = resultSet.getInt(1);
            }
        } catch (SQLException e) {
            logger.error("查询库失败", e);
        } finally {
            try {
                if (statement != null) {
                    statement.close();
                }
            } catch (SQLException e) {
                logger.error("关闭连接", e);
            }
        }
        if (flag == 0) {
            return true;
        } else {
            return false;
        }
    }

    //新建库
    public static void createDb(Connection conn, String db) {
        Statement statement = null;
        try {
            statement = conn.createStatement();
            String createDb = "CREATE DATABASE if not exists %s on cluster cluster_nx_copy;";
            statement.execute(String.format(createDb, db));
        } catch (SQLException e) {
            logger.error("创建库失败", e);
        } finally {
            try {
                if (statement != null) {
                    statement.close();
                }
            } catch (SQLException e) {
                logger.error("关闭连接", e);
            }
        }
    }

    //删除库
    public static void deleteDb(Connection conn, String db) {
        Statement statement = null;
        try {
            statement = conn.createStatement();
            String createDb = "DROP DATABASE %s on cluster cluster_nx_copy;";
            statement.execute(String.format(createDb, db));
        } catch (SQLException e) {
            logger.error("删除库失败", e);
        } finally {
            try {
                if (statement != null) {
                    statement.close();
                }
            } catch (SQLException e) {
                logger.error("关闭连接失败", e);
            }
        }
    }


    public static Object queryTables(Connection conn, String db) {
        Statement statement = null;
        try {
            statement = conn.createStatement();
            String queryDb = "SELECT count(*) as count from `system`.tables t  where  t.database  = '%s'  ;";
            final ResultSet resultSet = statement.executeQuery(String.format(queryDb, db));
            if (resultSet.next()) {
                String tc = resultSet.getString("count");
                if (StringUtil.isNotEmpty(tc)) {
                    return tc;
                } else {
                    return null;
                }

            }
        } catch (SQLException e) {
            logger.error("查询表失败", e);
        } finally {
            try {
                if (statement != null) {
                    statement.close();
                }
            } catch (SQLException e) {
                logger.error("关闭连接失败", e);
            }
        }
        return null;
    }

    public static Object querySpace(Connection conn, String db) {
        Statement statement = null;
        try {
            statement = conn.createStatement();
            String queryDb = "SELECT formatReadableSize(SUM(data_compressed_bytes)) as tc from `system`.parts p  where active and  p.database  = " +
                    "'%s'  ;";
            final ResultSet resultSet = statement.executeQuery(String.format(queryDb, db));
            if (resultSet.next()) {
                String tc = resultSet.getString("tc");
                if (StringUtil.isNotEmpty(tc)) {
                    return tc;
                } else {
                    return null;
                }
            }
        } catch (SQLException e) {
            logger.error("查询空间失败", e);
        } finally {
            try {
                if (statement != null) {
                    statement.close();
                }
            } catch (SQLException e) {
                logger.error("关闭连接失败", e);
            }
        }
        return null;
    }

    //查询库的存储空间，按MB
    public static BigDecimal queryDbSpaceInMB(Connection conn, String db) {
        Statement statement = null;
        try {
            statement = conn.createStatement();
            String queryDb = "select sum(t1.total_bytes_on_disk) as total_bytes from (SELECT database, table, sum(bytes_on_disk) AS " +
                    "total_bytes_on_disk FROM system.parts WHERE active and database = '" + db + "' GROUP BY database, table) t1;";
            final ResultSet resultSet = statement.executeQuery(queryDb);
            if (resultSet.next()) {
                Long tc = resultSet.getLong("total_bytes");

                if (tc != null) {
                    BigDecimal a = new BigDecimal(tc);
                    return a.divide(new BigDecimal(1024L * 1024L), 2, BigDecimal.ROUND_HALF_UP);
                } else {
                    return new BigDecimal(0);
                }
            }
        } catch (SQLException e) {
            logger.error("查询空间失败", e);
        } finally {
            try {
                if (conn != null) {
                    conn.close();
                }
                if (statement != null) {
                    statement.close();
                }
            } catch (SQLException e) {
                logger.error("关闭连接失败", e);
            }
        }
        return new BigDecimal(0);
    }

    public static void initTenantScript(Connection connection, Long tenantId, String dbName, TbCluster mysql, Long clusterId, String mysqlDb) throws Exception {
        // 格式化clickhouse脚本
        formatTenantScript(tenantId, dbName, mysql, mysqlDb);

        List<TbScriptHistory> scriptHistories = new ArrayList<>();
        ITbScriptHistoryService scriptHistoryService = (ITbScriptHistoryService) SpringUtil.getBean("tbScriptHistoryServiceImpl");
        try {
            // 查询已经执行过的脚本,如果已经执行则不需要重复执行
            List<TbScriptHistory> scriptHistoryList = scriptHistoryService.list(new QueryWrapper<TbScriptHistory>()
                    .eq("tenant_id", tenantId)
                    .eq("cluster_id", clusterId)
                    .eq("script_type", Constant.SCRIPTTYPE_SQL)
                    .eq("db_type", Constant.DB_CLICKHOUSE));
            List<String> readedFiles = scriptHistoryList.stream().map(script -> {
                return script.getScriptFile();
            }).collect(Collectors.toList());
            logger.info("租户{} 已执行的Clickhouse脚本文件{}", tenantId, JSONObject.toJSONString(readedFiles));

            // 获取clickhouse脚本文件
            File[] files = ArgusFileUtil.getSqlFileList(Constant.SCRIPTPATH + Constant.SCRIPTPATH_CLICKHOUSE + "/" + tenantId);

            if (files != null) {
                // 根据文件名排序
                List<File> fileList = Arrays.asList(files);
                fileList.sort(new Comparator<File>() {
                    @Override
                    public int compare(File o1, File o2) {
                        String o1Name = o1.getName();
                        String o2Name = o2.getName();
                        o1Name = o1Name.substring(1, o1Name.indexOf("__"));
                        o2Name = o2Name.substring(1, o2Name.indexOf("__"));

                        String o1Version = o1Name.substring(0, o1Name.indexOf("_"));
                        String o2Version = o2Name.substring(0, o2Name.indexOf("_"));

                        String o1Index = o1Name.substring(o1Name.indexOf("_") + 1);
                        String o2Index = o2Name.substring(o2Name.indexOf("_") + 1);

                        int compare = o1Version.compareTo(o2Version);
                        if (compare == 0) {
                            compare = Integer.valueOf(o1Index).compareTo(Integer.valueOf(o2Index));
                        }
                        return compare;
                    }
                });

                // 获取未执行的脚本文件
                List<File> notReadFile = new ArrayList<>();
                for (File file : fileList) {
                    String fileName = file.getName();
                    if (!readedFiles.contains(fileName)) {
                        logger.info("租户{} 需要执行的Clickhouse脚本文件{}", tenantId, fileName);
                        notReadFile.add(file);
                    }
                }

                // 执行clickhouse脚本
                if (CollectionUtils.isNotEmpty(notReadFile)) {
                    for (File file : notReadFile) {
                        logger.info("租户{} 执行脚本文件(Clickhouse){}开始...", tenantId, file.getName());
                        Resource resource = new FileSystemResource(file);
                        EncodedResource encodedResource = new EncodedResource(resource, "UTF-8");
                        TbScriptHistory scriptHistory = new TbScriptHistory();
                        scriptHistory.setClusterId(String.valueOf(clusterId));
                        scriptHistory.setScriptFile(file.getName());
                        scriptHistory.setTenantId(String.valueOf(tenantId));
                        scriptHistory.setScriptType(Constant.SCRIPTTYPE_SQL);
                        scriptHistory.setDbType(Constant.DB_CLICKHOUSE);
                        scriptHistory.setCreateTime(LocalDateTime.now());

                        try {
                            ScriptUtils.executeSqlScript(connection, encodedResource);

                            scriptHistory.setDelFlag(Constant.FLAG_UNABLE); // 执行成功
                            scriptHistories.add(scriptHistory);
                            logger.info("租户{} 执行脚本文件(Clickhouse){}结束", tenantId, file.getName());
                        } catch (Exception e) {
                            scriptHistory.setDelFlag(Constant.FLAG_ABLE);  // 执行失败
                            scriptHistory.setErrorMsg(e.getMessage());
                            scriptHistories.add(scriptHistory);
                            logger.error("租户{}初始化脚本(Clickhose){}失败", tenantId, file.getName());
                            logger.error(e.getMessage(), e);
                            break;
                        }
                    }
                    scriptHistoryService.saveBatch(scriptHistories);
                }
            }
        } catch (Exception e) {
            if (CollectionUtils.isNotEmpty(scriptHistories)) {
                scriptHistoryService.saveBatch(scriptHistories);
            }
            logger.error(e.getMessage(), e);
            throw new Exception(e.getMessage());
        }
    }

    /**
     * 结构化模板脚本
     *
     * @param tenantId
     * @param dbName
     * @param mysql
     */
    private static void formatTenantScript(Long tenantId, String dbName, TbCluster mysql, String mysqlDb) throws Exception {
        ITbScriptHistoryService scriptHistoryService = (ITbScriptHistoryService) SpringUtil.getBean("tbScriptHistoryServiceImpl");
        ITbDatasourceInfoService datasourceInfoService = (ITbDatasourceInfoService) SpringUtil.getBean("tbDatasourceInfoServiceImpl");
        Environment env = SpringUtil.getApplicationContext().getEnvironment();
        String clusterNx = env.getProperty("clickhouse.cluster_nx_copy", "cluster_nx_copy");
        String cluster1x = env.getProperty("clickhouse.cluster_1x_copy", "cluster_1x_copy");
        String cluster2x = env.getProperty("clickhouse.cluster_2x_copy", "cluster_2x_copy");

        String shardNx = env.getProperty("clickhouse.shard_for_nx_copy", "shard_for_nx_copy");
        String shard1x = env.getProperty("clickhouse.shard_for_1x_copy", "shard_for_1x_copy");
        String shard2x = env.getProperty("clickhouse.shard_for_2x_copy", "shard_for_2x_copy");

        String addr = mysql.getNodeAddress().split(",")[0];

        List<TbScriptHistory> scriptHistories = new ArrayList<>();
        try {
            // 查询已执行的模板文件
            List<TbScriptHistory> scriptHistoryList = scriptHistoryService.list(new QueryWrapper<TbScriptHistory>()
                    .eq("tenant_id", tenantId)
                    .eq("script_type", Constant.SCRIPTTYPE_TEMPLATE)
                    .eq("db_type", Constant.DB_CLICKHOUSE));
            List<String> readedFiles = scriptHistoryList.stream().map(script -> {
                return script.getScriptFile();
            }).collect(Collectors.toList());
            File[] files = ArgusFileUtil.getSqlFileList(Constant.SCRIPTPATH + Constant.SCRIPTPATH_TEMPLATE + "/");

            // 获取未执行的模板文件
            List<File> notReadFile = new ArrayList<>();
            for (File file : files) {
                String fileName = file.getName();
                if (!readedFiles.contains(fileName)) {
                    notReadFile.add(file);
                }
            }

            // 用于clickhouse的JDBC表引擎
            TbDatasourceInfo dsInfo = datasourceInfoService.getOne(new QueryWrapper<TbDatasourceInfo>()
                    .eq("tenant_id", tenantId).eq("datasource_type", Constant.DATASOURCE_TYPE_DEFAULT));
            StringBuilder builder = new StringBuilder(dsInfo.getDatasourceUrl());
            if (builder.toString().contains("?")) {
                builder.append("&user=").append(dsInfo.getDatasourceUsername()).append("&password=").append(dsInfo.getDatasourcePassword());
            } else {
                builder.append("?").append("user=").append(dsInfo.getDatasourceUsername()).append("&password=").append(dsInfo.getDatasourcePassword());
            }

            // 模板文件格式化
            if (CollectionUtils.isNotEmpty(notReadFile)) {
                for (File file : notReadFile) {
                    String filePath = file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf(File.separator));
                    filePath =
                            filePath.substring(0, filePath.lastIndexOf(File.separator)) + File.separator + Constant.SCRIPTPATH_CLICKHOUSE + File.separator + tenantId;
                    File scriptFile = new File(filePath);
                    if (!scriptFile.exists()) {
                        scriptFile.mkdirs();
                    }
                    scriptFile = new File(filePath + File.separator + file.getName());
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(file)));
                         BufferedWriter writer = new BufferedWriter(new FileWriter(scriptFile))) {
                        String line = null;
                        StringBuilder content = new StringBuilder();
                        while ((line = reader.readLine()) != null) {
                            if (line.contains("ENGINE = JDBC")) {
                                line = String.format(line, builder);
                            } else if (line.contains("ENGINE = PostgreSQL")) {
                                line = String.format(line, addr, mysqlDb, mysql.getClusterUsername(),
                                        AESUtil.defaultDecrypt(mysql.getClusterPassword()), mysqlDb);
                            } else if (line.contains("ENGINE = MySQL")) {
                                line = String.format(line, addr, mysqlDb, mysql.getClusterUsername(),
                                        AESUtil.defaultDecrypt(mysql.getClusterPassword()));
                            } else if (line.contains("ENGINE = Distributed")) {
                                line = String.format(line, dbName);
                            } else if (line.contains("MergeTree")) {
                                line = String.format(line, dbName);
                            } else if (line.contains("FROM")) {
                                line = String.format(line, dbName);
                            } else if (line.contains("CREATE VIEW")) {
                                line = String.format(line, dbName);
                            }
                            line = line.replace("cluster_nx_copy", clusterNx);
                            line = line.replace("cluster_1x_copy", cluster1x);
                            line = line.replace("cluster_2x_copy", cluster2x);
                            line = line.replace("shard_for_nx_copy", shardNx);
                            line = line.replace("shard_for_1x_copy", shard1x);
                            line = line.replace("shard_for_2x_copy", shard2x);
                            content.append(line).append("\n");
                        }
                        writer.write(content.toString());
                        writer.flush();

                        TbScriptHistory scriptHistory = new TbScriptHistory();
                        scriptHistory.setScriptFile(file.getName());
                        scriptHistory.setTenantId(String.valueOf(tenantId));
                        scriptHistory.setScriptType(Constant.SCRIPTTYPE_TEMPLATE);
                        scriptHistory.setDbType(Constant.DB_CLICKHOUSE);
                        scriptHistory.setCreateTime(LocalDateTime.now());
                        scriptHistories.add(scriptHistory);
                    } catch (Exception e) {
                        if (CollectionUtils.isNotEmpty(scriptHistories)) {
                            scriptHistoryService.saveBatch(scriptHistories);
                        }
                        logger.error(e.getMessage(), e);
                    }
                }
                scriptHistoryService.saveBatch(scriptHistories);
            }
        } catch (Exception e) {
            if (CollectionUtils.isNotEmpty(scriptHistories)) {
                scriptHistoryService.saveBatch(scriptHistories);
            }
            logger.error(e.getMessage(), e);
            throw new Exception(e.getMessage());
        }
    }


    /**
     * 执行sql
     *
     * @param conn
     * @param sql
     * @param consumer
     */
    public static void executeQuery(Connection conn, String sql, Consumer<Map<String, Object>> consumer) {
        logger.debug("执行SQL:{}", sql);
        PreparedStatement preparedStatement = null;
        try {
            preparedStatement = conn.prepareStatement(sql);
            ResultSet rs = preparedStatement.executeQuery();
            while (rs.next()) {
                int count = rs.getMetaData().getColumnCount();
                Map<String, Object> row = new HashMap();
                for (int j = 1; j <= count; ++j) {
                    row.put(rs.getMetaData().getColumnLabel(j), rs.getObject(j));
                }
                consumer.accept(row);
            }
        } catch (SQLException e) {
            logger.error("执行sql异常：{}，{}", sql, e.getMessage(), e);
        } finally {
            try {
                if (preparedStatement != null) {
                    preparedStatement.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    // 创建用户
    public static void createUser(Connection conn, String userName, String password) {
        Statement statement = null;
        try {
            statement = conn.createStatement();
            String sql = "CREATE USER IF NOT EXISTS %s IDENTIFIED BY '%s' ON CLUSTER cluster_nx_copy";
            statement.execute(String.format(sql, userName, password));
            logger.info("创建用户{}成功", userName);
        } catch (SQLException e) {
            logger.error("创建用户失败,{}", e.getMessage(), e);
        } finally {
            try {
                if (statement != null)
                    statement.close();
            } catch (SQLException e) {
                logger.error("关闭连接", e);
            }
        }
    }

    // 创建角色
    public static void createRole(Connection conn, String roleName) {
        Statement statement = null;
        try {
            statement = conn.createStatement();
            String sql = "CREATE ROLE IF NOT EXISTS %s ON CLUSTER cluster_nx_copy";
            statement.execute(String.format(sql, roleName));
            logger.info("创建角色{}成功", roleName);
        } catch (SQLException e) {
            logger.error("创建角色失败,{}", e.getMessage(), e);
        } finally {
            try {
                if (statement != null)
                    statement.close();
            } catch (SQLException e) {
                logger.error("关闭连接", e);
            }
        }
    }

    // 给角色授权
    public static void grantPrivilegesToRole(Connection conn, String dbName, String roleName) {
        Statement statement = null;
        try {
            statement = conn.createStatement();
            // 可根据需要设置为只读、只写、全部权限等
            String sql = "GRANT ALL ON %s.* TO %s ON CLUSTER cluster_nx_copy";
            statement.execute(String.format(sql, dbName, roleName));
            logger.info("给角色{}授权成功", roleName);
        } catch (SQLException e) {
            logger.error("授权失败,{}", e.getMessage(), e);
        } finally {
            try {
                if (statement != null)
                    statement.close();
            } catch (SQLException e) {
                logger.error("关闭连接", e);
            }
        }
    }

    // 把角色赋给用户
    public static void assignRoleToUser(Connection conn, String roleName, String userName) {
        Statement statement = null;
        try {
            statement = conn.createStatement();
            String sql = "GRANT %s TO %s ON CLUSTER cluster_nx_copy";
            statement.execute(String.format(sql, roleName, userName));
            logger.info("给用户{}赋角色{}成功", userName, roleName);
        } catch (SQLException e) {
            logger.error("给用户赋角色失败,{}", e.getMessage(), e);
        } finally {
            try {
                if (statement != null)
                    statement.close();
            } catch (SQLException e) {
                logger.error("关闭连接", e);
            }
        }
    }

}
