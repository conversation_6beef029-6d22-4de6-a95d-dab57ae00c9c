package com.idss.datalake.common.util;

/**
 * Copyright 2020 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2020/5/14 18:47
 * 时间选择 - 快捷时间
 */
public enum QUICK_TIME {

    TODAY("today", "今天"),
    ONEMIN("1min", "最近1分钟"),
    ONEDDAY("1day", "最近1天"),
    YESTERDAY("yesterday", "昨天"),
    THREEDAY("3day", "最近3天"),
    SEVENDAY("7day", "最近7天"),
    THISWEEK("thisweek", "本周"),
    TENMIN("10min", "最近10分钟"),
    THIRTYDAY("30day", "最近30天"),
    LASTWEEK("lastweek", "上周"),
    FIFTEENMIN("15min", "最近15分钟"),
    THREEMON("3mon", "最近3个月"),
    LASTMON("lastmonth", "上月"),
    ONEHOUR("1hour", "最近1小时"),
    ONEYEAR("1year", "最近1年"),
    THISYEAR("thisyear", "本年"),
    FOURHOUR("4hour", "最近4小时"),
    ALL("all", "所有时间"),
    LASTYEAR("lastyear", "去年"),
    TWEVERHOUR("12hour", "最近12小时"),
    THISMONTH("thismonth", "本月"),
    THIRTYMIN("30min", "最近30分钟"),
    SIXMON("6mon", "最近6个月"),
    ONE_HUNDRED_AND_EIGHTY_DAY("180day","最近180天");


    private String code;

    private String name;

    QUICK_TIME(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static QUICK_TIME getQuickTime(String quickTime){
        for(QUICK_TIME enums: QUICK_TIME.values()){
            if(enums.code.equals(quickTime)){
                return enums;
            }
        }
        return null;
    }
}
