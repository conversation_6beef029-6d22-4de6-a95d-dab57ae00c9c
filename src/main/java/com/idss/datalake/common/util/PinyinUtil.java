//package com.idss.datalake.common.util;
//
//import net.sourceforge.pinyin4j.PinyinHelper;
//import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
//import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
//import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
//
///**
// * 汉字转拼音
// *
// * Created by 追梦1819 on 2020-10-21.
// */
//public class PinyinUtil {
//
//    private static HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
//
//    private static String singleHanziToPinyin(char hanzi){
//        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
//        String[] pinyin = new String[0];
//        try {
//            pinyin = PinyinHelper.toHanyuPinyinStringArray(hanzi, format);
//            //pinyin4j规则，当转换的符串不是汉字，就返回null
//            if(pinyin == null){
//                return null;
//            }
//        } catch (BadHanyuPinyinOutputFormatCombination badHanyuPinyinOutputFormatCombination) {
//            badHanyuPinyinOutputFormatCombination.printStackTrace();
//        }
//        return pinyin[0];
//    }
//
//    public static String getStringPinYin(String pinYinStr){
//        StringBuffer sb = new StringBuffer();
//        String tempStr = null;
//        //循环字符串
//        for(int i = 0; i<pinYinStr.length(); i++) {
//
//            tempStr = singleHanziToPinyin(pinYinStr.charAt(i));
//            if(tempStr == null) {
//                //非汉字直接拼接
//                sb.append(pinYinStr.charAt(i));
//            } else {
//                sb.append(tempStr);
//            }
//        }
//        return sb.toString();
//    }
//
//    public static void main(String[] args) {
//        System.out.println(getStringPinYin("你在做什么"));
//    }
//}
