package com.idss.datalake.common.util;

import ru.yandex.clickhouse.ClickHouseDataSource;

import java.io.FileInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Properties;

public class CertificateUtils {

    public static void main(String[] args) {
        try {
            X509Certificate cert = loadCertificate("/Users/<USER>/Downloads/ca.pem");
            System.out.println("证书类型: " + type(cert));
            System.out.println("证书版本: " + version(cert));
            System.out.println("证书序列号: " + serialNumber16(cert));
            System.out.println("证书主题: " + subject(cert));
            System.out.println("证书签发者: " + issuer(cert));
            System.out.println("证书生效时间: " + validTimeStart(cert));
            System.out.println("证书失效时间: " + validTimeEnd(cert));
            System.out.println("证书签名算法: " + sigAlgName(cert));
            System.out.println("证书指纹: " + sha1Fingerprint(cert));
            System.out.println("证书SHA-256指纹: " + sha256Fingerprint(cert));
            System.out.println("证书MD5指纹: " + md5Fingerprint(cert));


            String url = "*******************************************";
            String user = "default";
            String password = "ugOZymrL54TawiV4";

            Properties properties = new Properties();
            properties.setProperty("user", user);
            properties.setProperty("password", password);
            properties.setProperty("ssl", "true");
            properties.setProperty("sslmode", "strict");
            properties.setProperty("sslrootcert", "/Users/<USER>/Downloads/ca.pem");

            try {
                ClickHouseDataSource dataSource = new ClickHouseDataSource(url, properties);
                try (Connection conn = dataSource.getConnection()) {
                    System.out.println("Connected to ClickHouse successfully!");
                    Statement statement = conn.createStatement();
                    ResultSet rs = statement.executeQuery("SHOW DATABASES");
                    while (rs.next()) {
                        System.out.println("Database: " + rs.getString(1));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 从 PEM 文件加载 X509 证书
     */
    public static X509Certificate loadCertificate(String certPath)
            throws CertificateException, IOException {

        CertificateFactory cf = CertificateFactory.getInstance("X.509");

        try (FileInputStream fis = new FileInputStream(certPath)) {
            return (X509Certificate) cf.generateCertificate(fis);
        }
    }


    /**
     * 获取证书的类型
     */
    public static String type(X509Certificate cert) throws NoSuchAlgorithmException {
        return cert.getType();
    }

    /**
     * 获取证书的版本
     */
    public static String version(X509Certificate cert) throws NoSuchAlgorithmException {
        return "V" + cert.getVersion();
    }

    /**
     * 获取证书的序列号
     */
    public static String serialNumber16(X509Certificate cert) throws NoSuchAlgorithmException {
        return cert.getSerialNumber().toString(16).toUpperCase();
    }

    /**
     * 获取证书的主体信息 (Subject)
     */
    public static String subject(X509Certificate cert) throws NoSuchAlgorithmException {
        return cert.getSubjectDN().getName();
    }

    /**
     * 获取证书的签发者信息 (Issuer)
     */
    public static String issuer(X509Certificate cert) throws NoSuchAlgorithmException {
        return cert.getIssuerDN().getName();
    }

    /**
     * 获取证书的生效时间-开始
     */
    public static String validTimeStart(X509Certificate cert) throws NoSuchAlgorithmException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(cert.getNotBefore());
    }

    /**
     * 获取证书的生效时间-结束
     */
    public static String validTimeEnd(X509Certificate cert) throws NoSuchAlgorithmException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(cert.getNotAfter());
    }

    /**
     * 获取证书的签名算法
     */
    public static String sigAlgName(X509Certificate cert) throws NoSuchAlgorithmException {
        return cert.getSigAlgName();
    }

    public static String sha1Fingerprint(X509Certificate cert) throws NoSuchAlgorithmException {
        byte[] sha1Fingerprint = getFingerprint(cert, "SHA-1");
        return bytesToHex(sha1Fingerprint);
    }

    public static String sha256Fingerprint(X509Certificate cert) throws NoSuchAlgorithmException {
        byte[] sha256Fingerprint = getFingerprint(cert, "SHA-256");
        return bytesToHex(sha256Fingerprint);
    }

    public static String md5Fingerprint(X509Certificate cert) throws NoSuchAlgorithmException {
        byte[] md5Fingerprint = getFingerprint(cert, "MD5");
        return bytesToHex(md5Fingerprint);
    }



    /**
     * 计算证书指纹
     */
    public static byte[] getFingerprint(X509Certificate cert, String algorithm)
            throws NoSuchAlgorithmException {
        try {
            MessageDigest md = MessageDigest.getInstance(algorithm);
            return md.digest(cert.getEncoded());
        } catch (Exception e) {
            throw new RuntimeException("计算指纹失败", e);
        }
    }

    /**
     * 字节数组转十六进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X:", b & 0xFF));
        }
        // 移除最后一个冒号
        if (sb.length() > 0) {
            sb.setLength(sb.length() - 1);
        }
        return sb.toString();
    }
}
