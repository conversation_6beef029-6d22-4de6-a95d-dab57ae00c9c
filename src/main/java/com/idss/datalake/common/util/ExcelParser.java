package com.idss.datalake.common.util;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

public class ExcelParser {

    private static Logger logger = LoggerFactory.getLogger(ExcelParser.class);

    /**
     * @param file   excel文件
     * @param colNum excel列数
     * @return
     */
    public static List<String[]> readExcel(File file, int colNum) {
        String fileType = file.getName().substring(file.getName().lastIndexOf(".") + 1);
        List<String[]> result;
        FileInputStream inputStream = null;
        Workbook workbook = null;
        try {
            inputStream = new FileInputStream(file);
            workbook = getWorkbook(inputStream, fileType);
            result = parseExcel(workbook, colNum);
            return result;
        } catch (Exception e) {
            logger.error("文件读取错误：", e);
            throw new RuntimeException("文件读取错误");
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                    workbook.close();
                } catch (Exception e) {
                    logger.error("关闭失败");
                }

            }
        }
    }

    public static Workbook getWorkbook(InputStream inputStream, String fileType) throws IOException {
        Workbook workbook = null;
        if (fileType.equalsIgnoreCase("xls")) {
            workbook = new HSSFWorkbook(inputStream);
        } else if (fileType.equalsIgnoreCase("xlsx")) {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    /**
     * 解析Excel数据  暂时支持单个sheet数据
     *
     * @param workbook Excel工作簿对象
     * @return 解析结果
     */
    private static List<String[]> parseExcel(Workbook workbook, int colNum) {
        List<String[]> resultDataList = new ArrayList<>();
        Sheet sheet = workbook.getSheetAt(0);
        // 校验sheet是否合法
        if (sheet == null) {
            return null;
        }
        // 获取第一行数据
        int firstRowNum = sheet.getFirstRowNum();
        Row firstRow = sheet.getRow(firstRowNum);
        if (null == firstRow) {
            logger.error("解析Excel失败，在第一行没有读取到任何数据！");
            return null;
        }

        // 解析每一行的数据，构造数据对象
        int rowStart = firstRowNum + 1;
        int rowEnd = sheet.getPhysicalNumberOfRows();
        for (int rowNum = rowStart; rowNum < rowEnd; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (null == row) {
                continue;
            }
            String[] resultData = convertRowToData(row, colNum);
            if (null == resultData) {
                logger.error("第 " + row.getRowNum() + "行数据不合法，已忽略！");
                continue;
            }
            resultDataList.add(resultData);
        }
        return resultDataList;
    }

    public static Logger getLogger() {
        return logger;
    }

    public static void setLogger(Logger logger) {
        ExcelParser.logger = logger;
    }

    /**
     * 提取每一行中需要的数据，构造成为一个结果数据对象
     * <p>
     * 当该行中有单元格的数据为空或不合法时，忽略该行的数据
     *
     * @param row 行数据
     * @return 解析后的行数据对象，行数据错误时返回null
     */
    private static String[] convertRowToData(Row row, int colNum) {
        List<String> dataList = new ArrayList<>();
        Cell cell;
        for (int i = 0; i < colNum; i++) {
            cell = row.getCell(i);
            dataList.add(convertCellValueToString(cell) == null ? "" : String.valueOf(convertCellValueToString(cell)));
        }
        return dataList.toArray(new String[dataList.size()]);
    }

    /**
     * 将单元格内容转换为字符串
     *
     * @param cell
     * @return
     */
    private static String convertCellValueToString(Cell cell) {
        if (cell == null) {
            return null;
        }
        String returnValue = null;
        switch (cell.getCellType()) {
            case NUMERIC:   //数字
                Double doubleValue = cell.getNumericCellValue();

                // 格式化科学计数法，取一位整数
                DecimalFormat df = new DecimalFormat("0");
                returnValue = df.format(doubleValue);
                break;
            case STRING:    //字符串
                returnValue = cell.getStringCellValue();
                break;
            case BOOLEAN:   //布尔
                Boolean booleanValue = cell.getBooleanCellValue();
                returnValue = booleanValue.toString();
                break;
            case BLANK:     // 空值
                break;
            case FORMULA:   // 公式
                returnValue = cell.getCellFormula();
                break;
            case ERROR:     // 故障
                break;
            default:
                break;
        }
        return returnValue;
    }
}
