package com.idss.datalake.common.util;

/**
 * <AUTHOR>
 * @description <p>元数据工具类</p>
 * @date 2024/6/18
 * @see
 */
public class MetaDataUtil {

    /**
     * 根据 元数据id、数据源类型、元数据类型、数据库名、表名 获取唯一标识,方便查询
     *
     * @param elementId      元数据id
     * @param datasourceType 数据源类型
     * @param itemType       元数据类型
     * @param databaseName   数据库名
     * @param tableName      表名
     * @return
     */
    public static String getTableUniqueId(Long elementId, String datasourceType, String itemType, String databaseName, String tableName) {
        return getFieldUniqueId(elementId, datasourceType, itemType, databaseName, tableName, "");
    }

    public static String getTableUniqueId(Long elementId, String datasourceType, String databaseName, String tableName) {
        return getTableUniqueId(elementId, datasourceType, null, databaseName, tableName);
    }

    public static String getFieldUniqueId(Long elementId, String datasourceType, String databaseName, String tableName,
                                          String fieldName) {
        return getFieldUniqueId(elementId, datasourceType, null, databaseName, tableName, fieldName);
    }

    public static String getFieldUniqueId(Long elementId, String datasourceType, String itemType, String databaseName, String tableName,
                                          String fieldName) {
        if (databaseName == null) {
            databaseName = "";
        }
        if (itemType == null) {
            itemType = "";
        }
        String md5 = MD5Util.md5Encode(elementId + "_" + datasourceType + "_" + itemType + "_" + databaseName + "_" + tableName + "_" + fieldName);
        return md5;
    }
}
