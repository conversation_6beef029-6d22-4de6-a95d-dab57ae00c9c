package com.idss.datalake.common.util;

import com.idss.datalake.datagovern.dataquality.enums.SqlTemplateColumn;
import com.idss.datalake.datagovern.metadata.model.detail.utils.ImportUtil;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ExcelUtil {

    private static Logger logger = LoggerFactory.getLogger(ExcelUtil.class);

    private static void createExcelTemplate(String filePath, String[] handers, List<String[]> downData,
                                            String[] downRows, String[] redRows, String type, HttpServletResponse response) {
        logger.info(Arrays.toString(handers));
        HSSFWorkbook wb = new HSSFWorkbook();// 创建工作薄

        // 表头样式
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        // 字体样式
        HSSFFont fontStyle = wb.createFont();
        fontStyle.setFontName("微软雅黑");
        fontStyle.setFontHeightInPoints((short) 12);
        fontStyle.setBold(true);
        style.setFont(fontStyle);

        // 指定字段的样式
        HSSFCellStyle style2 = wb.createCellStyle();
//        style.setFillBackgroundColor(new HSSFColor.RED().getIndex());
        HSSFFont redFont = wb.createFont();
        redFont.setColor(Font.COLOR_RED);
        redFont.setFontName("微软雅黑");
        redFont.setBold(true);
        redFont.setFontHeightInPoints((short) 12);

        style2.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        style2.setFont(redFont);

        // 新建sheet
        HSSFSheet assetSheet = wb.createSheet(type);
        assetSheet.createFreezePane(0, 1);

        // 生成sheet1内容
        HSSFRow rowFirst = assetSheet.createRow(0);// 第一个sheet的第一行为标题
        // 写标题
        for (int i = 0; i < handers.length; i++) {
            HSSFCell cell = rowFirst.createCell(i); // 获取第一行的每个单元格
            assetSheet.setColumnWidth(i, 4000); // 设置每列的列宽
            cell.setCellStyle(style); // 加样式
            cell.setCellValue(handers[i]); // 往单元格里写数据
        }
        // 给特定字段设置颜色
        if (redRows != null) {
            for (String redRow : redRows) {
                HSSFCell cell = rowFirst.createCell(Integer.valueOf(redRow));
                assetSheet.setColumnWidth(Integer.valueOf(redRow), 4000); // 设置每列的列宽
                cell.setCellStyle(style2); // 加样式
                cell.setCellValue(handers[Integer.valueOf(redRow)]); // 往单元格里写数据
            }
        }


        // 设置下拉框数据
        String[] arr = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S",
                "T", "U", "V", "W", "X", "Y", "Z" };
        HSSFRow row = null;
        if (downRows != null) {
            for (int r = 0; r < downRows.length; r++) {
                int index = 0;
                if ("-1".equals(downRows[r])) {
                    break;
                }
                String[] dlData = downData.get(r);// 获取下拉对象
                int colNum = Integer.parseInt(downRows[r]);
                HSSFSheet dataType = wb.createSheet("dataType" + (r + 1));
                wb.setSheetHidden(r + 1, true);
                // 1、设置有效性
                String strFormula = "dataType" + (r + 1) + "!$" + arr[index] + "$1:$" + arr[index] + "$5000"; // Sheet2第A1到A5000作为下拉列表来源数据
                dataType.setColumnWidth(r, 4000); // 设置每列的列宽
                // 设置数据有效性加载在哪个单元格上,参数分别是：从sheet2获取A1到A5000作为一个下拉的数据、起始行、终止行、起始列、终止列
                assetSheet.addValidationData(SetDataValidation(strFormula, 1, 50000, colNum, colNum)); // 下拉列表元素很多的情况
                // 2、生成sheet2内容
                for (int j = 0; j < dlData.length; j++) {
                    if (index == 0) { // 第1个下拉选项，直接创建行、列
                        row = dataType.createRow(j); // 创建数据行
                        dataType.setColumnWidth(j, 4000); // 设置每列的列宽
                        row.createCell(0).setCellValue(dlData[j]); // 设置对应单元格的值

                    } else { // 非第1个下拉选项

                        int rowCount = dataType.getLastRowNum();
                        if (j <= rowCount) { // 前面创建过的行，直接获取行，创建列
                            // 获取行，创建列
                            dataType.getRow(j).createCell(index).setCellValue(dlData[j]); // 设置对应单元格的值

                        } else { // 未创建过的行，直接创建行、创建列
                            dataType.setColumnWidth(j, 4000); // 设置每列的列宽
                            // 创建行、创建列
                            dataType.createRow(j).createCell(index).setCellValue(dlData[j]); // 设置对应单元格的值
                        }
                    }
                }
                index++;
            }
        }


        try {
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("content-Disposition", "attachment;filename=" + filePath);
            wb.write(response.getOutputStream());
            wb.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void getExcelTemplate(String fileName, String[] head, List<String[]> downData, String[] downRows, String[] redRows,
                                        HttpServletRequest request, HttpServletResponse response, String type) throws UnsupportedEncodingException {
        fileName = encodeChineseDownloadFileName(request, fileName);
        createExcelTemplate(fileName, head, downData, downRows, redRows, type, response);
    }

    /**
     * @Title: SetDataValidation @Description: 下拉列表元素很多的情况 (255以上的下拉) @param @param
     * strFormula @param @param firstRow 起始行 @param @param endRow
     * 终止行 @param @param firstCol 起始列 @param @param endCol
     * 终止列 @param @return @return HSSFDataValidation @throws
     */
    private static HSSFDataValidation SetDataValidation(String strFormula, int firstRow, int endRow, int firstCol,
                                                        int endCol) {

        // 设置数据有效性加载在哪个单元格上。四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DVConstraint constraint = DVConstraint.createFormulaListConstraint(strFormula);
        HSSFDataValidation dataValidation = new HSSFDataValidation(regions, constraint);

        dataValidation.createErrorBox("Error", "Error");
        dataValidation.createPromptBox("", null);

        return dataValidation;
    }

    /**
     * @Title: setDataValidation @Description: 下拉列表元素不多的情况(255以内的下拉) @param @param
     * sheet @param @param textList @param @param firstRow @param @param
     * endRow @param @param firstCol @param @param
     * endCol @param @return @return DataValidation @throws
     */
    private static DataValidation setDataValidation(Sheet sheet, String[] textList, int firstRow, int endRow,
                                                    int firstCol, int endCol) {

        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 加载下拉列表内容
        DataValidationConstraint constraint = helper.createExplicitListConstraint(textList);
        // DVConstraint constraint = new DVConstraint();
        constraint.setExplicitListValues(textList);

        // 设置数据有效性加载在哪个单元格上。四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList((short) firstRow, (short) endRow, (short) firstCol,
                (short) endCol);

        // 数据有效性对象
        DataValidation data_validation = helper.createValidation(constraint, regions);
        // DataValidation data_validation = new DataValidation(regions, constraint);

        return data_validation;
    }

    /**
     * @Title: getExcel @Description: 下载指定路径的Excel文件 @param @param url
     * 文件路径 @param @param fileName 文件名 @param @param response @return void @throws
     */
    public static void getExcel(String url, String fileName, HttpServletResponse response, HttpServletRequest request) {

        try {

            // 1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");

            // 2.设置文件头：最后一个参数是设置下载文件名
            response.setHeader("Content-disposition",
                    "attachment; filename=\"" + encodeChineseDownloadFileName(request, fileName + ".xls") + "\"");
//            response.setHeader("Content-Disposition", "attachment;filename="  
//                    + new String(fileName.getBytes("UTF-8"), "ISO-8859-1") + ".xls"); //中文文件名

            // 通过文件路径获得File对象
            File file = new File(url);

            FileInputStream in = new FileInputStream(file);
            // 3.通过response获取OutputStream对象(out)
            OutputStream out = new BufferedOutputStream(response.getOutputStream());

            int b = 0;
            byte[] buffer = new byte[2048];
            while ((b = in.read(buffer)) != -1) {
                out.write(buffer, 0, b); // 4.写到输出流(out)中
            }

            in.close();
            out.flush();
            out.close();

        } catch (IOException e) {
            logger.error("下载Excel模板异常", e);
        }
    }

    /**
     * @Title: encodeChineseDownloadFileName @Description:
     * TODO(这里用一句话描述这个方法的作用) @param @param request @param @param
     * pFileName @param @return @param @throws UnsupportedEncodingException @return
     * String @throws
     */
    private static String encodeChineseDownloadFileName(HttpServletRequest request, String pFileName)
            throws UnsupportedEncodingException {

        String filename = null;
        String agent = request.getHeader("USER-AGENT");
        // System.out.println("agent==========》"+agent);

        if (null != agent) {
            if (-1 != agent.indexOf("Firefox")) {// Firefox
                filename = "=?UTF-8?B?"
                        + (new String(org.apache.commons.codec.binary.Base64.encodeBase64(pFileName.getBytes("UTF-8"))))
                        + "?=";
            } else if (-1 != agent.indexOf("Chrome")) {// Chrome
                filename = new String(pFileName.getBytes(), "ISO8859-1");
            } else {// IE7+
                filename = java.net.URLEncoder.encode(pFileName, "UTF-8");
                filename = StringUtils.replace(filename, "+", "%20");// 替换空格
            }
        } else {
            filename = pFileName;
        }

        return filename;
    }

    /**
     * @Title: delFile @Description: 删除文件 @param @param filePath 文件路径 @return
     * void @throws
     */
    public static void delFile(String filePath) {
        File delFile = new File(filePath);
        delFile.delete();
    }

    public static List<String[]> readExcel(File file, int length, String type) {

        String fileType = file.getName().substring(file.getName().lastIndexOf(".") + 1, file.getName().length());
        List<String[]> result = new ArrayList<String[]>();
        FileInputStream inputStream = null;
        Workbook workbook = null;
        try {
            inputStream = new FileInputStream(file);
            workbook = getWorkbook(inputStream, fileType);
            result = parseExcel(workbook, type);

            return result;
        } catch (Exception e) {
            logger.error("文件读取错误：", e);
            throw new RuntimeException("文件读取错误");
        }
    }

    public static int parseExcelTotalLine(File file) {
        String fileType = file.getName().substring(file.getName().lastIndexOf(".") + 1, file.getName().length());
        FileInputStream inputStream = null;
        Workbook workbook = null;
        try {
            inputStream = new FileInputStream(file);
            workbook = getWorkbook(inputStream, fileType);
            Sheet sheet = workbook.getSheetAt(0);
            int row = sheet.getLastRowNum();
            if (row <= 0) {
                return 0;
            }
            return row;
        } catch (Exception e) {
            logger.error("文件读取错误：", e);
            throw new RuntimeException("文件读取错误");
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (workbook != null) {
                    workbook.close();
                }
            } catch (Exception e) {
                logger.error("关闭文件流异常:", e);
            }
        }
    }

    /**
     * 指定行数读取excel
     *
     * @param file
     * @param type
     * @return
     */
    public static List<String[]> readExcelBetween(File file, String type, int startRow, int endRow) {

        String fileType = file.getName().substring(file.getName().lastIndexOf(".") + 1, file.getName().length());
        List<String[]> result = new ArrayList<String[]>();
        FileInputStream inputStream = null;
        Workbook workbook = null;
        try {
            inputStream = new FileInputStream(file);
            workbook = getWorkbook(inputStream, fileType);
            result = parseExcelBetween(workbook, type, startRow, endRow);
            return result;
        } catch (Exception e) {
            logger.error("文件读取错误：", e);
            throw new RuntimeException("文件读取错误");
        }
    }

    public static Workbook getWorkbook(InputStream inputStream, String fileType) throws IOException {
        Workbook workbook = null;
        if (fileType.equalsIgnoreCase("xls")) {
            workbook = new HSSFWorkbook(inputStream);
        } else if (fileType.equalsIgnoreCase("xlsx")) {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    /**
     * 解析Excel数据
     *
     * @param workbook Excel工作簿对象
     * @return 解析结果
     */
    private static List<String[]> parseExcel(Workbook workbook, String type) {
        List<String[]> resultDataList = new ArrayList<>();
        // 解析sheet
//        for (int sheetNum = 0; sheetNum < workbook.getNumberOfSheets(); sheetNum++) {
        Sheet sheet = workbook.getSheetAt(0);

        // 校验sheet是否合法
        if (sheet == null) {
            return null;
        }

        // 获取第一行数据
        int firstRowNum = sheet.getFirstRowNum();
        Row firstRow = sheet.getRow(firstRowNum);
        if (null == firstRow) {
            logger.error("解析Excel失败，在第一行没有读取到任何数据！");
        }

        // 解析每一行的数据，构造数据对象
        int rowStart = firstRowNum + 1;
        int rowEnd = sheet.getPhysicalNumberOfRows();
        for (int rowNum = rowStart; rowNum < rowEnd; rowNum++) {
            Row row = sheet.getRow(rowNum);

            if (null == row) {
                continue;
            }

            String[] resultData = convertRowToData(row, type);
            if (null == resultData) {
                logger.error("第 " + row.getRowNum() + "行数据不合法，已忽略！");
                continue;
            }
            resultDataList.add(resultData);
        }
//        }

        return resultDataList;
    }

    /**
     * 按照开始行数-结束行数读取excel指定行
     *
     * @param workbook
     * @param type
     * @return
     */
    private static List<String[]> parseExcelBetween(Workbook workbook, String type, int startLine, int endLine) {
        if (startLine >= endLine) {
            return null;
        }

        List<String[]> resultDataList = new ArrayList<>();
        // 解析sheet
        Sheet sheet = workbook.getSheetAt(0);

        // 校验sheet是否合法
        if (sheet == null) {
            return null;
        }

        // 获取第一行数据
        int firstRowNum = sheet.getFirstRowNum();
        Row firstRow = sheet.getRow(firstRowNum);
        if (null == firstRow) {
            logger.error("解析Excel失败，在第一行没有读取到任何数据！");
        }

        // 解析每一行的数据，构造数据对象
        int rowStart = startLine;
        int rowEnd = endLine;
        for (int rowNum = rowStart; rowNum < rowEnd; rowNum++) {
            Row row = sheet.getRow(rowNum);

            if (null == row) {
                continue;
            }
            String[] resultData = convertRowToData(row, type);
            if (null == resultData) {
                logger.error("第 " + row.getRowNum() + "行数据不合法，已忽略！");
                continue;
            }
            resultDataList.add(resultData);
        }
        return resultDataList;
    }

    /**
     * 提取每一行中需要的数据，构造成为一个结果数据对象
     * <p>
     * 当该行中有单元格的数据为空或不合法时，忽略该行的数据
     *
     * @param row 行数据
     * @return 解析后的行数据对象，行数据错误时返回null
     */
    private static String[] convertRowToData(Row row, String type) {
        String[] resultData = null;

        Cell cell;
        if (type.equals(ElementTypeEnum.ES.getCode())) {
            resultData = new String[ImportUtil.IMPORT_ES_HEADER.length];
            for (int i = 0; i < ImportUtil.IMPORT_ES_HEADER.length; i++) {
                cell = row.getCell(i);
                resultData[i] = String.valueOf(convertCellValueToString(cell) == null ? "" : convertCellValueToString(cell));
            }
        } else if (type.equals(ElementTypeEnum.CH.getCode())) {
            resultData = new String[ImportUtil.IMPORT_CH_HEADER.length];
            for (int i = 0; i < ImportUtil.IMPORT_CH_HEADER.length; i++) {
                cell = row.getCell(i);
                resultData[i] = String.valueOf(convertCellValueToString(cell) == null ? "" : convertCellValueToString(cell));
            }
        } else if(type.equals("SQL")) {
            resultData = new String[SqlTemplateColumn.values().length];
            SqlTemplateColumn[] columns = SqlTemplateColumn.values();
            for(int i=0; i<columns.length; i++) {
                cell = row.getCell(i);
                resultData[i] = String.valueOf(convertCellValueToString(cell) == null ? "" : convertCellValueToString(cell));
            }
        }
        return resultData;
    }

    /**
     * 将单元格内容转换为字符串
     *
     * @param cell
     * @return
     */
    private static String convertCellValueToString(Cell cell) {
        if (cell == null) {
            return null;
        }
        String returnValue = null;
        switch (cell.getCellType()) {
            case NUMERIC:   //数字
                Double doubleValue = cell.getNumericCellValue();

                // 格式化科学计数法，取一位整数
                DecimalFormat df = new DecimalFormat("0");
                returnValue = df.format(doubleValue);
                break;
            case STRING:    //字符串
                returnValue = cell.getStringCellValue();
                break;
            case BOOLEAN:   //布尔
                Boolean booleanValue = cell.getBooleanCellValue();
                returnValue = booleanValue.toString();
                break;
            case BLANK:     // 空值
                break;
            case FORMULA:   // 公式
                returnValue = cell.getCellFormula();
                break;
            case ERROR:     // 故障
                break;
            default:
                break;
        }
        return returnValue;
    }
}
