package com.idss.datalake.common.util;

import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Slf4j
public class XmlUtils {

    public static Map<String, Object> xml2Map(String xml) {
        SAXReader reader = new SAXReader();
        Document document;
        try {
            document = reader.read(new ByteArrayInputStream(xml.getBytes()));
            Map<String, Object> map = xml2Map(document);
            return map;
        } catch (DocumentException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Map<String, Object> xml2Map(Document document) {
        Map<String, Object> map = new HashMap<>();
        if (document == null)
            return map;
        Element root = document.getRootElement();
        for (Iterator iterator = root.elementIterator(); iterator.hasNext(); ) {
            Element e = (Element) iterator.next();
            //System.out.println(e.getName());
            List list = e.elements();
            if (list.size() > 0) {
                map.put(e.getName(), xml2Map(e));
            } else
                map.put(e.getName(), e.getText());
        }
        return map;
    }

    /**
     * 将Element对象转为Map（String→Document→Element→Map）
     *
     * @return
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static Map xml2Map(Element e) {
        Map map = new HashMap();
        List list = e.elements();
        if (list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                Element iter = (Element) list.get(i);
                List mapList = new ArrayList();
                if (iter.elements().size() > 0) {
                    Map m = xml2Map(iter);
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if (!obj.getClass().getName().equals("java.util.ArrayList")) {
                            mapList = new ArrayList();
                            mapList.add(obj);
                            mapList.add(m);
                        }
                        if (obj.getClass().getName().equals("java.util.ArrayList")) {
                            mapList = (List) obj;
                            mapList.add(m);
                        }
                        map.put(iter.getName(), mapList);
                    } else
                        map.put(iter.getName(), m);
                } else {
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if (!obj.getClass().getName().equals("java.util.ArrayList")) {
                            mapList = new ArrayList();
                            mapList.add(obj);
                            mapList.add(iter.getText());
                        }
                        if (obj.getClass().getName().equals("java.util.ArrayList")) {
                            mapList = (List) obj;
                            mapList.add(iter.getText());
                        }
                        map.put(iter.getName(), mapList);
                    } else
                        map.put(iter.getName(), iter.getText());//公共map resultCode=0
                }
            }
        } else
            map.put(e.getName(), e.getText());
        return map;
    }

    public static void main(String[] args) {
        String xml = "<?xml version='1.0' encoding='UTF-8'?>\n" +
                "<USERRSP>\n" +
                "<HEAD>\n" +
                "\t\t<CODE>000</CODE>\n" +
                "\t\t<SID>000</SID>\n" +
                "\t\t<TIMESTAMP>YYYYMMDDHH24MMSS</TIMESTAMP>\n" +
                "\t\t<SERVICEID>应用标识</SERVICEID>\n" +
                "\t</HEAD>\n" +
                "\t<BODY>\n" +
                "\t\t\t<RSP>返回结果代码</RSP>\n" +
                "\t\t\t<MAINACCTID>当前主帐号</MAINACCTID>\n" +
                "<APPACCTID>从帐号登录名</APPACCTID>\n" +
                "</BODY>\n" +
                "</USERRSP>";
        Map<String, Object> map = (Map<String, Object>) xml2Map(xml).get("BODY");
        System.out.println("map>>> " + map);
    }
}
