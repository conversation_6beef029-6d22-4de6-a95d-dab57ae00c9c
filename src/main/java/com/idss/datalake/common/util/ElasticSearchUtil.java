package com.idss.datalake.common.util;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.Data;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.client.indices.GetIndexResponse;
import org.elasticsearch.client.indices.GetMappingsRequest;
import org.elasticsearch.client.indices.GetMappingsResponse;
import org.elasticsearch.cluster.metadata.MappingMetaData;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.metrics.Max;
import org.elasticsearch.search.aggregations.metrics.Min;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * es
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/19 0019
 */
public class ElasticSearchUtil {
    private static Logger logger = LoggerFactory.getLogger(ElasticSearchUtil.class);

    /**
     * 计算某个索引的总条数
     *
     * @param ip
     * @param port
     * @param userName
     * @param password
     * @param indexName
     * @return
     */
    public static Long indexLines(String ip, Integer port, String userName, String password, String indexName) {
        String[] split = ip.split(",");
        HttpHost[] hosts = new HttpHost[split.length];
        for (int i = 0; i < split.length; i++) {
            hosts[i] = new HttpHost(split[i], port, "http");
        }
        try (RestHighLevelClient client = new RestHighLevelClient(
                RestClient.builder(
                        hosts
                ).setHttpClientConfigCallback(httpClientBuilder -> {
                    CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    if (StringUtil.isNotEmpty(userName) && StringUtil.isNotEmpty(password)) {
                        credentialsProvider.setCredentials(AuthScope.ANY,
                                new UsernamePasswordCredentials(userName, password));
                    }
                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                })
        )) {
            SearchRequest searchRequest = new SearchRequest(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(QueryBuilders.matchAllQuery()); // Match all documents
            searchSourceBuilder.size(0); // No actual documents returned, just the count
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            return searchResponse.getHits().getTotalHits().value;
        } catch (IOException e) {
            logger.error("计算总条数失败", e);
        }
        return 0L;
    }

    public static Date queryTime(String ip, Integer port, String userName, String password, String indexName, String fieldName, String type) {
        String[] split = ip.split(",");
        HttpHost[] hosts = new HttpHost[split.length];
        for (int i = 0; i < split.length; i++) {
            hosts[i] = new HttpHost(split[i], port, "http");
        }
        try (RestHighLevelClient client = new RestHighLevelClient(
                RestClient.builder(
                        hosts
                ).setHttpClientConfigCallback(httpClientBuilder -> {
                    CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    if (StringUtil.isNotEmpty(userName) && StringUtil.isNotEmpty(password)) {
                        credentialsProvider.setCredentials(AuthScope.ANY,
                                new UsernamePasswordCredentials(userName, password));
                    }
                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                })
        )) {
            // 2. 构建查询和聚合
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.existsQuery(fieldName)); // 过滤掉字段为空的文档
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            if ("old".equals(type)) {
                searchSourceBuilder.aggregation(AggregationBuilders.min("min_date").field(fieldName));
            } else if ("new".equals(type)) {
                searchSourceBuilder.aggregation(AggregationBuilders.max("max_date").field(fieldName));
            } else {
                return null;
            }

            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.size(0); // 不需要返回文档，只需要聚合结果
            SearchRequest searchRequest = new SearchRequest(indexName);
            searchRequest.source(searchSourceBuilder);
            // 3. 执行查询
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            // 4. 处理响应结果
            if ("old".equals(type)) {
                Min minDateAggregation = searchResponse.getAggregations().get("min_date");
                if (minDateAggregation != null) {
                    double minDateValue = minDateAggregation.getValue();
                    return new Date((long) minDateValue);
                }
            } else {
                Max maxDateAggregation = searchResponse.getAggregations().get("max_date");
                if (maxDateAggregation != null) {
                    double maxDateValue = maxDateAggregation.getValue();
                    return new Date((long) maxDateValue);
                }
            }
        } catch (IOException e) {
            logger.error("查询日期失败", e);
        }
        return null;
    }

    /**
     * 查询索引使用空间
     *
     * @param ip
     * @param port
     * @param userName
     * @param password
     * @param indexName
     * @return
     */
    public static BigDecimal indexSpaceInMB(String ip, Integer port, String userName, String password, String indexName) {
        long storageSize = indexSpaceInBytes(ip, port, userName, password, indexName);
        if (storageSize == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal decimal = new BigDecimal(storageSize);
        return decimal.divide(new BigDecimal(1024L * 1024L), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 查询索引使用空间
     *
     * @param nodes     ip:port,ip:port
     * @param userName
     * @param password
     * @param indexName
     * @return
     */
    public static BigDecimal indexSpaceInMB(String nodes, String userName, String password, String indexName) {
        long storageSize = indexSpaceInBytes(nodes, userName, password, indexName);
        if (storageSize == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal decimal = new BigDecimal(storageSize);
        return decimal.divide(new BigDecimal(1024L * 1024L), 2, BigDecimal.ROUND_HALF_UP);
    }

    public static long indexSpaceInBytes(String nodes, String userName, String password, String indexName) {
        HttpHost[] hosts = getHttpHosts(nodes);
        Long storageSize = getStorageSize(userName, password, indexName, hosts);
        if (storageSize != null)
            return storageSize;
        return 0L;
    }

    private static HttpHost[] getHttpHosts(String nodes) {
        String[] split = nodes.split(",");
        HttpHost[] hosts = new HttpHost[split.length];
        for (int i = 0; i < split.length; i++) {
            String[] node = split[i].split(":");
            hosts[i] = new HttpHost(node[0], Integer.parseInt(node[1]), "http");
        }
        return hosts;
    }

    public static long indexSpaceInBytes(String ip, Integer port, String userName, String password, String indexName) {
        String[] split = ip.split(",");
        HttpHost[] hosts = new HttpHost[split.length];
        for (int i = 0; i < split.length; i++) {
            hosts[i] = new HttpHost(split[i], port, "http");
        }
        Long storageSize = getStorageSize(userName, password, indexName, hosts);
        if (storageSize != null) {
            return storageSize;
        }
        return 0L;
    }

    private static Long getStorageSize(String userName, String password, String indexName, HttpHost[] hosts) {
        try (RestHighLevelClient client = new RestHighLevelClient(
                RestClient.builder(hosts).setHttpClientConfigCallback(httpClientBuilder -> {
                    CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    if (StringUtil.isNotEmpty(userName) && StringUtil.isNotEmpty(password)) {
                        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(userName, password));
                    }
                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                }))) {
            Request request = new Request("GET", "/" + indexName + "/_stats/store");
            Response response = client.getLowLevelClient().performRequest(request);
            String responseBody = EntityUtils.toString(response.getEntity());
            JsonObject json = JsonParser.parseString(responseBody).getAsJsonObject();
            JsonObject indexStats = json.getAsJsonObject("_all").getAsJsonObject("total").getAsJsonObject("store");
            long storageSize = indexStats.get("size_in_bytes").getAsLong();
            return storageSize;
        } catch (IOException e) {
            logger.error("计算空间失败,{}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 查询所有的索引名称
     *
     * @param nodes    ip:port,ip:port
     * @param userName
     * @param password
     * @return
     */
    public static List<String> indexNames(String nodes, String userName, String password) {
        List<String> result = new ArrayList<>();
        HttpHost[] hosts = getHttpHosts(nodes);
        try (RestHighLevelClient client = new RestHighLevelClient(
                RestClient.builder(
                        hosts
                ).setHttpClientConfigCallback(httpClientBuilder -> {
                    CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    if (StringUtil.isNotEmpty(userName) && StringUtil.isNotEmpty(password)) {
                        credentialsProvider.setCredentials(AuthScope.ANY,
                                new UsernamePasswordCredentials(userName, password));
                    }
                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                })
        )) {
            // 获取所有索引
            GetIndexRequest request = new GetIndexRequest("*");
            GetIndexResponse response = client.indices().get(request, RequestOptions.DEFAULT);
            String[] indices = response.getIndices();

            // 打印索引名称
            for (String index : indices) {
                result.add(index);
            }
        } catch (Exception e) {
            logger.error("获取索引失败", e);
        }
        return result;
    }

    /**
     * 查询索引下的字段信息
     *
     * @param nodes     ip:port,ip:port
     * @param userName
     * @param password
     * @param indexName
     * @return
     */
    public static List<String> indexFields(String nodes, String userName, String password, String indexName) {
        List<String> result = new ArrayList<>();
        HttpHost[] hosts = getHttpHosts(nodes);
        try (RestHighLevelClient client = new RestHighLevelClient(
                RestClient.builder(
                        hosts
                ).setHttpClientConfigCallback(httpClientBuilder -> {
                    CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    if (StringUtil.isNotEmpty(userName) && StringUtil.isNotEmpty(password)) {
                        credentialsProvider.setCredentials(AuthScope.ANY,
                                new UsernamePasswordCredentials(userName, password));
                    }
                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                })
        )) {
            // 获取索引的映射信息
            logger.info("查询索引字段:{}", indexName);
            GetMappingsRequest request = new GetMappingsRequest().indices(indexName);
            GetMappingsResponse response = client.indices().getMapping(request, RequestOptions.DEFAULT);
            MappingMetaData mapping = response.mappings().get(indexName);

            // 获取字段信息
            Map<String, Object> properties = mapping.getSourceAsMap();
            Map<String, Object> fields = (Map<String, Object>) properties.get("properties");
            if (fields != null) {
                // 打印字段名称
                for (Map.Entry<String, Object> entry : fields.entrySet()) {
                    String fieldName = entry.getKey();
                    result.add(fieldName);
                }
            }
        } catch (Exception e) {
            logger.error("获取索引失败", e);
        }
        return result;
    }

    /**
     * 查询索引的配置信息
     *
     * @param nodes     ip:port,ip:port
     * @param userName
     * @param password
     * @param indexName
     * @return
     */
    public static IndexInfo indexSettings(String nodes, String userName, String password, String indexName) {
        IndexInfo indexInfo = new IndexInfo();
        HttpHost[] hosts = getHttpHosts(nodes);
        try (RestHighLevelClient client = new RestHighLevelClient(
                RestClient.builder(
                        hosts
                ).setHttpClientConfigCallback(httpClientBuilder -> {
                    CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    if (StringUtil.isNotEmpty(userName) && StringUtil.isNotEmpty(password)) {
                        credentialsProvider.setCredentials(AuthScope.ANY,
                                new UsernamePasswordCredentials(userName, password));
                    }
                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                })
        )) {
            // 创建 GetIndexRequest 请求
            GetIndexRequest request = new GetIndexRequest(indexName);

            // 执行请求
            GetIndexResponse response = client.indices().get(request, RequestOptions.DEFAULT);

            // 获取分区数
            int numberOfShards = response.getSettings().get(indexName).getAsInt("index.number_of_shards", 1);
            indexInfo.setNumberOfShards(numberOfShards);
            // 获取副本数
            int numberOfReplicas = response.getSettings().get(indexName).getAsInt("index.number_of_replicas", 1);
            indexInfo.setNumberOfReplicas(numberOfReplicas);
            // 获取 mapping JSON
            indexInfo.setMappingJSON(response.getMappings().get(indexName).source().toString());
        } catch (Exception e) {
            logger.error("获取索引失败", e);
        }
        return indexInfo;
    }

    /**
     * 创建索引
     *
     * @param nodes            ip:port,ip:port
     * @param userName
     * @param password
     * @param indexName
     * @param numberOfShards
     * @param numberOfReplicas
     * @param mappingJSON
     */
    public static void createIndex(String nodes, String userName, String password, String indexName, int numberOfShards,
                                   int numberOfReplicas, String mappingJSON) {
        HttpHost[] hosts = getHttpHosts(nodes);
        try (RestHighLevelClient client = new RestHighLevelClient(
                RestClient.builder(
                        hosts
                ).setHttpClientConfigCallback(httpClientBuilder -> {
                    CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    if (StringUtil.isNotEmpty(userName) && StringUtil.isNotEmpty(password)) {
                        credentialsProvider.setCredentials(AuthScope.ANY,
                                new UsernamePasswordCredentials(userName, password));
                    }
                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                })
        )) {
            JSONObject data = new JSONObject();
            JSONObject mappings = JSONObject.parseObject(mappingJSON);
            JSONObject settings = new JSONObject();
            settings.put("number_of_shards", numberOfShards);
            settings.put("number_of_replicas", numberOfReplicas);
            data.put("settings", settings);
            data.put("mappings", mappings);

            // 创建索引的请求
            CreateIndexRequest request = new CreateIndexRequest(indexName);
            request.source(data.toJSONString(), XContentType.JSON);

            // 执行索引创建请求
            CreateIndexResponse response = client.indices().create(request, RequestOptions.DEFAULT);
            if (response.isAcknowledged()) {
                logger.info("创建索引成功");
            }
        } catch (Exception e) {
            logger.error("获取索引失败", e);
        }
    }

    @Data
    public static class IndexInfo {
        private int numberOfShards;
        private int numberOfReplicas;
        private String mappingJSON;
    }

    public static void main(String[] args) {
        String urlString = "http://***********:9200/";

        try {
            URL url = new URL(urlString);
            String host = url.getHost();
            int port = url.getPort(); // 返回端口号，如果未指定则返回 -1

            System.out.println("Host: " + host);
            System.out.println("Port: " + (port != -1 ? port : url.getDefaultPort())); // 如果端口号未指定，则使用默认端口号
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
    }

    public static void selectFieldByAlias(String aliasName) {
        RestHighLevelClient client =
                new RestHighLevelClient(RestClient.builder(
                        new HttpHost("************", 9200, "http"),
                        new HttpHost("************", 9200, "http"),
                        new HttpHost("************", 9200, "http")));
        try {
            CountRequest countRequest = new CountRequest(aliasName);
            CountResponse count = client.count(countRequest, RequestOptions.DEFAULT);
            System.out.println(count.getCount());


            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.from(0);
            sourceBuilder.size(1);
            sourceBuilder.sort("create_time", SortOrder.DESC);
            SearchRequest request = new SearchRequest();
            request.indices(aliasName);
            request.source(sourceBuilder);
            SearchResponse response = client.search(request, RequestOptions.DEFAULT);
            System.out.println(response.getHits().getHits().length);
        } catch (IOException e) {
            logger.error("选择字段失败", e);
        } finally {
            try {
                client.close();
            } catch (IOException e) {
                logger.error("关闭连接失败", e);
            }
        }
    }

}
