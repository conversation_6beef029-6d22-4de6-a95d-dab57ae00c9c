
/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
** Product VERSION,UPDATED BY,UPDATE DATE                                     *
*   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* create 注释
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* M  {modifyComment}
* -  {delComment}
* +  {addCommnet}
\*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.common.util;

import org.apache.commons.lang.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;


/**
 * DOCUMENT ME!
 *
 * <AUTHOR>
 * @version $Revision$
  */
public abstract class DateStandardUtil {
  /**
   * 日期精度，秒
   */
  public static final int ACCURACY_SECOND = 1;

  /**
   * 日期精度，分
   */
  public static final int ACCURACY_MINUTE = 2;

  /**
   * 日期精度，小时
   */
  public static final int ACCURACY_HOUR = 3;

  /**
   * 日期精度，天
   */
  public static final int ACCURACY_DAY = 4;

  /**
   * 日期精度，月
   */
  public static final int ACCURACY_MONTH = 5;

  /**
   * 日期精度，年
   */
  public static final int ACCURACY_YEAR = 6;

  /**
   * DOCUMENT ME!
   */
  private static final long MILLISECONDS_PER_SECOND = 1000;

  /**
   * DOCUMENT ME!
   */
  private static final long MILLISECONDS_PER_MINUTE = 1000 * 60;

  /**
   * DOCUMENT ME!
   */
  private static final long MILLISECONDS_PER_HOUR = 1000 * 60 * 60;

  /**
   * DOCUMENT ME!
   */
  private static final long MILLISECONDS_PER_DAY = 1000 * 60 * 60 * 24;

  /**
   * DOCUMENT ME!
   */
  public static final String YYYY_MM_DD_HH_MM_SS_PATTERN = "yyyy-MM-dd HH:mm:ss";

  public static final String YYYYMMDDHHMMSS_PATTEN = "yyyyMMddHHmmss";

  /**
   * 舍弃时分秒
   *
   * @param date
   *            带时分秒的日期对象
   * @return 不带时分秒的日期对象
   */
  public static Date floor(Date date) {
    return parse(format(date, "yyyy-MM-dd"));
  }

  /**
   * 格式化日期输出,如果传入日期为空,返回0长度的字符串
   *
   * @param date
   *            日期
   * @param pattern
   *            日期格式字符串
   * @return 日期字符串
   */
  public static String format(Date date, String pattern) {
    if (date == null) {
      return "";
    }

    DateFormat df = new SimpleDateFormat(pattern);

    return df.format(date);
  }

  /**
   * 调整日期的时间 按照天数
   *
   * @param date
   * @param amount
   * @return
   */
  public static Date adjustDay(Date date, int amount) {
    Calendar calendar = dateToCalendar(date);
    calendar.add(Calendar.DAY_OF_MONTH, amount);

    return calendar.getTime();
  }

  /**
   * DOCUMENT ME!
   *
   * @param date DOCUMENT ME!
   * @param amount DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public static Date adjustMonth(Date date, int amount) {
    Calendar calendar = dateToCalendar(date);
    calendar.add(Calendar.MONTH, amount);

    return calendar.getTime();
  }

  /**
   * DOCUMENT ME!
   *
   * @param date DOCUMENT ME!
   * @param amount DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public static Date adjustYear(Date date, int amount) {
    Calendar calendar = dateToCalendar(date);
    calendar.add(Calendar.YEAR, amount);

    return calendar.getTime();
  }

  /**
   * DOCUMENT ME!
   *
   * @param date DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  private static Calendar dateToCalendar(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);

    return calendar;
  }

  /**
   * DOCUMENT ME!
   *
   * @param dateStr DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public static Date parseDate(String dateStr) {
    return parse(dateStr);
  }

  /**
   * DOCUMENT ME!
   *
   * @param dateStr DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public static Date parseDateEndWithMs(String dateStr) {
    if (dateStr.endsWith(".000")) {
      dateStr = dateStr.substring(0, dateStr.length() - 4);
    }

    return parse(dateStr);
  }

  /**
   * 日期格式字符串转换为日期，如果传入字符串为空白，返回null
   *
   * @param dateStr
   *            日期字符串
   * @return 日期
   */
  public static Date parse(String dateStr) {
    if (StringUtils.isBlank(dateStr)) {
      return null;
    }

    try {
      if ((dateStr.length() == 8) && (dateStr.indexOf('-') < 0) && (dateStr.indexOf('/') < 0)) {
        return org.apache.commons.lang.time.DateUtils.parseDate(dateStr, new String[] {
            "yyyyMMdd"
          });
      } else {
        return org.apache.commons.lang.time.DateUtils.parseDate(dateStr, new String[] {
            "yyyyMMddHHmmss", "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss",
            "yyyyMMdd HH:mm:ss", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss"
          });
      }
    } catch (ParseException parseEcp) {
      throw new RuntimeException("Not supported date format:" + dateStr);
    }
  }

  /**
   * 根据指定格式，返回日期时间字符串
   *
   * @param date
   *            日期变量
   * @param pattern
   * @return 日期时间字符串
   */
  public static String getDateStr(Date date, String pattern) {
    DateFormat df = new SimpleDateFormat(pattern);

    return df.format(date);
  }

  /**
   * DOCUMENT ME!
   *
   * @param date DOCUMENT ME!
   * @param pattern DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public static Date transDateFormat(Date date, String pattern) {
    String dateStr = getDateStr(date, pattern);

    return parse(dateStr);
  }

  /**
   * DOCUMENT ME!
   *
   * @param a DOCUMENT ME!
   * @param b DOCUMENT ME!
   * @param unit DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public static int getDateDistance(Date a, Date b, int unit) {
    int result = 0;

    if ((null != a) && (null != b)) {
      String pattern = null;

      switch (unit) {
      case ACCURACY_HOUR: // '\003'
        pattern = "yyyyMMddHH";

        break;

      case ACCURACY_MINUTE: // '\002'
        pattern = "yyyyMMddHHmm";

        break;

      case ACCURACY_SECOND: // '\001'
        pattern = "yyyyMMddHHmmss";

        break;

      default:
        pattern = "yyyyMMdd";
      }

      Date startDate = transDateFormat((a.compareTo(b) <= 0) ? a : b, pattern);
      Date endDate = transDateFormat((a.compareTo(b) <= 0) ? b : a, pattern);

      if ((1 <= unit) && (4 >= unit)) {
        result = getDistanceByUnit(startDate, endDate, unit);

        return result;
      }

      GregorianCalendar startCalendar = new GregorianCalendar();
      startCalendar.setTime(startDate);

      int startYears = startCalendar.get(Calendar.YEAR);
      int startMonths = startCalendar.get(Calendar.MONTH);
      int startDays = startCalendar.get(Calendar.DAY_OF_MONTH);

      GregorianCalendar endCalendar = new GregorianCalendar();
      endCalendar.setTime(endDate);

      int endYears = endCalendar.get(Calendar.YEAR);
      int endMonths = endCalendar.get(Calendar.MONTH);
      int endDays = endCalendar.get(Calendar.DAY_OF_MONTH);

      int yearBetween = endYears - startYears;
      int monthBetween = endMonths - startMonths;

      if ((endDays < startDays) && (endDays != endCalendar.getActualMaximum(Calendar.DATE))) {
        monthBetween--;
      }

      if (ACCURACY_YEAR == unit) {
        if (monthBetween < 0) {
          yearBetween--;
        }

        result = yearBetween;
      }

      if (ACCURACY_MONTH == unit) {
        result = ((yearBetween * 12) + monthBetween);
      }
    }

    return result;
  }

  /**
   * 调整日期
   *
   * @param date
   *            原日期
   * @param amount
   *            调整数
   * @param flag
   *            调整单位 A-岁 Y-年 M-月 D-填
   * @param birthday
   *            出生日期
   * @return 调整后的日期
   */
  public static Date adjustDate(Date date, int amount, String flag, Date birthday) {
    Calendar calendar = dateToCalendar(date);

    if (flag.equalsIgnoreCase("y")) {
      calendar.add(Calendar.YEAR, amount);
    } else if (flag.equalsIgnoreCase("m")) {
      calendar.add(Calendar.MONTH, amount);
    } else if (flag.equalsIgnoreCase("d")) {
      calendar.add(Calendar.DAY_OF_MONTH, amount);
    } else if (flag.equalsIgnoreCase("a")) {
      int age = getDateDistance(date, birthday, ACCURACY_YEAR);
      calendar.add(Calendar.YEAR, amount - age);
    }

    return calendar.getTime();
  }

  /**
   * DOCUMENT ME!
   *
   * @param startDate DOCUMENT ME!
   * @param endDate DOCUMENT ME!
   * @param unit DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  private static int getDistanceByUnit(Date startDate, Date endDate, int unit) {
    int result = 0;
    long millisecondPerUnit = MILLISECONDS_PER_DAY;

    switch (unit) {
    case ACCURACY_HOUR:
      millisecondPerUnit = MILLISECONDS_PER_HOUR;

      break;

    case ACCURACY_MINUTE:
      millisecondPerUnit = MILLISECONDS_PER_MINUTE;

      break;

    case ACCURACY_SECOND:
      millisecondPerUnit = MILLISECONDS_PER_SECOND;

      break;

    default:
      break;
    }

    long start = startDate.getTime();
    long end = endDate.getTime();
    long distance = end - start;
    result = Integer.valueOf((distance / millisecondPerUnit) + "");

    return result;
  }

  /**
   * 月底取齐
   *
   * @param srcDate
   *            参照日期
   * @param targetDate
   *            目标日期
   * @return 月底取齐后的目标日期
   */
  public static Date evenMonthEnd(final Date srcDate, final Date targetDate) {
    Date rsltDate = targetDate;
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(srcDate);

    if (calendar.get(Calendar.DAY_OF_MONTH) == calendar.getActualMaximum(Calendar.DAY_OF_MONTH)) {
      calendar.setTime(rsltDate);
      calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
      rsltDate = calendar.getTime();
    }

    return rsltDate;
  }

  /**
   * DOCUMENT ME!
   *
   * @param value DOCUMENT ME!
   * @param type DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public static Long conversionTimeRange(Integer value, String type) {
    //转成毫秒
    if ("m".equals(type)) {
      return Long.valueOf(value * 60 * 1000);
    } else if ("h".equals(type)) {
      return Long.valueOf(value * 60 * 60 * 1000);
    } else if ("d".equals(type)) {
      return Long.valueOf(value * 24 * 60 * 60 * 1000);
    } else {
      return Long.valueOf("0");
    }
  }

  /**
   * DOCUMENT ME!
   *
   * @param lastDate DOCUMENT ME!
   * @param currentDate DOCUMENT ME!
   * @param timeRange DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public static Integer compareToOther(Date lastDate, Date currentDate, Long timeRange) {
    Long preTime = lastDate.getTime();
    Long nowTime = currentDate.getTime();

    if (((preTime + timeRange) - nowTime) > 0) {
      return -1; //表示之前的时间目前还在在设置的时间范围内
    }

    return 1;
  }

  /**
   * DOCUMENT ME!
   *
   * @param startTime DOCUMENT ME!
   * @param endTime DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public static Integer compareToNow(Date startTime, Date endTime) {
    if ((startTime == null) || (endTime == null)) {
      return 1;
    }

    Long longStartTime = startTime.getTime();
    Long longEndTime = endTime.getTime();

    if (longStartTime > longEndTime) {
      return -2;
    }

    Long now =System.currentTimeMillis();

    if ((longStartTime <= now) && (longEndTime >= now)) {
      return 1;
    } else if (longStartTime > now) {
      return 0;
    } else if (longEndTime < now) {
      return -1;
    }

    return -1;
  }
}
