/**
 * Copyright (C), 2015-2018, 上海观安信息技术股份有限公司
 * Author:   yangpy
 * Date:     2018/8/7 16:12
 * Description:
 */
package com.idss.datalake.common.util;

import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;


/**
 * <AUTHOR>
 * @create 2018/8/7
 */
public class DateUtil {
    /**
     * 将10 or 13 位时间戳转为时间字符串
     *
     * @param strDate
     * @return
     */
    public static String timestampDate(String strDate) {
        if (StringUtils.isEmpty(strDate)) {
            return strDate;
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if (strDate.length() == 13) {
            String date = sdf.format(new Date(toLong(strDate)));

            return date;
        } else {
            String date = sdf.format(new Date(toLong(strDate) * 1000L));

            return date;
        }
    }

    /***
     * 数字日期转字符串
     *
     * @param format
     * @param timeStamp
     * @return
     */
    public static String isValidDate(FormatedTime format, Long timeStamp) {
        Date date = new Date(timeStamp);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format.getFormatStr());

        return simpleDateFormat.format(date);
    }

    /***
     * date转字符串
     *
     * @param format
     * @param date
     * @return
     */
    public static String isValidDate(FormatedTime format, Date date) {
        if (date == null) {
            return null;
        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format.getFormatStr());

        return simpleDateFormat.format(date);
    }

    /***
     * date转字符串
     *
     * @param date
     * @return
     */
    public static String isValidDate(Date date) {
        return isValidDate(FormatedTime.HYPHEN_YYYYMMDD_HHMMSS, date);
    }

    /**
     * String转long
     *
     * @param obj
     * @return 转换异常返回 0
     */
    public static long toLong(String obj) {
        try {
            return Long.parseLong(obj);
        } catch (Exception e) {
        }

        return 0;
    }

    /**
     * DOCUMENT ME!
     *
     * @param format  DOCUMENT ME!
     * @param dataStr DOCUMENT ME!
     * @return DOCUMENT ME!
     * @throws ParseException DOCUMENT ME!
     */
    public static Date formatDate(FormatedTime format, String dataStr) throws ParseException {
        if (StringUtils.isEmpty(dataStr)) {
            return null;
        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format.getFormatStr());

        return simpleDateFormat.parse(dataStr);
    }

    /**
     * add by zhouan 2021-04-26
     * Date转LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime Date2LocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zoneId);

        return localDateTime;
    }

    /**
     * 对比时间是否在一段时间区间内
     *
     * @param compareTime 对比时间
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 在区间内，返回true，否则返回false
     */
    public static boolean timeBetween(LocalDateTime compareTime, LocalDateTime startTime, LocalDateTime endTime) {
        if (compareTime.isAfter(startTime) && compareTime.isBefore(endTime)) {
            return true;
        }

        return false;
    }

    /**
     * 根据输入的日期字符串，获取与现在时间相差的天数
     *
     * @param time
     * @return Long
     */
    public static Integer getTimeDifference(Long time) throws Exception {
        Integer difference =
                Integer.parseInt(Math.abs((System.currentTimeMillis() - time) / (24 * 60 * 60 * 1000)) + "");
        return difference;
    }

    /**
     * 新增LocalDateTime转Date方法
     *
     * @param time
     * @return
     */
    public static Date LocalDateTime2Date(LocalDateTime time) {
        return Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String[] convertTimeRange(Integer value, String type) {
        if (value == null || StringUtils.isEmpty(type)) {
            return null;
        }
        String[] timeRange = new String[2];
        String startTime = "";
        String endTime = "";
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        endTime = format.format(date);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if ("h".equals(type)) {
            calendar.add(Calendar.HOUR, -value);
        } else if ("d".equals(type)) {
            calendar.add(Calendar.DATE, -value);
        } else if ("m".equals(type)) {
            calendar.add(Calendar.MINUTE, -value);
        } else {
            calendar.add(Calendar.DATE, -1);
        }
        startTime = format.format(calendar.getTime());

        timeRange[0] = startTime;
        timeRange[1] = endTime;
        return timeRange;
    }

    public static Date now(){
        return new Date();
    }

    public static String getTimeStr(Date date){
        return getTimeStr(date,null);
    }
    public static String getTimeStr(Date date,String format){
        if(StringUtils.isEmpty(format)){
            format = DateStandardUtil.YYYYMMDDHHMMSS_PATTEN;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

}
