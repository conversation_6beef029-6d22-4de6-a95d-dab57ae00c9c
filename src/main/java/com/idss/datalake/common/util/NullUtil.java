package com.idss.datalake.common.util;

import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created by yangpy on 2018/3/13.
 */
public class NullUtil {

    public static boolean strIsNull(String str) {

        return str == null || "".equals(str.trim());
    }

    public static boolean arrayIsNull(Object[] objs) {
        return objs == null || objs.length == 0;
    }

    public static boolean isNull(Collection c) {
        return c == null || c.isEmpty();
    }

    public static boolean isNull(Map map) {
        return map == null || map.isEmpty();
    }

    public static boolean isEmpty(Object obj){
        if(StringUtils.isEmpty(obj)){
            return true;
        }
        if((obj instanceof String)){
            return ((String) obj).trim().equals("");
        }
        if(obj instanceof List){
            return ((List) obj).size() == 0;
        }
        if(obj instanceof Map){
            return ((Map) obj).size() == 0;
        }
        return false;
    }
    public static boolean isNotEmpty(Object obj){
        return !isEmpty(obj);
    }

    public static boolean isNull(Object obj){
        if(String.valueOf(obj).equals("null")){
            return true;
        }
        return false;
    }
}
