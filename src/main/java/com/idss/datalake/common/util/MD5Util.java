/**
 * Copyright (C), 2015-2018, 上海观安信息技术股份有限公司
 * Author:   yangpy
 * Date:     2018/8/7 14:56
 * Description:
 */
package com.idss.datalake.common.util;

import org.apache.shiro.crypto.hash.SimpleHash;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 *
 * <AUTHOR>
 * @create 2018/8/7
 */
public class MD5Util {

    /**
     * 加密算法
     */
    public final static String md5AlgorithmName = "MD5";

    /**
     * 循环次数
     */
    public final static int md5Iterations = 8;

    /***
     * MD5加密算法
     * @param password
     * @param salt
     * @return
     */
    public static String md5Encode(String password, String salt) {
        return new SimpleHash(md5AlgorithmName, password, salt, md5Iterations).toString();
    }

    public static void main(String[] args) {
        System.out.println(md5Encode(AESUtil.defaultEncrypt("admin")));
    }

    public static String md5Encode(String str){
        byte[] digest = null;
        try {
            MessageDigest md5 = MessageDigest.getInstance("md5");
            digest  = md5.digest(str.getBytes("utf-8"));
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //16是表示转换为16进制数
        String md5Str = new BigInteger(1, digest).toString(16);
        return md5Str;
    }
}
