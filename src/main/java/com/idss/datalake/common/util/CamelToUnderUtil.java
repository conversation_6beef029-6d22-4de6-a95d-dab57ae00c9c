package com.idss.datalake.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class CamelToUnderUtil {

    /**
     * 驼峰转下划线字段名 排序用
     * @param name  每一个字段名
     * @return
     */
    private static String underscoreName(String name) {
        String suffix = "";
        String tempStr = name;
        StringBuilder result = new StringBuilder();
        if (name != null && name.length() > 0) {
            // 处理第一个字符，如果是-则需要在最末尾追加 desc ，否则 需要追加 asc
            if(name.startsWith("-")){
                suffix = " desc";
                tempStr = name.substring(1);
            }else {
                suffix = " asc";
            }
            // 循环处理其余字符
            for (int i = 0; i < tempStr.length(); i++) {
                String s = tempStr.substring(i, i + 1);
                // 在大写字母前添加下划线
                if (s.equals(s.toUpperCase()) && !Character.isDigit(s.charAt(0))) {
                    result.append("_");
                }
                // 其他字符直接转成大写
                result.append(s.toUpperCase());
            }
        }
        result.append(suffix);

        return result.toString();
    }

    public static String underField(String field){
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < field.length(); i++) {
            // 循环处理其余字符
            String s = field.substring(i, i + 1);
            // 在大写字母前添加下划线
            if (s.equals(s.toUpperCase()) && !Character.isDigit(s.charAt(0))) {
                result.append("_");
            }
            // 其他字符直接转成小写
            result.append(s.toLowerCase());
        }
        return result.toString();
    }

    /**
     * 驼峰转下划线字段名 排序用
     * @param sorts  每一个字段名
     * @return
     */
    public static String getSortString(List<String> sorts) {
        StringBuilder result = new StringBuilder();
        if(sorts== null ) {
            return null;
        }
        // 循环处理
        for (int i = 0; i < sorts.size(); i++) {
            String s = underscoreName(sorts.get(i));
            result.append(s.toUpperCase());
            if(i != sorts.size() -1){
                result.append(",");
            }
        }
        return result.toString();
    }
    public static String getOrderField(String orderField,String orderType,String tbPres) { 
        StringBuilder result = new StringBuilder();
        if(StringUtils.isEmpty(orderField)) {
            return null;
        }
        // 循环处理
        for (int i = 0; i < orderField.length(); i++) {
        	// 循环处理其余字符
            String s = orderField.substring(i, i + 1);
            // 在大写字母前添加下划线
            if (s.equals(s.toUpperCase()) && !Character.isDigit(s.charAt(0))) {
                result.append("_");
            }
            // 其他字符直接转成大写
            result.append(s.toUpperCase());
        }
        if(StringUtils.isNotEmpty(tbPres)) {
        	tbPres = tbPres + ".";
        }
        return " order by " + tbPres+result.toString() + " " + orderType;
    }

    public static String getOrderFieldWithOutOrderBy(String orderField,String orderType,String tbPres) {
        StringBuilder result = new StringBuilder();
        if(StringUtils.isEmpty(orderField)) {
            return null;
        }
        // 循环处理
        for (int i = 0; i < orderField.length(); i++) {
            // 循环处理其余字符
            String s = orderField.substring(i, i + 1);
            // 在大写字母前添加下划线
            if (s.equals(s.toUpperCase()) && !Character.isDigit(s.charAt(0))) {
                result.append("_");
            }
            // 其他字符直接转成大写
            result.append(s.toUpperCase());
        }
        if(StringUtils.isNotEmpty(tbPres)) {
            tbPres = tbPres + ".";
        }
        return tbPres+result.toString() + " " + orderType;
    }

    public static String getNotTransOrderField(String orderField,String orderType) {
        if(StringUtils.isEmpty(orderField)) {
            return null;
        }
        return " order by " + " " + orderField + " " + orderType;
    }
    public static String getOrderFieldSmall(String orderField,String orderType,String tbPres) {
        StringBuilder result = new StringBuilder();
        if(StringUtils.isEmpty(orderField)) {
            return null;
        }
        // 循环处理
        for (int i = 0; i < orderField.length(); i++) {
            // 循环处理其余字符
            String s = orderField.substring(i, i + 1);
            // 在大写字母前添加下划线
            if (s.equals(s.toUpperCase()) && !Character.isDigit(s.charAt(0))) {
                result.append("_");
            }
            // 其他字符直接转成大写
            result.append(s.toLowerCase());
        }
        if(StringUtils.isNotEmpty(tbPres)) {
            tbPres = tbPres + ".";
        }
        return tbPres+result.toString() + " " + orderType;
    }


}
