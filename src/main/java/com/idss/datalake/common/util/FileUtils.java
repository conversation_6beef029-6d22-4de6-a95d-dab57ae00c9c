package com.idss.datalake.common.util;

import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.FileCopyUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件相关操作
 * 复制、移动
 */
public class FileUtils {

    private static Logger logger = LoggerFactory.getLogger(FileUtils.class);

    public static final String DIR_SEPARATOR = "/";

    /**
     * CSV文件列分隔符
     */
    public static final String CSV_COLUMN_SEPARATOR = ",";

    /**
     * CSV文件行分隔符
     */
    public static final String CSV_ROW_SEPARATOR = "\r\n";


    public static void copy(String sourcePath, String targetPath) throws Exception {
        File file = new File(targetPath);
        String filePath = file.getParent();
        File fileParent = new File(filePath);
        if (!fileParent.exists()) {
            fileParent.mkdirs();
        }
        InputStream input = new FileInputStream(sourcePath);
        FileOutputStream output = new FileOutputStream(targetPath);
        FileCopyUtils.copy(input, output);
    }

    public static void download(List<String> sourcePath, HttpServletResponse response) throws Exception {
        download(sourcePath, null, response);
    }


    public static void download(List<String> sourcePath, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<File> files = new ArrayList<>();
        for (String filePath : sourcePath) {
            File file = new File(filePath);
            if (!file.exists()) {
                throw new Exception("文件[" + filePath + "]不存在");
            }
            files.add(file);
        }
        if (files.size() == 1) {
            download(files.get(0), request, response, false);
            return;
        }
        //如果有多个文件，则打包成zip下载
        // 创建zip包
        String zipPath = FileUtils.getDownloadFilename(files.get(0).getAbsolutePath().substring(0, files.get(0).getAbsolutePath().lastIndexOf("/")), "resource.zip");
        File zipFile = new File(zipPath);
        if (!zipFile.exists()) {
            zipFile.createNewFile();
        }
        FileOutputStream fous = new FileOutputStream(zipFile);
        ZipOutputStream zipOut = new ZipOutputStream(fous);
        try {
            FileUtils.zipFile(files, zipOut);
            zipOut.close();
            fous.close();
            download(zipFile, request, response, true);
        } catch (Exception e) {
            logger.error("下载资源文件错误：", e);
            e.printStackTrace();
        }

    }

    public static void download(File file, HttpServletRequest request, HttpServletResponse response, boolean delete) {
        OutputStream toClient = null;
        try {
            // 以流的形式下载文件。
            InputStream fis = new BufferedInputStream(new FileInputStream(file.getPath()));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            // 清空response
            response.reset();
            toClient = new BufferedOutputStream(response.getOutputStream());

            String encodedFileName = URLEncoder.encode(file.getName(), "UTF-8");
            // 清除buffer缓存
            response.reset();
            // 指定下载的文件名
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + encodedFileName + "");
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setDateHeader("Expires", 0);

            toClient.write(buffer);
            toClient.flush();

        } catch (IOException ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (delete) {
                    file.delete();
                }
                toClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static String getDownloadFilename(String sourcePath, String filename) {
        return String.format("%s/%s", sourcePath, filename);
    }

    public static void zipFile(List<String> fileNames, String fileAbsPath, String zipName) throws Exception {
        File zipFile = new File(fileAbsPath + Constant.SEPARATOR + zipName);
        if (!zipFile.exists()) {
            zipFile.createNewFile();
        }
        FileOutputStream fos = new FileOutputStream(zipFile);
        ZipOutputStream zipOut = new ZipOutputStream(fos);
        try {
            List<File> files = new ArrayList<>();
            for (String fileName : fileNames) {
                File file = new File(fileAbsPath + Constant.SEPARATOR + fileName);
                if (!file.exists()) {
                    throw new RuntimeException(fileAbsPath + Constant.SEPARATOR + fileName + "文件不存在");
                }
                files.add(file);
            }
            FileUtils.zipFile(files, zipOut);
        } catch (Exception e) {
            logger.error("下载资源文件错误：", e);
            e.printStackTrace();
        } finally {
            zipOut.close();
            fos.close();
        }

    }

    public static void zipFile(List<File> files, ZipOutputStream zipOut) {
        int size = files.size();
        for (int i = 0; i < size; i++) {
            File file = (File) files.get(i);
            zipFile(file, zipOut);
        }
    }

    public static void zipFile(File file, ZipOutputStream zipOut) {
        try {
            if (file.exists()) {
                if (file.isFile()) {
                    FileInputStream IN = new FileInputStream(file);
                    BufferedInputStream bins = new BufferedInputStream(IN, 512);
                    //org.apache.tools.zip.ZipEntry
                    ZipEntry entry = new ZipEntry(file.getName());
                    zipOut.putNextEntry(entry);
                    // 向压缩文件中输出数据
                    int nNumber;
                    byte[] buffer = new byte[512];
                    while ((nNumber = bins.read(buffer)) != -1) {
                        zipOut.write(buffer, 0, nNumber);
                    }
                    // 关闭创建的流对象
                    bins.close();
                    IN.close();
                } else {
                    try {
                        File[] files = file.listFiles();
                        for (int i = 0; i < files.length; i++) {
                            zipFile(files[i], zipOut);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void deleteFile(String sourceFile) {
        File file = new File(sourceFile);
        if (file.exists()) {
            if (file.isDirectory()) {
                String[] filelist = file.list();
                for (int i = 0; i < filelist.length; i++) {
                    File delfile = new File(sourceFile + DIR_SEPARATOR + filelist[i]);
                    if (!delfile.isDirectory()) {
                        delfile.delete();
                    } else if (delfile.isDirectory()) {
                        deleteFile(sourceFile + DIR_SEPARATOR + filelist[i]);
                    }
                }
            } else {
                file.delete();
            }
        }
    }

    public static void createCsv(String fileAbsPath, List<String> dataList, List<String> header) {
        BufferedWriter csvWriter = null;
        File csvFile = new File(fileAbsPath);
        File parent = csvFile.getParentFile();
        try {
            if (parent != null && !parent.exists()) {
                parent.mkdirs();
            }
            csvFile.createNewFile();
            csvWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(csvFile), "GB2312"), 1024);
            //这部分在第一行居中展示文件名称，根据实际情况，可选择取消注释
            csvWriter.write(String.join(",", header.toArray(new String[header.size()])));
            csvWriter.newLine();
            // 写入文件内容
            for (String data : dataList) {
                csvWriter.write(data==null?"":data);
                csvWriter.newLine();
            }
            csvWriter.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                csvWriter.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void outCsvStream(HttpServletResponse response, StringBuffer sb, String fileName) {
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            response.reset();
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/csv");
            response.setHeader("content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, "utf-8"));
            //为了保证excel打开csv不出现中文乱码
            out.write(sb.toString().getBytes("gbk"));
        } catch (Exception e) {
            logger.error("输出csv流失败:", e);
            if (out != null) {
                try {
                    out.close();
                } catch (IOException ioException) {
                    ioException.printStackTrace();
                }
            }
        }

    }

    /**
     * 根据文件后缀名，获取ContentType
     *
     * @param fileName
     * @return
     */
    public static String getMimeType(String fileName) {
        StringTokenizer st = new StringTokenizer(fileName, ".");
        String mimeType = "tindex/html; charset=UTF-8";
        String index = "";
        for (; st.hasMoreTokens(); ) {
            index = st.nextToken();
            if ("doc".equalsIgnoreCase(index)) {
                mimeType = "application/vnd.ms-word;charset=UTF-8";
            } else if ("bmp".equalsIgnoreCase(index)) {
                mimeType = "image/bmp";
            } else if ("xls".equalsIgnoreCase(index)) {
                mimeType = "application/msexcel;charset=UTF-8";
            } else if ("xlsx".equalsIgnoreCase(index)) {
                mimeType = "application/vnd.ms-excel;charset=UTF-8";
            } else if ("stk".equalsIgnoreCase(index)) {
                mimeType = "application/hyperstudio";
            } else if ("hqx".equalsIgnoreCase(index)) {
                mimeType = "application/mac-binhex40";
            } else if ("cpt".equalsIgnoreCase(index)) {
                mimeType = "application/mac-compactpro";
            } else if ("cil".equalsIgnoreCase(index)) {
                mimeType = "application/vnd.ms-artgalry";
            } else if ("ppt".equalsIgnoreCase(index)) {
                mimeType = "application/vnd.ms-powerpoint";
            } else if ("pss".equalsIgnoreCase(index)) {
                mimeType = "application/vnd.ms-powerpoint";
            } else if ("pot".equalsIgnoreCase(index)) {
                mimeType = "application/vnd.ms-powerpoint";
            } else if ("asf".equalsIgnoreCase(index)) {
                mimeType = "application/vnd.ms-asf";
            } else if ("scm".equalsIgnoreCase(index)) {
                mimeType = "application/vnd.lotus-screencam";
            } else if ("sam".equalsIgnoreCase(index)) {
                mimeType = "application/vnd.lotus-wordpro";
            } else if ("vsd".equalsIgnoreCase(index)) {
                mimeType = "application/vnd.visio";
            } else if ("vss".equalsIgnoreCase(index)) {
                mimeType = "application/vnd.visio";
            } else if ("see".equalsIgnoreCase(index)) {
                mimeType = "application/vnd.seemail";
            } else if ("bin".equalsIgnoreCase(index) || "exe".equalsIgnoreCase(index) || "class".equalsIgnoreCase(index)
                    || "dms".equalsIgnoreCase(index) || "lha".equalsIgnoreCase(index) || "lzh".equalsIgnoreCase(index)) {
                mimeType = "application/octet-stream";
            } else if ("pdf".equalsIgnoreCase(index)) {
                mimeType = "application/pdf";
            } else if ("vcd".equalsIgnoreCase(index)) {
                mimeType = "application/x-cdlink";
            } else if ("zip".equalsIgnoreCase(index) || "jar".equalsIgnoreCase(index)) {
                mimeType = "application/zip";
            } else if ("rar".equalsIgnoreCase(index)) {
                mimeType = "application/rar";
            } else if ("dir".equalsIgnoreCase(index)) {
                mimeType = "application/x-director";
            } else if ("cgi".equalsIgnoreCase(index)) {
                mimeType = "application/x-httpd-cgi";
            } else if ("js".equalsIgnoreCase(index)) {
                mimeType = "application/x-javascript";
            } else if ("mocha".equalsIgnoreCase(index)) {
                mimeType = "application/x-javascript";
            } else if ("tar".equalsIgnoreCase(index)) {
                mimeType = "application/x-tar";
            } else if ("ms".equalsIgnoreCase(index)) {
                mimeType = "application/x-troff-ms";
            } else if ("src".equalsIgnoreCase(index)) {
                mimeType = "application/x-wais-source";
            } else if ("ram".equalsIgnoreCase(index)) {
                mimeType = "audio/x-pn-realaudio";
            } else if ("mid".equalsIgnoreCase(index)) {
                mimeType = "audio/midi";
            } else if ("pdb".equalsIgnoreCase(index)) {
                mimeType = "chemical/x-pdb";
            } else if ("gif".equalsIgnoreCase(index)) {
                mimeType = "image/gif";
            } else if ("jpeg".equalsIgnoreCase(index) || "jpg".equalsIgnoreCase(index) || "jpe".equalsIgnoreCase(index)
                    || "jfif".equalsIgnoreCase(index) || "pjpeg".equalsIgnoreCase(index)) {
                mimeType = "image/jpeg";
            } else if ("png".equalsIgnoreCase(index)) {
                mimeType = "image/png";
            } else if ("tiff".equalsIgnoreCase(index)) {
                mimeType = "image/tiff";
            } else if ("ras".equalsIgnoreCase(index)) {
                mimeType = "image/x-cmu-raster";
            } else if ("htm".equalsIgnoreCase(index)) {
                mimeType = "tindex/html";
            } else if ("txt".equalsIgnoreCase(index)) {
                mimeType = "tindex/plain";
            } else if ("html".equalsIgnoreCase(index)) {
                mimeType = "tindex/html";
            } else if ("rtx".equalsIgnoreCase(index)) {
                mimeType = "tindex/richtindex";
            } else if ("etx".equalsIgnoreCase(index)) {
                mimeType = "tindex/x-setindex";
            } else if ("sgml".equalsIgnoreCase(index)) {
                mimeType = "tindex/x-sgml";
            } else if ("sgm".equalsIgnoreCase(index)) {
                mimeType = "tindex/x-sgml";
            } else if ("uri".equalsIgnoreCase(index)) {
                mimeType = "tindex/uri-list";
            } else if ("wav".equalsIgnoreCase(index)) {
                mimeType = "audio/x-wav";
            } else if ("sp".equalsIgnoreCase(index)) {
                mimeType = "tindex/vnd.in3d.spot";
            } else if ("abc".equalsIgnoreCase(index)) {
                mimeType = "tindex/vnd.abc";
            } else if ("3dm".equalsIgnoreCase(index)) {
                mimeType = "tindex/vnd.in3d.3dml";
            } else if ("sh".equalsIgnoreCase(index)) {
                mimeType = "application/x-sh";
            } else if ("mp3".equalsIgnoreCase(index) || "mp2".equalsIgnoreCase(index) || "mpa".equalsIgnoreCase(index)
                    || "abs".equalsIgnoreCase(index) || "mpega".equalsIgnoreCase(index)) {
                mimeType = "audio/x-mpeg";
            } else if ("m3u".equalsIgnoreCase(index)) {
                mimeType = "audio/x-mpegurl";
            } else {
                mimeType = "multipart/form-data";
            }
        }
        return mimeType;
    }

    public static boolean isOfficeFile(InputStream inputStream) throws Exception {
        InputStream is = FileMagic.prepareToCheckMagic(inputStream);
        FileMagic fileMagic = FileMagic.valueOf(is);
        if (FileMagic.OLE2.equals(fileMagic)) {
            return true;
        }
        if (FileMagic.OOXML.equals(fileMagic)) {
            return true;
        }
        return false;
    }

    public static Long getFileSize(File file) {
        if (file.exists()) {
            return file.length();
        }
        return 0L;
    }

    public static List<String[]> readFile(File file, int colNum) throws Exception {
        /**
         * 获取文件类型
         */
        List<String[]> data = new ArrayList<>();
        String fileName = file.getName();
        String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (FileTypeEnum.CSV.type.equals(fileType.toLowerCase())) {

        } else if (FileTypeEnum.XLS.type.equals(fileType.toLowerCase()) || FileTypeEnum.XLSX.type.equals(fileType.toLowerCase())) {
            data = ExcelParser.readExcel(file, colNum);
        } else {
            throw new ParamInvalidException("暂不支持" + fileType + "文件格式");
        }
        return data;
    }

    public static void createFile(String fileAbsPath,List<String> dataList){
        BufferedWriter csvWriter = null;
        File csvFile = new File(fileAbsPath);
        File parent = csvFile.getParentFile();
        try{
            if (parent != null && !parent.exists()) {
                parent.mkdirs();
            }
            csvFile.createNewFile();
            csvWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(csvFile), "utf-8"), 1024);
//            csvWriter.newLine();
            // 写入文件内容
            for (String data : dataList) {
                csvWriter.write(data);
                csvWriter.newLine();
            }
            csvWriter.flush();
        }catch(Exception e){
            e.printStackTrace();
        }finally {
            try {
                if (csvWriter != null) {
                    csvWriter.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public enum FileTypeEnum {
        CSV("csv"),
        XLS("xls"),
        XLSX("xlsx");
        private String type;

        FileTypeEnum(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }
}
