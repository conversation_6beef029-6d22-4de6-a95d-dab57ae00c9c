package com.idss.datalake.common.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.idss.datalake.datasource.entity.TenantDefineKerberosConfigs;
import com.idss.datalake.datasource.service.ITenantDefineKerberosConfigsService;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.ConsumerGroupListing;
import org.apache.kafka.clients.admin.DescribeTopicsResult;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.PartitionInfo;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <p>
 * kafka
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/19 0019
 */
public class KafkaUtil {

    public static final Logger log = LoggerFactory.getLogger(KafkaUtil.class);

    public static AdminClient getClient(String url) {
        Properties properties = new Properties();
        properties.put(CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG, url);
        properties.put(CommonClientConfigs.RETRIES_CONFIG, 10);
        properties.put(CommonClientConfigs.REQUEST_TIMEOUT_MS_CONFIG, 5000);
        properties.put(CommonClientConfigs.DEFAULT_API_TIMEOUT_MS_CONFIG, 5000);
        Properties consumerProperties = KerberosUtil.initKafkaConsumer();
        if (consumerProperties != null) {
            properties.putAll(consumerProperties);
        }
        log.info("创建AdminClient，properties：{}", JSONObject.toJSONString(properties));
        // properties.put("sasl.mechanism", "GSSAPI");
        // properties.put("security.protocol", "SASL_PLAINTEXT");
        // properties.put("sasl.kerberos.service.name", "kafka");
        return AdminClient.create(properties);
    }

    public static AdminClient getClient(String bootstrapServers, Integer clusterAuthType, Integer authConfigurationId) {
        boolean enabled = false;
        String keytabPath = "";
        String krb5Path = "";
        String principal = "";
        String serviceName = "";
        if (clusterAuthType != null && clusterAuthType == 2) {
            ITenantDefineKerberosConfigsService kerberosConfigsService = SpringUtil.getBean(ITenantDefineKerberosConfigsService.class);
            TenantDefineKerberosConfigs kerberosConfigs = kerberosConfigsService.getById(authConfigurationId);
            if (kerberosConfigs != null) {
                enabled = kerberosConfigs.getEnabled();
                principal = kerberosConfigs.getPrincipal();
                serviceName = kerberosConfigs.getServiceName();
                keytabPath = cn.hutool.core.codec.Base64.decodeStr(kerberosConfigs.getKeytabPath());
                krb5Path = cn.hutool.core.codec.Base64.decodeStr(kerberosConfigs.getKrb5Path());
            }
        }
        return getClient(bootstrapServers, enabled, keytabPath, krb5Path, principal, serviceName);
    }

    public static AdminClient getClient(String url, Boolean enabled, String keytabPath, String krb5Path, String principal, String serviceName) {
        log.info("kafka配置信息，url：{},enabled:{},keytabPath:{},krb5Path:{}", url, enabled, keytabPath, krb5Path);

        Properties properties = new Properties();
        properties.put(CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG, url);
        properties.put(CommonClientConfigs.RETRIES_CONFIG, 10);
        properties.put(CommonClientConfigs.REQUEST_TIMEOUT_MS_CONFIG, 60000);
        properties.put(CommonClientConfigs.DEFAULT_API_TIMEOUT_MS_CONFIG, 60000);

        boolean useKerberos = enabled != null && enabled &&
                keytabPath != null && !keytabPath.isEmpty() &&
                krb5Path != null && !krb5Path.isEmpty();
        if (useKerberos) {
            log.info("使用kerberos认证");
            // 验证文件存在
            File krb5File = new File(krb5Path);
            File keytabFile = new File(keytabPath);
            if (!krb5File.exists() || !krb5File.canRead()) {
                log.error("Kerberos 配置文件不可读: {}", krb5Path);
                throw new IllegalArgumentException("Kerberos 配置文件不可读: " + krb5Path);
            }
            if (!keytabFile.exists() || !keytabFile.canRead()) {
                log.error("Keytab 文件不可读: {}", keytabPath);
                throw new IllegalArgumentException("Keytab 文件不可读: " + keytabPath);
            }

            System.setProperty("java.security.krb5.conf", krb5Path);
            // System.setProperty("java.security.user.keytab", keytabPath);
            properties.put("security.protocol", "SASL_PLAINTEXT");
            properties.put("sasl.mechanism", "GSSAPI");
            properties.put("sasl.kerberos.service.name", serviceName);
            String jaasConfig = "com.sun.security.auth.module.Krb5LoginModule required " +
                    "useKeyTab=true " +
                    "storeKey=true " +
                    "keyTab=\"" + keytabPath + "\" " +
                    "principal=\"" + principal + "\";";
            properties.put("sasl.jaas.config", jaasConfig);
        }

        log.info("开始创建AdminClient，properties：{}", JSONObject.toJSONString(properties));
        return AdminClient.create(properties);
    }

    /**
     * 查询集群所有topic
     *
     * @param adminClient
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public static List<String> topics(AdminClient adminClient) {
        try {
            List<String> topics = adminClient.listTopics().names().get().stream().collect(Collectors.toList());
            log.info("查询所有topic：{}", JSONObject.toJSONString(topics));
            return topics;
        } catch (Exception e) {
            log.error("查询所有topic出错，{}", e.getMessage(), e);
        }
        return Lists.newArrayList();
    }

    public static void createTopic(AdminClient adminClient, String topic, Integer partitions, short replication) {
        NewTopic newTopic = new NewTopic(topic, partitions, replication);
        adminClient.createTopics(Collections.singletonList(newTopic));
    }

    public static boolean topicExist(AdminClient adminClient, String topic) throws ExecutionException, InterruptedException {
        Set<String> topics = adminClient.listTopics().names().get();
        return topics.contains(topic);
    }

    /**
     * 查询topic分区、分片
     *
     * @param adminClient
     * @param topics
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public static Map<String, TopicDescription> topicPartitionAndReplicas(AdminClient adminClient, List<String> topics)
            throws ExecutionException, InterruptedException {
        DescribeTopicsResult describeTopicsResult = adminClient.describeTopics(topics);
        Map<String, TopicDescription> topicDescriptionMap = describeTopicsResult.all().get();
        return topicDescriptionMap;
    }

    public static void deleteTopic(AdminClient adminClient, String topic) {
        adminClient.deleteTopics(Collections.singletonList(topic));
    }

    public static void close(AdminClient adminClient) {
        if (adminClient != null) {
            adminClient.close();
        }
    }

    public static Map<String, Long> getLags(AdminClient adminClient, String url, List topics) {
        List<String> groupIds = new ArrayList<>();
        Map<String, Long> map = new HashMap<>();
        try {
            final Collection<ConsumerGroupListing> consumerGroupListings = adminClient.listConsumerGroups().all().get();
            consumerGroupListings.forEach(x -> groupIds.add(x.groupId()));
            for (String group : groupIds) {
                KafkaConsumer<String, String> consumer = createConsumer(url, group);
                for (Object topic : topics) {
                    final Long lags = topicLags(consumer, topic.toString());
                    if (lags != null) {
                        if (map.get(topic.toString()) != null) {
                            map.replace(topic.toString(), lags + map.get(topic.toString()));
                        } else {
                            map.put(topic.toString(), lags);
                        }
                    } else {
                        map.putIfAbsent(topic.toString(), 0L);
                    }
                }
                consumer.close();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return map;
    }

    private static Long topicLags(KafkaConsumer<String, String> consumer, String topic) {
        List<PartitionInfo> partitionInfoList = consumer.partitionsFor(topic);
        Set<TopicPartition> topicPartitionList = new HashSet<>();
        for (PartitionInfo partitionInfo : partitionInfoList) {
            TopicPartition topicPartition = new TopicPartition(partitionInfo.topic(), partitionInfo.partition());
            topicPartitionList.add(topicPartition);
        }

        Map<Integer, Long> endOffsetMap = new HashMap<>();
        Map<TopicPartition, Long> endOffsets = consumer.endOffsets(topicPartitionList);
        for (TopicPartition partitionInfo : endOffsets.keySet()) {
            endOffsetMap.put(partitionInfo.partition(), endOffsets.get(partitionInfo));
        }
        //查询消费 offset
        Map<Integer, Long> commitOffsetMap = new HashMap<>();
        final Map<TopicPartition, OffsetAndMetadata> committeds = consumer.committed(topicPartitionList);

        for (Map.Entry entry : committeds.entrySet()) {
            if (entry.getValue() == null) {
                return null;
            }
            commitOffsetMap.put(((TopicPartition) entry.getKey()).partition(), ((OffsetAndMetadata) entry.getValue()).offset());
        }


        long lags = 0L;
        for (Map.Entry entry : endOffsetMap.entrySet()) {
            lags = lags + (Long) entry.getValue() - commitOffsetMap.get((Integer) entry.getKey());
        }
        return lags;
    }

    public static Integer getActiveBroker(KafkaConsumer<String, String> consumer, String topic) {
        List<PartitionInfo> partitionInfoList = consumer.partitionsFor(topic);
        Set<String> activeBroker = new HashSet<>();
        for (PartitionInfo partitionInfo : partitionInfoList) {
            final String host = partitionInfo.leader().host();
            activeBroker.add(host);
        }
        return activeBroker.size();
    }

    public static KafkaConsumer<String, String> createConsumer(String url, String groupId) {
        Properties consumerProperties = KerberosUtil.initKafkaConsumer();
        //消费者
        Properties properties = new Properties();
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, url);
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        if (consumerProperties != null) {
            properties.putAll(consumerProperties);
        }
        log.info("创建KafkaConsumer，properties：{}", JSONObject.toJSONString(properties));
        return new KafkaConsumer<>(properties);
    }

    public static void main(String[] args) throws Exception {
        // String url = "************:9092,************:9092,10.20.24.203:9092";
        String url = "10.20.24.43:9092";
        Properties properties = new Properties();
        properties.put(CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG, url);
        AdminClient adminClient = AdminClient.create(properties);
        Set<String> topics = new HashSet<>();
        topics = adminClient.listTopics().names().get();

        Set<String> groupIds = new HashSet<>();
        Map<String, Object> map = new HashMap<>();

        // topics = adminClient.listTopics().names().get();
        // DescribeTopicsResult describeTopicsResult = adminClient.describeTopics(topics);
        // Map<String, TopicDescription> topicDescriptionMap = describeTopicsResult.all().get();
        // for (Map.Entry<String, TopicDescription> entry : topicDescriptionMap.entrySet()) {
        //     TopicDescription topicDescription = entry.getValue();
        //     System.out.println("Topic: " + topicDescription.name());
        //     System.out.println("Partition count: " + topicDescription.partitions().size());
        //     System.out.println("Replication factor: " + topicDescription.partitions().get(0).replicas().size());
        // }


        //         Collection<ConsumerGroupListing> groupListings = adminClient.listConsumerGroups().all().get();
        //         System.out.println(JSONObject.toJSONString(groupListings));
        //             创建topic前，可以先检查topic是否存在，如果已经存在，则不用再创建了
        //             topics = adminClient.listTopics().names().get();
        //             System.out.println(topics);
        //            NewTopic topic = new NewTopic("test",3,(short) 2);
        //            adminClient.createTopics(Collections.singleton(topic));
        //            final Collection<ConsumerGroupListing> consumerGroupListings = adminClient.listConsumerGroups().all().get();
        //            System.out.println(consumerGroupListings);
        //            consumerGroupListings.forEach(x -> groupIds.add(x.groupId()));
        //            System.out.println(groupIds);

        //        System.out.println(map);
        //        topicLags(url,"test","group-test");
        //        final Map<String, Long> lags = getLags(url, Arrays.asList("test"));
        //        Date time = new Date();
        //        List<TbMonitorTopic> list = new ArrayList<>();
        //        for (Map.Entry lag : lags.entrySet()) {
        //            TbMonitorTopic monitorTopic = new TbMonitorTopic();
        //            monitorTopic.setClusterId(1L);
        //            monitorTopic.setTime(time);
        //            monitorTopic.setTopic(lag.getKey().toString());
        //            monitorTopic.setLags((Long) lag.getValue());
        //            list.add(monitorTopic);
        //        }
        //        System.out.println(list);
    }


    public static List<String> getGroups(AdminClient adminClient) throws ExecutionException, InterruptedException {
        List<String> groupIds = new ArrayList<>();
        final Collection<ConsumerGroupListing> consumerGroupListings = adminClient.listConsumerGroups().all().get();
        consumerGroupListings.forEach(x -> groupIds.add(x.groupId()));
        return groupIds;
    }

    public static Integer getTopicGroups(List<String> groupIds, String topic, String url) throws ExecutionException, InterruptedException {
        int count = 0;
        for (String group : groupIds) {
            KafkaConsumer<String, String> consumer = createConsumer(url, group);
            List<PartitionInfo> partitionInfoList = consumer.partitionsFor(topic);
            Set<TopicPartition> topicPartitionList = new HashSet<>();
            for (PartitionInfo partitionInfo : partitionInfoList) {
                TopicPartition topicPartition = new TopicPartition(partitionInfo.topic(), partitionInfo.partition());
                topicPartitionList.add(topicPartition);
            }
            final Map<TopicPartition, OffsetAndMetadata> committeds = consumer.committed(topicPartitionList);
            for (Map.Entry entry : committeds.entrySet()) {
                if (entry.getValue() != null) {
                    count++;
                    break;
                }
            }
            consumer.close();
        }
        return count;
    }
}
