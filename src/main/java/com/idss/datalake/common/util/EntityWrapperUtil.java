package com.idss.datalake.common.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;

/**
 * Copyright 2020 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2020/5/7 14:19
 * 类说明
 */
public class EntityWrapperUtil {
    public static final String ASC = "ASC";

    public static final String DESC = "DESC";

    public static <E> QueryWrapper<E> buildWrapperFromPropertyFilter(List<PropertyFilter> filters, Class<E> clazz, String orderBy) {
        QueryWrapper<E> wrapper = new QueryWrapper();
        TableInfo tableInfo = TableInfoHelper.getTableInfo(clazz);
        if (null != filters)
            for (PropertyFilter f : filters) {
                String column = getColumnName(f.getPropertyName(), tableInfo);
                if (StringUtils.isNotBlank(column)) {
                    if (PropertyFilter.MatchType.EQ.equals(f.getMatchType())) {
                        wrapper.eq(column, f.getMatchValue());
                        continue;
                    }
                    if (PropertyFilter.MatchType.NEQ.equals(f.getMatchType())) {
                        wrapper.ne(column, f.getMatchValue());
                        continue;
                    }
                    if (PropertyFilter.MatchType.LIKE.equals(f.getMatchType())) {
                        wrapper.like(column, (String)f.getMatchValue());
                        continue;
                    }
                    if (PropertyFilter.MatchType.LT.equals(f.getMatchType())) {
                        wrapper.lt(column, f.getMatchValue());
                        continue;
                    }
                    if (PropertyFilter.MatchType.GT.equals(f.getMatchType())) {
                        wrapper.gt(column, f.getMatchValue());
                        continue;
                    }
                    if (PropertyFilter.MatchType.LE.equals(f.getMatchType())) {
                        wrapper.le(column, f.getMatchValue());
                        continue;
                    }
                    if (PropertyFilter.MatchType.GE.equals(f.getMatchType())) {
                        wrapper.ge(column, f.getMatchValue());
                        continue;
                    }
                    if (PropertyFilter.MatchType.IN.equals(f.getMatchType())) {
                        wrapper.in(column, (Collection)f.getMatchValue());
                        continue;
                    }
                    if (PropertyFilter.MatchType.NIN.equals(f.getMatchType())) {
                        wrapper.notIn(column, (Collection)f.getMatchValue());
                        continue;
                    }
                    if (PropertyFilter.MatchType.ISN.equals(f.getMatchType())) {
                        wrapper.isNull(column);
                        continue;
                    }
                    if (PropertyFilter.MatchType.NOTN.equals(f.getMatchType())) {
                        wrapper.isNotNull(column);
                        continue;
                    }
                    if(PropertyFilter.MatchType.BTW.equals(f.getMatchType())) {
                        List<Object> param = (List<Object>) f.getMatchValue();
                        if(param.size() >= 1) {
                            wrapper.ge(column, param.get(0));
                        }
                        if(param.size() >=2) {
                            wrapper.le(column, param.get(1));
                        }
                        continue;
                    }
                }
            }
        if (StringUtils.isNotBlank(orderBy)) {
            String[] sorts = orderBy.split(",");
            for (String sort : sorts) {
                String property = StringUtils.substringBefore(sort, "+");
                String direction = StringUtils.substringBetween(sort, "+");
                if (ASC.equalsIgnoreCase(direction)) {
                    String column = getColumnName(property, tableInfo);
                    if (StringUtils.isNotBlank(column)) {
                        wrapper.orderByAsc(column);
                    }
                } else if (DESC.equalsIgnoreCase(direction)) {
                    String column = getColumnName(property, tableInfo);
                    if (StringUtils.isNotBlank(column)) {
                        wrapper.orderByDesc(column);
                    }
                } else {
                    throw new IllegalArgumentException("排序方式" + direction + "不合法");
                }
            }
        }
        return wrapper;
    }

    public static String getDbColumn(TableInfo tableInfo, String property) {
        String column = null;
        if (StringUtils.isNotBlank(property))
            if (property.equals(tableInfo.getKeyProperty())) {
                column = tableInfo.getKeyColumn();
            } else {
                for (TableFieldInfo field : tableInfo.getFieldList()) {
                    if (property.equals(field.getProperty())) {
                        column = field.getColumn();
                        break;
                    }
                }
            }
        return column;
    }

    private static String getColumnName(String propertyName, TableInfo tableInfo) {
        String column = getDbColumn(tableInfo, propertyName);
        return column;
    }
}
