package com.idss.datalake.common.util;

public class NumberUtils {
    /**
     * 格式化字节大小
     *
     * @param sizeInBytes
     * @return
     */
    public static String formatSize(long sizeInBytes) {
        if (sizeInBytes < 1024) {
            return sizeInBytes + " B";  // 小于 1024B
        } else if (sizeInBytes < 1024 * 1024) {
            return String.format("%.2f K", sizeInBytes / 1024.0);  // 小于 1024K
        } else if (sizeInBytes < 1024 * 1024 * 1024) {
            return String.format("%.2f M", sizeInBytes / (1024.0 * 1024));  // 小于 1024M
        } else if (sizeInBytes < 1024L * 1024 * 1024 * 1024) {
            return String.format("%.2f G", sizeInBytes / (1024.0 * 1024 * 1024));  // 小于 1024G
        } else {
            return String.format("%.2f T", sizeInBytes / (1024.0 * 1024 * 1024 * 1024));  // 小于 1024T
        }
    }

}
