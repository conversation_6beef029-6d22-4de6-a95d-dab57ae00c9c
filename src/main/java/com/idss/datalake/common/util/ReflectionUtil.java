
/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
** Product VERSION,UPDATED BY,UPDATE DATE                                     *
*   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* create 注释
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* M  {modifyComment}
* -  {delComment}
* +  {addCommnet}
\*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.common.util;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 反射工具类.为方便可以端使用,通常返回目标对象.
 *
 * <AUTHOR> 20170120
 * @version 1.0
 */
public class ReflectionUtil {
  /**
   * 属性路径的分割符
   */
  public static final char[] PROP_SPILT_CHARS = { '[', ']', '.' };



  /**
   * DOCUMENT ME!
   *
   * @param bean DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public static Map<String, Object> beanToMap(Object bean) {
    if (bean == null) {
      return null;
    }

    try {
      Map<String, Object> propMap = new HashMap<String, Object>();
      BeanInfo beanInfo = Introspector.getBeanInfo(bean.getClass());
      PropertyDescriptor[] beanPds = beanInfo.getPropertyDescriptors();

      for (int i = 0; i < beanPds.length; i++) {
        Method readMethod = beanPds[i].getReadMethod();

        if ((readMethod == null) || readMethod.getName().equals("getClass")) {
          continue;
        }

        propMap.put(beanPds[i].getName(), readMethod.invoke(bean, ArrayUtils.EMPTY_OBJECT_ARRAY));
      }

      return propMap;
    } catch (Exception ex) {
      throw new RuntimeException(ex);
    }
  }

  /**
   * 获取上一级属性路径
   *
   * @param propPath
   *            属性路径
   * @return 上一级属性路径
   */
  private static String getParentPath(String propPath) {
    String[] subPropPathA = StringUtils.split(propPath, ".[");

    if (subPropPathA.length == 1) {
      return null;
    } else {
      String parentPath = propPath.substring(0, propPath.length() - subPropPathA[subPropPathA.length - 1].length() - 1);

      return parentPath;
    }
  }

  /**
   * 生成指定类型的一个对象
   *
   * @param clazz
   *            指定类型
   * @return 对象
   */
  private static Object newInstance(Class<?> clazz) {
    try {
      Object obj;

      if (!clazz.isInterface()) {
        obj = clazz.newInstance();
      } else if (List.class.isAssignableFrom(clazz)) {
        obj = new ArrayList<Object>();
      } else if (Map.class.isAssignableFrom(clazz)) {
        obj = new HashMap<String, Object>();
      } else {
        throw new RuntimeException("Not Supported Instance Abstract Class:" + clazz);
      }

      return obj;
    } catch (Exception ex) {
      throw new RuntimeException(ex);
    }
  }


  /**
   * DOCUMENT ME!
   *
   * @param <T> DOCUMENT ME!
   * @param source DOCUMENT ME!
   * @param target DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public static <T> T copyLomBokProperties(Object source, T target) {
    return copyLomBokProperties(source, target, null);
  }

  /**
   * 新增lombok注解的实体类属性复制方法
   * @param source
   * @param target
   * @param ignoreProperties
   * @param <T>
   * @return
   */
  public static <T> T copyLomBokProperties(Object source, T target, String[] ignoreProperties) {
    if ((null == source) || (null == target)) {
      return null;
    }

    try {
      BeanUtils.copyProperties(source, target);

      return target;
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 获取对象的字段
   *
   * @param object
   *            对象
   * @param fieldName
   *            字段名
   * @return 字段
   */
  private static Field getField(final Object object, final String fieldName) {
    if (object == null) {
      return null;
    }

    if (StringUtils.isBlank(fieldName)) {
      return null;
    }

    Field field = null;

    for (Class<?> superClass = object.getClass(); superClass != Object.class;
        superClass = superClass.getSuperclass()) {
      try {
        field = superClass.getDeclaredField(fieldName);

        if (field != null) {
          break;
        }
      } catch (NoSuchFieldException e) {
        continue;
      }
    }

    return field;
  }

  /**
   * 获取对象的字段值
   *
   * @param <T>
   *            接收返回值的引用类型
   * @param object
   *            对象
   * @param field
   *            字段
   * @return 对象的字段值
   */
  @SuppressWarnings("unchecked")
  private static <T> T getFieldValue(final Object object, final Field field) {
    if ((object == null) || (field == null)) {
      return null;
    }

    if (!Modifier.isPublic(field.getModifiers())) {
      field.setAccessible(true);
    }

    try {
      return (T) field.get(object);
    } catch (Exception ex) {
      throw new RuntimeException(ex);
    }
  }

}
