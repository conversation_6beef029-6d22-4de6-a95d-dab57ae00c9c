package com.idss.datalake.common.util;

import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datashare.cluster.entity.TbCluster;
import com.idss.datalake.datasource.entity.TenantDefineCredentials;
import com.idss.datalake.datasource.entity.TenantDefineKerberosConfigs;
import com.idss.datalake.datasource.service.ITenantDefineCredentialsService;
import com.idss.datalake.datasource.service.ITenantDefineKerberosConfigsService;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;

/**
 * <p>
 * 数据库连接
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/19 0019
 */
public class HiveUtil {

    private static Logger logger = LoggerFactory.getLogger(HiveUtil.class);

    public static Connection getConnect(TbCluster tbCluster, String instance) {
        String enableKbs = "0";
        String principal = "";
        String keytabPath = "";
        String krb5Path = "";

        Integer clusterAuthType = tbCluster.getClusterAuthType();
        Integer authConfigurationId = tbCluster.getAuthConfigurationId();
        String username = null;
        String password = null;
        // 拼接url
        String url = Constant.DATASOURCE_URL_HIVE + tbCluster.getNodeAddress();
        if (StringUtils.isNotBlank(instance)) {
            url += "/" + instance;
        }
        if (StringUtils.isNotBlank(tbCluster.getConnectionParams())) {
            url += tbCluster.getConnectionParams();
        }

        if (clusterAuthType != null && clusterAuthType == 1) { // 密码认证
            ITenantDefineCredentialsService defineCredentialsService = SpringUtil.getBean(ITenantDefineCredentialsService.class);
            TenantDefineCredentials credentials = defineCredentialsService.getById(authConfigurationId);
            if (credentials != null) {
                username = credentials.getUsername();
                password = credentials.getPassword();
            }
        } else if (clusterAuthType != null && clusterAuthType == 2) { // kerberos认证
            ITenantDefineKerberosConfigsService kerberosConfigsService = SpringUtil.getBean(ITenantDefineKerberosConfigsService.class);
            TenantDefineKerberosConfigs kerberosConfigs = kerberosConfigsService.getById(authConfigurationId);
            if (kerberosConfigs != null) {
                enableKbs = kerberosConfigs.getEnabled() ? "1" : "0";
                principal = kerberosConfigs.getPrincipal();
                keytabPath = cn.hutool.core.codec.Base64.decodeStr(kerberosConfigs.getKeytabPath());
                krb5Path = cn.hutool.core.codec.Base64.decodeStr(kerberosConfigs.getKrb5Path());
            }
        }

        return getConnect(url, username, password, enableKbs, principal, keytabPath, krb5Path);
    }

    //连接测试
    public static Connection getConnect(String url, String username, String password,
                                        String enableKbs, String kbsAccount, String keyTabPath, String krb5ConfPath) {
        logger.info("创建connection，参数：url：{}, username：{}, password：{}, enableKbs：{}, kbsAccount：{}, keyTabPath：{}, krb5ConfPath：{}",
                url, username, password, enableKbs, kbsAccount, keyTabPath, krb5ConfPath);
        Properties props = new Properties();
        props.put("user", username == null ? "" : username);
        props.put("password", password == null ? "" : password);
        props.put("hive2.connect.timeout", "3600");
        Connection conn;
        try {
            // 1-开启kbs
            if (StringUtils.equals(enableKbs, "1")) {
                hiveKerberosConf(kbsAccount, keyTabPath, krb5ConfPath);
                props.put("user", kbsAccount);
            }
            Class<?> clazz = Class.forName(Constant.DATASOURCE_DRIVER_HIVE_CONNECTION);
            Object obj = clazz.getConstructor(String.class, Properties.class).newInstance(url, props);
            conn = (Connection) obj;
        } catch (Exception e) {
            logger.error("创建connection异常：{}", e.getMessage(), e);
            return null;
        }
        return conn;
    }

    /**
     * 手动上传的kbs认证
     *
     * @return
     */
    public static boolean hiveKerberosConf(String kbsAccount, String keyTabPath, String krb5ConfPath) {
        logger.info("=== 开始 Hive Kerberos 认证 ===");
        logger.info("kbsAccount: {}", kbsAccount);
        logger.info("keyTabPath: {}", keyTabPath);
        logger.info("krb5ConfPath: {}", krb5ConfPath);

        try {
            // 设置 JVM 参数
            System.setProperty("java.security.krb5.conf", krb5ConfPath);
            System.setProperty("sun.security.krb5.debug", "true"); // 启用 Kerberos 调试日志
            logger.info("JVM 参数 - java.security.krb5.conf: {}", System.getProperty("java.security.krb5.conf"));
            logger.info("JVM 参数 - sun.security.krb5.debug: {}", System.getProperty("sun.security.krb5.debug"));

            // 配置 Hadoop 认证
            Configuration conf = new Configuration();
            conf.set("hadoop.security.authentication", "kerberos");
            UserGroupInformation.setConfiguration(conf);
            logger.info("Hadoop 配置 - hadoop.security.authentication: {}", conf.get("hadoop.security.authentication"));

            // 打印认证前状态
            logger.info("当前用户（认证前）: {}", UserGroupInformation.getCurrentUser());

            // 执行 Kerberos 登录
            UserGroupInformation.loginUserFromKeytab(kbsAccount, keyTabPath);
            logger.info("Kerberos 认证成功！");

            // 打印认证后状态
            UserGroupInformation ugi = UserGroupInformation.getCurrentUser();
            logger.info("当前用户（认证后）: {}", ugi);
            logger.info("是否通过 Kerberos 认证: {}", ugi.hasKerberosCredentials());
            logger.info("认证类型: {}", ugi.getAuthenticationMethod());

            return true;
        } catch (IOException e) {
            logger.error("Kerberos 认证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    public static void main(String[] args) throws Exception {
        String kbsAccount = "hive/<EMAIL>";
        String keyTabPath = "/Users/<USER>/Documents/kbs-hive/hive.service.keytab";
        String krb5ConfPath = "/Users/<USER>/Documents/kbs-hive/krb5.conf";
        boolean success = hiveKerberosConf(kbsAccount, keyTabPath, krb5ConfPath);
        logger.info("认证结果: {}", success);
        //        Properties props = new Properties();
        //        props.setProperty("user", "");
        //        props.setProperty("password", "");
        //        Class<?> clazz = Class.forName(Constant.DATASOURCE_DRIVER_HIVE);
        //        Object obj = clazz.getConstructor(String.class, Properties.class).newInstance("***************************************", props);
        //        Connection conn = (Connection) obj;
        //        Connection conn = getConnect("****************************************",null, null, "0",null,
        //        null,null);
        //        try {
        //            System.out.println(getTableInfo(conn, "default", "sgb_lj_01"));
        //            conn.close();
        //        } catch (Exception e) {
        //            throw new RuntimeException(e);
        //        }
        //        deleteDb(conn,"test_bbb");

        //        String url = "*****************************************************/<EMAIL>;";
        //        //        String url = "******************************,************:2181,************:2181/mgrz_dwd;
        //        //        principal=hive/_HOST@XTSDCKDC;" +
        //        //                "serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2";
        //        String before = StringUtils.substringBefore(url, ";");
        //        String prefix = StringUtils.substringBefore(before, "/");
        //        //                String principal = StringUtils.substringAfter(url, ";");
        //        String principal = StringUtils.substringBetween(url + ";", "principal=", ";");
        //        System.out.println(principal);
    }

    /**
     * 查询表数据量
     *
     * @param conn
     * @param tableName
     * @return
     * @throws Exception
     */
    public static Long countTableLine(Connection conn, String tableName) throws Exception {
        Statement statement = conn.createStatement();
        // 查询表总数
        String query = "SELECT count(1) cnt FROM " + tableName;
        ResultSet resultSet = statement.executeQuery(query);
        if (resultSet.next()) {
            Long lineCount = resultSet.getLong(1);
            return lineCount;
        }
        resultSet.close();
        statement.close();
        return 0L;
    }

    public static BigDecimal countTableSpace(Connection conn, String dbName, String tableName) throws Exception {
        long spaceInBytes = countTableSpaceInBytes(conn, dbName, tableName);
        if (spaceInBytes == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal bigDecimal = new BigDecimal(spaceInBytes);
        //返回单位  MB
        return bigDecimal.divide(new BigDecimal(1024 * 1024), 2, RoundingMode.HALF_UP);
    }

    public static long countTableSpaceInBytes(Connection conn, String dbName, String tableName) throws Exception {
        Statement statement = null;
        try {
            statement = conn.createStatement();
            // 执行DESCRIBE FORMATTED命令
            String sql = "DESCRIBE FORMATTED " + dbName + "." + tableName;
            ResultSet resultSet = statement.executeQuery(sql);

            // 处理查询结果
            while (resultSet.next()) {
                String result = resultSet.getString(2);
                if (result != null && result.contains("totalSize")) {
                    String totalSize = resultSet.getString(3).trim();
                    return Long.valueOf(totalSize);
                }
            }
            resultSet.close();
            statement.close();
        } catch (Exception e) {
            logger.error("查询表空间大小异常：{}", e.getMessage(), e);
        } finally {
            if (statement != null) {
                statement.close();
            }
        }
        return 0L;
    }

    public static Date queryTime(Connection conn, String dbName, String tableName, String fieldName, String type) throws Exception {
        if ("old".equals(type)) {
            Statement statement = conn.createStatement();
            String sql = "SELECT MIN(" + fieldName + ") AS latest_date FROM " + dbName + "." + tableName + " where " + fieldName + " is not null";
            ResultSet resultSet = statement.executeQuery(sql);
            if (resultSet.next()) {
                return resultSet.getDate("latest_date");
            }
            resultSet.close();
            statement.close();
        } else if ("new".equals(type)) {
            Statement statement = conn.createStatement();
            String sql = "SELECT MAX(" + fieldName + ") AS latest_date FROM " + dbName + "." + tableName + " where " + fieldName + " is not null";
            ResultSet resultSet = statement.executeQuery(sql);
            if (resultSet.next()) {
                return resultSet.getDate("latest_date");
            }
            resultSet.close();
            statement.close();
        }
        return null;
    }

    public static List<String> queryTableField(Connection connection, String tableName) throws Exception {
        List<String> result = new ArrayList<>();
        Statement stmt = connection.createStatement();
        String query = "DESCRIBE " + tableName;
        ResultSet resultSet = stmt.executeQuery(query);


        // 遍历结果集并输出字段信息
        while (resultSet.next()) {
            String columnName = resultSet.getString(1);
            //            String columnType = resultSet.getString(2);
            result.add(columnName);
        }
        resultSet.close();
        stmt.close();
        return result;
    }


    /**
     * 获取指定数据库表的详细信息，包括列信息和字段注释
     *
     * @param connection 数据库连接
     * @param dbName     数据库名称
     * @param tableName  表名称
     * @return TableInfo 对象，包含表的所有列信息
     */
    public static TableInfo getTableInfo(Connection connection, String dbName, String tableName) {
        List<ColumnInfo> columnInfoList = new ArrayList<>();

        try {
            // 获取数据库元数据
            DatabaseMetaData metaData = connection.getMetaData();

            // 获取指定表的列信息
            try (ResultSet rs = metaData.getColumns(null, dbName, tableName, null)) {
                while (rs.next()) {
                    String columnName = rs.getString("COLUMN_NAME"); // 字段名
                    String columnType = rs.getString("TYPE_NAME"); // 字段类型
                    //                    int columnSize = rs.getInt("COLUMN_SIZE"); // 字段大小
                    //                    String isNullable = rs.getString("IS_NULLABLE"); // 是否允许为 NULL
                    //                    String columnDefault = rs.getString("COLUMN_DEF"); // 默认值

                    // 获取字段注释
                    String columnComment = getColumnComment(connection, dbName, tableName, columnName);

                    // 封装字段信息
                    ColumnInfo columnInfo = new ColumnInfo(columnName, columnType, columnComment);
                    columnInfoList.add(columnInfo);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        // 返回包含表信息的 TableInfo 对象
        //hive 没有主键
        return new TableInfo(columnInfoList, null);
    }

    /**
     * 获取字段的注释信息
     *
     * @param connection 数据库连接
     * @param dbName     数据库名称
     * @param tableName  表名称
     * @param columnName 列名称
     * @return 字段注释
     */
    private static String getColumnComment(Connection connection, String dbName, String tableName, String columnName) {
        String columnComment = "";
        try {
            // Hive 查询字段注释（通过 DESCRIBE FORMATTED）
            String sql = "DESCRIBE FORMATTED " + dbName + "." + tableName;
            try (Statement stmt = connection.createStatement()) {
                try (ResultSet rs = stmt.executeQuery(sql)) {
                    while (rs.next()) {
                        String line = rs.getString(1);
                        if (line.contains(columnName)) {
                            // 假设注释行的格式为 `# field_name   field_type   field_comment`
                            String[] parts = line.split("\\s+");
                            if (parts.length >= 3 && parts[0].equals(columnName)) {
                                columnComment = parts[2]; // 取第三部分为字段注释
                            }
                        }
                    }
                }
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return columnComment;
    }

    public static String queryTableDDL(Connection connection, String tableName) throws Exception {
        StringBuilder sb = new StringBuilder();
        Statement stmt = connection.createStatement();

        // 执行查询获取表的创建语句
        String query = "SHOW CREATE TABLE " + tableName;
        ResultSet resultSet = stmt.executeQuery(query);

        // 遍历结果集并输出表的创建语句
        while (resultSet.next()) {
            String createTableStatement = resultSet.getString(1);
            sb.append(createTableStatement);
        }

        resultSet.close();
        stmt.close();
        return new String(sb.toString().getBytes(), StandardCharsets.UTF_8);
    }

    public static List<String> queryDbTable(Connection connection) throws Exception {
        List<String> result = new ArrayList<>();
        Statement stmt = connection.createStatement();

        // 执行查询获取数据库下的所有表
        String query = "SHOW TABLES";
        ResultSet resultSet = stmt.executeQuery(query);

        // 遍历结果集并输出表名
        while (resultSet.next()) {
            String tableName = resultSet.getString(1);
            result.add(tableName);
        }
        resultSet.close();
        stmt.close();
        return result;
    }

    public static void close(Connection conn) {
        try {
            conn.close();
        } catch (SQLException e) {
            logger.error("连接关闭失败，{}", e.getMessage(), e);
        }
    }


    //检测库名是否重复
    public static Boolean queryDb(Connection conn, String db) {
        int flag = 0;
        Statement statement = null;
        try {
            statement = conn.createStatement();
            String queryDb = "show databases";
            final ResultSet resultSet = statement.executeQuery(queryDb);
            while (resultSet.next()) {
                String dbName = resultSet.getString(1);
                if (dbName.equals(db)) {
                    flag = 1;
                    break;
                }
            }
        } catch (SQLException e) {
            logger.error("连接关闭失败", e);
        } finally {
            try {
                if (statement != null) {
                    statement.close();
                }
                if (conn != null) {
                    conn.close();
                }
            } catch (SQLException e) {
                logger.error("连接关闭失败", e);
            }
        }
        if (flag == 1) {
            return true;
        } else {
            return false;
        }
    }


    //新建库
    public static void createDb(Connection conn, String db) {
        Statement statement = null;
        try {
            statement = conn.createStatement();
            String createDb = "CREATE DATABASE %s";
            statement.execute(String.format(createDb, db));
        } catch (SQLException e) {
            logger.error("创建数据库失败", e);
        } finally {
            try {

                if (statement != null) {
                    statement.close();
                }
                if (conn != null) {
                    conn.close();
                }
            } catch (SQLException e) {
                logger.error("关闭连接失败", e);
            }
        }
    }


    //删除库
    public static void deleteDb(Connection conn, String db) {
        Statement statement = null;
        try {
            statement = conn.createStatement();
            String createDb = "DROP DATABASE %s ";
            statement.execute(String.format(createDb, db));
        } catch (SQLException e) {
            logger.error("删除库失败", e);
        } finally {
            try {
                if (statement != null) {
                    statement.close();
                }
                if (conn != null) {
                    conn.close();
                }
            } catch (SQLException e) {
                logger.error("连接失败", e);
            }
        }
    }
}
