package com.idss.datalake.common.util;

import com.idss.datalake.common.enums.WeekEnum;
import com.idss.datalake.common.exception.ParamInvalidException;
import org.quartz.CronExpression;
import org.quartz.CronScheduleBuilder;
import org.quartz.CronTrigger;
import org.quartz.TriggerBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 系统中定时任务配置统一转换cron表达式逻辑
 */
public class CronUtil {

    private static Logger logger = LoggerFactory.getLogger(CronUtil.class);

    public static final String dayExecute = "day";
    public static final String weekExecute = "week";
    public static final String monthExecute = "month";
    public static final String yearExecute = "year";
    public static final String commonExecute = "*";
    public static final String mark = "?";

    /**
     * 每天执行:executeType = day , configJson = {"week":null,"month":null,"day":null,"time":"11:20:55"}
     * 每周执行:executeType = week , configJson = {"week":"MON","month":null,"day":null,"time":"11:20:55"}
     * 每月执行:executeType = month , configJson = {"week":null,"month":null,"day":1,"time":"11:31:18"}
     * 每年执行:executeType = year , configJson = {"week":"MON","month":1,"day":1,"time":"11:20:55"}
     * @param configJson
     * @return
     */
    public static CronDTO transCron(String executeType,String configJson) throws Exception{
        String cronExpress = "%s %s %s %s %s %s %s";
        CronUtil util = new CronUtil();
        CronUtil.CronDTO cron = util.new CronDTO();
        CronUtil.Config config = JSONUtil.jsonToObject(configJson,CronUtil.Config.class);
        String time = config.getTime();
        String hour = time.split(":")[0];
        String min = time.split(":")[1];
        String sec = time.split(":")[2];
        if(dayExecute.equals(executeType)){
            cron.setCron(String.format(cronExpress,sec,min,hour,commonExecute,commonExecute,mark,commonExecute));
            cron.setName("每天 " + time);
        }else if(weekExecute.equals(executeType)){
            String week = config.getWeek();
            cron.setCron(String.format(cronExpress,sec,min,hour,mark,commonExecute,week,commonExecute));
            cron.setName(WeekEnum.getWeekName(week)+ " " + time);
        }else if(monthExecute.equals(executeType)){
            String day = config.getDay();
            cron.setCron(String.format(cronExpress,sec,min,hour,day,commonExecute,mark,commonExecute));
            cron.setName("每月"+day+"号 " + time);
        }else if(yearExecute.equals(executeType)){
            String month = config.getMonth();
            String day = config.getDay();
            cron.setCron(String.format(cronExpress,sec,min,hour,day,month,mark,commonExecute));
            cron.setName("每年"+month+"月"+day+"号 " + time);
        }else{
            throw new ParamInvalidException("只支持的执行类型");
        }
        if (!CronExpression.isValidExpression(cron.getCron())) {
            //Cron表达式不正确
            logger.error("cron表达式错误:"+cron.getCron());
            throw new ParamInvalidException("转换后cron表达式格式错误");
        }
        return cron;
    }

    public class CronDTO{

        public CronDTO getCronDTO(){
            return new CronDTO();
        }

        private String name ;
        private String cron;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCron() {
            return cron;
        }

        public void setCron(String cron) {
            this.cron = cron;
        }
    }

    static class Config {
        private String week;
        private String month ;
        private String day;
        private String time;

        public String getWeek() {
            return week;
        }

        public void setWeek(String week) {
            this.week = week;
        }

        public String getMonth() {
            return month;
        }

        public void setMonth(String month) {
            this.month = month;
        }

        public String getDay() {
            return day;
        }

        public void setDay(String day) {
            this.day = day;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }
    }

    public static void main(String[] args) throws Exception {
        String type = "day";
        String json = "{\"week\":\"MON\",\"month\":null,\"day\":1,\"time\":\"11:20:55\"}";
        CronUtil.CronDTO cron = CronUtil.transCron(type,json);
        System.out.println(cron.getCron());
        System.out.println(cron.getName());

    }
    public static String getLastExecuteTime(String cron,int num) {
        if (!CronExpression.isValidExpression(cron)) {
            //Cron表达式不正确
            return null;
        }
        try {
            CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity("Caclulate Date").withSchedule(CronScheduleBuilder.cronSchedule(cron)).build();
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date time0 = trigger.getStartTime();
            Date time1 = trigger.getFireTimeAfter(time0);
            Date time2 = trigger.getFireTimeAfter(time1);
            Date time3 = trigger.getFireTimeAfter(time2);
            long l = time1.getTime() -(time3.getTime() -time2.getTime())*num;
            return format.format(new Date(l));
        } catch (Exception e) {
            logger.error("", e);
        }
        return null;
    }

    public static List<String> getNextExecuteTime(String cron, int count) {
        List<String> retList = new ArrayList<String>();
        if (!CronExpression.isValidExpression(cron)) {
            //Cron表达式不正确
            return retList;
        }
        try {
            CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity("Caclulate Date").withSchedule(CronScheduleBuilder.cronSchedule(cron)).build();
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date startTime = trigger.getStartTime();
            for (int i = 0; i < count; i++) {
                Date time = trigger.getFireTimeAfter(startTime);
                retList.add(format.format(time));
                startTime = time;
            }
        } catch (Exception e) {
            logger.error("", e);
        }
        return retList;
    }


}