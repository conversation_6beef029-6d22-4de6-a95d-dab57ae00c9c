package com.idss.datalake.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.LocatedFileStatus;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.RemoteIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;


/**
 * <p>
 * HDFS
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/19 0019
 */
public class HdfsUtil {
    private static Logger logger = LoggerFactory.getLogger(HdfsUtil.class);
    public static FileSystem getHdfs(String url) throws IOException {
        FileSystem hdfs = null;
        Configuration conf= new Configuration();
        conf.set("fs.defaultFS",url);
        System.setProperty(url,"root");
        hdfs =FileSystem.get(conf);
        return hdfs;
    }


    public static Boolean exists( FileSystem hdfs,String path) throws IOException {
        return hdfs.exists(new Path("/"+path));
    }

    public static Boolean mkdir( FileSystem hdfs,String path) throws IOException {
        return hdfs.mkdirs(new Path("/"+path));
    }

    public static Boolean delete( FileSystem hdfs,String path) throws IOException {
        return hdfs.delete(new Path("/"+path),true);
    }

    private static final BigDecimal BYTES_IN_TB = new BigDecimal(1024L * 1024L);

    public static BigDecimal getTotalCapacity(String hdfsUri, String username, String password) throws IOException {
        Configuration conf = new Configuration();
        FileSystem fs = null;
        BigDecimal totalCapacity = null;
        try {
            conf.set("fs.defaultFS", hdfsUri);
            conf.set("hadoop.security.authentication", "simple");
            if(StringUtils.isNotEmpty(username)){
                conf.set("hadoop.security.authentication.username", username);
            }
            if(StringUtils.isNotEmpty(password)){
                conf.set("hadoop.security.authentication.password", password);
            }

            fs = FileSystem.get(conf);
            totalCapacity = BigDecimal.ZERO;
            RemoteIterator<LocatedFileStatus> iterator = fs.listFiles(new Path("/data_bak"), true);
            while (iterator.hasNext()) {
                FileStatus status = iterator.next();
                totalCapacity = totalCapacity.add(new BigDecimal(status.getLen()));
            }
            fs.close();
        } catch (Exception e) {
            logger.error("计算空间失败",e);
        } finally {
            if (fs != null) {
                fs.close();
            }
        }
        return totalCapacity;
    }

    public static BigDecimal getTotalCapacityInMB(String hdfsUri, String username, String password){
        BigDecimal totalCapacity = null;
        try {
            totalCapacity = getTotalCapacity(hdfsUri, username, password);
        } catch (IOException e) {
            logger.error("计算空间失败",e);
            return new BigDecimal(0);
        }
        return totalCapacity.divide(BYTES_IN_TB, 2, BigDecimal.ROUND_HALF_UP);
    }
    public static void main(String[] args) throws IOException {


        BigDecimal totalCapacityInGB = getTotalCapacityInMB("hdfs://10.20.24.40:9100", "root", "");
        System.out.println("Total HDFS totalCapacityInGB: " + totalCapacityInGB + " GB");
    }
}
