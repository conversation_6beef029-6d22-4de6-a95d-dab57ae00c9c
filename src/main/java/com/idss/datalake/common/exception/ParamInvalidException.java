package com.idss.datalake.common.exception;

public class ParamInvalidException extends RuntimeException{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String value ;

	public ParamInvalidException() {
		super();
	}
	public ParamInvalidException(String msg) {
		super(msg);
	}
	public ParamInvalidException(String msg,String value) {
		super(msg);
		this.value = value ;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}

}
