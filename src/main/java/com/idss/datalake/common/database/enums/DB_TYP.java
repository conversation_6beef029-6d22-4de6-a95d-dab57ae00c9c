
/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
** Product VERSION,UPDATED BY,UPDATE DATE                                     *
*   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* create 注释
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* M  {modifyComment}
* -  {delComment}
* +  {addCommnet}
\*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.common.database.enums;


/**
 * Copyright 2020 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2020/4/23 13:37
 * 数据库类型
 */
public enum DB_TYP {
    Mysql("MYSQL", "com.mysql.cj.jdbc.Driver"),
    Hive("HIVE", "org.apache.hive.jdbc.HiveDriver"),
    PAN_WEI("PANWEI", "org.postgresql.Driver"),
    CLICKHOUSE("CLICKHOUSE", "ru.yandex.clickhouse.ClickHouseDriver");
    /**
     * DOCUMENT ME!
     */
    private String enName;

  /**
   * DOCUMENT ME!
   */
  private String dirver;

  /**
   * Creates a new DB_TYP object.
   *
   * @param enName DOCUMENT ME!
   * @param dirver DOCUMENT ME!
   */
  private DB_TYP(String enName, String dirver) {
    this.enName = enName;
    this.dirver = dirver;
  }

  /**
   * DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public String getEnName() {
    return enName;
  }

  /**
   * DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public String getDirver() {
    return dirver;
  }

  /**
   * DOCUMENT ME!
   *
   * @param dbType DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public static DB_TYP getType(String dbType) {
    for (DB_TYP enums : DB_TYP.values()) {
      if (enums.enName.equals(dbType)) {
        return enums;
      }
    }

    return null;
  }
}
