
/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
** Product VERSION,UPDATED BY,UPDATE DATE                                     *
*   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* create 注释
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* M  {modifyComment}
* -  {delComment}
* +  {addCommnet}
\*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.common.database;

import com.idss.datalake.common.database.entity.DbConnInfo;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;


/**
 * DOCUMENT ME!
 *
 * <AUTHOR>
 * @version $Revision$
  */
@Service("MysqlComponent")
public class MysqlComponent extends DatabaseAbstract {
  /**
   * DOCUMENT ME!
   */
  private static final Logger LOGGER = LoggerFactory.getLogger(MysqlComponent.class);

  /**
   * DOCUMENT ME!
   *
   * @param dbConnInfo DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   *
   * @throws Exception DOCUMENT ME!
   */
  @Override
  public List<String> getAllTableName(DbConnInfo dbConnInfo) throws Exception {
    Connection conn = getConnection(dbConnInfo);
    Statement stmt = conn.createStatement();
    ResultSet rs = stmt.executeQuery("SHOW TABLES ");
    List<String> tableNameList = new ArrayList<String>();

    while (rs.next()) {
      String tableName = rs.getString(1);
      tableNameList.add(tableName);
    }

    rs.close();
    conn.close();

    return tableNameList;
  }

  /**
   * 拼接数据库URL
   * @param dbConnInfo 数据库基本信息
   * @return
   */
  @Override
  public void concatDBUrl(DbConnInfo dbConnInfo) {
    int timeOut = (dbConnInfo.getTimeOut() == null) ? 60000 : dbConnInfo.getTimeOut();

    if (StringUtils.isEmpty(dbConnInfo.getUrl())) {
      StringBuilder dbUrl = new StringBuilder();
      dbUrl.append("jdbc:mysql://").append(dbConnInfo.getIpAddr()).append(":").append(dbConnInfo.getPort()).append("/").append(dbConnInfo.getDbName()).append("?connectTimeout=").append(timeOut);
      dbConnInfo.setUrl(dbUrl.toString());
    }
  }

  /**
   * 查询TOP N数据
   * @param selectSQL 查询SQL
   * @param lineNum 查询条数
   * @return
   */
  @Override
  public String formatQuerySQL(String selectSQL, int lineNum) {
    selectSQL = selectSQL + " LIMIT " + lineNum;

    return selectSQL;
  }

  /**
   * DOCUMENT ME!
   *
   * @param dbConnInfo DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  @Override
  public boolean checkConnection(DbConnInfo dbConnInfo) {
    concatDBUrl(dbConnInfo);

    try {
      getConnection(dbConnInfo);

      return true;
    } catch (Exception e) {
      LOGGER.error(e.getMessage(), e);
    }

    return false;
  }
}
