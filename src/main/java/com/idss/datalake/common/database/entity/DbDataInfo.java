
/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
** Product VERSION,UPDATED BY,UPDATE DATE                                     *
*   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* create 注释
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* M  {modifyComment}
* -  {delComment}
* +  {addCommnet}
\*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.common.database.entity;

import java.util.List;
import java.util.Map;


/**
 * Copyright 2020 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2020/3/30 9:55
 * 数据库示例数据
 */
public class DbDataInfo {

  private String tableName;

  public String getTableName() {
    return tableName;
  }

  public void setTableName(String tableName) {
    this.tableName = tableName;
  }

  /**
   * 查询的列信息
   */
  private List<String> columns;

  /**
   * 数据明细信息
   */
  private List<Map<String, Object>> data;

  /**
   * 总数
   */
  private Integer total = 0;

  /**
   * DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public List<String> getColumns() {
    return columns;
  }

  /**
   * DOCUMENT ME!
   *
   * @param columns DOCUMENT ME!
   */
  public void setColumns(List<String> columns) {
    this.columns = columns;
  }

  /**
   * DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public List<Map<String, Object>> getData() {
    return data;
  }

  /**
   * DOCUMENT ME!
   *
   * @param data DOCUMENT ME!
   */
  public void setData(List<Map<String, Object>> data) {
    this.data = data;
  }

  /**
   * DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  public Integer getTotal() {
    return total;
  }

  /**
   * DOCUMENT ME!
   *
   * @param total DOCUMENT ME!
   */
  public void setTotal(Integer total) {
    this.total = total;
  }
}
