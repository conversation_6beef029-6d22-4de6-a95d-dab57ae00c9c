
/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
** Product VERSION,UPDATED BY,UPDATE DATE                                     *
*   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* create 注释
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* M  {modifyComment}
* -  {delComment}
* +  {addCommnet}
\*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.common.database;

import com.idss.datalake.common.database.entity.DbConnInfo;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;


/**
 * DOCUMENT ME!
 *
 * <AUTHOR>
 * @version $Revision$
  */
@Service("HiveComponent")
public class HiveComponent extends DatabaseAbstract {
  /**
   * DOCUMENT ME!
   */
  private static final Logger LOGGER = LoggerFactory.getLogger(HiveComponent.class);

  /**
   * DOCUMENT ME!
   *
   * @param dbConnInfo DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   *
   * @throws Exception DOCUMENT ME!
   */
  @Override
  public List<String> getAllTableName(DbConnInfo dbConnInfo) throws Exception {
    Connection conn = getConnection(dbConnInfo);
    DatabaseMetaData metaData = conn.getMetaData();
    ResultSet resultSet = metaData.getTables(dbConnInfo.getDbName(), dbConnInfo.getDbName(), "%", new String[] {
        "TABLE"
      });
    List<String> tableNameList = new ArrayList<String>();

    while (resultSet.next()) {
      String tableName = resultSet.getString("TABLE_NAME");
      tableNameList.add(tableName);
    }

    resultSet.close();
    conn.close();

    return tableNameList;
  }

  /**
   * 拼接数据库URL
   * @param dbConnInfo 数据库基本信息
   * @return
   */
  @Override
  public void concatDBUrl(DbConnInfo dbConnInfo) {
    if (StringUtils.isEmpty(dbConnInfo.getUrl())) {
      StringBuilder dbUrl = new StringBuilder();
      dbUrl.append("jdbc:hive2://").append(dbConnInfo.getIpAddr()).append(":").append(dbConnInfo.getPort()).append("/").append(dbConnInfo.getDbName());
      dbConnInfo.setUrl(dbUrl.toString());
    }
  }

  /**
   * DOCUMENT ME!
   *
   * @param selectSQL DOCUMENT ME!
   * @param lineNum DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  @Override
  public String formatQuerySQL(String selectSQL, int lineNum) {
    selectSQL = selectSQL + " LIMIT " + lineNum;

    return selectSQL;
  }

  /**
   * DOCUMENT ME!
   *
   * @param dbConnInfo DOCUMENT ME!
   *
   * @return DOCUMENT ME!
   */
  @Override
  public boolean checkConnection(DbConnInfo dbConnInfo) {
    concatDBUrl(dbConnInfo);

    try {
      getConnection(dbConnInfo);

      return true;
    } catch (Exception e) {
      LOGGER.error(e.getMessage(), e);
    }

    return false;
  }
}
