package com.idss.datalake.common.enums;

/**
 * Copyright 2022 IDSS
 * <p> 资源状态
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/4 14:06
 */
public enum ResourceStatusEnum {

    NOT_ALLOT("0","未分配"),
    ALLOT("1", "分配中"),
    ALLOT_SUCCESS("2", "分配成功"),
    ALLOT_FAIL("3", "分配失败");

    private String code;

    private String name;

    ResourceStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
