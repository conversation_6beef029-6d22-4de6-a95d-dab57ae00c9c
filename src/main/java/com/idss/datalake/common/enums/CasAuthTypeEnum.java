package com.idss.datalake.common.enums;

public enum CasAuthTypeEnum {
    FOUR_A("4A","4A认证"),
    CAS("CAS","CAS认证");

    private String code;
    private String name;
    CasAuthTypeEnum(String code,String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
