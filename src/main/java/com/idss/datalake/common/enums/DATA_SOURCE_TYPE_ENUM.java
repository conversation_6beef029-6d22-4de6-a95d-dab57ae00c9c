package com.idss.datalake.common.enums;

public enum DATA_SOURCE_TYPE_ENUM {
    CLICKHOUSE("Clickhouse"),
    ELASTICSEARCH("Elasticsearch"),
    KAFKA("Kafka"),
    MYSQL("Mysql"),
    HDFS("HDFS"),
    PANWEI("PANWEI"),
    POSTGRESQL("PostgreSql"),
    ORACLE("Oracle"),
    HIVE("Hive");

    private String name;
    DATA_SOURCE_TYPE_ENUM(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static DATA_SOURCE_TYPE_ENUM getEnumByName(String name) {
        for (DATA_SOURCE_TYPE_ENUM dataSourceTypeEnum : DATA_SOURCE_TYPE_ENUM.values()) {
            if (dataSourceTypeEnum.getName().equals(name)) {
                return dataSourceTypeEnum;
            }
        }
        return null;
    }
}
