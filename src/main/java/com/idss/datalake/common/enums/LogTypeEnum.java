package com.idss.datalake.common.enums;


/**
 * <AUTHOR>
 * @description : <p>日志输出类型，通过LogAspect输出日志信息</p>
 * @see：
 * @since 2022/6/23 0023
 */
public enum LogTypeEnum {

    /**
     * 接入管理页面操作
     */
    MODULE("接入管理 : "),
    /**
     * 用户管理页面操作
     */
    USER("用户管理 : "),

    /**
     * 租户管理页面操作
     */
    TENANT("租户管理 : "),

    /**
     * 登录操作
     */
    LOGIN("用户登录 ： "),

    /**
     * 集群管理
     */
    CLUSTER("集群管理 ： ");

    String name;


    LogTypeEnum(String name) {
        this.name = name;
    }

    private String getName(LogTypeEnum logTypeEnum) {
        for (LogTypeEnum typeEnum : LogTypeEnum.values()) {
            if (logTypeEnum.equals(typeEnum)) {
                return logTypeEnum.getName();
            }
        }
        return null;
    }

    public String getName() {
        return this.name;
    }
}
