/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-07
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-07
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.config.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>元数据-table-模型信息表</p>
 * @since 2024-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_web_table_model")
public class QuaWebTableModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 元数据ID
     */
    @TableField("element_id")
    private Long elementId;

    /**
     * 数据库名称
     */
    @TableField("db_name")
    private String dbName;

    /**
     * 数据表名称
     */
    @TableField("table_name")
    private String tableName;

    /**
     * 数据源类型
     */
    @TableField("datasource_type")
    private String datasourceType;

    /**
     * 所属业务板块
     */
    @TableField("business_sector_id")
    private Long businessSectorId;
    @TableField(exist = false)
    private String businessSectorName;

    /**
     * 所属层级
     */
    @TableField("dw_level_id")
    private Long dwLevelId;
    @TableField(exist = false)
    private String dwLevelName;

    /**
     * 所属数据域
     */
    @TableField("data_domain_id")
    private Long dataDomainId;
    @TableField(exist = false)
    private String dataDomainName;

    /**
     * 所属业务过程
     */
    @TableField("business_process_id")
    private Long businessProcessId;
    @TableField(exist = false)
    private String businessProcessName;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;


}
