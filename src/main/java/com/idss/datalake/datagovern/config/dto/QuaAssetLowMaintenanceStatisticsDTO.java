/************************ <PERSON>ANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-13
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-13
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.config.dto;

import com.idss.datalake.datagovern.config.entity.QuaAssetLowMaintenanceStatistics;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>元数据表低维护资产统计 dto类</p>
 * @since 2024-11-13
 */
@Data
public class QuaAssetLowMaintenanceStatisticsDTO extends QuaAssetLowMaintenanceStatistics {
    private List<Long> ids;
}