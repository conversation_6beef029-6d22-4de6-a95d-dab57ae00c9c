package com.idss.datalake.datagovern.config.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.config.entity.QuaAssetHighValueStatistics;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 元数据表高价值资产统计 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
public interface QuaAssetHighValueStatisticsMapper extends BaseMapper<QuaAssetHighValueStatistics> {

    @Select("select * from qua_asset_high_value_statistics " +
            "where statistic_date = (select max(statistic_date) from qua_asset_high_value_statistics)")
    List<QuaAssetHighValueStatistics> latestData();
}
