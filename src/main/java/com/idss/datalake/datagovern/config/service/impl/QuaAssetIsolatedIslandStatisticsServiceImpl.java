package com.idss.datalake.datagovern.config.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.config.entity.QuaAssetIsolatedIslandStatistics;
import com.idss.datalake.datagovern.config.mapper.QuaAssetIsolatedIslandStatisticsMapper;
import com.idss.datalake.datagovern.config.service.IQuaAssetIsolatedIslandStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 元数据表孤岛资产统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
@Service
public class QuaAssetIsolatedIslandStatisticsServiceImpl extends ServiceImpl<QuaAssetIsolatedIslandStatisticsMapper,
        QuaAssetIsolatedIslandStatistics> implements IQuaAssetIsolatedIslandStatisticsService {
    @Autowired
    private QuaAssetIsolatedIslandStatisticsMapper mapper;

    @Override
    public List<QuaAssetIsolatedIslandStatistics> latestData() {
        return mapper.latestData();
    }
}
