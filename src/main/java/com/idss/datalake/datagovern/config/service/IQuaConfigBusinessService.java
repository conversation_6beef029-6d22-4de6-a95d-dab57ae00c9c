package com.idss.datalake.datagovern.config.service;

import com.idss.datalake.datagovern.config.entity.QuaConfigBusiness;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.config.model.BusinessRequestDto;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import java.util.List;

/**
 * <p>
 * 业务类型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-21
 */
public interface IQuaConfigBusinessService extends IService<QuaConfigBusiness> {

    /**
     * 分页查询业务类型分类
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<QuaConfigBusiness>> queryBusinessPage(BusinessRequestDto requestDto);

    /**
     * 新增或编辑业务类型分类
     * @param requestDto
     */
    void addOrUpdate(BusinessRequestDto requestDto);

    /**
     * 删除业务类型分类
     * @param requestDto
     */
    void deleteBusiness(BusinessRequestDto requestDto);
}
