package com.idss.datalake.datagovern.config.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.config.entity.DataScoreCalcConfig;
import com.idss.datalake.datagovern.config.mapper.DataScoreCalcConfigMapper;
import com.idss.datalake.datagovern.config.service.IDataScoreCalcConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 元数据分值计算配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Service
public class DataScoreCalcConfigServiceImpl extends ServiceImpl<DataScoreCalcConfigMapper, DataScoreCalcConfig> implements IDataScoreCalcConfigService {

}
