/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-27
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-27
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.config.dto;

import com.idss.datalake.datagovern.config.entity.TbSelectField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>自定义列表显示 dto类</p>
 * @since 2024-11-27
 */
@Data
public class TbSelectFieldDTO extends TbSelectField {
    private List<Long> ids;
}