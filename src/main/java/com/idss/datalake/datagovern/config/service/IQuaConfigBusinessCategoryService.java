package com.idss.datalake.datagovern.config.service;

import com.idss.datalake.datagovern.config.entity.QuaConfigBusinessCategory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.config.model.CategoryRequestDto;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import java.util.List;

/**
 * <p>
 * 业务类型分组 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-21
 */
public interface IQuaConfigBusinessCategoryService extends IService<QuaConfigBusinessCategory> {

    /**
     * 分页查询业务类型分类
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<QuaConfigBusinessCategory>> queryCategoryPage(CategoryRequestDto requestDto);

    /**
     * 新增或编辑业务类型分类
     * @param requestDto
     */
    void addOrUpdate(CategoryRequestDto requestDto);

    /**
     * 删除业务类型分类
     * @param requestDto
     */
    void deleteCategory(CategoryRequestDto requestDto) throws Exception;
}
