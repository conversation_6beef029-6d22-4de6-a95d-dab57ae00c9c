/************************ <PERSON>ANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-12
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-12
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.config.dto;

import com.idss.datalake.datagovern.config.entity.QuaAssetQuality;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>元数据表质量分计算统计 dto类</p>
 * @since 2024-11-12
 */
@Data
public class QuaAssetQualityDTO extends QuaAssetQuality {
    private List<Long> ids;
}