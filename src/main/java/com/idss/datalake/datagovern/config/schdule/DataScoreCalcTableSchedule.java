/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/7/12
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/7/12
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.config.schdule;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTable;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableService;
import com.idss.datalake.datagovern.config.entity.DataScoreCalcConfig;
import com.idss.datalake.datagovern.config.entity.QuaAssetHighValueStatistics;
import com.idss.datalake.datagovern.config.entity.QuaAssetLowMaintenanceStatistics;
import com.idss.datalake.datagovern.config.entity.QuaAssetLowQualityStatistics;
import com.idss.datalake.datagovern.config.entity.QuaAssetMaintenance;
import com.idss.datalake.datagovern.config.entity.QuaAssetQuality;
import com.idss.datalake.datagovern.config.service.IDataScoreCalcConfigService;
import com.idss.datalake.datagovern.config.service.IQuaAssetHighValueStatisticsService;
import com.idss.datalake.datagovern.config.service.IQuaAssetLowMaintenanceStatisticsService;
import com.idss.datalake.datagovern.config.service.IQuaAssetLowQualityStatisticsService;
import com.idss.datalake.datagovern.config.service.IQuaAssetMaintenanceService;
import com.idss.datalake.datagovern.config.service.IQuaAssetQualityService;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorJob;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorModel;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorModelResource;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorResult;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorJobService;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorModelResourceService;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorModelService;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorResultService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.NodeInfoRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.NodeInfoVo;
import com.idss.datalake.datagovern.metadata.model.detail.service.NodeService;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.ChJobColumnRequestDto;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.ChJobTableInfoRequestDto;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.EsJobFieldRequestDto;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.EsJobIndexInfoRequestDto;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.chjob.ChColumnInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.chjob.ChTableInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob.EsFieldInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob.EsIndexInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.hivejob.HiveColumnInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.hivejob.HiveTableInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.mysqljob.MysqlColumnInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.mysqljob.MysqlTableInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaJobService;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.datashare.tenant.service.ITbTenantService;
import com.idss.radar.datasource.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 元数据表维护分-质量分计算统计
 *
 * <AUTHOR>
 * @date 2024/11/11
 * @see
 */
@Component
@Slf4j
public class DataScoreCalcTableSchedule {
    @Autowired
    private IQuaMonitorModelService quaMonitorModelService;
    @Autowired
    private IQuaMonitorModelResourceService quaMonitorModelResourceService;
    @Autowired
    private IQuaMonitorJobService quaMonitorJobService;
    @Autowired
    private IQuaMonitorResultService resultService;
    @Autowired
    private IQuaAssetQualityService quaAssetQualityService;
    @Autowired
    private NodeService nodeService;
    @Autowired
    private QuaJobService jobService;
    @Autowired
    private IDataScoreCalcConfigService scoreCalcConfigService;
    @Autowired
    private QuaWabElementService elementService;
    @Autowired
    private IQuaAssetMaintenanceService quaAssetMaintenanceService;
    @Autowired
    private ITbTenantService tenantService;

    @Autowired
    private IQuaAssetLowMaintenanceStatisticsService lowMaintenanceStatisticsService;
    @Autowired
    private IQuaAssetLowQualityStatisticsService lowQualityStatisticsService;
    @Autowired
    private IQuaAssetHighValueStatisticsService highValueStatisticsService;
    @Autowired
    private IDataLineageRelationTableService lineageRelationTableService;

    /**
     * 默认总分
     */
    private static final BigDecimal sumScore = new BigDecimal(100.00);


    @Scheduled(cron = "${maintenance-score-schedule}")
    public void doStatistic() {
        try {
            DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
            DataScoreCalcConfig calcConfig = scoreCalcConfigService.getOne(new LambdaQueryWrapper<>());
            if (calcConfig == null) {
                log.error("元数据分值计算配置不存在，维护分-质量分停止统计");
                return;
            }
            qualityStatistic(calcConfig);
            maintenanceStatistic(calcConfig);
            highValueStatistic(calcConfig);
        } catch (Exception e) {
            log.error("统计异常,{}", e.getMessage(), e);
        } finally {
            DatasourceType.clearDataBaseType();
        }
    }

    /**
     * 质量分统计
     */
    private void qualityStatistic(DataScoreCalcConfig calcConfig) {
        log.info("开始统计-汇总质量分");
        try {
            List<QuaMonitorModel> models = quaMonitorModelService.list(new LambdaUpdateWrapper<QuaMonitorModel>().eq(QuaMonitorModel::getFlag, "1"));
            if (CollectionUtils.isEmpty(models)) {
                log.info("没有需要统计的质量模型");
                return;
            }
            List<QuaAssetQuality> assetQualities = new ArrayList<>();
            for (QuaMonitorModel model : models) {
                log.info("开始查询质量分,{}", model.getModelName());
                QuaMonitorJob monitorJob = quaMonitorJobService.getOne(new LambdaUpdateWrapper<QuaMonitorJob>()
                        .eq(QuaMonitorJob::getModelId, model.getId()).eq(QuaMonitorJob::getFlag, "1")
                        .orderByDesc(QuaMonitorJob::getUpdateTime).last("limit 1"));
                if (monitorJob != null) {
                    QuaMonitorResult result = resultService.getOne(new QueryWrapper<QuaMonitorResult>()
                            .eq("del_flag", 0).eq("job_id", monitorJob.getId()).orderByDesc("create_time")
                            .last("limit 1"));
                    if (result != null) {
                        String monitorScore = result.getMonitorScore();
                        List<QuaMonitorModelResource> modelResources =
                                quaMonitorModelResourceService.list(new QueryWrapper<QuaMonitorModelResource>().eq("model_id", model.getId()));
                        if (CollectionUtils.isNotEmpty(modelResources)) {
                            for (QuaMonitorModelResource modelResource : modelResources) {
                                QuaAssetQuality assetQuality = new QuaAssetQuality();
                                assetQuality.setElementId(model.getElementId());
                                assetQuality.setDbName(model.getDatabaseName());
                                assetQuality.setTableName(modelResource.getTableName());
                                assetQuality.setDatasourceType(model.getElementType());
                                assetQuality.setScore(new BigDecimal(monitorScore));
                                assetQuality.setCreateTime(result.getCreateTime());
                                assetQuality.setTenantId(model.getTenantId());
                                assetQualities.add(assetQuality);
                            }
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(assetQualities)) {
                log.info("没有需要更新的质量分");
                return;
            }

            log.info("开始更新质量分,数量：{}", assetQualities.size());
            quaAssetQualityService.update(new LambdaUpdateWrapper<QuaAssetQuality>().set(QuaAssetQuality::getStatus, 1).eq(QuaAssetQuality::getStatus, 0));
            quaAssetQualityService.saveBatch(assetQualities);

            log.info("低质量资产统计");
            // 二次统计，用于页面概览展示
            List<QuaAssetQuality> qualityList = quaAssetQualityService.list(new QueryWrapper<QuaAssetQuality>().eq("status", 0));
            if (CollectionUtils.isNotEmpty(qualityList)) {
                List<QuaAssetLowQualityStatistics> statisticsList = new ArrayList<>();
                Map<String, List<QuaAssetQuality>> dsTypeMap =
                        qualityList.stream().collect(Collectors.groupingBy(QuaAssetQuality::getDatasourceType));
                for (Map.Entry<String, List<QuaAssetQuality>> entry : dsTypeMap.entrySet()) {
                    String dsType = entry.getKey();
                    List<QuaAssetQuality> qualities = entry.getValue();
                    long count = qualities.stream().filter(x -> NumberUtil.compare(x.getScore().doubleValue(),
                            calcConfig.getLowQualityAssetScore()) < 0).count();
                    QuaAssetLowQualityStatistics statistics = new QuaAssetLowQualityStatistics();
                    statistics.setDatasourceType(dsType);
                    statistics.setNum(count);
                    statistics.setStatus(0);
                    statistics.setStatisticDate(DateUtil.today());
                    statistics.setCreateTime(LocalDateTime.now());
                    statisticsList.add(statistics);
                }
                lowQualityStatisticsService.remove(new LambdaQueryWrapper<QuaAssetLowQualityStatistics>()
                        .eq(QuaAssetLowQualityStatistics::getStatisticDate, DateUtil.today()));
                lowQualityStatisticsService.saveBatch(statisticsList);
            }
            log.info("低质量资产统计结束");
        } catch (Exception e) {
            log.error("质量分统计异常,{}", e.getMessage(), e);
        }
        log.info("质量分汇总-统计结束");
    }

    /**
     * 维护分统计
     */
    private void maintenanceStatistic(DataScoreCalcConfig calcConfig) {
        log.info("开始汇总维护分");
        // 按照租户轮询统计
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<TbTenant> tenants = tenantService.list(new QueryWrapper<TbTenant>()
                .eq("DEL_FLAG", "0")
                .eq("ACCOUNT_TYPE", "1")
                .eq("RESOURCE_STATUS", "2"));
        for (TbTenant tenant : tenants) {
            doMaintenanceStatistic("MYSQL", "MYSQL_DB", "MYSQL_TABLE", DATA_SOURCE_TYPE_ENUM.MYSQL, tenant.getId(), calcConfig);
            doMaintenanceStatistic("CH", "DB", "TABLE", DATA_SOURCE_TYPE_ENUM.CLICKHOUSE, tenant.getId(), calcConfig);
            doMaintenanceStatistic("HIVE", "HIVE_DB", "HIVE_TABLE", DATA_SOURCE_TYPE_ENUM.HIVE, tenant.getId(), calcConfig);
            doMaintenanceStatistic("ES", "INDEX", "", DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH, tenant.getId(), calcConfig);
        }
        log.info("汇总维护分结束");
        log.info("低维护资产统计");
        // 二次统计，用于页面概览展示
        List<QuaAssetMaintenance> maintenanceList = quaAssetMaintenanceService.list(new QueryWrapper<QuaAssetMaintenance>().eq("status", 0));
        if (CollectionUtils.isNotEmpty(maintenanceList)) {
            List<QuaAssetLowMaintenanceStatistics> statisticsList = new ArrayList<>();
            Map<String, List<QuaAssetMaintenance>> dsTypeMap = maintenanceList.stream()
                    .collect(Collectors.groupingBy(QuaAssetMaintenance::getDatasourceType));
            for (Map.Entry<String, List<QuaAssetMaintenance>> entry : dsTypeMap.entrySet()) {
                String dsType = entry.getKey();
                List<QuaAssetMaintenance> assetMaintenances = entry.getValue();
                long count = assetMaintenances.stream().filter(x -> NumberUtil.compare(x.getScore().doubleValue(),
                        calcConfig.getLowMaintenanceAssetScore()) < 0).count();
                QuaAssetLowMaintenanceStatistics statistics = new QuaAssetLowMaintenanceStatistics();
                statistics.setDatasourceType(dsType);
                statistics.setNum(count);
                statistics.setStatus(0);
                statistics.setStatisticDate(DateUtil.today());
                statistics.setCreateTime(LocalDateTime.now());
                statisticsList.add(statistics);
            }
            // 先删除当天数据再保存
            lowMaintenanceStatisticsService.remove(new LambdaQueryWrapper<QuaAssetLowMaintenanceStatistics>()
                    .eq(QuaAssetLowMaintenanceStatistics::getStatisticDate, DateUtil.today()));
            lowMaintenanceStatisticsService.saveBatch(statisticsList);
        }
        log.info("低维护资产统计结束");
    }

    /**
     * 统计维护分
     *
     * @param elementType
     * @param dbType
     * @param tableType
     * @param dbTypeEnum
     */
    private void doMaintenanceStatistic(String elementType, String dbType, String tableType, DATA_SOURCE_TYPE_ENUM dbTypeEnum, Long tenantId,
                                        DataScoreCalcConfig calcConfig) {
        try {
            NodeInfoRequestDto requestDto = new NodeInfoRequestDto();
            requestDto.setType(elementType);
            BaseResponse<List<NodeInfoVo>> nodeListInfo = nodeService.getNodeListInfo(requestDto, tenantId);
            if (nodeListInfo.getContent() != null) {
                List<NodeInfoVo> nodeInfoVos = nodeListInfo.getContent();
                for (NodeInfoVo nodeInfoVo : nodeInfoVos) {
                    log.info("开始统计元数据采集[{}]", nodeInfoVo.getElementName());
                    Long elementId = nodeInfoVo.getElementId();
                    BaseResponse<List<String>> versionList = nodeService.getSnapshootVersion(elementId);
                    if (versionList.getContent() != null && versionList.getContent().size() > 0) {
                        QuaWabElement element = elementService.getById(elementId);
                        String snapshootVersion = versionList.getContent().get(0);
                        requestDto = new NodeInfoRequestDto();
                        requestDto.setElementId(elementId);
                        requestDto.setSnapshootVersion(snapshootVersion);
                        requestDto.setType(dbType);
                        BaseResponse<List<NodeInfoVo>> dbListInfo = nodeService.getNodeListInfo(requestDto, tenantId);
                        if (dbListInfo.getContent() != null && dbListInfo.getContent().size() > 0) {
                            List<NodeInfoVo> dbList = dbListInfo.getContent();
                            if (dbTypeEnum.getName().equals(DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName())) {
                                doElasticsearch(dbTypeEnum, dbList, elementId, snapshootVersion, calcConfig, element);
                            } else {
                                for (NodeInfoVo dbVo : dbList) {
                                    requestDto = new NodeInfoRequestDto();
                                    requestDto.setElementId(elementId);
                                    requestDto.setDbId(dbVo.getDbId());
                                    requestDto.setSnapshootVersion(snapshootVersion);
                                    requestDto.setType(tableType);
                                    BaseResponse<List<NodeInfoVo>> tableListInfo = nodeService.getNodeListInfo(requestDto, tenantId);
                                    if (tableListInfo.getContent() != null) {
                                        if (dbTypeEnum.getName().equals(DATA_SOURCE_TYPE_ENUM.MYSQL.getName())) {
                                            calcMySQL(dbVo, tableListInfo.getContent(), elementId, snapshootVersion, calcConfig, element, dbTypeEnum);
                                        } else if (dbTypeEnum.getName().equals(DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName())) {
                                            calcClickHouse(dbVo, tableListInfo.getContent(), elementId, snapshootVersion, calcConfig, element,
                                                    dbTypeEnum);
                                        } else if (dbTypeEnum.getName().equals(DATA_SOURCE_TYPE_ENUM.HIVE.getName())) {
                                            calcHive(dbVo, tableListInfo.getContent(), elementId, snapshootVersion, calcConfig, element, dbTypeEnum);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("维护分统计异常,{}", e.getMessage(), e);
        }
    }

    /**
     * es维护分单独统计
     *
     * @param dbTypeEnum
     * @param dbList
     * @param elementId
     * @param snapshootVersion
     * @param calcConfig
     * @param element
     */
    private void doElasticsearch(DATA_SOURCE_TYPE_ENUM dbTypeEnum, List<NodeInfoVo> dbList, Long elementId, String snapshootVersion,
                                 DataScoreCalcConfig calcConfig, QuaWabElement element) {
        List<QuaAssetMaintenance> assetMaintenanceList = new ArrayList<>();
        for (NodeInfoVo indexVo : dbList) {
            EsJobIndexInfoRequestDto indexRequest = new EsJobIndexInfoRequestDto();
            indexRequest.setElementId(elementId);
            indexRequest.setIndexId(indexVo.getIndexId());
            indexRequest.setIndexName(indexVo.getIndexName());
            indexRequest.setSnapshootVersion(snapshootVersion);
            BaseResponse<EsIndexInfoVo> esIndexInfo = jobService.esIndexInfo(indexRequest);
            if (esIndexInfo.getContent() != null) {
                EsIndexInfoVo indexInfo = esIndexInfo.getContent();
                String tableNameCn = indexInfo.getBussIndexNameCn();
                BigDecimal tableCnNameScore = BigDecimal.valueOf(0);
                if (StringUtils.isNotBlank(tableNameCn)) {
                    tableCnNameScore = NumberUtil.mul(sumScore, calcConfig.getTableCnNameWi());
                }
                String tableDesc = indexInfo.getBussIndexDscribe();
                BigDecimal tableDescScore = BigDecimal.valueOf(0);
                if (StringUtils.isNotBlank(tableDesc)) {
                    tableDescScore = NumberUtil.mul(sumScore, calcConfig.getTableDescWi());
                }
                String tableOwner = indexInfo.getBussIndexOwner();
                BigDecimal tableOwnerScore = BigDecimal.valueOf(0);
                if (StringUtils.isNotBlank(tableOwner)) {
                    tableOwnerScore = NumberUtil.mul(sumScore, calcConfig.getTableOwnerWi());
                }
                BigDecimal dwLevelScore = BigDecimal.valueOf(0);
                if (indexInfo.getTableModel().getDwLevelId() != null) {
                    dwLevelScore = NumberUtil.mul(sumScore, calcConfig.getTableDwLevelWi());
                }
                BigDecimal businessSectorScore = BigDecimal.valueOf(0);
                if (indexInfo.getTableModel().getBusinessSectorId() != null) {
                    businessSectorScore = NumberUtil.mul(sumScore, calcConfig.getTableBusinessSectorWi());
                }
                BigDecimal dataDomainScore = BigDecimal.valueOf(0);
                if (indexInfo.getTableModel().getDataDomainId() != null) {
                    dataDomainScore = NumberUtil.mul(sumScore, calcConfig.getTableDataDomainWi());
                }
                BigDecimal businessProcessScore = BigDecimal.valueOf(0);
                if (indexInfo.getTableModel().getBusinessProcessId() != null) {
                    businessProcessScore = NumberUtil.mul(sumScore, calcConfig.getTableBusinessProcessWi());
                }
                // 索引字段
                EsJobFieldRequestDto columnRequest = new EsJobFieldRequestDto();
                columnRequest.setTenantId(element.getTenantId());
                columnRequest.setElementId(elementId);
                columnRequest.setIndexId(indexVo.getIndexId());
                columnRequest.setIndexName(indexVo.getIndexName());
                columnRequest.setSnapshootVersion(snapshootVersion);
                columnRequest.setPageNum(1);
                columnRequest.setPageSize(10000);
                BasePageResponse<List<EsFieldInfoVo>> columnPage = jobService.queryEsDetailFieldPage(columnRequest);
                BigDecimal fieldCnNameScore = BigDecimal.valueOf(0);
                BigDecimal fieldRequiredScore = BigDecimal.valueOf(0);
                BigDecimal fieldDescScore = BigDecimal.valueOf(0);
                BigDecimal fieldEnumScore = BigDecimal.valueOf(0);
                if (columnPage.getContent().getData() != null && columnPage.getContent().getData().size() > 0) {
                    List<EsFieldInfoVo> columnInfoVos = columnPage.getContent().getData();
                    int total = columnInfoVos.size();
                    if (CollectionUtils.isNotEmpty(columnInfoVos)) {
                        long cnNameCount = columnInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getFieldNameCn())).count();
                        fieldCnNameScore = NumberUtil.mul(sumScore, NumberUtil.div(cnNameCount, total), calcConfig.getFieldCnNameWi());
                        long fieldRequiredCount = columnInfoVos.stream().filter(x -> x.getIsRequired() != null && x.getIsRequired() == 1).count();
                        fieldRequiredScore = NumberUtil.mul(sumScore, NumberUtil.div(fieldRequiredCount, total), calcConfig.getFieldRequiredWi());
                        long fieldDescCount = columnInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getCnDesc())).count();
                        fieldDescScore = NumberUtil.mul(sumScore, NumberUtil.div(fieldDescCount, total), calcConfig.getFieldDescWi());
                        long fieldEnumCount = columnInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getEnumValue())).count();
                        fieldEnumScore = NumberUtil.mul(sumScore, NumberUtil.div(fieldEnumCount, total), calcConfig.getFieldEnumWi());
                    }
                }
                BigDecimal totalScore = NumberUtil.add(tableCnNameScore, tableDescScore, tableOwnerScore, dwLevelScore,
                        businessSectorScore, dataDomainScore, businessProcessScore, fieldCnNameScore, fieldRequiredScore,
                        fieldDescScore, fieldEnumScore);
                // 扣分项
                if (tableCnNameScore.compareTo(BigDecimal.ZERO) == 0) {
                    totalScore = NumberUtil.sub(totalScore, calcConfig.getTableCnNameDeduction());
                }
                if (fieldCnNameScore.compareTo(BigDecimal.ZERO) == 0) {
                    totalScore = NumberUtil.sub(totalScore, calcConfig.getFieldCnNameDeduction());
                }
                if (totalScore.compareTo(BigDecimal.ZERO) < 0) {
                    totalScore = BigDecimal.ZERO;
                }

                QuaAssetMaintenance maintenance = new QuaAssetMaintenance();
                maintenance.setElementId(elementId);
                maintenance.setDbName(null);
                maintenance.setTableName(indexVo.getIndexName());
                maintenance.setDatasourceType(dbTypeEnum.getName());
                maintenance.setScore(totalScore);
                maintenance.setTenantId(element.getTenantId());
                maintenance.setCreateTime(LocalDateTime.now());
                maintenance.setStatus(0);
                assetMaintenanceList.add(maintenance);
            }
        }
        if (CollectionUtils.isNotEmpty(assetMaintenanceList)) {
            log.info("更新索引维护分统计结果，elementId:{}", elementId);
            quaAssetMaintenanceService.update(new LambdaUpdateWrapper<QuaAssetMaintenance>()
                    .set(QuaAssetMaintenance::getStatus, 1)
                    .eq(QuaAssetMaintenance::getElementId, elementId)
                    .eq(QuaAssetMaintenance::getStatus, 0));
            quaAssetMaintenanceService.saveBatch(assetMaintenanceList);
        }
    }

    private void calcMySQL(NodeInfoVo dbVo, List<NodeInfoVo> tableList, Long elementId, String snapshootVersion,
                           DataScoreCalcConfig calcConfig, QuaWabElement element, DATA_SOURCE_TYPE_ENUM dbTypeEnum) {
        log.info("当前MySQL，开始统计库[{}]", dbVo.getDbName());
        List<QuaAssetMaintenance> assetMaintenanceList = new ArrayList<>();
        for (NodeInfoVo tableVo : tableList) {
            log.info("开始统计表[{}]", tableVo.getTableName());
            ChJobTableInfoRequestDto tableInfoRequestDto = new ChJobTableInfoRequestDto();
            tableInfoRequestDto.setElementId(elementId);
            tableInfoRequestDto.setDbId(dbVo.getDbId());
            tableInfoRequestDto.setDbName(dbVo.getDbName());
            tableInfoRequestDto.setTableId(tableVo.getTableId());
            tableInfoRequestDto.setTableName(tableVo.getTableName());
            tableInfoRequestDto.setSnapshootVersion(snapshootVersion);
            BaseResponse<MysqlTableInfoVo> mysqlTableInfo = jobService.mysqlTableInfo(tableInfoRequestDto);
            if (mysqlTableInfo.getContent() != null) {
                MysqlTableInfoVo tableInfo = mysqlTableInfo.getContent();
                String tableNameCn = tableInfo.getBussTableNameCn();
                BigDecimal tableCnNameScore = BigDecimal.valueOf(0);
                if (StringUtils.isNotBlank(tableNameCn)) {
                    tableCnNameScore = NumberUtil.mul(sumScore, calcConfig.getTableCnNameWi());
                }
                String tableDesc = tableInfo.getBussTableDscribe();
                BigDecimal tableDescScore = BigDecimal.valueOf(0);
                if (StringUtils.isNotBlank(tableDesc)) {
                    tableDescScore = NumberUtil.mul(sumScore, calcConfig.getTableDescWi());
                }
                String tableOwner = tableInfo.getBussTableOwner();
                BigDecimal tableOwnerScore = BigDecimal.valueOf(0);
                if (StringUtils.isNotBlank(tableOwner)) {
                    tableOwnerScore = NumberUtil.mul(sumScore, calcConfig.getTableOwnerWi());
                }
                BigDecimal dwLevelScore = BigDecimal.valueOf(0);
                if (tableInfo.getTableModel().getDwLevelId() != null) {
                    dwLevelScore = NumberUtil.mul(sumScore, calcConfig.getTableDwLevelWi());
                }
                BigDecimal businessSectorScore = BigDecimal.valueOf(0);
                if (tableInfo.getTableModel().getBusinessSectorId() != null) {
                    businessSectorScore = NumberUtil.mul(sumScore, calcConfig.getTableBusinessSectorWi());
                }
                BigDecimal dataDomainScore = BigDecimal.valueOf(0);
                if (tableInfo.getTableModel().getDataDomainId() != null) {
                    dataDomainScore = NumberUtil.mul(sumScore, calcConfig.getTableDataDomainWi());
                }
                BigDecimal businessProcessScore = BigDecimal.valueOf(0);
                if (tableInfo.getTableModel().getBusinessProcessId() != null) {
                    businessProcessScore = NumberUtil.mul(sumScore, calcConfig.getTableBusinessProcessWi());
                }
                ChJobColumnRequestDto columnRequest = new ChJobColumnRequestDto();
                columnRequest.setTenantId(element.getTenantId());
                columnRequest.setDbId(dbVo.getDbId());
                columnRequest.setDbName(dbVo.getDbName());
                columnRequest.setElementId(elementId);
                columnRequest.setSnapshootVersion(snapshootVersion);
                columnRequest.setTableId(tableVo.getTableId());
                columnRequest.setTableName(tableVo.getTableName());
                columnRequest.setPageNum(1);
                columnRequest.setPageSize(10000);
                BasePageResponse<List<MysqlColumnInfoVo>> columnPage = jobService.queryMysqlDetailColumnPage(columnRequest);
                BigDecimal fieldCnNameScore = BigDecimal.valueOf(0);
                BigDecimal fieldRequiredScore = BigDecimal.valueOf(0);
                BigDecimal fieldDescScore = BigDecimal.valueOf(0);
                BigDecimal fieldEnumScore = BigDecimal.valueOf(0);
                if (columnPage.getContent().getData() != null && columnPage.getContent().getData().size() > 0) {
                    List<MysqlColumnInfoVo> columnInfoVos = columnPage.getContent().getData();
                    int total = columnInfoVos.size();
                    if (CollectionUtils.isNotEmpty(columnInfoVos)) {
                        long cnNameCount = columnInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getColumnNameCn())).count();
                        fieldCnNameScore = NumberUtil.mul(sumScore, NumberUtil.div(cnNameCount, total), calcConfig.getFieldCnNameWi());
                        long fieldRequiredCount = columnInfoVos.stream().filter(x -> x.getIsRequired() != null && x.getIsRequired() == 1).count();
                        fieldRequiredScore = NumberUtil.mul(sumScore, NumberUtil.div(fieldRequiredCount, total), calcConfig.getFieldRequiredWi());
                        long fieldDescCount = columnInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getCnDesc())).count();
                        fieldDescScore = NumberUtil.mul(sumScore, NumberUtil.div(fieldDescCount, total), calcConfig.getFieldDescWi());
                        long fieldEnumCount = columnInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getEnumValue())).count();
                        fieldEnumScore = NumberUtil.mul(sumScore, NumberUtil.div(fieldEnumCount, total), calcConfig.getFieldEnumWi());
                    }
                }
                BigDecimal totalScore = NumberUtil.add(tableCnNameScore, tableDescScore, tableOwnerScore, dwLevelScore,
                        businessSectorScore, dataDomainScore, businessProcessScore, fieldCnNameScore, fieldRequiredScore,
                        fieldDescScore, fieldEnumScore);
                // 扣分项
                if (tableCnNameScore.compareTo(BigDecimal.ZERO) == 0) {
                    totalScore = NumberUtil.sub(totalScore, calcConfig.getTableCnNameDeduction());
                }
                if (fieldCnNameScore.compareTo(BigDecimal.ZERO) == 0) {
                    totalScore = NumberUtil.sub(totalScore, calcConfig.getFieldCnNameDeduction());
                }
                if (totalScore.compareTo(BigDecimal.ZERO) < 0) {
                    totalScore = BigDecimal.ZERO;
                }

                QuaAssetMaintenance maintenance = new QuaAssetMaintenance();
                maintenance.setElementId(elementId);
                maintenance.setDbName(dbVo.getDbName());
                maintenance.setTableName(tableVo.getTableName());
                maintenance.setDatasourceType(dbTypeEnum.getName());
                maintenance.setScore(totalScore);
                maintenance.setTenantId(element.getTenantId());
                maintenance.setCreateTime(LocalDateTime.now());
                maintenance.setStatus(0);
                assetMaintenanceList.add(maintenance);
            }
        }
        doSaveMaintenance(assetMaintenanceList, elementId, dbVo.getDbName());
    }

    private void calcClickHouse(NodeInfoVo dbVo, List<NodeInfoVo> tableList, Long elementId, String snapshootVersion,
                                DataScoreCalcConfig calcConfig, QuaWabElement element, DATA_SOURCE_TYPE_ENUM dbTypeEnum) {
        log.info("当前ClickHouse，开始统计库[{}]", dbVo.getDbName());
        List<QuaAssetMaintenance> assetMaintenanceList = new ArrayList<>();
        for (NodeInfoVo tableVo : tableList) {
            log.info("开始统计表[{}]", tableVo.getTableName());
            ChJobTableInfoRequestDto tableInfoRequestDto = new ChJobTableInfoRequestDto();
            tableInfoRequestDto.setElementId(elementId);
            tableInfoRequestDto.setDbId(dbVo.getDbId());
            tableInfoRequestDto.setDbName(dbVo.getDbName());
            tableInfoRequestDto.setTableId(tableVo.getTableId());
            tableInfoRequestDto.setTableName(tableVo.getTableName());
            tableInfoRequestDto.setSnapshootVersion(snapshootVersion);
            BaseResponse<ChTableInfoVo> mysqlTableInfo = jobService.chTableInfo(tableInfoRequestDto);
            if (mysqlTableInfo.getContent() != null) {
                ChTableInfoVo tableInfo = mysqlTableInfo.getContent();
                String tableNameCn = tableInfo.getBussTableNameCn();
                BigDecimal tableCnNameScore = BigDecimal.valueOf(0);
                if (StringUtils.isNotBlank(tableNameCn)) {
                    tableCnNameScore = NumberUtil.mul(sumScore, calcConfig.getTableCnNameWi());
                }
                String tableDesc = tableInfo.getBussTableDscribe();
                BigDecimal tableDescScore = BigDecimal.valueOf(0);
                if (StringUtils.isNotBlank(tableDesc)) {
                    tableDescScore = NumberUtil.mul(sumScore, calcConfig.getTableDescWi());
                }
                String tableOwner = tableInfo.getBussTableOwner();
                BigDecimal tableOwnerScore = BigDecimal.valueOf(0);
                if (StringUtils.isNotBlank(tableOwner)) {
                    tableOwnerScore = NumberUtil.mul(sumScore, calcConfig.getTableOwnerWi());
                }
                BigDecimal dwLevelScore = BigDecimal.valueOf(0);
                if (tableInfo.getTableModel().getDwLevelId() != null) {
                    dwLevelScore = NumberUtil.mul(sumScore, calcConfig.getTableDwLevelWi());
                }
                BigDecimal businessSectorScore = BigDecimal.valueOf(0);
                if (tableInfo.getTableModel().getBusinessSectorId() != null) {
                    businessSectorScore = NumberUtil.mul(sumScore, calcConfig.getTableBusinessSectorWi());
                }
                BigDecimal dataDomainScore = BigDecimal.valueOf(0);
                if (tableInfo.getTableModel().getDataDomainId() != null) {
                    dataDomainScore = NumberUtil.mul(sumScore, calcConfig.getTableDataDomainWi());
                }
                BigDecimal businessProcessScore = BigDecimal.valueOf(0);
                if (tableInfo.getTableModel().getBusinessProcessId() != null) {
                    businessProcessScore = NumberUtil.mul(sumScore, calcConfig.getTableBusinessProcessWi());
                }
                ChJobColumnRequestDto columnRequest = new ChJobColumnRequestDto();
                columnRequest.setTenantId(element.getTenantId());
                columnRequest.setDbId(dbVo.getDbId());
                columnRequest.setDbName(dbVo.getDbName());
                columnRequest.setElementId(elementId);
                columnRequest.setSnapshootVersion(snapshootVersion);
                columnRequest.setTableId(tableVo.getTableId());
                columnRequest.setTableName(tableVo.getTableName());
                columnRequest.setPageNum(1);
                columnRequest.setPageSize(10000);
                BasePageResponse<List<ChColumnInfoVo>> columnPage = jobService.queryChDetailColumnPage(columnRequest);
                BigDecimal fieldCnNameScore = BigDecimal.valueOf(0);
                BigDecimal fieldRequiredScore = BigDecimal.valueOf(0);
                BigDecimal fieldDescScore = BigDecimal.valueOf(0);
                BigDecimal fieldEnumScore = BigDecimal.valueOf(0);
                if (columnPage.getContent().getData() != null && columnPage.getContent().getData().size() > 0) {
                    List<ChColumnInfoVo> columnInfoVos = columnPage.getContent().getData();
                    int total = columnInfoVos.size();
                    if (CollectionUtils.isNotEmpty(columnInfoVos)) {
                        long cnNameCount = columnInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getColumnNameCn())).count();
                        fieldCnNameScore = NumberUtil.mul(sumScore, NumberUtil.div(cnNameCount, total), calcConfig.getFieldCnNameWi());
                        long fieldRequiredCount = columnInfoVos.stream().filter(x -> x.getIsRequired() != null && x.getIsRequired() == 1).count();
                        fieldRequiredScore = NumberUtil.mul(sumScore, NumberUtil.div(fieldRequiredCount, total), calcConfig.getFieldRequiredWi());
                        long fieldDescCount = columnInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getCnDesc())).count();
                        fieldDescScore = NumberUtil.mul(sumScore, NumberUtil.div(fieldDescCount, total), calcConfig.getFieldDescWi());
                        long fieldEnumCount = columnInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getEnumValue())).count();
                        fieldEnumScore = NumberUtil.mul(sumScore, NumberUtil.div(fieldEnumCount, total), calcConfig.getFieldEnumWi());
                    }
                }
                BigDecimal totalScore = NumberUtil.add(tableCnNameScore, tableDescScore, tableOwnerScore, dwLevelScore,
                        businessSectorScore, dataDomainScore, businessProcessScore, fieldCnNameScore, fieldRequiredScore,
                        fieldDescScore, fieldEnumScore);
                // 扣分项
                if (tableCnNameScore.compareTo(BigDecimal.ZERO) == 0) {
                    totalScore = NumberUtil.sub(totalScore, calcConfig.getTableCnNameDeduction());
                }
                if (fieldCnNameScore.compareTo(BigDecimal.ZERO) == 0) {
                    totalScore = NumberUtil.sub(totalScore, calcConfig.getFieldCnNameDeduction());
                }
                if (totalScore.compareTo(BigDecimal.ZERO) < 0) {
                    totalScore = BigDecimal.ZERO;
                }

                QuaAssetMaintenance maintenance = new QuaAssetMaintenance();
                maintenance.setElementId(elementId);
                maintenance.setDbName(dbVo.getDbName());
                maintenance.setTableName(tableVo.getTableName());
                maintenance.setDatasourceType(dbTypeEnum.getName());
                maintenance.setScore(totalScore);
                maintenance.setTenantId(element.getTenantId());
                maintenance.setCreateTime(LocalDateTime.now());
                maintenance.setStatus(0);
                assetMaintenanceList.add(maintenance);
            }
        }
        doSaveMaintenance(assetMaintenanceList, elementId, dbVo.getDbName());
    }

    private void calcHive(NodeInfoVo dbVo, List<NodeInfoVo> tableList, Long elementId, String snapshootVersion,
                          DataScoreCalcConfig calcConfig, QuaWabElement element, DATA_SOURCE_TYPE_ENUM dbTypeEnum) {
        log.info("当前Hive，开始统计库[{}]", dbVo.getDbName());
        List<QuaAssetMaintenance> assetMaintenanceList = new ArrayList<>();
        for (NodeInfoVo tableVo : tableList) {
            log.info("开始统计表[{}]", tableVo.getTableName());
            ChJobTableInfoRequestDto tableInfoRequestDto = new ChJobTableInfoRequestDto();
            tableInfoRequestDto.setElementId(elementId);
            tableInfoRequestDto.setDbId(dbVo.getDbId());
            tableInfoRequestDto.setDbName(tableVo.getDbName());
            tableInfoRequestDto.setTableId(tableVo.getTableId());
            tableInfoRequestDto.setTableName(tableVo.getTableName());
            tableInfoRequestDto.setSnapshootVersion(snapshootVersion);
            BaseResponse<HiveTableInfoVo> mysqlTableInfo = jobService.hiveTableInfo(tableInfoRequestDto);
            if (mysqlTableInfo.getContent() != null) {
                HiveTableInfoVo tableInfo = mysqlTableInfo.getContent();
                String tableNameCn = tableInfo.getBussTableNameCn();
                BigDecimal tableCnNameScore = BigDecimal.valueOf(0);
                if (StringUtils.isNotBlank(tableNameCn)) {
                    tableCnNameScore = NumberUtil.mul(sumScore, calcConfig.getTableCnNameWi());
                }
                String tableDesc = tableInfo.getBussTableDscribe();
                BigDecimal tableDescScore = BigDecimal.valueOf(0);
                if (StringUtils.isNotBlank(tableDesc)) {
                    tableDescScore = NumberUtil.mul(sumScore, calcConfig.getTableDescWi());
                }
                String tableOwner = tableInfo.getBussTableOwner();
                BigDecimal tableOwnerScore = BigDecimal.valueOf(0);
                if (StringUtils.isNotBlank(tableOwner)) {
                    tableOwnerScore = NumberUtil.mul(sumScore, calcConfig.getTableOwnerWi());
                }
                BigDecimal dwLevelScore = BigDecimal.valueOf(0);
                if (tableInfo.getTableModel().getDwLevelId() != null) {
                    dwLevelScore = NumberUtil.mul(sumScore, calcConfig.getTableDwLevelWi());
                }
                BigDecimal businessSectorScore = BigDecimal.valueOf(0);
                if (tableInfo.getTableModel().getBusinessSectorId() != null) {
                    businessSectorScore = NumberUtil.mul(sumScore, calcConfig.getTableBusinessSectorWi());
                }
                BigDecimal dataDomainScore = BigDecimal.valueOf(0);
                if (tableInfo.getTableModel().getDataDomainId() != null) {
                    dataDomainScore = NumberUtil.mul(sumScore, calcConfig.getTableDataDomainWi());
                }
                BigDecimal businessProcessScore = BigDecimal.valueOf(0);
                if (tableInfo.getTableModel().getBusinessProcessId() != null) {
                    businessProcessScore = NumberUtil.mul(sumScore, calcConfig.getTableBusinessProcessWi());
                }
                ChJobColumnRequestDto columnRequest = new ChJobColumnRequestDto();
                columnRequest.setTenantId(element.getTenantId());
                columnRequest.setDbId(dbVo.getDbId());
                columnRequest.setDbName(dbVo.getDbName());
                columnRequest.setElementId(elementId);
                columnRequest.setSnapshootVersion(snapshootVersion);
                columnRequest.setTableId(tableVo.getTableId());
                columnRequest.setTableName(tableVo.getTableName());
                columnRequest.setPageNum(1);
                columnRequest.setPageSize(10000);
                BasePageResponse<List<HiveColumnInfoVo>> columnPage = jobService.queryHiveDetailColumnPage(columnRequest);
                BigDecimal fieldCnNameScore = BigDecimal.valueOf(0);
                BigDecimal fieldRequiredScore = BigDecimal.valueOf(0);
                BigDecimal fieldDescScore = BigDecimal.valueOf(0);
                BigDecimal fieldEnumScore = BigDecimal.valueOf(0);
                if (columnPage.getContent().getData() != null && columnPage.getContent().getData().size() > 0) {
                    List<HiveColumnInfoVo> columnInfoVos = columnPage.getContent().getData();
                    int total = columnInfoVos.size();
                    if (CollectionUtils.isNotEmpty(columnInfoVos)) {
                        long cnNameCount = columnInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getColumnNameCn())).count();
                        fieldCnNameScore = NumberUtil.mul(sumScore, NumberUtil.div(cnNameCount, total), calcConfig.getFieldCnNameWi());
                        long fieldRequiredCount = columnInfoVos.stream().filter(x -> x.getIsRequired() != null && x.getIsRequired() == 1).count();
                        fieldRequiredScore = NumberUtil.mul(sumScore, NumberUtil.div(fieldRequiredCount, total), calcConfig.getFieldRequiredWi());
                        long fieldDescCount = columnInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getCnDesc())).count();
                        fieldDescScore = NumberUtil.mul(sumScore, NumberUtil.div(fieldDescCount, total), calcConfig.getFieldDescWi());
                        long fieldEnumCount = columnInfoVos.stream().filter(x -> StringUtils.isNotBlank(x.getEnumValue())).count();
                        fieldEnumScore = NumberUtil.mul(sumScore, NumberUtil.div(fieldEnumCount, total), calcConfig.getFieldEnumWi());
                    }
                }
                BigDecimal totalScore = NumberUtil.add(tableCnNameScore, tableDescScore, tableOwnerScore, dwLevelScore,
                        businessSectorScore, dataDomainScore, businessProcessScore, fieldCnNameScore, fieldRequiredScore,
                        fieldDescScore, fieldEnumScore);
                // 扣分项
                if (tableCnNameScore.compareTo(BigDecimal.ZERO) == 0) {
                    totalScore = NumberUtil.sub(totalScore, calcConfig.getTableCnNameDeduction());
                }
                if (fieldCnNameScore.compareTo(BigDecimal.ZERO) == 0) {
                    totalScore = NumberUtil.sub(totalScore, calcConfig.getFieldCnNameDeduction());
                }
                if (totalScore.compareTo(BigDecimal.ZERO) < 0) {
                    totalScore = BigDecimal.ZERO;
                }

                QuaAssetMaintenance maintenance = new QuaAssetMaintenance();
                maintenance.setElementId(elementId);
                maintenance.setDbName(dbVo.getDbName());
                maintenance.setTableName(tableVo.getTableName());
                maintenance.setDatasourceType(dbTypeEnum.getName());
                maintenance.setScore(totalScore);
                maintenance.setTenantId(element.getTenantId());
                maintenance.setCreateTime(LocalDateTime.now());
                maintenance.setStatus(0);
                assetMaintenanceList.add(maintenance);
            }
        }
        doSaveMaintenance(assetMaintenanceList, elementId, dbVo.getDbName());
    }

    private void doSaveMaintenance(List<QuaAssetMaintenance> assetMaintenanceList, Long elementId, String dbName) {
        if (CollectionUtils.isNotEmpty(assetMaintenanceList)) {
            log.info("更新维护分统计结果，elementId:{},库：{}", elementId, dbName);
            quaAssetMaintenanceService.update(new LambdaUpdateWrapper<QuaAssetMaintenance>()
                    .set(QuaAssetMaintenance::getStatus, 1)
                    .eq(QuaAssetMaintenance::getElementId, elementId)
                    .eq(QuaAssetMaintenance::getDbName, dbName)
                    .eq(QuaAssetMaintenance::getStatus, 0));
            quaAssetMaintenanceService.saveBatch(assetMaintenanceList);
        }
    }

    /**
     * 高价值资产统计
     *
     * @param calcConfig
     */
    private void highValueStatistic(DataScoreCalcConfig calcConfig) {
        log.info("开始统计-高价值资产");
        try {
            List<DataLineageRelationTable> relationTables = lineageRelationTableService.queryCitationTable(calcConfig.getHighValueAssetScore());
            if (CollectionUtils.isEmpty(relationTables)) {
                log.info("没有高价值资产");
                return;
            }
            List<QuaAssetHighValueStatistics> statisticsList = new ArrayList<>();
            Map<String, List<DataLineageRelationTable>> dsTypeMap =
                    relationTables.stream().collect(Collectors.groupingBy(DataLineageRelationTable::getDatasourceType));
            for (Map.Entry<String, List<DataLineageRelationTable>> entry : dsTypeMap.entrySet()) {
                String dsType = entry.getKey();
                List<DataLineageRelationTable> tables = entry.getValue();
                QuaAssetHighValueStatistics statistics = new QuaAssetHighValueStatistics();
                statistics.setDatasourceType(dsType);
                statistics.setNum(Long.valueOf(tables.size()));
                statistics.setStatus(0);
                statistics.setStatisticDate(DateUtil.today());
                statistics.setCreateTime(LocalDateTime.now());
                statisticsList.add(statistics);
            }
            highValueStatisticsService.remove(new LambdaUpdateWrapper<QuaAssetHighValueStatistics>()
                    .eq(QuaAssetHighValueStatistics::getStatisticDate, DateUtil.today()));
            highValueStatisticsService.saveBatch(statisticsList);
        } catch (Exception e) {
            log.error("高价值资产统计异常,{}", e.getMessage(), e);
        }
        log.info("高价值资产-统计结束");
    }
}
