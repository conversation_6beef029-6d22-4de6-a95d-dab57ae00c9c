/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-22
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-22
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.config.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>元数据表数据量和存储空间汇总</p>
 * @since 2024-11-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_asset_table_data_statistics")
public class QuaAssetTableDataStatistics implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 元数据ID
     */
    @TableField("element_id")
    private Long elementId;

    /**
     * 数据库名称
     */
    @TableField("db_name")
    private String dbName;

    /**
     * 数据表名称
     */
    @TableField("table_name")
    private String tableName;

    /**
     * 数据源类型
     */
    @TableField("datasource_type")
    private String datasourceType;

    /**
     * 数据条数
     */
    @TableField("data_count")
    private Long dataCount;

    /**
     * 数据存储量（字节）
     */
    @TableField("data_storage_size")
    private Long dataStorageSize;

    /**
     * 统计日期
     */
    @TableField("statistic_date")
    private String statisticDate;

    /**
     * 入库时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


}
