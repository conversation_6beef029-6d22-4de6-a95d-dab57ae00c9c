/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-06
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-06
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.config.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datagovern.config.dto.DataScoreCalcConfigDTO;
import com.idss.datalake.datagovern.config.manager.DataScoreCalcConfigManager;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 元数据分值计算配置
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/data-score-calc-config")
public class DataScoreCalcConfigController {
    private static final Logger logger = LoggerFactory.getLogger(DataScoreCalcConfigController.class);
    @Autowired
    private DataScoreCalcConfigManager dataScoreCalcConfigManager;

    @ApiOperation(value = "获取配置")
    @GetMapping(value = "/getConfig")
    public ResultBean detail() {
        try {
            return ResultBean.success(dataScoreCalcConfigManager.getConfig());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "保存或更新")
    @PostMapping(value = "/saveOrUpdate")
    public ResultBean saveOrUpdate(@RequestBody DataScoreCalcConfigDTO dto) {
        try {
            dataScoreCalcConfigManager.saveOrUpdate(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

}
