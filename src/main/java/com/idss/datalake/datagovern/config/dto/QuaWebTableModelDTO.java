/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-07
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-07
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.config.dto;

import com.idss.datalake.datagovern.config.entity.QuaWebTableModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>元数据-table-模型信息表 dto类</p>
 * @since 2024-11-07
 */
@Data
public class QuaWebTableModelDTO extends QuaWebTableModel {
    private List<Long> ids;
}