package com.idss.datalake.datagovern.config.service;

import com.idss.datalake.datagovern.config.entity.QuaConfigMasterDataType;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.config.model.MasterDataRequestDto;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import java.util.List;

/**
 * <p>
 * 主数据类型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-21
 */
public interface IQuaConfigMasterDataTypeService extends IService<QuaConfigMasterDataType> {

    /**
     * 分页查询业务类型分类
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<QuaConfigMasterDataType>> queryMasterTypePage(MasterDataRequestDto requestDto);

    /**
     * 新增或编辑业务类型分类
     * @param requestDto
     */
    void addOrUpdate(MasterDataRequestDto requestDto);

    /**
     * 删除业务类型分类
     * @param requestDto
     */
    void deleteMasterType(MasterDataRequestDto requestDto);
}
