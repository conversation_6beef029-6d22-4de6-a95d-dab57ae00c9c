package com.idss.datalake.datagovern.config.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.config.entity.QuaAssetLowMaintenanceStatistics;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 元数据表低维护资产统计 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface QuaAssetLowMaintenanceStatisticsMapper extends BaseMapper<QuaAssetLowMaintenanceStatistics> {

    @Select("select * from qua_asset_low_maintenance_statistics qalms " +
            "where statistic_date = (select max(statistic_date) from qua_asset_low_maintenance_statistics)")
    List<QuaAssetLowMaintenanceStatistics> latestData();
}
