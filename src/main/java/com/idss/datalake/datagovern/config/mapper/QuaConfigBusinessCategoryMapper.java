package com.idss.datalake.datagovern.config.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.config.entity.QuaConfigBusinessCategory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.config.model.CategoryRequestDto;

/**
 * <p>
 * 业务类型分组 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-21
 */
public interface QuaConfigBusinessCategoryMapper extends BaseMapper<QuaConfigBusinessCategory> {

    Page<QuaConfigBusinessCategory> queryCategoryPage(CategoryRequestDto requestDto);
}
