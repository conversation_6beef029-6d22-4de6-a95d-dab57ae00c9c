package com.idss.datalake.datagovern.config.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.config.entity.QuaAssetHighValueStatistics;
import com.idss.datalake.datagovern.config.mapper.QuaAssetHighValueStatisticsMapper;
import com.idss.datalake.datagovern.config.service.IQuaAssetHighValueStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 元数据表高价值资产统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
@Service
public class QuaAssetHighValueStatisticsServiceImpl extends ServiceImpl<QuaAssetHighValueStatisticsMapper, QuaAssetHighValueStatistics> implements IQuaAssetHighValueStatisticsService {

    @Autowired
    private QuaAssetHighValueStatisticsMapper mapper;

    @Override
    public List<QuaAssetHighValueStatistics> latestData() {
        return mapper.latestData();
    }
}
