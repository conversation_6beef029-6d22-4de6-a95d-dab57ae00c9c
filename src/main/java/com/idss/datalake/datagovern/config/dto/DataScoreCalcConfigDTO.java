/************************ <PERSON>ANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-06
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-06
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.config.dto;

import com.idss.datalake.datagovern.config.entity.DataScoreCalcConfig;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>元数据分值计算配置 dto类</p>
 * @since 2024-11-06
 */
@Data
public class DataScoreCalcConfigDTO extends DataScoreCalcConfig {
    private List<Long> ids;
}