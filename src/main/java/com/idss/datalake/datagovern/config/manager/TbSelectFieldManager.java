/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-27
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-27
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.config.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.config.entity.TbSelectField;
import com.idss.datalake.datagovern.config.service.ITbSelectFieldService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 自定义列表显示
 *
 * <AUTHOR>
 * @date 2024/11/27
 * @see
 */
@Component
public class TbSelectFieldManager {
    private static final Logger logger = LoggerFactory.getLogger(TbSelectFieldManager.class);

    @Autowired
    private ITbSelectFieldService iTbSelectFieldService;

    public void create(List<TbSelectField> selectFields) {
        boolean updateFlag = selectFields.get(0).getId() != null;
        LocalDateTime now = LocalDateTime.now();
        String currentUsername = UserUtil.getCurrentUsername();
        for (TbSelectField selectField : selectFields) {
            if (updateFlag) {
                selectField.setCreateTime(now);
                selectField.setCreateUser(currentUsername);
            }
            selectField.setUpdateTime(now);
            selectField.setUpdateUser(currentUsername);
        }
        iTbSelectFieldService.saveOrUpdateBatch(selectFields);
    }

    public List<TbSelectField> list(String moduleName) {
        return iTbSelectFieldService.list(new LambdaQueryWrapper<TbSelectField>().eq(TbSelectField::getModuleName, moduleName));
    }

}