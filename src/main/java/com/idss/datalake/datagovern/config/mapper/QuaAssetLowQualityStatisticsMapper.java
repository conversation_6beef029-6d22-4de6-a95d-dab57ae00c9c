package com.idss.datalake.datagovern.config.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.config.entity.QuaAssetLowMaintenanceStatistics;
import com.idss.datalake.datagovern.config.entity.QuaAssetLowQualityStatistics;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 元数据表低质量资产统计 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface QuaAssetLowQualityStatisticsMapper extends BaseMapper<QuaAssetLowQualityStatistics> {

    @Select("select * from qua_asset_low_quality_statistics qalqs " +
            "where statistic_date = (select max(statistic_date) from qua_asset_low_quality_statistics)")
    List<QuaAssetLowQualityStatistics> latestData();
}
