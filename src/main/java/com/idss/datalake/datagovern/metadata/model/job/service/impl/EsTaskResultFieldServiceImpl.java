package com.idss.datalake.datagovern.metadata.model.job.service.impl;

import com.idss.datalake.datagovern.metadata.model.job.entity.EsTaskResultField;
import com.idss.datalake.datagovern.metadata.model.job.mapper.EsTaskResultFieldMapper;
import com.idss.datalake.datagovern.metadata.model.job.service.EsTaskResultFieldService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ES扫描field记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Service
public class EsTaskResultFieldServiceImpl extends ServiceImpl<EsTaskResultFieldMapper, EsTaskResultField> implements EsTaskResultFieldService {

}
