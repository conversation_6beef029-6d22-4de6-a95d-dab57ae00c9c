package com.idss.datalake.datagovern.metadata.model.element.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: xiexiaofei
 * @Date: 18/6/2021 16:49
 * @Description: 元数据管理列表分页数据
 */
@Data
@ApiModel("统计启停，连通状态")
public class ElementCountVo {
    @ApiModelProperty("启动")
    private Integer jobStartCount;
    @ApiModelProperty("停用")
    private Integer jobStopCount;
    @ApiModelProperty("正常")
    private Integer connectNormalCount;
    @ApiModelProperty("非正常")
    private Integer connectUNNormalCount;
}
