package com.idss.datalake.datagovern.metadata.model.kbs.controller;


import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.kbs.entity.KbsFileConfig;
import com.idss.datalake.datagovern.metadata.model.kbs.pojo.dto.AddOrUpdateKbsTemplateRequest;
import com.idss.datalake.datagovern.metadata.model.kbs.pojo.dto.KbsFileConfigExt;
import com.idss.datalake.datagovern.metadata.model.kbs.pojo.dto.KbsTemplatePageRequest;
import com.idss.datalake.datagovern.metadata.model.kbs.service.KbsFileConfigService;
import com.idss.datalake.datagovern.metadata.model.operate.enums.OperateEnum;
import com.idss.datalake.datagovern.metadata.model.operate.util.OperateLogUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * Kbs认证文件配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Api("认证文件管理")
@RestController
@RequestMapping("/kbs/template")
public class KbsFileConfigController {
    @Autowired
    private KbsFileConfigService kbsFileConfigService;

    @ApiOperation("新增或修改认证文件")
    @PostMapping("/addOrUpdate")
    public BaseResponse<String> addOrUpdate(@Valid @RequestBody AddOrUpdateKbsTemplateRequest request) {
        OperateEnum operateEnum = null;
        String logConcatDesc = null;
        try {
            if (request.getId() == null) {
                operateEnum = OperateEnum.KBS_FILE_MANAGER_ADD;
                logConcatDesc = request.getTemplateName();
            } else {
                operateEnum = OperateEnum.KBS_FILE_MANAGER_UPDATE;
                logConcatDesc = request.getTemplateName();
            }
            OperateLogUtil.addLog(operateEnum, logConcatDesc, "成功");
            return kbsFileConfigService.addOrUpdate(request);
        } catch (Exception e) {
            OperateLogUtil.addLog(operateEnum, logConcatDesc, "失败");
            throw new RuntimeException(e);
        }
    }

    /**
     * 查看模板
     *
     * @param id
     * @return
     */
    @ApiOperation("查看模板")
    @GetMapping("/viewById/{id}")
    public BaseResponse<KbsFileConfigExt> viewById(@PathVariable("id") Long id) {
        try {
            OperateLogUtil.addLog(OperateEnum.KBS_FILE_MANAGER_READ, "", "成功");
            return kbsFileConfigService.viewById(id);
        } catch (Exception e) {
            OperateLogUtil.addLog(OperateEnum.KBS_FILE_MANAGER_READ, "", "失败");
            throw new RuntimeException(e);
        }
    }

    /**
     * 删除模板
     *
     * @param id
     * @return
     */
    @ApiOperation("删除模板")
    @GetMapping("/deleteById/{id}")
    public BaseResponse<String> deleteById(@PathVariable("id") Long id) {
        try {
            OperateLogUtil.addLog(OperateEnum.KBS_FILE_MANAGER_DELETE, "", "成功");
            return kbsFileConfigService.deleteById(id);
        } catch (Exception e) {
            OperateLogUtil.addLog(OperateEnum.KBS_FILE_MANAGER_DELETE, "", "失败");
            throw new RuntimeException(e);
        }
    }

    /**
     * 认证模板文件分页查询
     *
     * @param request
     * @return
     */
    @ApiOperation("认证模板文件分页查询")
    @PostMapping("/queryPage")
    public BasePageResponse<List<KbsFileConfig>> queryPage(@RequestBody KbsTemplatePageRequest request) {
        return kbsFileConfigService.queryPage(request);
    }

    /**
     * 查询所有认证模板
     *
     * @return
     */
    @ApiOperation("查询所有认证模板")
    @PostMapping("/queryAll")
    public BaseResponse<List<KbsFileConfig>> queryAll() {
        return kbsFileConfigService.queryAll();
    }


}
