package com.idss.datalake.datagovern.metadata.model.job.service;

import com.idss.datalake.datagovern.metadata.model.job.entity.ChTaskResultColumn;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * ch扫描Column记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
public interface ChTaskResultColumnService extends IService<ChTaskResultColumn> {

    String maxSnapshootVersionByElementId(Long elementId);
}
