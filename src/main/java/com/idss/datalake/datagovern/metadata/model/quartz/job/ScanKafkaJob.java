package com.idss.datalake.datagovern.metadata.model.quartz.job;

import com.idss.datalake.common.redis.RedissonLockUtil;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.utils.BtoaEncode;
import com.idss.datalake.datagovern.metadata.model.element.utils.SendScanUtil;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaJob;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaTask;
import com.idss.datalake.datagovern.metadata.model.job.enums.TaskStatusEnum;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaJobService;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import sun.misc.BASE64Decoder;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Author: xiexiaofei
 * @Date: 16/6/2021 15:19
 * @Description: 定时任务发送扫描Kafka请求
 */
@DisallowConcurrentExecution
@Slf4j
@Component
public class ScanKafkaJob implements Job {
    private static final String LOCK_KEY_PRE = "DATA_LAKE_SCAN_KAFKA_KEY_";

    private static final String ADD_TASK = "/scan/add";

    @Value("${data.collect.url}")
    private String collectUrl;

    @Autowired
    private QuaTaskService quaTaskService;
    @Autowired
    private QuaJobService quaJobService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("开始执行扫描 Kafka任务");
        JobDataMap jobDataMap = context.getMergedJobDataMap();
        Object element = jobDataMap.get("element");
        Object job = jobDataMap.get("job");
        String addScanUrl = collectUrl + ADD_TASK;

        try {
            if (element instanceof QuaWabElement && job instanceof QuaJob) {
                QuaWabElement quaWabElement = (QuaWabElement) element;
                QuaJob quaJob = (QuaJob) job;

                String lockKey = LOCK_KEY_PRE + quaWabElement.getId();
                boolean lock = RedissonLockUtil.tryLock(lockKey, 0, 30, TimeUnit.SECONDS);
                if (!lock) {
                    log.info("获取锁失败，任务正在执行中");
                    return;
                }

                try {
                    // 创建任务
                    QuaTask quaTask = new QuaTask();
                    quaTask.setJobId(quaJob.getId());
                    quaTask.setTaskNo(ElementTypeEnum.KAFKA.getCode() + "-" + quaJob.getId() + "-" + System.currentTimeMillis());
                    quaTask.setStartTime(LocalDateTime.now());
                    quaTask.setTaskProgress("0%");
                    quaTask.setStatus(TaskStatusEnum.RUNNING.getStatus());
                    quaTask.setTenantId(quaJob.getTenantId());
                    quaTask.setElementId(quaWabElement.getId());
                    quaTaskService.save(quaTask);

                    String taskNo = quaTask.getTaskNo();

                    try {
                        // 获取Kafka配置
                        String kafkaBrokers = quaWabElement.getKafkaBrokers();
                        String kafkaUserName = quaWabElement.getKafkaUserName();
                        String kafkaUserPassword = BtoaEncode.decrypt(quaWabElement.getKafkaUserPassword());

                        Map<String, ContentBody> reqParam = new HashMap<>();
                        reqParam.put("scanType", new StringBody(ElementTypeEnum.KAFKA.getCode(), ContentType.MULTIPART_FORM_DATA));
                        if(StringUtils.isNotEmpty(taskNo)){
                            reqParam.put("taskNo", new StringBody(taskNo, ContentType.MULTIPART_FORM_DATA));
                        }
                        if(StringUtils.isNotEmpty(kafkaBrokers)){
                            reqParam.put("kafkaBrokers", new StringBody(kafkaBrokers, ContentType.MULTIPART_FORM_DATA));
                        }
                        if(StringUtils.isNotEmpty(kafkaUserName)){
                            reqParam.put("kafkaUserName", new StringBody(kafkaUserName, ContentType.MULTIPART_FORM_DATA));
                        }
                        if(StringUtils.isNotEmpty(kafkaUserPassword)){
                            reqParam.put("kafkaUserPassword", new StringBody(kafkaUserPassword, ContentType.MULTIPART_FORM_DATA));
                        }


                        BASE64Decoder decoder = new BASE64Decoder();
                        if (!StringUtils.isEmpty(quaWabElement.getKeytabFilePath())) {
                            File keytab = new File(new String(decoder.decodeBuffer(quaWabElement.getKeytabFilePath())));
                            if (keytab.exists()) {
                                reqParam.put("keytabFile", new FileBody(keytab));
                            }
                        }
                        if (!StringUtils.isEmpty(quaWabElement.getKrb5FilePath())) {
                            File krb5 = new File(new String(decoder.decodeBuffer(quaWabElement.getKrb5FilePath())));
                            if (krb5.exists()) {
                                reqParam.put("krb5File", new FileBody(krb5));
                            }
                        }
                        log.info("发送扫描Kafka请求： {}","taskNo：".concat(taskNo).concat("， brokers:").concat(kafkaBrokers));
                        SendScanUtil.scan(addScanUrl, reqParam);

                    } catch (Exception e) {
                        log.error("扫描Kafka失败", e);
                        quaTask.setStatus(TaskStatusEnum.EXCEPTION.getStatus());
                        quaTask.setEndTime(LocalDateTime.now());
                        quaTask.setResult(e.getMessage());
                        quaTaskService.updateById(quaTask);
                    }
                } finally {
                    RedissonLockUtil.unlock(lockKey);
                }
            }
        } catch (Exception e) {
            log.error("执行Kafka扫描任务失败", e);
            throw new JobExecutionException(e);
        }
    }
} 