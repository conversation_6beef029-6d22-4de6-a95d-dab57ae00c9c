package com.idss.datalake.datagovern.metadata.config.schedule;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.redis.RedissonLockUtil;
import com.idss.datalake.common.util.HttpUtils;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaJob;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaTask;
import com.idss.datalake.datagovern.metadata.model.job.enums.TaskStatusEnum;
import com.idss.datalake.datagovern.metadata.model.job.manager.SyncManager;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.scan.ScanResultVo;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaTaskService;
import com.idss.radar.datasource.DatasourceType;
import com.idss.datalake.datagovern.metadata.model.job.service.ScanListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * @Author: xiexiaofei
 * @Date: 19/6/2021 14:35
 * @Description:
 */
@Component
@Slf4j
public class ScheduleConfig {
    private static final String LOCK_KEY_PRE = "DATA_LAKE_SCHEDULE_CONFIG_KEY_";

    public final static long FIXED_TIME = 30 * 1000;

    private static final String PROCESS_URL = "/scan/getProcess/%s";

    private static final String RESSULT_URL = "/scan/getScanResult/%s";

    @Value("${data.collect.url}")
    private String collectUrl;

    @Autowired
    private QuaTaskService quaTaskService;
    @Autowired
    private SyncManager syncManager;
    @Autowired
    private ScanListService scanListService;

    // @Transactional(rollbackFor = RuntimeException.class, isolation = Isolation.REPEATABLE_READ, propagation = Propagation.REQUIRED)
    @Scheduled(fixedDelay = FIXED_TIME)
    public void fixedDelayJob() {
        try {
            if (RedissonLockUtil.tryLock(LOCK_KEY_PRE)) {
                Thread.sleep(3000L);



                log.info("======= 开始同步数据 =======");
                QuaTask one = null;
                try {
                    //查询正在扫描的任务
                    List<QuaTask> taskList = quaTaskService.list(new QueryWrapper<QuaTask>().eq("status", TaskStatusEnum.RUNNING.getStatus()).orderByAsc(
                            "create_time"));
                    if (taskList != null && taskList.size() > 0) {
                        one = taskList.get(0);
                        int total = one.getCheckNum();
                        log.info("查询扫描任务: " + one.getTaskNo());
                        //检查扫描进度
                        String processUrl = String.format(PROCESS_URL, one.getTaskNo());
                        processUrl = collectUrl + processUrl;
                        String processResult = HttpUtils.doGet(processUrl, null, null);
                        if ("100%".equals(processResult)) {
                            //获取扫描结果
                            /*String resultUrl = String.format(RESSULT_URL, one.getTaskNo());
                            resultUrl = collectUrl + resultUrl;
                            String result = HttpUtils.doGet(resultUrl, null, null);
                            ScanResultVo scanResultVo = JSONObject.parseObject(result, ScanResultVo.class);
                            log.info("{}扫描结果{}", one.getTaskNo(), JSONObject.toJSONString(scanResultVo));*/
                            log.info("获取任务{}的扫描结果", one.getTaskNo());
                            ScanResultVo scanResultVo = scanListService.getScanResult(one.getTaskNo());
                            //同步任务结果与清单数据
                            boolean sync = syncManager.sync(scanResultVo, one.getId(), one.getElementId(), one.getTenantId());
                            if (!sync) {
                                log.info("======= 同步数据失败，记录失败状态 =======");
                                one.setStatus(TaskStatusEnum.EXCEPTION.getStatus());
                                one.setResult("同步数据失败");
                                quaTaskService.updateById(one);
                            } else {
                                log.info("======= 同步数据成功 =======");
                            }
                            return;
                        }

                        total = total + 1;
                        if (total >= 10) {
                            //判定获取结果异常
                            log.info("获取结果异常，超过10次，任务编号：{}", one.getTaskNo());
                            one.setStatus(TaskStatusEnum.EXCEPTION.getStatus());
                            one.setResult("获取扫描结果失败");
                        } else {
                            one.setCheckNum(total);
                        }
                        quaTaskService.updateById(one);
                    }
                } catch (Exception e) {
                    log.error("批次处理待扫描任务失败: {}", e.getMessage(), e);
                    if (one != null) {
                        one.setStatus(TaskStatusEnum.EXCEPTION.getStatus());
                        one.setResult(e.getMessage());
                        quaTaskService.updateById(one);
                    }
                }


            }
        } catch (Exception e) {
            log.error("DATA_LAKE_SCHEDULE_CONFIG_KEY执行失败:", e);
        } finally {
            if (RedissonLockUtil.isLocked(LOCK_KEY_PRE)) {
                RedissonLockUtil.unlock(LOCK_KEY_PRE);
            }
            DatasourceType.clearDataBaseType();
        }

    }
}
