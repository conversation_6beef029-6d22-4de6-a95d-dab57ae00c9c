package com.idss.datalake.datagovern.metadata.model.job.service.impl;

import com.idss.datalake.datagovern.metadata.model.job.entity.QuaWebMysqlTaskResultColumn;
import com.idss.datalake.datagovern.metadata.model.job.mapper.QuaWebMysqlTaskResultColumnMapper;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebMysqlTaskResultColumnService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * mysql扫描Column记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Service
public class QuaWebMysqlTaskResultColumnServiceImpl extends ServiceImpl<QuaWebMysqlTaskResultColumnMapper, QuaWebMysqlTaskResultColumn> implements IQuaWebMysqlTaskResultColumnService {

    @Override
    public String maxSnapshootVersionByElementId(Long elementId) {
        return this.baseMapper.maxSnapshootVersionByElementId(elementId);
    }
}
