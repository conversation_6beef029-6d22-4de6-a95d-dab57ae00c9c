package com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO;

import cn.hutool.core.annotation.Alias;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("盘维列导入DTO")
public class PanweiColumnImportDTO {

    @NotNull(message = "资源ID 不可为空")
    private Long elementId;
    @NotEmpty(message = "快照版本 不可为空")
    private String snapshootVersion;
    @NotNull(message = "库ID 不可为空")
    private Long dbId;
    @NotEmpty(message = "库名称 不可为空")
    private String dbName;
    @NotNull(message = "表ID 不可为空")
    private Long tableId;
    @NotEmpty(message = "表名称 不可为空")
    private String tableName;

    /**
     * 上传的csv文件位置
     */
    @NotEmpty(message = "文件 不可为空")
    private String filePath;

    @Data
    @ApiModel("列信息")
    public static class ColumnInfo {
        @Alias(value = "字段名称")
        private String schemaName;

        @Alias(value = "字段名称")
        private String columnName;

        @Alias(value = "字段中文名")
        private String columnNameCn;
        @Alias(value = "是否必填")
        private String isRequiredStr;
        @Alias(value = "中文描述")
        private String cnDesc;
        @Alias(value = "枚举值")
        private String enumValue;
        @Alias(value = "映射字段")
        private String mappingFields;

        @Alias(value = "是否为敏感表")
        private String isSensitiveStr;

        @Alias(value = "是否加密")
        private String isEncryptedStr;

        @Alias(value = "是否可用")
        private String isAvailableStr;

        @Alias("列类型")
        private String columnType;

        @Alias("列长度")
        private Long columnLength;

        @Alias(value = "可否为空")
        private String isNullableStr;

        @Alias("默认值")
        private String defaultValue;

        @Alias("列注释")
        private String columnComment;

        @Alias(value = "排序字段")
        private Integer sort;
    }
} 