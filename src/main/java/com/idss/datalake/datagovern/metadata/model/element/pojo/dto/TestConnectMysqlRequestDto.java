package com.idss.datalake.datagovern.metadata.model.element.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @Author: xiexiaofei
 * @Date: 18/6/2021 17:45
 * @Description:
 */
@ApiModel("测试Mysql请求体")
@Data
public class TestConnectMysqlRequestDto {
    @NotEmpty(message = "IP不能为空")
    private String mysqlIp;

    @NotNull(message = "端口不能为空")
    private Integer mysqlPort;

    @NotEmpty(message = "不能为空")
    private String mysqlUserName;

    @NotEmpty(message = "密码不能为空")
    private String mysqlUserPassword;
}
