/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/14 10:41
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.metadata.model.job.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorModel;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorModelResource;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorModelResourceService;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorModelService;
import com.idss.datalake.datagovern.metadata.model.detail.entity.*;
import com.idss.datalake.datagovern.metadata.model.detail.service.*;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.job.entity.*;
import com.idss.datalake.datagovern.metadata.model.job.enums.TaskStatusEnum;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.scan.ScanResultVo;
import com.idss.datalake.datagovern.metadata.model.job.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright 2022 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/14 10:41
 */
@Component
@Slf4j
public class SyncMysqlManager extends AbstractSync {
    @Autowired
    private IQuaWebMysqlTaskResultDbService taskResultDbService;
    @Autowired
    private IQuaWebMysqlTaskResultTableService taskResultTableService;
    @Autowired
    private IQuaWebMysqlTaskResultColumnService taskResultColumnService;
    @Autowired
    private IQuaWebMysqlElementDetailDbService detailDbService;
    @Autowired
    private IQuaWebMysqlElementDetailTableService detailTableService;
    @Autowired
    private IQuaWebMysqlElementDetailColumnService detailColumnService;
    @Autowired
    private QuaTaskService taskService;
    @Autowired
    private QuaWabElementService elementService;

    @Autowired
    private IQuaMonitorModelService monitorModelService;
    @Autowired
    private IQuaMonitorModelResourceService modelResourceService;

    /**
     * 同步数据Mysql (任务数据和清单数据)
     *
     * @param vo
     * @param taskId
     * @param elementId
     */
    public void syncMysql(ScanResultVo vo, Long taskId, Long elementId, Long tenantId) throws Exception {
        //最近的扫描结果,替换版本号字段再比对
        String snapshootVersion = vo.getMysqlDbs().get(0).getSnapshootVersion();
        //1.判断元数据与最近一次扫描是否有差异，如有差异再入库，没有差异就不处理
        ScanResultVo leastVo = new ScanResultVo();
        List<QuaTask> leastList = taskService.list(new QueryWrapper<QuaTask>().eq("element_id", elementId).eq("status",
                TaskStatusEnum.FINISH_SAVE.getStatus()).orderByDesc("create_time"));
        if (leastList == null || leastList.size() == 0) {
            log.info("======= 无最近扫描数据，直接处理 =======");
            //没有最近的扫描结果，直接处理
            //保存任务结果数据
            syncTaskResultMysql(vo, taskId, elementId, tenantId);
            //同步清单维表数据
            syncDetailMysql(vo, taskId, elementId, tenantId);
            QuaTask task = taskService.getById(taskId);
            task.setTaskProgress("100%");
            task.setStatus(TaskStatusEnum.FINISH_SAVE.getStatus());
            task.setResult(TaskStatusEnum.FINISH_SAVE.getDesc());
            task.setSnapshootVersion(snapshootVersion);
            task.setEndTime(LocalDateTime.now());
            taskService.updateById(task);

            log.info("======= 更新最近抽取时间 =======");
            updateElementLeastTime(task.getElementId());
        } else {
            //查询有最近的扫描结果，将版本号设置新的版本号再比对结果
            List<QuaWebMysqlTaskResultDb> leastDBs = taskResultDbService.list(new QueryWrapper<QuaWebMysqlTaskResultDb>().eq("snapshoot_version",
                    leastList.get(0).getSnapshootVersion()));
            List<QuaWebMysqlTaskResultTable> leastTables = taskResultTableService.list(new QueryWrapper<QuaWebMysqlTaskResultTable>().eq(
                    "snapshoot_version", leastList.get(0).getSnapshootVersion()));
            List<QuaWebMysqlTaskResultColumn> leastColumns = taskResultColumnService.list(new QueryWrapper<QuaWebMysqlTaskResultColumn>().eq(
                    "snapshoot_version", leastList.get(0).getSnapshootVersion()));

            List<QuaScanMysqlDb> mysqlDbs = new ArrayList<>();
            //迭代库
            for (QuaWebMysqlTaskResultDb leastDB : leastDBs) {
                QuaScanMysqlDb mysqlDb = new QuaScanMysqlDb();
                mysqlDb.setDbName(leastDB.getDbName());
                mysqlDb.setSnapshootVersion(snapshootVersion);
                List<QuaScanMysqlTable> mysqlTables = new ArrayList<>();
                //迭代表
                for (QuaWebMysqlTaskResultTable leastTable : leastTables) {
                    if (leastTable.getDbId().longValue() == leastDB.getId().longValue()) {
                        QuaScanMysqlTable mysqlTable = new QuaScanMysqlTable();
                        BeanUtils.copyProperties(leastTable, mysqlTable);
                        mysqlTable.setSnapshootVersion(snapshootVersion);
                        mysqlTables.add(mysqlTable);
                        List<QuaScanMysqlColumn> mysqlColumns = new ArrayList<>();
                        //迭代字段
                        for (QuaWebMysqlTaskResultColumn leastColumn : leastColumns) {
                            if (leastColumn.getTableId().longValue() == leastTable.getId().longValue()) {
                                QuaScanMysqlColumn mysqlColumn = new QuaScanMysqlColumn();
                                BeanUtils.copyProperties(leastColumn, mysqlColumn);
                                mysqlColumn.setSnapshootVersion(snapshootVersion);
                                mysqlColumns.add(mysqlColumn);
                            }
                        }
                        //设置字段
                        mysqlTable.setMysqlColumns(mysqlColumns);
                    }
                }
                //设置表
                mysqlDb.setMysqlTables(mysqlTables);
                //设置库
                mysqlDbs.add(mysqlDb);
            }
            leastVo.setMysqlDbs(mysqlDbs);
            leastVo.setScanType(ElementTypeEnum.MYSQL.getCode());
            if (vo.equals(leastVo)) {
                log.info("======= 与最近扫描数据无差异 =======");
                //扫描结果无差异，不需要更新版本
                QuaTask task = taskService.getById(taskId);
                task.setStatus(TaskStatusEnum.FINISH_NO_SAVE.getStatus());
                task.setResult(TaskStatusEnum.FINISH_NO_SAVE.getDesc());
                task.setTaskProgress("100%");
                task.setSnapshootVersion(snapshootVersion);
                task.setEndTime(LocalDateTime.now());
                log.info("======= 更新同步状态 =======");
                taskService.updateById(task);
            } else {
                log.info("======= 与最近扫描数据有差异 =======");
                //扫描结果有差异，需要更新
                //保存任务数据
                syncTaskResultMysql(vo, taskId, elementId, tenantId);
                //同步清单维表数据
                syncDetailMysql(vo, taskId, elementId, tenantId);
                log.info("======= 更新同步状态 =======");
                QuaTask task = taskService.getById(taskId);
                task.setStatus(TaskStatusEnum.FINISH_SAVE.getStatus());
                task.setResult(TaskStatusEnum.FINISH_SAVE.getDesc());
                task.setSnapshootVersion(snapshootVersion);
                task.setTaskProgress("100%");
                task.setEndTime(LocalDateTime.now());
                taskService.updateById(task);

                log.info("======= 更新最近抽取时间 =======");
                updateElementLeastTime(task.getElementId());

            }
        }
    }

    /**
     * 同步Mysql任务结果数据
     *
     * @param vo
     * @param taskId
     * @param elementId
     * @param tenantId
     */
    private void syncTaskResultMysql(ScanResultVo vo, Long taskId, Long elementId, Long tenantId) {
        QuaWabElement elementById = elementService.getById(elementId);

        List<QuaScanMysqlDb> mysqlDbs = vo.getMysqlDbs();
        List<QuaWebMysqlTaskResultDb> taskResultDbs = new ArrayList<>();
        for (QuaScanMysqlDb mysqlDb : mysqlDbs) {
            QuaWebMysqlTaskResultDb db = new QuaWebMysqlTaskResultDb();
            BeanUtils.copyProperties(mysqlDb, db);
            db.setTaskId(taskId);
            db.setTenantId(tenantId);
            db.setElementId(elementId);
            db.setCreateUser(elementById.getCreateUser());
            taskResultDbs.add(db);
        }
        //批量保存任务Mysql库数据
        log.info("======= 批量保存任务结果-elementId:{},taskId:{},库(SIZE::{}) =======", elementId, taskId, taskResultDbs.size());
        taskResultDbService.saveBatch(taskResultDbs);
        List<QuaWebMysqlTaskResultTable> taskResultTables = new ArrayList<>();
        for (QuaScanMysqlDb mysqlDb : mysqlDbs) {
            List<QuaScanMysqlTable> mysqlTables = mysqlDb.getMysqlTables();
            for (QuaScanMysqlTable mysqlTable : mysqlTables) {
                QuaWebMysqlTaskResultTable table = new QuaWebMysqlTaskResultTable();
                BeanUtils.copyProperties(mysqlTable, table);
                table.setDbId(taskResultDbs.stream().filter(
                        db -> db.getDbName().equals(mysqlDb.getDbName())).collect(Collectors.toList()).get(0).getId());
                table.setElementId(elementId);
                table.setTenantId(tenantId);
                table.setCreateUser(elementById.getCreateUser());
                taskResultTables.add(table);
            }
        }
        //批量保存任务Mysql表数据
        log.info("======= 批量保存任务结果-elementId:{},taskId:{},表(SIZE::{}) =======", elementId, taskId, taskResultTables.size());
        taskResultTableService.saveBatch(taskResultTables);
        List<QuaWebMysqlTaskResultColumn> taskResultColumns = new ArrayList<>();
        for (QuaScanMysqlDb mysqlDb : mysqlDbs) {
            List<QuaScanMysqlTable> mysqlTables = mysqlDb.getMysqlTables();
            for (QuaScanMysqlTable mysqlTable : mysqlTables) {
                List<QuaScanMysqlColumn> mysqlColumns = mysqlTable.getMysqlColumns();
                for (QuaScanMysqlColumn mysqlColumn : mysqlColumns) {
                    QuaWebMysqlTaskResultColumn column = new QuaWebMysqlTaskResultColumn();
                    BeanUtils.copyProperties(mysqlColumn, column);
                    column.setTableId(taskResultTables.stream().filter(
                                    table -> table.getDbName().equals(column.getDbName())
                                            && table.getTableName().equals(column.getTableName()))
                            .collect(Collectors.toList()).get(0).getId());
                    column.setElementId(elementId);
                    column.setTenantId(tenantId);
                    column.setCreateUser(elementById.getCreateUser());
                    taskResultColumns.add(column);
                }
            }
        }
        //批量保存任务Mysql字段数据
        log.info("======= 批量保存任务结果-elementId:{},taskId:{},字段(SIZE::{}) =======", elementId, taskId, taskResultColumns.size());
        taskResultColumnService.saveBatch(taskResultColumns);

        // 更新质量检测模型中db和table的id
        /*String snapshootVersion = mysqlDbs.get(0).getSnapshootVersion();
        List<QuaMonitorModel> monitorModels = monitorModelService.list(new QueryWrapper<QuaMonitorModel>()
                .eq("tenant_id", tenantId).eq("element_id", elementId));
        for (QuaMonitorModel monitorModel : monitorModels) {
            QuaWebMysqlTaskResultDb resultDbOne = taskResultDbService.getOne(new QueryWrapper<QuaWebMysqlTaskResultDb>()
                    .eq("tenant_id", tenantId).eq("element_id", elementId)
                    .eq("db_name", monitorModel.getDatabaseName()).eq("snapshoot_version", snapshootVersion));
            if (resultDbOne != null) {
                monitorModelService.update(new UpdateWrapper<QuaMonitorModel>().set("database_id", resultDbOne.getId())
                        .eq("id", monitorModel.getId()));
                log.info("更新模型的database_id：[{}]->[{}],elementId:{},taskId:{}", monitorModel.getDatabaseId(), resultDbOne.getId(), elementId, taskId);
                List<QuaMonitorModelResource> resources = modelResourceService.list(new QueryWrapper<QuaMonitorModelResource>()
                        .eq("model_id", monitorModel.getId()));
                for (QuaMonitorModelResource resource : resources) {
                    QuaWebMysqlTaskResultTable resultTableOne = taskResultTableService.getOne(new QueryWrapper<QuaWebMysqlTaskResultTable>()
                            .eq("tenant_id", tenantId).eq("element_id", elementId).eq("table_name", resource.getTableName())
                            .eq("db_id", resultDbOne.getId()).eq("snapshoot_version", snapshootVersion));
                    if (resultTableOne != null) {
                        modelResourceService.update(new UpdateWrapper<QuaMonitorModelResource>().set("table_id", resultTableOne.getId())
                                .eq("id", resource.getId()));
                        log.info("更新模型资源的table_id：[{}]->[{}],elementId:{},taskId:{}", resource.getTableId(), resultTableOne.getId(), elementId,
                                taskId);
                    }
                }
            }
        }*/
    }

    /**
     * 同步Mysql清单数据
     *
     * @param vo
     * @param taskId
     * @param elementId
     * @param tenantId
     */
    private void syncDetailMysql(ScanResultVo vo, Long taskId, Long elementId, Long tenantId) {
        QuaWabElement elementByid = elementService.getById(elementId);
        String snapshootVersion = vo.getMysqlDbs().get(0).getSnapshootVersion();
        List<QuaScanMysqlDb> mysqlDbs = vo.getMysqlDbs();
        List<QuaScanMysqlTable> tableList = new ArrayList<>();
        List<QuaScanMysqlColumn> columnList = new ArrayList<>();
        for (QuaScanMysqlDb mysqlDb : mysqlDbs) {
            List<QuaScanMysqlTable> mysqlTables = mysqlDb.getMysqlTables();
            for (QuaScanMysqlTable mysqlTable : mysqlTables) {
                columnList.addAll(mysqlTable.getMysqlColumns());
            }
            tableList.addAll(mysqlTables);
        }

        log.info("======= 同步清单维表数据-库 =======");
        //比对库
        List<QuaWebMysqlElementDetailDb> dbs = detailDbService.list(new QueryWrapper<QuaWebMysqlElementDetailDb>().eq("element_id", elementId));
        List<QuaWebMysqlElementDetailDb> addDbs = new ArrayList<>();
        if (dbs != null && dbs.size() > 0) {
            for (QuaScanMysqlDb mysqlDb : mysqlDbs) {
                if (dbs.stream().anyMatch(db -> db.getDbName().equals(mysqlDb.getDbName()))) {
                    //如果找到就更新版本号
                    for (QuaWebMysqlElementDetailDb detailDb : dbs) {
                        if (detailDb.getDbName().equals(mysqlDb.getDbName())) {
                            detailDb.setLastSnapshootVersion(snapshootVersion);
                        }
                    }
                } else {
                    //没有找到就新增
                    QuaWebMysqlElementDetailDb detailDb = new QuaWebMysqlElementDetailDb();
                    detailDb.setElementId(elementId);
                    detailDb.setDbName(mysqlDb.getDbName());
                    detailDb.setFirstSnapshootVersion(snapshootVersion);
                    detailDb.setLastSnapshootVersion(snapshootVersion);
                    detailDb.setTenantId(tenantId);
                    detailDb.setCreateUser(elementByid.getCreateUser());
                    detailDb.setUpdateUser(elementByid.getCreateUser());
                    addDbs.add(detailDb);
                }
            }
            detailDbService.updateBatchById(dbs);
        } else {
            for (QuaScanMysqlDb mysqlDb : mysqlDbs) {
                //没有找到就新增
                QuaWebMysqlElementDetailDb detailDb = new QuaWebMysqlElementDetailDb();
                detailDb.setElementId(elementId);
                detailDb.setDbName(mysqlDb.getDbName());
                detailDb.setFirstSnapshootVersion(snapshootVersion);
                detailDb.setLastSnapshootVersion(snapshootVersion);
                detailDb.setTenantId(tenantId);
                detailDb.setCreateUser(elementByid.getCreateUser());
                detailDb.setUpdateUser(elementByid.getCreateUser());
                addDbs.add(detailDb);
            }
        }
        if (addDbs.size() > 0) {
            detailDbService.saveBatch(addDbs);
        }

        log.info("======= 同步清单维表数据-表 =======");
        //比对表
        List<QuaWebMysqlElementDetailTable> tables = detailTableService.list(new QueryWrapper<QuaWebMysqlElementDetailTable>().eq("element_id",
                elementId));
        List<QuaWebMysqlElementDetailTable> addTables = new ArrayList<>();
        if (tables != null && tables.size() > 0) {
            for (QuaScanMysqlTable mysqlTable : tableList) {
                if (tables.stream().anyMatch(table -> table.getDbName().equals(mysqlTable.getDbName()) && table.getTableName().equals(mysqlTable.getTableName()))) {
                    for (QuaWebMysqlElementDetailTable detailTable : tables) {
                        if (detailTable.getDbName().equals(mysqlTable.getDbName()) && detailTable.getTableName().equals(mysqlTable.getTableName())) {
                            detailTable.setLastSnapshootVersion(snapshootVersion);
                        }
                    }
                } else {
                    //没有找到就新增
                    QuaWebMysqlElementDetailTable detailTable = new QuaWebMysqlElementDetailTable();
                    detailTable.setElementId(elementId);
                    detailTable.setDbName(mysqlTable.getDbName());
                    detailTable.setTableName(mysqlTable.getTableName());
                    detailTable.setFirstSnapshootVersion(snapshootVersion);
                    detailTable.setLastSnapshootVersion(snapshootVersion);
                    detailTable.setTenantId(tenantId);
                    detailTable.setCreateUser(elementByid.getCreateUser());
                    detailTable.setUpdateUser(elementByid.getCreateUser());
                    addTables.add(detailTable);
                }
            }
            detailTableService.updateBatchById(tables);
        } else {
            //没有找到就新增
            for (QuaScanMysqlTable mysqlTable : tableList) {
                QuaWebMysqlElementDetailTable detailTable = new QuaWebMysqlElementDetailTable();
                detailTable.setElementId(elementId);
                detailTable.setDbName(mysqlTable.getDbName());
                detailTable.setTableName(mysqlTable.getTableName());
                detailTable.setFirstSnapshootVersion(snapshootVersion);
                detailTable.setLastSnapshootVersion(snapshootVersion);
                detailTable.setTenantId(tenantId);
                detailTable.setCreateUser(elementByid.getCreateUser());
                detailTable.setUpdateUser(elementByid.getCreateUser());
                addTables.add(detailTable);
            }
        }
        if (addTables.size() > 0) {
            detailTableService.saveBatch(addTables);
        }

        log.info("======= 同步清单维表数据-字段 =======");
        //比对字段
        List<QuaWebMysqlElementDetailColumn> columns = detailColumnService.list(new QueryWrapper<QuaWebMysqlElementDetailColumn>().eq("element_id",
                elementId));
        List<QuaWebMysqlElementDetailColumn> addColumns = new ArrayList<>();
        if (columns != null && columns.size() > 0) {
            for (QuaScanMysqlColumn mysqlColumn : columnList) {
                if (columns.stream().anyMatch(column -> column.getDbName().equals(mysqlColumn.getDbName()) && column.getTableName().equals(mysqlColumn.getTableName()) && column.getColumnName().equals(mysqlColumn.getColumnName()))) {
                    for (QuaWebMysqlElementDetailColumn column : columns) {
                        if (column.getDbName().equals(mysqlColumn.getDbName()) && column.getTableName().equals(mysqlColumn.getTableName()) && column.getColumnName().equals(mysqlColumn.getColumnName())) {
                            column.setLastSnapshootVersion(snapshootVersion);
                            if (StringUtils.isBlank(column.getColumnNameCn()) && StringUtils.isNotBlank(mysqlColumn.getComment())) {
                                column.setColumnNameCn(mysqlColumn.getComment());
                            }
                        }
                    }
                } else {
                    //没有找到就新增
                    QuaWebMysqlElementDetailColumn column = new QuaWebMysqlElementDetailColumn();
                    column.setElementId(elementId);
                    column.setDbName(mysqlColumn.getDbName());
                    column.setTableName(mysqlColumn.getTableName());
                    column.setColumnName(mysqlColumn.getColumnName());
                    autocomplete(column);
                    column.setFirstSnapshootVersion(snapshootVersion);
                    column.setLastSnapshootVersion(snapshootVersion);
                    column.setTenantId(tenantId);
                    column.setCreateUser(elementByid.getCreateUser());
                    column.setUpdateUser(elementByid.getCreateUser());
                    column.setColumnNameCn(mysqlColumn.getComment());
                    addColumns.add(column);
                }
            }
            detailColumnService.updateBatchById(columns);
        } else {
            //没有找到就新增
            for (QuaScanMysqlColumn mysqlColumn : columnList) {
                QuaWebMysqlElementDetailColumn column = new QuaWebMysqlElementDetailColumn();
                column.setElementId(elementId);
                column.setDbName(mysqlColumn.getDbName());
                column.setTableName(mysqlColumn.getTableName());
                column.setColumnName(mysqlColumn.getColumnName());
                autocomplete(column);
                column.setFirstSnapshootVersion(snapshootVersion);
                column.setLastSnapshootVersion(snapshootVersion);
                column.setTenantId(tenantId);
                column.setCreateUser(elementByid.getCreateUser());
                column.setUpdateUser(elementByid.getCreateUser());
                column.setColumnNameCn(mysqlColumn.getComment());
                addColumns.add(column);
            }
        }
        if (addColumns.size() > 0) {
            detailColumnService.saveBatch(addColumns);
        }
    }

    /**
     * 自动补全
     *
     * @param field
     */
    private void autocomplete(QuaWebMysqlElementDetailColumn field) {
        if ("generic_datasource_type".equals(field.getColumnName())) {
            field.setColumnNameCn("采集设备类型");
            field.setCnDesc("采集设备类型");
        } else if ("generic_device_ip".equals(field.getColumnName())) {
            field.setColumnNameCn("采集设备ip");
            field.setCnDesc("采集设备ip");
        } else if ("parser_total".equals(field.getColumnName())) {
            field.setColumnNameCn("解析字段总数");
            field.setCnDesc(" 解析字段总数量");
        } else if ("parser_count".equals(field.getColumnName())) {
            field.setColumnNameCn("解析字段计数器");
            field.setCnDesc("解析字段总计数");
        } else if ("ueba_flow_id".equals(field.getColumnName())) {
            field.setColumnNameCn("数据采集任务id");
            field.setCnDesc("对应采集任务的任务id");
        } else if ("uuid".equals(field.getColumnName())) {
            field.setColumnNameCn("唯一标识");
            field.setCnDesc("唯一标识，采集入库自动生成无业务含义");
        } else if ("generic_into_time".equals(field.getColumnName())) {
            field.setColumnNameCn("入库时间");
            field.setCnDesc("采集入库的时间");
        } else if ("generic_create_time".equals(field.getColumnName())) {
            field.setColumnNameCn("读取数据的时间");
            field.setCnDesc("采集读取数据的时间");
        } else if ("generic_raw_log".equals(field.getColumnName())) {
            field.setColumnNameCn("原始日志");
            field.setCnDesc("源端发送的解析前的原始日志");
        }
    }
}
