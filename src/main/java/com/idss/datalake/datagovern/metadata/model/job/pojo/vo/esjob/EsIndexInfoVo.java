package com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.idss.datalake.datagovern.config.entity.QuaWebTableBaseInfo;
import com.idss.datalake.datagovern.config.entity.QuaWebTableModel;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.TableExtRequestDto;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTag;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: xiexiaofei
 * @Date: 23/6/2021 13:46
 * @Description:
 */
@Data
@ApiModel("Es索引信息")
public class EsIndexInfoVo {
    private String bussIndexNameCn;
    private String bussIndexDscribe;
    private String bussIndexOwner;
    private Integer bussIsSensitive;

    private Long detailId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime detailCreateTime;
    private String detailCreateUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime detailUpdateTime;
    private String detailUpdateUser;
    private Integer detailIsAvailable;
    private String detailFirstSnapshootVersion;
    private String detailLastSnapshootVersion;
    private String ipPorts;
    private String detailIndexName;
    private String detailTypeName;

    private Long snapshootId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime snapshootCreateTime;
    private String snapshootCreateUser;
    private String snapshootVersion;
    private String snapshootClusterName;
    private String snapshootClusterUuid;
    private String snapshootIndexState;
    private String snapshootIndexSettings;
    private String snapshootAliases;
    private String snapshootPrimaryTerms;
    private String snapshootInSyncAllocations;
    private List<TableExtRequestDto> extAttrs;

    /**
     * 元数据标签列表
     */
    private List<DataMetaTag> tagList;

    /**
     * DDL最后变更时间
     */
    private String ddlLastChangeTime;
    /**
     * 数据最后变更时间
     */
    private String dataLastChangeTime;
    /**
     * 最后查看时间
     */
    private String lastViewedTime;

    /**
     * 模型信息
     */
    private QuaWebTableModel tableModel;
    /**
     * 基础信息
     */
    private QuaWebTableBaseInfo tableBaseInfo;

    /**
     * 关键词，["a","b"]
     */
    private String keyWords;
}
