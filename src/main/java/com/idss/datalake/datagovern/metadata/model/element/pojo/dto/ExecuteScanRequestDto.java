package com.idss.datalake.datagovern.metadata.model.element.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: xiexiaofei
 * @Date: 18/6/2021 17:52
 * @Description: 执行扫描请求体
 */
@ApiModel("执行扫描请求体")
@Data
public class ExecuteScanRequestDto {
    @NotNull(message = "元数据ID 不能为空")
    @ApiModelProperty(value = "元数据ID")
    private Long elementId;
    @ApiModelProperty(value = "执行方式，2立即执行，3指定时间")
    private Integer executeType;
    @ApiModelProperty(value = "当时为指定时间时需传，格式 yyyy-MM-dd HH:mm:ss")
    private String dateTime;
}
