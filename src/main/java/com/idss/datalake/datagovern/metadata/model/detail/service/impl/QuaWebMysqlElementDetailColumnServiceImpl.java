package com.idss.datalake.datagovern.metadata.model.detail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.datagovern.dictionary.dto.DataDictionaryItemDTO;
import com.idss.datalake.datagovern.dictionary.enums.ItemTypeEnum;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryItemService;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.mapper.QuaWebMysqlElementDetailColumnMapper;
import com.idss.datalake.datagovern.metadata.model.detail.mapper.QuaWebMysqlElementDetailTableMapper;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.UpdateDetailChColumnBatchRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.UpdateDetailChColumnDto;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebMysqlElementDetailColumnService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * mysql清单Column表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Slf4j
@Service
public class QuaWebMysqlElementDetailColumnServiceImpl extends ServiceImpl<QuaWebMysqlElementDetailColumnMapper,
        QuaWebMysqlElementDetailColumn> implements IQuaWebMysqlElementDetailColumnService {

    @Resource
    private QuaWebMysqlElementDetailTableMapper detailTableMapper;
    @Autowired
    private IDataDictionaryItemService dataDictionaryItemService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public BaseResponse<String> updateDetail(UpdateDetailChColumnBatchRequestDto requestDto) {
        try {
            List<UpdateDetailChColumnDto> batchList = requestDto.getBatchList();
            List<Long> columnIds = batchList.stream().map(UpdateDetailChColumnDto::getId).collect(Collectors.toList());
            Collection<QuaWebMysqlElementDetailColumn> columns = listByIds(columnIds);
            loop:
            for (QuaWebMysqlElementDetailColumn column : columns) {
                for (UpdateDetailChColumnDto dto : batchList) {
                    if (dto.getId().longValue() == column.getId().longValue()) {
                        column.setColumnNameCn(dto.getColumnCnName());
                        column.setIsSensitive(dto.getIsSen());
                        column.setBusinessType(dto.getBusinessType());
                        column.setSenLevelId(dto.getSenLevelId());
                        column.setSenLevelName(dto.getSenLevelName());
                        column.setSenTypeId(dto.getSenTypeId());
                        column.setSenTypeName(dto.getSenTypeName());
                        column.setDesensitizationId(dto.getDesensitizationId());
                        column.setIsRequired(dto.getIsRequired());
                        column.setIsEncrypted(dto.getIsEncrypted());
                        column.setCnDesc(dto.getCnDesc());
                        column.setEnumValue(dto.getEnumValue());
                        column.setMappingFields(dto.getMappingFields());
                        column.setSort(dto.getSort());
                        continue loop;
                    }
                }
            }
            this.updateBatchById(columns);
            //判断是否都涉敏了
            QuaWebMysqlElementDetailColumn one = getById(requestDto.getBatchList().get(0).getId());
            List<QuaWebMysqlElementDetailColumn> allColumns = list(
                    new QueryWrapper<QuaWebMysqlElementDetailColumn>()
                            .eq("element_id", one.getElementId())
                            .eq("db_name", one.getDbName())
                            .eq("table_name", one.getTableName()));
            Integer sum =
                    allColumns.stream().map(column -> column.getIsSensitive() == null ? 0 : column.getIsSensitive()).reduce(Integer::sum).orElse(0);
            //更改表涉敏
            QuaWebMysqlElementDetailTable detailTable =
                    detailTableMapper.selectOne(
                            new QueryWrapper<QuaWebMysqlElementDetailTable>()
                                    .eq("element_id", one.getElementId())
                                    .eq("db_name", one.getDbName())
                                    .eq("table_name", one.getTableName()));
            if (sum > 0) {
                detailTable.setIsSensitive(1);
            } else {
                detailTable.setIsSensitive(0);
            }
            detailTableMapper.updateById(detailTable);

            // 更新数据字典项
            for (QuaWebMysqlElementDetailColumn column : columns) {
                DataDictionaryItemDTO itemDTO = new DataDictionaryItemDTO();
                itemDTO.setDatasourceType(DATA_SOURCE_TYPE_ENUM.MYSQL.getName());
                itemDTO.setItemType(ItemTypeEnum.field.name());
                itemDTO.setElementId(column.getElementId());
                itemDTO.setDatabaseName(column.getDbName());
                itemDTO.setTableName(column.getTableName());
                itemDTO.setItemName(column.getColumnName());
                itemDTO.setItemId(column.getId());
                itemDTO.setColumnNameCn(column.getColumnNameCn());
                itemDTO.setIsSensitive(column.getIsSensitive());
                itemDTO.setSenLevelId(column.getSenLevelId());
                itemDTO.setSenLevelName(column.getSenLevelName());
                itemDTO.setSenTypeId(column.getSenTypeId());
                itemDTO.setSenTypeName(column.getSenTypeName());
                itemDTO.setDesensitizationId(column.getDesensitizationId());
                itemDTO.setIsRequired(column.getIsRequired());
                itemDTO.setIsEncrypted(column.getIsEncrypted());
                itemDTO.setCnDesc(column.getCnDesc());
                itemDTO.setEnumValue(column.getEnumValue());
                itemDTO.setMappingFields(column.getMappingFields());
                itemDTO.setSort(column.getSort());

                dataDictionaryItemService.updateItemFromMeta(itemDTO);
            }
            return BaseResponse.success();
        } catch (Exception e) {
            log.error("批量更新清单 表-字段 失败：{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
