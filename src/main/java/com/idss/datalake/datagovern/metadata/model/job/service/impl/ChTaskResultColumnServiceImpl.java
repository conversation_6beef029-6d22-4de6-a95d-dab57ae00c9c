package com.idss.datalake.datagovern.metadata.model.job.service.impl;

import com.idss.datalake.datagovern.metadata.model.job.entity.ChTaskResultColumn;
import com.idss.datalake.datagovern.metadata.model.job.mapper.ChTaskResultColumnMapper;
import com.idss.datalake.datagovern.metadata.model.job.service.ChTaskResultColumnService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ch扫描Column记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Service
public class ChTaskResultColumnServiceImpl extends ServiceImpl<ChTaskResultColumnMapper, ChTaskResultColumn> implements ChTaskResultColumnService {

    @Override
    public String maxSnapshootVersionByElementId(Long elementId) {
        return this.baseMapper.maxSnapshootVersionByElementId(elementId);
    }
}
