/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/8/22
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/8/22
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadata.model.quartz;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.util.DateUtils;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorJob;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorJobService;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaJob;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaJobService;
import com.idss.datalake.datagovern.metadata.model.quartz.entity.BaseTask;
import com.idss.datalake.datagovern.metadata.model.quartz.job.QualityMonitorJob;
import com.idss.datalake.datagovern.metadata.model.quartz.job.ScanCHJob;
import com.idss.datalake.datagovern.metadata.model.quartz.job.ScanESJob;
import com.idss.datalake.datagovern.metadata.model.quartz.job.ScanHiveJob;
import com.idss.datalake.datagovern.metadata.model.quartz.job.ScanMysqlJob;
import com.idss.datalake.datagovern.metadata.model.quartz.service.QuartzJobService;
import com.idss.radar.datasource.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.quartz.CronExpression;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/8/22
 */
@Component
@Slf4j
public class QuartzSync {
    @Autowired
    private QuaJobService jobService;
    @Autowired
    private QuartzJobService quartzJobService;
    @Autowired
    private QuaWabElementService elementService;
    @Autowired
    private IQuaMonitorJobService monitorJobService;

    @Scheduled( fixedDelay = 5000)
    public void sync() {
        //在内存中的job
        try {
            log.info("开始同步Quartz定时任务");
            List<Object> exitsJobs = quartzJobService.getQuaJobs();
            List<QuaJob> exitsQuaJobs = new ArrayList<>();
            List<QuaMonitorJob> exitsQuaMonitorJobs = new ArrayList<>();
            for (Object exitsJob : exitsJobs) {
                if (exitsJob instanceof QuaJob) {
                    exitsQuaJobs.add((QuaJob) exitsJob);
                } else if (exitsJob instanceof QuaMonitorJob) {
                    exitsQuaMonitorJobs.add((QuaMonitorJob) exitsJob);
                }
            }

            //同步扫描job
            handleQuaJob(exitsQuaJobs);
            //同步监控job
            handleQuaMonitorJob(exitsQuaMonitorJobs);

        } catch (Exception e) {
            log.error("同步定时任务失败{}", e.getMessage());
        } finally {
            DatasourceType.clearDataBaseType();
        }
    }

    private void handleQuaMonitorJob(List<QuaMonitorJob> exitsQuaMonitorJobs) throws SchedulerException {
        List<QuaMonitorJob> monitorJobs = monitorJobService.list(new QueryWrapper<QuaMonitorJob>()
                .eq("flag", "1")
                .eq("execute_cycle", "02"));
        if (CollectionUtils.isNotEmpty(monitorJobs)) {
            for (QuaMonitorJob monitorJob : monitorJobs) {
                List<QuaMonitorJob> exitsInDbJob = exitsQuaMonitorJobs.stream().filter(data -> data.getId().longValue() == monitorJob.getId().longValue()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(exitsInDbJob)) {
                    QuaMonitorJob exitsJob = exitsInDbJob.get(0);
                    //两个更新时间不一致,更新
                    if (!DateUtils.localDateTimeToString(monitorJob.getUpdateTime()).equals(DateUtils.localDateTimeToString(exitsJob.getUpdateTime()))) {
                        //先删除
                        quartzJobService.deleteJob(JobKey.jobKey(exitsJob.getName(), String.valueOf(exitsJob.getId())));
                        //再新增
                        addMonitorJob(monitorJob);
                    }
                } else {
                    //新增
                    addMonitorJob(monitorJob);
                }
                //找出过期的oldTask 并移除
                List<Long> newIds = monitorJobs.stream().map(QuaMonitorJob::getId).collect(Collectors.toList());
                List<QuaMonitorJob> deleteJobs = exitsQuaMonitorJobs.stream().filter(data -> !newIds.contains(data.getId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(deleteJobs)) {
                    for (QuaMonitorJob deleteJob : deleteJobs) {
                        quartzJobService.deleteJob(JobKey.jobKey(deleteJob.getName(), String.valueOf(deleteJob.getId())));
                    }
                }
            }
        }
    }

    private void addMonitorJob(QuaMonitorJob monitorJob) throws SchedulerException {
        Map<String, Object> elementMap = new HashMap<>();
        elementMap.put("job", monitorJob);
        JobKey jobKey = JobKey.jobKey(monitorJob.getName(), String.valueOf(monitorJob.getId()));
        BaseTask task = new BaseTask(jobKey, monitorJob.getName(), monitorJob.getExecuteCron(), elementMap, QualityMonitorJob.class);
        quartzJobService.scheduleJob(task);
    }


    private void handleQuaJob(List<QuaJob> exitsQuaJobs) throws SchedulerException {
        List<QuaJob> dbQuaJobs = jobService.list(new QueryWrapper<QuaJob>()
                .eq("flag", "1")
                .in("job_type", "1","2","3"));
        if (CollectionUtils.isNotEmpty(dbQuaJobs)) {
            List<Long> elementIds = dbQuaJobs.stream().map(QuaJob::getElementId).collect(Collectors.toList());
            Collection<QuaWabElement> elements = elementService.listByIds(elementIds);
            for (QuaJob quaJob : dbQuaJobs) {
                try {
                    CronExpression cron = new CronExpression(quaJob.getJobCron());
                    // 获取下一个触发时间
                    Date nextValidTimeAfter = cron.getNextValidTimeAfter(new Date());
                    // 判断Cron表达式是否在当前时间之后有触发时间
                    if (nextValidTimeAfter == null) {
//                        log.info("该Cron表达式【{}】不会在此时间点之后触发",cron);
                        continue;
                    }
                } catch (ParseException e) {
                    log.error("cron表达式错误",e);
                    continue;
                }

                List<QuaWabElement> filterElements =
                        elements.stream().filter(element -> element.getId().longValue() == quaJob.getElementId().longValue()).collect(Collectors.toList());
                if (filterElements.size() == 0) {
                    log.info("检测到Job与Element不对应，JOBID:{}", quaJob.getId());
                } else {
                    QuaWabElement element = filterElements.get(0);
                    JobKey jobKey = JobKey.jobKey(quaJob.getJobName(), quaJob.getJobGroup());
                    Map<String, Object> elementMap = new HashMap<>();
                    elementMap.put("element", element);
                    elementMap.put("job", quaJob);
                    List<QuaJob> exitsInDbJob = exitsQuaJobs.stream().filter(data -> data.getId().longValue() == quaJob.getId().longValue()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(exitsInDbJob)) {
                        QuaJob exitsJob = exitsInDbJob.get(0);
                        LocalDateTime jobTime = quaJob.getUpdateTime();
                        LocalDateTime existTime = exitsJob.getUpdateTime();
                        //两个更新时间不一致,更新
                        if (jobTime == null || existTime == null || !DateUtils.localDateTimeToString(jobTime).equals(DateUtils.localDateTimeToString(existTime))) {
                            //先删除
                            quartzJobService.deleteJob(JobKey.jobKey(exitsJob.getJobName(), exitsJob.getJobGroup()));
                            //再新增
                            addJob(quaJob, element, jobKey, elementMap);
                        }
                    } else {
                        //新增
                        addJob(quaJob, element, jobKey, elementMap);
                    }
                }
            }
            //找出过期的oldTask 并移除
            List<Long> newIds = dbQuaJobs.stream().map(QuaJob::getId).collect(Collectors.toList());
            List<QuaJob> deleteJobs = exitsQuaJobs.stream().filter(data -> !newIds.contains(data.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deleteJobs)) {
                for (QuaJob deleteJob : deleteJobs) {
                    quartzJobService.deleteJob(JobKey.jobKey(deleteJob.getJobName(), deleteJob.getJobGroup()));
                }
            }
        }
    }

    private void addJob(QuaJob quaJob, QuaWabElement element, JobKey jobKey, Map<String, Object> elementMap) {
        try {
            if (ElementTypeEnum.CH.getCode().equals(element.getElementType())) {
                BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanCHJob.class);
                quartzJobService.scheduleJob(task);
            } else if (ElementTypeEnum.ES.getCode().equals(element.getElementType())) {
                BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanESJob.class);
                quartzJobService.scheduleJob(task);
            } else if (ElementTypeEnum.MYSQL.getCode().equals(element.getElementType())) {
                BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanMysqlJob.class);
                quartzJobService.scheduleJob(task);
            } else if (ElementTypeEnum.HIVE.getCode().equals(element.getElementType())) {
                BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanHiveJob.class);
                quartzJobService.scheduleJob(task);
            }
        } catch (SchedulerException e) {
            log.error("添加定时任务失败{}", e.getMessage());
        }
    }
}
