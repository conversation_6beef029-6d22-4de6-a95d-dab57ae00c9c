package com.idss.datalake.datagovern.metadata.model.job.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.Date;

/**
 * @Author: xiexiaofei
 * @Date: 26/6/2021 11:49
 * @Description: CH 导出
 */
@Data
public class ChExportDto {
    @ApiModelProperty(value = "CH节点IP地址")
    private String chIp;
    @ApiModelProperty(value = "CH节点端口")
    private Integer chPort;
    @ApiModelProperty(value = "账号")
    private String chUserName;
    @ApiModelProperty(value = "密码")
    private String chUserPassword;

    @ApiModelProperty(value = "快照-创建时间")
    private Date snapshootCreateTime;
    @ApiModelProperty(value = "快照-创建用户")
    private String snapshootCreateUser;
    @ApiModelProperty(value = "快照版本")
    private String snapshootVersion;
    @ApiModelProperty(value = "数据库名称")
    private String dbName;

    @ApiModelProperty(value = "维表-表-实体ID")
    private Long detailTableId;
    @ApiModelProperty(value = "维表-表-中文名")
    private String detailTableTableNameCn;
    @ApiModelProperty(value = "维表-表-业务描述")
    private String detailTableTableDscribe;
    @ApiModelProperty(value = "维表-表-责任人")
    private String detailTableTableOwner;
    @ApiModelProperty(value = "维表-表-是否涉敏")
    private Integer detailTableIsSensitive;
    @ApiModelProperty(value = "维表-表-创建时间")
    private Date detailTableCreateTime;
    @ApiModelProperty(value = "维表-表-创建用户")
    private String detailTableCreateUser;
    @ApiModelProperty(value = "维表-表-更新时间")
    private Date detailTableUpdateTime;
    @ApiModelProperty(value = "维表-表-更新用户")
    private String detailTableUpdateUser;
    @ApiModelProperty(value = "维表-表-是否可用")
    private Integer detailTableIsAvailable;
    @ApiModelProperty(value = "维表-表-最早快照版本号")
    private String detailTableFirstSnapshootVersion;
    @ApiModelProperty(value = "维表-表-最新快照版本号")
    private String detailTableLastSnapshootVersion;
    @ApiModelProperty(value = "维表-表-CH节点IP地址")
    private String detailTableChIp;
    @ApiModelProperty(value = "维表-表-CH节点端口")
    private Integer detailTableChPort;
    @ApiModelProperty(value = "维表-表-数据表名称")
    private String detailTableTableName;

    @ApiModelProperty(value = "维表-字段-实体ID")
    private Long detailColumnId;
    @ApiModelProperty(value = "维表-字段-名称")
    private String detailColumnColumnName;
    @ApiModelProperty(value = "维表-字段-中文名")
    private String detailColumnColumnNameCn;
    @ApiModelProperty(value = "维表-字段-是否敏感")
    private Integer detailColumnIsSensitive;
    @ApiModelProperty(value = "维表-字段-创建时间")
    private Date detailColumnCreateTime;
    @ApiModelProperty(value = "维表-字段-创建用户")
    private String detailColumnCreateUser;
    @ApiModelProperty(value = "维表-字段-更新时间")
    private Date detailColumnUpdateTime;
    @ApiModelProperty(value = "维表-字段-更新用户")
    private String detailColumnUpdateUser;
    @ApiModelProperty(value = "维表-字段-是否可用")
    private Integer detailColumnIsAvailable;
    @ApiModelProperty(value = "维表-字段-最早快照版本号")
    private String detailColumnFirstSnapshootVersion;
    @ApiModelProperty(value = "维表-字段-最新快照版本号")
    private String detailColumnLastSnapshootVersion;

    @ApiModelProperty(value = "快照-表-快照ID")
    private Long taskResultId;
    @ApiModelProperty(value = "快照-表-CH表系统ID")
    private String taskResultUuid;
    @ApiModelProperty(value = "快照-表-引擎类型")
    private String taskResultEngine;
    @ApiModelProperty(value = "快照-表-是否临时表")
    private Integer taskResultIsTemporary;
    @ApiModelProperty(value = "快照-表-数据地址")
    private String taskResultDataPaths;
    @ApiModelProperty(value = "快照-表-元数据地址")
    private String taskResultMetadataPath;
    @ApiModelProperty(value = "快照-表-元数据修改时间")
    private String taskResultMetadataModificationTime;
    @ApiModelProperty(value = "快照-表-依赖数据库")
    private String taskResultDependenciesDatabase;
    @ApiModelProperty(value = "快照-表-依赖数据表")
    private String taskResultDependenciesTable;
    @ApiModelProperty(value = "快照-表-建表语句")
    private String taskResultCreateTableQuery;
    @ApiModelProperty(value = "快照-表-引擎参数")
    private String taskResultEngineFull;
    @ApiModelProperty(value = "快照-表-分区字段")
    private String taskResultPartitionKey;
    @ApiModelProperty(value = "快照-表-排序字段")
    private String taskResultSortingKey;
    @ApiModelProperty(value = "快照-表-主键字段")
    private String taskResultPrimaryKey;
    @ApiModelProperty(value = "快照-表-抽样字段")
    private String taskResultSamplingKey;
    @ApiModelProperty(value = "快照-表-存储策略")
    private String taskResultStoragePolicy;
    @ApiModelProperty(value = "快照-表-总行数")
    private Long taskResultTotalRows;
    @ApiModelProperty(value = "快照-表-总字节数")
    private Long taskResultTotalBytes;
    @ApiModelProperty(value = "快照-表-插入总行数")
    private Long taskResultLifetimeRows;
    @ApiModelProperty(value = "快照-表-插入总字节数")
    private Long taskResultLifetimeBytes;

    @ApiModelProperty(value = "快照-字段-快照ID")
    private Long taskColumnId;
    @ApiModelProperty(value = "快照-字段-名称")
    private String taskColumnColumnName;
    @ApiModelProperty(value = "快照-字段-字段类型")
    private String taskColumnType;
    @ApiModelProperty(value = "快照-字段-字段序号")
    private Long taskColumnPosition;
    @ApiModelProperty(value = "快照-字段-字段默认值类型")
    private String taskColumnDefaultKind;
    @ApiModelProperty(value = "快照-字段-字段默认值表达式")
    private String taskColumnDefaultExpression;
    @ApiModelProperty(value = "快照-字段-压缩后字节数")
    private Long taskColumnDataCompressedBytes;
    @ApiModelProperty(value = "快照-字段-未压缩字节数")
    private Long taskColumnDataUncompressedBytes;
    @ApiModelProperty(value = "快照-字段-标记大小")
    private Long taskColumnMarksBytes;
    @ApiModelProperty(value = "快照-字段-字段描述")
    private String taskColumnComment;
    @ApiModelProperty(value = "快照-字段-是否分区字段")
    private Integer taskColumnIsInPartitionKey;
    @ApiModelProperty(value = "快照-字段-是否排序字段")
    private Integer taskColumnIsInSortingKey;
    @ApiModelProperty(value = "快照-字段-是否主键字段")
    private Integer taskColumnIsInPrimaryKey;
    @ApiModelProperty(value = "快照-字段-是否抽样字段")
    private Integer taskColumnIsInSamplingKey;
    @ApiModelProperty(value = "快照-字段-压缩编码器名称")
    private String taskColumnCompressionCodec;
    @ApiModelProperty(value = "快照-字段-可否为空")
    private Integer taskColumnIsNullable;
    @ApiModelProperty(value = "快照-字段-字段长度")
    private Integer taskColumnColumnLength;
    @ApiModelProperty(value = "快照-字段-字段精度")
    private Integer taskColumnColumnPrecision;

    public static void main(String[] args) {
        try {
            Class<?> clazz = Class.forName("com.idss.datalake.datagovern.metadata.model.job.pojo.dto.ChExportDto");
            Field[] declaredFields = clazz.getDeclaredFields();
            for (Field declaredField : declaredFields) {
                ApiModelProperty annotation = declaredField.getAnnotation(ApiModelProperty.class);
                System.out.println(annotation.value());
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }

    }

}
