package com.idss.datalake.datagovern.metadata.model.kbs.pojo.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.idss.datalake.datagovern.metadata.model.kbs.entity.KbsFileConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * Kbs认证文件配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Data
public class KbsFileConfigExt extends KbsFileConfig {


    private String keytabFilePathDesc;

    private String krb5FilePathDesc;

    private String jaasFilePathDesc;

}
