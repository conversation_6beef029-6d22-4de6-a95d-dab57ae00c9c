/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-05-20
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-05-20
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadata.model.detail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>磐维元素详情字段表</p>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_web_panwei_element_detail_column")
public class QuaWebPanweiElementDetailColumn implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 元素ID
     */
    @TableField("element_id")
    private Long elementId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 数据库名称
     */
    @TableField("db_name")
    private String dbName;

    /**
     * Schema名称
     */
    @TableField("schema_name")
    private String schemaName;

    /**
     * 表名称
     */
    @TableField("table_name")
    private String tableName;

    /**
     * 字段名称
     */
    @TableField("column_name")
    private String columnName;

    /**
     * 字段中文名，默认为空字符串
     */
    @TableField("column_name_cn")
    private String columnNameCn;

    /**
     * 是否为敏感表，默认不是
     */
    @TableField("is_sensitive")
    private Integer isSensitive;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField("create_user")
    private String createUser;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField("update_user")
    private String updateUser;

    /**
     * 是否可用，默认为1，可用
     */
    @TableField("is_available")
    private Integer isAvailable;

    /**
     * 最早快照版本号，新增记录时写入
     */
    @TableField("first_snapshoot_version")
    private String firstSnapshootVersion;

    /**
     * 最新快照版本号，更新记录时写入
     */
    @TableField("last_snapshoot_version")
    private String lastSnapshootVersion;

    /**
     * 业务系统
     */
    @TableField("business_type")
    private Long businessType;

    /**
     * 敏感分级ID
     */
    @TableField("sen_level_id")
    private Long senLevelId;

    /**
     * 敏感分级名称
     */
    @TableField("sen_level_name")
    private String senLevelName;

    /**
     * 敏感分类ID
     */
    @TableField("sen_type_id")
    private Long senTypeId;

    /**
     * 敏感分级名称
     */
    @TableField("sen_type_name")
    private String senTypeName;

    /**
     * 脱敏规则 ID
     */
    @TableField("desensitization_id")
    private Integer desensitizationId;

    /**
     * 是否必填，0-否，1-是
     */
    @TableField("is_required")
    private Integer isRequired;

    /**
     * 是否加密，0-否，1-是
     */
    @TableField("is_encrypted")
    private Integer isEncrypted;

    /**
     * 中文描述
     */
    @TableField("cn_desc")
    private String cnDesc;

    /**
     * 枚举值
     */
    @TableField("enum_value")
    private String enumValue;

    /**
     * 映射字段
     */
    @TableField("mapping_fields")
    private String mappingFields;

    /**
     * 排序字段
     */
    @TableField("sort")
    private Integer sort;


}
