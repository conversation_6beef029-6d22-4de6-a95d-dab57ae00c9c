package com.idss.datalake.datagovern.metadata.model.job.service.impl;

import com.idss.datalake.datagovern.metadata.model.job.entity.ChTaskResultTable;
import com.idss.datalake.datagovern.metadata.model.job.mapper.ChTaskResultTableMapper;
import com.idss.datalake.datagovern.metadata.model.job.service.ChTaskResultTableService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ch扫描Table记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Service
public class ChTaskResultTableServiceImpl extends ServiceImpl<ChTaskResultTableMapper, ChTaskResultTable> implements ChTaskResultTableService {

}
