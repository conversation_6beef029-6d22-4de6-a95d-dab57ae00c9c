/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadata.model.detail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>hdfs清单文件表</p>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_web_hdfs_element_detail_file")
public class QuaWebHdfsElementDetailFile implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("element_id")
    private Long elementId;

    /**
     * 名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 中文名称
     */
    @TableField("file_name_cn")
    private String fileNameCn;

    /**
     * 业务描述
     */
    @TableField("file_describe")
    private String fileDescribe;

    /**
     * 业务负责人
     */
    @TableField("file_owner")
    private String fileOwner;

    /**
     * 群组
     */
    @TableField("file_group")
    private String fileGroup;

    /**
     * 权限
     */
    @TableField("file_authority")
    private String fileAuthority;

    /**
     * 存储大小，单位字节
     */
    @TableField("storage_size")
    private Long storageSize;

    /**
     * 关键词，["a","b"]
     */
    @TableField("key_words")
    private String keyWords;

    /**
     * 是否为敏感，0非，1是，默认不是
     */
    @TableField("is_sensitive")
    private Integer isSensitive;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField("create_user")
    private String createUser;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField("update_user")
    private String updateUser;

    /**
     * 是否可用，0不可用，默认为1，可用
     */
    @TableField("is_available")
    private Integer isAvailable;

    /**
     * 自定义属性
     */
    @TableField("ext_attrs")
    private String extAttrs;

    /**
     * 最早快照版本号，新增记录时写入
     */
    @TableField("first_snapshoot_version")
    private String firstSnapshootVersion;

    /**
     * 最新快照版本号，更新记录时写入
     */
    @TableField("last_snapshoot_version")
    private String lastSnapshootVersion;

    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 最后查看时间
     */
    @TableField("last_viewed_time")
    private String lastViewedTime;

    /**
     * 元数据来源，1-自建，2-数据建模
     */
    @TableField("meta_source")
    private Integer metaSource;

    /**
     * 目录地址
     */
    @TableField("dir_path")
    private String dirPath;


}
