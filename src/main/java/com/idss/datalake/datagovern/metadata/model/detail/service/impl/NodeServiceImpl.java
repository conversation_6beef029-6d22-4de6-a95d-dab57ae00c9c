package com.idss.datalake.datagovern.metadata.model.detail.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.common.util.KafkaUtil;
import com.idss.datalake.datagovern.config.entity.DataScoreCalcConfig;
import com.idss.datalake.datagovern.config.entity.QuaAssetMaintenance;
import com.idss.datalake.datagovern.config.service.IDataScoreCalcConfigService;
import com.idss.datalake.datagovern.config.service.IQuaAssetMaintenanceService;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.mapper.QuaWebMysqlElementDetailTableMapper;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.NodeInfoRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementDbOverview;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeDbPage;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeOverview;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeTablePage;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.NodeInfoVo;
import com.idss.datalake.datagovern.metadata.model.detail.service.NodeService;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.mapper.QuaWabElementMapper;
import com.idss.datalake.datagovern.metadata.model.element.pojo.dto.EsOrChNodeDto;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaTask;
import com.idss.datalake.datagovern.metadata.model.job.enums.TaskStatusEnum;
import com.idss.datalake.datagovern.metadata.model.job.mapper.ChTaskResultDbMapper;
import com.idss.datalake.datagovern.metadata.model.job.mapper.ChTaskResultTableMapper;
import com.idss.datalake.datagovern.metadata.model.job.mapper.EsTaskResultIndexMapper;
import com.idss.datalake.datagovern.metadata.model.job.mapper.KafkaTopicMapper;
import com.idss.datalake.datagovern.metadata.model.job.mapper.QuaTaskMapper;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeDbDto;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeIndexDto;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTableDto;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTopicDto;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaJobService;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTag;
import com.idss.radar.util.UmsUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.admin.AdminClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: xiexiaofei
 * @Date: 24/6/2021 18:45
 * @Description:
 */
@Service
public class NodeServiceImpl implements NodeService {
    private static final Logger log = LoggerFactory.getLogger(NodeServiceImpl.class);

    @Resource
    private QuaWabElementMapper elementMapper;
    @Resource
    private EsTaskResultIndexMapper resultIndexMapper;
    @Resource
    private ChTaskResultDbMapper resultDbMapper;
    @Resource
    private ChTaskResultTableMapper resultTableMapper;
    @Resource
    private QuaTaskMapper taskMapper;
    @Resource
    private KafkaTopicMapper kafkaTopicMapper;
    @Resource
    private IQuaAssetMaintenanceService quaAssetMaintenanceService;
    @Autowired
    private IDataScoreCalcConfigService scoreCalcConfigService;
    @Autowired
    private QuaWebMysqlElementDetailTableMapper mysqlElementDetailTableMapper;
    @Autowired
    private QuaJobService jobService;

    @Override
    public BaseResponse<List<String>> getSnapshootVersion(Long elementId) {
        QuaWabElement element = elementMapper.selectOne(new QueryWrapper<QuaWabElement>().eq("id", elementId));
        if (element == null) {
            return BaseResponse.no_data();
        }
        List<QuaTask> quaTasks = taskMapper.selectList(new QueryWrapper<QuaTask>().isNotNull("snapshoot_version").eq("element_id", elementId).eq("status",
                TaskStatusEnum.FINISH_SAVE.getStatus()).orderByDesc("snapshoot_version"));
        if (quaTasks != null && quaTasks.size() > 0) {
            List<String> versions = quaTasks.stream().map(QuaTask::getSnapshootVersion).collect(Collectors.toList());
            return BaseResponse.success(versions);
        }
        return BaseResponse.no_data();
    }

    @Override
    public BaseResponse<List<NodeInfoVo>> getNodeListInfo(NodeInfoRequestDto requestDto, Long tenantId) {
        List<NodeInfoVo> voList = new ArrayList<>();
        if (tenantId == null) {
            tenantId = Long.valueOf(UmsUtils.getUVO().getTenantId());
        }
        if (ElementTypeEnum.ES.getCode().equals(requestDto.getType())) {
            //加载ES列表
            List<EsOrChNodeDto> esNode = elementMapper.getEsNode(tenantId);
            for (EsOrChNodeDto esOrChNodeDto : esNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType(ElementTypeEnum.ES.getCode());
                vo.setElementId(esOrChNodeDto.getElementId());
                vo.setElementName(esOrChNodeDto.getElementName());
                vo.setLabel(esOrChNodeDto.getElementName());
                vo.setCnt(esOrChNodeDto.getCnt());

                QuaWabElement element = elementMapper.selectById(esOrChNodeDto.getElementId());
                vo.setElementName(element.getElementName());
                vo.setIpPorts(element.getEsIpPort());

                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if (ElementTypeEnum.CH.getCode().equals(requestDto.getType())) {
            //加载CH列表
            List<EsOrChNodeDto> esNode = elementMapper.getChNode(tenantId);
            for (EsOrChNodeDto esOrChNodeDto : esNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType(ElementTypeEnum.CH.getCode());
                vo.setElementId(esOrChNodeDto.getElementId());
                vo.setElementName(esOrChNodeDto.getElementName());
                vo.setLabel(esOrChNodeDto.getElementName());
                vo.setCnt(esOrChNodeDto.getCnt());

                QuaWabElement element = elementMapper.selectById(esOrChNodeDto.getElementId());
                vo.setElementName(element.getElementName());
                vo.setIpPorts(element.getChIp() + ":" + element.getChPort());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if (ElementTypeEnum.MYSQL.getCode().equals(requestDto.getType())) {
            //加载MYSQL列表
            List<EsOrChNodeDto> esNode = elementMapper.getMysqlNode(tenantId);
            for (EsOrChNodeDto esOrChNodeDto : esNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType(ElementTypeEnum.MYSQL.getCode());
                vo.setElementId(esOrChNodeDto.getElementId());
                vo.setElementName(esOrChNodeDto.getElementName());
                vo.setLabel(esOrChNodeDto.getElementName());
                vo.setCnt(esOrChNodeDto.getCnt());

                QuaWabElement element = elementMapper.selectById(esOrChNodeDto.getElementId());
                vo.setElementName(element.getElementName());
                vo.setIpPorts(element.getChIp() + ":" + element.getChPort());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if (ElementTypeEnum.HIVE.getCode().equals(requestDto.getType())) {
            //加载Hive列表
            List<EsOrChNodeDto> esNode = elementMapper.getHiveNode(tenantId);
            for (EsOrChNodeDto esOrChNodeDto : esNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType(ElementTypeEnum.HIVE.getCode());
                vo.setElementId(esOrChNodeDto.getElementId());
                vo.setElementName(esOrChNodeDto.getElementName());
                vo.setLabel(esOrChNodeDto.getElementName());
                vo.setCnt(esOrChNodeDto.getCnt());

                QuaWabElement element = elementMapper.selectById(esOrChNodeDto.getElementId());
                vo.setElementName(element.getElementName());
                vo.setIpPorts(element.getChIp() + ":" + element.getChPort());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if (ElementTypeEnum.KAFKA.getCode().equals(requestDto.getType())) {
            //加载topic列表
            List<EsOrChNodeDto> kafkaNodes = elementMapper.getKafkaNode(tenantId);
            for (EsOrChNodeDto kafkaNode : kafkaNodes) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType(ElementTypeEnum.KAFKA.getCode());
                vo.setElementId(kafkaNode.getElementId());
                vo.setElementName(kafkaNode.getElementName());
                vo.setLabel(kafkaNode.getElementName());
                vo.setCnt(kafkaNode.getCnt());

                QuaWabElement element = elementMapper.selectById(kafkaNode.getElementId());
                vo.setElementName(element.getElementName());
                vo.setIpPorts(element.getKafkaBrokers());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if (ElementTypeEnum.PANWEI.getCode().equals(requestDto.getType())) {
            //加载PANWEI列表
            List<EsOrChNodeDto> esNode = elementMapper.getPanWeiNode(tenantId);
            for (EsOrChNodeDto esOrChNodeDto : esNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType(ElementTypeEnum.PANWEI.getCode());
                vo.setElementId(esOrChNodeDto.getElementId());
                vo.setElementName(esOrChNodeDto.getElementName());
                vo.setLabel(esOrChNodeDto.getElementName());
                vo.setCnt(esOrChNodeDto.getCnt());

                QuaWabElement element = elementMapper.selectById(esOrChNodeDto.getElementId());
                vo.setElementName(element.getElementName());
                vo.setIpPorts(element.getChIp() + ":" + element.getChPort());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if ("INDEX".equals(requestDto.getType())) {
            //加载某个ES下的索引列表,需传 elementId,snapshootVersion
            List<NodeIndexDto> indexNode = resultIndexMapper.getIndexNode(requestDto.getElementId(), requestDto.getSnapshootVersion());
            for (NodeIndexDto index : indexNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType("INDEX");
                vo.setIndexId(index.getIndexId());
                vo.setIndexName(index.getIndexName());
                vo.setLabel(index.getIndexName());
                vo.setCnt(index.getCnt());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if ("DB".equals(requestDto.getType())) {
            //加载某个CH下的DB列表,需传 elementId,snapshootVersion
            List<NodeDbDto> dbNode = resultDbMapper.getDbNode(requestDto.getElementId(), requestDto.getSnapshootVersion());
            for (NodeDbDto db : dbNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType("DB");
                vo.setDbId(db.getDbId());
                vo.setDbName(db.getDbName());
                vo.setLabel(db.getDbName());
                vo.setCnt(db.getCnt());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if ("TABLE".equals(requestDto.getType())) {
            //加载某个CH下的某个DB的table列表,,需传 dbId,snapshootVersion
            List<NodeTableDto> tableNode = resultTableMapper.getTableNode(requestDto.getDbId(), requestDto.getSnapshootVersion());
            for (NodeTableDto table : tableNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType("TABLE");
                vo.setTableId(table.getTableId());
                vo.setTableName(table.getTableName());
                vo.setLabel(table.getTableName());
                vo.setCnt(table.getCnt());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if ("MYSQL_DB".equals(requestDto.getType())) {
            //加载某个Mysql下的DB列表,需传 elementId,snapshootVersion
            List<NodeDbDto> dbNode = resultDbMapper.getMysqlDbNode(requestDto.getElementId(), requestDto.getSnapshootVersion());
            for (NodeDbDto db : dbNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType("MYSQL_DB");
                vo.setDbId(db.getDbId());
                vo.setDbName(db.getDbName());
                vo.setLabel(db.getDbName());
                vo.setCnt(db.getCnt());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if ("MYSQL_TABLE".equals(requestDto.getType())) {
            //加载某个CH下的某个DB的table列表,,需传 dbId,snapshootVersion
            List<NodeTableDto> tableNode = resultTableMapper.getMysqlTableNode(requestDto.getDbId(), requestDto.getSnapshootVersion());
            for (NodeTableDto table : tableNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType("MYSQL_TABLE");
                vo.setTableId(table.getTableId());
                vo.setTableName(table.getTableName());
                vo.setLabel(table.getTableName());
                vo.setCnt(table.getCnt());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if ("HIVE_DB".equals(requestDto.getType())) {
            //加载某个Mysql下的DB列表,需传 elementId,snapshootVersion
            List<NodeDbDto> dbNode = resultDbMapper.getHiveDbNode(requestDto.getElementId(), requestDto.getSnapshootVersion());
            for (NodeDbDto db : dbNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType("HIVE_DB");
                vo.setDbId(db.getDbId());
                vo.setDbName(db.getDbName());
                vo.setLabel(db.getDbName());
                vo.setCnt(db.getCnt());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if ("HIVE_TABLE".equals(requestDto.getType())) {
            //加载某个CH下的某个DB的table列表,,需传 dbId,snapshootVersion
            List<NodeTableDto> tableNode = resultTableMapper.getHiveTableNode(requestDto.getDbId(), requestDto.getSnapshootVersion());
            for (NodeTableDto table : tableNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType("HIVE_TABLE");
                vo.setTableId(table.getTableId());
                vo.setTableName(table.getTableName());
                vo.setLabel(table.getTableName());
                vo.setCnt(table.getCnt());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if ("TOPIC".equals(requestDto.getType())) {
            //加载某个KAFKA下的topic列表,需传 elementId,snapshootVersion
            List<NodeTopicDto> topicNode = kafkaTopicMapper.getKafkaTopicNode(requestDto.getElementId(), requestDto.getSnapshootVersion());
            for (NodeTopicDto topic : topicNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType("TOPIC");
                vo.setTopicId(topic.getTopicId());
                vo.setTopicName(topic.getTopicName());
                vo.setLabel(topic.getTopicName());
                vo.setCnt(0);
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if ("PANWEI_DB".equals(requestDto.getType())) {
            //加载某个PANWEI下的DB列表,需传 elementId,snapshootVersion
            List<NodeDbDto> dbNode = resultDbMapper.getPanweiDbNode(requestDto.getElementId(), requestDto.getSnapshootVersion());
            for (NodeDbDto db : dbNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType("PANWEI_DB");
                vo.setDbId(db.getDbId());
                vo.setDbName(db.getDbName());
                vo.setLabel(db.getDbName());
                vo.setCnt(db.getCnt());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        } else if ("PANWEI_TABLE".equals(requestDto.getType())) {
            //加载某个PANWEI下的某个DB的table列表,,需传 dbId,snapshootVersion
            List<NodeTableDto> tableNode = resultTableMapper.getPanweiTableNode(requestDto.getDbId(), requestDto.getSnapshootVersion());
            for (NodeTableDto table : tableNode) {
                NodeInfoVo vo = new NodeInfoVo();
                vo.setType("PANWEI_TABLE");
                vo.setTableId(table.getTableId());
                vo.setTableName(table.getTableName());
                vo.setLabel(table.getTableName());
                vo.setCnt(table.getCnt());
                voList.add(vo);
            }
            return BaseResponse.success(voList);
        }
        return BaseResponse.error("类型错误");
    }

    @Override
    public ElementNodeOverview getNodeOverview(NodeInfoRequestDto requestDto, Long tenantId) {
        ElementNodeOverview nodeOverview = new ElementNodeOverview();
        if (ElementTypeEnum.ES.getCode().equals(requestDto.getType())) {
            //加载某个ES下的索引列表,需传 elementId,snapshootVersion
            List<NodeIndexDto> indexNode = resultIndexMapper.getIndexNode(requestDto.getElementId(), requestDto.getSnapshootVersion());
            if (CollectionUtils.isNotEmpty(indexNode)) {
                nodeOverview.setDbCount(indexNode.size());
                nodeOverview.setTableCount(indexNode.stream().mapToLong(NodeIndexDto::getCnt).sum());
                // 获取维护信息
                List<QuaAssetMaintenance> maintenanceList = quaAssetMaintenanceService.list(new LambdaUpdateWrapper<QuaAssetMaintenance>()
                        .eq(QuaAssetMaintenance::getStatus, 0)
                        .eq(QuaAssetMaintenance::getElementId, requestDto.getElementId()));
                if (CollectionUtils.isNotEmpty(maintenanceList)) {
                    BigDecimal totalScore = maintenanceList.stream().map(QuaAssetMaintenance::getScore)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);  // 计算总和
                    BigDecimal avgScore = NumberUtil.div(totalScore, indexNode.size(), 2); // 平均维护分
                    nodeOverview.setAvgScore(avgScore);
                    long lowMaintenanceDbCount = maintenanceList.stream().filter(index -> index.getScore().compareTo(avgScore) < 0).count();
                    nodeOverview.setLowMaintenanceDbCount(lowMaintenanceDbCount);

                    long noMaintenanceTableCount =
                            maintenanceList.stream().filter(x -> NumberUtil.compare(x.getScore().doubleValue(), 0) == 0).count();
                    nodeOverview.setNoMaintenanceTableCount(noMaintenanceTableCount);
                }
            }
        } else if (ElementTypeEnum.CH.getCode().equals(requestDto.getType())) {
            //加载某个CH下的DB列表,需传 elementId,snapshootVersion
            List<NodeDbDto> dbNode = resultDbMapper.getDbNode(requestDto.getElementId(), requestDto.getSnapshootVersion());
            calcElementNodeOverview(requestDto.getElementId(), dbNode, nodeOverview);
        } else if (ElementTypeEnum.MYSQL.getCode().equals(requestDto.getType())) {
            //加载某个Mysql下的DB列表,需传 elementId,snapshootVersion
            List<NodeDbDto> dbNode = resultDbMapper.getMysqlDbNode(requestDto.getElementId(), requestDto.getSnapshootVersion());
            calcElementNodeOverview(requestDto.getElementId(), dbNode, nodeOverview);
        } else if (ElementTypeEnum.HIVE.getCode().equals(requestDto.getType())) {
            //加载某个Mysql下的DB列表,需传 elementId,snapshootVersion
            List<NodeDbDto> dbNode = resultDbMapper.getHiveDbNode(requestDto.getElementId(), requestDto.getSnapshootVersion());
            calcElementNodeOverview(requestDto.getElementId(), dbNode, nodeOverview);
        }
        return nodeOverview;
    }

    @Override
    public ElementDbOverview getDbOverview(NodeInfoRequestDto requestDto, Long tenantId) {
        DataScoreCalcConfig calcConfig = scoreCalcConfigService.getOne(new LambdaUpdateWrapper<>());
        ElementDbOverview dbOverview = new ElementDbOverview();
        if ("DB".equals(requestDto.getType())) {
            List<NodeTableDto> tableNode = resultTableMapper.getTableNode(requestDto.getDbId(), requestDto.getSnapshootVersion());
            calcElementDbOverview(requestDto.getElementId(), requestDto.getDbName(), tableNode, dbOverview, calcConfig);
        } else if ("MYSQL_DB".equals(requestDto.getType())) {
            List<NodeTableDto> tableNode = resultTableMapper.getMysqlTableNode(requestDto.getDbId(), requestDto.getSnapshootVersion());
            calcElementDbOverview(requestDto.getElementId(), requestDto.getDbName(), tableNode, dbOverview, calcConfig);
        } else if ("HIVE_DB".equals(requestDto.getType())) {
            List<NodeTableDto> tableNode = resultTableMapper.getHiveTableNode(requestDto.getDbId(), requestDto.getSnapshootVersion());
            calcElementDbOverview(requestDto.getElementId(), requestDto.getDbName(), tableNode, dbOverview, calcConfig);
        }
        return dbOverview;
    }

    @Override
    public Page<ElementNodeDbPage> getNodeDbPage(NodeInfoRequestDto request) {
        DataScoreCalcConfig calcConfig = scoreCalcConfigService.getOne(new LambdaUpdateWrapper<>());
        // 获取维护信息
        Map<String, List<QuaAssetMaintenance>> dbNameMap = new HashMap<>();
        List<QuaAssetMaintenance> maintenanceList = quaAssetMaintenanceService.list(new LambdaUpdateWrapper<QuaAssetMaintenance>()
                .eq(QuaAssetMaintenance::getStatus, 0)
                .eq(QuaAssetMaintenance::getElementId, request.getElementId()));
        if (CollectionUtils.isNotEmpty(maintenanceList)) {
            dbNameMap = maintenanceList.stream().collect(Collectors.groupingBy(QuaAssetMaintenance::getDbName));
        }
        PageHelper.offsetPage(request.getOffset(), request.getPageSize());
        Page<ElementNodeDbPage> nodeDbPage = null;
        if (ElementTypeEnum.CH.getCode().equals(request.getType())) {
            nodeDbPage = resultDbMapper.getChDbPage(request);
            calcDbPageData(nodeDbPage, dbNameMap, calcConfig);
        } else if (ElementTypeEnum.MYSQL.getCode().equals(request.getType())) {
            nodeDbPage = resultDbMapper.getMysqlDbPage(request);
            calcDbPageData(nodeDbPage, dbNameMap, calcConfig);
        } else if (ElementTypeEnum.HIVE.getCode().equals(request.getType())) {
            nodeDbPage = resultDbMapper.getHiveDbPage(request);
            calcDbPageData(nodeDbPage, dbNameMap, calcConfig);
        }
        return nodeDbPage;
    }

    @Override
    public Page<ElementNodeTablePage> getNodeTablePage(NodeInfoRequestDto request) {
        // 获取维护信息
        Map<String, BigDecimal> tableNameMap = new HashMap<>();
        List<QuaAssetMaintenance> maintenanceList = quaAssetMaintenanceService.list(new LambdaUpdateWrapper<QuaAssetMaintenance>()
                .eq(QuaAssetMaintenance::getStatus, 0)
                .eq(QuaAssetMaintenance::getElementId, request.getElementId())
                .eq(StringUtils.isNotBlank(request.getDbName()), QuaAssetMaintenance::getDbName, request.getDbName()));
        if (CollectionUtils.isNotEmpty(maintenanceList)) {
            tableNameMap = maintenanceList.parallelStream().collect(Collectors.toMap(QuaAssetMaintenance::getTableName,
                    QuaAssetMaintenance::getScore));
        }
        PageHelper.offsetPage(request.getOffset(), request.getPageSize());
        Page<ElementNodeTablePage> tablePage = null;
        if (ElementTypeEnum.ES.getCode().equals(request.getType())) {
            tablePage = resultIndexMapper.getIndexNodePage(request);
            if (CollectionUtils.isNotEmpty(tablePage.getResult())) {
                for (ElementNodeTablePage node : tablePage.getResult()) {
                    // 获取元数据标签
                    List<DataMetaTag> tagList = jobService.getMetaTag(request.getElementId(), DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName(), null,
                            node.getTableName());
                    node.setTagList(tagList);
                    // 获取评分
                    node.setScore(tableNameMap.getOrDefault(node.getTableName(), new BigDecimal("0")));
                }
            }
        }
        if ("DB".equals(request.getType())) {
            tablePage = resultTableMapper.getChTablePage(request);
            if (CollectionUtils.isNotEmpty(tablePage.getResult())) {
                for (ElementNodeTablePage node : tablePage.getResult()) {
                    // 获取元数据标签
                    List<DataMetaTag> tagList = jobService.getMetaTag(request.getElementId(), DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName(),
                            request.getDbName(), node.getTableName());
                    node.setTagList(tagList);
                    // 获取评分
                    node.setScore(tableNameMap.getOrDefault(node.getTableName(), new BigDecimal("0")));
                }
            }
        } else if ("MYSQL_DB".equals(request.getType())) {
            tablePage = resultTableMapper.getMysqlTablePage(request);
            if (CollectionUtils.isNotEmpty(tablePage.getResult())) {
                for (ElementNodeTablePage node : tablePage.getResult()) {
                    // 获取元数据标签
                    List<DataMetaTag> tagList = jobService.getMetaTag(request.getElementId(), DATA_SOURCE_TYPE_ENUM.MYSQL.getName(),
                            request.getDbName(), node.getTableName());
                    node.setTagList(tagList);
                    // 获取评分
                    node.setScore(tableNameMap.getOrDefault(node.getTableName(), new BigDecimal("0")));
                }
            }
        } else if ("HIVE_DB".equals(request.getType())) {
            tablePage = resultTableMapper.getHiveTablePage(request);
            if (CollectionUtils.isNotEmpty(tablePage.getResult())) {
                for (ElementNodeTablePage node : tablePage.getResult()) {
                    // 获取元数据标签
                    List<DataMetaTag> tagList = jobService.getMetaTag(request.getElementId(), DATA_SOURCE_TYPE_ENUM.HIVE.getName(),
                            request.getDbName(), node.getTableName());
                    node.setTagList(tagList);
                    // 获取评分
                    node.setScore(tableNameMap.getOrDefault(node.getTableName(), new BigDecimal("0")));
                }
            }
        }
        return tablePage;
    }

    private static void calcDbPageData(Page<ElementNodeDbPage> nodeDbPage, Map<String, List<QuaAssetMaintenance>> dbNameMap,
                                       DataScoreCalcConfig calcConfig) {
        for (ElementNodeDbPage dbPage : nodeDbPage.getResult()) {
            List<QuaAssetMaintenance> assetMaintenances = dbNameMap.get(dbPage.getDbName());
            if (CollectionUtils.isNotEmpty(assetMaintenances)) {
                long maintenanceTableCount =
                        assetMaintenances.stream().filter(x -> NumberUtil.compare(x.getScore().doubleValue(), 0) > 0).count();
                long lowMaintenanceTableCount = assetMaintenances.stream().filter(x -> NumberUtil.compare(x.getScore().doubleValue(),
                        calcConfig.getLowMaintenanceAssetScore()) < 0).count();
                // 计算总和
                BigDecimal totalScore = assetMaintenances.stream().map(QuaAssetMaintenance::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal avgScore = NumberUtil.div(totalScore, assetMaintenances.size(), 2); // 库平均维护分
                dbPage.setMaintenanceTableCount(maintenanceTableCount);
                dbPage.setLowMaintenanceTableCount(lowMaintenanceTableCount);
                dbPage.setAvgScore(avgScore);
            } else {
                dbPage.setMaintenanceTableCount(0);
                dbPage.setLowMaintenanceTableCount(0);
                dbPage.setAvgScore(new BigDecimal("0"));
            }
        }
    }

    /**
     * 计算element的db和table数量，平均分
     *
     * @param elementId
     * @param dbNode
     * @param nodeOverview
     */
    private void calcElementNodeOverview(Long elementId, List<NodeDbDto> dbNode, ElementNodeOverview nodeOverview) {
        if (CollectionUtils.isNotEmpty(dbNode)) {
            nodeOverview.setDbCount(dbNode.size());
            nodeOverview.setTableCount(dbNode.stream().mapToLong(NodeDbDto::getCnt).sum());
            // 获取维护信息
            List<QuaAssetMaintenance> maintenanceList = quaAssetMaintenanceService.list(new LambdaUpdateWrapper<QuaAssetMaintenance>()
                    .eq(QuaAssetMaintenance::getStatus, 0)
                    .eq(QuaAssetMaintenance::getElementId, elementId));
            if (CollectionUtils.isNotEmpty(maintenanceList)) {
                BigDecimal totalScore = maintenanceList.stream().map(QuaAssetMaintenance::getScore)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);  // 计算总和
                BigDecimal avgScore = NumberUtil.div(totalScore, dbNode.size(), 2); // 平均维护分
                nodeOverview.setAvgScore(avgScore);
                // maintenanceList按照库分组, 计算每个库的维护分
                Map<String, List<QuaAssetMaintenance>> dbMap =
                        maintenanceList.stream().collect(Collectors.groupingBy(QuaAssetMaintenance::getDbName));
                int lowMaintenanceDbCount = 0;
                for (List<QuaAssetMaintenance> dbList : dbMap.values()) {
                    // 将库的分数求和，筛选出低于平均维护分的库的数量
                    // 计算单库维护分总和
                    BigDecimal dbTotalScore = dbList.stream().map(QuaAssetMaintenance::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (dbTotalScore.compareTo(avgScore) < 0) { // 小于平均维护分则为低维护库
                        lowMaintenanceDbCount++;
                    }
                }
                nodeOverview.setLowMaintenanceDbCount(lowMaintenanceDbCount);

                long noMaintenanceTableCount = maintenanceList.stream().filter(x -> NumberUtil.compare(x.getScore().doubleValue(), 0) == 0).count();
                nodeOverview.setNoMaintenanceTableCount(noMaintenanceTableCount);
            }
        }
    }

    /**
     * 计算db的表数量，维护分
     *
     * @param elementId
     * @param dbName
     * @param tableNode
     * @param dbOverview
     */
    private void calcElementDbOverview(Long elementId, String dbName, List<NodeTableDto> tableNode, ElementDbOverview dbOverview,
                                       DataScoreCalcConfig calcConfig) {
        if (CollectionUtils.isNotEmpty(tableNode)) {
            dbOverview.setTableCount(tableNode.size());
            dbOverview.setFieldCount(tableNode.stream().mapToLong(NodeTableDto::getCnt).sum());
            // 获取维护信息
            List<QuaAssetMaintenance> maintenanceList = quaAssetMaintenanceService.list(new LambdaUpdateWrapper<QuaAssetMaintenance>()
                    .eq(QuaAssetMaintenance::getStatus, 0)
                    .eq(QuaAssetMaintenance::getElementId, elementId)
                    .eq(QuaAssetMaintenance::getDbName, dbName));
            if (CollectionUtils.isNotEmpty(maintenanceList)) {
                BigDecimal totalScore = maintenanceList.stream().map(QuaAssetMaintenance::getScore)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);  // 计算总和
                BigDecimal avgScore = NumberUtil.div(totalScore, tableNode.size(), 2); // 平均维护分
                dbOverview.setAvgScore(avgScore);

                long lowMaintenanceTableCount = maintenanceList.stream().filter(x -> NumberUtil.compare(x.getScore().doubleValue(),
                        calcConfig.getLowMaintenanceAssetScore()) < 0).count();
                dbOverview.setLowMaintenanceTableCount(lowMaintenanceTableCount);

                // 未维护的表数量
                long noMaintenanceTableCount =
                        maintenanceList.stream().filter(x -> NumberUtil.compare(x.getScore().doubleValue(), 0) == 0).count();
                dbOverview.setNoMaintenanceTableCount(noMaintenanceTableCount);
            }
        }
    }
}
