package com.idss.datalake.datagovern.metadata.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ApiModel(description = "基础分页请求类")
public class BasePageRequest extends BaseRequest{
    /**
     * 页码
     */
    @ApiModelProperty("页码,下标从1开始")
    private int pageNum = 1;

    /**
     * 单页数据数量
     */
    @ApiModelProperty("单页数据数量,默认20")
    private int pageSize = 20;

    @ApiModelProperty(hidden = true)
    private int offset = 0;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getOffset() {
        return (this.pageNum - 1) * this.pageSize;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public BasePageRequest() {
    }

    public BasePageRequest(int px, int ps) {
        this.pageNum = px;
        this.pageSize = ps;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("BasePageRequest{");
        sb.append("super=").append(super.toString());
        sb.append("pageNum=").append(pageNum);
        sb.append(", pageSize=").append(pageSize);
        sb.append('}');
        return sb.toString();
    }
}
