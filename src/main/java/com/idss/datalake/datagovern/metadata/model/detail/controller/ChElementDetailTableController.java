package com.idss.datalake.datagovern.metadata.model.detail.controller;


import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.UpdateDetailChTableRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.service.ChElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.operate.util.OperateLogUtil;
import com.idss.datalake.datagovern.metadata.model.operate.enums.OperateEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * CH清单Table表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@RestController
@RequestMapping("/detail/ch/table")
public class ChElementDetailTableController {
    @Autowired
    private ChElementDetailTableService detailTableService;
    @Autowired
    private QuaWabElementService elementService;

    /**
     * 更新清单表
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/update")
    public BaseResponse<String> updateDetail(@RequestBody @Valid UpdateDetailChTableRequestDto requestDto) {
        OperateEnum operateEnum = null;
        String logConcatDesc = null;
        ChElementDetailTable table = detailTableService.getById(requestDto.getId());
        QuaWabElement element = elementService.getById(table.getElementId());
        try {
            operateEnum = OperateEnum.DETAIL_CH_UPDATE;
            logConcatDesc = element.getElementName() + "," + element.getChIp()+":"+element.getChPort();
            OperateLogUtil.addLog(operateEnum, logConcatDesc, "成功");

            return detailTableService.updateDetail(requestDto);
        } catch (Exception e) {
            OperateLogUtil.addLog(operateEnum, logConcatDesc, "失败");
            throw new RuntimeException(e);
        }

    }
}
