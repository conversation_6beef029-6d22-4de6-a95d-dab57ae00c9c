package com.idss.datalake.datagovern.metadata.model.detail.controller;

import com.idss.datalake.common.util.ExcelUtil;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.service.ImportDataService;
import com.idss.datalake.datagovern.metadata.model.detail.utils.ImportUtil;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.operate.util.OperateLogUtil;
import com.idss.datalake.datagovern.metadata.model.operate.enums.OperateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: xiexiaofei
 * @Date: 28/6/2021 09:54
 * @Description:
 */
@RestController
@RequestMapping("/import")
@Slf4j
public class ImportDataController {
    @Autowired
    private ImportDataService importDataService;

    /**
     * 下载模板
     *
     * @param type
     * @param response
     * @param request
     */
    @GetMapping("/downloadTemp/{type}")
    public void downloadTemp(@PathVariable("type") String type, HttpServletResponse response, HttpServletRequest request) {
        if (StringUtils.isNotEmpty(type)) {
            try {
                if (ElementTypeEnum.ES.getCode().equals(type)) {
                    //下载ES模板
                    String[] header = ImportUtil.IMPORT_ES_HEADER;
                    String[] downRows = {"7"};
                    String[] redRows = {"0", "1"};
                    List<String[]> downData = new ArrayList<>();
                    String[] sen = {"是", "否"};
                    downData.add(sen);
                    ExcelUtil.getExcelTemplate("template.xls", header, downData, downRows, redRows, request, response, type);
                } else if (ElementTypeEnum.CH.getCode().equals(type)) {
                    //下载CH模板
                    String[] header = ImportUtil.IMPORT_CH_HEADER;
                    String[] redRows = {"0", "1", "2"};
                    String[] downRows = {"8"};
                    List<String[]> downData = new ArrayList<>();
                    String[] sen = {"是", "否"};
                    downData.add(sen);
                    ExcelUtil.getExcelTemplate("template.xls", header, downData, downRows, redRows, request, response, type);
                }
            } catch (Exception e) {
                log.error("下载模板失败");
            }
        }
    }

    /**
     * 导入
     */
    @PostMapping("/data")
    public BaseResponse<String> importData(@RequestBody Map<String, Object> params) {
        OperateEnum operateEnum = null;
        String logConcatDesc = "";
        try {
            String filePath = (String) params.get("filePath");
            String type = (String) params.get("type");
            if ("ES".equals(type)) {
                operateEnum = OperateEnum.DETAIL_ES_IMPORT;
            } else if ("CH".equals(type)) {
                operateEnum = OperateEnum.DETAIL_CH_IMPORT;
            }
            OperateLogUtil.addLog(operateEnum, logConcatDesc, "成功");
            importDataService.importData(filePath, type);
            return BaseResponse.success();
        } catch (Exception e) {
            log.error("导入失败: ", e);
            OperateLogUtil.addLog(operateEnum, logConcatDesc, "失败");
            throw new RuntimeException(e);
        }
    }
}
