package com.idss.datalake.datagovern.metadata.model.detail.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * hive清单DB表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_web_hive_element_detail_db")
public class QuaWebHiveElementDetailDb implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long elementId;

    /**
     * 库名
     */
    private String dbName;

    private LocalDateTime createTime;

    private String createUser;

    private LocalDateTime updateTime;

    private String updateUser;

    /**
     * 最早快照版本号，新增记录时写入
     */
    private String firstSnapshootVersion;

    /**
     * 最新快照版本号，更新记录时写入
     */
    private String lastSnapshootVersion;

    private Long tenantId;


}
