package com.idss.datalake.datagovern.metadata.model.element.pojo.dto;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @Author: x<PERSON>xiaof<PERSON>
 * @Date: 18/6/2021 17:14
 * @Description:
 */
@Data
@ApiModel("元数据分页查询请求体")
public class ElementPageRequestDto extends BasePageRequest {
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("连接状态：1：连通，2：不连通")
    private Integer connectState;
    @ApiModelProperty("执行状态：1：已开始，2已暂停")
    private Integer jobState;
    @NotEmpty(message = "元数据类型不能为空，ES 或 CH")
    private String elementType;
}
