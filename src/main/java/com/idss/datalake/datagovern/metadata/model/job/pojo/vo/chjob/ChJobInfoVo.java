package com.idss.datalake.datagovern.metadata.model.job.pojo.vo.chjob;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: xiexiaofei
 * @Date: 23/6/2021 22:04
 * @Description: CH任务详情信息
 */
@Data
@ApiModel(value = "CH任务详情信息")
public class ChJobInfoVo {
    @ApiModelProperty(value = "元数据ID")
    private Long elementId;

    @ApiModelProperty(value = "元数据名称")
    private String elementName;

    @ApiModelProperty(value = "版本")
    private String snapshootVersion;

    @ApiModelProperty(value = "CH IP")
    private String chIp;

    @ApiModelProperty(value = "CH port")
    private Integer chPort;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "连通状态 1：连通，2：不连通")
    private Integer isConnect;
    private String isConnectDesc;

    @ApiModelProperty(value = "总库数")
    private Integer dbCount;

    @ApiModelProperty(value = "总表数")
    private Integer tableCount;

    @ApiModelProperty(value = "总字段数")
    private Integer columnCount;

    @ApiModelProperty(value = "抽取时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "抽取状态 1执行中，2已完成，有差异，保存扫描结果，3已完成，无差异，不保存扫描结果 4执行异常")
    private Integer status;
    private String statusDesc;
}
