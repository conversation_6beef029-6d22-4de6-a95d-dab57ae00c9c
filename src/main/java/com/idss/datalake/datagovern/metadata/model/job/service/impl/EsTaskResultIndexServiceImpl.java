package com.idss.datalake.datagovern.metadata.model.job.service.impl;

import com.idss.datalake.datagovern.metadata.model.job.entity.EsTaskResultIndex;
import com.idss.datalake.datagovern.metadata.model.job.mapper.EsTaskResultIndexMapper;
import com.idss.datalake.datagovern.metadata.model.job.service.EsTaskResultIndexService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ES扫描index记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Service
public class EsTaskResultIndexServiceImpl extends ServiceImpl<EsTaskResultIndexMapper, EsTaskResultIndex> implements EsTaskResultIndexService {

    @Override
    public String maxSnapshootVersionByElementId(Long elementId) {
        return this.baseMapper.maxSnapshootVersionByElementId(elementId);
    }
}
