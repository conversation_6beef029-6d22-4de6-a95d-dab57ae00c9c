package com.idss.datalake.datagovern.metadata.model.detail.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * CH清单Table表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_web_ch_element_detail_table")
@ApiModel(value = "ChElementDetailTable对象", description = "CH清单Table表")
public class ChElementDetailTable extends Model<ChElementDetailTable> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long elementId;

    @ApiModelProperty(value = "库名")
    private String dbName;

    @ApiModelProperty(value = "数据表名称")
    private String tableName;

    @ApiModelProperty(value = "表中文名")
    private String tableNameCn;

    @ApiModelProperty(value = "表业务描述")
    private String tableDscribe;

    @ApiModelProperty(value = "表业务负责人")
    private String tableOwner;

    /**
     * 关键词，["a","b"]
     */
    private String keyWords;

    @ApiModelProperty(value = "是否为敏感表，默认不是")
    private Integer isSensitive;

    private LocalDateTime createTime;

    private String createUser;

    private LocalDateTime updateTime;

    private String updateUser;

    @ApiModelProperty(value = "是否可用，默认为1，可用")
    private Integer isAvailable;

    @ApiModelProperty(value = "最早快照版本号，新增记录时写入")
    private String firstSnapshootVersion;

    @ApiModelProperty(value = "最新快照版本号，更新记录时写入")
    private String lastSnapshootVersion;

    private Long tenantId;

    private String extAttrs;

    /**
     * DDL最后变更时间
     */
    private String ddlLastChangeTime;
    /**
     * 数据最后变更时间
     */
    private String dataLastChangeTime;
    /**
     * 最后查看时间
     */
    private String lastViewedTime;

    /**
     * 元数据来源，1-自建，2-数据建模
     */
    private Integer metaSource;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
