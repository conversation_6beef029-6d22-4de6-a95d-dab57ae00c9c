package com.idss.datalake.datagovern.metadata.model.element.utils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: xiexiaofei
 * @Date: 17/6/2021 11:06
 * @Description: 测试连通性返回体
 */
@ApiModel("测试连通性返回体")
@Data
public class TestConnectResponseVo {
    @ApiModelProperty("状态：0失败，1成功")
    private Integer status;
    @ApiModelProperty("描述")
    private String desc;
}
