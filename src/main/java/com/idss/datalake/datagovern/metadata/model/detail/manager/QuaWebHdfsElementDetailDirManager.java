/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadata.model.detail.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHdfsElementDetailDir;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHdfsElementDetailFile;
import com.idss.datalake.datagovern.metadata.model.detail.mapper.QuaWebHdfsElementDetailDirMapper;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.QuaWebHdfsElementDetailDirDTO;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.HdfsPageVo;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHdfsElementDetailDirService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHdfsElementDetailFileService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>hdfs清单目录表 manager处理类</p>
 * @since 2025-01-06
 */
@Component
public class QuaWebHdfsElementDetailDirManager {
    private static final Logger logger = LoggerFactory.getLogger(QuaWebHdfsElementDetailDirManager.class);

    @Autowired
    private IQuaWebHdfsElementDetailDirService hdfsElementDetailDirService;
    @Autowired
    private IQuaWebHdfsElementDetailFileService hdfsElementDetailFileService;
    @Autowired
    private QuaWebHdfsElementDetailDirMapper hdfsElementDetailDirMapper;


    /**
     * 分页
     *
     * @param requestDTO
     * @return
     * @throws Exception
     */
    public Map<String, Object> page(RequestDTO requestDTO) throws Exception {
        Map<String, Object> result = new HashMap<>();
        QuaWebHdfsElementDetailDirDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), QuaWebHdfsElementDetailDirDTO.class);
        Integer pageNum = requestDTO.getGlobal().getPageNum();
        Integer pageSize = requestDTO.getGlobal().getPageSize();

        dto.setPageSize(pageSize);
        dto.setOffset((pageNum - 1) * pageSize);
        long total = hdfsElementDetailDirMapper.queryPageCount(dto);
        List<HdfsPageVo> pageVos = hdfsElementDetailDirMapper.queryPage(dto);

        result.put("pageNum", pageNum);
        result.put("pageSize", pageSize);
        result.put("total", total);
        result.put("data", pageVos);
        return result;
    }

    public void create(QuaWebHdfsElementDetailDirDTO dto) {
        if (StringUtils.isAnyBlank(dto.getParentDirPath(), dto.getDirName())) {
            throw new ParamInvalidException("入参异常");
        }

        int count = hdfsElementDetailDirService.count(new LambdaQueryWrapper<QuaWebHdfsElementDetailDir>()
                .eq(QuaWebHdfsElementDetailDir::getElementId, dto.getElementId())
                .eq(QuaWebHdfsElementDetailDir::getParentDirPath, dto.getParentDirPath())
                .eq(QuaWebHdfsElementDetailDir::getDirName, dto.getDirName()));
        if (count > 0) {
            throw new ParamInvalidException("目录已存在");
        }

        QuaWebHdfsElementDetailDir dir = new QuaWebHdfsElementDetailDir();
        ReflectionUtil.copyLomBokProperties(dto, dir);
        dir.setDirPath(dto.getParentDirPath() + File.separator + dto.getDirName());
        dir.setDirAuthority("drwxwxrwx");
        dir.setTenantId(UserUtil.getLongCurrentTenantId());
        dir.setCreateTime(LocalDateTime.now());
        dir.setUpdateTime(LocalDateTime.now());
        dir.setCreateUser(UserUtil.getCurrentUsername());
        dir.setUpdateUser(UserUtil.getCurrentUsername());
        hdfsElementDetailDirService.save(dir);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        List<Long> dirIds = new ArrayList<>();
        List<Long> fileIds = new ArrayList<>();
        List<QuaWebHdfsElementDetailDir> dirs = hdfsElementDetailDirService.list(new QueryWrapper<QuaWebHdfsElementDetailDir>().in("id", ids));
        for (QuaWebHdfsElementDetailDir dir : dirs) {
            dirIds.add(dir.getId());
            childIds(dir.getElementId(), dir.getDirPath(), dirIds, fileIds);
        }

        hdfsElementDetailDirService.remove(new QueryWrapper<QuaWebHdfsElementDetailDir>().in("id", dirIds));
        if (CollectionUtils.isNotEmpty(fileIds)) {
            hdfsElementDetailFileService.remove(new QueryWrapper<QuaWebHdfsElementDetailFile>().in("id", fileIds));
        }
    }

    /**
     * 查询出子目录id和子文件id
     *
     * @param elementId
     * @param dirPath
     * @param dirIds
     * @param fileIds
     */
    private void childIds(Long elementId, String dirPath, List<Long> dirIds, List<Long> fileIds) {
        QuaWebHdfsElementDetailDirDTO dto = genDirDTO(elementId, dirPath);
        List<HdfsPageVo> voList = hdfsElementDetailDirMapper.queryPage(dto);
        if (CollectionUtils.isNotEmpty(voList)) {
            for (HdfsPageVo childVo : voList) {
                if ("dir".equals(childVo.getFileType())) {
                    dirIds.add(childVo.getId());
                    childIds(childVo.getElementId(), childVo.getDirPath(), dirIds, fileIds);
                } else if ("file".equals(childVo.getFileType())) {
                    fileIds.add(childVo.getId());
                }
            }
        }
    }

    private QuaWebHdfsElementDetailDirDTO genDirDTO(Long elementId, String dirPath) {
        QuaWebHdfsElementDetailDirDTO dto = new QuaWebHdfsElementDetailDirDTO();
        dto.setOffset(0);
        dto.setPageSize(1000);
        dto.setElementId(elementId);
        dto.setDirPath(dirPath);
        return dto;
    }

    public void edit(QuaWebHdfsElementDetailDirDTO dto) {
        if (ObjectUtils.isEmpty(dto.getId())) {
            throw new ParamInvalidException("入参异常");
        }

        QuaWebHdfsElementDetailDir dbOne = hdfsElementDetailDirService.getById(dto.getId());
        ReflectionUtil.copyLomBokProperties(dto, dbOne);
        dbOne.setDirPath(dto.getParentDirPath() + File.separator + dto.getDirName());
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        hdfsElementDetailDirService.updateById(dbOne);
    }

    public QuaWebHdfsElementDetailDir detail(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        return hdfsElementDetailDirService.getById(id);
    }
}