package com.idss.datalake.datagovern.metadata.model.job.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Date;

/**
 * @Author: xiexiaofei
 * @Date: 26/6/2021 11:49
 * @Description: CH 导出
 */
@Data
public class EsExportDto {
    @ApiModelProperty(value = "ES集群地址")
    private String esIpPort;
    @ApiModelProperty(value = "ES用户名")
    private String esUserName;
    @ApiModelProperty(value = "ES密码")
    private String esUserPassword;
    @ApiModelProperty(value = "ES 选择的模板ID")
    private Long esKbsTemplateId;
    @ApiModelProperty(value = "ES 选择的模板名称")
    private Long esKbsTemplateName;

    @ApiModelProperty(value = "快照-创建时间")
    private Date snapshootCreateTime;
    @ApiModelProperty(value = "快照-创建用户")
    private String snapshootCreateUser;
    @ApiModelProperty(value = "快照-快照版本")
    private String snapshootVersion;

    @ApiModelProperty(value = "维表-索引-实体ID")
    private Long detailIndexId;
    @ApiModelProperty(value = "维表-索引-中文名")
    private String detailIndexNameCn;
    @ApiModelProperty(value = "维表-索引-业务描述")
    private String detailIndexDscribe;
    @ApiModelProperty(value = "维表-索引-责任人")
    private String detailIndexOwner;
    @ApiModelProperty(value = "维表-索引-是否涉敏")
    private Integer detailIndexIsSensitive;
    @ApiModelProperty(value = "维表-索引-创建时间")
    private Date detailIndexCreateTime;
    @ApiModelProperty(value = "维表-索引-创建用户")
    private String detailIndexCreateUser;
    @ApiModelProperty(value = "维表-索引-更新时间")
    private Date detailIndexUpdateTime;
    @ApiModelProperty(value = "维表-索引-更新用户")
    private String detailIndexUpdateUser;
    @ApiModelProperty(value = "维表-索引-是否可用")
    private Integer detailIndexIsAvailable;
    @ApiModelProperty(value = "维表-索引-最早快照版本号")
    private String detailIndexFirstSnapshootVersion;
    @ApiModelProperty(value = "维表-索引-最新快照版本号")
    private String detailIndexLastSnapshootVersion;
    @ApiModelProperty(value = "维表-索引-节点IP")
    private String detailIndexHostAddress;
    @ApiModelProperty(value = "维表-索引-端口")
    private Integer detailIndexPort;
    @ApiModelProperty(value = "维表-索引-名称")
    private String detailIndexIndexName;
    @ApiModelProperty(value = "维表-索引-类型名称")
    private String detailIndexTypeName;

    @ApiModelProperty(value = "维表-字段-实体ID")
    private Long  detailFieldId;
    @ApiModelProperty(value = "维表-字段-名称")
    private String detailFieldFieldName;
    @ApiModelProperty(value = "维表-字段-中文名")
    private String detailFieldFieldNameCn;
    @ApiModelProperty(value = "维表-字段-是否敏感")
    private Integer detailFieldIsSensitive;
    @ApiModelProperty(value = "维表-字段-创建时间")
    private Date detailFieldCreateTime;
    @ApiModelProperty(value = "维表-字段-创建用户")
    private String detailFieldCreateUser;
    @ApiModelProperty(value = "维表-字段-更新时间")
    private Date detailFieldUpdateTime;
    @ApiModelProperty(value = "维表-字段-更新用户")
    private String detailFieldUpdateUser;
    @ApiModelProperty(value = "维表-字段-是否可用")
    private Integer detailFieldIsAvailable;
    @ApiModelProperty(value = "维表-字段-最早快照版本号")
    private String detailFieldFirstSnapshootVersion;
    @ApiModelProperty(value = "维表-字段-最新快照版本号")
    private String detailFieldLastSnapshootVersion;

    @ApiModelProperty(value = "快照-索引-快照ID")
    private Long snapshootIndexId;
    @ApiModelProperty(value = "快照-索引-集群名")
    private String snapshootIndexClusterName;
    @ApiModelProperty(value = "快照-索引-集群UUID")
    private String snapshootIndexClusterUuid;
    @ApiModelProperty(value = "快照-索引-索引状态")
    private String snapshootIndexIndexState;
    @ApiModelProperty(value = "快照-索引-索引配置")
    private String snapshootIndexIndexSettings;
    @ApiModelProperty(value = "快照-索引-索引别名")
    private String snapshootIndexAliases;
    @ApiModelProperty(value = "快照-索引-主分片更新版本")
    private String snapshootIndexPrimaryTerms;
    @ApiModelProperty(value = "快照-索引-分片分配标识")
    private String snapshootIndexInSyncAllocations;


    @ApiModelProperty(value = "快照-字段-快照ID")
    private Long snapshootFieldId;
    @ApiModelProperty(value = "快照-字段-数据类型")
    private String snapshootFieldFieldDataType;
    @ApiModelProperty(value = "快照-字段-analyzer")
    private String snapshootFieldAnalyzer;
    @ApiModelProperty(value = "快照-字段-boost")
    private String snapshootFieldBoost;
    @ApiModelProperty(value = "快照-字段-coerce")
    private String snapshootFieldCoerce;
    @ApiModelProperty(value = "快照-字段-copy_to")
    private String snapshootFieldCopyTo;
    @ApiModelProperty(value = "快照-字段-doc_values")
    private String snapshootFieldDocValues;
    @ApiModelProperty(value = "快照-字段-dynamic")
    private String snapshootFieldDynamic;
    @ApiModelProperty(value = "快照-字段-eager_global_ordinals")
    private String snapshootFieldEagerGlobalOrdinals;
    @ApiModelProperty(value = "快照-字段-enabled")
    private String snapshootFieldEnabled;
    @ApiModelProperty(value = "快照-字段-fielddata")
    private String snapshootFieldFielddata;
    @ApiModelProperty(value = "快照-字段-fields")
    private String snapshootFieldFields;
    @ApiModelProperty(value = "快照-字段-format")
    private String snapshootFieldFormat;
    @ApiModelProperty(value = "快照-字段-ignore_above")
    private String snapshootFieldIgnoreAbove;
    @ApiModelProperty(value = "快照-字段-ignore_malformed")
    private String snapshootFieldIgnoreMalformed;
    @ApiModelProperty(value = "快照-字段-index_options")
    private String snapshootFieldIndexOptions;
    @ApiModelProperty(value = "快照-字段-index_phrases")
    private String snapshootFieldIndexPhrases;
    @ApiModelProperty(value = "快照-字段-index_prefixes")
    private String snapshootFieldIndexPrefixes;
    @ApiModelProperty(value = "快照-字段-index")
    private String snapshootFieldIndex;
    @ApiModelProperty(value = "快照-字段-meta")
    private String snapshootFieldMeta;
    @ApiModelProperty(value = "快照-字段-normalizer")
    private String snapshootFieldNormalizer;
    @ApiModelProperty(value = "快照-字段-norms")
    private String snapshootFieldNorms;
    @ApiModelProperty(value = "快照-字段-null_value")
    private String snapshootFieldNullValue;
    @ApiModelProperty(value = "快照-字段-position_increment_gap")
    private String snapshootFieldPositionIncrementGap;
    @ApiModelProperty(value = "快照-字段-properties")
    private String snapshootFieldProperties;
    @ApiModelProperty(value = "快照-字段-search_analyzer")
    private String snapshootFieldSearchAnalyzer;
    @ApiModelProperty(value = "快照-字段-similarity")
    private String snapshootFieldSimilarity;
    @ApiModelProperty(value = "快照-字段-store")
    private String snapshootFieldStore;
    @ApiModelProperty(value = "快照-字段-term_vector")
    private String snapshootFieldTermVector;
    @ApiModelProperty(value = "快照-字段-字段长度")
    private Integer snapshootFieldFieldLength;
    @ApiModelProperty(value = "快照-字段-字段精度")
    private Integer snapshootFieldFieldPrecision;

    /*public static void main(String[] args) {
        try {
            Class<?> clazz = Class.forName("com.idss.datalake.datagovern.metadata.model.job.pojo.dto.EsExportDto");
            Method[] methods = clazz.getDeclaredMethods();
            for (Method method : methods) {
                System.out.println(method.getName());
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
    }*/

}
