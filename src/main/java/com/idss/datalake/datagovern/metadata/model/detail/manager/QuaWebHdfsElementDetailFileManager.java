/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadata.model.detail.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHdfsElementDetailFile;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.QuaWebHdfsElementDetailFileDTO;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHdfsElementDetailFileService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description <p>hdfs清单文件表 manager处理类</p>
 * @since 2025-01-06
 */
@Component
public class QuaWebHdfsElementDetailFileManager {
    private static final Logger logger = LoggerFactory.getLogger(QuaWebHdfsElementDetailFileManager.class);

    @Autowired
    private IQuaWebHdfsElementDetailFileService hdfsElementDetailFileService;

    public void create(QuaWebHdfsElementDetailFileDTO dto) {
        if (StringUtils.isAnyBlank(dto.getDirPath(), dto.getFileName())) {
            throw new ParamInvalidException("入参异常");
        }

        int count = hdfsElementDetailFileService.count(new LambdaQueryWrapper<QuaWebHdfsElementDetailFile>()
                .eq(QuaWebHdfsElementDetailFile::getElementId, dto.getElementId())
                .eq(QuaWebHdfsElementDetailFile::getDirPath, dto.getDirPath())
                .eq(QuaWebHdfsElementDetailFile::getFileName, dto.getFileName()));
        if (count > 0) {
            throw new ParamInvalidException("数据已存在");
        }

        QuaWebHdfsElementDetailFile file = new QuaWebHdfsElementDetailFile();
        ReflectionUtil.copyLomBokProperties(dto, file);
        file.setTenantId(UserUtil.getLongCurrentTenantId());
        file.setCreateTime(LocalDateTime.now());
        file.setUpdateTime(LocalDateTime.now());
        file.setCreateUser(UserUtil.getCurrentUsername());
        file.setUpdateUser(UserUtil.getCurrentUsername());
        hdfsElementDetailFileService.save(file);
    }

    public void delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        hdfsElementDetailFileService.remove(new QueryWrapper<QuaWebHdfsElementDetailFile>().in("id", ids));
    }

    public void edit(QuaWebHdfsElementDetailFileDTO dto) {
        if (ObjectUtils.isEmpty(dto.getId())) {
            throw new ParamInvalidException("入参异常");
        }

        QuaWebHdfsElementDetailFile dbOne = hdfsElementDetailFileService.getById(dto.getId());
        ReflectionUtil.copyLomBokProperties(dto, dbOne);
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        hdfsElementDetailFileService.updateById(dbOne);
    }

    public QuaWebHdfsElementDetailFile detail(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        return hdfsElementDetailFileService.getById(id);
    }

}