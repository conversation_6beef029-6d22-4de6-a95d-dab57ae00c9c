package com.idss.datalake.datagovern.metadata.model.detail.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailField;
import com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailIndex;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHiveElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHiveElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.mapper.ChElementDetailColumnMapper;
import com.idss.datalake.datagovern.metadata.model.detail.mapper.EsElementDetailFieldMapper;
import com.idss.datalake.datagovern.metadata.model.detail.mapper.QuaWebHiveElementDetailColumnMapper;
import com.idss.datalake.datagovern.metadata.model.detail.mapper.QuaWebMysqlElementDetailColumnMapper;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.ElementColumnUpdateRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.ElementDetailPageRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementDetailVo;
import com.idss.datalake.datagovern.metadata.model.detail.service.ChElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.detail.service.ChElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.detail.service.EsElementDetailFieldService;
import com.idss.datalake.datagovern.metadata.model.detail.service.EsElementDetailIndexService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHiveElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHiveElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebMysqlElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebMysqlElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datamart.enums.ElementTypeEnum;
import com.idss.radar.util.UmsUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * CH清单Column表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@RestController
@RequestMapping("/element/detail")
public class ElementDetailController {
    @Autowired
    private ChElementDetailColumnService chElementDetailColumnService;
    @Autowired
    private ChElementDetailTableService chElementDetailTableService;
    @Autowired
    private EsElementDetailIndexService esElementDetailIndexService;
    @Autowired
    private EsElementDetailFieldService esElementDetailFieldService;
    @Autowired
    private IQuaWebHiveElementDetailTableService hiveElementDetailTableService;
    @Autowired
    private IQuaWebHiveElementDetailColumnService hiveElementDetailColumnService;
    @Autowired
    private IQuaWebMysqlElementDetailTableService mysqlElementDetailTableService;
    @Autowired
    private IQuaWebMysqlElementDetailColumnService mysqlElementDetailColumnService;

    @Resource
    private ChElementDetailColumnMapper chElementDetailColumnMapper;
    @Resource
    private EsElementDetailFieldMapper esElementDetailFieldMapper;
    @Resource
    private QuaWebHiveElementDetailColumnMapper hiveElementDetailColumnMapper;
    @Resource
    private QuaWebMysqlElementDetailColumnMapper mysqlElementDetailColumnMapper;


    @Autowired
    private QuaWabElementService elementService;

    /**
     * 表详情
     * @param param
     * @return
     */
    @PostMapping("/tableDetail")
    public BaseResponse<JSONObject> tableDetail(@RequestBody JSONObject param){
        JSONObject result = new JSONObject();
        Long elementId = param.getLong("elementId");
        String assetPath = param.getString("assetPath");
        String assetType = param.getString("assetType");
        if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(assetType)) {
            String[] split = assetPath.split(",");
            ChElementDetailTable one = chElementDetailTableService.getOne(new QueryWrapper<ChElementDetailTable>().eq("element_id", elementId).eq("db_name", split[0]).eq("table_name", split[1]));
            result.put("tableNameCn",one.getTableNameCn());
            result.put("tableDscribe",one.getTableDscribe());
            result.put("tableOwner",one.getTableOwner());
            result.put("keyWords",one.getKeyWords());
        }else if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(assetType)) {
            String[] split = assetPath.split(",");
            QuaWebMysqlElementDetailTable one = mysqlElementDetailTableService.getOne(new QueryWrapper<QuaWebMysqlElementDetailTable>().eq("element_id", elementId).eq("db_name", split[0]).eq("table_name", split[1]));
            result.put("tableNameCn",one.getTableNameCn());
            result.put("tableDscribe",one.getTableDscribe());
            result.put("tableOwner",one.getTableOwner());
            result.put("keyWords",one.getKeyWords());
        }else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(assetType)) {
            String[] split = assetPath.split(",");
            QuaWebHiveElementDetailTable one = hiveElementDetailTableService.getOne(new QueryWrapper<QuaWebHiveElementDetailTable>().eq("element_id", elementId).eq("db_name", split[0]).eq("table_name", split[1]));
            result.put("tableNameCn",one.getTableNameCn());
            result.put("tableDscribe",one.getTableDscribe());
            result.put("tableOwner",one.getTableOwner());
            result.put("keyWords",one.getKeyWords());
        }else if (ElementTypeEnum.ELASTICSEARCH_INDEX.getCode().equals(assetType)) {
            String[] split = assetPath.split(",");
            EsElementDetailIndex one = esElementDetailIndexService.getOne(new QueryWrapper<EsElementDetailIndex>().eq("element_id", elementId).eq("index_name", split[0]));
            result.put("tableNameCn",one.getIndexNameCn());
            result.put("tableDscribe",one.getIndexDscribe());
            result.put("tableOwner",one.getIndexOwner());
            result.put("keyWords",one.getKeyWords());
        }
        return BaseResponse.success(result);
    }

    /**
     * 更新表
     *
     * @param param
     * @return
     */
    @PostMapping("/updateTable")
    public BaseResponse<String> updateTableDetail(@RequestBody JSONObject param) {
        Long elementId = param.getLong("elementId");
        String assetPath = param.getString("assetPath");
        String assetType = param.getString("assetType");
        String tableNameCn = param.containsKey("tableNameCn") ? param.getString("tableNameCn") : "";
        String tableDscribe = param.containsKey("tableDscribe") ? param.getString("tableDscribe") : "";
        String tableOwner = param.containsKey("tableOwner") ? param.getString("tableOwner") : "";
        JSONArray keyWords = param.containsKey("keyWords") ? param.getJSONArray("keyWords") : new JSONArray();

        if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(assetType)) {
            String[] split = assetPath.split(",");
            ChElementDetailTable one = chElementDetailTableService.getOne(new QueryWrapper<ChElementDetailTable>().eq("element_id", elementId).eq("db_name", split[0]).eq("table_name", split[1]));
            one.setTableNameCn(tableNameCn);
            one.setTableDscribe(tableDscribe);
            one.setTableOwner(tableOwner);
            one.setKeyWords(keyWords.toJSONString());
            chElementDetailTableService.updateById(one);
        } else if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(assetType)) {
            String[] split = assetPath.split(",");
            QuaWebMysqlElementDetailTable one = mysqlElementDetailTableService.getOne(new QueryWrapper<QuaWebMysqlElementDetailTable>().eq("element_id", elementId).eq("db_name", split[0]).eq("table_name", split[1]));
            one.setTableNameCn(tableNameCn);
            one.setTableDscribe(tableDscribe);
            one.setTableOwner(tableOwner);
            one.setKeyWords(keyWords.toJSONString());
            mysqlElementDetailTableService.updateById(one);
        } else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(assetType)) {
            String[] split = assetPath.split(",");
            QuaWebHiveElementDetailTable one = hiveElementDetailTableService.getOne(new QueryWrapper<QuaWebHiveElementDetailTable>().eq("element_id", elementId).eq("db_name", split[0]).eq("table_name", split[1]));
            one.setTableNameCn(tableNameCn);
            one.setTableDscribe(tableDscribe);
            one.setTableOwner(tableOwner);
            one.setKeyWords(keyWords.toJSONString());
            hiveElementDetailTableService.updateById(one);
        } else if (ElementTypeEnum.ELASTICSEARCH_INDEX.getCode().equals(assetType)) {
            String[] split = assetPath.split(",");
            EsElementDetailIndex one = esElementDetailIndexService.getOne(new QueryWrapper<EsElementDetailIndex>().eq("element_id", elementId).eq("index_name", split[0]));
            one.setIndexNameCn(tableNameCn);
            one.setIndexDscribe(tableDscribe);
            one.setIndexOwner(tableOwner);
            one.setKeyWords(keyWords.toJSONString());
            esElementDetailIndexService.updateById(one);
        }
        return BaseResponse.success();
    }

    /**
     * 查询表下的字段
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/queryColumnPage")
    public BasePageResponse<List<ElementDetailVo>> queryDetailColumnPage(@RequestBody @Valid ElementDetailPageRequestDto requestDto) {

        List<ElementDetailVo> list = new ArrayList<>();
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<ElementDetailVo> page = new Page<>();
        if (requestDto.getTenantId() == null) {
            requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        }
        if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(requestDto.getAssetType())) {
            String[] split = requestDto.getAssetPath().split(",");
            requestDto.setDbName(split[0]);
            requestDto.setTableName(split[1]);
            page = chElementDetailColumnMapper.queryDetailColumnPage(requestDto);
        } else if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(requestDto.getAssetType())) {
            String[] split = requestDto.getAssetPath().split(",");
            requestDto.setDbName(split[0]);
            requestDto.setTableName(split[1]);
            page = mysqlElementDetailColumnMapper.queryDetailColumnPage(requestDto);
        } else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(requestDto.getAssetType())) {
            String[] split = requestDto.getAssetPath().split(",");
            requestDto.setDbName(split[0]);
            requestDto.setTableName(split[1]);
            page = hiveElementDetailColumnMapper.queryDetailColumnPage(requestDto);
        } else if (ElementTypeEnum.ELASTICSEARCH_INDEX.getCode().equals(requestDto.getAssetType())) {
            String[] split = requestDto.getAssetPath().split(",");
            requestDto.setIndexName(split[0]);
            page = esElementDetailFieldMapper.queryDetailColumnPage(requestDto);
        }
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
    }


    /**
     * 批量更新字段
     * @param requestDto
     * @return
     */
    @PostMapping("/updateColumn")
    public BaseResponse<String> updateColumn(@RequestBody ElementColumnUpdateRequestDto requestDto) {
        if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(requestDto.getAssetType())) {
            for (ElementDetailVo elementDetailVo : requestDto.getDataList()) {
                ChElementDetailColumn byId = chElementDetailColumnService.getById(elementDetailVo.getId());
                byId.setColumnNameCn(elementDetailVo.getColumnNameCn());
                byId.setIsRequired(elementDetailVo.getIsRequired());
                chElementDetailColumnService.updateById(byId);
            }
        }else if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(requestDto.getAssetType())) {
            for (ElementDetailVo elementDetailVo : requestDto.getDataList()) {
                QuaWebMysqlElementDetailColumn byId = mysqlElementDetailColumnService.getById(elementDetailVo.getId());
                byId.setColumnNameCn(elementDetailVo.getColumnNameCn());
                byId.setIsRequired(elementDetailVo.getIsRequired());
                mysqlElementDetailColumnService.updateById(byId);
            }
        } else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(requestDto.getAssetType())) {
            for (ElementDetailVo elementDetailVo : requestDto.getDataList()) {
                QuaWebHiveElementDetailColumn byId = hiveElementDetailColumnService.getById(elementDetailVo.getId());
                byId.setColumnNameCn(elementDetailVo.getColumnNameCn());
                byId.setIsRequired(elementDetailVo.getIsRequired());
                hiveElementDetailColumnService.updateById(byId);
            }
        }else if (ElementTypeEnum.ELASTICSEARCH_INDEX.getCode().equals(requestDto.getAssetType())) {
            for (ElementDetailVo elementDetailVo : requestDto.getDataList()) {
                EsElementDetailField byId = esElementDetailFieldService.getById(elementDetailVo.getId());
                byId.setFieldNameCn(elementDetailVo.getColumnNameCn());
                byId.setIsRequired(elementDetailVo.getIsRequired());
                esElementDetailFieldService.updateById(byId);
            }
        }
        return BaseResponse.success();
    }
}
