package com.idss.datalake.datagovern.metadata.model.detail.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * CH清单DB表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_web_ch_element_detail_db")
@ApiModel(value = "ChElementDetailDb对象", description = "CH清单DB表")
public class ChElementDetailDb extends Model<ChElementDetailDb> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long elementId;

    @ApiModelProperty(value = "库名")
    private String dbName;

    private LocalDateTime createTime;

    private String createUser;

    private LocalDateTime updateTime;

    private String updateUser;

    @ApiModelProperty(value = "最早快照版本号，新增记录时写入")
    private String firstSnapshootVersion;

    @ApiModelProperty(value = "最新快照版本号，更新记录时写入")
    private String lastSnapshootVersion;

    private Long tenantId;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
