package com.idss.datalake.datagovern.metadata.model.element.enums;

/**
 * @Author: x<PERSON>xiaofei
 * @Date: 18/6/2021 15:16
 * @Description: Job执行状态
 */
public enum JobStateEnum {
    STARTED(1, "已开始"),
    STOP(2, "已暂停");
    private Integer state;
    private String desc;

    JobStateEnum(Integer state, String desc) {
        this.state = state;
        this.desc = desc;
    }

    public Integer getState() {
        return state;
    }

    public String getDesc() {
        return desc;
    }
}
