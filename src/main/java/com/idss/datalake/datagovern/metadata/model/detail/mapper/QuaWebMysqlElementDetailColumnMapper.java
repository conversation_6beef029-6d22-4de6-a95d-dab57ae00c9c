package com.idss.datalake.datagovern.metadata.model.detail.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailColumn;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.ElementDetailPageRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementDetailVo;

/**
 * <p>
 * mysql清单Column表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
public interface QuaWebMysqlElementDetailColumnMapper extends BaseMapper<QuaWebMysqlElementDetailColumn> {
    Page<ElementDetailVo> queryDetailColumnPage(ElementDetailPageRequestDto requestDto);
}
