package com.idss.datalake.datagovern.metadata.model.job.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaJob;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaTask;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.*;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO.ChColumnImportDTO;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO.EsColumnImportDTO;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO.HiveColumnImportDTO;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO.ImportChColumnDTO;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO.ImportEsColumnDTO;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO.ImportHiveColumnDTO;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO.ImportMysqlColumnDTO;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO.ImportPanweiColumnDTO;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO.MysqlColumnImportDTO;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO.PanweiColumnImportDTO;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.ColumnInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.chjob.ChColumnInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.chjob.ChCountNodeDto;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.chjob.ChJobInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.chjob.ChTableInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob.EsCountIndexVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob.EsFieldInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob.EsIndexInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob.EsJobInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob.EsTaskVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.hivejob.HiveColumnInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.hivejob.HiveTableInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.kafkajob.KafkaTopicInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.mysqljob.MysqlColumnInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.mysqljob.MysqlTableInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.panweijob.PanweiTableInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.panweijob.PanweiColumnInfoVo;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTag;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 扫描Job记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
public interface QuaJobService extends IService<QuaJob> {
    List<Map<String, Object>> listDesensitization();

    /**
     * 查询ES任务详情信息
     *
     * @param requestDto
     * @return
     */
    BaseResponse<EsJobInfoVo> getEsJobInfo(JobInfoRequestDto requestDto);

    /**
     * 查询ES任务详情索引统计
     *
     * @param requestDto
     * @return
     */
    BaseResponse<List<EsCountIndexVo>> countEsIndex(JobInfoRequestDto requestDto);

    /**
     * 查询ES任务详情索引信息
     *
     * @param requestDto
     * @return
     */
    BaseResponse<EsIndexInfoVo> esIndexInfo(EsJobIndexInfoRequestDto requestDto);

    /**
     * 查询Kafka任务详情索引信息
     *
     * @param requestDto
     * @return
     */
    BaseResponse<KafkaTopicInfoVo> kafkaTopicInfo(KafkaJobTopicInfoRequestDto requestDto);

    /**
     * 分页查询ES任务详情字段信息
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<EsFieldInfoVo>> queryEsDetailFieldPage(EsJobFieldRequestDto requestDto);

    /**
     * 版本信息分页
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<QuaTask>> queryVersionPage(VersionPageRequestDto requestDto);

    /**
     * 执行详情分页
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<EsTaskVo>> queryEsTaskPage(TaskPageRequestDto requestDto);

    /**
     * 暂停/恢复JOB
     *
     * @param jobId
     * @param state 1已开始，2已暂停
     * @return
     */
    BaseResponse<String> pauseOrResume(Long jobId, Integer state);

    /**
     * 删除
     *
     * @param jobId
     * @return
     */
    BaseResponse<String> delete(Long jobId);

    /**
     * 查询CH任务详情信息
     *
     * @param requestDto
     * @return
     */
    BaseResponse<ChJobInfoVo> getChJobInfo(JobInfoRequestDto requestDto);

    /**
     * 查询CH任务详情库统计
     *
     * @param requestDto
     * @return
     */
    BaseResponse<List<ChCountNodeDto>> countChDb(JobInfoRequestDto requestDto);

    /**
     * 查询CH任务详情表信息
     *
     * @param requestDto
     * @return
     */
    BaseResponse<ChTableInfoVo> chTableInfo(ChJobTableInfoRequestDto requestDto);

    /**
     * 分页查询CH任务详情字段信息
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<ChColumnInfoVo>> queryChDetailColumnPage(ChJobColumnRequestDto requestDto);

    /**
     * 更新CH脱敏规则
     *
     * @param requestDto {"id":1,"desensitizationId":1,"type":"es,ch,mysql,hive"}
     * @return
     */
    BaseResponse<String> updateChDesensitization(Map<String, Object> requestDto);

    /**
     * 查询MYSQL任务详情表信息
     *
     * @param requestDto
     * @return
     */
    BaseResponse<MysqlTableInfoVo> mysqlTableInfo(ChJobTableInfoRequestDto requestDto);

    /**
     * 查询panwei任务详情表信息
     *
     * @param requestDto
     * @return
     */
    BaseResponse<PanweiTableInfoVo> panweiTableInfo(ChJobTableInfoRequestDto requestDto);

    /**
     * 分页查询磐维任务详情字段信息
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<PanweiColumnInfoVo>> queryPanweiDetailColumnPage(ChJobColumnRequestDto requestDto);

    /**
     * 分页查询Mysql任务详情字段信息
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<MysqlColumnInfoVo>> queryMysqlDetailColumnPage(ChJobColumnRequestDto requestDto);

    /**
     * 查询HIVE任务详情表信息
     *
     * @param requestDto
     * @return
     */
    BaseResponse<HiveTableInfoVo> hiveTableInfo(ChJobTableInfoRequestDto requestDto);

    /**
     * 分页查询Hive任务详情字段信息
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<HiveColumnInfoVo>> queryHiveDetailColumnPage(ChJobColumnRequestDto requestDto);

    /**
     * 导入Mysql字段
     *
     * @param requestDto
     * @return
     */
    BaseResponse importMysqlColumn(MysqlColumnImportDTO requestDto) throws Exception;

    /**
     * 批量导入Mysql字段
     *
     * @param requestDto
     * @return
     * @throws IOException
     * @throws ParamInvalidException
     */
    BaseResponse importMysqlColumnBatch(ImportMysqlColumnDTO requestDto) throws Exception;

    void exportMysqlColumns(List<ImportMysqlColumnDTO> dtos, HttpServletResponse response) throws Exception;

    void exportHiveColumns(List<ImportHiveColumnDTO> dtos, HttpServletResponse response) throws Exception;

    void exportChColumns(List<ImportChColumnDTO> dtos, HttpServletResponse response) throws Exception;

    void exportEsColumns(List<ImportEsColumnDTO> dtos, HttpServletResponse response) throws Exception;

    void exportPanweiColumns(List<ImportPanweiColumnDTO> dtos, HttpServletResponse response) throws Exception;

    /**
     * 导入Hive字段
     *
     * @param requestDto
     * @return
     * @throws IOException
     * @throws ParamInvalidException
     */
    BaseResponse importHiveColumn(HiveColumnImportDTO requestDto) throws Exception;

    /**
     * 导入Hive字段
     *
     * @param requestDto
     * @return
     * @throws IOException
     * @throws ParamInvalidException
     */
    BaseResponse importHiveColumnBatch(ImportHiveColumnDTO requestDto) throws Exception;

    /**
     * 导入Ch字段
     *
     * @param requestDto
     * @return
     * @throws IOException
     * @throws ParamInvalidException
     */
    BaseResponse importChColumn(ChColumnImportDTO requestDto) throws Exception;

    /**
     * 导入Panwei字段
     *
     * @param requestDto
     * @return
     * @throws Exception
     */
    BaseResponse importPanweiColumn(PanweiColumnImportDTO requestDto) throws Exception;

    /**
     * 批量导入Panwei字段
     *
     * @param requestDto
     * @return
     * @throws Exception
     */
    BaseResponse importPanweiColumns(PanweiColumnImportDTO requestDto) throws Exception;

    /**
     * 导入Ch字段
     *
     * @param requestDto
     * @return
     * @throws IOException
     * @throws ParamInvalidException
     */
    BaseResponse importChColumnBatch(ImportChColumnDTO requestDto) throws Exception;

    /**
     * 导入Es字段
     *
     * @param requestDto
     * @return
     * @throws IOException
     * @throws ParamInvalidException
     */
    BaseResponse importEsColumn(EsColumnImportDTO requestDto) throws Exception;

    /**
     * 导入Es字段
     *
     * @param requestDto
     * @return
     * @throws IOException
     * @throws ParamInvalidException
     */
    BaseResponse importEsColumnBatch(ImportEsColumnDTO requestDto) throws Exception;

    /**
     * 下载模板
     *
     * @param response
     * @throws IOException
     */
    void downloadTemplateFile(HttpServletResponse response, String type) throws IOException;

    /**
     * 获取元数据标签
     *
     * @param elementId
     * @param datasourceType
     * @param dbName
     * @param tableName
     * @return
     */
    List<DataMetaTag> getMetaTag(Long elementId, String datasourceType, String dbName, String tableName);

    /**
     * 查询表字段信息
     *
     * @param elementId
     * @param datasourceType
     * @param databaseName
     * @param tableName
     * @return
     */
    List<ColumnInfoVo> queryTableFieldList(Long elementId, String datasourceType, String databaseName, String tableName);
}
