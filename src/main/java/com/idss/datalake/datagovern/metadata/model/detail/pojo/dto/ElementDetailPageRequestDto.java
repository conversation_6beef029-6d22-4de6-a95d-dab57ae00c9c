package com.idss.datalake.datagovern.metadata.model.detail.pojo.dto;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @Author: xiexiaofei
 * @Date: 23/6/2021 14:04
 * @Description: 任务详情查看JOB信息
 */
@Data
public class ElementDetailPageRequestDto extends BasePageRequest {
    /**
     * 资源ID
     */
    private Long elementId;

    /**
     * 资源路径
     */
    private String assetPath;

    private String dbName;

    private String tableName;

    private String indexName;

    /**
     * 资源类型
     */
    private String assetType;

    /**
     * 字段名称
     */
    private String columnName;
}
