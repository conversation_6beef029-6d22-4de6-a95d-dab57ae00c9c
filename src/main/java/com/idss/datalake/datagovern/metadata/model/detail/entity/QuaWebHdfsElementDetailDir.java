/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadata.model.detail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.HdfsPageVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description <p>hdfs清单目录表</p>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_web_hdfs_element_detail_dir")
public class QuaWebHdfsElementDetailDir implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("element_id")
    private Long elementId;

    /**
     * 上级目录地址
     */
    @TableField("parent_dir_path")
    private String parentDirPath;

    /**
     * 目录名称
     */
    @TableField("dir_name")
    private String dirName;

    /**
     * 目录中文名称
     */
    @TableField("dir_name_cn")
    private String dirNameCn;

    /**
     * 目录业务描述
     */
    @TableField("dir_describe")
    private String dirDescribe;

    /**
     * 目录业务负责人
     */
    @TableField("dir_owner")
    private String dirOwner;

    /**
     * 群组
     */
    @TableField("dir_group")
    private String dirGroup;

    /**
     * 权限
     */
    @TableField("dir_authority")
    private String dirAuthority;

    /**
     * 存储大小，单位字节
     */
    @TableField("storage_size")
    private Long storageSize;

    /**
     * 关键词，["a","b"]
     */
    @TableField("key_words")
    private String keyWords;

    /**
     * 是否为敏感，0非，1是，默认不是
     */
    @TableField("is_sensitive")
    private Integer isSensitive;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField("create_user")
    private String createUser;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField("update_user")
    private String updateUser;

    /**
     * 是否可用，0不可用，默认为1，可用
     */
    @TableField("is_available")
    private Integer isAvailable;

    /**
     * 自定义属性
     */
    @TableField("ext_attrs")
    private String extAttrs;

    /**
     * 最早快照版本号，新增记录时写入
     */
    @TableField("first_snapshoot_version")
    private String firstSnapshootVersion;

    /**
     * 最新快照版本号，更新记录时写入
     */
    @TableField("last_snapshoot_version")
    private String lastSnapshootVersion;

    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 最后查看时间
     */
    @TableField("last_viewed_time")
    private String lastViewedTime;

    /**
     * 元数据来源，1-自建，2-数据建模
     */
    @TableField("meta_source")
    private Integer metaSource;

    private String dirPath;
}
