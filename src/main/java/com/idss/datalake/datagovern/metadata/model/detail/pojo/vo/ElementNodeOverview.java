package com.idss.datalake.datagovern.metadata.model.detail.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采集节点概览
 *
 * <AUTHOR>
 * @date 2024/11/14
 * @see
 */
@Data
public class ElementNodeOverview {
    /**
     * 库个数
     */
    private long dbCount;

    /**
     * 表个数
     */
    private long tableCount;
    /**
     * 未维护表个数
     */
    private long noMaintenanceTableCount;

    /**
     * 平均维护分
     */
    private BigDecimal avgScore = new BigDecimal("0");

    /**
     * 低维护库个数
     */
    private long lowMaintenanceDbCount;

}
