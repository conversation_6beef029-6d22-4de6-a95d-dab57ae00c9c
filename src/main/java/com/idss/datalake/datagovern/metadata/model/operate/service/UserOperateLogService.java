package com.idss.datalake.datagovern.metadata.model.operate.service;

import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.model.operate.entity.UserOperateLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.metadata.model.operate.pojo.OptLogPageReqDto;
import com.idss.datalake.datagovern.metadata.model.operate.pojo.OptLogReqDto;

import java.util.List;

/**
 * <p>
 * 用户操作记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
public interface UserOperateLogService extends IService<UserOperateLog> {
    BasePageResponse<List<UserOperateLog>> queryPage(OptLogPageReqDto reqDto);
    List<UserOperateLog> queryAll(OptLogReqDto reqDto);
}
