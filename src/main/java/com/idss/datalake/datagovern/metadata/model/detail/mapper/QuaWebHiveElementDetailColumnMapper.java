package com.idss.datalake.datagovern.metadata.model.detail.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHiveElementDetailColumn;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.ElementDetailPageRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementDetailVo;

/**
 * <p>
 * hive清单Column表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
public interface QuaWebHiveElementDetailColumnMapper extends BaseMapper<QuaWebHiveElementDetailColumn> {
    Page<ElementDetailVo> queryDetailColumnPage(ElementDetailPageRequestDto requestDto);
}
