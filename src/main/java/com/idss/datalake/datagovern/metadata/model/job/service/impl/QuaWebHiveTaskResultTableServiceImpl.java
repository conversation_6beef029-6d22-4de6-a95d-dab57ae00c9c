package com.idss.datalake.datagovern.metadata.model.job.service.impl;

import com.idss.datalake.datagovern.metadata.model.job.entity.QuaWebHiveTaskResultTable;
import com.idss.datalake.datagovern.metadata.model.job.mapper.QuaWebHiveTaskResultTableMapper;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebHiveTaskResultTableService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * hive扫描Table记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Service
public class QuaWebHiveTaskResultTableServiceImpl extends ServiceImpl<QuaWebHiveTaskResultTableMapper, QuaWebHiveTaskResultTable> implements IQuaWebHiveTaskResultTableService {

}
