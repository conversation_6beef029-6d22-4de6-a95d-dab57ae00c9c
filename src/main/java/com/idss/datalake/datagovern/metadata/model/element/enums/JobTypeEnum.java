package com.idss.datalake.datagovern.metadata.model.element.enums;

/**
 * @Author: x<PERSON>xiaofei
 * @Date: 18/6/2021 13:37
 * @Description: JOB类型
 */
public enum JobTypeEnum {
    CYCLE(1,"周期"),
    EXECUTE_IMMEDIATELY(2,"立即执行"),
    SPECIFIED_TIME(3,"指定时间")
    ;
    private int type;
    private String desc;

    JobTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByJobType(int jobType) {
        for (JobTypeEnum typeEnum : JobTypeEnum.values()) {
            if (jobType == typeEnum.getType()) {
                return typeEnum.getDesc();
            }
        }
        return "";
    }
}
