package com.idss.datalake.datagovern.metadata.model.job.pojo.vo.chjob;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.idss.datalake.datagovern.datametarelation.entity.DataMetaRelation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: xiexiaofei
 * @Date: 23/6/2021 16:00
 * @Description:
 */
@Data
@ApiModel("CH字段信息")
public class ChColumnInfoVo {
    private Long id;

    private Long elementId;

    @ApiModelProperty(value = "字段名称")
    private String columnName;

    @ApiModelProperty(value = "字段中文名，默认为空字符串")
    private String columnNameCn;

    @ApiModelProperty(value = "是否为敏感表，默认不是")
    private Integer isSensitive;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private String updateUser;

    @ApiModelProperty(value = "是否可用，默认为1，可用")
    private Integer isAvailable;

    @ApiModelProperty(value = "最早快照版本号，新增记录时写入")
    private String firstSnapshootVersion;

    @ApiModelProperty(value = "最新快照版本号，更新记录时写入")
    private String lastSnapshootVersion;

    @ApiModelProperty("快照ID")
    private Long snapshootId;
    @ApiModelProperty("快照创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime snapshootCreateTime;
    @ApiModelProperty("快照创建用户")
    private String snapshootCreateUser;
    @ApiModelProperty("快照版本")
    private String snapshootVersion;

    @ApiModelProperty(value = "字段类型")
    private String type;

    @ApiModelProperty(value = "字段序号，取值从1开始")
    private Long position;

    @ApiModelProperty(value = "字段默认值类型，取值：DEFAULT、ALIAS")
    private String defaultKind;

    @ApiModelProperty(value = "字段默认值表达式，如：now()、CAST(1, 'Int32')")
    private String defaultExpression;

    @ApiModelProperty(value = "数据压缩后字节数")
    private Long dataCompressedBytes;

    @ApiModelProperty(value = "数据未压缩字节数")
    private Long dataUncompressedBytes;

    @ApiModelProperty(value = "标记的大小")
    private Long marksBytes;

    @ApiModelProperty(value = "字段描述")
    private String comment;

    @ApiModelProperty(value = "是否属于分区字段")
    private Integer isInPartitionKey;

    @ApiModelProperty(value = "是否属于排序字段")
    private Integer isInSortingKey;

    @ApiModelProperty(value = "是否属于主键字段")
    private Integer isInPrimaryKey;

    @ApiModelProperty(value = "是否属于抽样字段")
    private Integer isInSamplingKey;

    @ApiModelProperty(value = "压缩编码器名称")
    private String compressionCodec;

    @ApiModelProperty(value = "可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串")
    private Integer isNullable;

    @ApiModelProperty(value = "字段长度（bit），根据字段类型不同而不同，目前仅支持数值型字段")
    private Integer columnLength;

    @ApiModelProperty(value = "字段精度（bit），仅针对浮点数值型字段")
    private Integer columnPrecision;

    /**
     * 业务类型
     */
    private Long businessType;

    private String businessName;

    private Long senLevelId;
    private String senLevelName;
    private Long senTypeId;
    private String senTypeName;

    /**
     * 脱敏规则 ID
     */
    private Integer desensitizationId;

    /**
     * 是否必填，0-否，1-是
     */
    private Integer isRequired;
    /**
     * 是否加密，0-否，1-是
     */
    private Integer isEncrypted;

    @ApiModelProperty(value = "库名称")
    private String dbName;
    @ApiModelProperty(value = "表名称")
    private String tableName;
    /**
     * 字段关联关系
     */
    private DataMetaRelation dataMetaRelation;

    /**
     * 中文描述
     */
    private String cnDesc;
    /**
     * 枚举值
     */
    private String enumValue;
    /**
     * 映射字段
     */
    private String mappingFields;

    /**
     * 排序字段
     */
    private Integer sort;
}
