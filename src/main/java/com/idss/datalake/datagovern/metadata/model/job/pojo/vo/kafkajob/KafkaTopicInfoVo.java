package com.idss.datalake.datagovern.metadata.model.job.pojo.vo.kafkajob;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.idss.datalake.datagovern.config.entity.QuaWebTableBaseInfo;
import com.idss.datalake.datagovern.config.entity.QuaWebTableModel;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.TableExtRequestDto;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTag;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: xiexiaofei
 * @Date: 23/6/2021 13:46
 * @Description:
 */
@Data
@ApiModel("Es索引信息")
public class KafkaTopicInfoVo {
    private Long id;
    /**
     * 元素ID
     */
    private Long elementId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * topic名称
     */
    private String topicName;

    /**
     * 分区数
     */
    private Integer partitions;

    /**
     * 副本数
     */
    private Integer replicationFactor;

    /**
     * 首次快照版本
     */
    private String firstSnapshootVersion;

    /**
     * 最新快照版本
     */
    private String lastSnapshootVersion;

    /**
     * 创建人
     */
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private String topicNameCn;
    private String topicDescribe;
    private String topicOwner;
    private Integer isSensitive;
    /**
     * 元数据来源，1-自建，2-数据建模
     */
    private Integer metaSource;


    private Long snapshootId;
    /**
     * 元数据标签列表
     */
    private List<DataMetaTag> tagList;
    /**
     * 关键词，["a","b"]
     */
    private String keyWords;
}
