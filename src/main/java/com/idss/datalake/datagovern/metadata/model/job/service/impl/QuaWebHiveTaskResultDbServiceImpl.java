package com.idss.datalake.datagovern.metadata.model.job.service.impl;

import com.idss.datalake.datagovern.metadata.model.job.entity.QuaWebHiveTaskResultDb;
import com.idss.datalake.datagovern.metadata.model.job.mapper.QuaWebHiveTaskResultDbMapper;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebHiveTaskResultDbService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * hive扫描DB记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Service
public class QuaWebHiveTaskResultDbServiceImpl extends ServiceImpl<QuaWebHiveTaskResultDbMapper, QuaWebHiveTaskResultDb> implements IQuaWebHiveTaskResultDbService {

}
