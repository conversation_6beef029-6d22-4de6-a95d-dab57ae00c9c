package com.idss.datalake.datagovern.metadata.model.detail.service;

import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailColumn;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.UpdateDetailChColumnBatchRequestDto;

/**
 * <p>
 * CH清单Column表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
public interface ChElementDetailColumnService extends IService<ChElementDetailColumn> {
    BaseResponse<String> updateDetail(UpdateDetailChColumnBatchRequestDto requestDto);
}
