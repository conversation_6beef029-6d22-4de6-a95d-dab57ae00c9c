package com.idss.datalake.datagovern.metadata.base;

import io.swagger.annotations.ApiModel;

@ApiModel(description = "接口状态码")
public enum ApiReturnCode {
    //
    OK("success", "操作成功"),
    VALIDATE_FAILED("error", "参数校验失败"),
    ERROR("error", "操作失败，请稍后重试"),
    BAD_SERVICE("error", "服务调用异常"),
    NO_DATA("success", "数据不存在"),
    UN_LOGIN("error", "账号密码错误"),
    UNAUTHORIZED("error", "你已被登出,请重新登录!!!"),
    FAILED("error", "操作失败,请稍后重试"),
    FORBIDDEN("error", "没有相关权限,请联系管理员");

    private String status;
    private String message;

    ApiReturnCode(String status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
