package com.idss.datalake.datagovern.metadata.model.detail.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHdfsElementDetailDir;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.QuaWebHdfsElementDetailDirDTO;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.HdfsPageVo;

import java.util.List;

/**
 * <p>
 * hdfs清单目录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
public interface QuaWebHdfsElementDetailDirMapper extends BaseMapper<QuaWebHdfsElementDetailDir> {

    /**
     * 分页查询子目录和文件
     *
     * @param dto
     * @return
     */
    List<HdfsPageVo> queryPage(QuaWebHdfsElementDetailDirDTO dto);

    long queryPageCount(QuaWebHdfsElementDetailDirDTO dto);

}
