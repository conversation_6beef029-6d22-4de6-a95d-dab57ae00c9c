package com.idss.datalake.datagovern.metadata.model.detail.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.config.entity.QuaWebTableModel;
import com.idss.datalake.datagovern.config.service.IQuaWebTableModelService;
import com.idss.datalake.datagovern.dictionary.dto.DataDictionaryItemDTO;
import com.idss.datalake.datagovern.dictionary.enums.ItemTypeEnum;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryItemService;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.mapper.ChElementDetailTableMapper;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.UpdateDetailChTableRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.service.ChElementDetailTableService;
import com.idss.datalake.datagovern.metadatatag.dto.DataMetaTagMasterDTO;
import com.idss.datalake.datagovern.metadatatag.manager.DataMetaTagMasterManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <p>
 * CH清单Table表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Service
@Slf4j
public class ChElementDetailTableServiceImpl extends ServiceImpl<ChElementDetailTableMapper, ChElementDetailTable> implements ChElementDetailTableService {

    @Autowired
    private IDataDictionaryItemService dataDictionaryItemService;
    @Autowired
    private DataMetaTagMasterManager dataMetaTagMasterManager;
    @Autowired
    private IQuaWebTableModelService tableModelService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public BaseResponse<String> updateDetail(UpdateDetailChTableRequestDto requestDto) {
        try {
            ChElementDetailTable one = getById(requestDto.getId());
            one.setTableNameCn(requestDto.getTableNameCn());
            one.setTableDscribe(requestDto.getDesc());
            one.setTableOwner(requestDto.getOwner());
            if (CollectionUtils.isNotEmpty(requestDto.getExtAttrs())) {
                one.setExtAttrs(JSON.toJSONString(requestDto.getExtAttrs()));
            } else {
                one.setExtAttrs("");
            }
            one.setIsSensitive(requestDto.getIsSensitive());
            one.setKeyWords(requestDto.getKeyWords());
            updateById(one);

            // 更新数据字典项
            DataDictionaryItemDTO tableItem = new DataDictionaryItemDTO();
            tableItem.setDatasourceType(DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName());
            tableItem.setItemType(ItemTypeEnum.table.name());
            tableItem.setElementId(one.getElementId());
            tableItem.setDatabaseName(one.getDbName());
            tableItem.setTableName(one.getTableName());
            tableItem.setItemName(one.getTableName());
            tableItem.setItemId(requestDto.getId());
            tableItem.setColumnNameCn(requestDto.getTableNameCn());
            tableItem.setIsSensitive(requestDto.getIsSensitive());
            tableItem.setItemDesc(requestDto.getDesc());
            tableItem.setOwner(requestDto.getOwner());
            if (requestDto.getTableModel() != null) {
                tableItem.setBusinessSectorId(requestDto.getTableModel().getBusinessSectorId());
                tableItem.setDataDomainId(requestDto.getTableModel().getDataDomainId());
                tableItem.setDwLevelId(requestDto.getTableModel().getDwLevelId());
                tableItem.setBusinessProcessId(requestDto.getTableModel().getBusinessProcessId());
            }
            tableItem.setAssetType("clickhouse_table");
            tableItem.setAssetTypeCode("TABLE");
            tableItem.setAssetPath(one.getDbName() + "," + one.getTableName());
            tableItem.setAssetDataType("表");
            dataDictionaryItemService.updateItemFromMeta(tableItem);

            // 更新元数据标签
            DataMetaTagMasterDTO dto = new DataMetaTagMasterDTO();
            dto.setTagIds(requestDto.getTagIds());
            dto.setItemType(ItemTypeEnum.table.name());
            dto.setDatasourceType(DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName());
            dto.setDatabaseName(one.getDbName());
            dto.setTableName(one.getTableName());
            dto.setElementId(one.getElementId());
            dataMetaTagMasterManager.create(dto);

            // 更新模型信息
            if (requestDto.getTableModel() != null) {
                QuaWebTableModel quaWebTableModel = requestDto.getTableModel();
                if (quaWebTableModel.getId() != null) {
                    quaWebTableModel.setUpdateUser(UserUtil.getCurrentUsername());
                    quaWebTableModel.setUpdateTime(LocalDateTime.now());
                    tableModelService.updateById(quaWebTableModel);
                } else {
                    quaWebTableModel.setElementId(one.getElementId());
                    quaWebTableModel.setDbName(one.getDbName());
                    quaWebTableModel.setTableName(one.getTableName());
                    quaWebTableModel.setDatasourceType(DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName());
                    quaWebTableModel.setTenantId(UserUtil.getLongCurrentTenantId());
                    quaWebTableModel.setUpdateUser(UserUtil.getCurrentUsername());
                    quaWebTableModel.setUpdateTime(LocalDateTime.now());
                    quaWebTableModel.setCreateUser(UserUtil.getCurrentUsername());
                    quaWebTableModel.setCreateTime(LocalDateTime.now());
                    tableModelService.save(quaWebTableModel);
                }
            }
            return BaseResponse.success();
        } catch (Exception e) {
            log.info("更新CH清单-表-失败：{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
