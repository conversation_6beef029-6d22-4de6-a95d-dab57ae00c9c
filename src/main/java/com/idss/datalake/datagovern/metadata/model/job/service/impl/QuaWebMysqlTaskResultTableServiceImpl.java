package com.idss.datalake.datagovern.metadata.model.job.service.impl;

import com.idss.datalake.datagovern.metadata.model.job.entity.QuaWebMysqlTaskResultTable;
import com.idss.datalake.datagovern.metadata.model.job.mapper.QuaWebMysqlTaskResultTableMapper;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebMysqlTaskResultTableService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * mysql扫描Table记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Service
public class QuaWebMysqlTaskResultTableServiceImpl extends ServiceImpl<QuaWebMysqlTaskResultTableMapper, QuaWebMysqlTaskResultTable> implements IQuaWebMysqlTaskResultTableService {

}
