package com.idss.datalake.datagovern.metadata.model.job.service.impl;

import com.idss.datalake.datagovern.metadata.model.job.entity.QuaWebHiveTaskResultColumn;
import com.idss.datalake.datagovern.metadata.model.job.mapper.QuaWebHiveTaskResultColumnMapper;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebHiveTaskResultColumnService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * hive扫描Column记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Service
public class QuaWebHiveTaskResultColumnServiceImpl extends ServiceImpl<QuaWebHiveTaskResultColumnMapper, QuaWebHiveTaskResultColumn> implements IQuaWebHiveTaskResultColumnService {

    @Override
    public String maxSnapshootVersionByElementId(Long elementId) {
        return this.baseMapper.maxSnapshootVersionByElementId(elementId);
    }
}
