package com.idss.datalake.datagovern.metadata.model.element.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * <p>
 * 元数据管理
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_wab_element")
@ApiModel(value = "QuaWabElement对象", description = "元数据管理")
public class QuaWabElement extends Model<QuaWabElement> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "元数据类型 ：ES 或 CH")
    private String elementType;

    @ApiModelProperty(value = "元数据名称")
    private String elementName;

    @ApiModelProperty(value = "ES集群地址")
    private String esIpPort;

    @ApiModelProperty(value = "ES认证类型：1账密认证，2自上传文件认证，3选择文件认证")
    private Integer esAuthType;

    @ApiModelProperty(value = "ES用户名")
    private String esUserName;

    @ApiModelProperty(value = "ES密码")
    private String esUserPassword;

    @ApiModelProperty(value = "ES KBS账号")
    private String esKbsAccount;

    @ApiModelProperty(value = "ES keytab文件路径")
    private String esKeytabFilePath;

    @ApiModelProperty(value = "ES krb5文件路径")
    private String esKrb5FilePath;

    @ApiModelProperty(value = "ES jaas文件路径")
    private String esJaasFilePath;

    @ApiModelProperty(value = "ES 选择的模板ID")
    private Long esKbsTemplateId;

    @ApiModelProperty(value = "CH IP")
    private String chIp;

    @ApiModelProperty(value = "CH port")
    private Integer chPort;

    @ApiModelProperty(value = "CH 用户名")
    private String chUserName;

    @ApiModelProperty(value = "CH 密码")
    private String chUserPassword;

    @ApiModelProperty(value = "CH 是否SSL 0否，1是，默认1")
    private Integer chIsSsl;

    @ApiModelProperty(value = "是否连通，1：连通，2：不连通")
    private Integer isConnect;

    @ApiModelProperty(value = "失败连通原因")
    private String failConnectReason;

    @ApiModelProperty("执行类型")
    private String executeType;
    @ApiModelProperty("执行频率配置JSON")
    private String configJson;

    private LocalDateTime createTime;
    @ApiModelProperty("最近抽取时间")
    private LocalDateTime lateScanTime;

    private String createUser;

    private LocalDateTime updateTime;

    private String updateUser;

    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    @ApiModelProperty(value = "是否有效，0无效，1有效，默认1")
    private Integer flag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @ApiModelProperty(value = "keytab文件路径")
    private String keyTabPath;
    @ApiModelProperty(value = "krb5文件路径")
    private String krb5ConfPath;
    @ApiModelProperty(value = "jaas文件路径")
    private String jaasConfPath;

    /**
     * 是否开启kbs认证，0-不开启(默认)，1-开启
     */
    private Integer kbsEnable;
    /**
     * jdbc地址，当开启kbs认证时，会包含principal
     */
    private String jdbcUrl;

    /**
     * 统一数据源 id
     */
    private Integer tenantDatasourceId;

    @ApiModelProperty(value = "数据库名称")
    private String dbName;

    @ApiModelProperty(value = "Schema名称")
    private String schemaName;


    /**
     * Kafka broker地址
     */
    private String kafkaBrokers;

    /**
     * Kafka用户名
     */
    private String kafkaUserName;

    /**
     * Kafka密码
     */
    private String kafkaUserPassword;

    /**
     * keytab配置文件路径
     */
    private String keytabFilePath;

    /**
     * krb5配置文件路径
     */
    private String krb5FilePath;

    /**
     * principal
     */
    private String principal;

}
