package com.idss.datalake.datagovern.metadata.model.element.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ElementSelectDto {

    /**
     * 统一数据源 id
     */
    private Integer tenantDatasourceId;

    private String name;
    @ApiModelProperty(value = "CH IP")
    private String chIp;

    @ApiModelProperty(value = "CH port")
    private Integer chPort;

    @ApiModelProperty(value = "CH 用户名")
    private String chUserName;

    @ApiModelProperty(value = "CH 密码")
    private String chUserPassword;

    @ApiModelProperty(value = "CH 是否SSL 0否，1是，默认1")
    private Integer chIsSsl;

    @ApiModelProperty(value = "是否连通，1：连通，2：不连通")
    private Integer isConnect;

    @ApiModelProperty(value = "失败连通原因")
    private String failConnectReason;


    @ApiModelProperty(value = "ES集群地址")
    private String esIpPort;

    @ApiModelProperty(value = "ES认证类型：1账密认证，2自上传文件认证，3选择文件认证")
    private Integer esAuthType;

    @ApiModelProperty(value = "ES用户名")
    private String esUserName;

    @ApiModelProperty(value = "ES密码")
    private String esUserPassword;

    @ApiModelProperty(value = "ES KBS账号")
    private String esKbsAccount;

    @ApiModelProperty(value = "ES keytab文件路径")
    private String esKeytabFilePath;

    @ApiModelProperty(value = "ES krb5文件路径")
    private String esKrb5FilePath;

    @ApiModelProperty(value = "ES jaas文件路径")
    private String esJaasFilePath;

    @ApiModelProperty(value = "ES 选择的模板ID")
    private Long esKbsTemplateId;


    /**
     * 是否开启kbs认证，0-不开启(默认)，1-开启
     */
    private Integer kbsEnable;
    /**
     * jdbc地址，当开启kbs认证时，会包含principal
     */
    private String jdbcUrl;


    /**
     * Kafka broker地址
     */
    private String kafkaBrokers;

    /**
     * Kafka用户名
     */
    private String kafkaUserName;

    /**
     * Kafka密码
     */
    private String kafkaUserPassword;

    /**
     * keytab配置文件路径
     */
    private String keytabFilePath;

    /**
     * krb5配置文件路径
     */
    private String krb5FilePath;

    /**
     * principal
     */
    private String principal;


    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 数据库模式
     */
    private String schemaName;
}
