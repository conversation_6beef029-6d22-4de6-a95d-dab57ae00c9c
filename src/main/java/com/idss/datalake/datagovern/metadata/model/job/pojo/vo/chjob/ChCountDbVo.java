package com.idss.datalake.datagovern.metadata.model.job.pojo.vo.chjob;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: xiexiaofei
 * @Date: 24/6/2021 09:16
 * @Description:
 */
@Data
public class ChCountDbVo {
    @ApiModelProperty("资源ID")
    private Long elementId;
    @ApiModelProperty("索引ID")
    private Long dbId;
    @ApiModelProperty("快照版本")
    private String snapshootVersion;
    @ApiModelProperty("索引名称")
    private String dbName;
    @ApiModelProperty("统计表字段个数")
    private List<ChCountTableVo> chCountTableVoList;
}
