package com.idss.datalake.datagovern.metadata.model.element.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.common.util.CronUtil;
import com.idss.datalake.common.util.DateUtils;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.enums.ConnectStatusEnum;
import com.idss.datalake.datagovern.metadata.model.element.enums.ESAuthTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.enums.JobStateEnum;
import com.idss.datalake.datagovern.metadata.model.element.enums.JobTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.mapper.QuaWabElementMapper;
import com.idss.datalake.datagovern.metadata.model.element.pojo.dto.*;
import com.idss.datalake.datagovern.metadata.model.element.pojo.vo.ElementCountVo;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.element.utils.SendScanUtil;
import com.idss.datalake.datagovern.metadata.model.element.utils.TestConnectResponseVo;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaJob;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaJobService;
import com.idss.datalake.datagovern.metadata.model.kbs.entity.KbsFileConfig;
import com.idss.datalake.datagovern.metadata.model.kbs.service.KbsFileConfigService;
import com.idss.datalake.datagovern.metadata.model.quartz.entity.BaseTask;
import com.idss.datalake.datagovern.metadata.model.quartz.job.*;
import com.idss.datalake.datagovern.metadata.model.quartz.job.ScanPanWeiJob;
import com.idss.datalake.datagovern.metadata.model.quartz.service.QuartzJobService;
import com.idss.datalake.datasource.entity.TenantDefineDatasources;
import com.idss.datalake.datasource.service.ITenantDefineCertificatesService;
import com.idss.datalake.datasource.service.ITenantDefineCredentialsService;
import com.idss.datalake.datasource.service.ITenantDefineDatasourcesService;
import com.idss.datalake.datasource.service.ITenantDefineKerberosConfigsService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.quartz.JobKey;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 元数据管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Service
@Slf4j
public class QuaWabElementServiceImpl extends ServiceImpl<QuaWabElementMapper, QuaWabElement> implements QuaWabElementService {
    @Resource
    private QuaWabElementMapper elementMapper;
    @Autowired
    private QuaJobService jobService;
    @Autowired
    private QuartzJobService quartzJobService;
    @Autowired
    private KbsFileConfigService kbsFileConfigService;

    public static final String testConnnectES = "/scan/testConnectES";

    public static final String testConnectCH = "/scan/testConnectCH";

    public static final String testConnectMysql = "/scan/testConnectMysql";

    public static final String testConnectHive = "/scan/testConnectHive";

    public static final String testConnectPanWei = "/scan/testConnectPanWei";

    public static final String testConnectKafka = "/scan/testConnectKafka";

    @Value("${data.collect.url}")
    private String collectUrl;

    @Autowired
    private ITenantDefineCertificatesService tenantDefineCertificatesService;
    @Autowired
    private ITenantDefineCredentialsService tenantDefineCredentialsService;
    @Autowired
    private ITenantDefineKerberosConfigsService tenantDefineKerberosConfigsService;
    @Autowired
    private ITenantDefineDatasourcesService tenantDefineDatasourcesService;

    @Override
    public List<ElementSelectDto> selectList(String type) {
        List<ElementSelectDto> result = new ArrayList<>();
        if (type.equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.MYSQL.getName())) {
            List<TenantDefineDatasources> mysqlDatasourceList = tenantDefineDatasourcesService.list(
                    new QueryWrapper<TenantDefineDatasources>()
                            .eq("type", DATA_SOURCE_TYPE_ENUM.MYSQL.getName())
                            .eq("status", "connected")
                            .eq("tenant_id", UmsUtils.getUVO().getTenantId()));
            for (TenantDefineDatasources tenantDefineDatasources : mysqlDatasourceList) {
                ElementSelectDto elementSelectDto = new ElementSelectDto();
                elementSelectDto.setTenantDatasourceId(tenantDefineDatasources.getId());
                elementSelectDto.setName(tenantDefineDatasources.getName());
                elementSelectDto.setChIp(tenantDefineDatasources.getHost());
                elementSelectDto.setChPort(tenantDefineDatasources.getPort());
                elementSelectDto.setChUserName(tenantDefineDatasources.getCredentialsEnabled() ?
                        tenantDefineCredentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getUsername() : "");
                elementSelectDto.setChUserPassword(tenantDefineDatasources.getCredentialsEnabled() ?
                        tenantDefineCredentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getPassword() : "");
                elementSelectDto.setChIsSsl(tenantDefineDatasources.getCertificatesEnabled() ? 1 : 0);
                elementSelectDto.setIsConnect(1);
                elementSelectDto.setFailConnectReason("");
                result.add(elementSelectDto);
            }
        } else if (type.equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName())) {
            List<TenantDefineDatasources> clickhouseDatasourceList = tenantDefineDatasourcesService.list(
                    new QueryWrapper<TenantDefineDatasources>()
                            .eq("type", DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName())
                            .eq("status", "connected")
                            .eq("tenant_id", UmsUtils.getUVO().getTenantId()));
            for (TenantDefineDatasources tenantDefineDatasources : clickhouseDatasourceList) {
                ElementSelectDto elementSelectDto = new ElementSelectDto();
                elementSelectDto.setTenantDatasourceId(tenantDefineDatasources.getId());
                elementSelectDto.setName(tenantDefineDatasources.getName());
                elementSelectDto.setChIp(tenantDefineDatasources.getHost());
                elementSelectDto.setChPort(tenantDefineDatasources.getPort());
                elementSelectDto.setChUserName(tenantDefineDatasources.getCredentialsEnabled() ?
                        tenantDefineCredentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getUsername() : "");
                elementSelectDto.setChUserPassword(tenantDefineDatasources.getCredentialsEnabled() ?
                        tenantDefineCredentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getPassword() : "");
                elementSelectDto.setChIsSsl(tenantDefineDatasources.getCertificatesEnabled() ? 1 : 0);
                elementSelectDto.setIsConnect(1);
                elementSelectDto.setFailConnectReason("");
                result.add(elementSelectDto);
            }
        } else if (type.equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName())) {
            List<TenantDefineDatasources> esDatasourceList = tenantDefineDatasourcesService.list(
                    new QueryWrapper<TenantDefineDatasources>()
                            .eq("type", DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName())
                            .eq("status", "connected")
                            .eq("tenant_id", UmsUtils.getUVO().getTenantId()));
            for (TenantDefineDatasources tenantDefineDatasources : esDatasourceList) {
                ElementSelectDto elementSelectDto = new ElementSelectDto();
                elementSelectDto.setTenantDatasourceId(tenantDefineDatasources.getId());
                elementSelectDto.setName(tenantDefineDatasources.getName());
                elementSelectDto.setEsIpPort(tenantDefineDatasources.getHost() + ":" + tenantDefineDatasources.getPort());
                elementSelectDto.setEsAuthType(tenantDefineDatasources.getKerberosEnabled() ? 1 : 2);
                elementSelectDto.setEsUserName(tenantDefineDatasources.getCredentialsEnabled() ?
                        tenantDefineCredentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getUsername() : "");
                elementSelectDto.setEsUserPassword(tenantDefineDatasources.getCredentialsEnabled() ?
                        tenantDefineCredentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getPassword() : "");
                elementSelectDto.setEsKbsAccount(tenantDefineDatasources.getKerberosEnabled() ?
                        tenantDefineKerberosConfigsService.getById(tenantDefineDatasources.getKerberosConfigId()).getPrincipal() : "");
                elementSelectDto.setEsKeytabFilePath(tenantDefineDatasources.getKerberosEnabled() ?
                        tenantDefineKerberosConfigsService.getById(tenantDefineDatasources.getKerberosConfigId()).getKeytabPath() : "");
                elementSelectDto.setEsKrb5FilePath(tenantDefineDatasources.getKerberosEnabled() ?
                        tenantDefineKerberosConfigsService.getById(tenantDefineDatasources.getKerberosConfigId()).getKrb5Path() : "");
                elementSelectDto.setEsJaasFilePath("");
                elementSelectDto.setEsKbsTemplateId(null);
                elementSelectDto.setIsConnect(1);
                elementSelectDto.setFailConnectReason("");
                result.add(elementSelectDto);
            }
        } else if (type.equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.HIVE.getName())) {
            List<TenantDefineDatasources> hiveDatasourceList = tenantDefineDatasourcesService.list(
                    new QueryWrapper<TenantDefineDatasources>()
                            .eq("type", DATA_SOURCE_TYPE_ENUM.HIVE.getName())
                            .eq("status", "connected")
                            .eq("tenant_id", UmsUtils.getUVO().getTenantId()));
            for (TenantDefineDatasources tenantDefineDatasources : hiveDatasourceList) {
                ElementSelectDto elementSelectDto = new ElementSelectDto();
                elementSelectDto.setTenantDatasourceId(tenantDefineDatasources.getId());
                elementSelectDto.setName(tenantDefineDatasources.getName());
                elementSelectDto.setKbsEnable(tenantDefineDatasources.getKerberosEnabled() ? 1 : 0);
                elementSelectDto.setJdbcUrl(tenantDefineDatasources.getHiveJdbcUrl());

                elementSelectDto.setChIp(tenantDefineDatasources.getHost());
                elementSelectDto.setChPort(tenantDefineDatasources.getPort());

                elementSelectDto.setChUserName(tenantDefineDatasources.getCredentialsEnabled() ?
                        tenantDefineCredentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getUsername() : "");
                String chUserName = elementSelectDto.getChUserName();
                elementSelectDto.setChUserName(tenantDefineDatasources.getKerberosEnabled() ?
                        tenantDefineKerberosConfigsService.getById(tenantDefineDatasources.getKerberosConfigId()).getPrincipal() : chUserName);

                elementSelectDto.setChUserPassword(tenantDefineDatasources.getCredentialsEnabled() ?
                        tenantDefineCredentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getPassword() : "");
                elementSelectDto.setEsKeytabFilePath(tenantDefineDatasources.getKerberosEnabled() ?
                        tenantDefineKerberosConfigsService.getById(tenantDefineDatasources.getKerberosConfigId()).getKeytabPath() : "");
                elementSelectDto.setEsKrb5FilePath(tenantDefineDatasources.getKerberosEnabled() ?
                        tenantDefineKerberosConfigsService.getById(tenantDefineDatasources.getKerberosConfigId()).getKrb5Path() : "");
                elementSelectDto.setEsJaasFilePath("");
                elementSelectDto.setIsConnect(1);
                elementSelectDto.setFailConnectReason("");

                result.add(elementSelectDto);
            }
        } else if (type.equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.PANWEI.getName())) {
            List<TenantDefineDatasources> datasourcesList = tenantDefineDatasourcesService.list(
                    new QueryWrapper<TenantDefineDatasources>()
                            .eq("type", DATA_SOURCE_TYPE_ENUM.PANWEI.getName())
                            .eq("status", "connected")
                            .eq("tenant_id", UmsUtils.getUVO().getTenantId()));
            for (TenantDefineDatasources tenantDefineDatasources : datasourcesList) {
                ElementSelectDto elementSelectDto = new ElementSelectDto();
                elementSelectDto.setTenantDatasourceId(tenantDefineDatasources.getId());
                elementSelectDto.setName(tenantDefineDatasources.getName());
                elementSelectDto.setChIp(tenantDefineDatasources.getHost());
                elementSelectDto.setChPort(tenantDefineDatasources.getPort());
                elementSelectDto.setChUserName(tenantDefineDatasources.getCredentialsEnabled() ?
                        tenantDefineCredentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getUsername() : "");
                elementSelectDto.setChUserPassword(tenantDefineDatasources.getCredentialsEnabled() ?
                        tenantDefineCredentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getPassword() : "");
                elementSelectDto.setChIsSsl(tenantDefineDatasources.getCertificatesEnabled() ? 1 : 0);
                elementSelectDto.setIsConnect(1);
                elementSelectDto.setFailConnectReason("");
                elementSelectDto.setDbName(tenantDefineDatasources.getDbName());
                elementSelectDto.setSchemaName(tenantDefineDatasources.getSchemaName());
                result.add(elementSelectDto);
            }
        } else if (type.equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.KAFKA.getName())) {
            List<TenantDefineDatasources> datasourcesList = tenantDefineDatasourcesService.list(
                    new QueryWrapper<TenantDefineDatasources>()
                            .eq("type", DATA_SOURCE_TYPE_ENUM.KAFKA.getName())
                            .eq("status", "connected")
                            .eq("tenant_id", UmsUtils.getUVO().getTenantId()));
            for (TenantDefineDatasources tenantDefineDatasources : datasourcesList) {
                ElementSelectDto elementSelectDto = new ElementSelectDto();
                elementSelectDto.setTenantDatasourceId(tenantDefineDatasources.getId());
                elementSelectDto.setName(tenantDefineDatasources.getName());
                elementSelectDto.setKafkaBrokers(tenantDefineDatasources.getHost() + ":" + tenantDefineDatasources.getPort());
                elementSelectDto.setKafkaUserName(tenantDefineDatasources.getCredentialsEnabled() ?
                        tenantDefineCredentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getUsername() : "");
                elementSelectDto.setKafkaUserPassword(tenantDefineDatasources.getCredentialsEnabled() ?
                        tenantDefineCredentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getPassword() : "");
                elementSelectDto.setPrincipal(tenantDefineDatasources.getKerberosEnabled() ?
                        tenantDefineKerberosConfigsService.getById(tenantDefineDatasources.getKerberosConfigId()).getPrincipal() : "");
                elementSelectDto.setEsKeytabFilePath(tenantDefineDatasources.getKerberosEnabled() ?
                        tenantDefineKerberosConfigsService.getById(tenantDefineDatasources.getKerberosConfigId()).getKeytabPath() : "");
                elementSelectDto.setEsKrb5FilePath(tenantDefineDatasources.getKerberosEnabled() ?
                        tenantDefineKerberosConfigsService.getById(tenantDefineDatasources.getKerberosConfigId()).getKrb5Path() : "");
                elementSelectDto.setChIsSsl(tenantDefineDatasources.getCertificatesEnabled() ? 1 : 0);
                elementSelectDto.setIsConnect(1);
                elementSelectDto.setFailConnectReason("");
                result.add(elementSelectDto);
            }
        }
        return result;
    }

    @Override
    public BaseResponse<TestConnectResponseVo> testConnectES(String esIpPort, String esUserName, String esUserPassword, String esKbsAccount,
                                                             String esKeytabFile, String esKrb5File, String esJaasFile) {
        Map<String, ContentBody> reqParam = new HashMap<>();
        if (StringUtils.isNotEmpty(esIpPort)) {
            log.info("esIpPort is {}", esIpPort);
            reqParam.put("esIpPort", new StringBody(esIpPort, ContentType.MULTIPART_FORM_DATA));
        }
        if (StringUtils.isNotEmpty(esUserName)) {
            reqParam.put("esUserName", new StringBody(esUserName, ContentType.MULTIPART_FORM_DATA));
        }
        if (StringUtils.isNotEmpty(esUserPassword)) {
            reqParam.put("esUserPassword", new StringBody(esUserPassword, ContentType.MULTIPART_FORM_DATA));
        }
        if (StringUtils.isNotEmpty(esKbsAccount)) {
            reqParam.put("esKbsAccount", new StringBody(esKbsAccount, ContentType.MULTIPART_FORM_DATA));
        }

        if (!StringUtils.isEmpty(esKeytabFile)) {
            File esKeytab = new File(esKeytabFile);
            if (esKeytab.exists()) {
                reqParam.put("esKeytabFile", new FileBody(esKeytab));
            }
        }
        if (!StringUtils.isEmpty(esKrb5File)) {
            File esKrb5 = new File(esKrb5File);
            if (esKrb5.exists()) {
                reqParam.put("esKrb5File", new FileBody(esKrb5));
            }
        }
        if (!StringUtils.isEmpty(esJaasFile)) {
            File esJaas = new File(esJaasFile);
            if (esJaas.exists()) {
                reqParam.put("esJaasFile", new FileBody(esJaas));
            }
        }
        String esUrl = collectUrl + testConnnectES;
        TestConnectResponseVo scan = SendScanUtil.scan(esUrl, reqParam);
        if (scan.getStatus() == 0) {
            //设置连通性，给前端
            scan.setStatus(2);
        }
        return BaseResponse.success(scan);
    }

    @Override
    public BaseResponse<TestConnectResponseVo> testConnectHive(String ip, Integer port, String userName, String password, String keytabFile,
                                                               String krb5File, String jaasConfPath, Integer kbsEnable, String jdbcUrl) throws IOException {
        log.info("开始测试Hive连接");
        Map<String, ContentBody> reqParam = new HashMap<>();
        if (StringUtils.isEmpty(ip)) {
            ip = "";
        }
        reqParam.put("chIp", new StringBody(ip, ContentType.MULTIPART_FORM_DATA));
        reqParam.put("chPort", new StringBody(port == null ? "" : String.valueOf(port), ContentType.MULTIPART_FORM_DATA));
        if (StringUtils.isEmpty(userName)) {
            userName = "";
        }
        reqParam.put("chUserName", new StringBody(userName, ContentType.MULTIPART_FORM_DATA));
        if (StringUtils.isEmpty(password)) {
            password = "";
        }
        reqParam.put("chUserPassword", new StringBody(password, ContentType.MULTIPART_FORM_DATA));
        reqParam.put("kbsEnable", new StringBody(kbsEnable == null ? "" : String.valueOf(kbsEnable), ContentType.MULTIPART_FORM_DATA));
        if (StringUtils.isEmpty(jdbcUrl)) {
            jdbcUrl = "";
        }
        reqParam.put("jdbcUrl", new StringBody(jdbcUrl, ContentType.MULTIPART_FORM_DATA));

        BASE64Decoder decoder = new BASE64Decoder();
        if (!StringUtils.isEmpty(keytabFile)) {
            File keytab = new File(new String(decoder.decodeBuffer(keytabFile)));
            if (keytab.exists()) {
                reqParam.put("keytabFile", new FileBody(keytab));
            }
        }
        if (!StringUtils.isEmpty(krb5File)) {
            File krb5 = new File(new String(decoder.decodeBuffer(krb5File)));
            if (krb5.exists()) {
                reqParam.put("krb5File", new FileBody(krb5));
            }
        }
        if (!StringUtils.isEmpty(jaasConfPath)) {
            File jassConfFile = new File(new String(decoder.decodeBuffer(jaasConfPath)));
            if (jassConfFile.exists()) {
                reqParam.put("jaasConfFile", new FileBody(jassConfFile));
            }
        }

        String hiveUrl = collectUrl + testConnectHive;
        log.info("传递参数：{}", JSON.toJSONString(reqParam));
        TestConnectResponseVo scan = SendScanUtil.scan(hiveUrl, reqParam);
        if (scan.getStatus() == 0) {
            //设置连通性，给前端
            scan.setStatus(2);
        }
        return BaseResponse.success(scan);
    }

    @Override
    public BaseResponse<TestConnectResponseVo> testConnectCH(String chIp, Integer chPort, String chUserName, String chUserPassword,
                                                             Integer chIsSsl) {
        Map<String, ContentBody> reqParam = new HashMap<>();
        reqParam.put("chIp", new StringBody(chIp, ContentType.MULTIPART_FORM_DATA));
        reqParam.put("chPort", new StringBody(String.valueOf(chPort), ContentType.MULTIPART_FORM_DATA));
        reqParam.put("chUserName", new StringBody(chUserName, ContentType.MULTIPART_FORM_DATA));
        reqParam.put("chUserPassword", new StringBody(chUserPassword, ContentType.MULTIPART_FORM_DATA));
        if (chIsSsl == null) {
            chIsSsl = 1;
        }
        reqParam.put("chIsSsl", new StringBody(String.valueOf(chIsSsl), ContentType.MULTIPART_FORM_DATA));
        String chUrl = collectUrl + testConnectCH;
        TestConnectResponseVo scan = SendScanUtil.scan(chUrl, reqParam);
        if (scan.getStatus() == 0) {
            //设置连通性，给前端
            scan.setStatus(2);
        }
        return BaseResponse.success(scan);
    }

    @Override
    public BaseResponse<TestConnectResponseVo> testConnectMysql(String ip, Integer port, String userName, String password) {
        Map<String, ContentBody> reqParam = new HashMap<>();
        reqParam.put("chIp", new StringBody(ip, ContentType.MULTIPART_FORM_DATA));
        reqParam.put("chPort", new StringBody(String.valueOf(port), ContentType.MULTIPART_FORM_DATA));
        reqParam.put("chUserName", new StringBody(userName, ContentType.MULTIPART_FORM_DATA));
        reqParam.put("chUserPassword", new StringBody(password, ContentType.MULTIPART_FORM_DATA));
        String mysqlUrl = collectUrl + testConnectMysql;
        TestConnectResponseVo scan = SendScanUtil.scan(mysqlUrl, reqParam);
        if (scan.getStatus() == 0) {
            //设置连通性，给前端
            scan.setStatus(2);
        }
        return BaseResponse.success(scan);
    }

    @Override
    public BaseResponse<TestConnectResponseVo> testConnectPanWei(String ip, Integer port, String userName, String password, String dbName,
                                                                 String schemaName) {
        Map<String, ContentBody> reqParam = new HashMap<>();
        reqParam.put("chIp", new StringBody(ip, ContentType.MULTIPART_FORM_DATA));
        reqParam.put("chPort", new StringBody(String.valueOf(port), ContentType.MULTIPART_FORM_DATA));
        reqParam.put("chUserName", new StringBody(userName, ContentType.MULTIPART_FORM_DATA));
        reqParam.put("chUserPassword", new StringBody(password, ContentType.MULTIPART_FORM_DATA));
        reqParam.put("dbName", new StringBody(dbName, ContentType.MULTIPART_FORM_DATA));
        reqParam.put("schemaName", new StringBody(schemaName, ContentType.MULTIPART_FORM_DATA));
        String testUrl = collectUrl + testConnectPanWei;
        TestConnectResponseVo scan = SendScanUtil.scan(testUrl, reqParam);
        if (scan.getStatus() == 0) {
            //设置连通性，给前端
            scan.setStatus(2);
        }
        return BaseResponse.success(scan);
    }

    @Override
    public BaseResponse<TestConnectResponseVo> testConnectKafka(String kafkaBrokers, String kafkaUserName, String kafkaUserPassword,
                                                                String keytabFile, String krb5File, String principal) {
        log.info("开始测试Kafka连接");
        Map<String, ContentBody> reqParam = new HashMap<>();

        reqParam.put("kafkaBrokers", new StringBody(kafkaBrokers, ContentType.MULTIPART_FORM_DATA));
        reqParam.put("kafkaUserName", new StringBody(StringUtils.isEmpty(kafkaUserName) ? "" : kafkaUserName, ContentType.MULTIPART_FORM_DATA));
        reqParam.put("kafkaUserPassword", new StringBody(StringUtils.isEmpty(kafkaUserPassword) ? "" : kafkaUserPassword,
                ContentType.MULTIPART_FORM_DATA));
        reqParam.put("principal", new StringBody(StringUtils.isEmpty(principal) ? "" : principal, ContentType.MULTIPART_FORM_DATA));

        try {
            BASE64Decoder decoder = new BASE64Decoder();
            if (!StringUtils.isEmpty(keytabFile)) {
                File keytab = new File(new String(decoder.decodeBuffer(keytabFile)));
                if (keytab.exists()) {
                    reqParam.put("keytabFile", new FileBody(keytab));
                }
            }
            if (!StringUtils.isEmpty(krb5File)) {
                File krb5 = new File(new String(decoder.decodeBuffer(krb5File)));
                if (krb5.exists()) {
                    reqParam.put("krb5File", new FileBody(krb5));
                }
            }
        } catch (IOException e) {
            log.error("测试链接失败", e);
            return BaseResponse.error("测试链接失败");
        }


        String hiveUrl = collectUrl + testConnectKafka;
        log.info("传递参数：{}", JSON.toJSONString(reqParam));
        TestConnectResponseVo scan = SendScanUtil.scan(hiveUrl, reqParam);
        if (scan.getStatus() == 0) {
            //设置连通性，给前端
            scan.setStatus(2);
        }
        return BaseResponse.success(scan);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public BaseResponse<String> addOrUpdate(AddOrUpdateElementRequestDto requestDto) {
        log.info("requestDto---:{}", JSONUtil.toJsonStr(requestDto));
        Long id = requestDto.getId();
        UserValueObject uvo = UmsUtils.getUVO();
        Long tenantId = Long.valueOf(uvo.getTenantId());
        String userName = uvo.getUserName();
        try {
            if (id == null) {
                //验重
                List<QuaWabElement> exists = list(new QueryWrapper<QuaWabElement>()
                        .eq("element_type", requestDto.getElementType())
                        .eq("tenant_id", tenantId)
                        .eq("flag", 1)
                        .eq("element_name", requestDto.getElementName()));
                if (exists != null && exists.size() > 0) {
                    return BaseResponse.error("名称重复");
                }
                // 验证采集资源是否重复
                List<QuaWabElement> existCHElement = list(new QueryWrapper<QuaWabElement>()
                        .eq("element_type", requestDto.getElementType())
                        .eq("tenant_id", tenantId)
                        .eq("flag", 1)
                        .eq("ch_ip", requestDto.getChIp())
                        .eq("ch_port", requestDto.getChPort()));
                if (existCHElement != null && existCHElement.size() > 0) {
                    return BaseResponse.error("扫描资源已存在,请勿重复添加");
                }
                List<QuaWabElement> existESElement = list(new QueryWrapper<QuaWabElement>()
                        .eq("element_type", requestDto.getElementType())
                        .eq("tenant_id", tenantId)
                        .eq("flag", 1)
                        .eq("es_ip_port", requestDto.getEsIpPort()));
                if (existESElement != null && existESElement.size() > 0) {
                    return BaseResponse.error("扫描资源已存在,请勿重复添加");
                }
                //新增元数据
                QuaWabElement element = new QuaWabElement();
                BeanUtils.copyProperties(requestDto, element);
                element.setTenantId(tenantId);
                element.setFlag(1);
                element.setCreateUser(userName);
                element.setUpdateUser(userName);
                if (ElementTypeEnum.HIVE.getCode().equals(requestDto.getElementType())) {
                    if ((element.getKbsEnable() != null && element.getKbsEnable() == 1) ||
                            (StringUtils.isNotBlank(requestDto.getKeyTabPath()) && StringUtils.isNotBlank(requestDto.getKrb5ConfPath()))) {
                        element.setEsAuthType(ESAuthTypeEnum.UPLOAD_KBS_FILE.getCode());
                    } else {
                        // Hive先支持账号、密码认证
                        element.setEsAuthType(ESAuthTypeEnum.ACCOUNT.getCode());
                    }
                }
                this.save(element);
                //新Job并绑定
                QuaJob quaJob = new QuaJob();
                quaJob.setElementId(element.getId());
                //是否绑定JOB，默认是1
                quaJob.setIsMapToJob(1);
                quaJob.setJobGroup(requestDto.getElementType());
                quaJob.setJobName(requestDto.getElementName() + "-" + JobTypeEnum.CYCLE.getDesc() + "-" + System.currentTimeMillis());
                quaJob.setJobType(JobTypeEnum.CYCLE.getType());
                CronUtil.CronDTO cronDTO = CronUtil.transCron(requestDto.getExecuteType(), requestDto.getConfigJson());
                quaJob.setJobCron(cronDTO.getCron());
                quaJob.setJobCronExpression(cronDTO.getName());
                //1已启动，2已暂停
                if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                    //连接失败时就暂停
                    quaJob.setJobState(JobStateEnum.STOP.getState());
                }
                quaJob.setTenantId(tenantId);
                quaJob.setCreateUser(userName);
                quaJob.setUpdateUser(userName);
                quaJob.setFlag(1);
                jobService.save(quaJob);

                //启动JOB
                JobKey jobKey = JobKey.jobKey(quaJob.getJobName(), quaJob.getJobGroup());
                Map<String, Object> elementMap = new HashMap<>();
                elementMap.put("element", element);
                elementMap.put("job", quaJob);
                if (ElementTypeEnum.CH.getCode().equals(requestDto.getElementType())) {
                    BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanCHJob.class);
                    quartzJobService.scheduleJob(task);
                    if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                        //连接失败时就暂停
                        quartzJobService.pauseJob(jobKey);
                    }
                } else if (ElementTypeEnum.ES.getCode().equals(requestDto.getElementType())) {
                    BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanESJob.class);
                    quartzJobService.scheduleJob(task);
                    if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                        //连接失败时就暂停
                        quartzJobService.pauseJob(jobKey);
                    }
                } else if (ElementTypeEnum.MYSQL.getCode().equals(requestDto.getElementType())) {
                    BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanMysqlJob.class);
                    quartzJobService.scheduleJob(task);
                    if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                        //连接失败时就暂停
                        quartzJobService.pauseJob(jobKey);
                    }
                } else if (ElementTypeEnum.HIVE.getCode().equals(requestDto.getElementType())) {
                    BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanHiveJob.class);
                    quartzJobService.scheduleJob(task);
                    if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                        //连接失败时就暂停
                        quartzJobService.pauseJob(jobKey);
                    }
                } else if (ElementTypeEnum.PANWEI.getCode().equals(requestDto.getElementType())) {
                    BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanPanWeiJob.class);
                    quartzJobService.scheduleJob(task);
                    if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                        //连接失败时就暂停
                        quartzJobService.pauseJob(jobKey);
                    }
                } else if (ElementTypeEnum.KAFKA.getCode().equals(requestDto.getElementType())) {
                    BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanHiveJob.class);
                    quartzJobService.scheduleJob(task);
                    if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                        //连接失败时就暂停
                        quartzJobService.pauseJob(jobKey);
                    }
                } else {
                    throw new RuntimeException("扫描资源类型不支持");
                }
                return BaseResponse.success("新增元数据成功");
            } else {
                //验重
                List<QuaWabElement> exists = list(new QueryWrapper<QuaWabElement>().eq("tenant_id", tenantId).eq("flag", 1).ne("id", id).eq(
                        "element_name", requestDto.getElementName()));
                if (exists != null && exists.size() > 0) {
                    return BaseResponse.error("名称重复");
                }
                //修改元数据
                QuaWabElement element = getOne(new QueryWrapper<QuaWabElement>().eq("id", id));
                BeanUtils.copyProperties(requestDto, element);
                if (ElementTypeEnum.HIVE.getCode().equals(requestDto.getElementType())) {
                    if ((element.getKbsEnable() != null && element.getKbsEnable() == 1) ||
                            (StringUtils.isNotBlank(requestDto.getKeyTabPath()) && StringUtils.isNotBlank(requestDto.getKrb5ConfPath()))) {
                        element.setEsAuthType(ESAuthTypeEnum.UPLOAD_KBS_FILE.getCode());
                    } else {
                        // Hive先支持账号、密码认证
                        element.setEsAuthType(ESAuthTypeEnum.ACCOUNT.getCode());
                    }
                }
                element.setUpdateUser(userName);
                this.updateById(element);
                log.info("element2: {}", JSONUtil.toJsonStr(element));
                //修改Job记录
                QuaJob job = jobService.getOne(new QueryWrapper<QuaJob>().eq("element_id", id).eq("is_map_to_job", 1));
                if (job != null) {
                    //修改JOB
                    CronUtil.CronDTO cronDTO = CronUtil.transCron(requestDto.getExecuteType(), requestDto.getConfigJson());
                    job.setJobCron(cronDTO.getCron());
                    job.setJobCronExpression(cronDTO.getName());
                    job.setUpdateUser(userName);
                    //如果链接异常就设置暂停状态
                    if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                        job.setJobState(JobStateEnum.STOP.getState());
                    }
                    jobService.updateById(job);
                    //重新设置Quartz触发，只修改CRON表达式
                    Map<String, Object> elementMap = new HashMap<>();
                    elementMap.put("element", element);
                    elementMap.put("job", job);
                    BaseTask task = new BaseTask(JobKey.jobKey(job.getJobName(), job.getJobGroup()), job.getJobName(), cronDTO.getCron(),
                            elementMap, null);
                    quartzJobService.modifyJob(task);
                    if (job.getJobState().intValue() == JobStateEnum.STARTED.getState().intValue()) {
                        quartzJobService.resumeJob(JobKey.jobKey(job.getJobName(), job.getJobGroup()));
                        if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                            //连接失败时就暂停
                            quartzJobService.pauseJob(JobKey.jobKey(job.getJobName(), job.getJobGroup()));
                        }
                    } else if (job.getJobState().intValue() == JobStateEnum.STOP.getState().intValue()) {
                        quartzJobService.pauseJob(JobKey.jobKey(job.getJobName(), job.getJobGroup()));
                    }
                } else {
                    //新Job并绑定
                    QuaJob quaJob = new QuaJob();
                    quaJob.setElementId(element.getId());
                    //是否绑定JOB，默认是1
                    quaJob.setIsMapToJob(1);
                    quaJob.setJobGroup(requestDto.getElementType());
                    quaJob.setJobName(requestDto.getElementName() + "-" + JobTypeEnum.CYCLE.getDesc() + "-" + System.currentTimeMillis());
                    quaJob.setJobType(JobTypeEnum.CYCLE.getType());
                    CronUtil.CronDTO cronDTO = CronUtil.transCron(requestDto.getExecuteType(), requestDto.getConfigJson());
                    quaJob.setJobCron(cronDTO.getCron());
                    quaJob.setJobCronExpression(cronDTO.getName());
                    //1已启动，2已暂停
                    quaJob.setJobState(1);
                    if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                        quaJob.setJobState(2);
                    }
                    quaJob.setTenantId(tenantId);
                    quaJob.setCreateUser(userName);
                    quaJob.setUpdateUser(userName);
                    quaJob.setFlag(1);
                    jobService.save(quaJob);

                    //启动JOB
                    JobKey jobKey = JobKey.jobKey(quaJob.getJobName(), quaJob.getJobGroup());
                    Map<String, Object> elementMap = new HashMap<>();
                    elementMap.put("element", element);
                    elementMap.put("job", quaJob);
                    if (ElementTypeEnum.CH.getCode().equals(requestDto.getElementType())) {
                        BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanCHJob.class);
                        quartzJobService.scheduleJob(task);
                        if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                            //连接失败时就暂停
                            quartzJobService.pauseJob(jobKey);
                        }
                    } else if (ElementTypeEnum.ES.getCode().equals(requestDto.getElementType())) {
                        BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanESJob.class);
                        quartzJobService.scheduleJob(task);
                        if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                            //连接失败时就暂停
                            quartzJobService.pauseJob(jobKey);
                        }
                    } else if (ElementTypeEnum.MYSQL.getCode().equals(requestDto.getElementType())) {
                        BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanMysqlJob.class);
                        quartzJobService.scheduleJob(task);
                        if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                            //连接失败时就暂停
                            quartzJobService.pauseJob(jobKey);
                        }
                    } else if (ElementTypeEnum.KAFKA.getCode().equals(requestDto.getElementType())) {
                        BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanKafkaJob.class);
                        quartzJobService.scheduleJob(task);
                        if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                            //连接失败时就暂停
                            quartzJobService.pauseJob(jobKey);
                        }
                    } else if (ElementTypeEnum.HIVE.getCode().equals(requestDto.getElementType())) {
                        BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanHiveJob.class);
                        quartzJobService.scheduleJob(task);
                        if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                            //连接失败时就暂停
                            quartzJobService.pauseJob(jobKey);
                        }
                    } else if (ElementTypeEnum.PANWEI.getCode().equals(requestDto.getElementType())) {
                        BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanPanWeiJob.class);
                        quartzJobService.scheduleJob(task);
                        if (requestDto.getIsConnect().intValue() == ConnectStatusEnum.NOT_CONNECTED.getStatus().intValue()) {
                            //连接失败时就暂停
                            quartzJobService.pauseJob(jobKey);
                        }
                    } else {
                        throw new RuntimeException("扫描资源类型不支持");
                    }
                    return BaseResponse.success("修改元数据成功");
                }
            }
            return BaseResponse.success("修改元数据成功");
        } catch (Exception e) {
            log.error("新增或元数据异常: ", e);
            throw new RuntimeException(e);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public BaseResponse<String> pauseOrResumeJon(Long elementId, Integer state) {
        try {
            QuaJob job = jobService.getOne(new QueryWrapper<QuaJob>().eq("element_id", elementId).eq("is_map_to_job", 1));
            job.setJobState(state);
            jobService.updateById(job);
            if (JobStateEnum.STARTED.getState().intValue() == state.intValue()) {
                log.info("启动任务，{}", job.getJobName());
                quartzJobService.resumeJob(JobKey.jobKey(job.getJobName(), job.getJobGroup()));
            } else if (JobStateEnum.STOP.getState().intValue() == state.intValue()) {
                log.info("暂停任务，{}", job.getJobName());
                quartzJobService.pauseJob(JobKey.jobKey(job.getJobName(), job.getJobGroup()));
            }
            return BaseResponse.success("操作成功");
        } catch (Exception e) {
            log.error("启动或暂停JOB异常", e);
            throw new RuntimeException(e);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public BaseResponse<String> remove(Long elementId) {
        try {
            //删除JOB
            List<QuaJob> jobs = jobService.list(new QueryWrapper<QuaJob>().eq("element_id", elementId));
            for (QuaJob job : jobs) {
                if (job.getIsMapToJob() == 1 && JobStateEnum.STARTED.getState().intValue() == job.getJobState().intValue()) {
                    return BaseResponse.error("采集处于开启状态，不可删除");
                }
                job.setFlag(0);
            }
            jobService.updateBatchById(jobs);
            //删除元数据
            QuaWabElement byId = this.getById(elementId);
            byId.setFlag(0);
            this.updateById(byId);
            //删除Quartz里的Job
            for (QuaJob job : jobs) {
                quartzJobService.deleteJob(JobKey.jobKey(job.getJobName(), job.getJobGroup()));
            }
            return BaseResponse.success("删除成功");
        } catch (Exception e) {
            log.error("删除元数据异常", e);
            throw new RuntimeException(e);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public BaseResponse<String> execute(Long elementId, Integer executeType, String dateTime) {
        QuaWabElement element = this.getById(elementId);
        try {
            if (JobTypeEnum.EXECUTE_IMMEDIATELY.getType() == executeType) {
                //立即执行
                Calendar newTime = Calendar.getInstance();
                //加5秒执行
                newTime.add(Calendar.SECOND, 30);
                Date date = newTime.getTime();
                String cron = getCron(date);
                log.info("立即执行时间:{} ", cron);
                executeJob(element, executeType, cron, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date), date);
            } else if (JobTypeEnum.SPECIFIED_TIME.getType() == executeType) {
                //指定时间
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = format.parse(dateTime);
                String cron = getCron(date);
                executeJob(element, executeType, cron, dateTime, date);
            } else {
                return BaseResponse.error("执行方式有误！");
            }
            return BaseResponse.success("执行成功");
        } catch (Exception e) {
            log.error("执行扫描失败");
            throw new RuntimeException(e);
        }
    }

    @Override
    public BaseResponse<ElementCountVo> countVo(String elementType) {
        ElementCountVo vo = new ElementCountVo();
        vo.setConnectNormalCount(0);
        vo.setConnectUNNormalCount(0);
        vo.setJobStopCount(0);
        vo.setJobStartCount(0);
        Long tenantId = Long.valueOf(UmsUtils.getUVO().getTenantId());
        List<CountConnectDto> countConnectDtos = elementMapper.countConnect(tenantId, elementType);
        for (CountConnectDto countConnectDto : countConnectDtos) {
            if (countConnectDto.getConnect() == 2) {
                vo.setConnectUNNormalCount(countConnectDto.getCnt());
            } else if (countConnectDto.getConnect() == 1) {
                vo.setConnectNormalCount(countConnectDto.getCnt());
            }
        }
        List<CountJobStateDto> countJobStateDtos = elementMapper.countJobState(tenantId, elementType);
        for (CountJobStateDto countJobStateDto : countJobStateDtos) {
            if (countJobStateDto.getJobState() == 2) {
                vo.setJobStopCount(countJobStateDto.getCnt());
            } else if (countJobStateDto.getJobState() == 1) {
                vo.setJobStartCount(countJobStateDto.getCnt());
            }
        }
        return BaseResponse.success(vo);
    }

    @Override
    public BasePageResponse<List<ElementPageResponseDto>> queryPage(ElementPageRequestDto requestDto) {
        List<ElementPageResponseDto> list = new ArrayList<>();
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<ElementPageResponseDto> page = elementMapper.queryPage(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
    }

    @Override
    public BaseResponse<QuaWabElementExt> viewById(Long elementId) {
        QuaWabElement byId = getById(elementId);
        if (byId == null) {
            return BaseResponse.no_data();
        }
        QuaWabElementExt ext = new QuaWabElementExt();
        BeanUtils.copyProperties(byId, ext);

        /*if (byId.getElementType().equals(ElementTypeEnum.ES.getCode())) {
            if (StringUtils.isNotEmpty(ext.getEsKeytabFilePath())) {
                ext.setKeytabFilePathDesc(ext.getEsKeytabFilePath().split(Constant.UPLOAD_FILE_DELIMITER)[1]);
            }
            if (StringUtils.isNotEmpty(ext.getEsKrb5FilePath())) {
                ext.setKrb5FilePathDesc(ext.getEsKrb5FilePath().split(Constant.UPLOAD_FILE_DELIMITER)[1]);
            }
            if (StringUtils.isNotEmpty(ext.getEsJaasFilePath())) {
                ext.setJaasFilePathDesc(ext.getEsJaasFilePath().split(Constant.UPLOAD_FILE_DELIMITER)[1]);
            }

            if (byId.getEsAuthType() == ESAuthTypeEnum.ACCOUNT.getCode()) {
                ext.setEsAuthTypeDec(ESAuthTypeEnum.ACCOUNT.getDesc());
            } else {
                ext.setEsAuthTypeDec("文件认证");
            }
            if (byId.getEsKbsTemplateId() != null) {
                KbsFileConfig kbsFileConfig = kbsFileConfigService.getById(byId.getEsKbsTemplateId());
                if (kbsFileConfig != null) {
                    ext.setEsKbsTemplateName(kbsFileConfig.getTemplateName());
                }
            }
        }*/

        return BaseResponse.success(ext);
    }

    private void executeJob(QuaWabElement element, Integer executeType, String cron, String dateTime, Date date) throws Exception {
        UserValueObject uvo = UmsUtils.getUVO();
        String userName = uvo.getUserName();
        Long tenantId = Long.valueOf(uvo.getTenantId());
        QuaJob job = new QuaJob();
        job.setElementId(element.getId());
        job.setIsMapToJob(0);
        job.setJobGroup(element.getElementType());
        job.setJobName(element.getElementName() + "-" + JobTypeEnum.SPECIFIED_TIME.getDesc() + "-" + dateTime);
        job.setJobType(executeType);
        job.setJobCron(cron);
        job.setJobCronExpression(JobTypeEnum.SPECIFIED_TIME.getDesc() + "-" + dateTime);
        job.setConfigDateTime(DateUtils.Date2LocalDateTime(date));
        job.setJobState(JobStateEnum.STARTED.getState());
        job.setCreateUser(userName);
        job.setUpdateUser(userName);
        job.setTenantId(tenantId);
        job.setFlag(1);
        //保存指定时间Job
        jobService.save(job);
        Map<String, Object> elementMap = new HashMap<>();
        elementMap.put("element", element);
        elementMap.put("job", job);
        long timeMillis = System.currentTimeMillis();
        if (ElementTypeEnum.CH.getCode().equals(element.getElementType())) {
            BaseTask task = new BaseTask(JobKey.jobKey(job.getJobName() + timeMillis, job.getJobGroup()), job.getJobName(), job.getJobCron(),
                    elementMap,
                    ScanCHJob.class);
            quartzJobService.scheduleJob(task);
        } else if (ElementTypeEnum.ES.getCode().equals(element.getElementType())) {
            BaseTask task = new BaseTask(JobKey.jobKey(job.getJobName() + timeMillis, job.getJobGroup()), job.getJobName(), job.getJobCron(),
                    elementMap,
                    ScanESJob.class);
            quartzJobService.scheduleJob(task);
        } else if (ElementTypeEnum.MYSQL.getCode().equals(element.getElementType())) {
            BaseTask task = new BaseTask(JobKey.jobKey(job.getJobName() + timeMillis, job.getJobGroup()), job.getJobName(), job.getJobCron(),
                    elementMap,
                    ScanMysqlJob.class);
            quartzJobService.scheduleJob(task);
        } else if (ElementTypeEnum.HIVE.getCode().equals(element.getElementType())) {
            BaseTask task = new BaseTask(JobKey.jobKey(job.getJobName() + timeMillis, job.getJobGroup()), job.getJobName(), job.getJobCron(),
                    elementMap,
                    ScanHiveJob.class);
            quartzJobService.scheduleJob(task);
        } else if (ElementTypeEnum.PANWEI.getCode().equals(element.getElementType())) {
            BaseTask task = new BaseTask(JobKey.jobKey(job.getJobName() + timeMillis, job.getJobGroup()), job.getJobName(), job.getJobCron(),
                    elementMap,
                    ScanPanWeiJob.class);
            quartzJobService.scheduleJob(task);
        } else if (ElementTypeEnum.KAFKA.getCode().equals(element.getElementType())) {
            BaseTask task = new BaseTask(JobKey.jobKey(job.getJobName() + timeMillis, job.getJobGroup()), job.getJobName(), job.getJobCron(),
                    elementMap,
                    ScanKafkaJob.class);
            quartzJobService.scheduleJob(task);
        } else {
            throw new RuntimeException("扫描资源类型不支持");
        }
    }

    private String formatDateByPattern(Date date, String dateFormat) {
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        String formatTimeStr = null;
        if (date != null) {
            formatTimeStr = sdf.format(date);
        }
        return formatTimeStr;
    }

    private String getCron(Date date) {
        String dateFormat = "ss mm HH dd MM ? yyyy";
        return formatDateByPattern(date, dateFormat);
    }


}
