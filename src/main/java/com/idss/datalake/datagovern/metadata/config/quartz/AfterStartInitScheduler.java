//package com.idss.datalake.datagovern.metadata.config.quartz;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorJob;
//import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorJobService;
//import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
//import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
//import com.idss.datalake.datagovern.metadata.model.element.enums.JobStateEnum;
//import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
//import com.idss.datalake.datagovern.metadata.model.job.entity.QuaJob;
//import com.idss.datalake.datagovern.metadata.model.job.service.QuaJobService;
//import com.idss.datalake.datagovern.metadata.model.quartz.entity.BaseTask;
//import com.idss.datalake.datagovern.metadata.model.quartz.job.QualityMonitorJob;
//import com.idss.datalake.datagovern.metadata.model.quartz.job.ScanCHJob;
//import com.idss.datalake.datagovern.metadata.model.quartz.job.ScanESJob;
//import com.idss.datalake.datagovern.metadata.model.quartz.job.ScanHiveJob;
//import com.idss.datalake.datagovern.metadata.model.quartz.job.ScanMysqlJob;
//import com.idss.datalake.datagovern.metadata.model.quartz.service.QuartzJobService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.quartz.JobKey;
//import org.quartz.SchedulerException;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.ApplicationArguments;
//import org.springframework.boot.ApplicationRunner;
//import org.springframework.stereotype.Component;
//
//import java.util.Collection;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * @Author: xiexiaofei
// * @Date: 20/6/2021 11:06
// * @Description:
// */
//@Component
//@Slf4j
//public class AfterStartInitScheduler implements ApplicationRunner {
//    @Autowired
//    private QuaJobService jobService;
//    @Autowired
//    private QuartzJobService quartzJobService;
//    @Autowired
//    private QuaWabElementService elementService;
//    @Autowired
//    private IQuaMonitorJobService monitorJobService;
//
//    @Override
//    public void run(ApplicationArguments args) throws Exception {
//        try {
//            log.info("===== 系统初始后，初始化所有任务数据到内存中 =====");
//            //查询所有未删除的Job
//            List<QuaJob> jobList = jobService.list(new QueryWrapper<QuaJob>().eq("flag", "1"));
//            //查询Job对应的参数
//            if (jobList != null && jobList.size() > 0) {
//                List<Long> elementIds = jobList.stream().map(QuaJob::getElementId).collect(Collectors.toList());
//                Collection<QuaWabElement> elements = elementService.listByIds(elementIds);
//                for (QuaJob quaJob : jobList) {
//                    List<QuaWabElement> filterElements =
//                            elements.stream().filter(element -> element.getId().longValue() == quaJob.getElementId().longValue()).collect(Collectors.toList());
//                    if (filterElements.size() == 0) {
//                        log.info("检测到Job与Element不对应，JOBID:{}", quaJob.getId());
//                    } else {
//                        //初始化Quartz中
//                        QuaWabElement element = filterElements.get(0);
//                        JobKey jobKey = JobKey.jobKey(quaJob.getJobName(), quaJob.getJobGroup());
//                        Map<String, Object> elementMap = new HashMap<>();
//                        elementMap.put("element", element);
//                        elementMap.put("job", quaJob);
//                        try {
//                            if (ElementTypeEnum.CH.getCode().equals(element.getElementType())) {
//                                BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanCHJob.class);
//                                quartzJobService.scheduleJob(task);
//                            } else if (ElementTypeEnum.ES.getCode().equals(element.getElementType())) {
//                                BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanESJob.class);
//                                quartzJobService.scheduleJob(task);
//                            } else if (ElementTypeEnum.MYSQL.getCode().equals(element.getElementType())) {
//                                BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanMysqlJob.class);
//                                quartzJobService.scheduleJob(task);
//                            } else if (ElementTypeEnum.HIVE.getCode().equals(element.getElementType())) {
//                                BaseTask task = new BaseTask(jobKey, quaJob.getJobName(), quaJob.getJobCron(), elementMap, ScanHiveJob.class);
//                                quartzJobService.scheduleJob(task);
//                            } else {
//                                throw new RuntimeException("扫描资源类型不支持");
//                            }
//                            // 如果是已暂停状态，调度后再暂停
//                            if (JobStateEnum.STOP.getState().intValue() == quaJob.getJobState()) {
//                                log.info("初始化时，任务[{}]状态是暂停", quaJob.getJobName());
//                                quartzJobService.pauseJob(jobKey);
//                            }
//                        } catch (SchedulerException e) {
//                            log.error("系统初始化部分调度任务数据失败：{}", e.getMessage(), e);
//                        }
//                    }
//                }
//            }
//            // 初始化质量监测定时任务
//            List<QuaMonitorJob> monitorJobs = monitorJobService.list(new QueryWrapper<QuaMonitorJob>()
//                    .eq("flag", "1")
//                    .eq("execute_cycle", "02"));
//            if (CollectionUtils.isNotEmpty(monitorJobs)) {
//                for (QuaMonitorJob monitorJob : monitorJobs) {
//                    Map<String, Object> elementMap = new HashMap<>();
//                    elementMap.put("job", monitorJob);
//                    JobKey jobKey = JobKey.jobKey(monitorJob.getName(), String.valueOf(monitorJob.getId()));
//                    BaseTask task = new BaseTask(jobKey, monitorJob.getName(), monitorJob.getExecuteCron(), elementMap, QualityMonitorJob.class);
//                    quartzJobService.scheduleJob(task);
//                }
//            }
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            log.error("系统初始化失败");
//            System.exit(0);
//        }
//    }
//}
