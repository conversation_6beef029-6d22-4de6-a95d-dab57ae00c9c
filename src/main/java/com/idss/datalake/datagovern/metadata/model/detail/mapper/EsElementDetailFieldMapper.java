package com.idss.datalake.datagovern.metadata.model.detail.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailField;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.ElementDetailPageRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementDetailVo;

/**
 * <p>
 * ES清单Field表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
public interface EsElementDetailFieldMapper extends BaseMapper<EsElementDetailField> {
    Page<ElementDetailVo> queryDetailColumnPage(ElementDetailPageRequestDto requestDto);
}
