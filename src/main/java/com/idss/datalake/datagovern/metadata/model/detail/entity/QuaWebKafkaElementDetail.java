package com.idss.datalake.datagovern.metadata.model.detail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * Kafka元素详情表
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("qua_web_kafka_element_detail")
public class QuaWebKafkaElementDetail {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 元素ID
     */
    private Long elementId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * topic名称
     */
    private String topicName;

    /**
     * 分区数
     */
    private Integer partitions;

    /**
     * 副本数
     */
    private Integer replicationFactor;

    /**
     * 首次快照版本
     */
    private String firstSnapshootVersion;

    /**
     * 最新快照版本
     */
    private String lastSnapshootVersion;

    /**
     * 创建人
     */
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private String topicNameCn;
    private String topicDescribe;
    private String topicOwner;
    private Integer isSensitive;

    /**
     * 元数据来源，1-自建，2-数据建模
     */
    private Integer metaSource;
    /**
     * 关键词，["a","b"]
     */
    private String keyWords;
}