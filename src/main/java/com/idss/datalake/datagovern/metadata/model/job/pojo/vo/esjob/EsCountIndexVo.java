package com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON>xiaof<PERSON>
 * @Date: 23/6/2021 13:29
 * @Description:
 */
@Data
@ApiModel("ES任务详情统计索引")
public class EsCountIndexVo {
    @ApiModelProperty("资源ID")
    private Long elementId;
    @ApiModelProperty("索引ID")
    private Long indexId;
    @ApiModelProperty("快照版本")
    private String snapshootVersion;
    @ApiModelProperty("索引名称")
    private String indexName;
    @ApiModelProperty("字段数")
    private Integer fieldCount;
}
