package com.idss.datalake.datagovern.metadata.model.detail.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebKafkaElementDetail;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.UpdateDetailKafkaRequestDto;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.KafkaExtInfo;

import javax.validation.Valid;

/**
 * <p>
 * Kafka元素详情服务接口
 * </p>
 */
public interface IQuaWebKafkaElementDetailService extends IService<QuaWebKafkaElementDetail> {
    KafkaExtInfo extInfo(Long elementId, String topicName);

    BaseResponse<String> updateDetail(@Valid UpdateDetailKafkaRequestDto requestDto);
}