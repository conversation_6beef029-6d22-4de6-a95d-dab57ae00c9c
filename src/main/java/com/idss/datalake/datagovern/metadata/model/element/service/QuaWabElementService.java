package com.idss.datalake.datagovern.metadata.model.element.service;

import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.metadata.model.element.pojo.dto.*;
import com.idss.datalake.datagovern.metadata.model.element.pojo.vo.ElementCountVo;
import com.idss.datalake.datagovern.metadata.model.element.utils.TestConnectResponseVo;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 元数据管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
public interface QuaWabElementService extends IService<QuaWabElement> {

    /**
     * 元数据类型下拉框
     *
     * @param type 元数据类型
     * @return
     */
    List<ElementSelectDto> selectList(String type);


    /**
     * 测试连接ES
     *
     * @param esIpPort       ES集群地址
     * @param esUserName     ES用户名
     * @param esUserPassword ES密码
     * @param esKbsAccount   ES KBS账号
     * @param esKeytabFile   ES keytab文件
     * @param esKrb5File     ES krb5文件
     * @param esJaasFile     ES jaas文件
     * @return
     */
    BaseResponse<TestConnectResponseVo> testConnectES(String esIpPort, String esUserName, String esUserPassword, String esKbsAccount,
                                                      String esKeytabFile, String esKrb5File, String esJaasFile);

    /**
     * 测试连接Hive
     *
     * @return
     */
    BaseResponse<TestConnectResponseVo> testConnectHive(String ip, Integer port, String userName, String password, String keytabFile,
                                                        String krb5File, String jaasConfPath, Integer kbsEnable, String jdbcUrl) throws IOException;

    /**
     * 测试连接CH
     *
     * @param chIp           CH IP
     * @param chPort         CH port
     * @param chUserName     CH 用户名
     * @param chUserPassword CH 密码
     * @return
     */
    BaseResponse<TestConnectResponseVo> testConnectCH(String chIp, Integer chPort, String chUserName, String chUserPassword,
                                                      Integer chIsSsl);

    /**
     * 测试连接Mysql
     *
     * @return
     */
    BaseResponse<TestConnectResponseVo> testConnectMysql(String ip, Integer port, String userName, String password);

    /**
     * 测试连接PanWei
     *
     * @param ip
     * @param port
     * @param userName
     * @param password
     * @return
     */
    BaseResponse<TestConnectResponseVo> testConnectPanWei(String ip, Integer port, String userName, String password, String dbName,
                                                          String schemaName);

    /**
     * 测试连接Kafka
     * @param kafkaBrokers
     * @param kafkaUserName
     * @param kafkaUserPassword
     * @param keytabFile
     * @param krb5File
     * @param principal
     * @return
     */
    BaseResponse<TestConnectResponseVo> testConnectKafka(String kafkaBrokers, String kafkaUserName, String kafkaUserPassword, String keytabFile, String krb5File, String principal);
    /**
     * 新增或修改元数据
     *
     * @param requestDto
     * @return
     */
    BaseResponse<String> addOrUpdate(AddOrUpdateElementRequestDto requestDto);

    /**
     * 启停Job
     *
     * @param elementId
     * @param state     1启动，2暂停
     * @return
     */
    BaseResponse<String> pauseOrResumeJon(Long elementId, Integer state);

    /**
     * 删除元数据
     *
     * @param elementId
     * @return
     */
    BaseResponse<String> remove(Long elementId);

    /**
     * 执行扫描
     *
     * @param elementId   元数据ID
     * @param executeType 执行方式，2立即执行，3指定时间
     * @param dateTime    当时为指定时间时需传，格式 yyyy-MM-dd HH:mm:ss
     * @return
     */
    BaseResponse<String> execute(Long elementId, Integer executeType, String dateTime);

    /**
     * 状态统计
     *
     * @return
     */
    BaseResponse<ElementCountVo> countVo(String elementType);

    /**
     * 分页查询
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<ElementPageResponseDto>> queryPage(ElementPageRequestDto requestDto);

    /**
     * 查看元数据
     *
     * @param elementId
     * @return
     */
    BaseResponse<QuaWabElementExt> viewById(Long elementId);

    //TODO 抽取详情

}
