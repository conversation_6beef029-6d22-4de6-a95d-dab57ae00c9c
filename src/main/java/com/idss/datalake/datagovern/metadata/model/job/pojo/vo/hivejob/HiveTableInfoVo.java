package com.idss.datalake.datagovern.metadata.model.job.pojo.vo.hivejob;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.idss.datalake.datagovern.config.entity.QuaWebTableBaseInfo;
import com.idss.datalake.datagovern.config.entity.QuaWebTableModel;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.TableExtRequestDto;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: xiexiaofei
 * @Date: 23/6/2021 13:46
 * @Description:
 */
@Data
@ApiModel("Hive表信息")
public class HiveTableInfoVo {
    @ApiModelProperty("中文名")
    private String bussTableNameCn;
    @ApiModelProperty("业务描述")
    private String bussTableDscribe;
    @ApiModelProperty("责任人")
    private String bussTableOwner;
    @ApiModelProperty("是否涉敏")
    private Integer bussIsSensitive;

    @ApiModelProperty("实体ID")
    private Long detailId;
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime detailCreateTime;
    @ApiModelProperty("创建用户")
    private String detailCreateUser;
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime detailUpdateTime;
    @ApiModelProperty("更新用户")
    private String detailUpdateUser;
    @ApiModelProperty("是否可用")
    private Integer detailIsAvailable;
    @ApiModelProperty("最早快照版本号")
    private String detailFirstSnapshootVersion;
    @ApiModelProperty("最新快照版本号")
    private String detailLastSnapshootVersion;
    @ApiModelProperty("维表属性-表名")
    private String detailTableName;
    @ApiModelProperty("节点IP")
    private String hiveIp;
    @ApiModelProperty("端口")
    private Integer hivePort;

    @ApiModelProperty("快照ID")
    private Long snapshootId;
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime snapshootCreateTime;
    @ApiModelProperty("创建用户")
    private String snapshootCreateUser;
    @ApiModelProperty("快照版本")
    private String snapshootVersion;

    private List<TableExtRequestDto> extAttrs;

    /**
     * 元数据标签列表
     */
    private List<DataMetaTag> tagList;

    /**
     * DDL最后变更时间
     */
    private String ddlLastChangeTime;
    /**
     * 数据最后变更时间
     */
    private String dataLastChangeTime;
    /**
     * 最后查看时间
     */
    private String lastViewedTime;

    /**
     * 模型信息
     */
    private QuaWebTableModel tableModel;

    /**
     * 基础信息
     */
    private QuaWebTableBaseInfo tableBaseInfo;

    /**
     * 关键词，["a","b"]
     */
    private String keyWords;
}
