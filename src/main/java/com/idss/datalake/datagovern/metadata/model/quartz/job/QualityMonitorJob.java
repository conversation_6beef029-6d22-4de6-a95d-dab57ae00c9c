package com.idss.datalake.datagovern.metadata.model.quartz.job;

import com.idss.datalake.common.redis.RedissonLockUtil;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorJob;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorTask;
import com.idss.datalake.datagovern.dataquality.enums.TaskStatusEnum;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorTaskService;
import com.idss.datalake.datagovern.metadata.model.element.utils.BtoaEncode;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaJob;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * @Author: xiexiaofei
 * @Date: 16/6/2021 15:19
 * @Description: 定时任务-质量监测
 */
@DisallowConcurrentExecution
@Slf4j
@Component
public class QualityMonitorJob implements Job {
    private static final String LOCK_KEY_PRE = "DATA_LAKE_QUALITY_MONITOR_KEY_";

    @Autowired
    private IQuaMonitorTaskService taskService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        String taskNo = "JOB-" + System.currentTimeMillis();
        JobDataMap jobDataMap = context.getMergedJobDataMap();
        Object job = jobDataMap.get("job");
        try {
            if (job instanceof QuaMonitorJob) {
                QuaMonitorJob monitorJob = (QuaMonitorJob) job;

                String lockKey = LOCK_KEY_PRE + monitorJob.getId();
                try {
                    if (RedissonLockUtil.tryLock(lockKey)) {
                        Thread.sleep(3000L);

                        QuaMonitorTask task = new QuaMonitorTask();
                        task.setJobId(monitorJob.getId());
                        task.setModelId(monitorJob.getModelId());
                        task.setTaskNo(taskNo);
                        task.setJobRules(monitorJob.getJobRules());
                        task.setRuleWeight(monitorJob.getRuleWeight());
                        task.setSampleCnt(monitorJob.getSampleCnt());
                        task.setStartTime(LocalDateTime.now());
                        task.setRunProcess(0.0);
                        task.setStatus(TaskStatusEnum.TODO.getStatus());
                        task.setTenantId(monitorJob.getTenantId());
                        //保存task
                        taskService.save(task);
                    }
                } catch (Exception e) {
                    log.error("DATA_LAKE_QUALITY_MONITOR{}执行失败:", ((QuaJob) job).getJobName(), e);
                } finally {
                    if (RedissonLockUtil.isLocked(lockKey)) {
                        RedissonLockUtil.unlock(lockKey);
                    }
                }
            } else {
                log.info("检测到不含参数的Task触发了");
            }
        } catch (Exception e) {
            log.error("质量监测任务创建失败： ", e);
            throw new RuntimeException("质量监测任务创建失败");
        }
    }
}
