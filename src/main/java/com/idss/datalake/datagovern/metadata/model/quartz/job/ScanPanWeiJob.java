package com.idss.datalake.datagovern.metadata.model.quartz.job;

import com.idss.datalake.common.redis.RedissonLockUtil;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.utils.BtoaEncode;
import com.idss.datalake.datagovern.metadata.model.element.utils.SendScanUtil;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaJob;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaTask;
import com.idss.datalake.datagovern.metadata.model.job.enums.TaskStatusEnum;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaJobService;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.StringBody;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 定时任务发送扫描PanWei请求
 *
 * <AUTHOR>
 * @date 2025/5/20
 * @see
 */
@DisallowConcurrentExecution
@Slf4j
@Component
public class ScanPanWeiJob implements Job {
    private static final String LOCK_KEY_PRE = "DATA_LAKE_SCAN_PANWEI_KEY_";

    private static final String ADD_TASK = "/scan/add";

    @Value("${data.collect.url}")
    private String collectUrl;
    @Autowired
    private QuaTaskService quaTaskService;
    @Autowired
    private QuaJobService quaJobService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap jobDataMap = context.getMergedJobDataMap();
        Object element = jobDataMap.get("element");
        Object job = jobDataMap.get("job");
        try {
            if (element instanceof QuaWabElement && job instanceof QuaJob) {
                QuaWabElement quaWabElement = (QuaWabElement) element;
                QuaJob quaJob = (QuaJob) job;

                String lockKey = LOCK_KEY_PRE + quaJob.getId();
                try {
                    if (RedissonLockUtil.tryLock(lockKey)) {
                        Thread.sleep(3000L);

                        String chIp = quaWabElement.getChIp();
                        Integer chPort = quaWabElement.getChPort();
                        String chUserName = quaWabElement.getChUserName();
                        String chUserPassword = BtoaEncode.decrypt(quaWabElement.getChUserPassword());
                        String dbName = quaWabElement.getDbName();
                        String schemaName = quaWabElement.getSchemaName();

                        String taskNo = ElementTypeEnum.PANWEI.getCode() + "-" + quaJob.getId() + "-" + System.currentTimeMillis();
                        QuaTask task = new QuaTask();
                        task.setJobId(quaJob.getId());
                        task.setTaskNo(taskNo);
                        task.setStartTime(LocalDateTime.now());
                        task.setTaskProgress("0%");
                        task.setStatus(TaskStatusEnum.RUNNING.getStatus());
                        task.setTenantId(quaJob.getTenantId());
                        task.setElementId(quaWabElement.getId());
                        //保存task
                        quaTaskService.save(task);

                        Map<String, ContentBody> reqParam = new HashMap<>();
                        reqParam.put("scanType", new StringBody(ElementTypeEnum.PANWEI.getCode(), ContentType.MULTIPART_FORM_DATA));
                        reqParam.put("taskNo", new StringBody(taskNo, ContentType.MULTIPART_FORM_DATA));
                        reqParam.put("chIp", new StringBody(chIp, ContentType.MULTIPART_FORM_DATA));
                        reqParam.put("chPort", new StringBody(String.valueOf(chPort), ContentType.MULTIPART_FORM_DATA));
                        if (StringUtils.isNotEmpty(chUserName)) {
                            reqParam.put("chUserName", new StringBody(chUserName, ContentType.MULTIPART_FORM_DATA));
                        }
                        if (StringUtils.isNotEmpty(chUserPassword)) {
                            reqParam.put("chUserPassword", new StringBody(chUserPassword, ContentType.MULTIPART_FORM_DATA));
                        }
                        if (StringUtils.isNotEmpty(dbName)) {
                            reqParam.put("dbName", new StringBody(dbName, ContentType.MULTIPART_FORM_DATA));
                        }
                        if (StringUtils.isNotEmpty(schemaName)) {
                            reqParam.put("schemaName", new StringBody(schemaName, ContentType.MULTIPART_FORM_DATA));
                        }
                        log.info("发送扫描PanWei任务： {}",
                                "taskNo：".concat(taskNo).concat(", ip:").concat(chIp).concat(", port:").concat(String.valueOf(chPort)));
                        String addScanUrl = collectUrl + ADD_TASK;
                        SendScanUtil.scan(addScanUrl, reqParam);
                    }
                } catch (Exception e) {
                    log.error("DATA_LAKE_SCAN_PANWEI-{}执行失败:", ((QuaJob) job).getJobName(), e);
                } finally {
                    if (RedissonLockUtil.isLocked(lockKey)) {
                        RedissonLockUtil.unlock(lockKey);
                    }
                }
            } else {
                log.info("检测到不含参数的Task触发了");
            }
        } catch (Exception e) {
            log.error("发送扫描PanWei任务失败： ", e);
            throw new RuntimeException("发送扫描PanWei任务失败");
        }
    }
} 