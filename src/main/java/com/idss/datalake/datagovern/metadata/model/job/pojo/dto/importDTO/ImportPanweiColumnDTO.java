package com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("磐维元数据导入DTO")
public class ImportPanweiColumnDTO {
    @NotNull(message = "资源ID 不可为空")
    private Long elementId;
    @NotEmpty(message = "快照版本 不可为空")
    private String snapshootVersion;
    @NotNull(message = "库ID 不可为空")
    private Long dbId;
    @NotEmpty(message = "库名称 不可为空")
    private String dbName;
    @NotNull(message = "表ID 不可为空")
    private Long tableId;
    @NotEmpty(message = "表名称 不可为空")
    private String tableName;

    /**
     * 上传的csv文件位置
     */
    @NotEmpty(message = "文件 不可为空")
    private String filePath;

    @Data
    @ApiModel("磐维列信息")
    public static class ColumnInfo {
        @ApiModelProperty(value = "schema名称")
        private String schemaName;

        @ApiModelProperty(value = "字段名称")
        private String columnName;

        @ApiModelProperty(value = "字段中文名")
        private String columnNameCn;

        @ApiModelProperty(value = "是否必填")
        private String isRequiredStr;

        @ApiModelProperty(value = "中文描述")
        private String cnDesc;

        @ApiModelProperty(value = "枚举值")
        private String enumValue;

        @ApiModelProperty(value = "映射字段")
        private String mappingFields;

        @ApiModelProperty(value = "是否为敏感表")
        private String isSensitiveStr;

        @ApiModelProperty(value = "是否加密")
        private String isEncryptedStr;

        @ApiModelProperty(value = "是否可用")
        private String isAvailableStr;

        @ApiModelProperty("列类型")
        private String columnType;

        @ApiModelProperty("列长度")
        private Integer columnLength;

        @ApiModelProperty("是否不可为空(1=不可为空,0=可为空)")
        private String isNullableStr;

        @ApiModelProperty("默认值")
        private String defaultValue;

        @ApiModelProperty("列注释")
        private String columnComment;

        @ApiModelProperty(value = "排序字段")
        private Integer sort;
    }

    @Data
    public static class ExportColumnInfo {
        private String columnName;

        private String columnNameCn;

        private Integer isSensitive;

        private Integer isRequired;

        private Integer isEncrypted;

        private Integer isAvailable;

        /**
         * 模式名称
         */
        private String schemaName;

        /**
         * 列数据类型
         */
        private String columnType;

        /**
         * 列长度
         */
        private Integer columnLength;

        /**
         * 是否不可为空(1=不可为空,0=可为空)
         */
        private Integer isNotNull;

        /**
         * 默认值
         */
        private String defaultValue;

        /**
         * 列注释
         */
        private String columnComment;

        /**
         * 中文描述
         */
        private String cnDesc;
        /**
         * 枚举值
         */
        private String enumValue;
        /**
         * 映射字段
         */
        private String mappingFields;

        /**
         * 排序字段
         */
        private Integer sort;
    }
} 