package com.idss.datalake.datagovern.metadata.model.detail.service;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.NodeInfoRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementDbOverview;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeDbPage;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeOverview;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeTablePage;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.NodeInfoVo;

import java.util.List;

/**
 * @Author: xiexiaofei
 * @Date: 24/6/2021 18:44
 * @Description: 节点服务
 */
public interface NodeService {
    /**
     * 获取资源对应的扫描版本
     *
     * @param elementId
     * @return
     */
    BaseResponse<List<String>> getSnapshootVersion(Long elementId);

    /**
     * 获取节点
     *
     * @return
     */
    BaseResponse<List<NodeInfoVo>> getNodeListInfo(NodeInfoRequestDto requestDto, Long tenantId);

    /**
     * 获取节点概览
     *
     * @param requestDto
     * @param tenantId
     * @return
     */
    ElementNodeOverview getNodeOverview(NodeInfoRequestDto requestDto, Long tenantId);

    /**
     * 获取数据库概览
     *
     * @param requestDto
     * @param tenantId
     * @return
     */
    ElementDbOverview getDbOverview(NodeInfoRequestDto requestDto, Long tenantId);

    /**
     * 获取节点数据库分页
     *
     * @param requestDto
     * @return
     */
    Page<ElementNodeDbPage> getNodeDbPage(NodeInfoRequestDto requestDto);

    /**
     * 库的表分页
     *
     * @param requestDto
     * @return
     */
    Page<ElementNodeTablePage> getNodeTablePage(NodeInfoRequestDto requestDto);
}
