package com.idss.datalake.datagovern.metadata.model.kbs.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @Author: xiexiaofei
 * @Date: 16/6/2021 17:05
 * @Description:
 */
@ApiModel("新增或修改KBS认证模板")
@Data
public class AddOrUpdateKbsTemplateRequest {
    @ApiModelProperty(value = "修改时ID不能为空，新增时ID为空")
    private Long id;

    @ApiModelProperty(value = "模板名称")
    @NotEmpty(message = "模板名称不能为空")
    private String templateName;

    @ApiModelProperty(value = "KBS账号")
    @NotEmpty(message = "KBS账号不能为空")
    private String kbsAccount;

    @ApiModelProperty(value = "keytab文件")
    private String keytabFilePath;

    @ApiModelProperty(value = "krb5文件")
    private String krb5FilePath;

    @ApiModelProperty(value = "jaas文件")
    private String jaasFilePath;

    @ApiModelProperty(value = "备注")
    private String remark;

}
