/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadata.model.detail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.HdfsPageVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description <p>hdfs元数据管理</p>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_wab_element_hdfs")
public class QuaWabElementHdfs implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 元数据类型 ：HDFS
     */
    @TableField("element_type")
    private String elementType;

    /**
     * 元数据名称/目录名称
     */
    @TableField("element_name")
    private String elementName;

    /**
     * hdfs目录地址
     */
    @TableField("hdfs_dir")
    private String hdfsDir;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField("create_user")
    private String createUser;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField("update_user")
    private String updateUser;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 是否有效，0无效，1有效，默认1
     */
    @TableField("flag")
    private Boolean flag;

    /**
     * 是否开启kbs认证，0-不开启(默认)，1-开启
     */
    @TableField("kbs_enable")
    private Integer kbsEnable;

    /**
     * ES KBS账号
     */
    @TableField("kbs_account")
    private String kbsAccount;

    /**
     * keytab文件路径
     */
    @TableField("key_tab_path")
    private String keyTabPath;

    /**
     * krb5conf文件路径
     */
    @TableField("krb5_conf_path")
    private String krb5ConfPath;

    /**
     * jass文件路径
     */
    @TableField("jaas_conf_path")
    private String jaasConfPath;

    private String host;
    private Integer port;

    /**
     * 子目录/子文件
     */
    @TableField(exist = false)
    private List<HdfsPageVo> child = new ArrayList<>();
}
