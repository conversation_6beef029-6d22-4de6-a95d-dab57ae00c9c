package com.idss.datalake.datagovern.metadata.model.detail.controller;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.NodeInfoRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeDbPage;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeTablePage;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.NodeInfoVo;
import com.idss.datalake.datagovern.metadata.model.detail.service.NodeService;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.operate.enums.OperateEnum;
import com.idss.datalake.datagovern.metadata.model.operate.util.OperateLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 元数据节点相关接口
 *
 * @Author: xiexiaofei
 * @Date: 25/6/2021 10:00
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping("/detail/node")
public class NodeController {
    @Autowired
    private NodeService nodeService;
    @Autowired
    private QuaWabElementService elementService;

    /**
     * 获取资源对应的扫描版本
     *
     * @param elementId
     * @return
     */
    @GetMapping("/getSnapshootVersion/{elementId}")
    public BaseResponse<List<String>> getSnapshootVersion(@PathVariable("elementId") Long elementId) {
        if (elementId == null) {
            return BaseResponse.error("资源ID不可为空");
        }
        OperateEnum operateEnum = null;
        String logConcatDesc = null;
        QuaWabElement element = elementService.getById(elementId);
        if (ElementTypeEnum.ES.getCode().equals(element.getElementType())) {
            operateEnum = OperateEnum.DETAIL_ES_READ;
            logConcatDesc = element.getElementName() + "," + element.getEsIpPort();
        } else if (ElementTypeEnum.CH.getCode().equals(element.getElementType())) {
            operateEnum = OperateEnum.DETAIL_CH_READ;
            logConcatDesc = element.getElementName() + "," + element.getChIp() + ":" + element.getChPort();
        } else if (ElementTypeEnum.MYSQL.getCode().equals(element.getElementType())) {
            operateEnum = OperateEnum.DETAIL_MYSQL_READ;
            logConcatDesc = element.getElementName() + "," + element.getChIp() + ":" + element.getChPort();
        } else if (ElementTypeEnum.HIVE.getCode().equals(element.getElementType())) {
            operateEnum = OperateEnum.DETAIL_HIVE_READ;
            logConcatDesc = element.getElementName() + "," + element.getChIp() + ":" + element.getChPort();
        } else if (ElementTypeEnum.PANWEI.getCode().equals(element.getElementType())) {
            operateEnum = OperateEnum.DETAIL_PANWEI_READ;
            logConcatDesc = element.getElementName() + "," + element.getChIp() + ":" + element.getChPort();
        } else if (ElementTypeEnum.KAFKA.getCode().equals(element.getElementType())) {
            operateEnum = OperateEnum.DETAIL_KAFKA_READ;
            logConcatDesc = element.getKafkaBrokers();
        }
        try {
            OperateLogUtil.addLog(operateEnum, logConcatDesc, "成功");
            return nodeService.getSnapshootVersion(elementId);
        } catch (Exception e) {
            OperateLogUtil.addLog(operateEnum, logConcatDesc, "失败");
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取节点
     *
     * @return
     */
    @PostMapping("/getNodeListInfo")
    public BaseResponse<List<NodeInfoVo>> getNodeListInfo(@Valid @RequestBody NodeInfoRequestDto requestDto) {
        return nodeService.getNodeListInfo(requestDto, null);
    }

    /**
     * 获取节点概览
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/getNodeOverview")
    public BaseResponse getNodeOverview(@RequestBody NodeInfoRequestDto requestDto) {
        try {
            return BaseResponse.success(nodeService.getNodeOverview(requestDto, null));
        } catch (Exception e) {
            log.error("获取节点概览异常,{}", e.getMessage(), e);
        }
        return BaseResponse.error();
    }

    /**
     * 获取节点数据库分页
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/getNodeDbPage")
    public BaseResponse getNodeDbPage(@RequestBody NodeInfoRequestDto requestDto) {
        try {
            Page<ElementNodeDbPage> page = nodeService.getNodeDbPage(requestDto);
            if (page.isEmpty()) {
                return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), new ArrayList<>());
            }
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
        } catch (Exception e) {
            log.error("获取节点数据库分页异常,{}", e.getMessage(), e);
        }
        return BaseResponse.error();
    }

    /**
     * 获取数据库概览
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/getDbOverview")
    public BaseResponse getDbOverview(@RequestBody NodeInfoRequestDto requestDto) {
        try {
            return BaseResponse.success(nodeService.getDbOverview(requestDto, null));
        } catch (Exception e) {
            log.error("获取节点概览异常,{}", e.getMessage(), e);
        }
        return BaseResponse.error();
    }

    /**
     * 获取数据库表分页
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/getNodeTablePage")
    public BaseResponse getNodeTablePage(@RequestBody NodeInfoRequestDto requestDto) {
        try {
            Page<ElementNodeTablePage> page = nodeService.getNodeTablePage(requestDto);
            if (page.isEmpty()) {
                return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), new ArrayList<>());
            }
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
        } catch (Exception e) {
            log.error("获取获取数据库表分页异常,{}", e.getMessage(), e);
        }
        return BaseResponse.error();
    }
}
