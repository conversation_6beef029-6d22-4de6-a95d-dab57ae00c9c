package com.idss.datalake.datagovern.metadata.model.job.controller;

import com.idss.datalake.datagovern.metadata.model.job.service.ExportService;
import com.idss.datalake.datagovern.metadata.model.operate.util.OperateLogUtil;
import com.idss.datalake.datagovern.metadata.model.operate.enums.OperateEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author: xiexiaofei
 * @Date: 26/6/2021 17:12
 * @Description:
 */
@RestController
@RequestMapping("/export")
public class ExportController {
    @Autowired
    private ExportService exportService;

    @GetMapping("/es/{elementId}/{version}")
    public void exportEs(@PathVariable("elementId") Long elementId, @PathVariable("version") String version, HttpServletResponse response) {
        try {
            exportService.exportEs(elementId, version, response);
            OperateLogUtil.addLog(OperateEnum.DETAIL_ES_EXPORT, "", "成功");
        } catch (Exception e) {
            OperateLogUtil.addLog(OperateEnum.DETAIL_ES_EXPORT, "", "失败");
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/ch/{elementId}/{version}")
    public void exportCh(@PathVariable("elementId") Long elementId, @PathVariable("version") String version, HttpServletResponse response) {
        try {
            exportService.exportCh(elementId, version, response);
            OperateLogUtil.addLog(OperateEnum.DETAIL_CH_EXPORT, "", "成功");
        } catch (Exception e) {
            OperateLogUtil.addLog(OperateEnum.DETAIL_CH_EXPORT, "", "失败");
            throw new RuntimeException(e);
        }
    }

}
