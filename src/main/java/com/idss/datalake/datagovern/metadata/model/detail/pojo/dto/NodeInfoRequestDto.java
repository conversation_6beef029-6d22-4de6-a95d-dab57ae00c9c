package com.idss.datalake.datagovern.metadata.model.detail.pojo.dto;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @Author: xiexiaof<PERSON>
 * @Date: 24/6/2021 17:18
 * @Description:
 */
@Data
@ApiModel("清单树懒加载请求体")
public class NodeInfoRequestDto extends BasePageRequest {
    @ApiModelProperty("加载类型，ES,INDEX,CH,DB,TABLE")
    @NotEmpty(message = "加载类型不能为空")
    private String type;

    @ApiModelProperty("资源ID，加载类型 INDEX，DB，TABLE时必填")
    private Long elementId;

    private Long dbId;

    @ApiModelProperty("版本")
    private String snapshootVersion;

    /**
     * 库名
     */
    private String dbName;

    /**
     * 表个数
     */
    private Long tableCount;

     /**
     * 表名
     */
    private String tableName;

    /**
     * 表中文名
     */
    private String tableNameCn;

     /**
     * 表责任人
     */
    private String tableOwner;
}
