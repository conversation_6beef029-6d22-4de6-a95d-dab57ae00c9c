package com.idss.datalake.datagovern.metadata.model.detail.pojo.dto;

import com.idss.datalake.datagovern.config.entity.QuaWebTableModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: xiexiaofei
 * @Date: 25/6/2021 15:02
 * @Description:
 */
@Data
public class UpdateDetailEsIndexRequestDto {
    @NotNull(message = "id不可为空")
    private Long id;
    private String indexNameCn;
    private String desc;
    private String owner;
    private List<TableExtRequestDto> extAttrs;

    /**
     * 标签id集合
     */
    private List<Long> tagIds;

    /**
     * 模型信息
     */
    private QuaWebTableModel tableModel;

    /**
     * 是否涉敏，0-否，1-是
     */
    private Integer isSensitive;

    /**
     * 关键词，["a","b"]
     */
    private String keyWords;
}
