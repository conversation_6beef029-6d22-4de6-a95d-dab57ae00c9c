package com.idss.datalake.datagovern.metadata.model.job.pojo.dto.importDTO;

import cn.hutool.core.annotation.Alias;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description <p>Clickhouse字段导入dto</p>
 * @date 2023年2月28日
 * @see
 */
@Data
public class ChColumnImportDTO {
    @NotNull(message = "资源ID 不可为空")
    private Long elementId;
    @NotEmpty(message = "快照版本 不可为空")
    private String snapshootVersion;
    @NotNull(message = "库ID 不可为空")
    private Long dbId;
    @NotEmpty(message = "库名称 不可为空")
    private String dbName;
    @NotNull(message = "表ID 不可为空")
    private Long tableId;
    @NotEmpty(message = "表名称 不可为空")
    private String tableName;

    /**
     * 上传的csv文件位置
     */
    @NotEmpty(message = "文件 不可为空")
    private String filePath;

    @Data
    public static class ColumnInfo {
        @Alias(value = "字段名称")
        private String columnName;

        @Alias(value = "字段中文名")
        private String columnNameCn;
        @Alias(value = "是否必填")
        private String isRequiredStr;
        @Alias(value = "中文描述")
        private String cnDesc;
        @Alias(value = "枚举值")
        private String enumValue;
        @Alias(value = "映射字段")
        private String mappingFields;

        @Alias(value = "是否为敏感表")
        private String isSensitiveStr;

        @Alias(value = "是否加密")
        private String isEncryptedStr;

        @Alias(value = "是否可用")
        private String isAvailableStr;

        @Alias(value = "字段类型")
        private String type;

        @Alias(value = "字段描述")
        private String comment;

        @Alias(value = "可否为空")
        private String isNullableStr;

        @Alias(value = "字段长度")
        private Integer columnLength;

        @Alias(value = "字段精度")
        private Integer columnPrecision;

        @Alias(value = "字段序号")
        private Long position;

        @Alias(value = "字段默认值类型")
        private String defaultKind;

        @Alias(value = "字段默认值表达式")
        private String defaultExpression;

        @Alias(value = "数据压缩后字节数")
        private Long dataCompressedBytes;

        @Alias(value = "数据未压缩字节数")
        private Long dataUncompressedBytes;

        @Alias(value = "标记的大小")
        private Long marksBytes;

        @Alias(value = "是否属于分区字段")
        private String isInPartitionKeyStr;

        @Alias(value = "是否属于排序字段")
        private String isInSortingKeyStr;

        @Alias(value = "是否属于主键字段")
        private String isInPrimaryKeyStr;

        @Alias(value = "是否属于抽样字段")
        private String isInSamplingKeyStr;

        @Alias(value = "压缩编码器名称")
        private String compressionCodec;

        @Alias(value = "排序字段")
        private Integer sort;
    }
}
