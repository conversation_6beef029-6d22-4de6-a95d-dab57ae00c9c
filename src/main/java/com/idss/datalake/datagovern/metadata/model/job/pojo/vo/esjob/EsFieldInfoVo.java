package com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob;

import com.baomidou.mybatisplus.annotation.TableField;
import com.idss.datalake.datagovern.datametarelation.entity.DataMetaRelation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: xiexiaofei
 * @Date: 23/6/2021 16:00
 * @Description:
 */
@Data
@ApiModel("Es索引字段信息")
public class EsFieldInfoVo {
    private Long id;

    private Long elementId;

    @ApiModelProperty(value = "索引名称")
    private String indexName;

    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    @ApiModelProperty(value = "字段中文名，默认为空字符串")
    private String fieldNameCn;

    @ApiModelProperty(value = "是否为敏感表，默认不是")
    private Integer isSensitive;

    private LocalDateTime createTime;

    private String createUser;

    private LocalDateTime updateTime;

    private String updateUser;

    @ApiModelProperty(value = "是否可用，默认为1，可用")
    private Integer isAvailable;

    @ApiModelProperty(value = "最早快照版本号，新增记录时写入")
    private String firstSnapshootVersion;

    @ApiModelProperty(value = "最新快照版本号，更新记录时写入")
    private String lastSnapshootVersion;

    @ApiModelProperty("快照ID")
    private Long snapshootId;
    @ApiModelProperty("快照创建时间")
    private LocalDateTime snapshootCreateTime;
    @ApiModelProperty("快照创建用户")
    private String snapshootCreateUser;
    @ApiModelProperty("快照版本")
    private String snapshootVersion;

    @ApiModelProperty(value = "字段数据类型")
    private String fieldDataType;

    @ApiModelProperty(value = "用于文本分析的分析器")
    private String analyzer;

    @ApiModelProperty(value = "相关性得分计算参数")
    private String boost;

    @ApiModelProperty(value = "试图清除脏值以适应字段的数据类型")
    private String coerce;

    @ApiModelProperty(value = "将多个字段的值复制到组字段中，然后可以将组字段作为单个字段进行查询")
    private String copyTo;

    @ApiModelProperty(value = "文档索引时构建的磁盘数据结构")
    private String docValues;

    @ApiModelProperty(value = "对包含新字段的文档进行索引时，动态地添加到文档或文档内部对象中")
    private String dynamic;

    @ApiModelProperty(value = "字段类型使用序号映射来存储文档值，以获得更紧凑的表示")
    private String eagerGlobalOrdinals;

    @ApiModelProperty(value = "可配置跳过对字段内容的解析和索引")
    private String enabled;

    @ApiModelProperty(value = "可配置文本字段在默认情况下用于聚合、排序或脚本编写")
    private String fielddata;

    @ApiModelProperty(value = "以不同的方式为同一个字段建立索引通")
    private String fields;

    @ApiModelProperty(value = "配置识别日期字符串")
    private String format;

    @ApiModelProperty(value = "超过ignore_above设置的字符串将不会被索引或存储")
    private String ignoreAbove;

    @ApiModelProperty(value = "允许忽略异常。不正确的字段不会被索引，但是文档中的其他字段可被正常处理")
    private String ignoreMalformed;

    @ApiModelProperty(value = "控制向反向索引可添加哪些信息以进行搜索和高亮显示")
    private String indexOptions;

    @ApiModelProperty(value = "可配置允许精确短语查询更有效地运行，以牺牲更大的索引为代价")
    private String indexPhrases;

    @ApiModelProperty(value = "可允许对词汇前缀进行索引，从而加快前缀搜索")
    private String indexPrefixes;

    @TableField("\"index\"")
    @ApiModelProperty(value = "index选项控制是否对字段值进行索引，没有索引的字段是不可查询的")
    private String index;

    @ApiModelProperty(value = "附加到字段的元数据，用于共享关于字段的元信息")
    private String meta;

    @ApiModelProperty(value = "自定义规范化器可产生单个token")
    private String normalizer;

    @ApiModelProperty(value = "Norms存储了以后在查询时使用的各种规范化因子，以便计算文档相对于查询的得分")
    private String norms;

    @ApiModelProperty(value = "允许使用指定的值替换显式的空值，这样就可以对它进行索引和搜索")
    private String nullValue;

    @ApiModelProperty(value = "用以防止多个短语查询跨值匹配")
    private String positionIncrementGap;

    @ApiModelProperty(value = "属性可以是任何数据类型，包括对象和嵌套")
    private String properties;

    @ApiModelProperty(value = "查询可配置为使用在字段映射中定义的分析器")
    private String searchAnalyzer;

    @ApiModelProperty(value = "可配置每个字段的评分算法或相似度算法")
    private String similarity;

    @ApiModelProperty(value = "配置存储此字段")
    private String store;

    @ApiModelProperty(value = "配置分析过程产生的terms的信息")
    private String termVector;

    @ApiModelProperty(value = "字段长度，根据字段类型不同而不同，目前支持数值型字段")
    private Integer fieldLength;

    @ApiModelProperty(value = "字段精度，仅针对浮点数值型字段")
    private Integer fieldPrecision;

    private Long businessType;

    private String businessName;

    private Long senLevelId;
    private String senLevelName;
    private Long senTypeId;
    private String senTypeName;
    /**
     * 脱敏规则 ID
     */
    private Integer desensitizationId;

    /**
     * 是否必填，0-否，1-是
     */
    private Integer isRequired;
    /**
     * 是否加密，0-否，1-是
     */
    private Integer isEncrypted;

    /**
     * 中文描述
     */
    private String cnDesc;
    /**
     * 枚举值
     */
    private String enumValue;
    /**
     * 映射字段
     */
    private String mappingFields;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 字段关联关系
     */
    private DataMetaRelation dataMetaRelation;
}
