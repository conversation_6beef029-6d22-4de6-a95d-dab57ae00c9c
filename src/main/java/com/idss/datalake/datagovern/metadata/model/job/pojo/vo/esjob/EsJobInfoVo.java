package com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: xiexiaofei
 * @Date: 23/6/2021 10:12
 * @Description: ES任务详情信息
 */
@Data
@ApiModel("ES任务详情")
public class EsJobInfoVo {
    @ApiModelProperty(value = "元数据ID")
    private Long elementId;
    @ApiModelProperty(value = "元数据名称")
    private String elementName;
    @ApiModelProperty(value = "ES集群地址")
    private String esIpPort;
    @ApiModelProperty(value = "版本")
    private String snapshootVersion;
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty(value = "连通状态 1：连通，2：不连通")
    private Integer isConnect;
    private String isConnectDesc;
    @ApiModelProperty(value = "总索引数")
    private Integer indexCount;
    @ApiModelProperty(value = "ES认证类型：1账密认证，2自上传文件认证，3选择文件认证")
    private Integer esAuthType;
    private String esAuthTypeDesc;
    @ApiModelProperty(value = "抽取时间")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "抽取状态 1执行中，2已完成，有差异，保存扫描结果，3已完成，无差异，不保存扫描结果 4执行异常")
    private Integer status;
    private String statusDesc;
    @ApiModelProperty(value = "总字段数")
    private Integer fieldCount;
}
