/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-05-20
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-05-20
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadata.model.detail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR>
 * @description <p>磐维元素详情表表</p>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_web_panwei_element_detail_table")
public class QuaWebPanweiElementDetailTable implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 元素ID
     */
    @TableField("element_id")
    private Long elementId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 数据库名称
     */
    @TableField("db_name")
    private String dbName;

    /**
     * Schema名称
     */
    @TableField("schema_name")
    private String schemaName;

    /**
     * 表名称
     */
    @TableField("table_name")
    private String tableName;

    /**
     * 表中文名
     */
    @TableField("table_name_cn")
    private String tableNameCn;

    /**
     * 表业务描述
     */
    @TableField("table_dscribe")
    private String tableDscribe;

    /**
     * 表业务负责人
     */
    @TableField("table_owner")
    private String tableOwner;

    /**
     * 关键词，["a","b"]
     */
    @TableField("key_words")
    private String keyWords;

    /**
     * 是否为敏感表，默认不是
     */
    @TableField("is_sensitive")
    private Integer isSensitive;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @TableField("create_user")
    private String createUser;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField("update_user")
    private String updateUser;

    /**
     * 是否可用，默认为1，可用
     */
    @TableField("is_available")
    private Integer isAvailable;

    /**
     * 自定义属性
     */
    @TableField("ext_attrs")
    private String extAttrs;

    /**
     * 最早快照版本号，新增记录时写入
     */
    @TableField("first_snapshoot_version")
    private String firstSnapshootVersion;

    /**
     * 最新快照版本号，更新记录时写入
     */
    @TableField("last_snapshoot_version")
    private String lastSnapshootVersion;

    /**
     * DDL最后变更时间
     */
    @TableField("ddl_last_change_time")
    private String ddlLastChangeTime;

    /**
     * 最后数据变更时间
     */
    @TableField("data_last_change_time")
    private String dataLastChangeTime;

    /**
     * 最后查看时间
     */
    @TableField("last_viewed_time")
    private String lastViewedTime;

    /**
     * 元数据来源，1-自建，2-数据建模
     */
    private Integer metaSource;
}
