package com.idss.datalake.datagovern.metadata.model.operate.util;

import com.idss.datalake.common.util.SpringUtil;
import com.idss.datalake.datagovern.metadata.model.operate.entity.UserOperateLog;
import com.idss.datalake.datagovern.metadata.model.operate.enums.OperateEnum;
import com.idss.datalake.datagovern.metadata.model.operate.service.UserOperateLogService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @Author: xiexiaofei
 * @Date: 29/6/2021 17:17
 * @Description:
 */
@Slf4j
public class OperateLogUtil {
    private static ExecutorService logExecutor = Executors.newFixedThreadPool(32);

    private static UserOperateLogService userOperateLogService = SpringUtil.getBean(UserOperateLogService.class);

    public static void addLog(OperateEnum operateEnum, String concatDesc, String result) {
        UserOperateLog userOperateLog = new UserOperateLog();
        UserValueObject uvo = UmsUtils.getUVO();
        userOperateLog.setTenantId(Long.valueOf(uvo.getTenantId()));
        userOperateLog.setUserName(uvo.getUserName());
        userOperateLog.setRequestIp(uvo.getLoginIp());
        userOperateLog.setCreateUser(uvo.getUserName());
        userOperateLog.setOperateModel(operateEnum.getModel());
        userOperateLog.setOperateType(operateEnum.getType());
        userOperateLog.setOperateDesc(operateEnum.getDesc().concat(concatDesc));
        userOperateLog.setResult(result);
        logExecutor.execute(() -> {
            try {
                userOperateLogService.save(userOperateLog);
            } catch (Exception e) {
                log.error("插入日志失败,", e);
            }
        });
    }

}
