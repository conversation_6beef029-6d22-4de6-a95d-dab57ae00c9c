package com.idss.datalake.datagovern.metadata.model.job.service.impl;

import com.idss.datalake.datagovern.metadata.model.job.entity.QuaScanHiveColumn;
import com.idss.datalake.datagovern.metadata.model.job.mapper.QuaScanHiveColumnMapper;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaScanHiveColumnService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 扫描hive字段结果表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-14
 */
@Service
public class QuaScanHiveColumnServiceImpl extends ServiceImpl<QuaScanHiveColumnMapper, QuaScanHiveColumn> implements IQuaScanHiveColumnService {

}
