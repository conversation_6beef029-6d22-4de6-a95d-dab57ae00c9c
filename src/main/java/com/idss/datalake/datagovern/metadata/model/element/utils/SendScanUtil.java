package com.idss.datalake.datagovern.metadata.model.element.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * @Author: xiexiaofei
 * @Date: 18/6/2021 10:50
 * @Description: 发送扫描工具类
 */
@Slf4j
public class SendScanUtil {
    /**
     * 发送扫描 post请求
     *
     * @param url      地址
     * @param reqParam 参数
     * @return
     */
    public static TestConnectResponseVo scan(String url, Map<String, ContentBody> reqParam) {
        TestConnectResponseVo vo = new TestConnectResponseVo();
        CloseableHttpClient httpclient = HttpClients.createDefault();
        try {
            HttpPost httppost = new HttpPost(url);
            //setConnectTimeout：设置连接超时时间，单位毫秒。
            //setConnectionRequestTimeout：设置从connect Manager获取Connection 超时时间，单位毫秒。这个属性是新加的属性，因为目前版本是可以共享连接池的。
            //setSocketTimeout：请求获取数据的超时时间，单位毫秒。 如果访问一个接口，多少时间内无法返回数据，就直接放弃此次调用。
            RequestConfig defaultRequestConfig = RequestConfig.custom()
                    .setConnectTimeout(5000).
                    setConnectionRequestTimeout(5000)
                    .setSocketTimeout(120000).build();
            httppost.setConfig(defaultRequestConfig);
            log.info("发送请求：{}", httppost.getURI());
            MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
            for (Map.Entry<String, ContentBody> param : reqParam.entrySet()) {
                multipartEntityBuilder.addPart(param.getKey(), param.getValue());
            }
            HttpEntity reqEntity = multipartEntityBuilder.build();
            httppost.setEntity(reqEntity);
            // 执行post请求.
            CloseableHttpResponse response = httpclient.execute(httppost);
            try {
                // 获取响应实体
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    String entityString = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                    log.info("响应体：{}", entityString);
                    return JSONObject.parseObject(entityString, TestConnectResponseVo.class);
                }
            } catch (Exception e) {
                log.error("解析响应体失败：", e);
                vo.setStatus(0);
                vo.setDesc("解析响应体失败: " + e.getMessage());
                return vo;
            } finally {
                response.close();
            }
        } catch (Exception e) {
            log.error("发送请求失败：", e);
            vo.setStatus(0);
            vo.setDesc("发送请求失败: " + e.getMessage());
            return vo;
        } finally {
            // 关闭连接,释放资源
            try {
                httpclient.close();
            } catch (IOException e) {
                log.error("关闭连接失败：", e);
            }
        }
        vo.setStatus(0);
        vo.setDesc("发送请求失败");
        return vo;
    }
}
