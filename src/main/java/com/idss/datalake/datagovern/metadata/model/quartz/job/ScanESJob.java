package com.idss.datalake.datagovern.metadata.model.quartz.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.redis.RedissonLockUtil;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.enums.ESAuthTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.utils.BtoaEncode;
import com.idss.datalake.datagovern.metadata.model.element.utils.SendScanUtil;
import com.idss.datalake.datagovern.metadata.model.element.utils.TestConnectResponseVo;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaJob;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaTask;
import com.idss.datalake.datagovern.metadata.model.job.enums.TaskStatusEnum;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaJobService;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaTaskService;
import com.idss.datalake.datagovern.metadata.model.kbs.entity.KbsFileConfig;
import com.idss.datalake.datagovern.metadata.model.kbs.service.KbsFileConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: xiexiaofei
 * @Date: 16/6/2021 15:19
 * @Description: 定时任务发送扫描ES请求
 */
@DisallowConcurrentExecution
@Slf4j
@Component
public class ScanESJob implements Job {
    private static final String LOCK_KEY_PRE = "DATA_LAKE_SCAN_ES_KEY_";

    private static final String ADD_TASK = "/scan/add";

    @Value("${data.collect.url}")
    private String collectUrl;

    @Autowired
    private QuaTaskService quaTaskService;
    @Autowired
    private QuaJobService quaJobService;
    @Autowired
    private KbsFileConfigService kbsFileConfigService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap jobDataMap = context.getMergedJobDataMap();
        Object element = jobDataMap.get("element");
        Object job = jobDataMap.get("job");
        String addScanUrl = collectUrl + ADD_TASK;
        try {
            if (element instanceof QuaWabElement && job instanceof QuaJob) {
                QuaWabElement quaWabElement = (QuaWabElement) element;
                QuaJob quaJob = (QuaJob) job;

                String lockKey = LOCK_KEY_PRE + quaJob.getId();
                try {
                    if (RedissonLockUtil.tryLock(lockKey)) {
                        Thread.sleep(3000L);


                        String taskNo = ElementTypeEnum.ES.getCode() + "-" +quaJob.getId()+"-"+ System.currentTimeMillis();
                        Integer esAuthType = quaWabElement.getEsAuthType();
                        QuaTask task = new QuaTask();
                        task.setJobId(quaJob.getId());
                        task.setTaskNo(taskNo);
                        task.setStartTime(LocalDateTime.now());
                        task.setTaskProgress("0%");
                        task.setStatus(TaskStatusEnum.RUNNING.getStatus());
                        task.setTenantId(quaJob.getTenantId());
                        task.setElementId(quaWabElement.getId());

                        TestConnectResponseVo vo = null;
                        try {
                            if (ESAuthTypeEnum.ACCOUNT.getCode() == esAuthType) {
                                //账密认证
                                String esIpPort = quaWabElement.getEsIpPort();
                                String esUserName = quaWabElement.getEsUserName();
                                String esUserPassword = BtoaEncode.decrypt(quaWabElement.getEsUserPassword());
                                String esKbsAccount = quaWabElement.getEsKbsAccount();

                                Map<String, ContentBody> reqParam = new HashMap<>();
                                reqParam.put("scanType", new StringBody(ElementTypeEnum.ES.getCode(), ContentType.MULTIPART_FORM_DATA));
                                if(StringUtils.isNotEmpty(taskNo)) {
                                    reqParam.put("taskNo", new StringBody(taskNo, ContentType.MULTIPART_FORM_DATA));
                                }
                                if(StringUtils.isNotEmpty(esIpPort)) {
                                    reqParam.put("esIpPort", new StringBody(esIpPort, ContentType.MULTIPART_FORM_DATA));
                                }
                                if(StringUtils.isNotEmpty(esUserName)) {
                                    reqParam.put("esUserName", new StringBody(esUserName, ContentType.MULTIPART_FORM_DATA));
                                }
                                if (StringUtils.isNotEmpty(esUserPassword)) {
                                    reqParam.put("esUserPassword", new StringBody(esUserPassword, ContentType.MULTIPART_FORM_DATA));
                                }
                                if (StringUtils.isNotEmpty(esKbsAccount)) {
                                    reqParam.put("esKbsAccount", new StringBody(esKbsAccount, ContentType.MULTIPART_FORM_DATA));
                                }

                                log.info("发送扫描ES-账密方式： {}", "taskNo：".concat(taskNo).concat("， esIpPort:").concat(esIpPort));
                                vo = SendScanUtil.scan(addScanUrl, reqParam);
                            } else if (ESAuthTypeEnum.UPLOAD_KBS_FILE.getCode() == esAuthType) {
                                //自上传文件认证
                                String esIpPort = quaWabElement.getEsIpPort();
                                String esUserName = quaWabElement.getEsUserName();
                                String esUserPassword = BtoaEncode.decrypt(quaWabElement.getEsUserPassword());
                                String esKbsAccount = quaWabElement.getEsKbsAccount();
                                String esKeytabFilePath = quaWabElement.getEsKeytabFilePath();
                                String esKrb5FilePath = quaWabElement.getEsKrb5FilePath();
                                String esJaasFilePath = quaWabElement.getEsJaasFilePath();

                                Map<String, ContentBody> reqParam = new HashMap<>();
                                reqParam.put("scanType", new StringBody(ElementTypeEnum.ES.getCode(), ContentType.MULTIPART_FORM_DATA));
                                if (StringUtils.isNotEmpty(taskNo)) {
                                    reqParam.put("taskNo", new StringBody(taskNo, ContentType.MULTIPART_FORM_DATA));
                                }
                                if(StringUtils.isNotEmpty(esIpPort)) {
                                    reqParam.put("esIpPort", new StringBody(esIpPort, ContentType.MULTIPART_FORM_DATA));
                                }
                                if(StringUtils.isNotEmpty(esUserName)) {
                                    reqParam.put("esUserName", new StringBody(esUserName, ContentType.MULTIPART_FORM_DATA));
                                }
                                if (StringUtils.isNotEmpty(esUserPassword)) {
                                    reqParam.put("esUserPassword", new StringBody(esUserPassword, ContentType.MULTIPART_FORM_DATA));
                                }
                                if (StringUtils.isNotEmpty(esKbsAccount)) {
                                    reqParam.put("esKbsAccount", new StringBody(esKbsAccount, ContentType.MULTIPART_FORM_DATA));
                                }

                                if (!StringUtils.isEmpty(esKeytabFilePath)) {
                                    File esKeytab = new File(esKeytabFilePath);
                                    if (esKeytab.exists()) {
                                        reqParam.put("esKeytabFile", new FileBody(esKeytab));
                                    }
                                }
                                if (!StringUtils.isEmpty(esKrb5FilePath)) {
                                    File esKrb5 = new File(esKrb5FilePath);
                                    if (esKrb5.exists()) {
                                        reqParam.put("esKrb5File", new FileBody(esKrb5));
                                    }
                                }
                                if (!StringUtils.isEmpty(esJaasFilePath)) {
                                    File esJaas = new File(esJaasFilePath);
                                    if (esJaas.exists()) {
                                        reqParam.put("esJaasFile", new FileBody(esJaas));
                                    }
                                }
                                log.info("发送扫描ES-自上传文件： {}", "taskNo：".concat(taskNo).concat("， esIpPort:").concat(esIpPort));
                                vo = SendScanUtil.scan(addScanUrl, reqParam);
                            } else if (ESAuthTypeEnum.TEMPLATE.getCode() == esAuthType) {
                                //选择认证模板认证
                                String esIpPort = quaWabElement.getEsIpPort();
                                String esUserName = quaWabElement.getEsUserName();
                                String esUserPassword = BtoaEncode.decrypt(quaWabElement.getEsUserPassword());
                                String esKbsAccount = quaWabElement.getEsKbsAccount();
                                KbsFileConfig kbsFileConfig = kbsFileConfigService.getById(quaWabElement.getEsKbsTemplateId());
                                String keytabFilePath = kbsFileConfig.getKeytabFilePath();
                                String krb5FilePath = kbsFileConfig.getKrb5FilePath();
                                String jaasFilePath = kbsFileConfig.getJaasFilePath();

                                Map<String, ContentBody> reqParam = new HashMap<>();
                                reqParam.put("scanType", new StringBody(ElementTypeEnum.ES.getCode(), ContentType.MULTIPART_FORM_DATA));
                                if(StringUtils.isNotEmpty(taskNo)){
                                    reqParam.put("taskNo", new StringBody(taskNo, ContentType.MULTIPART_FORM_DATA));
                                }
                                if(StringUtils.isNotEmpty(esIpPort)){
                                    reqParam.put("esIpPort", new StringBody(esIpPort, ContentType.MULTIPART_FORM_DATA));
                                }
                                if(StringUtils.isNotEmpty(esUserName)){
                                    reqParam.put("esUserName", new StringBody(esUserName, ContentType.MULTIPART_FORM_DATA));
                                }
                                if (StringUtils.isNotEmpty(esUserPassword)) {
                                    reqParam.put("esUserPassword", new StringBody(esUserPassword, ContentType.MULTIPART_FORM_DATA));
                                }
                                if (StringUtils.isNotEmpty(esKbsAccount)) {
                                    reqParam.put("esKbsAccount", new StringBody(esKbsAccount, ContentType.MULTIPART_FORM_DATA));
                                }

                                if (!StringUtils.isEmpty(keytabFilePath)) {
                                    File esKeytab = new File(keytabFilePath);
                                    if (esKeytab.exists()) {
                                        reqParam.put("esKeytabFile", new FileBody(esKeytab));
                                    }
                                }
                                if (!StringUtils.isEmpty(krb5FilePath)) {
                                    File esKrb5 = new File(krb5FilePath);
                                    if (esKrb5.exists()) {
                                        reqParam.put("esKrb5File", new FileBody(esKrb5));
                                    }
                                }
                                if (!StringUtils.isEmpty(jaasFilePath)) {
                                    File esJaas = new File(jaasFilePath);
                                    if (esJaas.exists()) {
                                        reqParam.put("esJaasFile", new FileBody(esJaas));
                                    }
                                }
                                log.info("发送扫描ES-选择模板认证： {}", "taskNo：".concat(taskNo).concat("， esIpPort:").concat(esIpPort));
                                vo = SendScanUtil.scan(addScanUrl, reqParam);
                            } else {
                                vo = new TestConnectResponseVo();
                                vo.setStatus(0);
                                vo.setDesc("发送扫描ES任务，认证方式有误！");
                            }
                            if (vo.getStatus() == 0) {
                                task.setStatus(TaskStatusEnum.EXCEPTION.getStatus());
                                task.setResult("发送扫描ES任务，认证方式有误！");
                            }
                            quaTaskService.save(task);

                        } catch (Exception e) {
                            log.error("发送扫描ES任务失败： ", e);
                            throw new RuntimeException(e);
                        }
                    }
                } catch (Exception e) {
                    log.error("DATA_LAKE_SCAN_ES{}执行失败:", ((QuaJob) job).getJobName(), e);
                } finally {
                    if (RedissonLockUtil.isLocked(lockKey)) {
                        RedissonLockUtil.unlock(lockKey);
                    }
                }

            } else {
                log.info("检测到不含参数的Task触发了");
            }
        } catch (RuntimeException e) {
            log.error("发送扫描ES任务失败： ", e);
            throw new RuntimeException("发送扫描ES任务失败");
        }
    }
}
