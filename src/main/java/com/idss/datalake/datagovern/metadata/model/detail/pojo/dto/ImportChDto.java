package com.idss.datalake.datagovern.metadata.model.detail.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: xiexiaofei
 * @Date: 28/6/2021 11:44
 * @Description:
 */
@Data
public class ImportChDto {
    @ApiModelProperty(value = "元数据名称")
    private String elementName;
    @ApiModelProperty(value = "库名")
    private String dbName;
    @ApiModelProperty(value = "数据表名称")
    private String tableName;
    @ApiModelProperty(value = "表中文名")
    private String tableNameCn;
    @ApiModelProperty(value = "表业务描述")
    private String tableDscribe;
    @ApiModelProperty(value = "表业务负责人")
    private String tableOwner;
    @ApiModelProperty(value = "字段名称")
    private String columnName;
    @ApiModelProperty(value = "字段中文名，默认为空字符串")
    private String columnNameCn;
    @ApiModelProperty(value = "是否为敏感表，默认不是")
    private Integer isSensitive;
}
