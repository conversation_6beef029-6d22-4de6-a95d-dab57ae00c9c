package com.idss.datalake.datagovern.metadata.model.detail.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采集节点库分页
 *
 * <AUTHOR>
 * @date 2024/11/14
 * @see
 */
@Data
public class ElementNodeDbPage {

    /**
     * 库名
     */
    private String dbName;

    /**
     * 表个数
     */
    private long tableCount;

    /**
     * 已维护表个数
     */
    private long maintenanceTableCount;

    /**
     * 低维护表个数
     */
    private long lowMaintenanceTableCount;

    /**
     * 库平均维护分
     */
    private BigDecimal avgScore = new BigDecimal("0");

}
