package com.idss.datalake.datagovern.metadata.model.job.pojo.vo.scan;

import com.idss.datalake.datagovern.metadata.model.job.entity.KafkaTopic;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaScanHiveDb;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaScanMysqlDb;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaScanPanweiColumn;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaScanPanweiDb;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaScanPanweiTable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: xiexiaofei
 * @Date: 20/6/2021 11:55
 * @Description:
 */
@Data
@ApiModel("扫描结果")
public class ScanResultVo {
    @ApiModelProperty("扫描类型")
    private String scanType;
    @ApiModelProperty("扫描的CH库")
    private List<ChDbDto> chDbList;
    @ApiModelProperty("扫描的ES索引")
    private List<EsIndexDto> esIndexList;
    @ApiModelProperty("扫描的MYSQL库")
    private List<QuaScanMysqlDb> mysqlDbs;
    @ApiModelProperty("扫描的HIVE库")
    private List<QuaScanHiveDb> hiveDbs;
    /**
     * KAFKA
     */
    private List<KafkaTopic> kafkaTopics;

    /**
     * PanWei相关字段
     */
    private List<QuaScanPanweiDb> panWeiDbList;
}
