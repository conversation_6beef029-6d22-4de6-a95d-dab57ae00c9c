package com.idss.datalake.datagovern.metadata.model.job.service.impl;

import com.idss.datalake.datagovern.metadata.model.job.entity.ChTaskResultDb;
import com.idss.datalake.datagovern.metadata.model.job.mapper.ChTaskResultDbMapper;
import com.idss.datalake.datagovern.metadata.model.job.service.ChTaskResultDbService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ch扫描DB记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Service
public class ChTaskResultDbServiceImpl extends ServiceImpl<ChTaskResultDbMapper, ChTaskResultDb> implements ChTaskResultDbService {

}
