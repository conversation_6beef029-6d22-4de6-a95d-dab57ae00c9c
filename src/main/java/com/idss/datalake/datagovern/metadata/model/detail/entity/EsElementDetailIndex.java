package com.idss.datalake.datagovern.metadata.model.detail.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.tools.ant.types.resources.Last;

/**
 * <p>
 * ES清单Index表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_web_es_element_detail_index")
@ApiModel(value = "EsElementDetailIndex对象", description = "ES清单Index表")
public class EsElementDetailIndex extends Model<EsElementDetailIndex> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long elementId;

    @ApiModelProperty(value = "节点IP")
    private String hostAddress;

    @ApiModelProperty(value = "端口")
    private Integer port;

    @ApiModelProperty(value = "索引名称")
    private String indexName;

    @ApiModelProperty(value = "索引中文名")
    private String indexNameCn;

    @ApiModelProperty(value = "索引业务描述")
    private String indexDscribe;

    @ApiModelProperty(value = "索引业务负责人")
    private String indexOwner;

    /**
     * 关键词，["a","b"]
     */
    private String keyWords;

    @ApiModelProperty(value = "类型名称，如有多个，逗号分隔")
    private String typeName;

    @ApiModelProperty(value = "是否为敏感，0非，1是，默认不是")
    private Integer isSensitive;

    private LocalDateTime createTime;

    private String createUser;

    private LocalDateTime updateTime;

    private String updateUser;

    @ApiModelProperty(value = "是否可用，0不可用，默认为1，可用")
    private Integer isAvailable;

    @ApiModelProperty(value = "最早快照版本号，新增记录时写入")
    private String firstSnapshootVersion;

    @ApiModelProperty(value = "最新快照版本号，更新记录时写入")
    private String lastSnapshootVersion;

    private Long tenantId;

    private String extAttrs;

    /**
     * DDL最后变更时间
     */
    private String ddlLastChangeTime;
    /**
     * 数据最后变更时间
     */
    private String dataLastChangeTime;
    /**
     * 最后查看时间
     */
    private String lastViewedTime;

      /**
     * 元数据来源，1-自建，2-数据建模
     */
    private Integer metaSource;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
