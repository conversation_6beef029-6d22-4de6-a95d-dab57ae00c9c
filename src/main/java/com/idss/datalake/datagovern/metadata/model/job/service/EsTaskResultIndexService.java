package com.idss.datalake.datagovern.metadata.model.job.service;

import com.idss.datalake.datagovern.metadata.model.job.entity.EsTaskResultIndex;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * ES扫描index记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
public interface EsTaskResultIndexService extends IService<EsTaskResultIndex> {
    String maxSnapshootVersionByElementId(Long elementId);
}
