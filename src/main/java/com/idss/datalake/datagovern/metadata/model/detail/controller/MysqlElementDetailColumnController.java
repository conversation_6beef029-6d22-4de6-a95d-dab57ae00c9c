package com.idss.datalake.datagovern.metadata.model.detail.controller;


import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.UpdateDetailChColumnBatchRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.service.ChElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebMysqlElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.operate.enums.OperateEnum;
import com.idss.datalake.datagovern.metadata.model.operate.util.OperateLogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * CH清单Column表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@RestController
@RequestMapping("/detail/mysql/column")
public class MysqlElementDetailColumnController {
    @Autowired
    private IQuaWebMysqlElementDetailColumnService detailColumnService;
    @Autowired
    private QuaWabElementService elementService;

    @PostMapping("/update")
    public BaseResponse<String> updateDetail(@RequestBody @Valid UpdateDetailChColumnBatchRequestDto requestDto) {
        OperateEnum operateEnum = null;
        String logConcatDesc = null;
        QuaWebMysqlElementDetailColumn column = detailColumnService.getById(requestDto.getBatchList().get(0).getId());
        QuaWabElement element = elementService.getById(column.getElementId());
        try {
            operateEnum = OperateEnum.DETAIL_MYSQL_UPDATE;
            logConcatDesc = element.getElementName() + "," + element.getChIp() + ":" + element.getChPort();
            OperateLogUtil.addLog(operateEnum, logConcatDesc, "成功");

            return detailColumnService.updateDetail(requestDto);
        } catch (Exception e) {
            OperateLogUtil.addLog(operateEnum, logConcatDesc, "失败");
            throw new RuntimeException(e);
        }
    }
}
