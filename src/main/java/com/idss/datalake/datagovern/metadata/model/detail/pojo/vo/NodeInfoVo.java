package com.idss.datalake.datagovern.metadata.model.detail.pojo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: x<PERSON><PERSON><PERSON><PERSON>
 * @Date: 24/6/2021 13:32
 * @Description:
 */
@Data
@ApiModel("懒加载，清单树")
public class NodeInfoVo {
    @ApiModelProperty("加载类型，ES,INDEX,CH,DB,TABLE")
    private String type;

    private Long elementId;
    private String elementName;
    private String ipPorts;

    private Long indexId;
    private String indexName;

    private Long dbId;
    private String dbName;

    private Long tableId;
    private String tableName;

    private String label;
    @ApiModelProperty("统计个数")
    private Integer cnt;
    
    @ApiModelProperty("Kafka topic ID")
    private Long topicId;
    
    @ApiModelProperty("Kafka topic名称")
    private String topicName;
}
