/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadata.model.detail.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.model.detail.manager.QuaWebHdfsElementDetailFileManager;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.QuaWebHdfsElementDetailFileDTO;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * hdfs清单文件表
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@RestController
@RequestMapping("/detail/hdfs/file")
public class QuaWebHdfsElementDetailFileController {
    private static final Logger logger = LoggerFactory.getLogger(QuaWebHdfsElementDetailFileController.class);
    @Autowired
    private QuaWebHdfsElementDetailFileManager quaWebHdfsElementDetailFileManager;

    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody QuaWebHdfsElementDetailFileDTO dto) {
        try {
            quaWebHdfsElementDetailFileManager.create(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public ResultBean delete(@RequestBody QuaWebHdfsElementDetailFileDTO dto) {
        try {
            quaWebHdfsElementDetailFileManager.delete(dto.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Long id) {
        try {
            return ResultBean.success(quaWebHdfsElementDetailFileManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    @ApiOperation(value = "编辑")
    @PostMapping(value = "/edit")
    public ResultBean edit(@RequestBody QuaWebHdfsElementDetailFileDTO dto) {
        try {
            quaWebHdfsElementDetailFileManager.edit(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }
}
