package com.idss.datalake.datagovern.metadata.model.detail.utils;

/**
 * @Author: x<PERSON><PERSON><PERSON><PERSON>
 * @Date: 28/6/2021 09:57
 * @Description:
 */
public class ImportUtil {
    public static final String[] IMPORT_ES_HEADER = {
            "集群名称",
            "索引名称",
            "索引中文名",
            "索引业务描述",
            "索引责任人",
            "字段名称",
            "字段中文名",
            "是否涉敏"
    };

    public static final String[] IMPORT_CH_HEADER = {
            "CH库实例名称",
            "数据库名称",
            "数据表-名称",
            "数据表-中文",
            "数据表-业务描述",
            "数据表-责任人",
            "字段-名称",
            "字段-中文名",
            "字段-是否涉敏",
    };
}
