package com.idss.datalake.datagovern.metadata.model.element.pojo.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 元数据管理
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Data
public class QuaWabElementExt extends QuaWabElement {
    private String keytabFilePathDesc;

    private String krb5FilePathDesc;

    private String jaasFilePathDesc;

    @ApiModelProperty(value = "es认证方式描述")
    private String esAuthTypeDec;

    @ApiModelProperty(value = "es选择的模板名称")
    private String esKbsTemplateName;
}
