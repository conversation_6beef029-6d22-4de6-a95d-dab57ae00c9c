package com.idss.datalake.datagovern.metadata.model.job.service;

import com.idss.datalake.datagovern.metadata.model.job.entity.QuaWebMysqlTaskResultColumn;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * mysql扫描Column记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
public interface IQuaWebMysqlTaskResultColumnService extends IService<QuaWebMysqlTaskResultColumn> {
    String maxSnapshootVersionByElementId(Long elementId);
}
