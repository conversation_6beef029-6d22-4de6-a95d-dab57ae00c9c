package com.idss.datalake.datagovern.metadata.model.detail.service;

import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailTable;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.UpdateDetailChTableRequestDto;

/**
 * <p>
 * mysql清单Table表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
public interface IQuaWebMysqlElementDetailTableService extends IService<QuaWebMysqlElementDetailTable> {

    BaseResponse<String> updateDetail(UpdateDetailChTableRequestDto requestDto);
}
