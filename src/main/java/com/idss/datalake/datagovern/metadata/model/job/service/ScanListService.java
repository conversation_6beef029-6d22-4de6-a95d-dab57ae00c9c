package com.idss.datalake.datagovern.metadata.model.job.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.metadata.model.job.entity.ScanList;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.scan.ScanResultVo;

/**
 * <p>
 * 扫描任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
public interface ScanListService extends IService<ScanList> {
    /**
     * 获取扫描结果
     * @param taskNo
     * @return
     */
    ScanResultVo getScanResult(String taskNo) throws Exception;
}
