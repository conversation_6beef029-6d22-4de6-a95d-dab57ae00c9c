package com.idss.datalake.datagovern.metadata.model.detail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.datagovern.dictionary.dto.DataDictionaryItemDTO;
import com.idss.datalake.datagovern.dictionary.enums.ItemTypeEnum;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryItemService;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailField;
import com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailIndex;
import com.idss.datalake.datagovern.metadata.model.detail.mapper.EsElementDetailFieldMapper;
import com.idss.datalake.datagovern.metadata.model.detail.mapper.EsElementDetailIndexMapper;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.UpdateDetailEsFieldBatchRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.UpdateDetailEsFieldDto;
import com.idss.datalake.datagovern.metadata.model.detail.service.EsElementDetailFieldService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * ES清单Field表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Service
@Slf4j
public class EsElementDetailFieldServiceImpl extends ServiceImpl<EsElementDetailFieldMapper, EsElementDetailField> implements EsElementDetailFieldService {
    @Resource
    private EsElementDetailIndexMapper detailIndexMapper;
    @Autowired
    private IDataDictionaryItemService dataDictionaryItemService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public BaseResponse<String> updateDetail(UpdateDetailEsFieldBatchRequestDto requestDto) {
        try {
            List<UpdateDetailEsFieldDto> batchList = requestDto.getBatchList();
            List<Long> fieldIds = batchList.stream().map(UpdateDetailEsFieldDto::getId).collect(Collectors.toList());
            Collection<EsElementDetailField> fields = listByIds(fieldIds);
            loop:
            for (EsElementDetailField field : fields) {
                for (UpdateDetailEsFieldDto dto : batchList) {
                    if (dto.getId().longValue() == field.getId().longValue()) {
                        field.setFieldNameCn(dto.getFieldCnName());
                        field.setIsSensitive(dto.getIsSen());
                        field.setBusinessType(dto.getBusinessType());
                        field.setSenLevelId(dto.getSenLevelId());
                        field.setSenLevelName(dto.getSenLevelName());
                        field.setSenTypeId(dto.getSenTypeId());
                        field.setSenTypeName(dto.getSenTypeName());
                        field.setDesensitizationId(dto.getDesensitizationId());
                        field.setIsRequired(dto.getIsRequired());
                        field.setIsEncrypted(dto.getIsEncrypted());
                        field.setCnDesc(dto.getCnDesc());
                        field.setEnumValue(dto.getEnumValue());
                        field.setMappingFields(dto.getMappingFields());
                        field.setSort(dto.getSort());
                        continue loop;
                    }
                }
            }
            this.updateBatchById(fields);
            //判断是否有涉敏了
            EsElementDetailField one = getById(requestDto.getBatchList().get(0).getId());
            List<EsElementDetailField> allFields = list(new QueryWrapper<EsElementDetailField>().eq("element_id", one.getElementId()).eq(
                    "index_name", one.getIndexName()));
            Integer sum = allFields.stream().map(f -> f.getIsSensitive() == null ? 0 : f.getIsSensitive()).reduce(Integer::sum).orElse(0);
            //更改索引涉敏
            EsElementDetailIndex detailIndex = detailIndexMapper.selectOne(
                    new QueryWrapper<EsElementDetailIndex>()
                            .eq("element_id", one.getElementId())
                            .eq("index_name", one.getIndexName()));
            if (sum > 0) {
                detailIndex.setIsSensitive(1);
            } else {
                detailIndex.setIsSensitive(0);
            }
            detailIndexMapper.updateById(detailIndex);

            // 更新数据字典项
            for (EsElementDetailField field : fields) {
                DataDictionaryItemDTO itemDTO = new DataDictionaryItemDTO();
                itemDTO.setDatasourceType(DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName());
                itemDTO.setItemType(ItemTypeEnum.field.name());
                itemDTO.setElementId(field.getElementId());
                itemDTO.setTableName(field.getIndexName());
                itemDTO.setItemName(field.getFieldName());
                itemDTO.setItemId(field.getId());
                itemDTO.setColumnNameCn(field.getFieldNameCn());
                itemDTO.setIsSensitive(field.getIsSensitive());
                itemDTO.setSenLevelId(field.getSenLevelId());
                itemDTO.setSenLevelName(field.getSenLevelName());
                itemDTO.setSenTypeId(field.getSenTypeId());
                itemDTO.setSenTypeName(field.getSenTypeName());
                itemDTO.setDesensitizationId(field.getDesensitizationId());
                itemDTO.setIsRequired(field.getIsRequired());
                itemDTO.setIsEncrypted(field.getIsEncrypted());
                itemDTO.setCnDesc(field.getCnDesc());
                itemDTO.setEnumValue(field.getEnumValue());
                itemDTO.setMappingFields(field.getMappingFields());
                itemDTO.setSort(field.getSort());
                dataDictionaryItemService.updateItemFromMeta(itemDTO);
            }

            return BaseResponse.success();
        } catch (Exception e) {
            log.error("批量更新清单索引字段失败：{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
