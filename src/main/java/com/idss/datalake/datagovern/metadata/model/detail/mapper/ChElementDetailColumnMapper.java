package com.idss.datalake.datagovern.metadata.model.detail.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailColumn;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.ElementDetailPageRequestDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementDetailVo;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.ChJobColumnRequestDto;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.chjob.ChColumnInfoVo;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * CH清单Column表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
public interface ChElementDetailColumnMapper extends BaseMapper<ChElementDetailColumn> {

    Page<ElementDetailVo> queryDetailColumnPage(ElementDetailPageRequestDto requestDto);
}
