package com.idss.datalake.datagovern.metadata.model.job.pojo.vo.scan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("扫描CH库结果表")
public class ChDbDto {
    @ApiModelProperty(value = "数据库名称")
    private String dbName;
    @ApiModelProperty(value = "快照版本")
    private String snapshootVersion;
    @ApiModelProperty(value = "包含的CH表")
    private List<ChTableDto> chTableDtoList;
}
