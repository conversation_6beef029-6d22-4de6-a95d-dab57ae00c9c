/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-01-06
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadata.model.detail.pojo.dto;

import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHdfsElementDetailFile;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>hdfs清单文件表 dto类</p>
 * @since 2025-01-06
 */
@Data
public class QuaWebHdfsElementDetailFileDTO extends QuaWebHdfsElementDetailFile {
    private List<Long> ids;
}