package com.idss.datalake.datagovern.metadata.model.detail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.util.ExcelUtil;
import com.idss.datalake.datagovern.metadata.model.detail.entity.*;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.ImportChDto;
import com.idss.datalake.datagovern.metadata.model.detail.pojo.dto.ImportEsDto;
import com.idss.datalake.datagovern.metadata.model.detail.service.*;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.radar.util.UmsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: xiexiaofei
 * @Date: 28/6/2021 11:36
 * @Description:
 */
@Service
@Slf4j
public class ImportDataServiceImpl implements ImportDataService {
    @Autowired
    private QuaWabElementService quaWabElementService;
    @Autowired
    private EsElementDetailIndexService detailIndexService;
    @Autowired
    private EsElementDetailFieldService detailFieldService;
    @Autowired
    private ChElementDetailDbService detailDbService;
    @Autowired
    private ChElementDetailTableService detailTableService;
    @Autowired
    private ChElementDetailColumnService detailColumnService;


    @Transactional(rollbackFor = RuntimeException.class, isolation = Isolation.REPEATABLE_READ, propagation = Propagation.REQUIRED)
    @Override
    public void importData(String filePath, String type) {
        long start = System.currentTimeMillis();
        File file = new File(filePath);
        if (!file.exists()) {
            throw new RuntimeException("文件不存在");
        }
        try {
            List<String[]> dataList = ExcelUtil.readExcel(file, 0, type);
            if (ElementTypeEnum.ES.getCode().equals(type)) {
                importEs(dataList);
            } else if (ElementTypeEnum.CH.getCode().equals(type)) {
                importCh(dataList);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        long end = System.currentTimeMillis();
        log.info("导入耗时： {}", (end - start));

    }

    /**
     * 导入ES
     *
     * @param dataList
     */
    private void importEs(List<String[]> dataList) throws Exception {
        int tenantId = UmsUtils.getUVO().getTenantId();
        List<ImportEsDto> importEsDtoList = new ArrayList<>();
        for (String[] data : dataList) {
            ImportEsDto esDto = new ImportEsDto();
            esDto.setElementName(data[0]);
            esDto.setIndexName(data[1]);
            esDto.setIndexNameCn(data[2]);
            esDto.setIndexDscribe(data[3]);
            esDto.setIndexOwner(data[4]);
            esDto.setFieldName(data[5]);
            esDto.setFieldNameCn(data[6]);
            esDto.setIsSensitive("是".equals(data[7]) ? 1 : 0);
            importEsDtoList.add(esDto);
        }
        //根据集群名称分组
        Map<String, List<ImportEsDto>> collect = importEsDtoList.stream().collect(Collectors.groupingBy(ImportEsDto::getElementName));
        collect.forEach((eleName, esDtos) -> {
            List<QuaWabElement> elements = quaWabElementService.list(
                    new QueryWrapper<QuaWabElement>()
                            .eq("tenant_id", tenantId)
                            .eq("flag", 1)
                            .eq("element_name", eleName));
            //集群存在
            if (elements != null && elements.size() > 0) {
                QuaWabElement element = elements.get(0);
                //需要更新的索引与字段数据
                List<EsElementDetailIndex> updateBatchIndex = new ArrayList<>();
                List<EsElementDetailField> updateBatchField = new ArrayList<>();

                //查询库中存在的索引和字段数据
                List<EsElementDetailIndex> indices = detailIndexService.list(
                        new QueryWrapper<EsElementDetailIndex>().eq("element_id", element.getId()));
                List<EsElementDetailField> fields = detailFieldService.list(
                        new QueryWrapper<EsElementDetailField>().eq("element_id", element.getId()));

                //按索引分组
                Map<String, List<ImportEsDto>> indexMap = esDtos.stream().collect(Collectors.groupingBy(ImportEsDto::getIndexName));
                indexMap.forEach((indexName, indexDtos) -> {
                    //处理单个索引
                    ImportEsDto indexDto = indexDtos.get(0);
                    EsElementDetailIndex detailIndex;
                    List<EsElementDetailIndex> detailIndices = indices.stream().filter(index -> index.getIndexName().equals(indexName)).collect(Collectors.toList());
                    if (detailIndices.size() > 0) {
                        //存在的索引，更新索引描述数据
                        detailIndex = detailIndices.get(0);
                        detailIndex.setIndexDscribe(indexDto.getIndexDscribe());
                        detailIndex.setIndexNameCn(indexDto.getIndexNameCn());
                        detailIndex.setIndexOwner(indexDto.getIndexOwner());
                        updateBatchIndex.add(detailIndex);
                    }
                    //迭代索引下的字段
                    for (ImportEsDto filedDto : indexDtos) {
                        EsElementDetailField detailField;
                        List<EsElementDetailField> detailFields = fields.stream().filter(f -> f.getIndexName().equals(indexName) && f.getFieldName().equals(filedDto.getFieldName())).collect(Collectors.toList());
                        if (detailFields.size() > 0) {
                            //存在的字段，更新字段描述数据
                            detailField = detailFields.get(0);
                            detailField.setFieldNameCn(filedDto.getFieldNameCn());
                            detailField.setIsSensitive(filedDto.getIsSensitive());
                            updateBatchField.add(detailField);
                        }
                    }
                });
                //批量处理
                if (updateBatchField.size() > 0) {
                    detailFieldService.updateBatchById(updateBatchField);
                }
                if (updateBatchIndex.size() > 0) {
                    for (EsElementDetailIndex batchIndex : updateBatchIndex) {
                        List<EsElementDetailField> detailFields = detailFieldService.list(new QueryWrapper<EsElementDetailField>().eq("element_id", batchIndex.getElementId()).eq("index_name", batchIndex.getIndexName()));
                        //找到字段涉敏的就更改索引涉敏
                        if (detailFields.stream().anyMatch(field -> field.getIsSensitive() == 1)) {
                            batchIndex.setIsSensitive(1);
                        } else {
                            // 未找到字段涉敏，更改索引非涉敏
                            batchIndex.setIsSensitive(0);
                        }
                    }
                    detailIndexService.updateBatchById(updateBatchIndex);
                }
            }
        });
    }

    /**
     * 导入CH
     *
     * @param dataList
     */
    private void importCh(List<String[]> dataList) throws Exception {
        int tenantId = UmsUtils.getUVO().getTenantId();
        List<ImportChDto> importChDtoList = new ArrayList<>();
        for (String[] data : dataList) {
            ImportChDto chDto = new ImportChDto();
            chDto.setElementName(data[0]);
            chDto.setDbName(data[1]);
            chDto.setTableName(data[2]);
            chDto.setTableNameCn(data[3]);
            chDto.setTableDscribe(data[4]);
            chDto.setTableOwner(data[5]);
            chDto.setColumnName(data[6]);
            chDto.setColumnNameCn(data[7]);
            chDto.setIsSensitive("是".equals(data[8]) ? 1 : 0);
            importChDtoList.add(chDto);
        }

        //根据集群名称分组
        Map<String, List<ImportChDto>> collect = importChDtoList.stream().collect(Collectors.groupingBy(ImportChDto::getElementName));
        collect.forEach((eleName, chDtos) -> {
            List<QuaWabElement> elements = quaWabElementService.list(new QueryWrapper<QuaWabElement>().eq("tenant_id", tenantId).eq("flag", 1).eq("element_name", eleName));
            //集群存在
            if (elements != null && elements.size() > 0) {
                QuaWabElement element = elements.get(0);
                //需要更新的表和字段数据
                List<ChElementDetailTable> updateBatchTable = new ArrayList<>();
                List<ChElementDetailColumn> updateBatchColumn = new ArrayList<>();

                //查询库中存在的库、表、字段数据
                List<ChElementDetailDb> dbs = detailDbService.list(new QueryWrapper<ChElementDetailDb>().eq("element_id", element.getId()));
                List<ChElementDetailTable> tables = detailTableService.list(new QueryWrapper<ChElementDetailTable>().eq("element_id", element.getId()));
                List<ChElementDetailColumn> columns = detailColumnService.list(new QueryWrapper<ChElementDetailColumn>().eq("element_id", element.getId()));

                //按DB分组
                Map<String, List<ImportChDto>> dbMap = chDtos.stream().collect(Collectors.groupingBy(ImportChDto::getDbName));
                dbMap.forEach((dbName, dbDtos) -> {
                    List<ChElementDetailDb> detailDbs = dbs.stream().filter(db -> db.getDbName().equals(dbName)).collect(Collectors.toList());
                    //存在的库
                    if (detailDbs.size() > 0) {
                        //按表分组
                        Map<String, List<ImportChDto>> tableMap = dbDtos.stream().collect(Collectors.groupingBy(ImportChDto::getTableName));
                        tableMap.forEach((tableName, tableDtos) -> {
                            //处理表
                            ImportChDto tableDto = tableDtos.get(0);
                            ChElementDetailTable detailTable;
                            List<ChElementDetailTable> detailTables = tables.stream().filter(table -> table.getDbName().equals(dbName) && table.getTableName().equals(tableName)).collect(Collectors.toList());
                            //存在重复的表
                            if (detailTables.size() > 0) {
                                //存在的表，更新表描述数据
                                detailTable = detailTables.get(0);
                                detailTable.setTableNameCn(tableDto.getTableNameCn());
                                detailTable.setTableDscribe(tableDto.getTableDscribe());
                                detailTable.setTableOwner(tableDto.getTableOwner());
                                updateBatchTable.add(detailTable);
                            }
                            //迭代表下的字段
                            for (ImportChDto columnDto : tableDtos) {
                                ChElementDetailColumn detailColumn;
                                List<ChElementDetailColumn> detailColumns = columns.stream().filter(column -> column.getDbName().equals(columnDto.getDbName()) && column.getTableName().equals(columnDto.getTableName()) && column.getColumnName().equals(columnDto.getColumnName())).collect(Collectors.toList());
                                if (detailColumns.size() > 0) {
                                    //存在的字段，更新字段描述数据
                                    detailColumn = detailColumns.get(0);
                                    detailColumn.setColumnNameCn(columnDto.getColumnNameCn());
                                    detailColumn.setIsSensitive(columnDto.getIsSensitive());
                                    updateBatchColumn.add(detailColumn);
                                }
                            }
                        });
                    }
                });
                //批量处理
                if (updateBatchColumn.size() > 0) {
                    detailColumnService.updateBatchById(updateBatchColumn);
                }
                if (updateBatchTable.size() > 0) {
                    for (ChElementDetailTable table : updateBatchTable) {
                        List<ChElementDetailColumn> detailColumns = detailColumnService.list(new QueryWrapper<ChElementDetailColumn>().eq("element_id", table.getElementId()).eq("db_name", table.getDbName()).eq("table_name", table.getTableName()));
                        //找到字段涉敏的就更改表涉敏
                        if (detailColumns.stream().anyMatch(column -> column.getIsSensitive() == 1)) {
                            table.setIsSensitive(1);
                        } else {
                            // 未找到字段涉敏，更改表非涉敏
                            table.setIsSensitive(0);
                        }
                    }
                    detailTableService.updateBatchById(updateBatchTable);
                }
            }
        });
    }
}
