package com.idss.datalake.datagovern.metadata.model.detail.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * mysql清单Column表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qua_web_mysql_element_detail_column")
public class QuaWebMysqlElementDetailColumn implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 元数据ID
     */
    private Long elementId;

    /**
     * 库名称
     */
    private String dbName;

    /**
     * 表名称
     */
    private String tableName;

    /**
     * 字段名称
     */
    private String columnName;

    @TableField(exist = false)
    private String columnType;

    /**
     * 字段中文名，默认为空字符串
     */
    private String columnNameCn;

    /**
     * 是否为敏感表，默认不是
     */
    private Integer isSensitive;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String createUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private String updateUser;

    /**
     * 是否可用，默认为1，可用
     */
    private Boolean isAvailable;

    /**
     * 最早快照版本号，新增记录时写入
     */
    private String firstSnapshootVersion;

    /**
     * 最新快照版本号，更新记录时写入
     */
    private String lastSnapshootVersion;

    private Long tenantId;

    private Long businessType;

    private Long senLevelId;
    private String senLevelName;
    private Long senTypeId;
    private String senTypeName;
    /**
     * 脱敏规则 ID
     */
    private Integer desensitizationId;

    /**
     * 是否必填，0-否，1-是
     */
    private Integer isRequired;
    /**
     * 是否加密，0-否，1-是
     */
    private Integer isEncrypted;
    /**
     * 中文描述
     */
    private String cnDesc;
    /**
     * 枚举值
     */
    private String enumValue;
    /**
     * 映射字段
     */
    private String mappingFields;

    /**
     * 排序字段
     */
    private Integer sort;
}
