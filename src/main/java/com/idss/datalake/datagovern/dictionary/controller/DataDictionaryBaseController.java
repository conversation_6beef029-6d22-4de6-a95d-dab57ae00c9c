package com.idss.datalake.datagovern.dictionary.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryBase;
import com.idss.datalake.datagovern.dictionary.model.DictRequestDto;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryBaseService;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryCategoryService;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryItemService;
import com.idss.datalake.datagovern.dictionary.vo.OverviewDataTieringDetailVo;
import com.idss.datalake.datagovern.dictionary.vo.OverviewSubjectDomainDetailVo;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 数据字典基础信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@RestController
@Slf4j
@RequestMapping(Constant.API_PREFIX + "/dataDictionary")
public class DataDictionaryBaseController {

    @Autowired
    private IDataDictionaryBaseService dataDictionaryBaseService;
    @Autowired
    private IDataDictionaryCategoryService iDataDictionaryCategoryService;
    @Autowired
    private IDataDictionaryItemService dictionaryItemService;

    /**
     * 分页查询字典
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/page")
    public BasePageResponse<List<DataDictionaryBase>> page(@RequestBody DictRequestDto requestDto) {
        try {
            return dataDictionaryBaseService.page(requestDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 新增或编辑字典
     *
     * @param dataDictionaryBase
     * @return
     */
    @PostMapping("/saveOrEdit")
    public ResultBean saveOrEdit(@RequestBody DataDictionaryBase dataDictionaryBase) {
        try {
            dataDictionaryBaseService.saveOrEdit(dataDictionaryBase);
            return ResultBean.success("操作成功");
        } catch (ParamInvalidException e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("操作失败");
        }
    }

    /**
     * 查看字典详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable Long id) {
        try {
            DataDictionaryBase dictionaryBase = dataDictionaryBaseService.getById(id);
            return ResultBean.success(dictionaryBase, "查询成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 删除字典
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/delete")
    public ResultBean delete(@RequestBody DictRequestDto requestDto) {
        try {
            dataDictionaryBaseService.delete(requestDto.getIds());
            return ResultBean.success("删除成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("删除失败");
        }
    }

    /**
     * 字段概览
     *
     * @return
     */
    @GetMapping("/overview")
    public ResultBean overview() {
        try {
            return ResultBean.success(dataDictionaryBaseService.overview());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 分类概览-数据分层明细
     *
     * @param categoryId
     * @return
     */
    @GetMapping("/categoryOverview/dataTiering/{categoryId}")
    public ResultBean categoryOverview(@PathVariable Long categoryId) {
        try {
            List<OverviewDataTieringDetailVo> categoryDetailVoList = dataDictionaryBaseService.overviewDataTieringDetail(categoryId);
            return ResultBean.success(categoryDetailVoList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 分类概览-主题域明细
     *
     * @param categoryId
     * @return
     */
    @GetMapping("/categoryOverview/subjectDomain/{categoryId}")
    public ResultBean overviewSubjectDomain(@PathVariable Long categoryId) {
        try {
            List<OverviewSubjectDomainDetailVo> detailVoList = dataDictionaryBaseService.overviewSubjectDomainDetail(categoryId);
            return ResultBean.success(detailVoList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }
}
