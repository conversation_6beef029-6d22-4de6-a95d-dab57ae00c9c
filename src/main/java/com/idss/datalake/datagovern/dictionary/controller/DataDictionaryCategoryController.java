/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-11-28
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-11-28
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.dictionary.controller;


import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datagovern.dictionary.dto.DataDictionaryCategoryDTO;
import com.idss.datalake.datagovern.dictionary.manager.DataDictionaryCategoryManager;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description <p>数据字典分类 前端控制器</p>
 * @since 2023-11-28
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/dictionaryCategory")
public class DataDictionaryCategoryController {
    private static final Logger logger = LoggerFactory.getLogger(DataDictionaryCategoryController.class);
    @Autowired
    private DataDictionaryCategoryManager dataDictionaryCategoryManager;

    @ApiOperation(value = "新增/编辑")
    @PostMapping(value = "/saveOrEdit")
    public ResultBean saveOrEdit(@RequestBody DataDictionaryCategoryDTO dto) {
        try {
            if (dto.getId() == null) {
                dataDictionaryCategoryManager.create(dto);
            } else {
                dataDictionaryCategoryManager.edit(dto);
            }
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public ResultBean delete(@RequestBody DataDictionaryCategoryDTO dto) {
        try {
            dataDictionaryCategoryManager.delete(dto.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Long id) {
        try {
            return ResultBean.success(dataDictionaryCategoryManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询列表")
    @PostMapping(value = "/page")
    public ResultBean page(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataDictionaryCategoryManager.page(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查看字典分类树")
    @GetMapping(value = "/tree/{dictId}")
    public ResultBean tree(@PathVariable Long dictId) {
        try {
            return ResultBean.success(dataDictionaryCategoryManager.tree(dictId));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查看字典树")
    @GetMapping(value = "/dicTree")
    public ResultBean dicTree() {
        try {
            return ResultBean.success(dataDictionaryCategoryManager.dicTree());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }
}
