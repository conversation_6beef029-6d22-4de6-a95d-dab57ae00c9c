package com.idss.datalake.datagovern.dictionary.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItemTableTrend;
import com.idss.datalake.datagovern.dictionary.mapper.DataDictionaryItemTableTrendMapper;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryItemTableTrendService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据字典-数据条数趋势表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Service
public class DataDictionaryItemTableTrendServiceImpl extends ServiceImpl<DataDictionaryItemTableTrendMapper, DataDictionaryItemTableTrend> implements IDataDictionaryItemTableTrendService {

}
