/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-11-28
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-11-28
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.dictionary.controller;


import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datagovern.dictionary.dto.DataDictionaryItemDTO;
import com.idss.datalake.datagovern.dictionary.dto.DictDataMartAssetDTO;
import com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest;
import com.idss.datalake.datagovern.dictionary.manager.DataDictionaryItemManager;
import com.idss.datalake.datagovern.dictionary.vo.QueryElementVo;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>数据字典项 前端控制器</p>
 * @since 2023-11-28
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/dictionaryItem")
public class DataDictionaryItemController {
    private static final Logger logger = LoggerFactory.getLogger(DataDictionaryItemController.class);
    @Autowired
    private DataDictionaryItemManager dataDictionaryItemManager;

    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody List<DataDictionaryItemDTO> dtos) {
        try {
            dataDictionaryItemManager.create(dtos);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public ResultBean delete(@RequestBody DataDictionaryItemDTO dataDictionaryItemDTO) {
        try {
            dataDictionaryItemManager.delete(dataDictionaryItemDTO.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Long id) {
        try {
            return ResultBean.success(dataDictionaryItemManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "编辑")
    @PutMapping(value = "/edit")
    public ResultBean edit(@RequestBody DataDictionaryItemDTO dataDictionaryItemDTO) {
        try {
            dataDictionaryItemManager.edit(dataDictionaryItemDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询列表")
    @PostMapping(value = "/page")
    public ResultBean page(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataDictionaryItemManager.page(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 分页查询元数据
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/queryElementPage")
    public BasePageResponse<List<QueryElementVo>> queryElementPage(@RequestBody QueryElementRequest requestDto) {
        return dataDictionaryItemManager.queryElementPage(requestDto);
    }

    @ApiOperation(value = "全部字典项数据")
    @PostMapping(value = "/allPage")
    public ResultBean allPage(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataDictionaryItemManager.allPage(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "发布到集市")
    @PostMapping(value = "/addToDataMart")
    public ResultBean addToDataMart(@RequestBody List<DictDataMartAssetDTO> dtoList) {
        try {
            dataDictionaryItemManager.addToDataMart(dtoList);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据表查询数据字典里关联的字段")
    @PostMapping(value = "/queryDetailColumnPage")
    public ResultBean queryDetailColumnPage(@RequestBody RequestDTO requestDTO) {
        try {
            Map<String, Object> page = dataDictionaryItemManager.queryDetailColumnPage(requestDTO);
            return ResultBean.success(page);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "更新所有的字典项")
    @GetMapping(value = "/updateItems")
    public ResultBean updateItems() {
        try {
            dataDictionaryItemManager.updateItems();
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "表指标项查询概览")
    @PostMapping(value = "/pageWithTag")
    public ResultBean pageWithTag(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataDictionaryItemManager.pageWithTag(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 数据条数趋势
     *
     * @param id
     * @return
     */
    @GetMapping("/tableTrend/{id}")
    public ResultBean tableTrend(@PathVariable("id") Long id) {
        try {
            return ResultBean.success(dataDictionaryItemManager.tableTrend(id));
        } catch (Exception e) {
            logger.error("数据条数趋势,{}", e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    /**
     * 预览数据
     *
     * @param itemId
     * @return
     */
    @GetMapping("/previewData/{itemId}")
    public ResultBean previewData(@PathVariable Long itemId) {
        try {
            return dataDictionaryItemManager.previewData(itemId);
        } catch (Exception e) {
            logger.error("预览数据错误,{}", e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    /**
     * 存储概览
     *
     * @return
     */
    @GetMapping("/storageOverview")
    public ResultBean storageOverview() {
        try {
            return ResultBean.success(dataDictionaryItemManager.storageOverview());
        } catch (Exception e) {
            logger.error("存储概览,{}", e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    /**
     * 数据源概览
     *
     * @return
     */
    @GetMapping("/datasourceTypeOverview")
    public ResultBean datasourceTypeOverview() {
        try {
            return ResultBean.success(dataDictionaryItemManager.datasourceTypeOverview());
        } catch (Exception e) {
            logger.error("数据源概览,{}", e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    /**
     * 数据层级概览
     *
     * @return
     */
    @GetMapping("/dwLevelOverview")
    public ResultBean dwLevelOverview() {
        try {
            return ResultBean.success(dataDictionaryItemManager.dwLevelOverview());
        } catch (Exception e) {
            logger.error("数据层级概览,{}", e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    @ApiOperation(value = "字典资产分页")
    @PostMapping(value = "/itemAssetPage")
    public ResultBean itemAssetPage(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataDictionaryItemManager.itemAssetPage(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 存储详情
     *
     * @param id
     * @return
     */
    @GetMapping("/storageDetail/{id}")
    public ResultBean storageDetail(@PathVariable("id") Long id) {
        try {
            return ResultBean.success(dataDictionaryItemManager.storageDetail(id));
        } catch (Exception e) {
            logger.error("数据条数趋势,{}", e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    /**
     * 更新元数据到字典项
     *
     * @return
     */
    @GetMapping("/updateMetaToItem")
    public ResultBean updateMetaToItem() {
        try {
            dataDictionaryItemManager.updateMetaToItem();
            return ResultBean.success();
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
            return ResultBean.fail();
        }
    }
}
