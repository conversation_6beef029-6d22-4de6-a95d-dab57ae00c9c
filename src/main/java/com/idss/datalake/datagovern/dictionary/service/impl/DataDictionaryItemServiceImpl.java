package com.idss.datalake.datagovern.dictionary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.config.entity.QuaAssetTableDataStatistics;
import com.idss.datalake.datagovern.config.service.IQuaAssetTableDataStatisticsService;
import com.idss.datalake.datagovern.dictionary.dto.DataDictionaryItemDTO;
import com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItem;
import com.idss.datalake.datagovern.dictionary.enums.ItemTypeEnum;
import com.idss.datalake.datagovern.dictionary.mapper.DataDictionaryItemMapper;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryItemService;
import com.idss.datalake.datagovern.dictionary.vo.QueryElementVo;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailField;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHiveElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.service.ChElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.detail.service.EsElementDetailFieldService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHiveElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebMysqlElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.job.entity.ChTaskResultColumn;
import com.idss.datalake.datagovern.metadata.model.job.entity.EsTaskResultField;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaWebHiveTaskResultColumn;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaWebMysqlTaskResultColumn;
import com.idss.datalake.datagovern.metadata.model.job.service.ChTaskResultColumnService;
import com.idss.datalake.datagovern.metadata.model.job.service.EsTaskResultFieldService;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebHiveTaskResultColumnService;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebMysqlTaskResultColumnService;
import com.idss.datalake.datamart.enums.ElementTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据字典项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
@Slf4j
public class DataDictionaryItemServiceImpl extends ServiceImpl<DataDictionaryItemMapper, DataDictionaryItem> implements IDataDictionaryItemService {
    @Resource
    private DataDictionaryItemMapper dictionaryItemMapper;
    @Autowired
    private IQuaWebHiveElementDetailColumnService hiveElementDetailColumnService;
    @Autowired
    private IQuaWebMysqlElementDetailColumnService mysqlElementDetailColumnService;
    @Autowired
    private ChElementDetailColumnService chElementDetailColumnService;
    @Autowired
    private EsElementDetailFieldService esElementDetailFieldService;
    @Autowired
    private ChTaskResultColumnService chTaskResultColumnService;
    @Autowired
    private IQuaWebHiveTaskResultColumnService hiveTaskResultColumnService;
    @Autowired
    private IQuaWebMysqlTaskResultColumnService mysqlTaskResultColumnService;
    @Autowired
    private EsTaskResultFieldService esTaskResultFieldService;
    @Autowired
    private IQuaAssetTableDataStatisticsService tableDataStatisticsService;

    @Override
    public IPage<DataDictionaryItem> pageCustom(Page<DataDictionaryItem> page, QueryWrapper<DataDictionaryItem> wrapper) {
        return dictionaryItemMapper.pageCustom(page, wrapper);
    }

    @Override
    public Long pageWithTagCount(DataDictionaryItemDTO dto) {
        return dictionaryItemMapper.pageWithTagCount(dto);
    }

    @Override
    public List<DataDictionaryItem> pageWithTag(DataDictionaryItemDTO dto) {
        return dictionaryItemMapper.pageWithTag(dto);
    }

    @Override
    public void updateItemFromMeta(String datasourceType, String itemType, Long elementId, String dbName, String tableName,
                                   String itemName, Long itemId, String columnNameCn, String desc, String owner) {
        QueryWrapper<DataDictionaryItem> wrapper = new QueryWrapper<DataDictionaryItem>()
                .eq("datasource_type", datasourceType)
                .eq("item_type", itemType)
                .eq("element_id", elementId)
                .eq("item_name", itemName);
        if (itemType.equals(ItemTypeEnum.field.name())) { // 只有字段才需要tableName
            wrapper.eq("table_name", tableName);
        }
        if (StringUtils.isNotBlank(dbName)) { // es没有db值
            wrapper.eq("database_name", dbName);
        }
        List<DataDictionaryItem> items = dictionaryItemMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(items)) {
            for (DataDictionaryItem item : items) {
                item.setColumnNameCn(columnNameCn);
                if ("table".equals(itemType)) { // 编辑字段没有修改描述
                    item.setItemDesc(desc);
                    item.setOwner(owner);
                }
            }
            this.updateBatchById(items);
            log.info("更新字典数据项完成");
        }
    }

    /**
     * 从元数据维护编辑保存到字典
     *
     * @param itemDTO
     */
    @Override
    public void addFromMetadata(DataDictionaryItemDTO itemDTO) {
        if (itemDTO == null) {
            return;
        }
        try {
            if (StringUtils.equalsAny(itemDTO.getItemType(), ItemTypeEnum.table.name(), ItemTypeEnum.index.name())) {
                List<DataDictionaryItem> fieldItems = new ArrayList<>();
                Long tenantId = itemDTO.getTenantId() == null ? UserUtil.getLongCurrentTenantId() : itemDTO.getTenantId();
                String username = StringUtils.isNotBlank(itemDTO.getCreateUser()) ? itemDTO.getCreateUser() : UserUtil.getCurrentUsername();
                //保存
                DataDictionaryItem dataDictionaryItem = new DataDictionaryItem();
                ReflectionUtil.copyLomBokProperties(itemDTO, dataDictionaryItem);
                dataDictionaryItem.setTenantId(tenantId);
                dataDictionaryItem.setCreateTime(LocalDateTime.now());
                dataDictionaryItem.setUpdateTime(LocalDateTime.now());
                dataDictionaryItem.setCreateUser(username);
                dataDictionaryItem.setUpdateUser(username);

                // 数据量
                QuaAssetTableDataStatistics tableDataStatistics =
                        tableDataStatisticsService.getOne(new LambdaQueryWrapper<QuaAssetTableDataStatistics>()
                                .eq(QuaAssetTableDataStatistics::getElementId, itemDTO.getElementId())
                                .eq(StringUtils.isNotBlank(itemDTO.getDatabaseName()), QuaAssetTableDataStatistics::getDbName,
                                        itemDTO.getDatabaseName())
                                .eq(QuaAssetTableDataStatistics::getTableName, itemDTO.getTableName())
                                .eq(QuaAssetTableDataStatistics::getDatasourceType, itemDTO.getDatasourceType())
                                .orderByDesc(QuaAssetTableDataStatistics::getCreateTime)
                                .last("limit 1"));
                if (tableDataStatistics != null) {
                    dataDictionaryItem.setDataCount(tableDataStatistics.getDataCount());
                    dataDictionaryItem.setDataStorageSize(tableDataStatistics.getDataStorageSize());
                }

                // 如果新增表，则将表里的字段一起全部保存
                QueryWrapper queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("tenant_id", tenantId);
                queryWrapper.eq("datasource_type", itemDTO.getDatasourceType());
                if (!ItemTypeEnum.index.name().equals(itemDTO.getItemType())) {
                    queryWrapper.eq("database_name", itemDTO.getDatabaseName());
                }
                queryWrapper.eq("table_name", itemDTO.getItemName());
                queryWrapper.eq("item_type", "field");
                List<DataDictionaryItem> existFieldlist = this.list(queryWrapper);
                Set<Long> existItemIds = new HashSet<>();
                if (CollectionUtils.isNotEmpty(existFieldlist)) {
                    Set<Long> itemIds = existFieldlist.stream().map(existField -> existField.getItemId()).collect(Collectors.toSet());
                    existItemIds.addAll(itemIds);
                }
                QueryElementRequest requestDto = new QueryElementRequest();
                requestDto.setTableName(itemDTO.getItemName());
                requestDto.setDbName(itemDTO.getDatabaseName());
                requestDto.setElementType(itemDTO.getAssetType());
                requestDto.setId(itemDTO.getItemId());
                List<QueryElementVo> filedList = this.queryFiledList(requestDto, tenantId);
                if (CollectionUtils.isNotEmpty(filedList)) {
                    LocalDateTime now = LocalDateTime.now();
                    for (QueryElementVo fieldVo : filedList) {
                        // 已存在的field不需要添加
                        if (existItemIds.contains(fieldVo.getId())) {
                            continue;
                        }
                        DataDictionaryItem fieldItem = new DataDictionaryItem();
                        fieldItem.setDictId(itemDTO.getDictId());
                        fieldItem.setCategoryId(itemDTO.getCategoryId());
                        fieldItem.setItemId(fieldVo.getId());
                        fieldItem.setItemName(fieldVo.getElementName());
                        fieldItem.setItemType(ItemTypeEnum.field.name());
                        fieldItem.setDatasourceType(itemDTO.getDatasourceType());
                        fieldItem.setDatabaseName(itemDTO.getDatabaseName());
                        fieldItem.setFieldType(fieldVo.getFieldType());
                        fieldItem.setAssetPath(fieldVo.getAssetPath());
                        fieldItem.setAssetType(fieldVo.getAssetType());
                        fieldItem.setAssetTypeCode(fieldVo.getAssetTypeCode());
                        fieldItem.setElementId(fieldVo.getElementId());
                        fieldItem.setAssetDataType(fieldVo.getDataType());
                        this.setCnName(fieldItem, fieldItem.getDatasourceType(), fieldItem.getItemId());
                        fieldItem.setCnDesc(fieldVo.getCnDesc());
                        fieldItem.setEnumValue(fieldVo.getEnumValue());
                        fieldItem.setMappingFields(fieldVo.getMappingFields());
                        fieldItem.setSort(fieldVo.getSort());
                        fieldItem.setIsPrimaryKey(fieldVo.getIsPrimaryKey());

                        fieldItem.setTenantId(tenantId);
                        fieldItem.setCreateTime(now);
                        fieldItem.setUpdateTime(now);
                        fieldItem.setCreateUser(username);
                        fieldItem.setUpdateUser(username);
                        fieldItems.add(fieldItem);
                    }
                }

                log.info("新增字典项，{}", dataDictionaryItem.getItemName());
                this.save(dataDictionaryItem);
                if (CollectionUtils.isNotEmpty(fieldItems)) {
                    log.info("新增字典项关联字段，数量：{}", fieldItems.size());
                    this.saveBatch(fieldItems);
                }
            }
        } catch (Exception e) {
            log.error("元数据维护编辑保存到字典失败,{}", e.getMessage(), e);
        }
    }

    @Override
    public void updateItemFromMeta(DataDictionaryItemDTO itemDTO) {
        QueryWrapper<DataDictionaryItem> wrapper = new QueryWrapper<DataDictionaryItem>()
                .eq("datasource_type", itemDTO.getDatasourceType())
                .eq("item_type", itemDTO.getItemType())
                .eq("element_id", itemDTO.getElementId())
                .eq("item_name", itemDTO.getItemName());
        if (StringUtils.isNotBlank(itemDTO.getDatabaseName())) { // es没有db值
            wrapper.eq("database_name", itemDTO.getDatabaseName());
        }
        if (itemDTO.getItemType().equals(ItemTypeEnum.field.name())) { // 只有字段才需要tableName
            wrapper.eq("table_name", itemDTO.getTableName());
        }
        List<DataDictionaryItem> items = dictionaryItemMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(items)) {
            for (DataDictionaryItem item : items) {
                item.setColumnNameCn(itemDTO.getColumnNameCn());
                if (StringUtils.equalsAny(itemDTO.getItemType(), ItemTypeEnum.table.name(), ItemTypeEnum.index.name())) { // 编辑字段没有修改描述
                    item.setItemDesc(itemDTO.getItemDesc());
                    item.setOwner(itemDTO.getOwner());
                    item.setIsSensitive(itemDTO.getIsSensitive());
                    item.setBusinessSectorId(itemDTO.getBusinessSectorId());
                    item.setDataDomainId(itemDTO.getDataDomainId());
                    item.setDwLevelId(itemDTO.getDwLevelId());
                    item.setBusinessProcessId(itemDTO.getBusinessProcessId());
                } else if ("field".equals(itemDTO.getItemType())) {
                    item.setIsSensitive(itemDTO.getIsSensitive());
                    item.setSenLevelId(itemDTO.getSenLevelId());
                    item.setSenLevelName(itemDTO.getSenLevelName());
                    item.setSenTypeId(itemDTO.getSenTypeId());
                    item.setSenTypeName(itemDTO.getSenTypeName());
                    item.setDesensitizationId(itemDTO.getDesensitizationId());
                    item.setIsRequired(itemDTO.getIsRequired());
                    item.setIsEncrypted(itemDTO.getIsEncrypted());
                    item.setCnDesc(itemDTO.getCnDesc());
                    item.setEnumValue(itemDTO.getEnumValue());
                    item.setMappingFields(itemDTO.getMappingFields());
                    item.setSort(itemDTO.getSort());
                }
            }
            this.updateBatchById(items);
            log.info("更新字典数据项完成");
        } else {
            if (itemDTO.getBusinessSectorId() != null && itemDTO.getDataDomainId() != null && itemDTO.getDwLevelId() != null) {
                this.addFromMetadata(itemDTO);
            }
        }
    }

    @Override
    public IPage<DataDictionaryItem> itemAssetPage(Page<DataDictionaryItem> page, QueryWrapper<DataDictionaryItem> wrapper) {
        return dictionaryItemMapper.itemAssetPage(page, wrapper);
    }

    @Override
    public void setCnName(DataDictionaryItem dataDictionaryItem, String datasourceType, Long itemId) {
        if ("mysql".equalsIgnoreCase(datasourceType)) {
            QuaWebMysqlElementDetailColumn one = mysqlElementDetailColumnService.getById(itemId);
            if (one != null) {
                dataDictionaryItem.setColumnNameCn(one.getColumnNameCn());
                dataDictionaryItem.setTableName(one.getTableName());
                dataDictionaryItem.setSenLevelId(one.getSenLevelId());
                dataDictionaryItem.setSenLevelName(one.getSenLevelName());
                dataDictionaryItem.setSenTypeId(one.getSenTypeId());
                dataDictionaryItem.setSenTypeName(one.getSenTypeName());
                dataDictionaryItem.setIsSensitive(one.getIsSensitive());
                dataDictionaryItem.setDesensitizationId(one.getDesensitizationId());
                dataDictionaryItem.setIsRequired(one.getIsRequired());
                dataDictionaryItem.setIsEncrypted(one.getIsEncrypted());
            }
        } else if ("clickhouse".equalsIgnoreCase(datasourceType)) {
            ChElementDetailColumn one = chElementDetailColumnService.getById(itemId);
            if (one != null) {
                dataDictionaryItem.setColumnNameCn(one.getColumnNameCn());
                dataDictionaryItem.setTableName(one.getTableName());
                dataDictionaryItem.setSenLevelId(one.getSenLevelId());
                dataDictionaryItem.setSenLevelName(one.getSenLevelName());
                dataDictionaryItem.setSenTypeId(one.getSenTypeId());
                dataDictionaryItem.setSenTypeName(one.getSenTypeName());
                dataDictionaryItem.setIsSensitive(one.getIsSensitive());
                dataDictionaryItem.setDesensitizationId(one.getDesensitizationId());
                dataDictionaryItem.setIsRequired(one.getIsRequired());
                dataDictionaryItem.setIsEncrypted(one.getIsEncrypted());
            }
        } else if ("hive".equalsIgnoreCase(datasourceType)) {
            QuaWebHiveElementDetailColumn one = hiveElementDetailColumnService.getById(itemId);
            if (one != null) {
                dataDictionaryItem.setColumnNameCn(one.getColumnNameCn());
                dataDictionaryItem.setTableName(one.getTableName());
                dataDictionaryItem.setSenLevelId(one.getSenLevelId());
                dataDictionaryItem.setSenLevelName(one.getSenLevelName());
                dataDictionaryItem.setSenTypeId(one.getSenTypeId());
                dataDictionaryItem.setSenTypeName(one.getSenTypeName());
                dataDictionaryItem.setIsSensitive(one.getIsSensitive());
                dataDictionaryItem.setDesensitizationId(one.getDesensitizationId());
                dataDictionaryItem.setIsRequired(one.getIsRequired());
                dataDictionaryItem.setIsEncrypted(one.getIsEncrypted());
            }
        } else if ("elasticsearch".equalsIgnoreCase(datasourceType)) {
            EsElementDetailField one = esElementDetailFieldService.getById(itemId);
            if (one != null) {
                dataDictionaryItem.setColumnNameCn(one.getFieldNameCn());
                dataDictionaryItem.setTableName(one.getIndexName());
                dataDictionaryItem.setSenLevelId(one.getSenLevelId());
                dataDictionaryItem.setSenLevelName(one.getSenLevelName());
                dataDictionaryItem.setSenTypeId(one.getSenTypeId());
                dataDictionaryItem.setSenTypeName(one.getSenTypeName());
                dataDictionaryItem.setIsSensitive(one.getIsSensitive());
                dataDictionaryItem.setDesensitizationId(one.getDesensitizationId());
                dataDictionaryItem.setIsRequired(one.getIsRequired());
                dataDictionaryItem.setIsEncrypted(one.getIsEncrypted());
            }
        }
    }

    @Override
    public List<QueryElementVo> queryFiledList(QueryElementRequest requestDto, Long tenantId) {
        requestDto.setPageSize(2000);
        requestDto.setTenantId(tenantId);
        List<QueryElementVo> result = null;
        if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(requestDto.getElementType())) {
            String maxVersion = dictionaryItemMapper.queryVersionById("qua_web_ch_element_detail_table", requestDto.getId());
            requestDto.setSnapshootVersion(maxVersion);
            result = dictionaryItemMapper.queryClickhouseField(requestDto);
            if (CollectionUtils.isNotEmpty(result)) {
                // 查询field_type
                Set<String> dbNames = result.stream().map(x -> x.getDbName()).collect(Collectors.toSet());
                List<String> columnNames = result.stream().map(x -> x.getElementName()).collect(Collectors.toList());
                Set<String> tableNames = result.stream().map(x -> x.getTableName()).collect(Collectors.toSet());
                Long elementId = result.get(0).getElementId();
                QueryWrapper<ChTaskResultColumn> wrapper = new QueryWrapper<>();
                wrapper.eq("element_id", elementId).eq("tenant_id", tenantId).eq("snapshoot_version", maxVersion).in("db_name", dbNames).in(
                        "table_name", tableNames).in("column_name", columnNames);
                List<ChTaskResultColumn> taskResultColumns = chTaskResultColumnService.list(wrapper);
                if (CollectionUtils.isNotEmpty(taskResultColumns)) {
                    loop:
                    for (QueryElementVo vo : result) {
                        for (ChTaskResultColumn resultColumn : taskResultColumns) {
                            if (vo.getElementId().equals(resultColumn.getElementId()) && vo.getDbName().equals(resultColumn.getDbName()) && vo.getTableName().equals(resultColumn.getTableName()) && vo.getElementName().equals(resultColumn.getColumnName())) {
                                vo.setFieldType(resultColumn.getType());
                                continue loop;
                            }
                        }
                    }
                }
            }
        } else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(requestDto.getElementType())) {
            String maxVersion = dictionaryItemMapper.queryVersionById("qua_web_hive_element_detail_table", requestDto.getId());
            requestDto.setSnapshootVersion(maxVersion);
            result = dictionaryItemMapper.queryHiveField(requestDto);
            if (CollectionUtils.isNotEmpty(result)) {
                // 查询field_type
                Set<String> dbNames = result.stream().map(x -> x.getDbName()).collect(Collectors.toSet());
                List<String> columnNames = result.stream().map(x -> x.getElementName()).collect(Collectors.toList());
                Set<String> tableNames = result.stream().map(x -> x.getTableName()).collect(Collectors.toSet());
                Long elementId = result.get(0).getElementId();
                QueryWrapper<QuaWebHiveTaskResultColumn> wrapper = new QueryWrapper<>();
                wrapper.eq("element_id", elementId).eq("tenant_id", tenantId).eq("snapshoot_version", maxVersion).in("db_name", dbNames).in(
                        "table_name", tableNames).in("column_name", columnNames);
                List<QuaWebHiveTaskResultColumn> taskResultColumns = hiveTaskResultColumnService.list(wrapper);
                if (CollectionUtils.isNotEmpty(taskResultColumns)) {
                    loop:
                    for (QueryElementVo vo : result) {
                        for (QuaWebHiveTaskResultColumn resultColumn : taskResultColumns) {
                            if (vo.getElementId().equals(resultColumn.getElementId()) && vo.getDbName().equals(resultColumn.getDbName()) && vo.getTableName().equals(resultColumn.getTableName()) && vo.getElementName().equals(resultColumn.getColumnName())) {
                                vo.setFieldType(resultColumn.getType());
                                continue loop;
                            }
                        }
                    }
                }
            }
        } else if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(requestDto.getElementType())) {
            String maxVersion = dictionaryItemMapper.queryVersionById("qua_web_mysql_element_detail_table", requestDto.getId());
            requestDto.setSnapshootVersion(maxVersion);
            result = dictionaryItemMapper.queryMysqlField(requestDto);
            if (CollectionUtils.isNotEmpty(result)) {
                // 查询field_type
                Set<String> dbNames = result.stream().map(x -> x.getDbName()).collect(Collectors.toSet());
                Set<String> columnNames = result.stream().map(x -> x.getElementName()).collect(Collectors.toSet());
                Set<String> tableNames = result.stream().map(x -> x.getTableName()).collect(Collectors.toSet());
                Long elementId = result.get(0).getElementId();
                QueryWrapper<QuaWebMysqlTaskResultColumn> wrapper = new QueryWrapper<>();
                wrapper.eq("element_id", elementId).eq("tenant_id", tenantId).eq("snapshoot_version", maxVersion).in("db_name", dbNames).in(
                        "table_name", tableNames).in("column_name", columnNames);
                List<QuaWebMysqlTaskResultColumn> taskResultColumns = mysqlTaskResultColumnService.list(wrapper);
                if (CollectionUtils.isNotEmpty(taskResultColumns)) {
                    loop:
                    for (QueryElementVo vo : result) {
                        for (QuaWebMysqlTaskResultColumn resultColumn : taskResultColumns) {
                            if (vo.getElementId().equals(resultColumn.getElementId()) && vo.getDbName().equals(resultColumn.getDbName()) && vo.getTableName().equals(resultColumn.getTableName()) && vo.getElementName().equals(resultColumn.getColumnName())) {
                                vo.setFieldType(resultColumn.getType());
                                continue loop;
                            }
                        }
                    }
                }
            }
        } else if (ElementTypeEnum.ELASTICSEARCH_INDEX.getCode().equals(requestDto.getElementType())) {
            String maxVersion = dictionaryItemMapper.queryVersionById("qua_web_es_element_detail_index", requestDto.getId());
            requestDto.setSnapshootVersion(maxVersion);
            result = dictionaryItemMapper.queryElasticsearchField(requestDto);
            if (CollectionUtils.isNotEmpty(result)) {
                // 查询field_type
                List<String> columnNames = result.stream().map(x -> x.getElementName()).collect(Collectors.toList());
                Set<String> tableNames = result.stream().map(x -> x.getTableName()).collect(Collectors.toSet());
                Long elementId = result.get(0).getElementId();
                QueryWrapper<EsTaskResultField> wrapper = new QueryWrapper<>();
                wrapper.eq("element_id", elementId).eq("tenant_id", tenantId).eq("snapshoot_version", maxVersion).in("index_name",
                        tableNames).in("field_name", columnNames);
                List<EsTaskResultField> taskResultColumns = esTaskResultFieldService.list(wrapper);
                if (CollectionUtils.isNotEmpty(taskResultColumns)) {
                    loop:
                    for (QueryElementVo vo : result) {
                        for (EsTaskResultField resultColumn : taskResultColumns) {
                            if (vo.getElementId().equals(resultColumn.getElementId()) && vo.getTableName().equals(resultColumn.getIndexName()) && vo.getElementName().equals(resultColumn.getFieldName())) {
                                vo.setFieldType(resultColumn.getFieldDataType());
                                continue loop;
                            }
                        }
                    }
                }
            }
        }
        return result;
    }
}
