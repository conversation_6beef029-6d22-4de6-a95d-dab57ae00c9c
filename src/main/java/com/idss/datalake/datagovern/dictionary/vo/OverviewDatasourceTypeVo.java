package com.idss.datalake.datagovern.dictionary.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description <p>字典概览-元数据类型卡片</p>
 * @date
 * @see
 */
@Data
public class OverviewDatasourceTypeVo {
    private Long id;
    /**
     * 字典名称
     */
    private String dictName;
    /**
     * 分类数量
     */
    private int count = 0;

    /**
     * 描述
     */
    private String dictDesc;

    /**
     * 字典卡片
     */
    private List<OverviewDatasourceTypeItemVo> items = new ArrayList<>();
}
