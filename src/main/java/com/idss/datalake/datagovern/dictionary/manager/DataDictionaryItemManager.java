/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-11-28
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-11-28
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.dictionary.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.database.ClickhouseComponent;
import com.idss.datalake.common.database.HiveComponent;
import com.idss.datalake.common.database.MysqlComponent;
import com.idss.datalake.common.database.entity.DbConnInfo;
import com.idss.datalake.common.database.entity.DbDataInfo;
import com.idss.datalake.common.database.enums.DB_TYP;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.BtoaEncode;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.datalake.common.util.DateUtils;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.common.util.KerberosUtil;
import com.idss.datalake.common.util.MetaDataUtil;
import com.idss.datalake.common.util.NumberUtils;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmService;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTable;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableIsland;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableIslandService;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableService;
import com.idss.datalake.datagovern.config.entity.DataScoreCalcConfig;
import com.idss.datalake.datagovern.config.entity.QuaAssetMaintenance;
import com.idss.datalake.datagovern.config.entity.QuaAssetTableDataStatistics;
import com.idss.datalake.datagovern.config.entity.QuaWebTableModel;
import com.idss.datalake.datagovern.config.service.IDataScoreCalcConfigService;
import com.idss.datalake.datagovern.config.service.IQuaAssetMaintenanceService;
import com.idss.datalake.datagovern.config.service.IQuaAssetQualityService;
import com.idss.datalake.datagovern.config.service.IQuaAssetTableDataStatisticsService;
import com.idss.datalake.datagovern.config.service.IQuaWebTableModelService;
import com.idss.datalake.datagovern.datametarelation.model.Node;
import com.idss.datalake.datagovern.dictionary.dto.DataDictionaryItemDTO;
import com.idss.datalake.datagovern.dictionary.dto.DictDataMartAssetDTO;
import com.idss.datalake.datagovern.dictionary.dto.ItemPreviewRequest;
import com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryBase;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryCategory;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItem;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItemTableStorageDetail;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItemTableTrend;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItemViewRecord;
import com.idss.datalake.datagovern.dictionary.enums.ItemTypeEnum;
import com.idss.datalake.datagovern.dictionary.mapper.DataDictionaryItemMapper;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryBaseService;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryCategoryService;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryItemService;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryItemTableTrendService;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryItemViewRecordService;
import com.idss.datalake.datagovern.dictionary.vo.DatasourceTypeOverviewVo;
import com.idss.datalake.datagovern.dictionary.vo.DwLevelOverviewVo;
import com.idss.datalake.datagovern.dictionary.vo.ItemOverviewVo;
import com.idss.datalake.datagovern.dictionary.vo.QueryElementVo;
import com.idss.datalake.datagovern.dictionary.vo.StorageOverviewVo;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailField;
import com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailIndex;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHiveElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHiveElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.service.ChElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.detail.service.ChElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.detail.service.EsElementDetailFieldService;
import com.idss.datalake.datagovern.metadata.model.detail.service.EsElementDetailIndexService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHiveElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHiveElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebMysqlElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebMysqlElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.mapper.QuaWabElementMapper;
import com.idss.datalake.datagovern.metadata.model.job.entity.ChTaskResultColumn;
import com.idss.datalake.datagovern.metadata.model.job.entity.EsTaskResultField;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaTask;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaWebHiveTaskResultColumn;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaWebMysqlTaskResultColumn;
import com.idss.datalake.datagovern.metadata.model.job.service.ChTaskResultColumnService;
import com.idss.datalake.datagovern.metadata.model.job.service.EsTaskResultFieldService;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebHiveTaskResultColumnService;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebMysqlTaskResultColumnService;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaJobService;
import com.idss.datalake.datagovern.metadatatag.model.DataMetaTagAndTypeModel;
import com.idss.datalake.datagovern.metadatatag.service.IDataMetaTagMasterService;
import com.idss.datalake.datamart.entity.DataMartAsset;
import com.idss.datalake.datamart.entity.DataMartSubscribe;
import com.idss.datalake.datamart.enums.ElementTypeEnum;
import com.idss.datalake.datamart.service.IDataMartAssetService;
import com.idss.datalake.datamart.service.IDataMartSubscribeService;
import com.idss.datalake.datashare.tenant.entity.TbDwLevel;
import com.idss.datalake.datashare.tenant.service.ITbDwLevelService;
import com.idss.datalake.dict.bean.UebaDictionary;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.net.URI;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p>数据字典项 manager处理类</p>
 * @since 2023-11-28
 */
@Component
public class DataDictionaryItemManager {
    private static final Logger logger = LoggerFactory.getLogger(DataDictionaryItemManager.class);

    @Autowired
    private IDataDictionaryItemService dictionaryItemService;
    @Autowired
    private DataDictionaryItemMapper dictionaryItemMapper;
    @Autowired
    private IDataDictionaryCategoryService categoryService;
    @Autowired
    private IDataDictionaryBaseService dictionaryBaseService;
    @Autowired
    private IQuaWebMysqlTaskResultColumnService mysqlTaskResultColumnService;
    @Autowired
    private IQuaWebHiveTaskResultColumnService hiveTaskResultColumnService;
    @Autowired
    private ChTaskResultColumnService chTaskResultColumnService;
    @Autowired
    private EsTaskResultFieldService esTaskResultFieldService;

    @Autowired
    private IQuaWebMysqlElementDetailTableService mysqlElementDetailTableService;
    @Autowired
    private IQuaWebMysqlElementDetailColumnService mysqlElementDetailColumnService;

    @Autowired
    private IQuaWebHiveElementDetailTableService hiveElementDetailTableService;
    @Autowired
    private IQuaWebHiveElementDetailColumnService hiveElementDetailColumnService;

    @Autowired
    private ChElementDetailTableService chElementDetailTableService;
    @Autowired
    private ChElementDetailColumnService chElementDetailColumnService;

    @Autowired
    private EsElementDetailIndexService esElementDetailIndexService;
    @Autowired
    private EsElementDetailFieldService esElementDetailFieldService;

    @Autowired
    private IDataMartAssetService dataMartAssetService;
    @Autowired
    private IDataMartSubscribeService subscribeService;
    @Autowired
    private IDataEncryptionAlgorithmService dataEncryptionAlgorithmService;
    @Autowired
    private QuaJobService jobService;
    @Autowired
    private IDataMetaTagMasterService metaTagMasterService;
    @Autowired
    private IDataDictionaryItemTableTrendService itemTableTrendService;
    @Autowired
    private QuaWabElementMapper quaWabElementMapper;
    @Autowired
    private ClickhouseComponent clickhouseComponent;
    @Autowired
    private HiveComponent hiveComponent;
    @Autowired
    private MysqlComponent mysqlComponent;
    @Autowired
    private ITbDwLevelService dwLevelService;
    @Autowired
    private IDataScoreCalcConfigService scoreCalcConfigService;
    @Autowired
    private IQuaAssetMaintenanceService quaAssetMaintenanceService;
    @Autowired
    private IQuaAssetQualityService quaAssetQualityService;
    @Autowired
    private IQuaAssetTableDataStatisticsService tableDataStatisticsService;
    @Autowired
    private IDataDictionaryItemViewRecordService itemViewRecordService;
    @Autowired
    private IDataLineageRelationTableService relationTableService;
    @Autowired
    private IDataLineageRelationTableIslandService tableIslandService;
    @Autowired
    private IQuaWebTableModelService tableModelService;

    public Map<String, Object> createPageCondition(RequestDTO requestDTO) {
        if (requestDTO != null && requestDTO.getGlobal() != null) {
            GlobalDTO globalDTO = requestDTO.getGlobal();
            if (globalDTO.getPageNum() == null) {
                globalDTO.setPageNum(Constant.DEFAULT_PAGE_NUM);
            }
            if (globalDTO.getPageSize() == null) {
                globalDTO.setPageSize(Constant.DEFAULT_PAGE_SIZE);
            }
            if (StringUtils.isEmpty(globalDTO.getOrderType())) {
                globalDTO.setOrderType("desc");
            }
            if (StringUtils.isEmpty(globalDTO.getOrderField())) {
                globalDTO.setOrderField("createTime");
            }
        } else {
            throw new ParamInvalidException("入参错误");
        }
        return null;
    }

    /**
     * 分页
     *
     * @param requestDTO
     * @return
     * @throws Exception
     */
    public Map<String, Object> page(RequestDTO requestDTO) throws Exception {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<DataDictionaryItem> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataDictionaryItem> queryWrapper = new QueryWrapper<>();
        boolean customCategorySearch = false;
        Long tenantId = UserUtil.getLongCurrentTenantId();
        if (requestDTO.getParam() != null) {
            DataDictionaryItemDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), DataDictionaryItemDTO.class);
            if (dto.getCustomDictId() != null || dto.getCustomCategoryId() != null) {
                queryWrapper.eq("t2.tenant_id", tenantId);
                queryWrapper.eq("t1.category_id", dto.getCategoryId());
                if (dto.getCustomDictId() != null) {
                    queryWrapper.eq("t2.dict_id", dto.getCustomDictId());
                } else {
                    queryWrapper.eq("t2.category_id", dto.getCustomCategoryId());
                }
                if (StringUtils.isNotBlank(dto.getItemName())) {
                    queryWrapper.like("t2.item_name", dto.getItemName());
                }
                if (StringUtils.isNotBlank(dto.getDatasourceType())) {
                    queryWrapper.eq("t2.datasource_type", dto.getDatasourceType());
                }
                if (StringUtils.isNotBlank(dto.getItemType())) {
                    queryWrapper.eq("t2.item_type", dto.getItemType());
                }
                if (StringUtils.isNotBlank(dto.getTableName())) {
                    queryWrapper.like("t2.table_name", dto.getTableName());
                    queryWrapper.apply("t2.table_name != t2.item_name");
                }
                customCategorySearch = true;
            } else {
                if (dto.getDictId() != null) {
                    queryWrapper.eq("dict_id", dto.getDictId());
                }
                if (dto.getCategoryId() != null) {
                    queryWrapper.eq("category_id", dto.getCategoryId());
                }
                if (StringUtils.isNotBlank(dto.getItemName())) {
                    queryWrapper.like("item_name", dto.getItemName());
                }
                if (StringUtils.isNotBlank(dto.getDatasourceType())) {
                    queryWrapper.eq("datasource_type", dto.getDatasourceType());
                }
                if (StringUtils.isNotBlank(dto.getItemType())) {
                    queryWrapper.eq("item_type", dto.getItemType());
                }
                if (StringUtils.isNotBlank(dto.getTableName())) {
                    queryWrapper.like("table_name", dto.getTableName());
                    queryWrapper.apply("table_name != item_name");
                }
                if (requestDTO.getGlobal() != null) {
                    queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                            CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
                }
            }
        }
        IPage<DataDictionaryItem> pageResult;
        if (customCategorySearch) {
            pageResult = dictionaryItemService.pageCustom(page, queryWrapper);
        } else {
            pageResult = dictionaryItemService.page(page, queryWrapper);
        }
        List<DataDictionaryItem> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            String currentUserId = UserUtil.getCurrentUserId();
            List<Long> assetIds = records.stream().map(x -> x.getDataMartAssetId()).collect(Collectors.toList());
            List<DataMartSubscribe> subscribes = subscribeService.list(new QueryWrapper<DataMartSubscribe>().in("asset_id", assetIds).eq(
                    "tenant_id", tenantId).eq("user_id", currentUserId));
            Set<Long> assetIdSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(subscribes)) {
                assetIdSet = subscribes.stream().map(x -> x.getAssetId()).collect(Collectors.toSet());
            }
            for (DataDictionaryItem record : records) {
                if (record.getDataMartAssetId() != null) {
                    record.setHasPublished(true);
                    if (assetIdSet.contains(record.getDataMartAssetId())) {
                        record.setHasSubscribe(true);
                    }
                }
            }
        }
        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);
        return result;
    }

    public void create(List<DataDictionaryItemDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            throw new ParamInvalidException("入参异常");
        }
        List<DataDictionaryItem> items = new ArrayList<>();
        List<DataDictionaryItem> fieldItems = new ArrayList<>();
        Long tenantId = UserUtil.getLongCurrentTenantId();
        String username = UserUtil.getCurrentUsername();
        for (DataDictionaryItemDTO itemDTO : dtos) {
            if (StringUtils.isBlank(itemDTO.getItemName())) {
                throw new ParamInvalidException("入参异常");
            }
            List<DataDictionaryItem> list = dictionaryItemService.list(new QueryWrapper<DataDictionaryItem>().eq("item_id",
                    itemDTO.getItemId()).eq("category_id", itemDTO.getCategoryId()).eq("tenant_id", tenantId).eq("datasource_type",
                    itemDTO.getDatasourceType()).eq("database_name", itemDTO.getDatabaseName()));
            if (CollectionUtils.isNotEmpty(list)) {
                continue;
            }

            //保存
            DataDictionaryItem dataDictionaryItem = new DataDictionaryItem();
            ReflectionUtil.copyLomBokProperties(itemDTO, dataDictionaryItem);
            dataDictionaryItem.setTenantId(tenantId);
            dataDictionaryItem.setCreateTime(LocalDateTime.now());
            dataDictionaryItem.setUpdateTime(LocalDateTime.now());
            dataDictionaryItem.setCreateUser(username);
            dataDictionaryItem.setUpdateUser(username);
            // 字段时查询中文名、表名
            if ("field".equals(itemDTO.getItemType())) {
                dataDictionaryItem.setItemType(ItemTypeEnum.field.name());
                dictionaryItemService.setCnName(dataDictionaryItem, itemDTO.getDatasourceType(), itemDTO.getItemId());
            } else if (StringUtils.equalsAny(itemDTO.getItemType(), ItemTypeEnum.table.name(), ItemTypeEnum.index.name())) {
                dataDictionaryItem.setTableName(itemDTO.getItemName());
                // 如果新增表，则将表里的字段一起全部保存
                QueryWrapper queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("tenant_id", tenantId);
                queryWrapper.eq("category_id", itemDTO.getCategoryId());
                queryWrapper.eq("datasource_type", itemDTO.getDatasourceType());
                if (!ItemTypeEnum.index.name().equals(itemDTO.getItemType())) {
                    queryWrapper.eq("database_name", itemDTO.getDatabaseName());
                }
                queryWrapper.eq("table_name", itemDTO.getItemName());
                queryWrapper.eq("item_type", "field");
                List<DataDictionaryItem> existFieldlist = dictionaryItemService.list(queryWrapper);
                Set<Long> existItemIds = new HashSet<>();
                if (CollectionUtils.isNotEmpty(existFieldlist)) {
                    Set<Long> itemIds = existFieldlist.stream().map(existField -> existField.getItemId()).collect(Collectors.toSet());
                    existItemIds.addAll(itemIds);
                }
                QueryElementRequest requestDto = new QueryElementRequest();
                requestDto.setTableName(itemDTO.getItemName());
                requestDto.setDbName(itemDTO.getDatabaseName());
                requestDto.setElementType(itemDTO.getAssetType());
                requestDto.setId(itemDTO.getItemId());
                List<QueryElementVo> filedList = dictionaryItemService.queryFiledList(requestDto, tenantId);
                if (CollectionUtils.isNotEmpty(filedList)) {
                    LocalDateTime now = LocalDateTime.now();
                    for (QueryElementVo fieldVo : filedList) {
                        // 已存在的field不需要添加
                        if (existItemIds.contains(fieldVo.getId())) {
                            continue;
                        }
                        DataDictionaryItem fieldItem = new DataDictionaryItem();
                        fieldItem.setDictId(itemDTO.getDictId());
                        fieldItem.setCategoryId(itemDTO.getCategoryId());
                        fieldItem.setItemId(fieldVo.getId());
                        fieldItem.setItemName(fieldVo.getElementName());
                        fieldItem.setItemType(ItemTypeEnum.field.name());
                        fieldItem.setDatasourceType(itemDTO.getDatasourceType());
                        fieldItem.setDatabaseName(itemDTO.getDatabaseName());
                        fieldItem.setFieldType(fieldVo.getFieldType());
                        fieldItem.setAssetPath(fieldVo.getAssetPath());
                        fieldItem.setAssetType(fieldVo.getAssetType());
                        fieldItem.setAssetTypeCode(fieldVo.getAssetTypeCode());
                        fieldItem.setElementId(fieldVo.getElementId());
                        fieldItem.setAssetDataType(fieldVo.getDataType());
                        dictionaryItemService.setCnName(fieldItem, fieldItem.getDatasourceType(), fieldItem.getItemId());
                        fieldItem.setCnDesc(fieldVo.getCnDesc());
                        fieldItem.setEnumValue(fieldVo.getEnumValue());
                        fieldItem.setMappingFields(fieldVo.getMappingFields());
                        fieldItem.setSort(fieldVo.getSort());
                        fieldItem.setIsPrimaryKey(fieldVo.getIsPrimaryKey());

                        fieldItem.setTenantId(tenantId);
                        fieldItem.setCreateTime(now);
                        fieldItem.setUpdateTime(now);
                        fieldItem.setCreateUser(username);
                        fieldItem.setUpdateUser(username);
                        fieldItems.add(fieldItem);
                    }
                }
            }
            items.add(dataDictionaryItem);
        }
        if (CollectionUtils.isNotEmpty(items)) {
            logger.info("新增字典项，数量：{}", items.size());
            dictionaryItemService.saveBatch(items);
        }
        if (CollectionUtils.isNotEmpty(fieldItems)) {
            logger.info("新增字典项关联字段，数量：{}", fieldItems.size());
            dictionaryItemService.saveBatch(fieldItems);
        }
    }

    public void delete(List<Long> ids) {
        Long tenantId = UserUtil.getLongCurrentTenantId();
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        List<DataDictionaryItem> items = dictionaryItemService.list(new QueryWrapper<DataDictionaryItem>().in("id", ids).isNotNull(
                "data_mart_asset_id"));
        if (CollectionUtils.isNotEmpty(items)) {
            throw new ParamInvalidException("存在已发布的数据，无法删除");
        }
        List<DataDictionaryItem> itemList = dictionaryItemService.list(new QueryWrapper<DataDictionaryItem>().in("id", ids)
                .eq("tenant_id", tenantId));
        List<DataDictionaryItem> tableList = itemList.stream().filter(item -> item.getItemType().equals("table")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tableList)) {
            // 如果删除表，则将表里的字段一起删除
            Set<Long> existIds = new HashSet<>();
            for (DataDictionaryItem table : tableList) {
                List<DataDictionaryItem> existFieldlist = dictionaryItemService.list(new QueryWrapper<DataDictionaryItem>()
                        .eq("tenant_id", tenantId).eq("category_id", table.getCategoryId())
                        .eq("datasource_type", table.getDatasourceType()).eq("database_name", table.getDatabaseName())
                        .eq("table_name", table.getItemName()).eq("item_type", "field"));
                if (CollectionUtils.isNotEmpty(existFieldlist)) {
                    Set<Long> itemIds = existFieldlist.stream().map(existField -> existField.getId()).collect(Collectors.toSet());
                    existIds.addAll(itemIds);
                }
            }
            if (CollectionUtils.isNotEmpty(existIds)) {
                logger.info("删除关联字段id：{}", existIds);
                dictionaryItemService.remove(new QueryWrapper<DataDictionaryItem>().in("id", existIds).eq("tenant_id", tenantId));

            }
        }
        dictionaryItemService.remove(new QueryWrapper<DataDictionaryItem>().in("id", ids).eq("tenant_id", tenantId));
    }

    public void edit(DataDictionaryItemDTO dto) {
        if (ObjectUtils.isEmpty(dto.getId())) {
            throw new ParamInvalidException("入参异常");
        }

        DataDictionaryItem dbOne = dictionaryItemService.getById(dto.getId());
        ReflectionUtil.copyLomBokProperties(dto, dbOne);
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        dictionaryItemService.saveOrUpdate(dbOne);
    }

    public DataDictionaryItem detail(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        DataDictionaryItem item = dictionaryItemService.getById(id);

        List<DataMetaTagAndTypeModel> metaTagList =
                metaTagMasterService.selectTagByItemUniqueIds(Collections.singletonList(MetaDataUtil.getTableUniqueId(item.getElementId(),
                        item.getDatasourceType(), item.getItemType(), item.getDatabaseName(), item.getItemName())), item.getTenantId());
        if (CollectionUtils.isNotEmpty(metaTagList)) {
            item.setMetaTagList(metaTagList);
        }
        item.setBusinessSectorName(item.getBusinessSectorId() == null ? "" :
                dictionaryItemMapper.queryBusinessSectorName(item.getBusinessSectorId()));
        item.setDataDomainName(item.getDataDomainId() == null ? "" : dictionaryItemMapper.queryDataDomainName(item.getDataDomainId()));
        item.setDwLevelName(item.getDwLevelId() == null ? "" : dictionaryItemMapper.queryDwLevelName(item.getDwLevelId()));
        item.setBusinessProcessName(item.getBusinessProcessId() == null ? "" :
                dictionaryItemMapper.queryBusinessProcessName(item.getBusinessProcessId()));
        item.setStorageSize(NumberUtils.formatSize(item.getDataStorageSize()));

        // 查询质量任务id
        Long taskId = dictionaryItemMapper.queryTaskId(item.getElementId(), item.getDatabaseName(), item.getTableName());
        item.setTaskId(taskId);
        return item;
    }

    /**
     * 浏览记录
     *
     * @param item
     */
    private void saveViewRecord(DataDictionaryItem item, Long tenantId) {
        ThreadUtil.execute(() -> {
            DataDictionaryItemViewRecord viewRecord = new DataDictionaryItemViewRecord();
            viewRecord.setDatasourceType(item.getDatasourceType());
            viewRecord.setElementId(item.getElementId());
            viewRecord.setDbName(item.getDatabaseName());
            viewRecord.setTableName(item.getTableName());
            viewRecord.setViewerTenantId(tenantId);
            viewRecord.setCreateTime(LocalDateTime.now());
            itemViewRecordService.save(viewRecord);
        });
    }

    public BasePageResponse<List<QueryElementVo>> queryElementPage(QueryElementRequest requestDto) {
        List<QueryElementVo> list = new ArrayList<>();
        Long tenantId = Long.valueOf(UmsUtils.getUVO().getTenantId());
        requestDto.setTenantId(tenantId);
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        com.github.pagehelper.Page<QueryElementVo> page = null;
        if (ElementTypeEnum.CLICKHOUSE_DB.getCode().equals(requestDto.getElementType())) {
            page = dictionaryItemMapper.queryClickhouseDbPage(requestDto);
        } else if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(requestDto.getElementType())) {
            String maxVersion = dictionaryItemMapper.queryMaxVersion("qua_web_ch_task_result_db", tenantId);
            requestDto.setSnapshootVersion(maxVersion);
            page = dictionaryItemMapper.queryClickhouseTablePage(requestDto);
        } else if (ElementTypeEnum.CLICKHOUSE_FIELD.getCode().equals(requestDto.getElementType())) {
            String maxVersion = dictionaryItemMapper.queryMaxVersion("qua_web_ch_task_result_db", tenantId);
            requestDto.setSnapshootVersion(maxVersion);
            page = dictionaryItemMapper.queryClickhouseFieldPage(requestDto);
            if (!page.isEmpty()) {
                // 查询field_type
                List<QueryElementVo> result = page.getResult();
                Set<String> dbNames = result.stream().map(x -> x.getDbName()).collect(Collectors.toSet());
                List<String> columnNames = result.stream().map(x -> x.getElementName()).collect(Collectors.toList());
                Set<String> tableNames = result.stream().map(x -> x.getTableName()).collect(Collectors.toSet());
                Long elementId = result.get(0).getElementId();
                QueryWrapper<ChTaskResultColumn> wrapper = new QueryWrapper<>();
                wrapper.eq("element_id", elementId).eq("tenant_id", tenantId).eq("snapshoot_version", maxVersion).in("db_name", dbNames).in(
                        "table_name", tableNames).in("column_name", columnNames);
                List<ChTaskResultColumn> taskResultColumns = chTaskResultColumnService.list(wrapper);
                if (CollectionUtils.isNotEmpty(taskResultColumns)) {
                    loop:
                    for (QueryElementVo vo : result) {
                        for (ChTaskResultColumn resultColumn : taskResultColumns) {
                            if (vo.getElementId().equals(resultColumn.getElementId()) && vo.getDbName().equals(resultColumn.getDbName()) && vo.getTableName().equals(resultColumn.getTableName()) && vo.getElementName().equals(resultColumn.getColumnName())) {
                                vo.setFieldType(resultColumn.getType());
                                continue loop;
                            }
                        }
                    }
                }
            }
        } else if (ElementTypeEnum.HIVE_DB.getCode().equals(requestDto.getElementType())) {
            page = dictionaryItemMapper.queryHiveDbPage(requestDto);
        } else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(requestDto.getElementType())) {
            String maxVersion = dictionaryItemMapper.queryMaxVersion("qua_web_hive_task_result_db", tenantId);
            requestDto.setSnapshootVersion(maxVersion);
            page = dictionaryItemMapper.queryHiveTablePage(requestDto);
        } else if (ElementTypeEnum.HIVE_FIELD.getCode().equals(requestDto.getElementType())) {
            String maxVersion = dictionaryItemMapper.queryMaxVersion("qua_web_hive_task_result_db", tenantId);
            requestDto.setSnapshootVersion(maxVersion);
            page = dictionaryItemMapper.queryHiveFieldPage(requestDto);
            if (!page.isEmpty()) {
                // 查询field_type
                List<QueryElementVo> result = page.getResult();
                Set<String> dbNames = result.stream().map(x -> x.getDbName()).collect(Collectors.toSet());
                List<String> columnNames = result.stream().map(x -> x.getElementName()).collect(Collectors.toList());
                Set<String> tableNames = result.stream().map(x -> x.getTableName()).collect(Collectors.toSet());
                Long elementId = result.get(0).getElementId();
                QueryWrapper<QuaWebHiveTaskResultColumn> wrapper = new QueryWrapper<>();
                wrapper.eq("element_id", elementId).eq("tenant_id", tenantId).eq("snapshoot_version", maxVersion).in("db_name", dbNames).in(
                        "table_name", tableNames).in("column_name", columnNames);
                List<QuaWebHiveTaskResultColumn> taskResultColumns = hiveTaskResultColumnService.list(wrapper);
                if (CollectionUtils.isNotEmpty(taskResultColumns)) {
                    loop:
                    for (QueryElementVo vo : result) {
                        for (QuaWebHiveTaskResultColumn resultColumn : taskResultColumns) {
                            if (vo.getElementId().equals(resultColumn.getElementId()) && vo.getDbName().equals(resultColumn.getDbName()) && vo.getTableName().equals(resultColumn.getTableName()) && vo.getElementName().equals(resultColumn.getColumnName())) {
                                vo.setFieldType(resultColumn.getType());
                                continue loop;
                            }
                        }
                    }
                }
            }
        } else if (ElementTypeEnum.MYSQL_DB.getCode().equals(requestDto.getElementType())) {
            page = dictionaryItemMapper.queryMysqlDbPage(requestDto);
        } else if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(requestDto.getElementType())) {
            String maxVersion = dictionaryItemMapper.queryMaxVersion("qua_web_mysql_task_result_db", tenantId);
            requestDto.setSnapshootVersion(maxVersion);
            page = dictionaryItemMapper.queryMysqlTablePage(requestDto);
        } else if (ElementTypeEnum.MYSQL_FIELD.getCode().equals(requestDto.getElementType())) {
            String maxVersion = dictionaryItemMapper.queryMaxVersion("qua_web_mysql_task_result_db", tenantId);
            requestDto.setSnapshootVersion(maxVersion);
            page = dictionaryItemMapper.queryMysqlFieldPage(requestDto);
            if (!page.isEmpty()) {
                // 查询field_type
                List<QueryElementVo> result = page.getResult();
                Set<String> dbNames = result.stream().map(x -> x.getDbName()).collect(Collectors.toSet());
                Set<String> columnNames = result.stream().map(x -> x.getElementName()).collect(Collectors.toSet());
                Set<String> tableNames = result.stream().map(x -> x.getTableName()).collect(Collectors.toSet());
                Long elementId = result.get(0).getElementId();
                QueryWrapper<QuaWebMysqlTaskResultColumn> wrapper = new QueryWrapper<>();
                wrapper.eq("element_id", elementId).eq("tenant_id", tenantId).eq("snapshoot_version", maxVersion).in("db_name", dbNames).in(
                        "table_name", tableNames).in("column_name", columnNames);
                List<QuaWebMysqlTaskResultColumn> taskResultColumns = mysqlTaskResultColumnService.list(wrapper);
                if (CollectionUtils.isNotEmpty(taskResultColumns)) {
                    loop:
                    for (QueryElementVo vo : result) {
                        for (QuaWebMysqlTaskResultColumn resultColumn : taskResultColumns) {
                            if (vo.getElementId().equals(resultColumn.getElementId()) && vo.getDbName().equals(resultColumn.getDbName()) && vo.getTableName().equals(resultColumn.getTableName()) && vo.getElementName().equals(resultColumn.getColumnName())) {
                                vo.setFieldType(resultColumn.getType());
                                continue loop;
                            }
                        }
                    }
                }
            }
        } else if (ElementTypeEnum.ELASTICSEARCH_INDEX.getCode().equals(requestDto.getElementType())) {
            String maxVersion = dictionaryItemMapper.queryMaxVersion("qua_web_es_task_result_index", tenantId);
            requestDto.setSnapshootVersion(maxVersion);
            page = dictionaryItemMapper.queryElasticsearchIndexPage(requestDto);
        } else if (ElementTypeEnum.ELASTICSEARCH_FIELD.getCode().equals(requestDto.getElementType())) {
            String maxVersion = dictionaryItemMapper.queryMaxVersion("qua_web_es_task_result_index", tenantId);
            requestDto.setSnapshootVersion(maxVersion);
            page = dictionaryItemMapper.queryElasticsearchFieldPage(requestDto);
            if (!page.isEmpty()) {
                // 查询field_type
                List<QueryElementVo> result = page.getResult();
                List<String> columnNames = result.stream().map(x -> x.getElementName()).collect(Collectors.toList());
                Set<String> tableNames = result.stream().map(x -> x.getTableName()).collect(Collectors.toSet());
                Long elementId = result.get(0).getElementId();
                QueryWrapper<EsTaskResultField> wrapper = new QueryWrapper<>();
                wrapper.eq("element_id", elementId).eq("tenant_id", tenantId).eq("snapshoot_version", maxVersion).in("index_name",
                        tableNames).in("field_name", columnNames);
                List<EsTaskResultField> taskResultColumns = esTaskResultFieldService.list(wrapper);
                if (CollectionUtils.isNotEmpty(taskResultColumns)) {
                    loop:
                    for (QueryElementVo vo : result) {
                        for (EsTaskResultField resultColumn : taskResultColumns) {
                            if (vo.getElementId().equals(resultColumn.getElementId()) && vo.getTableName().equals(resultColumn.getIndexName()) && vo.getElementName().equals(resultColumn.getFieldName())) {
                                vo.setFieldType(resultColumn.getFieldDataType());
                                continue loop;
                            }
                        }
                    }
                }
            }
        }
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
    }

    public Map<String, Object> allPage(RequestDTO requestDTO) {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<DataDictionaryItem> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataDictionaryItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", dictionaryBaseService.getBaseTenantId());
        if (requestDTO.getParam() != null) {
            DataDictionaryItemDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), DataDictionaryItemDTO.class);
            if (dto.getDictId() != null) {
                queryWrapper.eq("dict_id", dto.getDictId());
            }
            if (dto.getCategoryId() != null) {
                Set<Long> ids = new HashSet<>();
                queryCategoryIdAndChild(dto.getCategoryId(), ids);
                queryWrapper.in("category_id", ids);
            }
            if (StringUtils.isNotBlank(dto.getItemName())) {
                queryWrapper.like("item_name", dto.getItemName());
            }
            if (StringUtils.isNotBlank(dto.getDatasourceType())) {
                queryWrapper.eq("datasource_type", dto.getDatasourceType());
            }
            if (StringUtils.isNotBlank(dto.getItemType())) {
                queryWrapper.eq("item_type", dto.getItemType());
            }
            if (StringUtils.isNotBlank(dto.getColumnNameCn())) {
                queryWrapper.like("column_name_cn", dto.getColumnNameCn());
            }
            if (StringUtils.isNotBlank(dto.getItemDesc())) {
                queryWrapper.like("item_desc", dto.getItemDesc());
            }
            if (StringUtils.isNotBlank(dto.getTableName())) { // 所属表
                queryWrapper.like("table_name", dto.getTableName());
                queryWrapper.apply("table_name != item_name");
            }
        }
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }

        IPage<DataDictionaryItem> pageResult = dictionaryItemService.page(page, queryWrapper);
        List<DataDictionaryItem> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            for (DataDictionaryItem record : records) {
                StringBuilder nameSb = new StringBuilder();
                String joinedCategoryName = joinCategoryName(record.getCategoryId(), nameSb);
                DataDictionaryBase dictionaryBase = dictionaryBaseService.getById(record.getDictId());
                record.setDictNamePath(dictionaryBase.getDictName() + joinedCategoryName);
            }
        }
        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);
        return result;
    }

    /**
     * 拼接当前节点及其父节点名称
     *
     * @param categoryId
     * @param nameSb
     * @return
     */
    private String joinCategoryName(Long categoryId, StringBuilder nameSb) {
        DataDictionaryCategory category = categoryService.getById(categoryId);
        if (category == null) {
            return nameSb.toString();
        }
        nameSb.insert(0, "/" + category.getCategoryName());
        if (category.getParentId() != null) {
            return joinCategoryName(category.getParentId(), nameSb);
        }
        return nameSb.toString();
    }

    /**
     * 查询当前节点及其子节点
     *
     * @param categoryId
     * @param ids
     */
    private void queryCategoryIdAndChild(Long categoryId, Set<Long> ids) {
        ids.add(categoryId);
        List<DataDictionaryCategory> child = categoryService.list(new QueryWrapper<DataDictionaryCategory>().eq("parent_id", categoryId));
        if (CollectionUtils.isEmpty(child)) {
            return;
        }
        for (DataDictionaryCategory childCategory : child) {
            queryCategoryIdAndChild(childCategory.getId(), ids);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void addToDataMart(List<DictDataMartAssetDTO> dtoList) {
        int tenantId = UmsUtils.getUVO().getTenantId();
        for (DictDataMartAssetDTO dto : dtoList) {
            DataDictionaryItem item = dictionaryItemService.getById(dto.getId());
            String dataName = item.getItemName() + DateUtil.current();
            List<DataMartAsset> exits = dataMartAssetService.list(new QueryWrapper<DataMartAsset>().eq("data_name", dataName).eq(
                    "tenant_id", tenantId));
            if (CollectionUtils.isNotEmpty(exits)) {
                throw new ParamInvalidException("资产已存在【" + dataName + "】");
            }
            DataMartAsset dataMartAsset = new DataMartAsset();
            dataMartAsset.setDataName(dataName);
            dataMartAsset.setAssetDesc(dto.getDesc());
            dataMartAsset.setStartTime(dto.getStartTime());
            dataMartAsset.setEndTime(dto.getEndTime());
            dataMartAsset.setTypeId(dto.getTypeId());
            dataMartAsset.setGroupId(dto.getGroupId());

            com.idss.datalake.datamart.dto.response.QueryElementVo elementVo = new com.idss.datalake.datamart.dto.response.QueryElementVo();
            elementVo.setId(item.getItemId());
            elementVo.setElementName(item.getItemName());
            elementVo.setElementType(item.getDatasourceType());
            elementVo.setDataType(item.getAssetDataType());
            elementVo.setElementId(item.getElementId());
            elementVo.setAssetPath(item.getAssetPath());
            elementVo.setAssetType(item.getAssetType());
            elementVo.setAssetTypeCode(item.getAssetTypeCode());

            dataMartAsset.setAssetType(item.getAssetType());
            dataMartAsset.setAssetTypeCode(item.getAssetTypeCode());
            dataMartAsset.setAssetJson(JSONUtil.objectToJson(ListUtil.of(elementVo)));
            dataMartAsset.setSubscribeNum(0);
            dataMartAsset.setThumbsUpNum(0);
            dataMartAsset.setReleaseStatus(false);
            dataMartAsset.setCreateUser(UmsUtils.getUVO().getUserName());
            dataMartAsset.setTenantId(tenantId);
            dataMartAsset.setDbName(item.getDatabaseName());
            dataMartAsset.setTableName(item.getTableName());

            // 数据入库
            dataMartAssetService.save(dataMartAsset);
            // 更新字典表中资产id
            dictionaryItemService.update(new UpdateWrapper<DataDictionaryItem>().eq("id", item.getId()).set("data_mart_asset_id",
                    dataMartAsset.getId()));
        }
    }

    public static void main(String[] args) {
        String itemType = "index";
        System.out.println(!StringUtils.equalsAny(itemType, ItemTypeEnum.table.name(), ItemTypeEnum.index.name()));
    }

    /**
     * 根据表查询数据字典里关联的字段
     *
     * @param requestDTO
     */
    public Map<String, Object> queryDetailColumnPage(RequestDTO requestDTO) {
        Map<String, Object> result = new HashMap<>();
        DataDictionaryItemDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), DataDictionaryItemDTO.class);
        DataDictionaryItem item = dictionaryItemService.getById(dto.getId());
        if (item == null || !StringUtils.equalsAny(item.getItemType(), ItemTypeEnum.table.name(), ItemTypeEnum.index.name())) {
            return result;
        }

        Page<DataDictionaryItem> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataDictionaryItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("element_id", item.getElementId());
        queryWrapper.eq("datasource_type", item.getDatasourceType());
        if (item.getCategoryId() != null) {
            queryWrapper.eq("category_id", item.getCategoryId());
        }
        if (StringUtils.isNotBlank(dto.getDatabaseName())) {
            queryWrapper.eq("database_name", item.getDatabaseName());
        }
        queryWrapper.eq("table_name", item.getItemName());
        queryWrapper.eq("item_type", "field");
        if (StringUtils.isNotBlank(dto.getItemName())) {
            queryWrapper.like("item_name", dto.getItemName());
        }
        if (StringUtils.isNotBlank(dto.getFieldType())) {
            queryWrapper.like("field_type", dto.getFieldType());
        }
        if (dto.getIsRequired() != null) {
            queryWrapper.eq("is_required", dto.getIsRequired());
        }
        if (dto.getIsPrimaryKey() != null) {
            queryWrapper.eq("is_primary_key", dto.getIsPrimaryKey());
        }
        queryWrapper.orderByAsc("sort");
        IPage<DataDictionaryItem> pageResult = dictionaryItemService.itemAssetPage(page, queryWrapper);
        List<DataDictionaryItem> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            Map<Long, String> senTypeMap = getSenTypeMap();
            Map<Long, String> senLevelMap = getSenLevelMap();
            Map<Long, String> desensitizationMap = getDesensitization();
            for (DataDictionaryItem record : records) {
                if (record.getSenTypeId() != null) {
                    record.setSenTypeName(senTypeMap.get(record.getSenTypeId()));
                }
                if (record.getSenLevelId() != null) {
                    record.setSenLevelName(senLevelMap.get(record.getSenLevelId()));
                }
                if (record.getDesensitizationId() != null) {
                    record.setDesensitizationName(desensitizationMap.get(record.getDesensitizationId()));
                }
            }
        }

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);

        saveViewRecord(item, UserUtil.getLongCurrentTenantId());
        return result;
    }

    private Map<Long, String> getSenTypeMap() {
        Map<Long, String> senTypeMap = new HashMap<>();
        List<UebaDictionary> senType = dataEncryptionAlgorithmService.getSenType();
        for (UebaDictionary uebaDictionary : senType) {
            senTypeMap.put(uebaDictionary.getId(), uebaDictionary.getValue());
        }
        return senTypeMap;
    }

    private Map<Long, String> getSenLevelMap() {
        Map<Long, String> senLevelMap = new HashMap<>();
        List<UebaDictionary> senLevel = dataEncryptionAlgorithmService.getSenLevel();
        for (UebaDictionary uebaDictionary : senLevel) {
            senLevelMap.put(uebaDictionary.getId(), uebaDictionary.getValue());
        }
        return senLevelMap;
    }

    /**
     * 获取脱敏算法
     *
     * @return
     */
    private Map<Long, String> getDesensitization() {
        Map<Long, String> desensitizationMap = new HashMap<>();
        List<Map<String, Object>> list = jobService.listDesensitization();
        for (Map<String, Object> map : list) {
            Long id = Long.valueOf(map.get("id").toString());
            desensitizationMap.put(id, map.get("name").toString());
        }
        return desensitizationMap;
    }

    /**
     * 更新所有的字典项
     */
    public void updateItems() {
        UserValueObject uvo = UmsUtils.getUVO();
        String userName = uvo.getUserName();
        Long tenantId = Long.valueOf(uvo.getTenantId());

        // 1. 查询所有的字典项
        List<DataDictionaryItem> itemList = dictionaryItemService.list();
        if (CollectionUtils.isEmpty(itemList)) {
            logger.info("暂无字典项，无需更新");
            return;
        }
        Map<Long, List<DataDictionaryItem>> elementMap =
                itemList.parallelStream().collect(Collectors.groupingBy(DataDictionaryItem::getElementId));
        for (Map.Entry<Long, List<DataDictionaryItem>> entry : elementMap.entrySet()) {
            Long elementId = entry.getKey();
            // 2. 查询最新已完成的采集的快照版本
            QuaTask quaTask = dictionaryItemMapper.queryLatestTaskByElementId(elementId);
            if (quaTask == null) {
                continue;
            }
            List<DataDictionaryItem> items = entry.getValue();
            // 对items按照更新时间进行排序并获取最早的一条数据
            DataDictionaryItem earliestItem = items.stream().min(Comparator.comparing(DataDictionaryItem::getUpdateTime)).get();
            Date earliestDate = DateUtils.LocalDateTime2Date(earliestItem.getUpdateTime());
            String snapshootVersion = quaTask.getSnapshootVersion();
            DateTime snapshootDate = DateUtil.parse(snapshootVersion);
            if (DateUtil.compare(earliestDate, snapshootDate) > 0) { // 如果最早一条的时间大于快照时间，即字典项生成在快照之后，说明最新没有新采集任务，无需更新数据
                continue;
            }
            logger.info("开始正式更新,elementId:{}", elementId);
            String datasourceType = items.get(0).getDatasourceType();
            if (DATA_SOURCE_TYPE_ENUM.MYSQL.getName().equalsIgnoreCase(datasourceType)) {
                updateMysqlField(items, elementId, snapshootVersion, userName);
                updateMysqlTable(items, elementId, snapshootVersion, userName, tenantId);
            } else if (DATA_SOURCE_TYPE_ENUM.HIVE.getName().equalsIgnoreCase(datasourceType)) {
                updateHiveField(items, elementId, snapshootVersion, userName);
                updateHiveTable(items, elementId, snapshootVersion, userName, tenantId);
            } else if (DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().equalsIgnoreCase(datasourceType)) {
                updateClickHouseField(items, elementId, snapshootVersion, userName);
                updateClickHouseTable(items, elementId, snapshootVersion, userName, tenantId);
            } else if (DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().equalsIgnoreCase(datasourceType)) {
                updateEsField(items, elementId, snapshootVersion, userName);
                updateEsIndex(items, elementId, snapshootVersion, userName, tenantId);
            }
            logger.info("更新完成,elementId:{}", elementId);
        }
    }

    private void updateMysqlTable(List<DataDictionaryItem> items, Long elementId, String snapshootVersion, String userName, Long tenantId) {
        List<DataDictionaryItem> tableItemList =
                items.stream().filter(x -> x.getItemType().equalsIgnoreCase(ItemTypeEnum.table.name())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tableItemList)) {
            for (DataDictionaryItem tableItem : tableItemList) {
                QuaWebMysqlElementDetailTable latestOne = getMysqlElementDetailTable(elementId, snapshootVersion, tableItem.getDatabaseName(),
                        tableItem.getItemName());
                if (latestOne == null) { // 没查到说明当前数据原始值已被删除，需要从字典表里删除
                    if (tableItem.getDataMartAssetId() != null) { // 若被发布到数据集市，也需要同步删除
                        dataMartAssetService.delete(tableItem.getDataMartAssetId());
                    }
                    dictionaryItemService.removeById(tableItem.getId());
                } else { // 更新相关数据
                    updateItemTable(userName, tenantId, tableItem, latestOne.getTableDscribe(), latestOne.getTableNameCn());
                }
            }
        }
    }

    private QuaWebMysqlElementDetailTable getMysqlElementDetailTable(Long elementId, String snapshootVersion, String databaseName, String tableName) {
        QuaWebMysqlElementDetailTable latestOne =
                mysqlElementDetailTableService.getOne(new QueryWrapper<QuaWebMysqlElementDetailTable>()
                        .eq("element_id", elementId)
                        .eq("db_name", databaseName)
                        .eq("table_name", tableName)
                        .eq("last_snapshoot_version", snapshootVersion));
        return latestOne;
    }

    private void updateClickHouseTable(List<DataDictionaryItem> items, Long elementId, String snapshootVersion, String userName,
                                       Long tenantId) {
        List<DataDictionaryItem> tableItemList =
                items.stream().filter(x -> x.getItemType().equalsIgnoreCase(ItemTypeEnum.table.name())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tableItemList)) {
            for (DataDictionaryItem tableItem : tableItemList) {
                ChElementDetailTable latestOne = getChElementDetailTable(elementId, snapshootVersion, tableItem.getDatabaseName(),
                        tableItem.getItemName());
                if (latestOne == null) { // 没查到说明当前数据原始值已被删除，需要从字典表里删除
                    if (tableItem.getDataMartAssetId() != null) { // 若被发布到数据集市，也需要同步删除
                        dataMartAssetService.delete(tableItem.getDataMartAssetId());
                    }
                    dictionaryItemService.removeById(tableItem.getId());
                } else { // 更新相关数据
                    updateItemTable(userName, tenantId, tableItem, latestOne.getTableDscribe(), latestOne.getTableNameCn());
                }
            }
        }
    }

    private ChElementDetailTable getChElementDetailTable(Long elementId, String snapshootVersion, String databaseName, String tableName) {
        ChElementDetailTable latestOne =
                chElementDetailTableService.getOne(new QueryWrapper<ChElementDetailTable>()
                        .eq("element_id", elementId)
                        .eq("db_name", databaseName)
                        .eq("table_name", tableName)
                        .eq("last_snapshoot_version", snapshootVersion));
        return latestOne;
    }

    private void updateHiveTable(List<DataDictionaryItem> items, Long elementId, String snapshootVersion, String userName, Long tenantId) {
        List<DataDictionaryItem> tableItemList =
                items.stream().filter(x -> x.getItemType().equalsIgnoreCase(ItemTypeEnum.table.name())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tableItemList)) {
            for (DataDictionaryItem tableItem : tableItemList) {
                QuaWebHiveElementDetailTable latestOne = getHiveElementDetailTable(elementId, snapshootVersion, tableItem.getDatabaseName(),
                        tableItem.getItemName());
                if (latestOne == null) { // 没查到说明当前数据原始值已被删除，需要从字典表里删除
                    if (tableItem.getDataMartAssetId() != null) { // 若被发布到数据集市，也需要同步删除
                        dataMartAssetService.delete(tableItem.getDataMartAssetId());
                    }
                    dictionaryItemService.removeById(tableItem.getId());
                } else { // 更新相关数据
                    updateItemTable(userName, tenantId, tableItem, latestOne.getTableDscribe(), latestOne.getTableNameCn());
                }
            }
        }
    }

    private QuaWebHiveElementDetailTable getHiveElementDetailTable(Long elementId, String snapshootVersion, String databaseName, String tableName) {
        QuaWebHiveElementDetailTable latestOne =
                hiveElementDetailTableService.getOne(new QueryWrapper<QuaWebHiveElementDetailTable>()
                        .eq("element_id", elementId)
                        .eq("db_name", databaseName)
                        .eq("table_name", tableName)
                        .eq("last_snapshoot_version", snapshootVersion));
        return latestOne;
    }

    private void updateEsIndex(List<DataDictionaryItem> items, Long elementId, String snapshootVersion, String userName, Long tenantId) {
        List<DataDictionaryItem> indexItemList =
                items.stream().filter(x -> x.getItemType().equalsIgnoreCase(ItemTypeEnum.index.name())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(indexItemList)) {
            for (DataDictionaryItem tableItem : indexItemList) {
                EsElementDetailIndex latestOne = getEsElementDetailIndex(elementId, snapshootVersion, tableItem.getItemName());
                if (latestOne == null) { // 没查到说明当前数据原始值已被删除，需要从字典表里删除
                    if (tableItem.getDataMartAssetId() != null) { // 若被发布到数据集市，也需要同步删除
                        dataMartAssetService.delete(tableItem.getDataMartAssetId());
                    }
                    dictionaryItemService.removeById(tableItem.getId());
                } else { // 更新相关数据
                    updateItemTable(userName, tenantId, tableItem, latestOne.getIndexDscribe(), latestOne.getIndexNameCn());
                }
            }
        }
    }

    private EsElementDetailIndex getEsElementDetailIndex(Long elementId, String snapshootVersion, String tableName) {
        EsElementDetailIndex latestOne =
                esElementDetailIndexService.getOne(new QueryWrapper<EsElementDetailIndex>()
                        .eq("element_id", elementId)
                        .eq("index_name", tableName)
                        .eq("last_snapshoot_version", snapshootVersion));
        return latestOne;
    }

    /**
     * 更新item数据
     *
     * @param userName
     * @param tenantId
     * @param tableItem
     * @param tableDscribe
     * @param tableNameCn
     */
    private void updateItemTable(String userName, Long tenantId, DataDictionaryItem tableItem, String tableDscribe, String tableNameCn) {
        tableItem.setItemDesc(tableDscribe);
        tableItem.setColumnNameCn(tableNameCn);
        tableItem.setUpdateTime(LocalDateTime.now());
        tableItem.setUpdateUser(userName);
        dictionaryItemService.updateById(tableItem);

        // 查询出原始表所有字段，与当前数据字典数据做判断，如果不存在，则说明是新字段，需要保存
        QueryElementRequest requestDto = new QueryElementRequest();
        requestDto.setTableName(tableItem.getItemName());
        requestDto.setDbName(tableItem.getDatabaseName());
        requestDto.setElementType(tableItem.getAssetType());
        requestDto.setId(tableItem.getItemId());
        List<QueryElementVo> filedList = dictionaryItemService.queryFiledList(requestDto, tenantId);
        if (CollectionUtils.isNotEmpty(filedList)) {
            LocalDateTime now = LocalDateTime.now();
            List<DataDictionaryItem> fieldItems = new ArrayList<>();
            for (QueryElementVo fieldVo : filedList) {
                // 已存在的field不需要添加
                int count = dictionaryItemService.count(new QueryWrapper<DataDictionaryItem>()
                        .eq("tenant_id", tenantId)
                        .eq("category_id", tableItem.getCategoryId())
                        .eq("datasource_type", tableItem.getDatasourceType())
                        .eq("database_name", tableItem.getDatabaseName())
                        .eq("table_name", tableItem.getItemName())
                        .eq("item_name", fieldVo.getElementName())
                        .eq("item_type", "field"));
                if (count > 0) {
                    continue;
                }
                DataDictionaryItem fieldItem = new DataDictionaryItem();
                fieldItem.setDictId(tableItem.getDictId());
                fieldItem.setCategoryId(tableItem.getCategoryId());
                fieldItem.setItemId(fieldVo.getId());
                fieldItem.setItemName(fieldVo.getElementName());
                fieldItem.setItemType(fieldVo.getAssetTypeCode().toLowerCase(Locale.ROOT));
                fieldItem.setDatasourceType(tableItem.getDatasourceType());
                fieldItem.setDatabaseName(tableItem.getDatabaseName());
                fieldItem.setFieldType(fieldVo.getFieldType());
                fieldItem.setAssetPath(fieldVo.getAssetPath());
                fieldItem.setAssetType(fieldVo.getAssetType());
                fieldItem.setAssetTypeCode(fieldVo.getAssetTypeCode());
                fieldItem.setElementId(fieldVo.getElementId());
                fieldItem.setAssetDataType(fieldVo.getDataType());
                dictionaryItemService.setCnName(fieldItem, fieldItem.getDatasourceType(), fieldItem.getItemId());

                fieldItem.setTenantId(tenantId);
                fieldItem.setCreateTime(now);
                fieldItem.setUpdateTime(now);
                fieldItem.setCreateUser(userName);
                fieldItem.setUpdateUser(userName);

                fieldItem.setCnDesc(fieldVo.getCnDesc());
                fieldItem.setEnumValue(fieldVo.getEnumValue());
                fieldItem.setMappingFields(fieldVo.getMappingFields());
                fieldItem.setSort(fieldVo.getSort());

                fieldItems.add(fieldItem);
            }
            if (CollectionUtils.isNotEmpty(fieldItems)) {
                logger.info("新增字典项[{}]关联字段，数量：{}", tableItem.getItemName(), fieldItems.size());
                dictionaryItemService.saveBatch(fieldItems);
            }
        }
    }

    private void updateClickHouseField(List<DataDictionaryItem> items, Long elementId, String snapshootVersion, String userName) {
        List<DataDictionaryItem> fieldItemList =
                items.stream().filter(x -> x.getItemType().equalsIgnoreCase(ItemTypeEnum.field.name())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fieldItemList)) {
            for (DataDictionaryItem fieldItem : fieldItemList) {
                ChElementDetailColumn latestOne = getChElementDetailColumn(elementId, snapshootVersion, fieldItem.getDatabaseName(),
                        fieldItem.getTableName(), fieldItem.getItemName());
                if (latestOne == null) { // 没查到说明当前数据原始值已被删除，需要从字典表里删除
                    if (fieldItem.getDataMartAssetId() != null) { // 若被发布到数据集市，也需要同步删除
                        dataMartAssetService.delete(fieldItem.getDataMartAssetId());
                    }
                    dictionaryItemService.removeById(fieldItem.getId());
                } else { // 更新相关数据
                    // fieldItem.setItemDesc(latestOne.getColumnNameCn());
                    fieldItem.setColumnNameCn(latestOne.getColumnNameCn());
                    fieldItem.setUpdateTime(LocalDateTime.now());
                    fieldItem.setUpdateUser(userName);

                    fieldItem.setCnDesc(latestOne.getCnDesc());
                    fieldItem.setEnumValue(latestOne.getEnumValue());
                    fieldItem.setMappingFields(latestOne.getMappingFields());
                    fieldItem.setSort(latestOne.getSort());
                    dictionaryItemService.updateById(fieldItem);
                }
            }
        }
    }

    private ChElementDetailColumn getChElementDetailColumn(Long elementId, String snapshootVersion, String databaseName, String tableName,
                                                           String fieldName) {
        ChElementDetailColumn latestOne =
                chElementDetailColumnService.getOne(new QueryWrapper<ChElementDetailColumn>()
                        .eq("element_id", elementId)
                        .eq("db_name", databaseName)
                        .eq("table_name", tableName)
                        .eq("column_name", fieldName)
                        .eq("last_snapshoot_version", snapshootVersion));
        return latestOne;
    }

    private void updateHiveField(List<DataDictionaryItem> items, Long elementId, String snapshootVersion, String userName) {
        List<DataDictionaryItem> fieldItemList =
                items.stream().filter(x -> x.getItemType().equalsIgnoreCase(ItemTypeEnum.field.name())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fieldItemList)) {
            for (DataDictionaryItem fieldItem : fieldItemList) {
                QuaWebHiveElementDetailColumn latestOne = getHiveElementDetailColumn(elementId, snapshootVersion, fieldItem.getDatabaseName(),
                        fieldItem.getTableName(), fieldItem.getItemName());
                if (latestOne == null) { // 没查到说明当前数据原始值已被删除，需要从字典表里删除
                    if (fieldItem.getDataMartAssetId() != null) { // 若被发布到数据集市，也需要同步删除
                        dataMartAssetService.delete(fieldItem.getDataMartAssetId());
                    }
                    dictionaryItemService.removeById(fieldItem.getId());
                } else { // 更新相关数据
                    // fieldItem.setItemDesc(latestOne.getColumnNameCn());
                    fieldItem.setColumnNameCn(latestOne.getColumnNameCn());
                    fieldItem.setUpdateTime(LocalDateTime.now());
                    fieldItem.setUpdateUser(userName);

                    fieldItem.setCnDesc(latestOne.getCnDesc());
                    fieldItem.setEnumValue(latestOne.getEnumValue());
                    fieldItem.setMappingFields(latestOne.getMappingFields());
                    fieldItem.setSort(latestOne.getSort());
                    dictionaryItemService.updateById(fieldItem);
                }
            }
        }
    }

    private QuaWebHiveElementDetailColumn getHiveElementDetailColumn(Long elementId, String snapshootVersion, String databaseName, String tableName
            , String fieldName) {
        QuaWebHiveElementDetailColumn latestOne =
                hiveElementDetailColumnService.getOne(new QueryWrapper<QuaWebHiveElementDetailColumn>()
                        .eq("element_id", elementId)
                        .eq("db_name", databaseName)
                        .eq("table_name", tableName)
                        .eq("column_name", fieldName)
                        .eq("last_snapshoot_version", snapshootVersion));
        return latestOne;
    }

    private void updateMysqlField(List<DataDictionaryItem> items, Long elementId, String snapshootVersion, String userName) {
        List<DataDictionaryItem> fieldItemList =
                items.stream().filter(x -> x.getItemType().equalsIgnoreCase(ItemTypeEnum.field.name())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fieldItemList)) {
            for (DataDictionaryItem fieldItem : fieldItemList) {
                QuaWebMysqlElementDetailColumn latestOne = getMysqlElementDetailColumn(elementId, snapshootVersion, fieldItem.getDatabaseName(),
                        fieldItem.getTableName(), fieldItem.getItemName());
                if (latestOne == null) { // 没查到说明当前数据原始值已被删除，需要从字典表里删除
                    if (fieldItem.getDataMartAssetId() != null) { // 若被发布到数据集市，也需要同步删除
                        dataMartAssetService.delete(fieldItem.getDataMartAssetId());
                    }
                    dictionaryItemService.removeById(fieldItem.getId());
                } else { // 更新相关数据
                    // fieldItem.setItemDesc(latestOne.getColumnNameCn());
                    fieldItem.setColumnNameCn(latestOne.getColumnNameCn());
                    fieldItem.setUpdateTime(LocalDateTime.now());
                    fieldItem.setUpdateUser(userName);

                    fieldItem.setCnDesc(latestOne.getCnDesc());
                    fieldItem.setEnumValue(latestOne.getEnumValue());
                    fieldItem.setMappingFields(latestOne.getMappingFields());
                    fieldItem.setSort(latestOne.getSort());
                    dictionaryItemService.updateById(fieldItem);
                }
            }
        }
    }

    private QuaWebMysqlElementDetailColumn getMysqlElementDetailColumn(Long elementId, String snapshootVersion, String databaseName, String tableName,
                                                                       String fieldName) {
        QuaWebMysqlElementDetailColumn latestOne =
                mysqlElementDetailColumnService.getOne(new QueryWrapper<QuaWebMysqlElementDetailColumn>()
                        .eq("element_id", elementId)
                        .eq("db_name", databaseName)
                        .eq("table_name", tableName)
                        .eq("column_name", fieldName)
                        .eq("last_snapshoot_version", snapshootVersion));
        return latestOne;
    }

    private void updateEsField(List<DataDictionaryItem> items, Long elementId, String snapshootVersion, String userName) {
        List<DataDictionaryItem> fieldItemList =
                items.stream().filter(x -> x.getItemType().equalsIgnoreCase(ItemTypeEnum.field.name())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fieldItemList)) {
            for (DataDictionaryItem fieldItem : fieldItemList) {
                EsElementDetailField latestOne = getEsElementDetailField(elementId, snapshootVersion, fieldItem.getTableName(),
                        fieldItem.getItemName());
                if (latestOne == null) { // 没查到说明当前数据原始值已被删除，需要从字典表里删除
                    if (fieldItem.getDataMartAssetId() != null) { // 若被发布到数据集市，也需要同步删除
                        dataMartAssetService.delete(fieldItem.getDataMartAssetId());
                    }
                    dictionaryItemService.removeById(fieldItem.getId());
                } else { // 更新相关数据
                    // fieldItem.setItemDesc(latestOne.getFieldNameCn());
                    fieldItem.setColumnNameCn(latestOne.getFieldNameCn());
                    fieldItem.setUpdateTime(LocalDateTime.now());
                    fieldItem.setUpdateUser(userName);

                    fieldItem.setCnDesc(latestOne.getCnDesc());
                    fieldItem.setEnumValue(latestOne.getEnumValue());
                    fieldItem.setMappingFields(latestOne.getMappingFields());
                    fieldItem.setSort(latestOne.getSort());
                    dictionaryItemService.updateById(fieldItem);
                }
            }
        }
    }

    private EsElementDetailField getEsElementDetailField(Long elementId, String snapshootVersion, String tableName, String fieldName) {
        EsElementDetailField latestOne =
                esElementDetailFieldService.getOne(new QueryWrapper<EsElementDetailField>()
                        .eq("element_id", elementId)
                        .eq("index_name", tableName)
                        .eq("field_name", fieldName)
                        .eq("last_snapshoot_version", snapshootVersion));
        return latestOne;
    }

    /**
     * 表指标项查询概览
     *
     * @param requestDTO
     * @return
     */
    public Map<String, Object> pageWithTag(RequestDTO requestDTO) {
        Map<String, Object> result = new HashMap<>();
        Long tenantId = UserUtil.getLongCurrentTenantId();
        DataDictionaryItemDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), DataDictionaryItemDTO.class);
        dto.setTenantId(tenantId);
        dto.setPageSize(requestDTO.getGlobal().getPageSize());
        dto.setOffset((requestDTO.getGlobal().getPageNum() - 1) * requestDTO.getGlobal().getPageSize());

        Long total = dictionaryItemService.pageWithTagCount(dto);
        List<DataDictionaryItem> records = new ArrayList<>();
        if (total > 0) {
            records = dictionaryItemService.pageWithTag(dto);
            // 查询标签
            for (DataDictionaryItem record : records) {
                if (StringUtils.isNotBlank(record.getMetaTagIds())) {
                    List<Long> tagIds = Arrays.asList(record.getMetaTagIds().split(",")).stream().map(x -> Long.parseLong(x))
                            .collect(Collectors.toList());
                    record.setMetaTagList(metaTagMasterService.selectTagByIds(tagIds, tenantId));
                }
            }
        }

        result.put("pageNum", requestDTO.getGlobal().getPageNum());
        result.put("pageSize", requestDTO.getGlobal().getPageSize());
        result.put("total", total);
        result.put("data", records);
        return result;
    }

    public List<DataDictionaryItemTableTrend> tableTrend(Long id) {
        List<DataDictionaryItemTableTrend> list = itemTableTrendService.list(new QueryWrapper<DataDictionaryItemTableTrend>().eq("item_id", id)
                .orderByDesc("create_time").last("limit 30"));
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().sorted(Comparator.comparing(DataDictionaryItemTableTrend::getCreateTime).reversed())
                    .collect(Collectors.toList());
        }
        return null;
    }

    public Node queryTableNode(Long elementId, String datasourceType, String databaseName, String tableName) {
        if (DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().equalsIgnoreCase(datasourceType)) {
            return queryNode(elementId, ItemTypeEnum.index.name(), datasourceType, databaseName, tableName, null);
        }
        return queryNode(elementId, ItemTypeEnum.table.name(), datasourceType, databaseName, tableName, null);
    }

    public Node queryNode(Long elementId, String itemType, String datasourceType, String databaseName, String tableName, String fieldName) {
        // 2. 查询最新已完成的采集的快照版本
        QuaTask quaTask = dictionaryItemMapper.queryLatestTaskByElementId(elementId);
        if (quaTask == null) {
            return null;
        }
        Node node = new Node();
        String snapshootVersion = quaTask.getSnapshootVersion();
        if (DATA_SOURCE_TYPE_ENUM.MYSQL.getName().equalsIgnoreCase(datasourceType)) {
            if (itemType.equals(ItemTypeEnum.table.name())) {
                QuaWebMysqlElementDetailTable table = getMysqlElementDetailTable(elementId, snapshootVersion, databaseName, tableName);
                if (table == null) {
                    return null;
                }
                node.setName(table.getTableName());
                node.setCnName(table.getTableNameCn());
            } else {
                QuaWebMysqlElementDetailColumn column = getMysqlElementDetailColumn(elementId, snapshootVersion, databaseName, tableName, fieldName);
                if (column == null) {
                    return null;
                }
                node.setName(column.getColumnName());
                node.setCnName(column.getColumnNameCn());
            }
            return node;
        } else if (DATA_SOURCE_TYPE_ENUM.HIVE.getName().equalsIgnoreCase(datasourceType)) {
            if (itemType.equals(ItemTypeEnum.table.name())) {
                QuaWebHiveElementDetailTable table = getHiveElementDetailTable(elementId, snapshootVersion, databaseName, tableName);
                if (table == null) {
                    return null;
                }
                node.setName(table.getTableName());
                node.setCnName(table.getTableNameCn());
            } else {
                QuaWebHiveElementDetailColumn column = getHiveElementDetailColumn(elementId, snapshootVersion, databaseName, tableName, fieldName);
                if (column == null) {
                    return null;
                }
                node.setName(column.getColumnName());
                node.setCnName(column.getColumnNameCn());
            }
            return node;
        } else if (DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().equalsIgnoreCase(datasourceType)) {
            if (itemType.equals(ItemTypeEnum.table.name())) {
                ChElementDetailTable table = getChElementDetailTable(elementId, snapshootVersion, databaseName, tableName);
                if (table == null) {
                    return null;
                }
                node.setName(table.getTableName());
                node.setCnName(table.getTableNameCn());
            } else {
                ChElementDetailColumn column = getChElementDetailColumn(elementId, snapshootVersion, databaseName, tableName, fieldName);
                if (column == null) {
                    return null;
                }
                node.setName(column.getColumnName());
                node.setCnName(column.getColumnNameCn());
            }
            return node;
        } else if (DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().equalsIgnoreCase(datasourceType)) {
            if (itemType.equals(ItemTypeEnum.index.name())) {
                EsElementDetailIndex index = getEsElementDetailIndex(elementId, snapshootVersion, tableName);
                if (index == null) {
                    return null;
                }
                node.setName(index.getIndexName());
                node.setCnName(index.getIndexNameCn());
            } else {
                EsElementDetailField field = getEsElementDetailField(elementId, snapshootVersion, tableName, fieldName);
                if (field == null) {
                    return null;
                }
                node.setName(field.getFieldName());
                node.setCnName(field.getFieldNameCn());
                return node;
            }
        }
        return null;
    }

    /**
     * 预览数据
     *
     * @return
     */
    public ResultBean previewData(Long itemId) throws Exception {
        logger.info("开始预览数据, itemId: {}", itemId);
        DataDictionaryItem dictionaryItem = dictionaryItemService.getById(itemId);
        List<DbDataInfo> result = new ArrayList<>();
        ItemPreviewRequest req = new ItemPreviewRequest();
        String assetType = dictionaryItem.getAssetType();
        String[] assetPath = dictionaryItem.getAssetPath().split(",");
        if (ElementTypeEnum.isTable(assetType)) {
            req.setDbName(assetPath[0]);
            req.setTableName(assetPath[1]);
        } else if (ElementTypeEnum.isIndex(assetType)) {
            req.setIndexName(dictionaryItem.getItemName());
        } else {
            // 不是表或者索引直接退出
            return ResultBean.success(result);
        }
        req.setAssetType(assetType);
        req.setElementId(dictionaryItem.getElementId());
        DbDataInfo dbDataInfo = queryData(req);
        if (dbDataInfo != null) {
            dbDataInfo.setTableName(StringUtils.isNotBlank(req.getTableName()) ? req.getTableName() : req.getIndexName());
            result.add(dbDataInfo);
        }
        return ResultBean.success(result);
    }

    private DbDataInfo queryData(ItemPreviewRequest request) throws Exception {
        QuaWabElement elementInfo = quaWabElementMapper.selectById(request.getElementId());
        if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(request.getAssetType())) {
            DbConnInfo dbConnInfo = new DbConnInfo();
            dbConnInfo.setDbType(DB_TYP.CLICKHOUSE.getEnName());
            dbConnInfo.setUser(elementInfo.getChUserName());
            dbConnInfo.setPwd(BtoaEncode.decrypt(elementInfo.getChUserPassword()));
            dbConnInfo.setUrl("jdbc:clickhouse://" + elementInfo.getChIp() + ":" + elementInfo.getChPort() + "/");
            DbDataInfo sampleData = clickhouseComponent.getSampleData(dbConnInfo,
                    "select * from ".concat(request.getDbName()).concat(".").concat(request.getTableName().concat(" limit 3")),
                    DB_TYP.CLICKHOUSE.getEnName());
            sampleData.setTotal(sampleData.getData().size());
            return sampleData;
        } else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(request.getAssetType())) {
            DbConnInfo dbConnInfo = new DbConnInfo();
            dbConnInfo.setDbType(DB_TYP.Hive.getEnName());
            dbConnInfo.setUser(elementInfo.getChUserName());
            dbConnInfo.setPwd(BtoaEncode.decrypt(elementInfo.getChUserPassword()));
            dbConnInfo.setUrl("jdbc:hive2://" + elementInfo.getChIp() + ":" + elementInfo.getChPort() + "/");
            DbDataInfo sampleData = hiveComponent.getSampleData(dbConnInfo,
                    "select * from ".concat(request.getDbName()).concat(".").concat(request.getTableName()).concat(" limit 3"),
                    DB_TYP.Hive.getEnName());
            sampleData.setTotal(sampleData.getData().size());
            return sampleData;
        } else if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(request.getAssetType())) {
            DbConnInfo dbConnInfo = new DbConnInfo();
            dbConnInfo.setDbType(DB_TYP.Mysql.getEnName());
            dbConnInfo.setUser(elementInfo.getChUserName());
            dbConnInfo.setPwd(BtoaEncode.decrypt(elementInfo.getChUserPassword()));
            dbConnInfo.setUrl("jdbc:mysql://" + elementInfo.getChIp() + ":" + elementInfo.getChPort() + "/" + request.getDbName() + "?useSSL=false");
            DbDataInfo sampleData = mysqlComponent.getSampleData(dbConnInfo,
                    "select * from `".concat(request.getDbName()).concat("`.`").concat(request.getTableName()).concat("` limit 3"),
                    DB_TYP.Mysql.getEnName());
            sampleData.setTotal(sampleData.getData().size());
            return sampleData;
        } else if (ElementTypeEnum.ELASTICSEARCH_INDEX.getCode().equals(request.getAssetType())) {
            Set<String> columnSets = new HashSet<>();
            List<String> columns = new ArrayList<>();
            List<Map<String, Object>> data = new ArrayList<>();
            DbDataInfo dataInfo = new DbDataInfo();
            dataInfo.setColumns(columns);
            dataInfo.setData(data);

            RestHighLevelClient client;
            KerberosUtil.initElasticsearch();
            if (StringUtils.isNotEmpty(elementInfo.getEsUserName()) && StringUtils.isNotEmpty(elementInfo.getEsUserName())) {
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY,
                        new UsernamePasswordCredentials(elementInfo.getEsUserName(), BtoaEncode.decrypt(elementInfo.getEsUserPassword())));
                client = new RestHighLevelClient(
                        RestClient.builder(makeHttpHost(elementInfo.getEsIpPort())).setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)));
            } else {
                client = new RestHighLevelClient(RestClient.builder(makeHttpHost(elementInfo.getEsIpPort())));
            }

            SearchRequest searchRequest = new SearchRequest();
            searchRequest.indices(request.getIndexName());
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.from(0);
            sourceBuilder.size(3);

            searchRequest.source(sourceBuilder);
            SearchResponse rp = client.search(searchRequest, RequestOptions.DEFAULT);
            Iterator<SearchHit> iterator = rp.getHits().iterator();
            while (iterator.hasNext()) {
                SearchHit next = iterator.next();
                Map<String, Object> sourceAsMap = next.getSourceAsMap();
                data.add(sourceAsMap);
                columnSets.addAll(sourceAsMap.keySet());
            }
            for (String columnSet : columnSets) {
                columns.add(columnSet);
            }
            dataInfo.setTotal(dataInfo.getData().size());
            client.close();
            return dataInfo;
        }
        return null;
    }

    private HttpHost[] makeHttpHost(String urls) {
        String[] hostUrlArray = urls.split(",");
        HttpHost[] httpHostArray = new HttpHost[hostUrlArray.length];
        for (int i = 0; i < hostUrlArray.length; i++) {
            URI uri = URI.create(hostUrlArray[i]);
            if (urls.contains("https")) {
                httpHostArray[i] = new HttpHost(uri.getHost(), uri.getPort(), "https");
            } else {
                httpHostArray[i] = new HttpHost(uri.getHost(), uri.getPort(), "http");
            }
        }
        return httpHostArray;
    }

    /**
     * 存储概览
     */
    public StorageOverviewVo storageOverview() {
        StorageOverviewVo vo = new StorageOverviewVo();
        ItemOverviewVo itemOverviewVo = queryOverviewField(null);
        BeanUtil.copyProperties(itemOverviewVo, vo);
        // 业务板块和数据域数量
        long businessSectorCount = dictionaryItemMapper.selectList(new QueryWrapper<DataDictionaryItem>()
                .select("DISTINCT business_sector_id")).stream().count();
        vo.setBusinessSectorCount(businessSectorCount);
        long dataDomainCount = dictionaryItemMapper.selectList(new QueryWrapper<DataDictionaryItem>()
                .select("DISTINCT data_domain_id")).stream().count();
        vo.setDataDomainCount(dataDomainCount);
        return vo;
    }

    private ItemOverviewVo queryOverviewField(String datasourceType) {
        ItemOverviewVo vo = new ItemOverviewVo();
        // 数据源数量
        QueryWrapper<DataDictionaryItem> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(datasourceType)) {
            queryWrapper.eq("datasource_type", datasourceType);
        }
        List<DataDictionaryItem> items = dictionaryItemService.list(queryWrapper);
        if (CollectionUtils.isEmpty(items)) {
            return vo;
        }
        // 获取items中去重后的所有的elementId
        Set<Long> elementIds = items.stream().map(DataDictionaryItem::getElementId).collect(Collectors.toSet());
        List<QuaWabElement> elements = quaWabElementMapper.selectBatchIds(elementIds);
        // 如果elementType是CH,MYSQL,HIVE,则根据ch_ip和chPort去重获取数据源数量，如果elementType是ES,则根据es_ip_port去重获取数据源数量
        Set<String> chIps = elements.stream().filter(element ->
                        (com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum.CH.getCode().equals(element.getElementType()) ||
                                com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum.MYSQL.getCode().equals(element.getElementType()) ||
                                com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum.HIVE.getCode().equals(element.getElementType())))
                .map(quaWabElement -> quaWabElement.getChIp() + ":" + quaWabElement.getChPort()).collect(Collectors.toSet());
        long esDsCount = 0;
        List<QuaWabElement> esElements = elements.stream().filter(element ->
                        com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum.ES.getCode().equals(element.getElementType()))
                .collect(Collectors.toList());
        Set<String> esIpPortSet = new HashSet<>();
        for (QuaWabElement esElement : esElements) {
            boolean hit = true;
            Set<String> ipPorts = extractIpPorts(esElement.getEsIpPort());
            for (String ipPort : ipPorts) {
                if (esIpPortSet.contains(ipPort)) {
                    hit = false;
                    break;
                }
                esIpPortSet.add(ipPort);
            }
            if (!hit) {
                esDsCount++;
            }
        }
        vo.setDatasourceCount(chIps.size() + esDsCount);
        // 数据库数量
        long dbCount = dictionaryItemMapper.queryDatabaseCount(null, datasourceType);
        vo.setDbCount(dbCount);
        // 数据表数量
        long tableCount = dictionaryItemMapper.queryTableCount(null, datasourceType);
        vo.setTableCount(tableCount);
        // 存储大小
        Long storageSize = dictionaryItemMapper.queryTotalStorageSize(datasourceType);
        vo.setStorageSize(storageSize == null ? "0" : NumberUtils.formatSize(storageSize));
        return vo;
    }

    // 提取集群中所有的 ip:port 格式的节点
    private static Set<String> extractIpPorts(String esIpPorts) {
        Set<String> ipPorts = new HashSet<>();
        // 分割多个节点的 ip:port，假设它们是以逗号分隔的
        String[] nodes = esIpPorts.split(",");
        for (String node : nodes) {
            // 使用正则表达式从 http://ip:port 提取 ip 和 port
            Pattern pattern = Pattern.compile("http://([\\d.]+):(\\d+)");
            Matcher matcher = pattern.matcher(node.trim());
            if (matcher.matches()) {
                String ip = matcher.group(1);   // 提取 IP 部分
                String port = matcher.group(2); // 提取端口部分
                ipPorts.add(ip + ":" + port);   // 返回 ip:port 格式
            }
        }
        return ipPorts;  // 返回所有去重后的 ip:port 集合
    }

    /**
     * 数据源概览
     */
    public List<DatasourceTypeOverviewVo> datasourceTypeOverview() {
        List<DatasourceTypeOverviewVo> voList = new ArrayList<>();
        List<String> datasourceTypeList = dictionaryItemMapper.queryDatasourceTypeList(null);
        if (CollectionUtils.isEmpty(datasourceTypeList)) {
            return voList;
        }
        // 昨日的开始时间和结束时间，打印出来一定要是昨天00:00:00开始，23:59:59结束
        DateTime beginOfDay = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1));
        DateTime endOfDay = DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -1));
        for (String datasourceType : datasourceTypeList) {
            DatasourceTypeOverviewVo vo = new DatasourceTypeOverviewVo();
            vo.setDatasourceType(datasourceType);
            ItemOverviewVo itemOverviewVo = queryOverviewField(datasourceType);
            BeanUtil.copyProperties(itemOverviewVo, vo);
            // 昨日新增表数量
            Long yesterdayAddTable = dictionaryItemMapper.queryYesterdayAddTableCount(datasourceType, beginOfDay, endOfDay);
            vo.setYesterdayAddTable(yesterdayAddTable);
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 数据层级概览
     *
     * @return
     */
    public List<DwLevelOverviewVo> dwLevelOverview() {
        List<DwLevelOverviewVo> voList = new ArrayList<>();
        List<Long> dwLevelList = dictionaryItemMapper.queryDwLevelIdList(null);
        if (CollectionUtils.isEmpty(dwLevelList)) {
            return voList;
        }
        List<TbDwLevel> dwLevels = dwLevelService.list();
        Map<Long, TbDwLevel> dwLevelMap = dwLevels.stream().collect(Collectors.toMap(TbDwLevel::getId, Function.identity())); // 按照id分组
        for (Long dwLevelId : dwLevelList) {
            if (dwLevelId == null) {
                continue;
            }
            DwLevelOverviewVo vo = new DwLevelOverviewVo();
            long businessSectorCount = dictionaryItemMapper.queryBusinessSectorCount(null, dwLevelId);
            vo.setBusinessSectorCount(businessSectorCount);
            long dataDomainCount = dictionaryItemMapper.queryDataDomainCount(null, dwLevelId);
            vo.setDataDomainCount(dataDomainCount);
            long tableCount = dictionaryItemMapper.queryTableCountByDwLevel(null, dwLevelId);
            vo.setTableCount(tableCount);
            vo.setDwLevelCnName(dwLevelMap.get(dwLevelId).getCnName());
            vo.setDwLevelEnName(dwLevelMap.get(dwLevelId).getEnName());
            voList.add(vo);
        }
        return voList;
    }

    public Map<String, Object> itemAssetPage(RequestDTO requestDTO) {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<DataDictionaryItem> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataDictionaryItem> queryWrapper = new QueryWrapper<>();
        if (requestDTO.getParam() != null) {
            DataDictionaryItemDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), DataDictionaryItemDTO.class);
            if (StringUtils.isNotBlank(dto.getTableName())) {
                queryWrapper.like("di.table_name", dto.getTableName());
            }
            if (StringUtils.isNotBlank(dto.getColumnNameCn())) {
                queryWrapper.like("di.column_name_cn", dto.getColumnNameCn());
            }
            if (StringUtils.isNotBlank(dto.getDatasourceType())) {
                queryWrapper.eq("di.datasource_type", dto.getDatasourceType());
            }
            if (dto.getMetaSource() != null) {
                queryWrapper.eq("di.meta_source", dto.getMetaSource());
            }
            if (dto.getDataCount() != null) {
                queryWrapper.eq("di.data_count", dto.getDataCount());
            }
            if (dto.getIsSensitive() != null) {
                queryWrapper.eq("di.is_sensitive", dto.getIsSensitive());
            }
            if (StringUtils.isNotBlank(dto.getOwner())) {
                queryWrapper.like("di.owner", dto.getOwner());
            }
            if (dto.getBusinessSectorId() != null) {
                queryWrapper.eq("di.business_sector_id", dto.getBusinessSectorId());
            }
            if (dto.getDataDomainId() != null) {
                queryWrapper.eq("di.data_domain_id", dto.getDataDomainId());
            }
            if (dto.getDwLevelId() != null) {
                queryWrapper.eq("di.dw_level_id", dto.getDwLevelId());
            }
            queryWrapper.in("di.item_type", ItemTypeEnum.table.name(), ItemTypeEnum.index.name()); // 只展示表和索引，不展示字段
            if (requestDTO.getGlobal() != null) {
                queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                        CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
            }
        }
        IPage<DataDictionaryItem> pageResult = dictionaryItemService.itemAssetPage(page, queryWrapper);
        List<DataDictionaryItem> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            DataScoreCalcConfig calcConfig = scoreCalcConfigService.getOne(new LambdaQueryWrapper<>());
            for (DataDictionaryItem record : records) {
                lowMaintenanceAsset(calcConfig, record);
                lowQualityAsset(calcConfig, record);
                record.setStorageSize(NumberUtils.formatSize(record.getDataStorageSize()));
                record.setBusinessSectorName(record.getBusinessSectorId() == null ? "" :
                        dictionaryItemMapper.queryBusinessSectorName(record.getBusinessSectorId()));
                record.setDataDomainName(record.getDataDomainId() == null ? "" : dictionaryItemMapper.queryDataDomainName(record.getDataDomainId()));
                record.setDwLevelName(record.getDwLevelId() == null ? "" : dictionaryItemMapper.queryDwLevelName(record.getDwLevelId()));

                int martAssetCount = dataMartAssetService.count(new LambdaQueryWrapper<DataMartAsset>()
                        .eq(DataMartAsset::getElementId, record.getElementId())
                        .eq(StringUtils.isNotBlank(record.getDatabaseName()), DataMartAsset::getDbName, record.getDatabaseName())
                        .and(query -> query.eq(DataMartAsset::getTableName, record.getTableName())
                                .or().eq(DataMartAsset::getIndexName, record.getTableName()))
                        .in(DataMartAsset::getAssetTypeCode, "TABLE", "INDEX"));
                if (martAssetCount > 0) {
                    record.getAssetStatusList().add("集市表");
                }

                // 上游表和下游表
                String tableUniqueId = MetaDataUtil.getTableUniqueId(record.getElementId(), record.getDatasourceType(), record.getDatabaseName(),
                        record.getItemName());
                DataLineageRelationTable relationTable = relationTableService.getOne(new LambdaQueryWrapper<DataLineageRelationTable>()
                        .eq(DataLineageRelationTable::getUniqueId, tableUniqueId)
                        .orderByDesc(DataLineageRelationTable::getCreateTime)
                        .last("limit 1"));
                if (relationTable != null) {
                    record.setUpstreamDependencyCount(relationTable.getUpstreamTableNum());
                    record.setDownstreamDependencyCount(relationTable.getDownstreamTableNum());
                }
                // 高价值资产和孤岛资产
                Long highValueTableCount = 0L;
                if (calcConfig != null) {
                    highValueTableCount = relationTableService.highValueTableCount(tableUniqueId, calcConfig.getHighValueAssetScore());
                }
                if (highValueTableCount > 0) {
                    record.getAssetTypeList().add("高价值资产");
                } else {
                    int count = tableIslandService.count(new LambdaQueryWrapper<DataLineageRelationTableIsland>()
                            .eq(DataLineageRelationTableIsland::getUniqueId, tableUniqueId));
                    if (count > 0) {
                        record.getAssetTypeList().add("孤岛资产");
                    }
                }
            }
        }
        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);
        return result;
    }

    /**
     * 低维护资产
     *
     * @param calcConfig
     * @param record
     */
    private void lowMaintenanceAsset(DataScoreCalcConfig calcConfig, DataDictionaryItem record) {
        if (calcConfig != null) {
            QuaAssetMaintenance maintenanceAsset = quaAssetMaintenanceService.getOne(new LambdaQueryWrapper<QuaAssetMaintenance>()
                    .eq(QuaAssetMaintenance::getElementId, record.getElementId())
                    .eq(StringUtils.isNotBlank(record.getDatabaseName()), QuaAssetMaintenance::getDbName, record.getDatabaseName())
                    .eq(QuaAssetMaintenance::getTableName, record.getTableName())
                    .eq(QuaAssetMaintenance::getStatus, 0));
            if (maintenanceAsset != null && NumberUtil.compare(maintenanceAsset.getScore().doubleValue(), calcConfig.getLowMaintenanceAssetScore()) < 0) {
                record.getAssetTypeList().add("低维护资产");
            }
        }
    }

    /**
     * 低质量资产
     *
     * @param calcConfig
     * @param record
     */
    private void lowQualityAsset(DataScoreCalcConfig calcConfig, DataDictionaryItem record) {
        if (calcConfig != null) {
            Double score = dictionaryItemMapper.queryTaskScore(record.getElementId(), record.getDatabaseName(), record.getTableName());
            if (score != null && NumberUtil.compare(score, calcConfig.getLowQualityAssetScore()) < 0) {
                record.getAssetTypeList().add("低质量资产");
            }
        }
    }

    /**
     * 存储详情
     *
     * @param id
     * @return
     */
    public DataDictionaryItemTableStorageDetail storageDetail(Long id) {
        DataDictionaryItem item = dictionaryItemService.getById(id);
        if (item == null) {
            throw new ParamInvalidException("数据不存在");
        }
        DataDictionaryItemTableStorageDetail storageDetail = new DataDictionaryItemTableStorageDetail();
        // 最近一条数据
        QuaAssetTableDataStatistics latestData = tableDataStatisticsService.getOne(new LambdaQueryWrapper<QuaAssetTableDataStatistics>()
                .eq(QuaAssetTableDataStatistics::getElementId, item.getElementId())
                .eq(StringUtils.isNotBlank(item.getDatabaseName()), QuaAssetTableDataStatistics::getDbName, item.getDatabaseName())
                .eq(QuaAssetTableDataStatistics::getTableName, item.getTableName())
                .orderByDesc(QuaAssetTableDataStatistics::getCreateTime)
                .last("limit 1")
        );
        if (latestData == null) {
            return storageDetail;
        }
        storageDetail.setTotal(latestData.getDataCount());
        storageDetail.setStorageSize(NumberUtils.formatSize(latestData.getDataStorageSize()));
        // 获取最近的第二条数据作为昨日数据
        QuaAssetTableDataStatistics yesterdayData = tableDataStatisticsService.getOne(new LambdaQueryWrapper<QuaAssetTableDataStatistics>()
                .eq(QuaAssetTableDataStatistics::getElementId, item.getElementId())
                .eq(StringUtils.isNotBlank(item.getDatabaseName()), QuaAssetTableDataStatistics::getDbName, item.getDatabaseName())
                .eq(QuaAssetTableDataStatistics::getTableName, item.getTableName())
                .orderByDesc(QuaAssetTableDataStatistics::getCreateTime)
                .last("limit 1,1")
        );
        if (yesterdayData != null) {
            storageDetail.setYesterdayTotal(yesterdayData.getDataCount());
            storageDetail.setYesterdayStorageSize(NumberUtils.formatSize(yesterdayData.getDataStorageSize()));
            // 获取最近的第三条数据计算昨日新增数据
            QuaAssetTableDataStatistics twoYesterdayData = tableDataStatisticsService.getOne(new LambdaQueryWrapper<QuaAssetTableDataStatistics>()
                    .eq(QuaAssetTableDataStatistics::getElementId, item.getElementId())
                    .eq(StringUtils.isNotBlank(item.getDatabaseName()), QuaAssetTableDataStatistics::getDbName, item.getDatabaseName())
                    .eq(QuaAssetTableDataStatistics::getTableName, item.getTableName())
                    .orderByDesc(QuaAssetTableDataStatistics::getCreateTime)
                    .last("limit 2,1")
            );
            if (twoYesterdayData != null) {
                storageDetail.setYesterdayAddTotal(yesterdayData.getDataCount() - twoYesterdayData.getDataCount());
                storageDetail.setYesterdayAddStorageSize(NumberUtils.formatSize(yesterdayData.getDataStorageSize() - twoYesterdayData.getDataStorageSize()));
            }
        }

        // 默认近15天数据
        LocalDate date = LocalDate.now().minusDays(15);
        Date startOfDay = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
        String startTime = DateUtil.format(startOfDay, "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        QueryWrapper<QuaAssetTableDataStatistics> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("create_time", startTime, endTime);
        queryWrapper.orderByAsc("create_time");
        queryWrapper.eq("element_id", item.getElementId());
        queryWrapper.eq(StringUtils.isNotBlank(item.getDatabaseName()), "db_name", item.getDatabaseName());
        queryWrapper.eq("table_name", item.getTableName());
        List<QuaAssetTableDataStatistics> dataStatistics = tableDataStatisticsService.list(queryWrapper);
        Map<String, Long> dataCountMap = new HashMap<>();
        Map<String, Long> storageMap = new HashMap<>();
        for (QuaAssetTableDataStatistics dataStatistic : dataStatistics) {
            dataCountMap.put(dataStatistic.getStatisticDate(), dataStatistic.getDataCount());
            storageMap.put(dataStatistic.getStatisticDate(), dataStatistic.getDataStorageSize());
        }
        // 折线图数据
        List<Map<String, Object>> dataCountChartData = fillDates(startTime, endTime, dataCountMap);
        List<Map<String, Object>> storageChartData = fillStorageDates(startTime, endTime, storageMap);
        storageDetail.setDataCountChartData(dataCountChartData);
        storageDetail.setStorageChartData(storageChartData);
        return storageDetail;
    }

    public List<Map<String, Object>> fillDates(String startTime, String endTime, Map<String, Long> statisticMap) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startTime.substring(0, 10), formatter);
        LocalDate endDate = LocalDate.parse(endTime.substring(0, 10), formatter);
        // 生成日期范围
        List<String> allDates = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            allDates.add(date.format(formatter));
        }
        // 补充缺失的日期，返回新的统计数据列表
        List<Map<String, Object>> result = new ArrayList<>();
        for (String date : allDates) {
            Map<String, Object> statMap = new HashMap<>();
            statMap.put("date", date);
            statMap.put("num", statisticMap.getOrDefault(date, 0L));
            result.add(statMap);
        }
        return result;
    }

    public List<Map<String, Object>> fillStorageDates(String startTime, String endTime, Map<String, Long> storageMap) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startTime.substring(0, 10), formatter);
        LocalDate endDate = LocalDate.parse(endTime.substring(0, 10), formatter);
        // 生成日期范围
        List<String> allDates = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            allDates.add(date.format(formatter));
        }
        // 补充缺失的日期，返回新的统计数据列表
        List<Map<String, Object>> result = new ArrayList<>();
        for (String date : allDates) {
            Map<String, Object> statMap = new HashMap<>();
            statMap.put("date", date);
            statMap.put("num", storageMap.getOrDefault(date, 0L));
            result.add(statMap);
        }
        return result;
    }

    /**
     * 将历史元数据更新到字典项里，一次性功能
     */
    public void updateMetaToItem() {
        List<QuaWebTableModel> tableModelList = tableModelService.list(new LambdaQueryWrapper<QuaWebTableModel>()
                .isNotNull(QuaWebTableModel::getBusinessSectorId)
                .isNotNull(QuaWebTableModel::getDataDomainId)
                .isNotNull(QuaWebTableModel::getDwLevelId));
        if (CollectionUtils.isNotEmpty(tableModelList)) {
            for (QuaWebTableModel tableModel : tableModelList) {
                logger.info("开始处理已维护的数据，{}", cn.hutool.json.JSONUtil.toJsonStr(tableModel));
                int count = dictionaryItemService.count(new LambdaQueryWrapper<DataDictionaryItem>()
                        .eq(DataDictionaryItem::getDatasourceType, tableModel.getDatasourceType())
                        .eq(DataDictionaryItem::getElementId, tableModel.getElementId())
                        .eq(StringUtils.isNotBlank(tableModel.getDbName()), DataDictionaryItem::getDatabaseName, tableModel.getDbName())
                        .eq(DataDictionaryItem::getTableName, tableModel.getTableName())
                        .in(DataDictionaryItem::getItemType, ItemTypeEnum.table.name(), ItemTypeEnum.index.name()));
                if (count > 0) {
                    logger.info("数据字典表项已存在，跳过");
                    continue;
                }
                Long elementId = tableModel.getElementId();
                QuaTask quaTask = dictionaryItemMapper.queryLatestTaskByElementId(elementId);
                if (quaTask == null) {
                    continue;
                }
                String snapshootVersion = quaTask.getSnapshootVersion();
                String datasourceType = tableModel.getDatasourceType();
                String databaseName = tableModel.getDbName();
                String tableName = tableModel.getTableName();
                DataDictionaryItemDTO tableItem = new DataDictionaryItemDTO();
                tableItem.setTenantId(tableModel.getTenantId());
                tableItem.setCreateUser(tableModel.getCreateUser());
                tableItem.setBusinessSectorId(tableModel.getBusinessSectorId());
                tableItem.setDataDomainId(tableModel.getDataDomainId());
                tableItem.setDwLevelId(tableModel.getDwLevelId());
                tableItem.setBusinessProcessId(tableModel.getBusinessProcessId());
                if (DATA_SOURCE_TYPE_ENUM.MYSQL.getName().equalsIgnoreCase(datasourceType)) {
                    QuaWebMysqlElementDetailTable table = getMysqlElementDetailTable(elementId, snapshootVersion, databaseName, tableName);
                    if (table != null) {
                        tableItem.setDatasourceType(DATA_SOURCE_TYPE_ENUM.MYSQL.getName());
                        tableItem.setItemType(ItemTypeEnum.table.name());
                        tableItem.setElementId(table.getElementId());
                        tableItem.setDatabaseName(table.getDbName());
                        tableItem.setTableName(table.getTableName());
                        tableItem.setItemName(table.getTableName());
                        tableItem.setItemId(table.getId());
                        tableItem.setColumnNameCn(table.getTableNameCn());
                        tableItem.setIsSensitive(table.getIsSensitive());
                        tableItem.setItemDesc(table.getTableDscribe());
                        tableItem.setOwner(table.getTableOwner());
                        tableItem.setAssetType("mysql_table");
                        tableItem.setAssetTypeCode("TABLE");
                        tableItem.setAssetPath(table.getDbName() + "," + table.getTableName());
                        tableItem.setAssetDataType("表");

                        dictionaryItemService.addFromMetadata(tableItem);
                    }
                } else if (DATA_SOURCE_TYPE_ENUM.HIVE.getName().equalsIgnoreCase(datasourceType)) {
                    QuaWebHiveElementDetailTable table = getHiveElementDetailTable(elementId, snapshootVersion, databaseName, tableName);
                    if (table != null) {
                        tableItem.setDatasourceType(DATA_SOURCE_TYPE_ENUM.HIVE.getName());
                        tableItem.setItemType(ItemTypeEnum.table.name());
                        tableItem.setElementId(table.getElementId());
                        tableItem.setDatabaseName(table.getDbName());
                        tableItem.setTableName(table.getTableName());
                        tableItem.setItemName(table.getTableName());
                        tableItem.setItemId(table.getId());
                        tableItem.setColumnNameCn(table.getTableNameCn());
                        tableItem.setIsSensitive(table.getIsSensitive());
                        tableItem.setItemDesc(table.getTableDscribe());
                        tableItem.setOwner(table.getTableOwner());
                        tableItem.setAssetType("hive_table");
                        tableItem.setAssetTypeCode("TABLE");
                        tableItem.setAssetPath(table.getDbName() + "," + table.getTableName());
                        tableItem.setAssetDataType("表");

                        dictionaryItemService.addFromMetadata(tableItem);
                    }
                } else if (DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().equalsIgnoreCase(datasourceType)) {
                    ChElementDetailTable table = getChElementDetailTable(elementId, snapshootVersion, databaseName, tableName);
                    if (table != null) {
                        tableItem.setDatasourceType(DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName());
                        tableItem.setItemType(ItemTypeEnum.table.name());
                        tableItem.setElementId(table.getElementId());
                        tableItem.setDatabaseName(table.getDbName());
                        tableItem.setTableName(table.getTableName());
                        tableItem.setItemName(table.getTableName());
                        tableItem.setItemId(table.getId());
                        tableItem.setColumnNameCn(table.getTableNameCn());
                        tableItem.setIsSensitive(table.getIsSensitive());
                        tableItem.setItemDesc(table.getTableDscribe());
                        tableItem.setOwner(table.getTableOwner());
                        tableItem.setAssetType("clickhouse_table");
                        tableItem.setAssetTypeCode("TABLE");
                        tableItem.setAssetPath(table.getDbName() + "," + table.getTableName());
                        tableItem.setAssetDataType("表");

                        dictionaryItemService.addFromMetadata(tableItem);
                    }
                } else if (DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().equalsIgnoreCase(datasourceType)) {
                    EsElementDetailIndex index = getEsElementDetailIndex(elementId, snapshootVersion, tableName);
                    if (index != null) {
                        tableItem.setDatasourceType(DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName());
                        tableItem.setItemType(ItemTypeEnum.index.name());
                        tableItem.setElementId(index.getElementId());
                        tableItem.setTableName(index.getIndexName());
                        tableItem.setItemName(index.getIndexName());
                        tableItem.setItemId(index.getId());
                        tableItem.setColumnNameCn(index.getIndexNameCn());
                        tableItem.setIsSensitive(index.getIsSensitive());
                        tableItem.setItemDesc(index.getIndexDscribe());
                        tableItem.setOwner(index.getIndexOwner());
                        tableItem.setAssetType("elasticsearch_index");
                        tableItem.setAssetTypeCode("INDEX");
                        tableItem.setAssetPath(index.getIndexName());
                        tableItem.setAssetDataType("索引");

                        dictionaryItemService.addFromMetadata(tableItem);
                    }
                }
            }
        }
        logger.info("updateMetaToItem 更新完成");
    }
}