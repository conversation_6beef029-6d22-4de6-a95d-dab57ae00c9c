package com.idss.datalake.datagovern.dictionary.init;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryBase;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryBaseService;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.datashare.tenant.service.ITbTenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>数据字典数据初始化</p>
 * @date 2024/2/4
 * @see
 */
//@Component
@Slf4j
public class InitDictBaseRunner implements CommandLineRunner {
    @Autowired
    private ITbTenantService tenantService;
    @Autowired
    private IDataDictionaryBaseService dataDictionaryBaseService;

    @Override
    public void run(String... args) throws Exception {
        log.info("======= 开始初始化字典数据 =======");
        // 按照租户轮询
        //        List<TbTenant> tenants = tenantService.list(new QueryWrapper<TbTenant>()
        //                .eq("DEL_FLAG", "0")
        //                .eq("ACCOUNT_TYPE", "1")
        //                .eq("RESOURCE_STATUS", "2"));
        Map<String, String> map = new HashMap<>();
        map.put("数据分层", "将数据以分层的方式表现，提高效率，降低存储成本，提高使用效率，保障数据质量。");
        map.put("主题域", "在特定领域或主题上的数据集合，有助于用户更便捷、更准确查找和使用数据。");
        //        for (TbTenant tenant : tenants) {
        doSave(map);
        //        }
        log.info("======= 结束初始化字典数据 =======");
    }

    /**
     * 执行操作
     */
    private void doSave(Map<String, String> map) {
        try {
            for (String dictName : map.keySet()) {
                // 判断字典目录名称是否已经存在
                DataDictionaryBase dictionaryBase = dataDictionaryBaseService.getOne(new QueryWrapper<DataDictionaryBase>()
                        .eq("dict_name", dictName));
                if (dictionaryBase != null) {
                    if (dictionaryBase.getBuiltIn() == 1) {
                        dataDictionaryBaseService.update(new UpdateWrapper<DataDictionaryBase>().eq("id", dictionaryBase.getId())
                                .set("built_in", 0)
                                .set("dict_desc", map.get(dictName))
                        );
                    }
                    continue;
                }
                dictionaryBase = new DataDictionaryBase();
                dictionaryBase.setDictName(dictName);
                dictionaryBase.setDictDesc(map.get(dictName));
                dictionaryBase.setCreateTime(LocalDateTime.now());
                dictionaryBase.setUpdateTime(LocalDateTime.now());
                //                dictionaryBase.setCreateUser(tenant.getAccountName());
                //                dictionaryBase.setUpdateUser(tenant.getAccountName());
                //                dictionaryBase.setTenantId(tenant.getTenantId());
                dictionaryBase.setBuiltIn(0);
                dataDictionaryBaseService.save(dictionaryBase);
                log.info("保存字典数据[{}]", dictName);
            }
        } catch (Exception e) {
            log.error("初始化字典数据异常，{}", e.getMessage(), e);
        }
    }

}

