package com.idss.datalake.datagovern.dictionary.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @description <p>字典概览</p>
 * @date
 * @see
 */
@Data
public class OverviewDataDictItemVo {

    private Long id;
    /**
     * 分类名称
     */
    private String name;
    /**
     * 层级数量
     */
    private int levelCount;
    /**
     * 表数量
     */
    private int tableCount;
    /**
     * 字段数量
     */
    private int columnCount;
    /**
     * 主题域数量
     */
    private int subjectDomainCount;

}
