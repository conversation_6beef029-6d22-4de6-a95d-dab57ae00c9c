package com.idss.datalake.datagovern.dictionary.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.datalake.datagovern.dictionary.dto.DataDictionaryItemDTO;
import com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.dictionary.vo.QueryElementVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 数据字典项 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface IDataDictionaryItemService extends IService<DataDictionaryItem> {
    IPage<DataDictionaryItem> pageCustom(Page<DataDictionaryItem> page, @Param(Constants.WRAPPER) QueryWrapper<DataDictionaryItem> wrapper);

    Long pageWithTagCount(DataDictionaryItemDTO dto);

    List<DataDictionaryItem> pageWithTag(DataDictionaryItemDTO dto);

    /**
     * 更新数据字典项
     *
     * @param datasource_type
     * @param item_type
     * @param itemId
     * @param columnNameCn
     * @param desc
     */
    void updateItemFromMeta(String datasource_type, String item_type, Long elementId, String dbName, String tableName, String itemName,
                            Long itemId, String columnNameCn, String desc, String owner);

    /**
     * 更新数据字典项
     *
     * @param itemDTO
     */
    void updateItemFromMeta(DataDictionaryItemDTO itemDTO);

    IPage<DataDictionaryItem> itemAssetPage(Page<DataDictionaryItem> page, @Param(Constants.WRAPPER) QueryWrapper<DataDictionaryItem> wrapper);

    /**
     * 设置字段中文名、表名
     *
     * @param dataDictionaryItem
     * @param datasourceType
     * @param itemId
     */
    void setCnName(DataDictionaryItem dataDictionaryItem, String datasourceType, Long itemId);

    List<QueryElementVo> queryFiledList(QueryElementRequest requestDto, Long tenantId);

    void addFromMetadata(DataDictionaryItemDTO itemDTO);
}
