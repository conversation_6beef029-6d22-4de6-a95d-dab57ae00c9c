/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/3/2
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/3/2
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.dictionary.dto;

import lombok.Data;

/**
 * ItemPreviewRequest
 *
 * <AUTHOR>
 * @date 2024/10/9
 * @see
 */
@Data
public class ItemPreviewRequest {
    private Long elementId;
    private String assetType;
    private String dbName;
    private String tableName;
    private String indexName;

    /**
     * 字典项id
     */
    private String itemId;
}
