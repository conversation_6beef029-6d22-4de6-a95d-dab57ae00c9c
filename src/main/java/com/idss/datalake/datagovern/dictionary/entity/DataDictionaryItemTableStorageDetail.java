/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-19
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-19
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.dictionary.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 字典数据存储详情
 *
 * <AUTHOR>
 * @date 2024/11/23
 * @see
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DataDictionaryItemTableStorageDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总数
     */
    private long total;
    /**
     * 昨天总数
     */
    private long yesterdayTotal;
    /**
     * 昨天新增数据量
     */
    private long yesterdayAddTotal;
    /**
     * 存储大小
     */
    private String storageSize;
    /**
     * 昨天存储大小
     */
    private String yesterdayStorageSize;
    /**
     * 昨天新增存储大小
     */
    private String yesterdayAddStorageSize;

    /**
     * 表占用磁盘空间趋势
     */
    List<Map<String, Object>> storageChartData = new ArrayList<>();

    /**
     * 数据条数趋势
     */
    List<Map<String, Object>> dataCountChartData = new ArrayList<>();

}
