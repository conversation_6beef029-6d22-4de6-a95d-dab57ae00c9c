package com.idss.datalake.datagovern.dictionary.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.idss.datalake.datagovern.metadatatag.model.DataMetaTagAndTypeModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 数据字典项
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataDictionaryItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典ID
     */
    private Long dictId;

    /**
     * 字典分类ID
     */
    private Long categoryId;

    /**
     * 字典项ID
     */
    private Long itemId;

    /**
     * 字典项名称
     */
    private String itemName;

    /**
     * 字典项描述
     */
    private String itemDesc;

    /**
     * 字典项类型 table-表  field-字段
     */
    private String itemType;

    /**
     * 字典项约束
     */
    private String itemConstraint;

    /**
     * 数据源类型
     */
    private String datasourceType;

    /**
     * 数据库名称
     */
    private String databaseName;
    /**
     * 数据库id
     */
    private Long databaseId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 所属字典路径
     */
    @TableField(exist = false)
    private String dictNamePath;

    /**
     * 字典项中文名
     */
    private String columnNameCn;

    /**
     * 字段类型
     */
    private String fieldType;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 发布到数据集市时使用
     */
    private String assetPath;
    private String assetType;
    private String assetTypeCode;
    private Long elementId;
    private String assetDataType;
    /**
     * 发布到集市的资产id
     */
    private Long dataMartAssetId;

    /**
     * 是否已发布到集市
     */
    @TableField(exist = false)
    private boolean hasPublished = false;
    /**
     * 是否已订阅
     */
    @TableField(exist = false)
    private boolean hasSubscribe = false;

    /**
     * 敏感分级ID
     */
    @TableField("sen_level_id")
    private Long senLevelId;

    /**
     * 敏感分级名称
     */
    @TableField("sen_level_name")
    private String senLevelName;

    /**
     * 敏感分类ID
     */
    @TableField("sen_type_id")
    private Long senTypeId;

    /**
     * 敏感分级名称
     */
    @TableField("sen_type_name")
    private String senTypeName;

    /**
     * 是否敏感，默认不是，0-否，1-是
     */
    @TableField("is_sensitive")
    private Integer isSensitive;

    /**
     * 脱敏规则 ID
     */
    @TableField("desensitization_id")
    private Integer desensitizationId;

    /**
     * 脱敏规则名称
     */
    @TableField(exist = false)
    private String desensitizationName;

    /**
     * 是否必填，0-否，1-是
     */
    @TableField("is_required")
    private Integer isRequired;

    /**
     * 是否加密，0-否，1-是
     */
    @TableField("is_encrypted")
    private Integer isEncrypted;


    /**
     * 中文描述
     */
    private String cnDesc;
    /**
     * 枚举值
     */
    private String enumValue;
    /**
     * 映射字段
     */
    private String mappingFields;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 负责人
     */
    private String owner;

    /**
     * 所属标签列表
     */
    @TableField(exist = false)
    private List<DataMetaTagAndTypeModel> metaTagList = new ArrayList<>();

    /**
     * 标签ID列表，逗号分开
     */
    @TableField(exist = false)
    private String metaTagIds;

    /**
     * 所属业务板块
     */
    @TableField("business_sector_id")
    private Long businessSectorId;
    @TableField(exist = false)
    private String businessSectorName;

    /**
     * 所属层级
     */
    @TableField("dw_level_id")
    private Long dwLevelId;
    @TableField(exist = false)
    private String dwLevelName;

    /**
     * 所属数据域
     */
    @TableField("data_domain_id")
    private Long dataDomainId;
    @TableField(exist = false)
    private String dataDomainName;

    /**
     * 所属业务过程
     */
    @TableField("business_process_id")
    private Long businessProcessId;
    @TableField(exist = false)
    private String businessProcessName;

    /**
     * 数据条数
     */
    @TableField("data_count")
    private Long dataCount;

    /**
     * 数据存储量（字节）
     */
    @TableField("data_storage_size")
    private Long dataStorageSize;

    /**
     * 数据大小
     */
    @TableField(exist = false)
    private String storageSize;

    /**
     * 元数据来源，1-自建，2-数据建模
     */
    @TableField("meta_source")
    private Integer metaSource;

    /**
     * 资产类型列表
     */
    @TableField(exist = false)
    private List<String> assetTypeList = new ArrayList<>();
    /**
     * 资产状态列表
     */
    @TableField(exist = false)
    private List<String> assetStatusList = new ArrayList<>();
    /**
     * 上游依赖数量
     */
    @TableField(exist = false)
    private int upstreamDependencyCount;
    /**
     * 下游依赖数量
     */
    @TableField(exist = false)
    private int downstreamDependencyCount;

    /**
     * 质量任务id，用于页面查询质量任务相关数据
     */
    @TableField(exist = false)
    private Long taskId;

    /**
     * 是否主键，0-否，1-是
     */
    @TableField("is_primary_key")
    private Integer isPrimaryKey;


    @TableField(exist = false)
    private Integer thumbsUpNum;
    @TableField(exist = false)
    private Integer subscribeNum;
    @TableField(exist = false)
    private Integer subscribeState;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private LocalDateTime subscribeTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private LocalDateTime thumbsUpTime;

}

