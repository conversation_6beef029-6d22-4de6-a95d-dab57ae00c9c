/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/4/18
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/4/18
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.dictionary.schdule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.common.util.ClickhouseUtil;
import com.idss.datalake.common.util.MetaDataUtil;
import com.idss.datalake.common.util.MysqlUtil;
import com.idss.datalake.common.util.ReflectionUtil;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItem;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItemTableTrend;
import com.idss.datalake.datagovern.dictionary.enums.ItemTypeEnum;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryItemService;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryItemTableTrendService;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.element.utils.BtoaEncode;
import com.idss.radar.datasource.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>数据字典-数据趋势统计</p>
 * @date 2024/6/19
 * @see
 */
@Component
@Slf4j
public class TableTrendSchedule {
    @Autowired
    private IDataDictionaryItemService dictionaryItemService;
    @Autowired
    private IDataDictionaryItemTableTrendService itemTableTrendService;
    @Autowired
    private QuaWabElementService quaWabElementService;

    // @Scheduled(fixedDelay = 3600 * 1000 * 24)
    @Scheduled(cron = "0 0 22 * * ?")
    public void doCount() {
        DatasourceType.clearDataBaseType();
        //获取所有字典项
        List<DataDictionaryItem> itemList = dictionaryItemService.list(new QueryWrapper<DataDictionaryItem>()
                .eq("item_type", ItemTypeEnum.table.name())
                .in("datasource_type", DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName(), DATA_SOURCE_TYPE_ENUM.MYSQL.getName()));
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        List<DataDictionaryItemTableTrend> tableTrendList = new ArrayList<>();
        Map<String, DataDictionaryItemTableTrend> tableTrendMap = new HashMap<>(); // 用于存储相同键的数据，避免重复查询
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String today = format.format(new Date());
        for (DataDictionaryItem item : itemList) {
            String tableUniqueId = MetaDataUtil.getTableUniqueId(item.getElementId(), item.getDatasourceType(), item.getItemType(),
                    item.getDatabaseName(), item.getItemName());
            if (tableTrendMap.containsKey(tableUniqueId)) {
                DataDictionaryItemTableTrend tableTrend = tableTrendMap.get(tableUniqueId);
                DataDictionaryItemTableTrend newTableTrend = new DataDictionaryItemTableTrend();
                ReflectionUtil.copyLomBokProperties(tableTrend, newTableTrend);
                newTableTrend.setItemId(item.getId());
                tableTrendList.add(newTableTrend);
                continue;
            }

            String databaseName = item.getDatabaseName();
            String tableName = item.getItemName();
            log.info("开始统计数据字典-【{}】【{}】库【{}】表的趋势数据", item.getItemType(), databaseName, tableName);
            Long tableLine = 0L;
            BigDecimal tableSpace = new BigDecimal(0);
            QuaWabElement element = quaWabElementService.getById(item.getElementId());
            if (DATA_SOURCE_TYPE_ENUM.MYSQL.getName().equals(item.getItemType())) {
                Connection connect = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_MYSQL,
                        "jdbc:mysql://" + element.getChIp() + ":" + element.getChPort() + "/" + databaseName, element.getChUserName(),
                        BtoaEncode.decrypt(element.getChUserPassword()));
                try {
                    tableLine = MysqlUtil.countTableLine(connect, tableName);
                    tableSpace = tableSpace.add(MysqlUtil.countTableSpace(connect, databaseName, tableName));
                } catch (Exception e) {
                    log.error("数据字典-查询表行数失败，{}", e.getMessage(), e);
                } finally {
                    if (connect != null) {
                        MysqlUtil.close(connect);
                    }
                }
            } else if (DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().equals(item.getItemType())) {
                Connection connect =
                        ClickhouseUtil.getConnect("jdbc:clickhouse://" + element.getChIp() + ":" + element.getChPort() + "/" + databaseName,
                                element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()));
                try {
                    tableLine = ClickhouseUtil.countTableLine(connect, tableName);
                    tableSpace = tableSpace.add(ClickhouseUtil.countTableSpace(connect, databaseName, tableName));
                } catch (Exception e) {
                    log.error("数据字典-查询表行数失败，{}", e.getMessage(), e);
                } finally {
                    if (connect != null) {
                        ClickhouseUtil.close(connect);
                    }
                }
            }
            DataDictionaryItemTableTrend tableTrend = new DataDictionaryItemTableTrend();
            tableTrend.setItemId(item.getId());
            tableTrend.setCountDay(today);
            tableTrend.setTableLine(tableLine);
            tableTrend.setTableSpace(tableSpace);
            tableTrend.setCreateTime(LocalDateTime.now());
            tableTrendList.add(tableTrend);
            tableTrendMap.put(tableUniqueId, tableTrend);
        }
        itemTableTrendService.saveBatch(tableTrendList);
        log.info("数据字典-统计数据趋势数据完成");
    }
}
