/************************ <PERSON>ANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-19
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-19
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.dictionary.dto;

import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItemTableTrend;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>数据字典-数据条数趋势表 dto类</p>
 * @since 2024-06-19
 */
@Data
public class DataDictionaryItemTableTrendDTO extends DataDictionaryItemTableTrend {
    private List<Long> ids;
}