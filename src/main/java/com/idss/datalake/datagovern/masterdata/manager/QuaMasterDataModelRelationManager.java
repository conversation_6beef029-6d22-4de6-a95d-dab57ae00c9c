/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-02-27
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-02-27
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.masterdata.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.masterdata.dto.QuaMasterDataModelRelationDTO;
import com.idss.datalake.datagovern.masterdata.entity.QuaMasterDataModelRelation;
import com.idss.datalake.datagovern.masterdata.service.IQuaMasterDataModelRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <AUTHOR>
 * @description <p>主数据模型 manager处理类</p>
 * @since 2023-02-27
 */
@Component
public class QuaMasterDataModelRelationManager {
    private static final Logger logger = LoggerFactory.getLogger(QuaMasterDataModelRelationManager.class);

    @Autowired
    private IQuaMasterDataModelRelationService iQuaMasterDataModelRelationService;

    public IService<QuaMasterDataModelRelation> getService() {
        return this.iQuaMasterDataModelRelationService;
    }

    public void create(QuaMasterDataModelRelationDTO dto) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(dto.getModelId()) || ObjectUtils.isEmpty(dto.getRelatedType()) || ObjectUtils.isEmpty(dto.getRelatedModelId())) {
            throw new ParamInvalidException("入参异常");
        }
        // 删除原关联关系
        iQuaMasterDataModelRelationService.remove(new QueryWrapper<QuaMasterDataModelRelation>().eq("model_id", dto.getModelId()));
        //保存
        QuaMasterDataModelRelation quaMasterDataModelRelation = new QuaMasterDataModelRelation();
        ReflectionUtil.copyLomBokProperties(dto, quaMasterDataModelRelation);
        iQuaMasterDataModelRelationService.save(quaMasterDataModelRelation);
    }

    public QuaMasterDataModelRelation detail(Long modelId) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(modelId)) {
            throw new ParamInvalidException("入参异常");
        }
        QueryWrapper<QuaMasterDataModelRelation> wrapper = new QueryWrapper();
        wrapper.eq("model_id", modelId);
        return iQuaMasterDataModelRelationService.getOne(wrapper);
    }
}