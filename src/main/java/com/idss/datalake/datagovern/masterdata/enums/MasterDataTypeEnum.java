/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/22 15:31
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.masterdata.enums;

/**
 * Copyright 2022 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/22 15:31
 */
public enum MasterDataTypeEnum {

    STRING("String", "字符型"),
    Integer("Int", "整型"),
    Date("Date", "时间戳"),
    FLOAT("Float", "浮点型");

    private String code;

    private String name;

    MasterDataTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (MasterDataTypeEnum value : MasterDataTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return "";
    }

    public static String getCodeByName(String name) {
        for (MasterDataTypeEnum value : MasterDataTypeEnum.values()) {
            if (value.getName().equals(name)) {
                return value.getCode();
            }
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
