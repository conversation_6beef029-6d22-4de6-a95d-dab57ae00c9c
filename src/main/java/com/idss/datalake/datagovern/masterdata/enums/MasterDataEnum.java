/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/22 15:31
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.masterdata.enums;

/**
 * Copyright 2022 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/22 15:31
 */
public enum MasterDataEnum {

    YES("1", "是"),
    NO("0", "否");

    private String code;

    private String name;

    MasterDataEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getTypeName(String code) {
        for (MasterDataEnum value : MasterDataEnum.values()) {
            if(value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
