package com.idss.datalake.datagovern.masterdata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <p>
 * 主数据模型
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuaMasterDataModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主数据模型名称
     */
    private String modelName;

    /**
     * 编码规则
     */
    private String modelCode;

    /**
     * 主数据类型
     */
    private Long modelTypeId;
    /**
     * 主数据类型
     */
    @TableField(exist = false)
    private String modelTypeName;

    /**
     * 模型状态:1-正常；0-冻结
     */
    private String modelStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新时间
     */
    private String updateUser;

    /**
     * 有效标识:1-有效；0-无效
     */
    private String flag;

    /**
     * 租户ID
     */
    private Long tenantId;

    @TableField(exist = false)
    private String modelStatusName;

    /**
     * 开通状态:1-正常；0-冻结
     */
    private String openStatus;

    @TableField(exist = false)
    private String openStatusName;
}
