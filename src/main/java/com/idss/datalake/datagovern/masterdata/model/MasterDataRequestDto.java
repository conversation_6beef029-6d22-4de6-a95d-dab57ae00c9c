/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/23 10:01
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.masterdata.model;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Copyright 2022 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/23 10:01
 */
@Data
public class MasterDataRequestDto extends BasePageRequest {

    @ApiModelProperty("字段名称")
    private String fieldName;

    @ApiModelProperty("主数据模型ID")
    private Long modelId;

    @ApiModelProperty("字段编码")
    private String fieldCode;

    @ApiModelProperty("字段编码")
    private String fieldDesc;

    @ApiModelProperty("字段类型")
    private String dataType;

    @ApiModelProperty("字段长度")
    private Integer fieldLength;

    @ApiModelProperty("字段精度")
    private Integer fieldPrecision;

    @ApiModelProperty("是否为空")
    private String isNull;

    @ApiModelProperty("是否唯一")
    private String isUnique;

    @ApiModelProperty("状态")
    private String dataStatus;

    @ApiModelProperty("状态")
    private String openStatus;

    private Long id;

    private List<Long> ids;
}
