package com.idss.datalake.datagovern.masterdata.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.masterdata.entity.QuaMasterData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.masterdata.model.MasterDataRequestDto;
import com.idss.datalake.datagovern.masterdata.model.MasterDataResponseVo;

/**
 * <p>
 * 主数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
public interface QuaMasterDataMapper extends BaseMapper<QuaMasterData> {

    Page<MasterDataResponseVo> queryMasterDataPage(MasterDataRequestDto requestDto);
}
