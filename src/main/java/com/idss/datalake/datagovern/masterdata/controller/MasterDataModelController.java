package com.idss.datalake.datagovern.masterdata.controller;


import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.syslog.annotation.SysLog;
import com.idss.datalake.common.syslog.entity.OptModule;
import com.idss.datalake.common.syslog.entity.OptType;
import com.idss.datalake.datagovern.masterdata.dto.QuaMasterDataModelRelationDTO;
import com.idss.datalake.datagovern.masterdata.entity.QuaMasterDataModel;
import com.idss.datalake.datagovern.masterdata.manager.QuaMasterDataModelManager;
import com.idss.datalake.datagovern.masterdata.manager.QuaMasterDataModelRelationManager;
import com.idss.datalake.datagovern.masterdata.model.MasterDataModelRequestDto;
import com.idss.datalake.datagovern.masterdata.model.QuaMasterDataModelDTO;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 主数据模型 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/masterData/model")
public class MasterDataModelController {
    private static final Logger logger = LoggerFactory.getLogger(MasterDataModelController.class);
    @Autowired
    private QuaMasterDataModelManager quaMasterDataModelManager;
    @Autowired
    private QuaMasterDataModelRelationManager quaMasterDataModelRelationManager;

    @SysLog(logName = "新增模型", optType = OptType.ADD, optModule = OptModule.DATA_LAKE_GOVERNANCE_MASTERDATA_MODEL, switchRedisStatus = true)
    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody QuaMasterDataModelDTO quaMasterDataModelDTO) {
        try {
            quaMasterDataModelManager.create(quaMasterDataModelDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "删除模型", optType = OptType.DELETE, optModule = OptModule.DATA_LAKE_GOVERNANCE_MASTERDATA_MODEL, switchRedisStatus = true)
    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public ResultBean delete(@RequestBody QuaMasterDataModelDTO quaMasterDataModelDTO) {
        try {
            quaMasterDataModelManager.delete(quaMasterDataModelDTO.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "模型详情", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_MASTERDATA_MODEL, switchRedisStatus = true)
    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Long id) {
        try {
            return ResultBean.success(quaMasterDataModelManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "编辑模型", optType = OptType.EDIT, optModule = OptModule.DATA_LAKE_GOVERNANCE_MASTERDATA_MODEL, switchRedisStatus = true)
    @ApiOperation(value = "编辑")
    @PutMapping(value = "/edit")
    public ResultBean edit(@RequestBody QuaMasterDataModelDTO quaMasterDataModelDTO) {
        try {
            quaMasterDataModelManager.edit(quaMasterDataModelDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "冻结/解冻", optType = OptType.FREEZE, optModule = OptModule.DATA_LAKE_GOVERNANCE_MASTERDATA_MODEL, switchRedisStatus = true)
    @ApiOperation(value = "冻结/解冻")
    @PutMapping(value = "/freeze")
    public ResultBean freeze(@RequestBody QuaMasterDataModelDTO quaMasterDataModelDTO) {
        try {
            quaMasterDataModelManager.freeze(quaMasterDataModelDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "开通/失效", optType = OptType.FREEZE, optModule = OptModule.DATA_LAKE_GOVERNANCE_MASTERDATA_MODEL, switchRedisStatus = true)
    @ApiOperation(value = "开通/失效")
    @PutMapping(value = "/open")
    public ResultBean open(@RequestBody QuaMasterDataModelDTO quaMasterDataModelDTO) {
        try {
            quaMasterDataModelManager.open(quaMasterDataModelDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "分页查询", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_MASTERDATA_MODEL, switchRedisStatus = true)
    @ApiOperation(value = "分页查询列表")
    @PostMapping(value = "/page")
    public BasePageResponse<List<QuaMasterDataModel>> page(@RequestBody MasterDataModelRequestDto requestDTO) {
        try {
            return quaMasterDataModelManager.page(requestDTO);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    @ApiOperation(value = "获取主数据类型")
    @GetMapping(value = "/category")
    public ResultBean category() {
        try {
            return ResultBean.success(quaMasterDataModelManager.category());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 创建主数据模型关联关系
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/relation/create")
    public ResultBean createRelation(@RequestBody QuaMasterDataModelRelationDTO dto) {
        try {
            quaMasterDataModelRelationManager.create(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @GetMapping(value = "/relation/{modelId}")
    public ResultBean detailRelation(@PathVariable Long modelId) {
        try {
            return ResultBean.success(quaMasterDataModelRelationManager.detail(modelId));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @GetMapping(value = "/otherAll/{modelId}")
    public ResultBean otherAll(@PathVariable Long modelId) {
        try {
            return ResultBean.success(quaMasterDataModelManager.otherAll(modelId));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

}
