package com.idss.datalake.datagovern.masterdata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 主数据
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuaMasterData implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主数据模型ID
     */
    private Long modelId;

    /**
     * 字段名称
     */
    private String fieldName;

    private String fieldCode;

    /**
     * 字段描述
     */
    private String fieldDesc;

    /**
     * 主数据类型
     */
    private String dataType;

    /**
     * 字段长度
     */
    private Integer fieldLength;

    /**
     * 字段精度
     */
    private Integer fieldPrecision;

    /**
     * 是否可以为空 1-是 0-否
     */
    private String isNull;

    /**
     * 是否可以唯一 1-是 0-否
     */
    private String isUnique;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新时间
     */
    private String updateUser;

    /**
     * 有效标识:1-有效；0-无效
     */
    private String flag;

    /**
     * 租户ID
     */
    private Long tenantId;


    /**
     * 状态:1-正常；0-冻结
     */
    private String dataStatus;

    /**
     * 状态:1-正常；0-失效
     */
    private String openStatus;
}
