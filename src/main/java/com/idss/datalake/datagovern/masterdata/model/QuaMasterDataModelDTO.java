/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-22
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-22
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.masterdata.model;

import com.idss.datalake.datagovern.masterdata.entity.QuaMasterDataModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>主数据模型 dto类</p>
 * @since 2022-08-22
 */
@Data
public class QuaMasterDataModelDTO extends QuaMasterDataModel {

    private List<Long> ids;
}