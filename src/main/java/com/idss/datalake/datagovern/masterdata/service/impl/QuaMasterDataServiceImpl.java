package com.idss.datalake.datagovern.masterdata.service.impl;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.FileUtils;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.dataquality.model.SelectVo;
import com.idss.datalake.datagovern.masterdata.entity.QuaMasterData;
import com.idss.datalake.datagovern.masterdata.enums.MasterDataEnum;
import com.idss.datalake.datagovern.masterdata.enums.MasterDataTypeEnum;
import com.idss.datalake.datagovern.masterdata.mapper.QuaMasterDataMapper;
import com.idss.datalake.datagovern.masterdata.model.MasterDataRequestDto;
import com.idss.datalake.datagovern.masterdata.model.MasterDataResponseVo;
import com.idss.datalake.datagovern.masterdata.service.IQuaMasterDataService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.model.job.utils.ExportUtil;
import com.idss.radar.util.StringUtil;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 主数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@Service
public class QuaMasterDataServiceImpl extends ServiceImpl<QuaMasterDataMapper, QuaMasterData> implements IQuaMasterDataService {
    private static final Logger logger = LoggerFactory.getLogger(QuaMasterDataServiceImpl.class);

    private String[] headers = new String[]{"字段名", "字段编码", "字段描述", "数据类型", "长度", "小数位数", "是否可为空", "是否唯一"};

    @Resource
    private QuaMasterDataMapper masterDataMapper;

    @Override
    public BasePageResponse<List<MasterDataResponseVo>> queryMasterDataPage(MasterDataRequestDto requestDto) {
        List<MasterDataResponseVo> list = new ArrayList<>();
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<MasterDataResponseVo> page = masterDataMapper.queryMasterDataPage(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        } else {
            List<MasterDataResponseVo> vos = page.getResult();
            for (MasterDataResponseVo vo : vos) {
                vo.setDataType(MasterDataTypeEnum.getNameByCode(vo.getDataType()));
                vo.setIsNull(MasterDataEnum.getTypeName(vo.getIsNull()));
                vo.setIsUnique(MasterDataEnum.getTypeName(vo.getIsUnique()));
                if ("1".equals(vo.getDataStatus())){
                    vo.setDataStatusName("正常");
                }else {
                    vo.setDataStatusName("冻结");
                }
                if ("1".equals(vo.getOpenStatus())){
                    vo.setOpenStatusName("正常");
                }else {
                    vo.setOpenStatusName("失效");
                }
            }
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), vos);
        }
    }

    @Override
    public void addOrUpdate(MasterDataRequestDto requestDto) {
        UserValueObject uvo = UmsUtils.getUVO();
        if (requestDto.getId() == null) {
            // 新增主数据
            QuaMasterData masterData = new QuaMasterData();
            masterData.setModelId(requestDto.getModelId());
            masterData.setFieldName(requestDto.getFieldName());
            masterData.setFieldCode(requestDto.getFieldCode());
            masterData.setFieldDesc(requestDto.getFieldDesc());
            masterData.setDataType(requestDto.getDataType());
            masterData.setFieldLength(requestDto.getFieldLength());
            masterData.setFieldPrecision(requestDto.getFieldPrecision());
            masterData.setIsNull(requestDto.getIsNull());
            masterData.setIsUnique(requestDto.getIsUnique());
            masterData.setTenantId(Long.valueOf(uvo.getTenantId()));
            masterData.setCreateUser(uvo.getUserName());

            save(masterData);
        } else {
            // 编辑主数据
            QuaMasterData masterData = getById(requestDto.getId());
            masterData.setModelId(requestDto.getModelId());
            masterData.setFieldName(requestDto.getFieldName());
            masterData.setFieldCode(requestDto.getFieldCode());
            masterData.setFieldDesc(requestDto.getFieldDesc());
            masterData.setDataType(requestDto.getDataType());
            masterData.setFieldLength(requestDto.getFieldLength());
            masterData.setFieldPrecision(requestDto.getFieldPrecision());
            masterData.setIsNull(requestDto.getIsNull());
            masterData.setIsUnique(requestDto.getIsUnique());
            masterData.setUpdateUser(uvo.getUserName());

            updateById(masterData);
        }
    }

    @Override
    public void delete(MasterDataRequestDto requestDto) {
        this.update(new UpdateWrapper<QuaMasterData>()
                .set("flag", "0")
                .in("id", requestDto.getIds()));
    }

    @Override
    public List<SelectVo> getDataType() {
        List<SelectVo> vos = new ArrayList<>();
        for (MasterDataTypeEnum value : MasterDataTypeEnum.values()) {
            SelectVo vo = new SelectVo();
            vo.setCode(value.getCode());
            vo.setValue(value.getName());

            vos.add(vo);
        }
        return vos;
    }

    /**
     * 导入
     *
     * @param params
     * @throws ParamInvalidException
     */
    @Override
    @SneakyThrows
    public void importData(Map<String, Object> params) {
        String filePath = (String) params.get("filePath");
        BASE64Decoder decoder = new BASE64Decoder();
        filePath = new String(decoder.decodeBuffer(filePath));
        File file = new File(filePath);
        if (!file.exists()) {
            throw new ParamInvalidException("文件不存在");
        }
        Object modelIdObj = params.get("modelId");
        if (modelIdObj == null) {
            throw new ParamInvalidException("主数据模型不存在");
        }
        Long modelId = Long.valueOf(modelIdObj.toString());

        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        List<QuaMasterData> masterDataList = this.list(new QueryWrapper<QuaMasterData>()
                .eq("tenant_id", tenantId)
                .eq("model_id", modelId)
                .eq("flag", "1"));
        Set<String> fieldNameSets = masterDataList.stream().map(x -> x.getFieldName()).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(fieldNameSets)) {
            fieldNameSets = new HashSet<>();
        }
        CsvReader reader = CsvUtil.getReader();
        List<MasterDataCsvBean> csvBeans = reader.read(FileUtil.getReader(file, Charset.forName("gbk")), MasterDataCsvBean.class);
        if (CollectionUtils.isEmpty(csvBeans)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        String username = UserUtil.getCurrentUsername();
        List<QuaMasterData> masterDatas = new ArrayList<>();
        for (MasterDataCsvBean csvBean : csvBeans) {
            String fieldName = csvBean.getFieldName();
            // 中文名判重，包括数据库的和本次传入的数据
            if (fieldNameSets.contains(fieldName)) {
                throw new ParamInvalidException(fieldName + "不能重复");
            } else {
                fieldNameSets.add(fieldName);
            }
            QuaMasterData masterData = new QuaMasterData();
            masterData.setModelId(modelId);
            masterData.setFieldName(fieldName);
            masterData.setFieldCode(csvBean.getFieldCode());
            masterData.setFieldDesc(csvBean.getFieldDesc());
            masterData.setDataType(MasterDataTypeEnum.getCodeByName(csvBean.getDataType()));
            masterData.setFieldLength(csvBean.getFieldLength());
            masterData.setFieldPrecision(csvBean.getFieldPrecision());
            masterData.setIsNull("否".equals(csvBean.isNull) ? "0" : "1");
            masterData.setIsUnique("否".equals(csvBean.isUnique) ? "0" : "1");

            masterData.setCreateTime(now);
            masterData.setCreateUser(username);
            masterData.setUpdateTime(now);
            masterData.setUpdateUser(username);
            masterData.setTenantId(tenantId);

            masterDatas.add(masterData);
        }

        this.saveBatch(masterDatas);
    }

    @Override
    public void downloadData(MasterDataRequestDto dto, HttpServletResponse response) throws ParamInvalidException {
        List<QuaMasterData> masterDataList = null;
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        if (!CollectionUtils.isEmpty(dto.getIds())) {
            QueryWrapper<QuaMasterData> queryWrapper = new QueryWrapper<QuaMasterData>().eq("flag","1").eq("tenant_id", tenantId).in("id", dto.getIds());
            masterDataList = this.list(queryWrapper);
        } else if (dto.getModelId() != null) {
            QueryWrapper<QuaMasterData> queryWrapper = new QueryWrapper<QuaMasterData>().eq("flag","1").eq("tenant_id", tenantId)
                    .in("model_id", dto.getModelId());
            masterDataList = this.list(queryWrapper);
        }
        if (masterDataList == null) {
            throw new ParamInvalidException("请选择数据");
        }
        StringBuffer sb = new StringBuffer();
        for (String header : headers) {
            sb.append(StringUtil.createCsvColumn(header)).append(FileUtils.CSV_COLUMN_SEPARATOR);
        }
        sb.append(FileUtils.CSV_ROW_SEPARATOR);

        for (QuaMasterData masterData : masterDataList) {
            sb.append(StringUtil.createCsvColumn(masterData.getFieldName())).append(FileUtils.CSV_COLUMN_SEPARATOR);
            sb.append(StringUtil.createCsvColumn(masterData.getFieldCode())).append(FileUtils.CSV_COLUMN_SEPARATOR);
            sb.append(StringUtil.createCsvColumn(masterData.getFieldDesc())).append(FileUtils.CSV_COLUMN_SEPARATOR);
            sb.append(StringUtil.createCsvColumn(MasterDataTypeEnum.getNameByCode(masterData.getDataType()))).append(FileUtils.CSV_COLUMN_SEPARATOR);
            sb.append(StringUtil.createCsvColumn(String.valueOf(masterData.getFieldLength()))).append(FileUtils.CSV_COLUMN_SEPARATOR);
            sb.append(StringUtil.createCsvColumn(String.valueOf(masterData.getFieldPrecision()))).append(FileUtils.CSV_COLUMN_SEPARATOR);
            sb.append(StringUtil.createCsvColumn("0".equals(masterData.getIsNull()) ? "否" : "是")).append(FileUtils.CSV_COLUMN_SEPARATOR);
            sb.append(StringUtil.createCsvColumn("0".equals(masterData.getIsUnique()) ? "否" : "是")).append(FileUtils.CSV_COLUMN_SEPARATOR);
            sb.append(FileUtils.CSV_ROW_SEPARATOR);
        }
        FileUtils.outCsvStream(response, sb, "data_" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + ".csv");
    }

    @Override
    public void downloadTemplateFile(HttpServletResponse response) {
        List<String> headers = ExportUtil.headersByAlias(MasterDataCsvBean.class);
        StringBuffer sb = new StringBuffer();
        for (String header : headers) {
            sb.append(StringUtil.createCsvColumn(header)).append(FileUtils.CSV_COLUMN_SEPARATOR);
        }
        sb.append(FileUtils.CSV_ROW_SEPARATOR);

        FileUtils.outCsvStream(response, sb, "template.csv");
    }

    @Override
    public void freeze(MasterDataRequestDto requestDto) throws ParamInvalidException {
        if (requestDto.getId() == null || StringUtils.isBlank(requestDto.getDataStatus())) {
            throw new ParamInvalidException("入参异常");
        }

        this.update(new UpdateWrapper<QuaMasterData>()
                .eq("tenant_id", UserUtil.getCurrentTenantId())
                .eq("id", requestDto.getId())
                .set("data_status", requestDto.getDataStatus()));
    }

    @Override
    public void open(MasterDataRequestDto requestDto) throws ParamInvalidException  {
        if (requestDto.getId() == null || StringUtils.isBlank(requestDto.getOpenStatus())) {
            throw new ParamInvalidException("入参异常");
        }

        this.update(new UpdateWrapper<QuaMasterData>()
                .eq("tenant_id", UserUtil.getCurrentTenantId())
                .eq("id", requestDto.getId())
                .set("open_status", requestDto.getOpenStatus()));
    }

    @Data
    private static class MasterDataCsvBean {
        @Alias("字段名")
        private String fieldName;
        @Alias("字段编码")
        private String fieldCode;
        @Alias("字段描述")
        private String fieldDesc;
        @Alias("数据类型")
        private String dataType;
        @Alias("长度")
        private Integer fieldLength;
        @Alias("小数位数")
        private Integer fieldPrecision;
        @Alias("是否可为空")
        private String isNull;
        @Alias("是否唯一")
        private String isUnique;
    }

}
