package com.idss.datalake.datagovern.masterdata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.datagovern.dataquality.model.SelectVo;
import com.idss.datalake.datagovern.masterdata.entity.QuaMasterData;
import com.idss.datalake.datagovern.masterdata.model.MasterDataRequestDto;
import com.idss.datalake.datagovern.masterdata.model.MasterDataResponseVo;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 主数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
public interface IQuaMasterDataService extends IService<QuaMasterData> {

    /**
     * 分页查询主数据
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<MasterDataResponseVo>> queryMasterDataPage(MasterDataRequestDto requestDto);

    /**
     * 新增或编辑主数据
     *
     * @param requestDto
     */
    void addOrUpdate(MasterDataRequestDto requestDto);

    /**
     * 删除主数据
     *
     * @param requestDto
     */
    void delete(MasterDataRequestDto requestDto);

    /**
     * 获取数据类型
     *
     * @return
     */
    List<SelectVo> getDataType();

    /**
     * 导入数据
     *
     * @param params
     */
    void importData(Map<String, Object> params);

    /**
     * 导入数据
     *
     * @param dto
     * @param response
     */
    void downloadData(MasterDataRequestDto dto, HttpServletResponse response) throws ParamInvalidException;

    /**
     * 下载模板
     *
     * @param response
     * @throws IOException
     */
    void downloadTemplateFile(HttpServletResponse response);

    void freeze(MasterDataRequestDto requestDto) throws ParamInvalidException ;

    void open(MasterDataRequestDto requestDto) throws ParamInvalidException;
}
