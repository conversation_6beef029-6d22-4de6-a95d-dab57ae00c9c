/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/22 15:31
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.masterdata.enums;

/**
 * <AUTHOR>
 * @description <p>主数据关联关系</p>
 * @date 2023年2月27日
 * @see
 */
public enum MasterDataRelation {

    INVOKE(1, "调用"),
    BELONG_TO(2, "属于"),
    CONTAIN(3, "包含");

    private Integer code;

    private String name;

    MasterDataRelation(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getTypeName(String code) {
        for (MasterDataRelation value : MasterDataRelation.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
