/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-22
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-22
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.masterdata.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.config.entity.QuaConfigMasterDataType;
import com.idss.datalake.datagovern.config.service.IQuaConfigMasterDataTypeService;
import com.idss.datalake.datagovern.masterdata.entity.QuaMasterData;
import com.idss.datalake.datagovern.masterdata.entity.QuaMasterDataModel;
import com.idss.datalake.datagovern.masterdata.entity.QuaMasterDataModelRelation;
import com.idss.datalake.datagovern.masterdata.mapper.QuaMasterDataModelMapper;
import com.idss.datalake.datagovern.masterdata.model.MasterDataModelRequestDto;
import com.idss.datalake.datagovern.masterdata.model.QuaMasterDataModelDTO;
import com.idss.datalake.datagovern.masterdata.service.IQuaMasterDataModelRelationService;
import com.idss.datalake.datagovern.masterdata.service.IQuaMasterDataModelService;
import com.idss.datalake.datagovern.masterdata.service.IQuaMasterDataService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.radar.util.UmsUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>主数据模型 manager处理类</p>
 * @since 2022-08-22
 */
@Component
public class QuaMasterDataModelManager {

    private static final Logger logger = LoggerFactory.getLogger(QuaMasterDataModelManager.class);

    @Autowired
    private IQuaMasterDataModelService iQuaMasterDataModelService;
    @Autowired
    private IQuaConfigMasterDataTypeService dataTypeService;
    @Autowired
    private IQuaMasterDataService masterDataService;
    @Autowired
    private IQuaMasterDataModelRelationService quaMasterDataModelRelationService;

    @Autowired
    private QuaMasterDataModelMapper masterDataModelMapper;

    public Map<String, Object> createPageCondition(RequestDTO requestDTO) throws ParamInvalidException {
        if (requestDTO != null && requestDTO.getGlobal() != null) {
            GlobalDTO globalDTO = requestDTO.getGlobal();
            if (globalDTO.getPageNum() == null) {
                globalDTO.setPageNum(Constant.DEFAULT_PAGE_NUM);
            }
            if (globalDTO.getPageSize() == null) {
                globalDTO.setPageSize(Constant.DEFAULT_PAGE_SIZE);
            }
            if (StringUtils.isEmpty(globalDTO.getOrderType())) {
                globalDTO.setOrderType("desc");
            }
            if (StringUtils.isEmpty(globalDTO.getOrderField())) {
                globalDTO.setOrderField("createTime");
            }
        } else {
            throw new ParamInvalidException("入参错误");
        }
        return null;
    }

    /**
     * 分页
     *
     * @param requestDto
     * @return
     * @throws Exception
     */
    public BasePageResponse<List<QuaMasterDataModel>> page(MasterDataModelRequestDto requestDto) throws Exception {
        List<QuaMasterDataModel> list = new ArrayList<>();
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<QuaMasterDataModel> page = masterDataModelMapper.queryMasterDataModelPage(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        } else {
            List<QuaMasterDataModel> models = page.getResult();
            for (QuaMasterDataModel model : models) {
                if ("1".equals(model.getModelStatus())) {
                    model.setModelStatusName("正常");
                } else {
                    model.setModelStatusName("冻结");
                }
                if ("1".equals(model.getOpenStatus())) {
                    model.setOpenStatusName("正常");
                } else {
                    model.setOpenStatusName("失效");
                }
            }
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());

    }

    public void create(QuaMasterDataModelDTO quaMasterDataModelDTO) throws ParamInvalidException {
        if (StringUtils.isAnyBlank(quaMasterDataModelDTO.getModelName(), quaMasterDataModelDTO.getModelCode())) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        List<QuaMasterDataModel> dbDataModels = iQuaMasterDataModelService.list(new QueryWrapper<QuaMasterDataModel>()
                .eq("model_code", quaMasterDataModelDTO.getModelCode()).eq("tenant_id", tenantId));
        if (CollectionUtils.isNotEmpty(dbDataModels)) {
            throw new ParamInvalidException("编码规则不能重复");
        }
        //保存
        QuaMasterDataModel quaMasterDataModel = new QuaMasterDataModel();
        ReflectionUtil.copyLomBokProperties(quaMasterDataModelDTO, quaMasterDataModel);
        quaMasterDataModel.setTenantId(tenantId);
        quaMasterDataModel.setCreateTime(LocalDateTime.now());
        quaMasterDataModel.setUpdateTime(LocalDateTime.now());
        quaMasterDataModel.setCreateUser(UserUtil.getCurrentUsername());
        quaMasterDataModel.setUpdateUser(UserUtil.getCurrentUsername());
        iQuaMasterDataModelService.save(quaMasterDataModel);
    }

    public void delete(List<Long> ids) throws ParamInvalidException {
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        // 被主数据引用，无法删除
        List<QuaMasterData> masterDataList = masterDataService.list(new QueryWrapper<QuaMasterData>().eq("tenant_id", tenantId).in("model_id", ids));
        if (CollectionUtils.isNotEmpty(masterDataList)) {
            throw new ParamInvalidException("主数据模型被引用，无法删除");
        }
        // 已建立关联关系，无法删除
        List<QuaMasterDataModelRelation> relatedModels =
                quaMasterDataModelRelationService.list(new QueryWrapper<QuaMasterDataModelRelation>().in("related_model_id", ids));
        if (CollectionUtils.isNotEmpty(relatedModels)) {
            throw new ParamInvalidException("主数据模型存在被关联关系，无法删除");
        }
        iQuaMasterDataModelService.remove(new QueryWrapper<QuaMasterDataModel>().in("id", ids).eq("tenant_id", tenantId));
        // 同时删除关联关系
        quaMasterDataModelRelationService.remove(new QueryWrapper<QuaMasterDataModelRelation>().in("model_id", ids));
    }

    public void edit(QuaMasterDataModelDTO quaMasterDataModelDTO) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(quaMasterDataModelDTO.getId())) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        List<QuaMasterDataModel> dbDataModels = iQuaMasterDataModelService.list(new QueryWrapper<QuaMasterDataModel>()
                .eq("model_code", quaMasterDataModelDTO.getModelCode()).eq("tenant_id", tenantId).ne("id", quaMasterDataModelDTO.getId()));
        if (CollectionUtils.isNotEmpty(dbDataModels)) {
            throw new ParamInvalidException("编码规则已存在");
        }
        QuaMasterDataModel dbOne = iQuaMasterDataModelService.getById(quaMasterDataModelDTO.getId());
        ReflectionUtil.copyLomBokProperties(quaMasterDataModelDTO, dbOne);
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        iQuaMasterDataModelService.saveOrUpdate(dbOne);
    }

    public void freeze(QuaMasterDataModelDTO dto) throws ParamInvalidException {
        if (dto.getId() == null || StringUtils.isBlank(dto.getModelStatus())) {
            throw new ParamInvalidException("入参异常");
        }

        iQuaMasterDataModelService.update(new UpdateWrapper<QuaMasterDataModel>()
                .eq("tenant_id", UserUtil.getCurrentTenantId())
                .eq("id", dto.getId())
                .set("model_status", dto.getModelStatus()));
    }

    public QuaMasterDataModel detail(Long id) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        QueryWrapper<QuaMasterDataModel> wrapper = new QueryWrapper();
        wrapper.eq("tenant_id", tenantId).eq("id", id);
        QuaMasterDataModel one = iQuaMasterDataModelService.getOne(wrapper);
        one.setModelTypeName(dataTypeService.getById(one.getModelTypeId()).getTypeName());
        return one;
    }

    public List<QuaConfigMasterDataType> category() {
        return dataTypeService.list(new QueryWrapper<QuaConfigMasterDataType>()
                .eq("flag", "1")
                .eq("tenant_id", UmsUtils.getUVO().getTenantId()));
    }

    /**
     * 查询除自身外的其他主数据模型
     *
     * @param modelId
     * @return
     */
    public List<QuaMasterDataModel> otherAll(Long modelId) {
        QueryWrapper<QuaMasterDataModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UmsUtils.getUVO().getTenantId());
        queryWrapper.eq("flag", "1");
        queryWrapper.ne("id", modelId);
        return iQuaMasterDataModelService.list(queryWrapper);
    }

    public void open(QuaMasterDataModelDTO dto)throws ParamInvalidException {
        if (dto.getId() == null || StringUtils.isBlank(dto.getOpenStatus())) {
            throw new ParamInvalidException("入参异常");
        }

        iQuaMasterDataModelService.update(new UpdateWrapper<QuaMasterDataModel>()
                .eq("tenant_id", UserUtil.getCurrentTenantId())
                .eq("id", dto.getId())
                .set("open_status", dto.getOpenStatus()));
    }
}