package com.idss.datalake.datagovern.masterdata.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.syslog.annotation.SysLog;
import com.idss.datalake.common.syslog.entity.OptModule;
import com.idss.datalake.common.syslog.entity.OptType;
import com.idss.datalake.datagovern.dataquality.model.SelectVo;
import com.idss.datalake.datagovern.masterdata.entity.QuaMasterData;
import com.idss.datalake.datagovern.masterdata.model.MasterDataRequestDto;
import com.idss.datalake.datagovern.masterdata.model.MasterDataResponseVo;
import com.idss.datalake.datagovern.masterdata.service.IQuaMasterDataService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 主数据 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/masterData/data")
public class MasterDataController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MasterDataController.class);

    @Autowired
    private IQuaMasterDataService masterDataService;

    /**
     * 分页查询模型
     *
     * @param requestDto
     * @return
     */
    @SysLog(logName = "分页查询", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_MASTERDATA_MAINTAIN, switchRedisStatus = true)
    @PostMapping("/page")
    public BasePageResponse<List<MasterDataResponseVo>> page(@RequestBody MasterDataRequestDto requestDto) {
        try {
            return masterDataService.queryMasterDataPage(requestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查看主数据详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable String id) {
        ResultBean resultBean = new ResultBean();
        try {
            QuaMasterData masterData = masterDataService.getById(id);
            resultBean.setContent(masterData);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 新增获取编辑主数据
     *
     * @param requestDto
     * @return
     */
    @SysLog(logName = "主数据", checkAddField = "id", optModule = OptModule.DATA_LAKE_GOVERNANCE_MASTERDATA_MAINTAIN, switchRedisStatus = true)
    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody MasterDataRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            masterDataService.addOrUpdate(requestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 删除主数据
     *
     * @param requestDto
     * @return
     */
    @SysLog(logName = "删除主数据", optType = OptType.DELETE, optModule = OptModule.DATA_LAKE_GOVERNANCE_MASTERDATA_MAINTAIN, switchRedisStatus = true)
    @PostMapping("/delete")
    public ResultBean delete(@RequestBody MasterDataRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            masterDataService.delete(requestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    @SysLog(logName = "冻结/解冻", optType = OptType.FREEZE, optModule = OptModule.DATA_LAKE_GOVERNANCE_MASTERDATA_MODEL, switchRedisStatus = true)
    @ApiOperation(value = "冻结/解冻")
    @PutMapping(value = "/freeze")
    public ResultBean freeze(@RequestBody MasterDataRequestDto requestDto) {
        try {
            masterDataService.freeze(requestDto);
            return ResultBean.success();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "开通/失效", optType = OptType.FREEZE, optModule = OptModule.DATA_LAKE_GOVERNANCE_MASTERDATA_MODEL, switchRedisStatus = true)
    @ApiOperation(value = "开通/失效")
    @PutMapping(value = "/open")
    public ResultBean open(@RequestBody MasterDataRequestDto requestDto) {
        try {
            masterDataService.open(requestDto);
            return ResultBean.success();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 获取数据类型
     *
     * @return
     */
    @GetMapping("/type")
    public ResultBean dataType() {
        ResultBean resultBean = new ResultBean();
        try {
            List<SelectVo> vos = masterDataService.getDataType();
            resultBean.setContent(vos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 导入
     */
    @PostMapping("/import")
    public ResultBean importData(@RequestBody Map<String, Object> params) {
        try {
            masterDataService.importData(params);
            return ResultBean.success();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 导出
     *
     * @param dto
     * @param response
     */
    @PostMapping("/download")
    public void downloadData(@RequestBody MasterDataRequestDto dto, HttpServletResponse response) {
        try {
            masterDataService.downloadData(dto, response);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    /**
     * 下载模板文件
     *
     * @return
     */
    @GetMapping("/downloadTemplate")
    public void downloadTemplateFile(HttpServletResponse response) {
        try {
            masterDataService.downloadTemplateFile(response);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }
}
