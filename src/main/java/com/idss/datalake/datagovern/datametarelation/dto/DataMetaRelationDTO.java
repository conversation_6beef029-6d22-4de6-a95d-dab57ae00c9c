/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-18
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-18
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datametarelation.dto;

import com.idss.datalake.datagovern.datametarelation.entity.DataMetaRelation;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>元数据-关系表 dto类</p>
 * @since 2024-06-18
 */
@Data
public class DataMetaRelationDTO extends DataMetaRelation {
    private List<Long> ids;
}