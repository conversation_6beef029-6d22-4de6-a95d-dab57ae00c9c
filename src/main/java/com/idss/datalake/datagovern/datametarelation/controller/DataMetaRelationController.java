/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-18
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-18
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datametarelation.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datagovern.datametarelation.dto.DataMetaRelationDTO;
import com.idss.datalake.datagovern.datametarelation.manager.DataMetaRelationManager;
import com.idss.datalake.datagovern.datametarelation.model.RelationModel;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description <p>元数据-关系表 前端控制器</p>
 * @since 2024-06-18
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/datagovern/data-meta-relation")
public class DataMetaRelationController {
    private static final Logger logger = LoggerFactory.getLogger(DataMetaRelationController.class);
    @Autowired
    private DataMetaRelationManager dataMetaRelationManager;

    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody DataMetaRelationDTO dto) {
        try {
            dataMetaRelationManager.create(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除")
    @GetMapping(value = "/delete/{id}")
    public ResultBean delete(@PathVariable Long id) {
        try {
            dataMetaRelationManager.delete(id);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "获取关系图数据")
    @PostMapping(value = "/getRelationList")
    public ResultBean getRelationList(@RequestBody DataMetaRelationDTO dto) {
        try {
            RelationModel relationModel = dataMetaRelationManager.getRelationList(dto);
            return ResultBean.success(relationModel);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    @ApiOperation(value = "获取字段关系图数据")
    @PostMapping(value = "/getFieldRelationList")
    public ResultBean getFieldRelationList(@RequestBody DataMetaRelationDTO dto) {
        try {
            RelationModel relationModel = dataMetaRelationManager.getFieldRelationList(dto);
            return ResultBean.success(relationModel);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }

}
