/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-18
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-18
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datametarelation.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.MetaDataUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.datametarelation.dto.DataMetaRelationDTO;
import com.idss.datalake.datagovern.datametarelation.entity.DataMetaRelation;
import com.idss.datalake.datagovern.datametarelation.model.Link;
import com.idss.datalake.datagovern.datametarelation.model.Node;
import com.idss.datalake.datagovern.datametarelation.model.RelationModel;
import com.idss.datalake.datagovern.datametarelation.service.IDataMetaRelationService;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItem;
import com.idss.datalake.datagovern.dictionary.enums.ItemTypeEnum;
import com.idss.datalake.datagovern.dictionary.manager.DataDictionaryItemManager;
import com.idss.datalake.datagovern.dictionary.service.IDataDictionaryItemService;
import com.idss.datalake.datagovern.metadata.model.detail.entity.*;
import com.idss.datalake.datagovern.metadata.model.detail.service.*;
import com.idss.datalake.datagovern.metadata.model.job.pojo.vo.ColumnInfoVo;
import com.idss.datalake.datagovern.metadata.model.job.service.QuaJobService;
import com.idss.datalake.datamart.enums.ElementTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description <p>元数据-关系表 manager处理类</p>
 * @since 2024-06-18
 */
@Component
public class DataMetaRelationManager {
    private static final Logger logger = LoggerFactory.getLogger(DataMetaRelationManager.class);

    @Autowired
    private IDataMetaRelationService iDataMetaRelationService;
    @Autowired
    private DataDictionaryItemManager dataDictionaryItemManager;
    @Autowired
    private IDataDictionaryItemService dictionaryItemService;
    @Autowired
    private EsElementDetailFieldService esElementDetailFieldService;
    @Autowired
    private ChElementDetailColumnService chElementDetailColumnService;
    @Autowired
    private IQuaWebMysqlElementDetailColumnService mysqlElementDetailColumnService;
    @Autowired
    private IQuaWebHiveElementDetailColumnService hiveElementDetailColumnService;

    @Autowired
    private QuaJobService quaJobService;

    public void create(DataMetaRelationDTO dto) {
        if (StringUtils.isAnyBlank(dto.getSourceDatasourceType(), dto.getSourceDatabaseName(), dto.getSourceTableName(),
                dto.getTargetDatasourceType(), dto.getTargetDatabaseName(), dto.getTargetTableName()) ||
                ObjectUtils.isEmpty(dto.getSourceElementId()) || ObjectUtils.isEmpty(dto.getTargetElementId())) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = UserUtil.getLongCurrentTenantId();
        QueryWrapper<DataMetaRelation> wrapper = new QueryWrapper<DataMetaRelation>()
                .eq("tenant_id", tenantId)
                .eq("source_item_type", ItemTypeEnum.field.name())
                .eq("source_element_id", dto.getSourceElementId())
                .eq("source_datasource_type", dto.getSourceDatasourceType())
                .eq("source_database_name", dto.getSourceDatabaseName())
                .eq("source_table_name", dto.getSourceTableName());
        int count = iDataMetaRelationService.count(wrapper);
        if (count > 50) {
            throw new ParamInvalidException("最多关联50个字段");
        }
        wrapper.eq("source_field_name", dto.getSourceFieldName());
        count = iDataMetaRelationService.count(wrapper);
        if (count > 0) {
            throw new ParamInvalidException("关联已存在");
        }

        DataMetaRelation dataMetaRelation = new DataMetaRelation();
        ReflectionUtil.copyLomBokProperties(dto, dataMetaRelation);

        dataMetaRelation.setSourceItemType(ItemTypeEnum.field.name());
        dataMetaRelation.setSourceUniqueId(MetaDataUtil.getFieldUniqueId(dataMetaRelation.getSourceElementId(),
                dataMetaRelation.getSourceDatasourceType(), dataMetaRelation.getSourceItemType(), dataMetaRelation.getSourceDatabaseName(),
                dataMetaRelation.getSourceTableName(), dataMetaRelation.getSourceFieldName()));

        dataMetaRelation.setTargetItemType(ItemTypeEnum.field.name());
        dataMetaRelation.setTargetUniqueId(MetaDataUtil.getFieldUniqueId(dataMetaRelation.getTargetElementId(),
                dataMetaRelation.getTargetDatasourceType(), dataMetaRelation.getTargetItemType(), dataMetaRelation.getTargetDatabaseName(),
                dataMetaRelation.getTargetTableName(), dataMetaRelation.getTargetFieldName()));

        dataMetaRelation.setTenantId(tenantId);
        dataMetaRelation.setCreateTime(LocalDateTime.now());
        dataMetaRelation.setUpdateTime(LocalDateTime.now());
        dataMetaRelation.setCreateUser(UserUtil.getCurrentUsername());
        dataMetaRelation.setUpdateUser(UserUtil.getCurrentUsername());
        iDataMetaRelationService.save(dataMetaRelation);
    }

    /**
     * 删除关联关系
     *
     * @param id
     */
    public void delete(Long id) {
        Long tenantId = UserUtil.getLongCurrentTenantId();
        if (id == null) {
            throw new ParamInvalidException("入参异常");
        }
        iDataMetaRelationService.remove(new QueryWrapper<DataMetaRelation>().eq("id", id).eq("tenant_id", tenantId));
    }

    /**
     * 获取关系图数据
     *
     * @param dto
     * @return
     */
    public RelationModel getRelationList(DataMetaRelationDTO dto) {
        RelationModel relationModel = new RelationModel();
        QueryWrapper<DataMetaRelation> wrapper = new QueryWrapper<DataMetaRelation>()
                .eq("source_item_type", ItemTypeEnum.field.name())
                .eq("source_element_id", dto.getSourceElementId())
                .eq("source_datasource_type", dto.getSourceDatasourceType())
                .eq("source_database_name", dto.getSourceDatabaseName())
                .eq("source_table_name", dto.getSourceTableName());
        List<DataMetaRelation> metaRelationList = iDataMetaRelationService.list(wrapper);
        if (CollectionUtils.isEmpty(metaRelationList)) {
            return relationModel;
        }
        DataMetaRelation sourceRelation = metaRelationList.get(0);
        // 起始节点
        Node sourceNode = dataDictionaryItemManager.queryTableNode(sourceRelation.getSourceElementId(), sourceRelation.getSourceDatasourceType(),
                sourceRelation.getSourceDatabaseName(), sourceRelation.getSourceTableName());
        if (sourceNode == null) {
            return relationModel;
        }
        int id = 1;
        sourceNode.setId(String.valueOf(id));
        relationModel.getNodes().add(sourceNode);
        // 获取目标节点
        for (DataMetaRelation relation : metaRelationList) {
            Node targetNode = dataDictionaryItemManager.queryTableNode(relation.getTargetElementId(), relation.getTargetDatasourceType(),
                    relation.getTargetDatabaseName(), relation.getTargetTableName());
            if (targetNode == null) {
                continue;
            }
            targetNode.setId(String.valueOf(++id));
            Node fieldNode = dataDictionaryItemManager.queryNode(relation.getTargetElementId(), relation.getTargetItemType(),
                    relation.getTargetDatasourceType(), relation.getTargetDatabaseName(), relation.getTargetTableName(),
                    relation.getTargetFieldName());
            if (fieldNode != null) {
                targetNode.getFields().add(fieldNode);
            }

            relationModel.getNodes().add(targetNode);
            // 创建关联关系
            Link link = new Link();
            link.setSource(sourceNode.getId().toString());
            link.setTarget(targetNode.getId().toString());
            relationModel.getLinks().add(link);
        }

        return relationModel;
    }

    /**
     * 获取字段关系图数据
     *
     * @param dto
     * @return
     */
    public RelationModel getFieldRelationList(DataMetaRelationDTO dto) {
        RelationModel relationModel = new RelationModel();
        QueryWrapper<DataMetaRelation> wrapper = new QueryWrapper<DataMetaRelation>()
                .eq("source_item_type", ItemTypeEnum.field.name())
                .eq("source_element_id", dto.getSourceElementId())
                .eq("source_datasource_type", dto.getSourceDatasourceType())
                .eq("source_database_name", dto.getSourceDatabaseName())
                .eq("source_table_name", dto.getSourceTableName());
        List<DataMetaRelation> metaRelationList = iDataMetaRelationService.list(wrapper);
        if (CollectionUtils.isEmpty(metaRelationList)) {
            return relationModel;
        }
        DataMetaRelation sourceRelation = metaRelationList.get(0);
        // 起始节点表
        Node sourceNode = dataDictionaryItemManager.queryTableNode(sourceRelation.getSourceElementId(), sourceRelation.getSourceDatasourceType(),
                sourceRelation.getSourceDatabaseName(), sourceRelation.getSourceTableName());
        if (sourceNode == null) {
            return relationModel;
        }
        sourceNode.setId(generateTableKey(sourceRelation.getSourceElementId(), sourceRelation.getSourceDatabaseName(),
                sourceRelation.getSourceTableName(), sourceRelation.getSourceFieldName(), true));
        // 起始节点字段
        if (DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().equals(dto.getSourceDatasourceType())) {
            List<ChElementDetailColumn> list = chElementDetailColumnService.list(
                    new QueryWrapper<ChElementDetailColumn>()
                            .eq("element_id", dto.getSourceElementId())
                            .eq("db_name",dto.getSourceDatabaseName())
                            .eq("table_name",dto.getSourceTableName()));
            for (ChElementDetailColumn item : list) {
                Node fieldNode = new Node();
                fieldNode.setId(generateTableKey(item.getElementId(), item.getDbName(), item.getTableName(), item.getColumnName(), false));
                fieldNode.setName(item.getColumnName());
                sourceNode.getFields().add(fieldNode);
            }
        } else if (DATA_SOURCE_TYPE_ENUM.MYSQL.getName().equals(dto.getSourceDatasourceType())) {
            List<QuaWebMysqlElementDetailColumn> list = mysqlElementDetailColumnService.list(
                    new QueryWrapper<QuaWebMysqlElementDetailColumn>()
                            .eq("element_id", dto.getSourceElementId())
                            .eq("db_name",dto.getSourceDatabaseName())
                            .eq("table_name",dto.getSourceTableName()));
            for (QuaWebMysqlElementDetailColumn item : list) {
                Node fieldNode = new Node();
                fieldNode.setId(generateTableKey(item.getElementId(), item.getDbName(), item.getTableName(), item.getColumnName(), false));
                fieldNode.setName(item.getColumnName());
                sourceNode.getFields().add(fieldNode);
            }
        }else if (DATA_SOURCE_TYPE_ENUM.HIVE.getName().equals(dto.getSourceDatasourceType())) {
            List<QuaWebHiveElementDetailColumn> list = hiveElementDetailColumnService.list(
                    new QueryWrapper<QuaWebHiveElementDetailColumn>()
                            .eq("element_id", dto.getSourceElementId())
                            .eq("db_name",dto.getSourceDatabaseName())
                            .eq("table_name",dto.getSourceTableName()));
            for (QuaWebHiveElementDetailColumn item : list) {
                Node fieldNode = new Node();
                fieldNode.setId(generateTableKey(item.getElementId(), item.getDbName(), item.getTableName(), item.getColumnName(), false));
                fieldNode.setName(item.getColumnName());
                sourceNode.getFields().add(fieldNode);
            }
        }else if (DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().equals(dto.getSourceDatasourceType())) {
            List<EsElementDetailField> list = esElementDetailFieldService.list(
                    new QueryWrapper<EsElementDetailField>()
                            .eq("element_id", dto.getSourceElementId())
                            .eq("index_name", dto.getSourceTableName()));
            for (EsElementDetailField item : list) {
                Node fieldNode = new Node();
                fieldNode.setId(generateTableKey(item.getElementId(), "", item.getIndexName(), item.getFieldName(), false));
                fieldNode.setName(item.getFieldName());
                sourceNode.getFields().add(fieldNode);
            }
        }
        relationModel.getNodes().add(sourceNode);

        // 获取目标节点
        Set<String> tableKeySet = new HashSet<>();
        for (DataMetaRelation relation : metaRelationList) {
            String tableKey = generateTableKey(relation.getTargetElementId(), relation.getTargetDatasourceType(), relation.getTargetDatabaseName(),
                    relation.getTargetTableName(), true);
            if (tableKeySet.contains(tableKey)) {
                continue;
            }
            Node targetNode = dataDictionaryItemManager.queryTableNode(relation.getTargetElementId(), relation.getTargetDatasourceType(),
                    relation.getTargetDatabaseName(), relation.getTargetTableName());
            if (targetNode == null) {
                continue;
            }

            targetNode.setId(tableKey);
            List<ColumnInfoVo> columnInfoVos = quaJobService.queryTableFieldList(relation.getTargetElementId(), relation.getTargetDatasourceType(),
                    relation.getTargetDatabaseName(), relation.getTargetTableName());
            for (ColumnInfoVo columnInfoVo : columnInfoVos) {
                Node fieldNode = new Node();
                fieldNode.setId(generateTableKey(columnInfoVo.getElementId(), columnInfoVo.getDbName(), columnInfoVo.getTableName(),
                        columnInfoVo.getColumnName(), false));
                fieldNode.setName(columnInfoVo.getColumnName());
                targetNode.getFields().add(fieldNode);
            }

            relationModel.getNodes().add(targetNode);
            // 创建关联关系
            Link link = new Link();
            link.setSource(generateTableKey(relation.getSourceElementId(), relation.getSourceDatabaseName(), relation.getSourceTableName(),
                    relation.getSourceFieldName(), false));
            link.setTarget(generateTableKey(relation.getTargetElementId(), relation.getTargetDatabaseName(), relation.getTargetTableName(),
                    relation.getTargetFieldName(), false));
            relationModel.getLinks().add(link);
        }

        return relationModel;
    }

    private String generateTableKey(Long elementId, String databaseName, String tableName, String fieldName, boolean tableKey) {
        if (StringUtils.isBlank(databaseName)) {
            databaseName = "";
        }
        if (tableKey) {
            return elementId + "_" + databaseName + "_" + tableName;
        } else {
            return elementId + "_" + databaseName + "_" + tableName + "_" + fieldName;
        }
    }
}