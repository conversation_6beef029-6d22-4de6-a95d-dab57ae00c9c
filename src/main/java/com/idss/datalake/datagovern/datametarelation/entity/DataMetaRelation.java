/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-18
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-18
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datametarelation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>元数据-关系表</p>
 * @since 2024-06-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("data_meta_relation")
public class DataMetaRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 起始元数据ID
     */
    @TableField("source_element_id")
    private Long sourceElementId;

    /**
     * 起始类型 table-表  field-字段
     */
    @TableField("source_item_type")
    private String sourceItemType;

    /**
     * 起始数据源类型
     */
    @TableField("source_datasource_type")
    private String sourceDatasourceType;

    /**
     * 起始数据库名称
     */
    @TableField("source_database_name")
    private String sourceDatabaseName;

    /**
     * 起始表名称
     */
    @TableField("source_table_name")
    private String sourceTableName;

    /**
     * 起始字段名称
     */
    @TableField("source_field_name")
    private String sourceFieldName;

    /**
     * 起始元数据唯一字段
     */
    @TableField("source_unique_id")
    private String sourceUniqueId;

    /**
     * 目标元数据ID
     */
    @TableField("target_element_id")
    private Long targetElementId;

    /**
     * 目标类型 table-表  field-字段
     */
    @TableField("target_item_type")
    private String targetItemType;

    /**
     * 目标数据源类型
     */
    @TableField("target_datasource_type")
    private String targetDatasourceType;

    /**
     * 目标数据库名称
     */
    @TableField("target_database_name")
    private String targetDatabaseName;

    /**
     * 目标表名称
     */
    @TableField("target_table_name")
    private String targetTableName;

    /**
     * 目标字段名称
     */
    @TableField("target_field_name")
    private String targetFieldName;

    /**
     * 目标元数据唯一字段
     */
    @TableField("target_unique_id")
    private String targetUniqueId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;


}
