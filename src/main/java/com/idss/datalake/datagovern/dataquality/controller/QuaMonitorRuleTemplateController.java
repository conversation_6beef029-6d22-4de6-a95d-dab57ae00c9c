package com.idss.datalake.datagovern.dataquality.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.syslog.annotation.SysLog;
import com.idss.datalake.common.syslog.entity.OptModule;
import com.idss.datalake.common.syslog.entity.OptType;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleTemplate;
import com.idss.datalake.datagovern.dataquality.model.ReportRequestDto;
import com.idss.datalake.datagovern.dataquality.model.RuleRequestDto;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorRuleTemplateService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeDatatimeFunctionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数据质量-模型管理 前端控制器
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@RestController
@Slf4j
@RequestMapping(Constant.API_PREFIX + "/dataQuality/rule")
public class QuaMonitorRuleTemplateController {

    @Autowired
    private IQuaMonitorRuleTemplateService ruleTemplateService;

    /**
     * 分页查询监测规则
     *
     * @param ruleRequestDto
     * @return
     */
    @SysLog(logName = "查询规则", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_RULE, switchRedisStatus = true)
    @PostMapping("/page")
    public BasePageResponse<List<QuaMonitorRuleTemplate>> page(@RequestBody RuleRequestDto ruleRequestDto) {
        try {
            return ruleTemplateService.queryRulePage(ruleRequestDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 新增或编辑规则
     *
     * @param ruleTemplate
     * @return
     */
    @SysLog(logName = "质量监测规则", checkAddField = "id", optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_RULE, switchRedisStatus = true)
    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody QuaMonitorRuleTemplate ruleTemplate) {
        ResultBean resultBean = new ResultBean();
        try {
            ruleTemplateService.addOrUpdate(ruleTemplate);
        } catch (ParamInvalidException e) {
            log.error(e.getMessage(), e);
            resultBean = ResultBean.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 取消新增
     *
     * @param ruleTemplate
     * @return
     */
    @PostMapping("/add/cancel")
    public ResultBean addCancel(@RequestBody QuaMonitorRuleTemplate ruleTemplate) {
        try {
            ruleTemplateService.addCancel(ruleTemplate);
            return ResultBean.success("操作成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("操作失败");
        }
    }

    /**
     * 查询规则详情
     *
     * @param id
     * @return
     */
    @SysLog(logName = "查询规则详情", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_RULE, switchRedisStatus = true)
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            QuaMonitorRuleTemplate ruleTemplate = ruleTemplateService.queryRuleDetail(id);
            return ResultBean.success(ruleTemplate, "查询成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 删除规则
     *
     * @param requestDto
     * @return
     */
    @SysLog(logName = "删除规则", optType = OptType.DELETE, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_RULE, switchRedisStatus = true)
    @PostMapping("/delete")
    public ResultBean delete(@RequestBody RuleRequestDto requestDto) {
        try {
            ruleTemplateService.delete(requestDto);
            return ResultBean.success("删除成功");
        } catch (ParamInvalidException e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("删除失败");
        }
    }

    /**
     * 分页查询质量报告信息
     *
     * @param ruleRequestDto
     * @return
     */
    @SysLog(logName = "查询规则", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_RULE, switchRedisStatus = true)
    @PostMapping("/report")
    public BaseResponse report(@RequestBody ReportRequestDto ruleRequestDto) {
        try {
            return ruleTemplateService.report(ruleRequestDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return BaseResponse.error("查询失败");
        }
    }

    /**
     * 表字段对应的稽核信息
     *
     * @param ruleRequestDto
     * @return
     */
    @SysLog(logName = "查询规则", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_RULE, switchRedisStatus = true)
    @PostMapping("/report/table")
    public BaseResponse reportTable(@RequestBody ReportRequestDto ruleRequestDto) {
        try {
            return ruleTemplateService.reportTable(ruleRequestDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return BaseResponse.error("查询失败");
        }
    }

    /**
     * 查询时间函数列表
     *
     * @return
     */
    @GetMapping("/datetimeFunctionList")
    public ResultBean datetimeFunctionList() {
        try {
            return ResultBean.success(ElementTypeDatatimeFunctionEnum.getFunctionList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }
}
