/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/16 16:18
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.controller;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.syslog.annotation.SysLog;
import com.idss.datalake.common.syslog.entity.OptModule;
import com.idss.datalake.common.syslog.entity.OptType;
import com.idss.datalake.datagovern.dataquality.model.ModelRequestDto;
import com.idss.datalake.datagovern.dataquality.model.ModelResponseVo;
import com.idss.datalake.datagovern.dataquality.model.ModelTaskVo;
import com.idss.datalake.datagovern.dataquality.model.SelectVo;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorModelService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.portal.dto.LineData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.ws.rs.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Copyright 2022 IDSS
 * <p> 质量监测模型控制器
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/16 16:18
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/dataQuality/model")
public class ModelController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModelController.class);

    @Autowired
    private IQuaMonitorModelService monitorModelService;

    /**
     * 分页查询模型
     *
     * @param modelRequestDto
     * @return
     */
    @SysLog(logName = "分页查询", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_MODEL, switchRedisStatus = true)
    @PostMapping("/page")
    public BasePageResponse<List<ModelResponseVo>> page(@RequestBody ModelRequestDto modelRequestDto) {
        try {
            return monitorModelService.queryModelPage(modelRequestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查看模型详情
     *
     * @param id
     * @return
     */
    @SysLog(logName = "详情", optType = OptType.DETAIL, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_MODEL, switchRedisStatus = true)
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable String id) {
        ResultBean resultBean = new ResultBean();
        try {
            ModelResponseVo vo = monitorModelService.queryModelDetail(id);
            resultBean.setContent(vo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 新增获取编辑模型
     *
     * @param modelRequestDto
     * @return
     */
    @SysLog(logName = "质量监测模型", checkAddField = "id", optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_MODEL, switchRedisStatus = true)
    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody ModelRequestDto modelRequestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            monitorModelService.addOrUpdate(modelRequestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 获取资源类型
     *
     * @return
     */
    @GetMapping("/elementType")
    public ResultBean detail() {
        ResultBean resultBean = new ResultBean();
        try {
            List<SelectVo> vos = new ArrayList<>();
            for (ElementTypeEnum value : ElementTypeEnum.values()) {
                SelectVo vo = new SelectVo();
                vo.setCode(value.getCode());
                vo.setValue(value.getDesc());

                vos.add(vo);
            }
            resultBean.setContent(vos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 删除模型
     *
     * @param modelRequestDto
     * @return
     */
    @SysLog(logName = "删除模型", optType = OptType.DELETE, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_MODEL, switchRedisStatus = true)
    @PostMapping("/delete")
    public ResultBean delete(@RequestBody ModelRequestDto modelRequestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            monitorModelService.delete(modelRequestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    @GetMapping("/all")
    public ResultBean allModel() {
        ResultBean resultBean = new ResultBean();
        try {
            List<Map<String, Object>> vos = monitorModelService.queryAllModel();
            resultBean.setContent(vos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 启用/关闭数据源
     * @return
     */
    @PostMapping("/openOrClose")
    public ResultBean openOrClose(@RequestBody ModelRequestDto modelRequestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            monitorModelService.openOrClose(modelRequestDto);
            resultBean.setMessage("操作成功");
        } catch (ParamInvalidException e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail(e.getMessage());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 启用/关闭数据源
     * @return
     */
    @GetMapping("/task/detail/{id}")
    public ResultBean openOrClose(@PathVariable Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            ModelTaskVo modelTaskVo  = monitorModelService.taskDetail(id);
            resultBean.setContent(modelTaskVo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 数据源任务趋势
     * @return
     */
    @GetMapping("/task/trend/{id}")
    public ResultBean taskTrend(@PathVariable Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            LineData lineData  = monitorModelService.taskTrend(id);
            resultBean.setContent(lineData);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 启用/关闭数据源
     * @return
     */
    @PostMapping("/task/detail/page")
    public BaseResponse taskPage(@RequestBody ModelRequestDto modelRequestDto) {
        try {
            return monitorModelService.queryTaskDetailPage(modelRequestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return BaseResponse.error("查询失败");
        }
    }
}
