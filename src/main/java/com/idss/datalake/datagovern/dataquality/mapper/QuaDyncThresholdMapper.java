package com.idss.datalake.datagovern.dataquality.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.dataquality.dto.DyncThresholdJobPageRequest;
import com.idss.datalake.datagovern.dataquality.dto.DyncThresholdJobPageVo;
import com.idss.datalake.datagovern.dataquality.dto.DyncThresholdPageRequest;
import com.idss.datalake.datagovern.dataquality.entity.QuaDyncThreshold;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;


/**
 * <p>
 * 数据质量动态阈值管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
public interface QuaDyncThresholdMapper extends BaseMapper<QuaDyncThreshold> {
    Page<DyncThresholdJobPageVo> jobLogPage(DyncThresholdJobPageRequest request);

    Page<QuaDyncThreshold> page(DyncThresholdPageRequest request);
}
