package com.idss.datalake.datagovern.dataquality.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.dataquality.model.*;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTableDto;

import java.util.List;

/**
 * <p>
 * 数据质量-模型管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
public interface QuaMonitorRuleTemplateMapper extends BaseMapper<QuaMonitorRuleTemplate> {

    Page<NodeTableDto> getCHTables(ReportRequestDto requestDto);

    Page<NodeTableDto> getMysqlTables(ReportRequestDto requestDto);

    Page<NodeTableDto> getIndexes(ReportRequestDto requestDto);

    Page<NodeTableDto> getHiveTables(ReportRequestDto requestDto);

    List<ReportRuleVo> reportRule(ReportRequestDto requestDto);

    List<ReportJobVo> reportJob(ReportRequestDto requestDto);

    List<ReportColumnVo> ruleTemplate(ReportRequestDto requestDto);

    Page<ReportColumnInfo> queryCHColumn(ReportRequestDto requestDto);

    Page<ReportColumnInfo> queryMysqlColumn(ReportRequestDto requestDto);

    Page<ReportColumnInfo> queryESColumn(ReportRequestDto requestDto);

    Page<ReportColumnInfo> queryHiveColumn(ReportRequestDto requestDto);
}
