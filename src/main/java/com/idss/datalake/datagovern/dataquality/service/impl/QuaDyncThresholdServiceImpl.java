package com.idss.datalake.datagovern.dataquality.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.dataquality.dto.DyncThresholdJobPageRequest;
import com.idss.datalake.datagovern.dataquality.dto.DyncThresholdJobPageVo;
import com.idss.datalake.datagovern.dataquality.dto.DyncThresholdPageRequest;
import com.idss.datalake.datagovern.dataquality.entity.QuaDyncThreshold;
import com.idss.datalake.datagovern.dataquality.mapper.QuaDyncThresholdMapper;
import com.idss.datalake.datagovern.dataquality.service.IQuaDyncThresholdService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailIndex;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHiveElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailColumn;
import com.idss.datalake.datagovern.metadata.model.detail.service.ChElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.detail.service.EsElementDetailIndexService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHiveElementDetailColumnService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebMysqlElementDetailColumnService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 数据质量动态阈值管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Service
public class QuaDyncThresholdServiceImpl extends ServiceImpl<QuaDyncThresholdMapper, QuaDyncThreshold> implements IQuaDyncThresholdService {
    @Resource
    private QuaDyncThresholdMapper quaDyncThresholdMapper;

    @Autowired
    private EsElementDetailIndexService esElementDetailIndexService;
    @Autowired
    private IQuaWebMysqlElementDetailColumnService mysqlElementDetailColumnService;
    @Autowired
    private IQuaWebHiveElementDetailColumnService hiveElementDetailColumnService;
    @Autowired
    private ChElementDetailColumnService chElementDetailColumnService;

    @Override
    public BasePageResponse<List<DyncThresholdJobPageVo>> queryJobPage(DyncThresholdJobPageRequest requestDto) {
        List<DyncThresholdJobPageVo> list = new ArrayList<>();
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<DyncThresholdJobPageVo> page = quaDyncThresholdMapper.jobLogPage(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());

    }

    @Override
    public BasePageResponse<List<QuaDyncThreshold>> page(DyncThresholdPageRequest requestDto) {
        List<QuaDyncThreshold> list = new ArrayList<>();
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<QuaDyncThreshold> page = quaDyncThresholdMapper.page(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
    }


    @Override
    public ResultBean elementDbname(String sourceType) {
        UserValueObject uvo = UmsUtils.getUVO();
        List<JSONObject> result = new ArrayList<>();
        if ("mysql".equals(sourceType)) {
            List<QuaWebMysqlElementDetailColumn> data = mysqlElementDetailColumnService.list(new QueryWrapper<QuaWebMysqlElementDetailColumn>().select("element_id", "db_name").eq("tenant_id", uvo.getTenantId()).groupBy("element_id", "db_name"));
            for (QuaWebMysqlElementDetailColumn datum : data) {
                JSONObject item = new JSONObject();
                item.put("element_id", datum.getElementId());
                item.put("dbName", datum.getDbName());
                result.add(item);
            }
        } else if ("hive".equals(sourceType)) {
            List<QuaWebHiveElementDetailColumn> data = hiveElementDetailColumnService.list(new QueryWrapper<QuaWebHiveElementDetailColumn>().select("element_id", "db_name").eq("tenant_id", uvo.getTenantId()).groupBy("element_id", "db_name"));
            for (QuaWebHiveElementDetailColumn datum : data) {
                JSONObject item = new JSONObject();
                item.put("element_id", datum.getElementId());
                item.put("dbName", datum.getDbName());
                result.add(item);
            }
        } else if ("clickhouse".equals(sourceType)) {
            List<ChElementDetailColumn> data = chElementDetailColumnService.list(new QueryWrapper<ChElementDetailColumn>().select("element_id", "db_name").eq("tenant_id", uvo.getTenantId()).groupBy("element_id", "db_name"));
            for (ChElementDetailColumn datum : data) {
                JSONObject item = new JSONObject();
                item.put("element_id", datum.getElementId());
                item.put("dbName", datum.getDbName());
                result.add(item);
            }
        }
        return  ResultBean.success(result);
    }

    @Override
    public ResultBean elementTable(String sourceType,Long elementId,String dbName) {
        List<JSONObject> result = new ArrayList<>();
        if ("mysql".equals(sourceType)) {
            List<QuaWebMysqlElementDetailColumn> data = mysqlElementDetailColumnService.list(new QueryWrapper<QuaWebMysqlElementDetailColumn>().select("distinct table_name").eq("element_id", elementId).eq("db_name",dbName));
            for (QuaWebMysqlElementDetailColumn datum : data) {
                JSONObject item = new JSONObject();
                item.put("element_id", elementId);
                item.put("tableName", datum.getTableName());
                result.add(item);
            }
        } else if ("hive".equals(sourceType)) {
            List<QuaWebHiveElementDetailColumn> data = hiveElementDetailColumnService.list(new QueryWrapper<QuaWebHiveElementDetailColumn>().select("distinct table_name").eq("element_id", elementId).eq("db_name",dbName));
            for (QuaWebHiveElementDetailColumn datum : data) {
                JSONObject item = new JSONObject();
                item.put("element_id", elementId);
                item.put("tableName", datum.getTableName());
                result.add(item);
            }
        } else if ("clickhouse".equals(sourceType)) {
            List<ChElementDetailColumn> data = chElementDetailColumnService.list(new QueryWrapper<ChElementDetailColumn>().select("distinct table_name").eq("element_id", elementId).eq("db_name",dbName));
            for (ChElementDetailColumn datum : data) {
                JSONObject item = new JSONObject();
                item.put("element_id", elementId);
                item.put("tableName", datum.getTableName());
                result.add(item);
            }
        }
        return  ResultBean.success(result);
    }

    @Override
    public ResultBean elementIndex() {
        UserValueObject uvo = UmsUtils.getUVO();
        List<JSONObject> result = new ArrayList<>();
        List<EsElementDetailIndex> data = esElementDetailIndexService.list(new QueryWrapper<EsElementDetailIndex>().select("element_id", "index_name").eq("tenant_id", uvo.getTenantId()).groupBy("element_id", "index_name"));
        for (EsElementDetailIndex datum : data) {
            JSONObject item = new JSONObject();
            item.put("element_id", datum.getElementId());
            item.put("indexName", datum.getIndexName());
            result.add(item);
        }
        return  ResultBean.success(result);
    }
}
