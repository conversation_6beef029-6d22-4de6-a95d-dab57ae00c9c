package com.idss.datalake.datagovern.dataquality.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 质量报告
 */
@Data
public class TaskReport {

    private Long id;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 库名
     */
    private String dbName;

    /**
     * 字段总数
     */
    private Integer columnCnt;

    /**
     * 稽核字段数
     */
    private Integer checkColumnCnt;

    /**
     * 稽核模板数
     */
    private Integer checkTemplateCnt;

    /**
     * 模型名称
     */
    private String ruleTypeName;

    /**
     * 稽核次数
     */
    private Integer checkCnt;

    /**
     * 最新评分
     */
    private String score;

    /**
     * 最新稽核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkTime;

    private String templateId;

    private Long taskId;

    private String ipPort;
}
