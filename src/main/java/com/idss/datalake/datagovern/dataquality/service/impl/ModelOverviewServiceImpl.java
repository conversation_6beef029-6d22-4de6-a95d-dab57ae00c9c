package com.idss.datalake.datagovern.dataquality.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.util.TimeRange;
import com.idss.datalake.datagovern.config.entity.QuaAssetHighValueStatistics;
import com.idss.datalake.datagovern.config.entity.QuaAssetIsolatedIslandStatistics;
import com.idss.datalake.datagovern.config.entity.QuaAssetLowMaintenanceStatistics;
import com.idss.datalake.datagovern.config.entity.QuaAssetLowQualityStatistics;
import com.idss.datalake.datagovern.config.service.IQuaAssetHighValueStatisticsService;
import com.idss.datalake.datagovern.config.service.IQuaAssetIsolatedIslandStatisticsService;
import com.idss.datalake.datagovern.config.service.IQuaAssetLowMaintenanceStatisticsService;
import com.idss.datalake.datagovern.config.service.IQuaAssetLowQualityStatisticsService;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorJob;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorModel;
import com.idss.datalake.datagovern.dataquality.enums.WarnLevelEnum;
import com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorModelMapper;
import com.idss.datalake.datagovern.dataquality.model.ModelOverviewRequestDto;
import com.idss.datalake.datagovern.dataquality.model.ModelOverviewVo;
import com.idss.datalake.datagovern.dataquality.service.IModelOverviewService;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorJobService;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorModelService;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.portal.dto.BarYData;
import com.idss.datalake.portal.dto.MultiLine;
import com.idss.datalake.portal.dto.MultiLineData;
import com.idss.datalake.portal.dto.PieData;
import com.idss.radar.datasource.DatasourceType;
import com.idss.radar.util.UmsUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ModelOverviewServiceImpl implements IModelOverviewService {

    @Autowired
    private IQuaMonitorJobService monitorJobService;

    @Autowired
    private IQuaMonitorModelService monitorModelService;

    @Autowired
    private QuaMonitorModelMapper monitorModelMapper;

    @Autowired
    private IQuaAssetLowMaintenanceStatisticsService lowMaintenanceStatisticsService;
    @Autowired
    private IQuaAssetLowQualityStatisticsService lowQualityStatisticsService;
    @Autowired
    private IQuaAssetHighValueStatisticsService highValueStatisticsService;
    @Autowired
    private IQuaAssetIsolatedIslandStatisticsService islandStatisticsService;


    private static final String INCREASE_TB = "tb";

    private static final String INCREASE_HB = "hb";

    @Override
    public Map<String, Object> general(ModelOverviewRequestDto requestDto) throws Exception {
        Map<String, Object> result = new HashMap<>();
        int tenantId = UmsUtils.getUVO().getTenantId();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 同比时间段
        Calendar tbStart = Calendar.getInstance();
        Calendar tbEnd = Calendar.getInstance();
        tbStart.setTime(format.parse(requestDto.getStartTime()));
        tbEnd.setTime(format.parse(requestDto.getEndTime()));
        tbStart.add(Calendar.YEAR, -1);
        tbEnd.add(Calendar.YEAR, -1);

        // 环比时间段
        Calendar hbStart = Calendar.getInstance();
        Calendar hbEnd = Calendar.getInstance();
        hbStart.setTime(format.parse(requestDto.getStartTime()));
        hbEnd.setTime(format.parse(requestDto.getEndTime()));
        hbStart.add(Calendar.DATE, -1);
        hbEnd.add(Calendar.DATE, -1);

        // 质量/规则数
        ModelOverviewVo jobVo = new ModelOverviewVo();
        ModelOverviewVo ruleVo = new ModelOverviewVo();
        Map<String, Integer> jobCntMap = jobCount(requestDto.getStartTime(), requestDto.getEndTime(), tenantId);
        Integer curJobCnt = jobCntMap.get("jobCnt");
        Integer curRuleCnt = jobCntMap.get("ruleCnt");

        // 质量/规则同比
        Map<String, Integer> tbJobCntMap = jobCount(format.format(tbStart.getTime()), format.format(tbEnd.getTime()), tenantId);
        Integer tbJobCnt = tbJobCntMap.get("jobCnt");
        Integer tbRuleCnt = tbJobCntMap.get("ruleCnt");

        // 质量/规则环比
        Map<String, Integer> hbJobCntMap = jobCount(format.format(hbStart.getTime()), format.format(hbEnd.getTime()), tenantId);
        Integer hbJobCnt = hbJobCntMap.get("jobCnt");
        Integer hbRuleCnt = hbJobCntMap.get("ruleCnt");

        jobVo.setCount(curJobCnt);
        ruleVo.setCount(curRuleCnt);
        // 质量任务同比计算
        increase(curJobCnt, tbJobCnt, jobVo, INCREASE_TB);
        // 质量任务环比计算
        increase(curJobCnt, hbJobCnt, jobVo, INCREASE_HB);
        // 规则同比计算
        increase(curRuleCnt, tbRuleCnt, ruleVo, INCREASE_TB);
        // 规则环比
        increase(curRuleCnt, hbRuleCnt, ruleVo, INCREASE_HB);

        // 告警数
        ModelOverviewVo warnVo = new ModelOverviewVo();
        int curWarnCnt = warnCount(requestDto.getStartTime(), requestDto.getEndTime(), tenantId);
        int tbWarnCnt = warnCount(format.format(tbStart.getTime()), format.format(tbEnd.getTime()), tenantId);
        int hbWarnCnt = warnCount(format.format(hbStart.getTime()), format.format(hbEnd.getTime()), tenantId);
        warnVo.setCount(curWarnCnt);
        // 告警数同比计算
        increase(curWarnCnt, tbWarnCnt, warnVo, INCREASE_TB);
        // 高加索环比计算
        increase(curWarnCnt, hbWarnCnt, warnVo, INCREASE_HB);


        result.put("task", jobVo);
        result.put("rule", ruleVo);
        result.put("warn", warnVo);
        return result;
    }

    @Override
    public BarYData task(ModelOverviewRequestDto requestDto) throws Exception {
        List<Map<String, Object>> models = monitorModelService.listMaps(new QueryWrapper<QuaMonitorModel>()
                .select("element_type elementType, count(1) cnt")
                .eq("flag", "1")
                .eq("tenant_id", UmsUtils.getUVO().getTenantId())
                .ge("create_time", requestDto.getStartTime())
                .le("create_time", requestDto.getEndTime())
                .groupBy("element_type"));
        List<String> yData = new ArrayList<>();
        List<Integer> xData = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(models)) {
            for (Map<String, Object> model : models) {
                resultMap.put(String.valueOf(model.get("elementType")), model.get("cnt"));
            }
            for (ElementTypeEnum value : ElementTypeEnum.values()) {
                yData.add(value.getCode());
                if (resultMap.get(value.getCode()) != null) {
                    xData.add(Integer.valueOf(String.valueOf(resultMap.get(value.getCode()))));
                } else {
                    xData.add(Integer.valueOf(0));
                }
            }
        } else {
            for (ElementTypeEnum value : ElementTypeEnum.values()) {
                yData.add(value.getCode());
                xData.add(Integer.valueOf(0));
            }
        }

        BarYData barYData = new BarYData();
        barYData.setXData(xData);
        barYData.setYData(yData);
        return barYData;
    }

    @Override
    public List<PieData> risk(ModelOverviewRequestDto requestDto) throws Exception {
        List<PieData> pies = new ArrayList<>();
        int tenantId = UmsUtils.getUVO().getTenantId();
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenantId);
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", requestDto.getStartTime());
        params.put("endDate", requestDto.getEndTime());
        List<Map<String, Object>> results = monitorModelMapper.warnRisk(params);


        if (CollectionUtils.isNotEmpty(results)) {
            Map<String, Object> resultMap = new HashMap<>();
            for (Map<String, Object> result : results) {
                resultMap.put(String.valueOf(result.get("alarmSerious")), result.get("cnt"));
            }

            for (WarnLevelEnum value : WarnLevelEnum.values()) {
                PieData pieData = new PieData();
                pieData.setName(value.getDesc());
                if (resultMap.get(value.getCode()) != null) {
                    pieData.setValue(Integer.valueOf(String.valueOf(resultMap.get(value.getCode()))));
                } else {
                    pieData.setValue(0);
                }
                pies.add(pieData);
            }
        } else {
            for (WarnLevelEnum value : WarnLevelEnum.values()) {
                PieData pieData = new PieData();
                pieData.setName(value.getDesc());
                pieData.setValue(0);

                pies.add(pieData);
            }
        }
        return pies;
    }

    @Override
    public MultiLineData trend(ModelOverviewRequestDto requestDto) throws Exception {
        int tenantId = UmsUtils.getUVO().getTenantId();
        MultiLineData multiLineData = new MultiLineData();
        List<MultiLine> line = new ArrayList<>();
        // 获取X轴数据
        List<String> xData = TimeRange.getDateRange(requestDto.getStartTime(), requestDto.getEndTime(), Calendar.DATE, "yyyy-MM-dd");
        multiLineData.setXData(xData);
        multiLineData.setLine(line);
        for (ElementTypeEnum value : ElementTypeEnum.values()) {
            MultiLine multiLine = new MultiLine();
            List<Integer> yData = new ArrayList<>();
            multiLine.setName(value.getCode());
            multiLine.setYData(yData);
            line.add(multiLine);
            Map<String, Object> params = new HashMap<>();
            params.put("elementType", value.getCode());
            params.put("startDate", requestDto.getStartTime());
            params.put("endDate", requestDto.getEndTime());
            params.put("tenantId", tenantId);
            List<Map<String, Object>> results = monitorModelMapper.jobTrend(params);
            if (CollectionUtils.isNotEmpty(results)) {
                Map<String, Object> resultMap = new HashMap<>();
                for (Map<String, Object> map : results) {
                    resultMap.put(String.valueOf(map.get("createTime")), map.get("cnt"));
                }
                for (String time : xData) {
                    if (resultMap.get(time) != null) {
                        yData.add(Integer.valueOf(String.valueOf(resultMap.get(time))));
                    } else {
                        yData.add(0);
                    }
                }
            } else {
                for (String time : xData) {
                    yData.add(0);
                }
            }
        }
        return multiLineData;
    }

    /**
     * 资产统计
     *
     * @param requestDto
     * @return
     */
    @Override
    public List<Map<String, Object>> assetStatistic(ModelOverviewRequestDto requestDto) {
        List<Map<String, Object>> result = new ArrayList<>();
        QueryWrapper queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 0);
        String startTime;
        String endTime;
        if (StringUtils.isNotBlank(requestDto.getStartTime()) && StringUtils.isNotBlank(requestDto.getEndTime())) {
            startTime = requestDto.getStartTime();
            endTime = requestDto.getEndTime();
        } else {
            // 默认近15天数据
            LocalDate date = LocalDate.now().minusDays(15);
            Date startOfDay = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
            startTime = DateUtil.format(startOfDay, "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        }
        queryWrapper.between("create_time", startTime, endTime);
        queryWrapper.orderByAsc("create_time");

        // 高价值资产
        List<QuaAssetHighValueStatistics> highValueLatestData = highValueStatisticsService.latestData();
        // 折线图数据
        List<QuaAssetHighValueStatistics> highValueList = highValueStatisticsService.list(queryWrapper);
        Map<String, Long> highValueMap = highValueList.stream().collect(Collectors.groupingBy(QuaAssetHighValueStatistics::getStatisticDate,
                Collectors.summingLong(QuaAssetHighValueStatistics::getNum)
        ));
        List<Map<String, Object>> highValueChartData = fillDates(startTime, endTime, highValueMap);
        Map<String, Object> highValueAsset = new HashMap<>();
        highValueAsset.put("name", "高价值资产");
        highValueAsset.put("type", "high_value");
        highValueAsset.put("latestData", highValueLatestData);
        highValueAsset.put("total", CollectionUtils.isEmpty(highValueLatestData) ? 0 :
                highValueLatestData.stream().mapToLong(QuaAssetHighValueStatistics::getNum).sum());
        highValueAsset.put("chartData", highValueChartData);
        result.add(highValueAsset);

        // 孤岛资产
        List<QuaAssetIsolatedIslandStatistics> islandLatestData = islandStatisticsService.latestData();
        // 折线图数据
        List<QuaAssetIsolatedIslandStatistics> islandList = islandStatisticsService.list(queryWrapper);
        Map<String, Long> islandMap = islandList.stream().collect(Collectors.groupingBy(QuaAssetIsolatedIslandStatistics::getStatisticDate,
                Collectors.summingLong(QuaAssetIsolatedIslandStatistics::getNum)
        ));
        List<Map<String, Object>> islandChartData = fillDates(startTime, endTime, islandMap);
        Map<String, Object> islandAsset = new HashMap<>();
        islandAsset.put("name", "孤岛资产");
        islandAsset.put("type", "isolated_island");
        islandAsset.put("latestData", islandLatestData);
        islandAsset.put("total", CollectionUtils.isEmpty(islandLatestData) ? 0 :
                islandLatestData.stream().mapToLong(QuaAssetIsolatedIslandStatistics::getNum).sum());
        islandAsset.put("chartData", islandChartData);
        result.add(islandAsset);

        // 低维护资产
        // 最新数据
        List<QuaAssetLowMaintenanceStatistics> latestData = lowMaintenanceStatisticsService.latestData();
        // 折线图数据
        List<QuaAssetLowMaintenanceStatistics> maintenanceList = lowMaintenanceStatisticsService.list(queryWrapper);
        Map<String, Long> statisticMap1 = maintenanceList.stream().collect(Collectors.groupingBy(QuaAssetLowMaintenanceStatistics::getStatisticDate,
                Collectors.summingLong(QuaAssetLowMaintenanceStatistics::getNum)
        ));
        List<Map<String, Object>> maintenanceChartData = fillDates(startTime, endTime, statisticMap1);
        Map<String, Object> lowMaintenanceAsset = new HashMap<>();
        lowMaintenanceAsset.put("name", "低维护资产");
        lowMaintenanceAsset.put("type", "low_maintenance");
        lowMaintenanceAsset.put("latestData", latestData);
        lowMaintenanceAsset.put("total", CollectionUtils.isEmpty(latestData) ? 0 :
                latestData.stream().mapToLong(QuaAssetLowMaintenanceStatistics::getNum).sum());
        lowMaintenanceAsset.put("chartData", maintenanceChartData);
        result.add(lowMaintenanceAsset);

        // 低质量资产
        // 最新数据
        List<QuaAssetLowQualityStatistics> qualityLatestData = lowQualityStatisticsService.latestData();
        // 折线图数据
        List<QuaAssetLowQualityStatistics> qualityList = lowQualityStatisticsService.list(queryWrapper);
        Map<String, Long> statisticMap2 = qualityList.stream().collect(Collectors.groupingBy(QuaAssetLowQualityStatistics::getStatisticDate,
                Collectors.summingLong(QuaAssetLowQualityStatistics::getNum)
        ));
        List<Map<String, Object>> qualityChartData = fillDates(startTime, endTime, statisticMap2);
        Map<String, Object> lowQualityAsset = new HashMap<>();
        lowQualityAsset.put("name", "低质量资产");
        lowQualityAsset.put("type", "low_quality");
        lowQualityAsset.put("latestData", qualityLatestData);
        lowQualityAsset.put("total", CollectionUtils.isEmpty(qualityLatestData) ? 0 :
                qualityLatestData.stream().mapToLong(QuaAssetLowQualityStatistics::getNum).sum());
        lowQualityAsset.put("chartData", qualityChartData);
        result.add(lowQualityAsset);

        return result;
    }

    public List<Map<String, Object>> fillDates(String startTime, String endTime, Map<String, Long> statisticMap) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startTime.substring(0, 10), formatter);
        LocalDate endDate = LocalDate.parse(endTime.substring(0, 10), formatter);
        // 生成日期范围
        List<String> allDates = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            allDates.add(date.format(formatter));
        }
        // 补充缺失的日期，返回新的统计数据列表
        List<Map<String, Object>> result = new ArrayList<>();
        for (String date : allDates) {
            Map<String, Object> statMap = new HashMap<>();
            statMap.put("date", date);
            statMap.put("num", statisticMap.getOrDefault(date, 0L));
            result.add(statMap);
        }
        return result;
    }

    public static void main(String[] args) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse("", formatter);
    }

    /**
     * 质量/规则同环比数据计算
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param tenantId  租户
     * @return
     */
    private Map<String, Integer> jobCount(String startTime, String endTime, int tenantId) {
        Integer ruleCount = 0;
        Integer jobCount = 0;
        List<QuaMonitorJob> jobs = monitorJobService.list(new QueryWrapper<QuaMonitorJob>()
                .eq("tenant_id", tenantId)
                .eq("flag", "1")
                .ge("create_time", startTime)
                .le("create_time", endTime));
        if (CollectionUtils.isNotEmpty(jobs)) {
            for (QuaMonitorJob job : jobs) {
                String rules = job.getJobRules();
                if (StringUtils.isNotEmpty(rules)) {
                    ruleCount = ruleCount + rules.split(",").length;
                }
            }
            jobCount = jobs.size();
        }

        Map<String, Integer> result = new HashMap<>();
        result.put("jobCnt", jobCount);
        result.put("ruleCnt", ruleCount);
        return result;
    }

    /**
     * 同环比增长率计算
     *
     * @param curCnt     当期数据
     * @param preCnt     上期数据
     * @param overviewVo
     * @param type       类型
     */
    private void increase(Integer curCnt, Integer preCnt, ModelOverviewVo overviewVo, String type) {
        if (preCnt == 0) {
            if (INCREASE_TB.equals(type)) {
                overviewVo.setTb("-");
                overviewVo.setTbType("");
            } else {
                overviewVo.setHb("-");
                overviewVo.setHbType("");
            }
        } else {
            Integer compareCt = curCnt - preCnt;
            if (compareCt > 0) {
                if (INCREASE_TB.equals(type)) {
                    overviewVo.setTbType("1");
                } else {
                    overviewVo.setHbType("1");
                }
            } else {
                if (INCREASE_TB.equals(type)) {
                    overviewVo.setTbType("0");
                } else {
                    overviewVo.setHbType("0");
                }
            }
            DecimalFormat decimalFormat = new DecimalFormat("0");
            if (INCREASE_TB.equals(type)) {
                overviewVo.setTb(decimalFormat.format((float) compareCt / preCnt * 100) + "%");
            } else {
                overviewVo.setHb(decimalFormat.format((float) compareCt / preCnt * 100) + "%");
            }
        }
    }

    /**
     * 质量告警同环比数据计算
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param tenantId  租户
     * @return
     */
    private Integer warnCount(String startTime, String endTime, int tenantId) {
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenantId);
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", startTime);
        params.put("endDate", endTime);
        Integer warnCount = monitorModelMapper.warnCount(params);
        return warnCount;
    }
}
