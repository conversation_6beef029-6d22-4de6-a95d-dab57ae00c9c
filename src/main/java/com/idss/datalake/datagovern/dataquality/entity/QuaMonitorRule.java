package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 质量监测规则
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuaMonitorRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模型ID
     */
    private Long modelId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则代码
     */
    private String ruleCode;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 规则类型ID
     */
    private Long ruleTypeId;

    /**
     * 表名/索引名
     */
    private String tableName;

    /**
     * 字段名 多个字段使用逗号分隔
     */
    private String columnName;

    /**
     * 规则明细 {config_key:config_value}
     */
    private String ruleDetail;

    /**
     * 问题级别
     */
    private String ruleLevel;

    /**
     * 规则名称
     */
    private String ruleDesc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新时间
     */
    private String updateUser;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 有效标识:1-有效；0-无效
     */
    private String flag;

    private String templateId;

    /**
     * 阈值
     */
    private String thresholdValue;

    private Integer ruleWeight;

    private String thresholdOperator;
}
