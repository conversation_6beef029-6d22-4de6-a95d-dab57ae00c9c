package com.idss.datalake.datagovern.dataquality.service;

import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleTemplate;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorWeight;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.dataquality.model.RuleRequestDto;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import java.util.List;

/**
 * <p>
 * 数据质量-权重管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
public interface IQuaMonitorWeightService extends IService<QuaMonitorWeight> {

    /**
     * 分页查询权重信息
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<QuaMonitorWeight>> queryPage(RuleRequestDto requestDto);
}
