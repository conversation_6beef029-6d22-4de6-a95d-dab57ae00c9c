package com.idss.datalake.datagovern.dataquality.service;

import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleTemplate;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.dataquality.model.*;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import java.util.List;

/**
 * <p>
 * 数据质量-模型管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
public interface IQuaMonitorRuleTemplateService extends IService<QuaMonitorRuleTemplate> {

    /**
     * 分页查询规则信息
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<QuaMonitorRuleTemplate>> queryRulePage(RuleRequestDto requestDto);

    /**
     * 新增或编辑规则
     */
    void addOrUpdate(QuaMonitorRuleTemplate ruleTemplate) throws Exception;

    /**
     * 取消新增操作
     * @param ruleTemplate
     * @throws Exception
     */
    void addCancel(QuaMonitorRuleTemplate ruleTemplate) throws Exception;

    /**
     * 查看规则详情
     * @param id
     * @return
     */
    QuaMonitorRuleTemplate queryRuleDetail(Long id);

    /**
     * 删除规则
     * @param requestDto
     */
    void delete(RuleRequestDto requestDto) throws Exception;

    /**
     * 分页查询报告
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<TaskReport>> report(ReportRequestDto requestDto) throws Exception;

    /**
     * 分页查询报告
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<ReportColumnInfo>> reportTable(ReportRequestDto requestDto) throws Exception;
}
