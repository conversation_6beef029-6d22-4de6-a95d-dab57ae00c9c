/************************ CHAN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/16 16:21
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.model;

import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleType;
import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Copyright 2022 IDSS
 * <p> 监测规则查询条件
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/16 16:21
 */
@Data
public class RuleRequestDto extends BasePageRequest {

    @ApiModelProperty("规则名称")
    private String ruleName;

    @ApiModelProperty("规则编码")
    private String ruleCode;

    @ApiModelProperty("规则类型")
    private String ruleType;

    @ApiModelProperty("模型Id")
    private Long modelId;

    @ApiModelProperty("表")
    private String tableName;

    @ApiModelProperty("模型名称")
    private String templateName;

    @ApiModelProperty("字段")
    private List<String> columns;

    @ApiModelProperty("问题级别")
    private String ruleLevel;

    @ApiModelProperty("个性化配置")
    private RuleExtConfig ruleDetail;

    @ApiModelProperty("规则Id")
    private Long id;

    private List<Long> ids;

    /**
     * 规则类型编码
     */
    private String typeCode;

    private String typeName;

    private List<String> ruleTypeList;

    /**
     * 字段名称
     */
    private String columnName;

    /**
     * 模板名称
     */
    private String modelName;

    /**
     * 规则模板ID
     */
    private String templateId;

    /**
     * 字段类型
     */
    private String columnType;

    /**
     * 维度名称
     */
    private String dimName;

    /**
     * 规则ID
     */
    private Long ruleTypeId;
}
