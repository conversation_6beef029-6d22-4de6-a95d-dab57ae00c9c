package com.idss.datalake.datagovern.dataquality.service.impl;

import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorResult;
import com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorResultMapper;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorResultService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 质量监测结果表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Service
public class QuaMonitorResultServiceImpl extends ServiceImpl<QuaMonitorResultMapper, QuaMonitorResult> implements IQuaMonitorResultService {

}
