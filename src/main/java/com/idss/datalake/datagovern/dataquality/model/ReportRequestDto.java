/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/16 16:21
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.model;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Copyright 2022 IDSS
 * <p> 监测规则查询条件
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/16 16:21
 */
@Data
public class ReportRequestDto extends BasePageRequest {

    private Long elementId;

    private Long dbId;

    private String snapshootVersion;

    private String dbName;

    private String type;

    private Long tableId;

    private String tableName;

    private String templateName;

    private List<String> tableNames;

    private List<String> columnNames;

    private List<String> templateIds;

    private String columnName;

    private String ruleType;

    private String ruleTypeName;

    private String templateId;

    private String fields;
}
