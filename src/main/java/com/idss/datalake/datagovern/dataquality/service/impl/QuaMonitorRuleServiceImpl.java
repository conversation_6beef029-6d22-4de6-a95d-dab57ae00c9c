package com.idss.datalake.datagovern.dataquality.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.ExcelUtil;
import com.idss.datalake.common.util.HttpUtils;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.dataquality.entity.*;
import com.idss.datalake.datagovern.dataquality.enums.*;
import com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorRuleMapper;
import com.idss.datalake.datagovern.dataquality.model.*;
import com.idss.datalake.datagovern.dataquality.service.*;
import com.idss.datalake.datagovern.datastandard.entity.DataStandardConfig;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardDic;
import com.idss.datalake.datagovern.datastandard.enums.DataStandardTypeEnum;
import com.idss.datalake.datagovern.datastandard.enums.PublishStatusEnum;
import com.idss.datalake.datagovern.datastandard.service.IDataStandardConfigService;
import com.idss.datalake.datagovern.datastandard.service.IQuaDataStandardDicService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.job.entity.ChTaskResultColumn;
import com.idss.datalake.datagovern.metadata.model.job.entity.EsTaskResultField;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaWebHiveTaskResultColumn;
import com.idss.datalake.datagovern.metadata.model.job.entity.QuaWebMysqlTaskResultColumn;
import com.idss.datalake.datagovern.metadata.model.job.service.ChTaskResultColumnService;
import com.idss.datalake.datagovern.metadata.model.job.service.EsTaskResultFieldService;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebHiveTaskResultColumnService;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebMysqlTaskResultColumnService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 质量监测规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Service
public class QuaMonitorRuleServiceImpl extends ServiceImpl<QuaMonitorRuleMapper, QuaMonitorRule> implements IQuaMonitorRuleService {

    private static final Logger LOGGER = LoggerFactory.getLogger(QuaMonitorRuleServiceImpl.class);

    @Autowired
    private QuaMonitorRuleMapper monitorRuleMapper;

    @Autowired
    private IQuaMonitorRuleTemplateService monitorRuleTemplateService;

    @Autowired
    private IQuaMonitorRuleService monitorRuleService;

    @Autowired
    private IQuaInternalModelService internalModelService;

    @Autowired
    private IQuaDataStandardDicService quaDataStandardDicService;

    @Autowired
    private IDataStandardConfigService dataStandardConfigService;

    @Autowired
    private IQuaDyncThresholdService dyncThresholdService;

    @Autowired
    private IQuaMonitorRuleTypeService monitorRuleTypeService;

    @Autowired
    private IQuaMonitorModelResourceService modelResourceService;

    @Autowired
    private IQuaMonitorModelService modelService;

    @Autowired
    private ChTaskResultColumnService chTaskResultColumnService;

    @Autowired
    private EsTaskResultFieldService esTaskResultFieldService;

    @Autowired
    private IQuaWebMysqlTaskResultColumnService mysqlTaskResultColumnService;

    @Autowired
    private IQuaWebHiveTaskResultColumnService hiveTaskResultColumnService;

    @Autowired
    private IQuaMonitorRuleExecRecordService ruleExecRecordService;

    @Value("${file.uploadPath}")
    private String filePath;

    @Value("${data.collect.url}")
    private String collectUrl;

    private static final String TEST_EXEC = "/monitor/execRule/";

    private static final String SELFDEFINE_RULE_CALC = "①将“字典变量”、“数据变量1”、“数据变量2”带入自定义SQL，执行输出一个结果。<br/>" +
            "②将该“结果”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>" +
            "③基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>" +
            "④基于“模板打分占比”计算模板得分：模板得分=模板打分占比*100，得分取整数。<br/>";

    @Override
    public BasePageResponse<List<RuleResponseVo>> queryColumnPage(RuleRequestDto requestDto) {
        List<RuleResponseVo> list = new ArrayList<>();
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<RuleResponseVo> page = monitorRuleMapper.queryColumnPage(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
    }

    @Override
    public void saveOrEditColumn(QualityRule qualityRule) throws Exception {
        UserValueObject uvo = UmsUtils.getUVO();
        LocalDateTime now = LocalDateTime.now();
        if(qualityRule.getId() == null) {
            QuaMonitorRule rule = new QuaMonitorRule();
            rule.setCreateUser(uvo.getUserName());
            rule.setCreateTime(now);
            wrapRule(qualityRule, rule);
            this.save(rule);
        } else {
            QuaMonitorRule rule = this.getById(qualityRule.getId());
            wrapRule(qualityRule, rule);
            this.updateById(rule);
        }
    }

    @Override
    public QualityRule columnDetail(Long id) throws Exception {
        QuaMonitorRule monitorRule = this.getById(id);
        QualityRule qualityRule = new QualityRule();
        String ruleType = monitorRule.getRuleType();
        String ruleDetail = monitorRule.getRuleDetail();
        JSONObject jsonDetail = JSONObject.parseObject(ruleDetail);
        qualityRule.setRuleTypeId(monitorRule.getRuleTypeId());
        qualityRule.setRuleType(ruleType);
        qualityRule.setThreshold(monitorRule.getThresholdValue());
        qualityRule.setRuleLevel(monitorRule.getRuleLevel());
        qualityRule.setRuleDesc(monitorRule.getRuleDesc());
        qualityRule.setId(monitorRule.getId());
        qualityRule.setModelId(monitorRule.getModelId());
        qualityRule.setTemplateId(monitorRule.getTemplateId());
        qualityRule.setRuleWeight(monitorRule.getRuleWeight());
        qualityRule.setThresholdOperator(monitorRule.getThresholdOperator());
        if(RuleTypeEnum.timely.getCode().equals(ruleType)) {
            // 及时性检查
            qualityRule.setTimely(JSON.toJavaObject(jsonDetail, TemplateTimely.class));
        } else if(RuleTypeEnum.repeatRate.getCode().equals(ruleType)) {
            // 重复率检查
            qualityRule.setRepeatRate(JSON.toJavaObject(jsonDetail, TemplateRepeatRate.class));
        } else if(RuleTypeEnum.repeatLine.getCode().equals(ruleType)) {
            // 重复行检查
            qualityRule.setRepeatLine(JSON.toJavaObject(jsonDetail, TemplateRepeatLine.class));
        } else if(RuleTypeEnum.nullRate.getCode().equals(ruleType)) {
            // 空值率检查
            qualityRule.setNullRate(JSON.toJavaObject(jsonDetail, TemplateNullRate.class));
        } else if(RuleTypeEnum.nullLine.getCode().equals(ruleType)) {
            // 空值行检查
            qualityRule.setNullLine(JSONObject.toJavaObject(jsonDetail, TemplateNullLine.class));
        } else if(RuleTypeEnum.logicLine.getCode().equals(ruleType)) {
            // 逻辑检查
            qualityRule.setLogicLine(JSONObject.toJavaObject(jsonDetail, TemplateLogicLine.class));
        } else if(RuleTypeEnum.logicRate.getCode().equals(ruleType)) {
            // 逻辑检查
            qualityRule.setLogicRate(JSONObject.toJavaObject(jsonDetail, TemplateLogicRate.class));
        } else if(RuleTypeEnum.lengthRate.getCode().equals(ruleType)) {
            // 长度规范率
            qualityRule.setLengthRate(JSONObject.toJavaObject(jsonDetail, TemplateLengthRate.class));
        } else if(RuleTypeEnum.lengthLine.getCode().equals(ruleType)) {
            // 长度规范行
            qualityRule.setLengthLine(JSONObject.toJavaObject(jsonDetail, TemplateLengthLine.class));
        } else if(RuleTypeEnum.formatRate.getCode().equals(ruleType)) {
            // 格式规范率
            qualityRule.setFormatRate(JSONObject.toJavaObject(jsonDetail, TemplateFormatRate.class));
        } else if(RuleTypeEnum.formatLine.getCode().equals(ruleType)) {
            // 数据规范行
            qualityRule.setFormatLine(JSONObject.toJavaObject(jsonDetail, TemplateFormatLine.class));
        } else if(RuleTypeEnum.rangeLine.getCode().equals(ruleType)) {
            // 值域通过数检查
            qualityRule.setRangeLine(JSONObject.toJavaObject(jsonDetail, TemplateRangeLine.class));
        } else if(RuleTypeEnum.rangeRate.getCode().equals(ruleType)) {
            // 值域通过率检查
            qualityRule.setRangeRate(JSONObject.toJavaObject(jsonDetail, TemplateRangeRate.class));
        } else if(RuleTypeEnum.tableSize.getCode().equals(ruleType)) {
            // 表大小波动
            qualityRule.setTableSize(JSONObject.toJavaObject(jsonDetail, TemplateTableSize.class));
        } else if(RuleTypeEnum.tableLine.getCode().equals(ruleType)) {
            // 表行数波动
            qualityRule.setTableLine(JSONObject.toJavaObject(jsonDetail, TemplateTableLine.class));
        } else if(RuleTypeEnum.selfDefine.getCode().equals(ruleType)) {
            // 自定义模型
            qualityRule.setSelfDefine(JSONObject.toJavaObject(jsonDetail, TemplateSelfDefine.class));
        }
        return qualityRule;
    }

    @Override
    public void delete(RuleRequestDto requestDto) throws Exception {
        this.remove(new LambdaQueryWrapper<QuaMonitorRule>()
                .in(QuaMonitorRule::getId, requestDto.getIds()));
    }

    @Override
    public List<GroupSelectVo> queryAllRuleType() {
        // 查询内置模型
        List<QuaInternalModel> buildInModels = internalModelService.list(new LambdaUpdateWrapper<QuaInternalModel>()
                .eq(QuaInternalModel::getModelType, "1"));
        // 查询自定义模型
        List<QuaInternalModel> selfDefineModels = internalModelService.list(new LambdaUpdateWrapper<QuaInternalModel>()
                .eq(QuaInternalModel::getModelType, "2"));

        GroupSelectVo buildInSelect = new GroupSelectVo();
        List<GroupSelectOption> buildInOptions = new ArrayList<>();
        buildInSelect.setLabel("内置模板");
        buildInSelect.setOptions(buildInOptions);
        for (QuaInternalModel buildInModel : buildInModels) {
            GroupSelectOption selectOption = new GroupSelectOption();
            selectOption.setValue(buildInModel.getId());
            selectOption.setLabel(buildInModel.getName());
            selectOption.setRuleType(buildInModel.getModelCode());
            selectOption.setRuleCalc(buildInModel.getModelCalc());
            buildInOptions.add(selectOption);
        }

        GroupSelectVo selfDefineSelect = new GroupSelectVo();
        List<GroupSelectOption> selfDefineOptions = new ArrayList<>();
        selfDefineSelect.setLabel("自定义模板");
        selfDefineSelect.setOptions(selfDefineOptions);
        for (QuaInternalModel selfDefineModel : selfDefineModels) {
            GroupSelectOption selectOption = new GroupSelectOption();
            selectOption.setValue(selfDefineModel.getId());
            selectOption.setLabel(selfDefineModel.getName());
            selectOption.setRuleType(RuleTypeEnum.selfDefine.getCode());
            selectOption.setRuleCalc(SELFDEFINE_RULE_CALC);
            selfDefineOptions.add(selectOption);
        }
        List<GroupSelectVo> result = new ArrayList<>();
        result.add(buildInSelect);
        result.add(selfDefineSelect);

        return result;
    }

    @Override
    public SelfDefineRule querySelfDefine(RuleRequestDto requestDto) throws Exception {
        QuaInternalModel internalModel = internalModelService.getById(requestDto.getRuleTypeId());
        TemplateSelfDefine selfDefine = null;
        if(requestDto.getId() != null) {
            QuaMonitorRule monitorRule = monitorRuleService.getById(requestDto.getId());
            JSONObject jsonDetail = JSON.parseObject(monitorRule.getRuleDetail());
            selfDefine = JSONObject.toJavaObject(jsonDetail, TemplateSelfDefine.class);
        }
        SelfDefineRule selfDefineRule = new SelfDefineRule();
        List<SelfDefineVariable> variables = new ArrayList<>();
        String sql = internalModel.getCustomSql();
        selfDefineRule.setSql(sql);
        selfDefineRule.setVariable(variables);
        if(sql.contains("$column")) {
            SelfDefineVariable variable = new SelfDefineVariable();
            variable.setLabel("字段变量");
            variable.setCode("column");
            variable.setType("select");
            if(requestDto.getId() != null) {
                variable.setValue(selfDefine.getColumn());
            }
            variables.add(variable);
        }
        if(sql.contains("$param1")) {
            SelfDefineVariable variable = new SelfDefineVariable();
            variable.setLabel("数据变量1");
            variable.setCode("param1");
            variable.setType("input");
            if(requestDto.getId() != null) {
                variable.setValue(selfDefine.getParam1());
            }
            variables.add(variable);
        }
        if(sql.contains("$param2")) {
            SelfDefineVariable variable = new SelfDefineVariable();
            variable.setLabel("数据变量2");
            variable.setCode("param2");
            variable.setType("input");
            if(requestDto.getId() != null) {
                variable.setValue(selfDefine.getParam2());
            }
            variables.add(variable);
        }

        return selfDefineRule;
    }

    @Override
    public List<SelectVo> queryAllDictionary() throws Exception {
        List<QuaDataStandardDic> standardDics = quaDataStandardDicService.list(new LambdaQueryWrapper<QuaDataStandardDic>()
                .eq(QuaDataStandardDic::getTenantId, UserUtil.getCurrentTenantId()));

        List<SelectVo> vos = new ArrayList<>();
        for (QuaDataStandardDic standardDic : standardDics) {
            SelectVo vo = new SelectVo();
            vo.setCode(String.valueOf(standardDic.getId()));
            vo.setValue(standardDic.getDicName());

            vos.add(vo);
        }
        return vos;
    }

    @Override
    public List<GroupSelectVo> queryAllDataStandard(RuleRequestDto ruleRequestDto) {
        UserValueObject uvo = UmsUtils.getUVO();
        String ruleType = ruleRequestDto.getRuleType();
        LambdaQueryWrapper<DataStandardConfig> metaQuery = new LambdaQueryWrapper<>();
        metaQuery.eq(DataStandardConfig::getType, "1")
                .eq(DataStandardConfig::getStatus, "1")
                .eq(DataStandardConfig::getPublishStatus, 1)
                .eq(DataStandardConfig::getTenantId, uvo.getTenantId());

        LambdaQueryWrapper<DataStandardConfig> basicQuery = new LambdaQueryWrapper<>();
        basicQuery.eq(DataStandardConfig::getType, "0")
                .eq(DataStandardConfig::getStatus, "1")
                .eq(DataStandardConfig::getPublishStatus, 1)
                .eq(DataStandardConfig::getTenantId, uvo.getTenantId());
        if(RuleTypeEnum.lengthLine.getCode().equals(ruleType) || RuleTypeEnum.lengthRate.getCode().equals(ruleType)) {
            metaQuery.eq(DataStandardConfig::getStandardType, 1);
            basicQuery.eq(DataStandardConfig::getStandardType, 1);
        }
        // 查询元数据标准
        List<DataStandardConfig> metaConfigs = dataStandardConfigService.list(metaQuery);
        //查询基础数据标准
        List<DataStandardConfig> basicConfigs = dataStandardConfigService.list(basicQuery);

        GroupSelectVo basicSelect = new GroupSelectVo();
        List<GroupSelectOption> basicOptions = new ArrayList<>();
        basicSelect.setLabel("基础数据标准");
        basicSelect.setOptions(basicOptions);
        for (DataStandardConfig basicConfig : basicConfigs) {
            GroupSelectOption option = new GroupSelectOption();
            option.setValue(String.valueOf(basicConfig.getId()));
            option.setLabel(basicConfig.getCnName());
            basicOptions.add(option);
        }

        GroupSelectVo metaSelect = new GroupSelectVo();
        List<GroupSelectOption> metaOptions = new ArrayList<>();
        metaSelect.setLabel("数据元标准");
        metaSelect.setOptions(metaOptions);
        for (DataStandardConfig metaConfig : metaConfigs) {
            GroupSelectOption option = new GroupSelectOption();
            option.setValue(String.valueOf(metaConfig.getId()));
            option.setLabel(metaConfig.getCnName());
            metaOptions.add(option);
        }

        List<GroupSelectVo> vos = new ArrayList<>();
        vos.add(basicSelect);
        vos.add(metaSelect);
        return vos;
    }

    @Override
    public List<SelectVo> queryAllThreshold(RuleRequestDto ruleRequestDto) {
        QuaMonitorModel monitorModel = modelService.getById(ruleRequestDto.getModelId());
        String target = "";
        if(RuleTypeEnum.tableSize.getCode().equals(ruleRequestDto.getRuleType())) {
            target = "space";
        } else if(RuleTypeEnum.tableLine.getCode().equals(ruleRequestDto.getRuleType())) {
            target = "line";
        }
        List<QuaDyncThreshold> thresholds = dyncThresholdService.list(new LambdaQueryWrapper<QuaDyncThreshold>()
                .eq(QuaDyncThreshold::getElementId, monitorModel.getElementId())
                .eq(QuaDyncThreshold::getDbName, monitorModel.getDatabaseName())
                .eq(QuaDyncThreshold::getTableName, ruleRequestDto.getTableName())
                .eq(QuaDyncThreshold::getForecastTarget, target));

        List<SelectVo> vos = new ArrayList<>();
        for (QuaDyncThreshold threshold : thresholds) {
            SelectVo vo = new SelectVo();
            vo.setCode(String.valueOf(threshold.getId()));
            vo.setValue(threshold.getJobName());

            vos.add(vo);
        }
        return vos;
    }

    @Override
    public List<QuaMonitorRuleType> queryRuleTypeExt(String typeCode) {
        List<QuaMonitorRuleType> ruleTypes = monitorRuleTypeService.list(new QueryWrapper<QuaMonitorRuleType>()
                .eq("type_code", typeCode));
        return ruleTypes;
    }

    @Override
    public List<SelectVo> queryTables(RuleRequestDto requestDto) {
        List<QuaMonitorModelResource> monitorModelResources = modelResourceService.list(new QueryWrapper<QuaMonitorModelResource>()
                .eq("model_id", requestDto.getModelId()));
        // 如果表已经配置了模型，则无需重复配置(一个表仅能配置一个模型)
        List<QuaMonitorRuleTemplate> ruleTemplates = monitorRuleTemplateService.list(new LambdaUpdateWrapper<QuaMonitorRuleTemplate>()
                .eq(QuaMonitorRuleTemplate::getDeleteFlag, "0")
                .eq(QuaMonitorRuleTemplate::getModelId, requestDto.getModelId()));
        List<String> existTables = ruleTemplates.stream().map(data -> data.getTableName()).collect(Collectors.toList());
        List<SelectVo> vos = new ArrayList<>();
        for (QuaMonitorModelResource monitorModelResource : monitorModelResources) {
            if(!existTables.contains(monitorModelResource.getTableName())) {
                SelectVo vo = new SelectVo();
                vo.setCode(monitorModelResource.getTableName());
                vo.setValue(monitorModelResource.getTableName());

                vos.add(vo);
            }
        }
        return vos;
    }

    @Override
    public List<SelectVo> queryColumns(RuleRequestDto requestDto) {
        QuaMonitorModel model = modelService.getById(requestDto.getModelId());
        List<SelectVo> vos = new ArrayList<>();
        if (ElementTypeEnum.CH.getCode().equals(model.getElementType())) {
            QueryWrapper<ChTaskResultColumn> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("element_id", model.getElementId())
                    .eq("db_name", model.getDatabaseName())
                    .eq("table_name", requestDto.getTableName())
                    .eq("snapshoot_version", model.getSnapshootVersion());
            if("date".equals(requestDto.getColumnType())) {
                queryWrapper.like("type", "DateTime");
            }
            List<ChTaskResultColumn> columns = chTaskResultColumnService.list(queryWrapper);
            for (ChTaskResultColumn column : columns) {
                SelectVo vo = new SelectVo();
                vo.setCode(column.getColumnName());
                vo.setValue(column.getColumnName());

                vos.add(vo);
            }
        } else if (ElementTypeEnum.ES.getCode().equals(model.getElementType())) {
            QueryWrapper<EsTaskResultField> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("element_id", model.getElementId())
                    .eq("index_name", requestDto.getTableName())
                    .eq("snapshoot_version", model.getSnapshootVersion());
            if("date".equals(requestDto.getColumnType())) {
                queryWrapper.like("field_data_type", "date");
            }
            List<EsTaskResultField> fields = esTaskResultFieldService.list(queryWrapper);
            for (EsTaskResultField field : fields) {
                SelectVo vo = new SelectVo();
                vo.setCode(field.getFieldName());
                vo.setValue(field.getFieldName());

                vos.add(vo);
            }
        } else if (ElementTypeEnum.MYSQL.getCode().equals(model.getElementType())) {
            QueryWrapper<QuaWebMysqlTaskResultColumn> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("element_id", model.getElementId())
                    .eq("db_name", model.getDatabaseName())
                    .eq("table_name", requestDto.getTableName())
                    .eq("snapshoot_version", model.getSnapshootVersion());
            if("date".equals(requestDto.getColumnType())) {
                queryWrapper.like("type", "datetime");
            }
            List<QuaWebMysqlTaskResultColumn> columns = mysqlTaskResultColumnService.list(queryWrapper);
            for (QuaWebMysqlTaskResultColumn column : columns) {
                SelectVo vo = new SelectVo();
                vo.setCode(column.getColumnName());
                vo.setValue(column.getColumnName());

                vos.add(vo);
            }
        } else if (ElementTypeEnum.HIVE.getCode().equals(model.getElementType())) {
            QueryWrapper<QuaWebHiveTaskResultColumn> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("element_id", model.getElementId())
                    .eq("db_name", model.getDatabaseName())
                    .eq("table_name", requestDto.getTableName())
                    .eq("snapshoot_version", model.getSnapshootVersion());
            if("date".equals(requestDto.getColumnType())) {
                queryWrapper.like("type", "datetime");
            }
            List<QuaWebHiveTaskResultColumn> columns = hiveTaskResultColumnService.list(queryWrapper);
            for (QuaWebHiveTaskResultColumn column : columns) {
                SelectVo vo = new SelectVo();
                vo.setCode(column.getColumnName());
                vo.setValue(column.getColumnName());

                vos.add(vo);
            }
        }
        return vos;
    }

    @Override
    public List<SelectVo> queryDateColumns(RuleRequestDto requestDto) {
        QuaMonitorModel model = modelService.getById(requestDto.getModelId());
        List<SelectVo> vos = new ArrayList<>();
        if (ElementTypeEnum.CH.getCode().equals(model.getElementType())) {
            List<ChTaskResultColumn> columns = chTaskResultColumnService.list(new QueryWrapper<ChTaskResultColumn>()
                    .eq("element_id", model.getElementId())
                    .eq("db_name", model.getDatabaseName())
                    .eq("table_name", requestDto.getTableName())
                    .eq("snapshoot_version", model.getSnapshootVersion())
                    .like("type", "Time"));
            for (ChTaskResultColumn column : columns) {
                SelectVo vo = new SelectVo();
                vo.setCode(column.getColumnName());
                vo.setValue(column.getColumnName());

                vos.add(vo);
            }
        } else if (ElementTypeEnum.ES.getCode().equals(model.getElementType())) {
            List<EsTaskResultField> fields = esTaskResultFieldService.list(new QueryWrapper<EsTaskResultField>()
                    .eq("element_id", model.getElementId())
                    .eq("index_name", requestDto.getTableName())
                    .eq("snapshoot_version", model.getSnapshootVersion())
                    .eq("field_data_type", "\"date\""));
            for (EsTaskResultField field : fields) {
                SelectVo vo = new SelectVo();
                vo.setCode(field.getFieldName());
                vo.setValue(field.getFieldName());

                vos.add(vo);
            }
        } else if (ElementTypeEnum.MYSQL.getCode().equals(model.getElementType())) {
            List<QuaWebMysqlTaskResultColumn> columns = mysqlTaskResultColumnService.list(new QueryWrapper<QuaWebMysqlTaskResultColumn>()
                    .eq("element_id", model.getElementId())
                    .eq("db_name", model.getDatabaseName())
                    .eq("table_name", requestDto.getTableName())
                    .eq("snapshoot_version", model.getSnapshootVersion())
                    .like("type", "%time%"));
            for (QuaWebMysqlTaskResultColumn column : columns) {
                SelectVo vo = new SelectVo();
                vo.setCode(column.getColumnName());
                vo.setValue(column.getColumnName());

                vos.add(vo);
            }
        } else if (ElementTypeEnum.HIVE.getCode().equals(model.getElementType())) {
            List<QuaWebHiveTaskResultColumn> columns = hiveTaskResultColumnService.list(new QueryWrapper<QuaWebHiveTaskResultColumn>()
                    .eq("element_id", model.getElementId())
                    .eq("db_name", model.getDatabaseName())
                    .eq("table_name", requestDto.getTableName())
                    .eq("snapshoot_version", model.getSnapshootVersion())
                    .eq("type", "TIMESTAMP"));
            for (QuaWebHiveTaskResultColumn column : columns) {
                SelectVo vo = new SelectVo();
                vo.setCode(column.getColumnName());
                vo.setValue(column.getColumnName());

                vos.add(vo);
            }
        }
        return vos;
    }

    @Override
    public List<SelectVo> queryNumericColumns(RuleRequestDto requestDto) {
        QuaMonitorModel model = modelService.getById(requestDto.getModelId());
        List<SelectVo> vos = new ArrayList<>();
        if (ElementTypeEnum.CH.getCode().equals(model.getElementType())) {
            List<ChTaskResultColumn> columns = chTaskResultColumnService.list(new QueryWrapper<ChTaskResultColumn>()
                    .eq("element_id", model.getElementId())
                    .eq("db_name", model.getDatabaseName())
                    .eq("table_name", requestDto.getTableName())
                    .eq("snapshoot_version", model.getSnapshootVersion())
                    .and(wrapper -> wrapper.like("type", "Int").or().like("type", "Float").or().like("type", "Decimal")));
            for (ChTaskResultColumn column : columns) {
                SelectVo vo = new SelectVo();
                vo.setCode(column.getColumnName());
                vo.setValue(column.getColumnName());

                vos.add(vo);
            }
        } else if (ElementTypeEnum.ES.getCode().equals(model.getElementType())) {
            List<EsTaskResultField> fields = esTaskResultFieldService.list(new QueryWrapper<EsTaskResultField>()
                    .eq("element_id", model.getElementId())
                    .eq("index_name", requestDto.getTableName())
                    .eq("snapshoot_version", model.getSnapshootVersion())
                    .and(wrapper -> wrapper.in("field_data_type", "long", "integer", "short", "byte", "double").or().like(
                            "field_data_type", "float")));
            for (EsTaskResultField field : fields) {
                SelectVo vo = new SelectVo();
                vo.setCode(field.getFieldName());
                vo.setValue(field.getFieldName());

                vos.add(vo);
            }
        } else if (ElementTypeEnum.MYSQL.getCode().equals(model.getElementType())) {
            List<QuaWebMysqlTaskResultColumn> columns = mysqlTaskResultColumnService.list(new QueryWrapper<QuaWebMysqlTaskResultColumn>()
                    .eq("element_id", model.getElementId())
                    .eq("db_name", model.getDatabaseName())
                    .eq("table_name", requestDto.getTableName())
                    .eq("snapshoot_version", model.getSnapshootVersion())
                    .and(wrapper -> wrapper.in("type", "float", "double", "decimal").or().like("type", "int")));
            for (QuaWebMysqlTaskResultColumn column : columns) {
                SelectVo vo = new SelectVo();
                vo.setCode(column.getColumnName());
                vo.setValue(column.getColumnName());

                vos.add(vo);
            }
        } else if (ElementTypeEnum.HIVE.getCode().equals(model.getElementType())) {
            List<QuaWebHiveTaskResultColumn> columns = hiveTaskResultColumnService.list(new QueryWrapper<QuaWebHiveTaskResultColumn>()
                    .eq("element_id", model.getElementId())
                    .eq("db_name", model.getDatabaseName())
                    .eq("table_name", requestDto.getTableName())
                    .eq("snapshoot_version", model.getSnapshootVersion())
                    .and(wrapper -> wrapper.in("type", "FLOAT", "DOUBLE", "DECIMAL").or().like("type", "INT")));
            for (QuaWebHiveTaskResultColumn column : columns) {
                SelectVo vo = new SelectVo();
                vo.setCode(column.getColumnName());
                vo.setValue(column.getColumnName());

                vos.add(vo);
            }
        }
        return vos;
    }

    @Override
    public List<SelectVo> queryRuleLevel() {
        List<SelectVo> vos = new ArrayList<>();
        for (RuleLevelEnum value : RuleLevelEnum.values()) {
            SelectVo vo = new SelectVo();
            vo.setCode(value.getCode());
            vo.setValue(value.getDesc());

            vos.add(vo);
        }
        return vos;
    }

    @Override
    public List<SelectVo> queryNormType() {
        List<SelectVo> vos = new ArrayList<>();
        for (NormTypeEnum value : NormTypeEnum.values()) {
            SelectVo vo = new SelectVo();
            vo.setCode(value.getCode());
            vo.setValue(value.getDesc());

            vos.add(vo);
        }
        return vos;
    }

    /**
     * 查询已发布的“数据类型”的标准
     *
     * @return
     */
    @Override
    public List<SelectVo> queryStandard() {
        List<DataStandardConfig> dataStandardConfigs = dataStandardConfigService.list(new QueryWrapper<DataStandardConfig>()
                .eq("status", "1")
                .eq("tenant_id", UmsUtils.getUVO().getTenantId())
                .eq("standard_type", DataStandardTypeEnum.data_column_type.getCode())
                .eq("publish_status", PublishStatusEnum.PUBLISHED.getCode()));
        List<SelectVo> vos = new ArrayList<>();
        for (DataStandardConfig dataStandardConfig : dataStandardConfigs) {
            SelectVo vo = new SelectVo();
            vo.setCode(String.valueOf(dataStandardConfig.getId()));
            vo.setValue(dataStandardConfig.getCnName());

            vos.add(vo);
        }
        return vos;
    }

    @Override
    public List<SelectVo> queryStandardType() {
        List<SelectVo> vos = new ArrayList<>();
        for (StandardTypeEnum value : StandardTypeEnum.values()) {
            SelectVo vo = new SelectVo();
            vo.setCode(value.getCode());
            vo.setValue(value.getDesc());

            vos.add(vo);
        }
        return vos;
    }

    @Override
    public List<SelectVo> queryOperator() {
        List<SelectVo> vos = new ArrayList<>();
        for (OperatorEnum value : OperatorEnum.values()) {
            SelectVo vo = new SelectVo();
            vo.setCode(value.getCode());
            vo.setValue(value.getDesc());

            vos.add(vo);
        }
        return vos;
    }

    @Override
    public List<RuleResponseVo> queryRule(RuleRequestDto requestDto) throws Exception {
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        requestDto.setRuleType("SQL");
        List<RuleResponseVo> results = monitorRuleMapper.queryRule(requestDto);
        if (CollectionUtils.isNotEmpty(results)) {
            for (RuleResponseVo result : results) {
                result.setRuleDetail(JSON.parseObject(result.getDetail(), new TypeReference<RuleExtConfig>() {
                }));
            }
        }
        return results;
    }

    @Override
    public ResultBean fileUpload(MultipartFile file, String name) {
        if (file == null || file.isEmpty()) {
            return ResultBean.fail("请选择需上传的文件");
        }

        // 获取文件大小，如果文件大于10MB，禁止上传
        long size = file.getSize();
        size = size / 1024 / 1024;
        if (size > 10) {
            return ResultBean.fail("上传文件不能大于10MB");
        }


        // 判断上传的文件是否合法
        String fileName = file.getOriginalFilename();
        // 文件上传路径
        String uploadPath = "";
        int tenantId = UmsUtils.getUVO().getTenantId();
        if (StringUtils.isNotEmpty(name)) {
            uploadPath = filePath + File.separator + tenantId + File.separator + name;
        } else {
            uploadPath = filePath + File.separator + tenantId;
        }

        // 如果路径不存在则生成路径
        File uploadFile = new File(uploadPath);
        if (!uploadFile.exists()) {
            uploadFile.mkdirs();
        }

        // 上传文件
        try {
            uploadFile = new File(uploadPath, fileName);
            byte[] bytes = file.getBytes();
            Path path = Paths.get(uploadFile.getAbsolutePath());
            Files.write(path, bytes);
            Map<String, String> map = new HashMap<String, String>();
            map.put("filePath", uploadFile.getAbsolutePath());
            map.put("fileName", fileName);
            return ResultBean.success(map);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultBean.fail("文件上传失败");
        }
    }

    @Override
    public String importRule(String filePath) throws Exception {
        StringBuilder resultMsg = new StringBuilder();
        // 用该字段判断是否有异常，出现异常时 isError != 0
        int isError = 0;
        long start = System.currentTimeMillis();
        File file = new File(filePath);
        if (!file.exists()) {
            throw new ParamInvalidException("文件不存在");
        }
        //默认开始行
        int startLine = 1;
        List<String> errorResults = new ArrayList<>();
        List<String[]> dataList = ExcelUtil.readExcel(file, 0, "SQL");
        //excel数据去重
        Set<String> unique = new HashSet<>();
        for (int i = dataList.size() - 1; i >= 0; i--) {
            String ruleCode = dataList.get(i)[1];
            if (unique.contains(ruleCode)) {
                dataList.remove(i);
            }
            unique.add(ruleCode);
        }
        int totalLine = dataList.size();
        int endLine = totalLine;
        List<String[]> dataTemp = dataList.subList(startLine - 1, endLine);
        int failLine = 0;

        // 获取数据源信息
        int tenantId = UmsUtils.getUVO().getTenantId();
        List<QuaMonitorModel> datasources = modelService.list(new QueryWrapper<QuaMonitorModel>()
                .eq("flag", "1")
                .eq("tenant_id", tenantId));
        Map<String, Object> datasourceMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(datasources)) {
            for (QuaMonitorModel datasource : datasources) {
                datasourceMap.put(datasource.getModelName(), datasource.getId());
            }
        }

        Map<String, Object> operatorMap = new HashMap<>();
        for (OperatorEnum value : OperatorEnum.values()) {
            operatorMap.put(value.getDesc(), value.getCode());
        }

        Map<String, Object> levelMap = new HashMap<>();
        for (RuleLevelEnum value : RuleLevelEnum.values()) {
            levelMap.put(value.getDesc(), value.getCode());
        }

        // 需保存的数据项
        List<QuaMonitorRule> insertData = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder();
        LocalDateTime now = LocalDateTime.now();
        for (String[] data : dataTemp) {
            LOGGER.info("正在处理第【" + startLine + "】行数据.....");
            startLine++;

            QuaMonitorRule rule = new QuaMonitorRule();
            String ruleName = data[0];
            String ruleCode = data[1];
            String dataSourceId = String.valueOf(datasourceMap.get(data[2]));
            String tableName = data[3];
            String columnName = data[4];
            String sql = data[5];
            String compareType = String.valueOf(operatorMap.get(data[6]));
            String compareValue = data[7];
            String ruleLevel = String.valueOf(levelMap.get(data[8]));
            RuleExtConfig ruleDetail = new RuleExtConfig();
            ruleDetail.setSql(sql);
            ruleDetail.setCompareValue(compareValue);
            ruleDetail.setOperator(compareType);
            if (StringUtils.isEmpty(ruleName)
                    || StringUtils.isEmpty(ruleCode)
                    || StringUtils.isEmpty(dataSourceId)
                    || StringUtils.isEmpty(tableName)
                    || StringUtils.isEmpty(columnName)
                    || StringUtils.isEmpty(sql)
                    || StringUtils.isEmpty(compareType)
                    || StringUtils.isEmpty(compareValue)
                    || StringUtils.isEmpty(ruleLevel)) {
                failLine++;
                stringBuilder = stringBuilder.append("第【").append(startLine - 1).append("】行数据不能为空");
                continue;
            }

            rule.setRuleCode(ruleCode);
            rule.setRuleName(ruleName);
            rule.setModelId(Long.valueOf(dataSourceId));
            rule.setTableName(tableName);
            rule.setRuleType("SQL");
            rule.setColumnName(columnName);
            rule.setRuleLevel(ruleLevel);
            rule.setRuleDetail(JSONObject.toJSONString(ruleDetail));
            rule.setCreateTime(now);
            rule.setUpdateTime(now);
            rule.setTenantId(Long.valueOf(tenantId));
            rule.setCreateUser(UmsUtils.getUVO().getUserName());

            insertData.add(rule);
        }

        if (!CollectionUtils.isEmpty(insertData)) {
            this.saveBatch(insertData);
        }

        long end = System.currentTimeMillis();
        LOGGER.info("导入" + totalLine + "条账号总耗时：" + ((end - start) / 1000) + "秒");

        if (failLine > 0) {
            int successLine = totalLine - failLine;
            LOGGER.info("导入失败" + failLine + "条数据");
            resultMsg =
                    new StringBuilder("总共").append(totalLine).append("条数据,").append(successLine).append("条数据导入成功,").append(failLine).append("条数据导入失败;").append(errorResults);
        } else {
            resultMsg = resultMsg.append("总共").append(totalLine).append("条数据,全部成功;");
        }

        ExcelUtil.delFile(filePath);
        return resultMsg.toString();
    }

    /**
     * 试跑规则
     *
     * @param ruleId
     * @return
     * @throws Exception
     */
    @Override
    public String testExec(Long ruleId) throws Exception {
        UserValueObject uvo = UmsUtils.getUVO();
        LocalDateTime now = LocalDateTime.now();

        String requestPath = TEST_EXEC + ruleId;
        String testExecUrl = collectUrl + requestPath;
        String execResult = HttpUtils.doGet(testExecUrl, null, null);

        // 保存匹配结果
        QuaMonitorRuleExecRecord ruleExecRecord = new QuaMonitorRuleExecRecord();
        ruleExecRecord.setRuleId(ruleId);
        ruleExecRecord.setExecResult(execResult);
        ruleExecRecord.setCreateTime(now);
        ruleExecRecord.setCreateUser(uvo.getUserName());
        ruleExecRecord.setTenantId(Long.valueOf(uvo.getTenantId()));
        ruleExecRecordService.save(ruleExecRecord);

        return execResult;
    }

    @Override
    public RuleResponseVo execRecord(Long ruleId) throws Exception {
        UserValueObject uvo = UmsUtils.getUVO();
        RuleResponseVo ruleResponse = new RuleResponseVo();
        List<QuaMonitorRuleExecRecord> records = ruleExecRecordService.list(new QueryWrapper<QuaMonitorRuleExecRecord>()
                .eq("rule_id", ruleId)
                .eq("create_user", uvo.getUserName())
                .eq("tenant_id", uvo.getTenantId())
                .orderByDesc("create_time"));
        if (CollectionUtils.isNotEmpty(records)) {
            QuaMonitorRuleExecRecord latestRecord = records.get(0);

            RuleRequestDto requestDto = new RuleRequestDto();
            requestDto.setId(ruleId);
            requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
            PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
            QuaMonitorRuleTemplate ruleTemplate = monitorRuleTemplateService.getById(ruleId);
            QuaMonitorModel model = modelService.getById(ruleTemplate.getModelId());
            List<QuaMonitorRule> rules = monitorRuleService.list(new LambdaQueryWrapper<QuaMonitorRule>()
                    .eq(QuaMonitorRule::getTemplateId, String.valueOf(ruleId)));
            Set<String> ruleTypes = new HashSet<>();
            Set<String> levelNames = new HashSet<>();
            for (QuaMonitorRule rule : rules) {
                String ruleType = rule.getRuleType();
                String level = rule.getRuleLevel();
                if(StringUtils.isNotEmpty(level)) {
                    levelNames.add(RuleLevelEnum.getLevelName(level));
                }
                if(StringUtils.isNotEmpty(ruleType)) {
                    ruleTypes.add(RuleTypeEnum.getNameByCode(ruleType));
                }
            }
            ruleResponse.setTableName(ruleTemplate.getTableName());
            ruleResponse.setModelName(model.getModelName());
            ruleResponse.setRuleType(StringUtils.join(ruleTypes, ","));
            ruleResponse.setRuleLevel(StringUtils.join(levelNames, ","));
            ruleResponse.setExecTime(latestRecord.getCreateTime());
            if ("1".equals(latestRecord.getExecResult())) {
                ruleResponse.setExecResult("是");
            } else {
                ruleResponse.setExecResult("否");
            }
        }
        return ruleResponse;
    }

    private void wrapRule(QualityRule qualityRule, QuaMonitorRule rule) {
        UserValueObject uvo = UmsUtils.getUVO();
        LocalDateTime now = LocalDateTime.now();
        rule.setRuleTypeId(qualityRule.getRuleTypeId());
        rule.setModelId(qualityRule.getModelId());
        rule.setRuleLevel(qualityRule.getRuleLevel());
        rule.setRuleDesc(qualityRule.getRuleDesc());
        rule.setRuleWeight(qualityRule.getRuleWeight());
        if(StringUtils.isEmpty(qualityRule.getTemplateId())) {
            rule.setTemplateId(qualityRule.getVirtualId());
        } else {
            rule.setTemplateId(qualityRule.getTemplateId());
        }
        rule.setThresholdValue(qualityRule.getThreshold());
        rule.setThresholdOperator(qualityRule.getThresholdOperator());
        rule.setUpdateTime(now);
        rule.setUpdateUser(uvo.getUserName());
        rule.setTenantId(Long.valueOf(uvo.getTenantId()));
        if(qualityRule.getTimely() != null) {
            // 及时性检查参数
            String column = qualityRule.getTimely().getColumn1() + "," + qualityRule.getTimely().getColumn2();
            rule.setColumnName(column);
            rule.setRuleType(RuleTypeEnum.timely.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getTimely()));
        } else if(qualityRule.getRepeatRate() != null) {
            // 重复率检查
            rule.setColumnName(qualityRule.getRepeatRate().getColumn());
            rule.setRuleType(RuleTypeEnum.repeatRate.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getRepeatRate()));
        } else if(qualityRule.getRepeatLine() != null) {
            // 重复行检查
            rule.setColumnName(qualityRule.getRepeatLine().getColumn());
            rule.setRuleType(RuleTypeEnum.repeatLine.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getRepeatLine()));
        } else if(qualityRule.getNullRate() != null) {
            // 空值率检查
            rule.setColumnName(qualityRule.getNullRate().getColumn());
            rule.setRuleType(RuleTypeEnum.nullRate.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getNullRate()));
        } else if(qualityRule.getNullLine() != null) {
            // 空值行检查
            rule.setColumnName(qualityRule.getNullLine().getColumn());
            rule.setRuleType(RuleTypeEnum.nullLine.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getNullLine()));
        } else if(qualityRule.getLogicLine() != null) {
            // 逻辑通过数检查
            rule.setRuleType(RuleTypeEnum.logicLine.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getLogicLine()));
            List<TemplateLogicContent> contents = qualityRule.getLogicLine().getLogicContent();
            String column = "";
            for (TemplateLogicContent content : contents) {
                column = column + content.getColumn() + ",";
            }
            rule.setColumnName(column.substring(0, column.length() - 1));
        } else if(qualityRule.getLogicRate() != null) {
            // 逻辑通过率检查
            rule.setRuleType(RuleTypeEnum.logicRate.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getLogicRate()));
            List<TemplateLogicContent> contents = qualityRule.getLogicRate().getLogicContent();
            String column = "";
            for (TemplateLogicContent content : contents) {
                column = column + content.getColumn() + ",";
            }
            rule.setColumnName(column.substring(0, column.length() - 1));
        } else if(qualityRule.getLengthRate() != null) {
            // 长度规范率
            rule.setColumnName(qualityRule.getLengthRate().getColumn());
            rule.setRuleType(RuleTypeEnum.lengthRate.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getLengthRate()));
        } else if(qualityRule.getLengthLine() != null) {
            // 长度规范行
            rule.setColumnName(qualityRule.getLengthLine().getColumn());
            rule.setRuleType(RuleTypeEnum.lengthLine.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getLengthLine()));
        } else if(qualityRule.getFormatRate() != null) {
            // 格式规范率
            rule.setColumnName(qualityRule.getFormatRate().getColumn());
            rule.setRuleType(RuleTypeEnum.formatRate.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getFormatRate()));
        } else if(qualityRule.getFormatLine() != null) {
            // 数据规范行
            rule.setColumnName(qualityRule.getFormatLine().getColumn());
            rule.setRuleType(RuleTypeEnum.formatLine.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getFormatLine()));
        } else if(qualityRule.getRangeLine() != null) {
            // 值域通过数检查
            rule.setColumnName(qualityRule.getRangeLine().getColumn());
            rule.setRuleType(RuleTypeEnum.rangeLine.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getRangeLine()));
        } else if(qualityRule.getRangeRate() != null) {
            // 值域通过率检查
            rule.setColumnName(qualityRule.getRangeRate().getColumn());
            rule.setRuleType(RuleTypeEnum.rangeRate.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getRangeRate()));
        } else if(qualityRule.getTableSize() != null) {
            // 表大小波动
            rule.setRuleType(RuleTypeEnum.tableSize.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getTableSize()));
        } else if(qualityRule.getTableLine() != null) {
            // 表行数波动
            rule.setRuleType(RuleTypeEnum.tableLine.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getTableLine()));
        } else if(qualityRule.getSelfDefine() != null) {
            // 自定义模板
            rule.setColumnName(qualityRule.getSelfDefine().getColumn());
            rule.setRuleType(RuleTypeEnum.selfDefine.getCode());
            rule.setRuleDetail(JSONObject.toJSONString(qualityRule.getSelfDefine()));
        }
    }
}
