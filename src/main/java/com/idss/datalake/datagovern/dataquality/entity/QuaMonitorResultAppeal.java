package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 质量检测结果申诉
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QuaMonitorResultAppeal implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long resultDetailId;

    /**
     * 申诉人
     */
    private String appealUser;

    /**
     * 申诉原因
     */
    private String appealReason;

    /**
     * 申诉时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime appealTime;

    /**
     * 处理人
     */
    private String handleUser;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime handleTime;

    /**
     * 处理状态：0未处理，1已处理
     */
    private Boolean handleStatus;

    private Integer tenantId;


    @TableField(exist = false)
    private String jobName;
    @TableField(exist = false)
    private String columnName;
    @TableField(exist = false)
    private String ruleType;
    @TableField(exist = false)
    private String ruleTypeDesc;

    private Integer delFlag;
}
