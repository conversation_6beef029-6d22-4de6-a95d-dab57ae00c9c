package com.idss.datalake.datagovern.dataquality.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleTemplate;
import com.idss.datalake.datagovern.dataquality.model.RuleRequestDto;
import com.idss.datalake.datagovern.dataquality.model.RuleResponseVo;
import com.idss.datalake.datagovern.dataquality.model.RuleTypeResponseVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 质量监测规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
public interface QuaMonitorRuleMapper extends BaseMapper<QuaMonitorRule> {

    Page<QuaMonitorRuleTemplate> queryRulePage(RuleRequestDto requestDto);

    Page<RuleResponseVo> queryColumnPage(RuleRequestDto requestDto);

    List<Map<String, Object>> existRule(Map<String, Object> params);

    Page<RuleTypeResponseVo> queryRuleTypePage(RuleRequestDto requestDto);

    List<RuleResponseVo> queryRule(RuleRequestDto params);
}
