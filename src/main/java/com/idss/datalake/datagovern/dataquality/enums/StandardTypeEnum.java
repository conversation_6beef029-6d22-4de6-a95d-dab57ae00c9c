package com.idss.datalake.datagovern.dataquality.enums;

/**
 * @Author: x<PERSON>xiaofei
 * @Date: 19/6/2021 13:05
 * @Description: 规范类别
 */
public enum StandardTypeEnum {
    uniformity("uniformity", "一致性"),
    timeliness("timeliness", "及时性"),
    uniqueness("uniqueness", "唯一性"),
    effectiveness("effectiveness", "有效性"),
    integrality("integrality", "完整性"),
    precision("precision", "准确性");



    StandardTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getNameByCode(String code) {
        for (StandardTypeEnum value : StandardTypeEnum.values()) {
            if(code.equals(value.getCode())) {
                return value.getDesc();
            }
        }
        return code;
    }

    private String code;
    private String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
