package com.idss.datalake.datagovern.dataquality.service;

import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.dataquality.model.*;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.portal.dto.LineData;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 质量监测模型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
public interface IQuaMonitorModelService extends IService<QuaMonitorModel> {

    /**
     * 分页查询模型信息
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<ModelResponseVo>> queryModelPage(ModelRequestDto requestDto);

    /**
     * 查看模型详情
     * @param id
     * @return
     */
    ModelResponseVo queryModelDetail(String id);

    /**
     * 新增或编辑模型
     * @param modelRequestDto
     */
    void addOrUpdate(ModelRequestDto modelRequestDto);

    /**
     * 删除模型
     * @param modelRequestDto
     */
    void delete(ModelRequestDto modelRequestDto);

    List<Map<String, Object>> queryAllModel();

    void openOrClose(ModelRequestDto modelRequestDto) throws Exception;

    ModelTaskVo taskDetail(Long modelId) throws Exception;

    LineData taskTrend(Long modelId) throws Exception;

    /**
     * 分页查询质量详情
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<ModelTaskDetailVo>> queryTaskDetailPage(ModelRequestDto requestDto);
}
