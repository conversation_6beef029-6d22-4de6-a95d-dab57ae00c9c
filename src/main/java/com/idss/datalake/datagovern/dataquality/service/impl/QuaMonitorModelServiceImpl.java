package com.idss.datalake.datagovern.dataquality.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.TimeRange;
import com.idss.datalake.datagovern.dataquality.entity.*;
import com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorModelMapper;
import com.idss.datalake.datagovern.dataquality.model.*;
import com.idss.datalake.datagovern.dataquality.service.*;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.portal.dto.LineData;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.exolab.castor.xml.validators.IntegerValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 质量监测模型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Service
public class QuaMonitorModelServiceImpl extends ServiceImpl<QuaMonitorModelMapper, QuaMonitorModel> implements IQuaMonitorModelService {

    @Autowired
    private QuaMonitorModelMapper monitorModelMapper;

    @Autowired
    private IQuaMonitorModelResourceService modelResourceService;

    @Autowired
    private IQuaMonitorJobService monitorJobService;

    @Autowired
    private IQuaMonitorTaskService monitorTaskService;

    @Autowired
    private IQuaMonitorRuleService monitorRuleService;

    @Autowired
    private IQuaMonitorResultService monitorResultService;

    @Override
    public BasePageResponse<List<ModelResponseVo>> queryModelPage(ModelRequestDto requestDto) {
        List<ModelResponseVo> list = new ArrayList<>();
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<ModelResponseVo> page = monitorModelMapper.queryModelPage(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        List<ModelResponseVo> result = page.getResult();
        result.stream().forEach(vo -> {
            // ES类型没有db，回显时将table(即index)名称赋值给database_name显示
            if (ElementTypeEnum.ES.getCode().equals(vo.getElementType())) {
                List<QuaMonitorModelResource> resources = modelResourceService.list(new QueryWrapper<QuaMonitorModelResource>()
                        .eq("model_id", vo.getId()));
                String tableNames = resources.stream().filter(x -> StringUtils.isNotBlank(x.getTableName()))
                        .map(x -> x.getTableName()).collect(Collectors.joining(","));
                vo.setDatabaseName(tableNames);
            }
        });
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), result);
    }

    @Override
    public ModelResponseVo queryModelDetail(String id) {
        QuaMonitorModel monitorModel = getById(id);
        List<QuaMonitorModelResource> resources = modelResourceService.list(new QueryWrapper<QuaMonitorModelResource>()
                .eq("model_id", id));

        ModelResponseVo vo = new ModelResponseVo();
        vo.setModelName(monitorModel.getModelName());
        vo.setModelDesc(monitorModel.getModelDesc());
        vo.setDatabaseId(monitorModel.getDatabaseId());
        vo.setDatabaseName(monitorModel.getDatabaseName());
        vo.setElementId(monitorModel.getElementId());
        vo.setElementType(monitorModel.getElementType());
        vo.setSnapshootVersion(monitorModel.getSnapshootVersion());
        List<ModelTable> tables = new ArrayList<>();
        for (QuaMonitorModelResource resource : resources) {
            ModelTable table = new ModelTable();
            table.setId(resource.getTableId());
            table.setName(resource.getTableName());

            tables.add(table);
        }
        vo.setTables(tables);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(ModelRequestDto modelRequestDto) {
        UserValueObject uvo = UmsUtils.getUVO();
        if (modelRequestDto.getId() == null) {
            // 保存模型信息
            QuaMonitorModel model = new QuaMonitorModel();
            model.setModelName(modelRequestDto.getModelName());
            model.setModelDesc(modelRequestDto.getModelDesc());
            model.setElementId(modelRequestDto.getElementId());
            model.setElementType(modelRequestDto.getElementType());
            model.setDatabaseId(modelRequestDto.getDatabaseId());
            model.setDatabaseName(modelRequestDto.getDatabaseName());
            model.setTenantId(Long.valueOf(uvo.getTenantId()));
            model.setSnapshootVersion(modelRequestDto.getSnapshootVersion());
            model.setCreateUser(uvo.getUserName());
            save(model);

            // 保存模型相关的表
            saveModelResource(modelRequestDto, model, uvo);
        } else {
            // 修改模型数据
            QuaMonitorModel monitorModel = getById(modelRequestDto.getId());
            monitorModel.setModelName(modelRequestDto.getModelName());
            monitorModel.setModelDesc(modelRequestDto.getModelDesc());
            monitorModel.setElementId(modelRequestDto.getElementId());
            monitorModel.setElementType(modelRequestDto.getElementType());
            monitorModel.setDatabaseId(modelRequestDto.getDatabaseId());
            monitorModel.setDatabaseName(modelRequestDto.getDatabaseName());
            monitorModel.setTenantId(Long.valueOf(uvo.getTenantId()));
            monitorModel.setSnapshootVersion(modelRequestDto.getSnapshootVersion());
            monitorModel.setUpdateUser(uvo.getUserName());
            updateById(monitorModel);

            modelResourceService.remove(new QueryWrapper<QuaMonitorModelResource>()
                    .eq("model_id", modelRequestDto.getId()));
            saveModelResource(modelRequestDto, monitorModel, uvo);
        }
    }

    @Override
    public void delete(ModelRequestDto modelRequestDto) {
        update(new UpdateWrapper<QuaMonitorModel>()
                .set("flag", "0")
                .in("id", modelRequestDto.getIds()));
    }

    @Override
    public List<Map<String, Object>> queryAllModel() {
        List<Map<String, Object>> vos = new ArrayList<>();

        int tenantId = UmsUtils.getUVO().getTenantId();
        List<QuaMonitorModel> monitorModels = this.list(new QueryWrapper<QuaMonitorModel>()
                .eq("flag", "1")
                .eq("open_status", "1")
                .eq("tenant_id", tenantId));
        for (QuaMonitorModel monitorModel : monitorModels) {
            Map<String, Object> vo = new HashMap<>();
            vo.put("code", monitorModel.getId());
            vo.put("value", monitorModel.getModelName());
            vo.put("elementType", monitorModel.getElementType());

            vos.add(vo);
        }

        return vos;
    }

    @Override
    public void openOrClose(ModelRequestDto modelRequestDto) throws Exception {
        // 如果数据源被使用，则无法关闭
        List<QuaMonitorJob> jobs = monitorJobService.list(new QueryWrapper<QuaMonitorJob>()
                .eq("model_id", modelRequestDto.getId())
                .eq("flag", "1"));
        if(CollectionUtils.isNotEmpty(jobs) && "0".equals(modelRequestDto.getOpenStatus())) {
            throw new ParamInvalidException("数据源已被使用无法关闭");
        }

        this.update(new UpdateWrapper<QuaMonitorModel>()
                .set("open_status", modelRequestDto.getOpenStatus())
                .eq("id", modelRequestDto.getId()));
    }

    @Override
    public ModelTaskVo taskDetail(Long modelId) throws Exception {
        int tenantId = UmsUtils.getUVO().getTenantId();
        ModelTaskVo modelTaskVo = new ModelTaskVo();
        QuaMonitorModel monitorModel = this.getById(modelId);
        modelTaskVo.setDatasourceName(monitorModel.getModelName());
        String openStatus = "开启";
        if("0".equals(monitorModel.getOpenStatus())) {
            openStatus = "关闭";
        }
        modelTaskVo.setOpenStatus(openStatus);
        modelTaskVo.setDbName(monitorModel.getDatabaseName());
        List<QuaMonitorModelResource> resources = modelResourceService.list(new QueryWrapper<QuaMonitorModelResource>()
                .eq("model_id", modelId));
        String tableNames = resources.stream().filter(x -> StringUtils.isNotBlank(x.getTableName()))
                .map(x -> x.getTableName()).collect(Collectors.joining(","));
        modelTaskVo.setTableName(tableNames);
        if (ElementTypeEnum.ES.getCode().equals(monitorModel.getElementType())) {
            modelTaskVo.setDbName(tableNames);
            modelTaskVo.setTableName("");
        }

        // 质量任务数/质量规则数/质量分
        List<QuaMonitorJob> jobs = monitorJobService.list(new QueryWrapper<QuaMonitorJob>()
                .eq("model_id", modelId)
                .eq("tenant_id", tenantId));
        Integer ruleScore = 0;
        if(CollectionUtils.isNotEmpty(jobs)) {
            modelTaskVo.setTaskNum(jobs.size());
            int ruleNum = 0;
            for (QuaMonitorJob job : jobs) {
                String rules = job.getJobRules();
                List<QuaMonitorRule> ruleList = monitorRuleService.list(new LambdaQueryWrapper<QuaMonitorRule>()
                        .eq(QuaMonitorRule::getTemplateId, rules));
                if(CollectionUtils.isNotEmpty(ruleList)) {
                    ruleNum = ruleNum + ruleList.size();
                }
                // 获取质量分
                List<QuaMonitorResult> results = monitorResultService.list(new QueryWrapper<QuaMonitorResult>()
                        .eq("del_flag",0).eq("job_id", job.getId())
                        .orderByDesc("create_time"));
                if(CollectionUtils.isNotEmpty(results)) {
                    String score = results.get(0).getMonitorScore();
                    ruleScore = ruleScore + Integer.valueOf(score);
                }
            }
            modelTaskVo.setRuleNum(ruleNum);
        }
        modelTaskVo.setRuleScore(ruleScore);
        modelTaskVo.setRuleScoreTotal(100 * (jobs.size()));

        return modelTaskVo;
    }

    @Override
    public LineData taskTrend(Long modelId) throws Exception {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 获取最近7天的数据
        Calendar endDate = Calendar.getInstance();
        Calendar startDate = Calendar.getInstance();
        startDate.add(Calendar.DATE, -6);
        String start = format.format(startDate.getTime());
        String end = format.format(endDate.getTime());
        Map<String, Object> params = new HashMap<>();
        params.put("modelId", modelId);
        params.put("startDate", start);
        params.put("endDate", end);
        List<Map<String, Object>> results = monitorModelMapper.taskTrend(params);
        List<String> times = TimeRange.getDateRange(start, end, Calendar.DATE, "yyyy-MM-dd");
        List<String> xdata = new ArrayList<>();  // x轴数据
        List<Integer> ydata = new ArrayList<>();  // y轴数据

        if(CollectionUtils.isNotEmpty(results)) {
            Map<String, Object> resultMap = new HashMap<>();
            for(Map<String, Object> map : results) {
                resultMap.put(String.valueOf(map.get("createTime")), map.get("cnt"));
            }
            for(String time : times) {
                xdata.add(time);
                if(resultMap.get(time) != null) {
                    ydata.add(Integer.valueOf(String.valueOf(resultMap.get(time))));
                } else {
                    ydata.add(0);
                }
            }
        } else {
            for(String time : times) {
                xdata.add(time);
                ydata.add(0);
            }
        }

        LineData lineData = new LineData();
        lineData.setXData(xdata);
        lineData.setYData(ydata);
        return lineData;
    }

    @Override
    public BasePageResponse<List<ModelTaskDetailVo>> queryTaskDetailPage(ModelRequestDto requestDto) {
        List<ModelTaskDetailVo> list = new ArrayList<>();
        int tenantId = UmsUtils.getUVO().getTenantId();
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        QueryWrapper<QuaMonitorJob> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        if(requestDto.getId() != null) {
            queryWrapper.eq("model_id", requestDto.getId());
        }
        if(StringUtils.isNotEmpty(requestDto.getTaskName())) {
            queryWrapper.like("name", "%" + requestDto.getTaskName() + "%");
        }
        if(StringUtils.isNotEmpty(requestDto.getStartTime())) {
            queryWrapper.ge("create_time", requestDto.getStartTime());
        }
        if(StringUtils.isNotEmpty(requestDto.getEndTime())) {
            queryWrapper.le("create_time", requestDto.getEndTime());
        }
        PageInfo<QuaMonitorJob> pageInfo = new PageInfo(monitorJobService.list(queryWrapper));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new BasePageResponse<>(pageInfo.getPages(), pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), list);
        } else {
            List<QuaMonitorJob> jobs = pageInfo.getList();
            for (QuaMonitorJob job : jobs) {
                ModelTaskDetailVo vo = new ModelTaskDetailVo();
                list.add(vo);
                vo.setTaskName(job.getName());
                if ("02".equals(job.getExecuteType())) {
                    vo.setTaskType("周期任务");
                } else {
                    vo.setTaskType("一次性任务");
                }
                // 获取执行次数
                int execNum = monitorTaskService.count(new QueryWrapper<QuaMonitorTask>()
                        .eq("job_id", job.getId())
                        .eq("tenant_id", tenantId));
                vo.setExecNum(execNum);

                // 规则格式
                if (StringUtils.isNotEmpty(job.getJobRules())) {
                    List<String> ruleIds = Arrays.asList(job.getJobRules().split(","));

                    // 获取字段数
                    int columnNum = 0;
                    List<QuaMonitorRule> rules = monitorRuleService.list(new QueryWrapper<QuaMonitorRule>()
                            .in("template_id", ruleIds));
                    vo.setRuleNum(rules.size());
                    for (QuaMonitorRule rule : rules) {
                        String columns = rule.getColumnName();
                        if (StringUtils.isNotEmpty(columns)) {
                            columnNum = columnNum + columns.split(",").length;
                        }
                    }
                    vo.setColumnNum(columnNum);
                }

                // 获取质量分
                QueryWrapper<QuaMonitorResult> wrapper = new QueryWrapper<>();
                List<Map<String, Object>> scores = monitorResultService.listMaps(new QueryWrapper<QuaMonitorResult>()
                        .select("CAST(avg(monitor_score) AS SIGNED) score")
                        .eq("del_flag",0)
                        .eq("job_id", job.getId()));
                if (CollectionUtils.isNotEmpty(scores)) {
                    if(scores.get(0) != null) {
                        vo.setTaskScore(Integer.valueOf(String.valueOf(scores.get(0).get("score"))));
                    }
                }
            }

            return new BasePageResponse<>(pageInfo.getPages(), pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), list);
        }
    }

    private void saveModelResource(ModelRequestDto modelRequestDto, QuaMonitorModel model, UserValueObject uvo) {
        List<ModelTable> tables = modelRequestDto.getTables();
        List<QuaMonitorModelResource> resources = new ArrayList<>();
        for (ModelTable table : tables) {
            QuaMonitorModelResource resource = new QuaMonitorModelResource();
            resource.setModelId(model.getId());
            resource.setTableId(table.getId());
            resource.setTableName(table.getName());
            resource.setTenantId(Long.valueOf(uvo.getTenantId()));
            resource.setCreateUser(uvo.getUserName());

            resources.add(resource);
        }
        if (CollectionUtils.isNotEmpty(resources)) {
            modelResourceService.saveBatch(resources);
        }
    }
}
