/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/17 17:17
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.model;

import lombok.Data;

import java.util.List;

/**
 * Copyright 2022 IDSS
 * <p> 规则个性化配置
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/17 17:17
 */
@Data
public class RuleExtConfig {

    private String unionNull;

    private String rangeValue;

    private String normType;

    private String reg;

    private String days;

    private String missingTable;

    private List<String> missingColumn;

    private String quoteTable;

    private List<String> quoteColumn;

    private String baseLine;

    private String fluctuationRange;

    private String standards;

    private String checkType;

    private String sql;

    private String operator;

    private String compareValue;

    private String type;
}
