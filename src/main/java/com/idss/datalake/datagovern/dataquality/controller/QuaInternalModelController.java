package com.idss.datalake.datagovern.dataquality.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datagovern.dataquality.dto.InternalModelPageRequest;
import com.idss.datalake.datagovern.dataquality.entity.QuaInternalModel;
import com.idss.datalake.datagovern.dataquality.service.IQuaInternalModelService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 数据质量内置模版 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-13
 */
@Slf4j
@RestController
@RequestMapping(Constant.API_PREFIX + "/dataQuality/internalModel")
public class QuaInternalModelController {
    @Autowired
    private IQuaInternalModelService quaInternalModelService;

    @PostMapping("/page")
    public BasePageResponse<List<QuaInternalModel>> page(@RequestBody InternalModelPageRequest requestDto) {
        return quaInternalModelService.page(requestDto);
    }

    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        return ResultBean.success(quaInternalModelService.getById(id));
    }
}
