package com.idss.datalake.datagovern.dataquality.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.datagovern.dataquality.dto.CustomModelPageRequest;
import com.idss.datalake.datagovern.dataquality.entity.QuaInternalModel;
import com.idss.datalake.datagovern.dataquality.enums.RuleTypeEnum;
import com.idss.datalake.datagovern.dataquality.service.IQuaCustomizeModelService;
import com.idss.datalake.datagovern.dataquality.service.IQuaInternalModelService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 数据质量自定义模版 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-13
 */
@RestController
@Slf4j
@RequestMapping(Constant.API_PREFIX + "/dataQuality/customizeModel")
public class QuaCustomizeModelController {
    @Autowired
    private IQuaInternalModelService quaInternalModelService;

    @Autowired
    private IQuaCustomizeModelService quaCustomizeModelService;

    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody QuaInternalModel model) {
        UserValueObject uvo = UmsUtils.getUVO();
        try {
            checkSQL(model.getCustomSql());
        } catch (ParamInvalidException e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
        if (model.getId() == null) {
            int count = quaInternalModelService.count(new LambdaQueryWrapper<QuaInternalModel>()
                    .eq(QuaInternalModel::getName, model.getName())
                    .eq(QuaInternalModel::getModelType, "2")
                    .eq(QuaInternalModel::getTenantId, uvo.getTenantId()));
            if (count > 0) {
                return ResultBean.fail("名称已存在");
            }
            model.setModelCode(RuleTypeEnum.selfDefine.getCode());
            model.setCustomSql(model.getCustomSql());
            model.setModelType("2");
            model.setTenantId(uvo.getTenantId());
            model.setCreateUser(uvo.getUserName());
            model.setCreateTime(LocalDateTime.now());
            model.setUpdateUser(uvo.getUserName());
            model.setUpdateTime(LocalDateTime.now());
            quaInternalModelService.save(model);
        } else {
            int count = quaInternalModelService.count(new LambdaUpdateWrapper<QuaInternalModel>()
                    .ne(QuaInternalModel::getId, model.getId())
                    .eq(QuaInternalModel::getName, model.getName())
                    .eq(QuaInternalModel::getModelType, "2")
                    .eq(QuaInternalModel::getTenantId, uvo.getTenantId()));
            if (count > 0) {
                return ResultBean.fail("名称已存在");
            }
            QuaInternalModel byId = quaInternalModelService.getById(model.getId());
            byId.setCustomSql(model.getCustomSql());
            byId.setUpdateUser(uvo.getUserName());
            byId.setUpdateTime(LocalDateTime.now());
            byId.setName(model.getName());
            byId.setModelDesc(model.getModelDesc());
            byId.setDimensions(model.getDimensions());
            quaInternalModelService.updateById(byId);
        }
        return ResultBean.success();
    }

    @GetMapping("/delete/{id}")
    public ResultBean delete(@PathVariable("id") Long id) {
        quaInternalModelService.removeById(id);
        return ResultBean.success();
    }

    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        return ResultBean.success(quaInternalModelService.getById(id));
    }

    @PostMapping("/page")
    public BasePageResponse<List<QuaInternalModel>> page(@RequestBody CustomModelPageRequest requestDto) {
        return quaCustomizeModelService.page(requestDto);
    }

    /**
     * 校验自定义sql是否合肥
     * @param sql
     * @throws Exception
     */
    private void checkSQL(String sql) throws ParamInvalidException {
        if(!sql.contains("$table")) {
            throw new ParamInvalidException("自定义SQL必须包含$table变量");
        }
    }
}
