package com.idss.datalake.datagovern.dataquality.model;

import lombok.Data;

@Data
public class ModelTaskVo {

    private String datasourceName;

    private String dbName;

    private String tableName;

    /**
     * 数据源状态
     */
    private String openStatus;

    /**
     * 质量任务数
     */
    private Integer taskNum = 0;

    /**
     * 质量规则数
     */
    private Integer ruleNum = 0;

    /**
     * 规则总分
     */
    private Integer ruleScoreTotal = 0;

    /**
     * 规则分
     */
    private Integer ruleScore = 0;
}
