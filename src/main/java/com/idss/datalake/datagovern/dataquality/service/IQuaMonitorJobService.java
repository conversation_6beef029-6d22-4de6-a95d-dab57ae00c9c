package com.idss.datalake.datagovern.dataquality.service;

import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorJob;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRule;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleTemplate;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorTask;
import com.idss.datalake.datagovern.dataquality.model.*;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 质量监测JOB 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
public interface IQuaMonitorJobService extends IService<QuaMonitorJob> {

    /**
     * 分页查询监测任务信息
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<JobResponseVo>> queryJobPage(JobRequestDto requestDto);

    /**
     * 查看任务详情
     *
     * @param id
     * @return
     */
    QuaMonitorJob queryJobDetail(String id);

    /**
     * 查看规则
     *
     * @param modelId
     * @return
     */
    List<QuaMonitorRule> queryRules(JobRequestDto requestDto);

    /**
     * 新增或编辑规则
     *
     * @param requestDto
     */
    void addOrUpdate(JobRequestDto requestDto) throws Exception;

    /**
     * 重新执行
     * @param jobId
     * @param taskNo
     */
    void reRun(Long jobId,String taskNo);

    /**
     * 查看子任务
     */
    Map<String, Object> taskList(String jobId, JobRequestDto requestDto) throws Exception;

    void pauseOrResumeJon(JobRequestDto requestDto);

    /**
     * 查看任务结果
     *
     * @param taskId
     * @return
     */
    TaskResultResponseVo taskResult(Long taskId);

    /**
     * 分页查询监测任务信息
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<TaskResultDetailVo>> taskDetail(JobRequestDto requestDto);

    /**
     * 删除规则
     *
     * @param requestDto
     */
    void delete(JobRequestDto requestDto) throws Exception;

    /**
     * 表信息
     * @param requestDto
     * @return
     * @throws Exception
     */
    TaskOverview tableInfo(JobRequestDto requestDto) throws Exception;

    /**
     * 评分趋势
     * @param requestDto
     * @return
     * @throws Exception
     */
    TaskScoreTrend scoreTrend(JobRequestDto requestDto) throws Exception;

    /**
     * 评分详情
     * @param requestDto
     * @return
     * @throws Exception
     */
    BasePageResponse<List<TaskScoreDetail>> scoreDetail(JobRequestDto requestDto) throws Exception;

    /**
     * 评分详情导出
     * @param requestDto
     * @param response
     * @throws Exception
     */
    void scoreDownload(JobRequestDto requestDto, HttpServletResponse response) throws Exception;

    /**
     * 批量导出
     * @param requestDto
     * @param response
     */
    void batchDownload(List<JobRequestDto> requestDto, HttpServletResponse response);

    /**
     * 维度统计
     * @param requestDto
     * @return
     * @throws Exception
     */
    Map<String, Object> taskDim(JobRequestDto requestDto) throws Exception;
}
