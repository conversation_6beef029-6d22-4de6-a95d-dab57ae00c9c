package com.idss.datalake.datagovern.dataquality.service;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.dataquality.dto.DyncThresholdJobPageRequest;
import com.idss.datalake.datagovern.dataquality.dto.DyncThresholdJobPageVo;
import com.idss.datalake.datagovern.dataquality.dto.DyncThresholdPageRequest;
import com.idss.datalake.datagovern.dataquality.entity.QuaDyncThreshold;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import java.util.List;

/**
 * <p>
 * 数据质量动态阈值管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
public interface IQuaDyncThresholdService extends IService<QuaDyncThreshold> {

    BasePageResponse<List<DyncThresholdJobPageVo>> queryJobPage(DyncThresholdJobPageRequest requestDto);

    BasePageResponse<List<QuaDyncThreshold>> page(DyncThresholdPageRequest requestDto);


    ResultBean elementDbname(String sourceType);
    ResultBean elementTable(String sourceType,Long elementId,String dbName);
    ResultBean elementIndex();

}
