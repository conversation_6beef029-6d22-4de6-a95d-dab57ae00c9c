/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/21 16:45
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright 2022 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/21 16:45
 */
@Data
public class JobCron {

    @ApiModelProperty("正则表达式")
    private String executeCron;

    private String executeType;

    private JobCronConfig executeConfig;
}
