package com.idss.datalake.datagovern.dataquality.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.datagovern.dataquality.dto.CustomModelPageRequest;
import com.idss.datalake.datagovern.dataquality.entity.QuaInternalModel;
import com.idss.datalake.datagovern.dataquality.mapper.QuaCustomizeModelMapper;
import com.idss.datalake.datagovern.dataquality.service.IQuaCustomizeModelService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.radar.util.UmsUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 数据质量自定义模版 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-13
 */
@Service
public class QuaCustomizeModelServiceImpl implements IQuaCustomizeModelService {
    @Resource
    private QuaCustomizeModelMapper quaCustomizeModelMapper;
    @Override
    public BasePageResponse<List<QuaInternalModel>> page(CustomModelPageRequest requestDto) {
        List<QuaInternalModel> list = new ArrayList<>();
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<QuaInternalModel> page = quaCustomizeModelMapper.page(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
    }
}
