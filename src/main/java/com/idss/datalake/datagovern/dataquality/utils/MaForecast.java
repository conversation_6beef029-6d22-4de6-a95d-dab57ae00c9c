package com.idss.datalake.datagovern.dataquality.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ResourceUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.util.Set;

@Slf4j
public class MaForecast {
    public static final String MA = "MA";
    public static final String ARIMA = "ARIMA";
    public static final String EWMA = "EWMA";


    /**
     * @param pythonPath python 运行路径
     * @param type       预测类型
     * @param json       {"date":["2020-01-01","2020-01-02"],"data":[100,101]}
     * @return
     * @throws Exception
     */
    public static Long forecast(String pythonPath, String type, JSONObject json) throws Exception {
//        String pythonPath = "/Users/<USER>/anaconda3/envs/trade/bin/python";
        log.info("开始调用python脚本预测,类型:{},参数:{}",type,json.toJSONString());
        String[] cmd = null;
        if (MA.equals(type)) {
            cmd = new String[]{
                    pythonPath,
                    ResourceUtils.getFile("classpath:python/ma_forecast.py").getAbsolutePath(),
                    "--window", "1",
                    "--steps", "1"
            };
        } else if (ARIMA.equals(type)) {
            cmd = new String[]{
                    pythonPath,
                    ResourceUtils.getFile("classpath:python/arima_forecast.py").getAbsolutePath(),
                    "--p", "1",
                    "--d", "1",
                    "--q", "1",
                    "--steps", "1"
            };
        } else if (EWMA.equals(type)) {
            cmd = new String[]{
                    pythonPath,
                    ResourceUtils.getFile("classpath:python/ewma_forecast.py").getAbsolutePath(),
                    "--alpha", "0.3",
                    "--steps", "1"
            };
        } else {
            throw new RuntimeException("类型错误");
        }
        // 创建进程
        ProcessBuilder pb = new ProcessBuilder(cmd);
        Process process = pb.start();

        // 将JSON数据写入Python脚本的标准输入
        OutputStreamWriter writer = new OutputStreamWriter(process.getOutputStream());
        writer.write(json.toJSONString());
        writer.close();

        // 读取Python脚本的输出
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        StringBuilder output = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            output.append(line);
        }

        // 捕获错误输出
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        StringBuilder errorOutput = new StringBuilder();
        String errorLine;
        while ((errorLine = errorReader.readLine()) != null) {
            errorOutput.append(errorLine);
        }

        // 等待进程结束
        int exitCode = process.waitFor();
        if (exitCode == 0) {
            // 解析输出的JSON格式预测值
            String jsonOutput = output.toString();
            log.info("{} 预测值: {}", type, jsonOutput);
            JSONObject resultObject = JSONObject.parseObject(jsonOutput);
            JSONObject forecast = resultObject.getJSONObject("forecast");
            Set<String> keySet = forecast.keySet();
            String next = keySet.iterator().next();
            return forecast.getLong(next);
        } else {
            log.error("Error: Python script execution failed with exit code " + exitCode);
            log.error("Error Output: " + errorOutput);
            throw new RuntimeException("python 脚本执行错误！");
        }
    }

}
