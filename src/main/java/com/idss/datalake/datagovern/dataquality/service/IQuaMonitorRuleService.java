package com.idss.datalake.datagovern.dataquality.service;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRule;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleTemplate;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleType;
import com.idss.datalake.datagovern.dataquality.model.*;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 质量监测规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
public interface IQuaMonitorRuleService extends IService<QuaMonitorRule> {

    /**
     * 分页查询列信息
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<RuleResponseVo>> queryColumnPage(RuleRequestDto requestDto);

    /**
     * 新增/编辑列的规则
     * @param qualityRule
     * @throws Exception
     */
    void saveOrEditColumn(QualityRule qualityRule) throws Exception;

    /**
     * 查询列规则明细
     * @param id
     * @return
     * @throws Exception
     */
    QualityRule columnDetail(Long id) throws Exception;

    /**
     * 删除规则
     * @param requestDto
     */
    void delete(RuleRequestDto requestDto) throws Exception;

    /**
     * 查看所有规则类型
     * @return
     */
    List<GroupSelectVo> queryAllRuleType();

    SelfDefineRule querySelfDefine(RuleRequestDto requestDto) throws Exception;

    /**
     * 获取数据标准-数据字典
     * @return
     * @throws Exception
     */
    List<SelectVo> queryAllDictionary() throws Exception;

    /**
     * 获取数据标准
     * @return
     */
    List<GroupSelectVo> queryAllDataStandard(RuleRequestDto ruleRequestDto);

    /**
     * 获取动态阈值
     * @return
     */
    List<SelectVo> queryAllThreshold(RuleRequestDto ruleRequestDto);

    /**
     * 查询规则个性化配置
     * @param typeCode
     * @return
     */
    List<QuaMonitorRuleType> queryRuleTypeExt(String typeCode);

    /**
     * 查询表
     * @param requestDto
     * @return
     */
    List<SelectVo> queryTables(RuleRequestDto requestDto);

    /**
     * 查询字段
     * @param requestDto
     * @return
     */
    List<SelectVo> queryColumns(RuleRequestDto requestDto);

    /**
     * 查询时间字段
     * @param requestDto
     * @return
     */
    List<SelectVo> queryDateColumns(RuleRequestDto requestDto);

    /**
     * 查询数值字段
     * @param requestDto
     * @return
     */
    List<SelectVo> queryNumericColumns(RuleRequestDto requestDto);

    /**
     * 查询问题级别
     * @return
     */
    List<SelectVo> queryRuleLevel();

    /**
     * 检查范围-类型
     * @return
     */
    List<SelectVo> queryNormType();

    /**
     * 标准检查-类型
     * @return
     */
    List<SelectVo> queryStandard();

    /**
     * 标准检查-类型
     * @return
     */
    List<SelectVo> queryStandardType();

    /**
     * 自定义SQL-操作符
     * @return
     */
    List<SelectVo> queryOperator();

    List<RuleResponseVo> queryRule(RuleRequestDto requestDto) throws Exception;

    ResultBean fileUpload(MultipartFile file, String name);

    String importRule(String filePath) throws Exception;

    String testExec(Long ruleId) throws Exception;

    RuleResponseVo execRecord(Long ruleId) throws Exception;
}
