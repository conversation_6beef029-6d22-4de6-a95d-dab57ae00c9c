package com.idss.datalake.datagovern.dataquality.enums;

/**
 * @Author: <PERSON><PERSON>xiaof<PERSON>
 * @Date: 19/6/2021 13:05
 * @Description: 规则问题级别
 */
public enum RuleLevelEnum {
    severity("severity", "严重"),
    important("important", "重要"),
    general("general", "一般");

    RuleLevelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    private String code;

    private String desc;

    public static String getLevelName(String code) {
        for (RuleLevelEnum value : RuleLevelEnum.values()) {
            if(value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
