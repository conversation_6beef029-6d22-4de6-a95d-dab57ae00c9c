/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/16 16:18
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.syslog.annotation.SysLog;
import com.idss.datalake.common.syslog.entity.OptModule;
import com.idss.datalake.common.syslog.entity.OptType;
import com.idss.datalake.common.util.ExcelUtil;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleTemplate;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleType;
import com.idss.datalake.datagovern.dataquality.enums.OperatorEnum;
import com.idss.datalake.datagovern.dataquality.enums.RuleLevelEnum;
import com.idss.datalake.datagovern.dataquality.enums.SqlTemplateColumn;
import com.idss.datalake.datagovern.dataquality.model.*;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorModelService;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorRuleService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Copyright 2022 IDSS
 * <p> 质量监测规则控制器
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/16 16:18
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/dataQuality/rule")
public class RuleController {

    private static final Logger LOGGER = LoggerFactory.getLogger(RuleController.class);

    @Value("${file.uploadPath}")
    private String filePath;

    @Autowired
    private IQuaMonitorRuleService monitorRuleService;

    @Autowired
    private IQuaMonitorModelService monitorModelService;

    /**
     * 分页查询字段
     *
     * @param ruleRequestDto
     * @return
     */
    @PostMapping("/column/page")
    public BasePageResponse<List<RuleResponseVo>> page(@RequestBody RuleRequestDto ruleRequestDto) {
        try {
            return monitorRuleService.queryColumnPage(ruleRequestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 新增/编辑字段
     *
     * @param qualityRule
     * @return
     */
    @SysLog(logName = "新增/编辑规则模板", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_RULE, switchRedisStatus = true)
    @PostMapping("/column/saveOrEdit")
    public ResultBean saveOrEdit(@RequestBody QualityRule qualityRule) {
        try {
            monitorRuleService.saveOrEditColumn(qualityRule);
            return ResultBean.success("操作成功");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail("操作失败");
        }
    }

    /**
     * 查看字段详情
     *
     * @param id
     * @return
     */
    @SysLog(logName = "查询规则模板明细", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_RULE, switchRedisStatus = true)
    @GetMapping("/column/detail/{id}")
    public ResultBean columnDetail(@PathVariable Long id) {
        try {
            QualityRule qualityRule = monitorRuleService.columnDetail(id);
            return ResultBean.success(qualityRule, "查询成功");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 删除规则
     *
     * @param requestDto
     * @return
     */
    @SysLog(logName = "删除规则模板", optType = OptType.DELETE, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_RULE, switchRedisStatus = true)
    @PostMapping("/column/delete")
    public ResultBean delete(@RequestBody RuleRequestDto requestDto) {
        try {
            monitorRuleService.delete(requestDto);
            return ResultBean.success("删除成功");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail("删除失败");
        }
    }

    /**
     * 查询所有的规则类型
     *
     * @return
     */
    @GetMapping("/ruleType")
    public ResultBean ruleType() {
        try {
            List<GroupSelectVo> vos = monitorRuleService.queryAllRuleType();
            return ResultBean.success(vos, "查询成功");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 根据自定义模板获取模板信息
     *
     * @return
     */
    @PostMapping("/selfDefine")
    public ResultBean selfDefine(@RequestBody RuleRequestDto requestDto) {
        try {
            SelfDefineRule selfDefineRule = monitorRuleService.querySelfDefine(requestDto);
            return ResultBean.success(selfDefineRule, "查询成功");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 预览SQL
     *
     * @return
     */
    @PostMapping("/selfDefine/preview")
    public ResultBean preview(@RequestBody SqlPreview sqlPreview) {
        try {
            String sql = sqlPreview.getSql();
            sql = sql.replace("$table", sqlPreview.getTableName());
            sql = sql.replace("$column", sqlPreview.getColumnName());
            sql = sql.replace("$param1", sqlPreview.getValue1());
            sql = sql.replace("$param2", sqlPreview.getValue2());
            return ResultBean.success(sql, "操作成功");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail("操作失败");
        }
    }

    /**
     * 查询所有的规则类型
     *
     * @return
     */
    @GetMapping("/dictionary")
    public ResultBean dictionary() {
        try {
            List<SelectVo> vos = monitorRuleService.queryAllDictionary();
            return ResultBean.success(vos, "查询成功");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 查询所有的规则类型
     *
     * @return
     */
    @PostMapping("/dataStandard")
    public ResultBean dataStandard(@RequestBody RuleRequestDto ruleRequestDto) {
        try {
            List<GroupSelectVo> vos = monitorRuleService.queryAllDataStandard(ruleRequestDto);
            return ResultBean.success(vos, "查询成功");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 查询所有的规则类型
     *
     * @return
     */
    @PostMapping("/dyncThreshold")
    public ResultBean dyncThreshold(@RequestBody RuleRequestDto ruleRequestDto) {
        try {
            List<SelectVo> vos = monitorRuleService.queryAllThreshold(ruleRequestDto);
            return ResultBean.success(vos, "查询成功");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 查询规则类型的个性化配置
     *
     * @return
     */
    @PostMapping("/ruleType/ext")
    public ResultBean ruleTypeExt(@RequestBody RuleRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            List<QuaMonitorRuleType> ruleTypes = monitorRuleService.queryRuleTypeExt(requestDto.getTypeCode());
            resultBean.setContent(ruleTypes);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 查询规则表
     *
     * @return
     */
    @PostMapping("/table")
    public ResultBean ruleTypeTable(@RequestBody RuleRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            List<SelectVo> vos = monitorRuleService.queryTables(requestDto);
            resultBean.setContent(vos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 查询规则表相关的字段
     *
     * @return
     */
    @PostMapping("/column")
    public ResultBean ruleTypeColumn(@RequestBody RuleRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            List<SelectVo> vos = monitorRuleService.queryColumns(requestDto);
            resultBean.setContent(vos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 查询规则表相关的字段
     *
     * @return
     */
    @PostMapping("/dateColumn")
    public ResultBean ruleTypeDateColumn(@RequestBody RuleRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            List<SelectVo> vos = monitorRuleService.queryDateColumns(requestDto);
            resultBean.setContent(vos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 查询规则表相关的字段
     *
     * @return
     */
    @PostMapping("/numericColumn")
    public ResultBean ruleTypeNumericColumn(@RequestBody RuleRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            List<SelectVo> vos = monitorRuleService.queryNumericColumns(requestDto);
            resultBean.setContent(vos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 查询规则问题级别
     *
     * @return
     */
    @GetMapping("/ruleLevel")
    public ResultBean ruleLevel() {
        ResultBean resultBean = new ResultBean();
        try {
            List<SelectVo> vos = monitorRuleService.queryRuleLevel();
            resultBean.setContent(vos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 查询规则问题级别
     *
     * @return
     */
    @PostMapping("/normType")
    public ResultBean normType() {
        ResultBean resultBean = new ResultBean();
        try {
            List<SelectVo> vos = monitorRuleService.queryNormType();
            resultBean.setContent(vos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 选择规范
     *
     * @return
     */
    @PostMapping("/standard")
    public ResultBean standard() {
        ResultBean resultBean = new ResultBean();
        try {
            List<SelectVo> vos = monitorRuleService.queryStandard();
            resultBean.setContent(vos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 检查类型
     *
     * @return
     */
    @PostMapping("/standardType")
    public ResultBean standardType() {
        ResultBean resultBean = new ResultBean();
        try {
            List<SelectVo> vos = monitorRuleService.queryStandardType();
            resultBean.setContent(vos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 操作符
     *
     * @return
     */
    @GetMapping("/operator")
    public ResultBean operator() {
        ResultBean resultBean = new ResultBean();
        try {
            List<SelectVo> vos = monitorRuleService.queryOperator();
            resultBean.setContent(vos);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    @PostMapping("/export")
    public void export(@RequestBody RuleRequestDto ruleRequestDto, HttpServletRequest request, HttpServletResponse response) {
        try {
            List<RuleResponseVo> results = monitorRuleService.queryRule(ruleRequestDto);
            List<ExportRule> exportRules = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(results)) {
                for (RuleResponseVo result : results) {
                    ExportRule exportRule = new ExportRule();
                    exportRule.setRuleCode(result.getRuleCode());
                    exportRule.setRuleName(result.getRuleName());
                    exportRule.setDatasourceName(result.getModelName());
                    exportRule.setTableName(result.getTableName());
                    exportRule.setColumnName(result.getColumnName());
                    exportRule.setSql(result.getRuleDetail().getSql());
                    exportRule.setCompareType(OperatorEnum.getNameByCode(result.getRuleDetail().getOperator()));
                    exportRule.setCompareValue(result.getRuleDetail().getCompareValue());

                    exportRules.add(exportRule);
                }
            }
            long timestamp = System.currentTimeMillis();
            String dirPath = filePath + File.separator + "rule" + File.separator + timestamp;
            File dirFile = new File(dirPath);
            if (!dirFile.exists()) {
                dirFile.mkdirs();
            }
            String fileName = dirPath + File.separator + "自定义SQL模型.xlsx";
            EasyExcel.write(fileName, ExportRule.class).excludeColumnFiledNames(Collections.singleton("sampleData")).sheet("data").doWrite(exportRules);

            File downloadFile = new File(fileName);
            try (OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
                 InputStream fis = new FileInputStream(downloadFile)) {
                 byte[] bytes = new byte[1024];
                 int len = 0;
                 response.setContentType("application/octet-stream;charset=utf-8");
                 // 如果输出的是中文名的文件，在此处就要用URLEncoder.encode方法进行处理
                 response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("自定义SQL模型.xlsx", "UTF-8"));
                 while ((len = fis.read(bytes)) > 0) {
                     toClient.write(bytes, 0, len);
                 }
                 toClient.flush();
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            } finally {
                FileUtils.deleteDirectory(dirFile);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @ApiOperation(value = "下载模板", httpMethod = "GET")
    @GetMapping("/downloadTemplate")
    @ResponseBody
    public void downloadTemp(HttpServletResponse response, HttpServletRequest request) {
        LOGGER.info("正在进行模板下载");
        List<String> head = new ArrayList<String>();
        // 获取数据源信息
        List<Map<String, Object>> datasources = monitorModelService.queryAllModel();
        String[] downRows = null;
        String[] redRows = null;
        List<String[]> downData = null;
        for (SqlTemplateColumn column : SqlTemplateColumn.values()) {
            head.add(column.getLabel());
        }
        downRows = new String[]{"2", "6", "8"};
        redRows = new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8"};
        downData = new ArrayList<>();

        //数据源
        String[] datasourceArr = new String[datasources.size()];
        if (CollectionUtils.isNotEmpty(datasources)) {
            for (int i = 0; i < datasources.size(); i++) {
                datasourceArr[i] = (String) datasources.get(i).get("value");
            }
        }
        downData.add(datasourceArr);

        // 比较方式
        String[] compareArr = new String[OperatorEnum.values().length];
        OperatorEnum[] operatorEnums = OperatorEnum.values();
        for (int i = 0; i < operatorEnums.length; i++) {
            compareArr[i] = operatorEnums[i].getDesc();
        }
        downData.add(compareArr);

        // 问题级别
        String[] levelArr = new String[RuleLevelEnum.values().length];
        RuleLevelEnum[] ruleLevelEnums = RuleLevelEnum.values();
        for (int i = 0; i < ruleLevelEnums.length; i++) {
            levelArr[i] = ruleLevelEnums[i].getDesc();
        }
        downData.add(levelArr);
        try {
            String fileName = "template.xls";
            String[] header = new String[head.size()];
            for (int i = 0; i < head.size(); i++) {
                header[i] = head.get(i);
            }

            ExcelUtil.getExcelTemplate(fileName, header, downData, downRows, redRows, request, response, "SQL");
        } catch (Exception e) {
            LOGGER.error("下载模板失败:", e);
        }
    }

    @ApiOperation(value = "上传导入文件")
    @PostMapping("/upload")
    public ResultBean upload(@RequestParam(value = "file", required = false) MultipartFile file) {
        ResultBean resultBean = monitorRuleService.fileUpload(file, null);
        return resultBean;
    }

    @ApiOperation(value = "模型导入")
    @PostMapping("/import")
    public ResultBean importRule(@RequestBody Map<String, Object> params) {
        try {
            String filePath = (String) params.get("filePath");
            String msg = monitorRuleService.importRule(filePath);
            LOGGER.info("资源导入成功{}", msg);
            return ResultBean.success(msg,"成功");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail("资源导入失败");
        }
    }

    /**
     * 查询规则详情
     *
     * @param id
     * @return
     */
    @GetMapping("/testExec/{id}")
    public ResultBean testExec(@PathVariable Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            String execResult = monitorRuleService.testExec(id);
            if("0".equals(execResult) || "1".equals(execResult)) {
                resultBean.setContent("试跑成功,请在详情中查看试跑结果");
            } else {
                resultBean.setContent("试跑失败,请重试");
            }
            resultBean.setContent(execResult);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("试跑失败");
        }
        return resultBean;
    }

    /**
     * 查询规则详情
     *
     * @param id
     * @return
     */
    @GetMapping("/execRecord/{id}")
    public ResultBean execRecord(@PathVariable Long id) {
        ResultBean resultBean = new ResultBean();
        try {
            RuleResponseVo ruleResponseVo = monitorRuleService.execRecord(id);
            resultBean.setContent(ruleResponseVo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }
}
