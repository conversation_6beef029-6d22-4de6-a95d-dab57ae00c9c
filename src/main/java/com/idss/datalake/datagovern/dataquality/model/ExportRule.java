/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2023/4/6 18:08
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * Copyright 2023 IDSS
 * <p> 导出模型字段信息
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2023/4/6 18:08
 */
@Data
public class ExportRule {

    @ExcelProperty("模型名称")
    private String ruleName;

    @ExcelProperty("模型代码")
    private String ruleCode;

    @ExcelProperty("数据源名称")
    private String datasourceName;

    @ExcelProperty("选择表")
    private String tableName;

    @ExcelProperty("检查字段")
    private String columnName;

    @ExcelProperty("自定义sql")
    private String sql;

    @ExcelProperty("比较方式")
    private String compareType;

    @ExcelProperty("比较值")
    private String compareValue;

    @ExcelProperty("问题级别")
    private String ruleLevel;
}
