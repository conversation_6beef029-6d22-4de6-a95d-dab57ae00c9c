package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据质量-模型管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QuaMonitorRuleTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模型名称
     */
    private String templateName;

    /**
     * 数据源ID
     */
    private Long modelId;

    /**
     * 表名/索引名
     */
    private String tableName;

    /**
     * 时间字段名
     */
    private String columnName;

    /**
     * 取数日期: 0昨天，1上周，2上月
     */
    private String dataTime;
    /**
     * 时间范围开始点
     */
    private String timeRangeStart;
    /**
     * 时间范围结束点
     */
    private String timeRangeEnd;

    /**
     * 删除标识 0-未删除 1-已删除
     */
    private String deleteFlag;

    /**
     * 筛选数据条数  >1 条
     */
    private String dataFilter;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 租户ID
     */
    private Long tenantId;

    @TableField(exist = false)
    private String virtualId;

    /**
     * 是否基于时间字段过滤，0-是，1-否
     */
    private Integer filterByDatetime;
    /**
     * 时间转换函数
     */
    private String datetimeFunction;
}
