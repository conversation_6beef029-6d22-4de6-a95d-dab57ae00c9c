package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 质量监测结果明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuaMonitorResultDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    private Long jobId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 字段名
     */
    private String columnName;

    /**
     * 触发规则
     */
    private String ruleType;

    /**
     * 规则权重
     */
    private String ruleWeight;

    /**
     * 问题级别
     */
    private String ruleLevel;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    private Long tenantId;

    private Integer delFlag;

    /**
     * 执行状态: 0正常，1异常
     */
    private Integer executeStatus;
}
