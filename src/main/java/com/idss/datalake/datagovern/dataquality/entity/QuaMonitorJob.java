package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import com.idss.datalake.datagovern.dataquality.model.JobCron;
import com.idss.datalake.datagovern.dataquality.model.JobRule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p>
 * 质量监测JOB
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuaMonitorJob implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long modelId;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 01-人工执行；02-自动执行；03:定时执行;04:循环执行
     */
    private String executeAuto;

    /**
     * 01:一次性任务;02:周期任务
     */
    private String executeCycle;

    /**
     * 执行周期描述
     */
    private String executeCycleDesc;

    /**
     * 执行方式:day,week,month,year,cron
     */
    private String executeType;

    /**
     * 执行配置，json格式
     */
    private String executeConfig;

    /**
     * quartz表达式
     */
    private String executeCron;

    /**
     * 定时执行指定时间
     */
    private LocalDateTime defineTime;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 起始时间
     */
    private LocalDateTime validTime;

    /**
     * 截止时间
     */
    private LocalDateTime invalidTime;

    /**
     * 本次执行时间
     */
    private LocalDateTime executeCurrentTime;

    /**
     * 下次执行时间
     */
    private LocalDateTime executeNextTime;

    /**
     * 任务描述
     */
    private String jobDesc;

    /**
     * 执行状态
     */
    private Integer executeStatus;

    /**
     * 执行结果说明
     */
    private String executeResult;

    /**
     * 核查结果说明
     */
    private String auditRemark;

    /**
     * 规则 多个规则使用逗号分隔
     */
    private String jobRules;

    /**
     * 规则权重 权重和规则一一对应
     */
    private String ruleWeight;

    /**
     * 样例数据条数
     */
    private Integer sampleCnt;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 创建者
     */
    private String createUser;

    /**
     * 创建日期
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateUser;

    /**
     * 更新日期
     */
    private LocalDateTime updateTime;

    /**
     * 有效标识:1-有效；0-无效
     */
    private String flag;

    private Integer status;

    /**
     * 01-弱规则执行  02-强规则执行
     */
    private String executeRuleType;

    private Long flowId;

    @TableField(exist = false)
    private List<JobRule> rules;

    @TableField(exist = false)
    private JobCron cronTab;
}
