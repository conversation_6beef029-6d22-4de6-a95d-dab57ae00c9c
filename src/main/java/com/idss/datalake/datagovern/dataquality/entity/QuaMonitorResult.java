package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 质量监测结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuaMonitorResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    private Long jobId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 资产ID
     */
    private String elementId;

    /**
     * 监测分数
     */
    private String monitorScore;

    /**
     * 级别数量统计{level:cnt}
     */
    private String levelCnt;

    /**
     * 执行成功匹配规则
     */
    private String successMatchRules;

    /**
     * 执行成功不匹配规则
     */
    private String successNotMatchRules;

    /**
     * 执行失败规则 多个规则使用逗号分隔
     */
    private String failRules;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 是否新增
     */
    private String isNew;

    private Integer delFlag;
}
