/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/16 16:21
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.model;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import lombok.Data;

import java.util.List;

/**
 * Copyright 2022 IDSS
 * <p> 监测申诉查询条件
 * All right reserved.
 */
@Data
public class AppealDto extends BasePageRequest {
    /**
     * 是否处理者查询
     */
    private Boolean isHandler;
    /**
     * 字段名
     */
    private String columnName;
    /**
     * 处理状态
     */
    private List<Integer> handleStatus;

    private String appealUser;
}
