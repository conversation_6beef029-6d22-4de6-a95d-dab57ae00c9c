package com.idss.datalake.datagovern.dataquality.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.util.CronUtil;
import com.idss.datalake.common.util.DateUtils;
import com.idss.datalake.common.util.QuickTime;
import com.idss.datalake.datagovern.dataquality.entity.*;
import com.idss.datalake.datagovern.dataquality.enums.RuleLevelEnum;
import com.idss.datalake.datagovern.dataquality.enums.RuleTypeEnum;
import com.idss.datalake.datagovern.dataquality.enums.StandardTypeEnum;
import com.idss.datalake.datagovern.dataquality.enums.TaskStatusEnum;
import com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorJobMapper;
import com.idss.datalake.datagovern.dataquality.model.*;
import com.idss.datalake.datagovern.dataquality.service.*;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.quartz.entity.BaseTask;
import com.idss.datalake.datagovern.metadata.model.quartz.job.QualityMonitorJob;
import com.idss.datalake.datagovern.metadata.model.quartz.service.QuartzJobService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import io.swagger.models.auth.In;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <p>
 * 质量监测JOB 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Service
public class QuaMonitorJobServiceImpl extends ServiceImpl<QuaMonitorJobMapper, QuaMonitorJob> implements IQuaMonitorJobService {

    @Autowired
    private QuaMonitorJobMapper monitorJobMapper;

    @Autowired
    private IQuaMonitorTaskService taskService;

    @Autowired
    private IQuaMonitorRuleService ruleService;

    @Autowired
    private IQuaMonitorJobService jobService;

    @Autowired
    private QuartzJobService quartzJobService;

    @Autowired
    private IQuaMonitorResultService resultService;

    @Autowired
    private QuaWabElementService elementService;

    @Autowired
    private IQuaMonitorModelService monitorModelService;

    @Autowired
    private IQuaMonitorRuleTemplateService ruleTemplateService;


    private static final String[] scoreDetailHeaders = {"表名", "字段名", "模板名称", "阈值", "稽核值", "是否异常", "稽核时间","执行状态"};


    @Override
    public BasePageResponse<List<JobResponseVo>> queryJobPage(JobRequestDto requestDto) {
        List<JobResponseVo> list = new ArrayList<>();
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<JobResponseVo> page = monitorJobMapper.queryJobPage(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        } else {
            List<JobResponseVo> jobResponseVos = page.getResult();
            for (JobResponseVo jobResponseVo : jobResponseVos) {
                String[] jobRules = jobResponseVo.getJobRules().split(",");
                jobResponseVo.setRuleCnt(jobRules.length);
            }
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), jobResponseVos);
        }
    }

    @Override
    public QuaMonitorJob queryJobDetail(String id) {
        QuaMonitorJob job = getById(id);
        String[] jobRules = job.getJobRules().split(",");
        List<QuaMonitorRule> monitorRules = ruleService.list(new QueryWrapper<QuaMonitorRule>()
                .in("id", jobRules));
        Map<String, String> ruleMap = new HashMap<>();
        for (QuaMonitorRule monitorRule : monitorRules) {
            ruleMap.put(String.valueOf(monitorRule.getId()), monitorRule.getRuleName());
        }
        String[] ruleWeights = job.getRuleWeight().split(",");

        List<JobRule> rules = new ArrayList<>();
        for (int i = 0; i < jobRules.length; i++) {
            JobRule jobRule = new JobRule();
            String rule = jobRules[i];
            String weight = ruleWeights[i];
            jobRule.setId(Long.valueOf(rule));
            jobRule.setRuleName(ruleMap.get(rule));
            jobRule.setWeight(Double.valueOf(weight));

            rules.add(jobRule);
        }
        job.setRules(rules);
        JobCron jobCron = new JobCron();
        if (Constant.JOB_CYCLE.equals(job.getExecuteCycle())) {
            jobCron.setExecuteCron(job.getExecuteCron());
            jobCron.setExecuteType(job.getExecuteType());
            jobCron.setExecuteConfig(JSON.parseObject(job.getExecuteConfig(), new TypeReference<JobCronConfig>() {
            }));
        }
        job.setCronTab(jobCron);
        return job;
    }

    @Override
    public List<QuaMonitorRule> queryRules(JobRequestDto requestDto) {
        /*List<QuaMonitorRule> rules = ruleService.list(new QueryWrapper<QuaMonitorRule>()
                .eq("model_id", modelId)
                .eq("flag", "1"));*/
        List<QuaMonitorRule> rules = new ArrayList<>();
        List<QuaMonitorRuleTemplate> templates = ruleTemplateService.list(new LambdaQueryWrapper<QuaMonitorRuleTemplate>()
                .eq(QuaMonitorRuleTemplate::getModelId, requestDto.getModelId())
                .eq(QuaMonitorRuleTemplate::getDeleteFlag, "0"));

        LambdaQueryWrapper<QuaMonitorJob> jobQuery = new LambdaQueryWrapper<>();
        jobQuery.eq(QuaMonitorJob::getFlag, "1");
        if(requestDto.getJobId() != null) {
            jobQuery.ne(QuaMonitorJob::getId, requestDto.getJobId());
        }
        List<QuaMonitorJob> jobs = jobService.list(jobQuery);
        // 质量模型不能被任务重复使用
        List<String> existRules = jobs.stream().map(data -> data.getJobRules()).collect(Collectors.toList());
        for (QuaMonitorRuleTemplate template : templates) {
            if(!existRules.contains(String.valueOf(template.getId()))) {
                QuaMonitorRule rule = new QuaMonitorRule();
                rule.setId(template.getId());
                rule.setRuleName(template.getTemplateName() + "(" + template.getTableName() + ")");
                rule.setTableName(template.getTableName());

                rules.add(rule);
            }
        }
        return rules;
    }

    @Override
    public void addOrUpdate(JobRequestDto requestDto) throws Exception {
        UserValueObject uvo = UmsUtils.getUVO();
        try {
            Long tenantId = Long.valueOf(uvo.getTenantId());
            if (requestDto.getId() == null) {
                // 检查任务是否已存在
                List<QuaMonitorJob> existJobs = this.list(new QueryWrapper<QuaMonitorJob>()
                        .eq("name", requestDto.getName()).eq("tenant_id", tenantId).eq("flag", "1"));
                if (CollectionUtils.isNotEmpty(existJobs)) {
                    throw new RuntimeException("任务名称已存在");
                }

                // 新增任务
                JobCron jobCron = requestDto.getCronTab();
                QuaMonitorJob monitorJob = new QuaMonitorJob();
                monitorJob.setName(requestDto.getName());
                monitorJob.setJobDesc(requestDto.getJobDesc());
                monitorJob.setExecuteCycle(requestDto.getExecuteCycle());
                monitorJob.setExecuteRuleType(requestDto.getExecuteRuleType());
                monitorJob.setFlowId(requestDto.getFlowId());
                monitorJob.setExecuteType(jobCron == null ? "" : jobCron.getExecuteType());
                monitorJob.setExecuteConfig(jobCron == null ? "" : JSON.toJSONString(jobCron.getExecuteConfig()));
                monitorJob.setModelId(requestDto.getModelId());
                monitorJob.setStatus(TaskStatusEnum.RUNNING.getStatus());
                if (Constant.JOB_ONCE.equals(requestDto.getExecuteCycle())) {
                    monitorJob.setExecuteCron("");
                } else {
                    if ("cron".equals(monitorJob.getExecuteType())) {
                        monitorJob.setExecuteCron(jobCron == null ? "" : jobCron.getExecuteCron());
                    } else {
                        CronUtil.CronDTO cronDTO = CronUtil.transCron(jobCron == null ? "" : jobCron.getExecuteType(), monitorJob.getExecuteConfig());
                        monitorJob.setExecuteCron(cronDTO.getCron());
                    }
                }
                List<JobRule> jobRules = requestDto.getRules();
                List<String> ruleIds = jobRules.stream().map(rule -> {
                    return String.valueOf(rule.getId());
                }).collect(Collectors.toList());
                List<String> ruleWeights = jobRules.stream().map(rule -> {
                    return String.valueOf(rule.getWeight());
                }).collect(Collectors.toList());
                monitorJob.setJobRules(StringUtils.join(ruleIds, ","));
                monitorJob.setRuleWeight(StringUtils.join(ruleWeights, ","));
                monitorJob.setSampleCnt(requestDto.getSampleCnt());
                monitorJob.setTenantId(tenantId);
                monitorJob.setCreateUser(uvo.getUserName());
                save(monitorJob);

                //启动JOB
                if (Constant.JOB_CYCLE.equals(requestDto.getExecuteCycle())) {
                    // 定时任务
                    Map<String, Object> elementMap = new HashMap<>();
                    elementMap.put("job", monitorJob);
                    JobKey jobKey = JobKey.jobKey(monitorJob.getName(), String.valueOf(monitorJob.getId()));
                    BaseTask task = new BaseTask(jobKey, monitorJob.getName(), monitorJob.getExecuteCron(), elementMap, QualityMonitorJob.class);
                    quartzJobService.scheduleJob(task);
                } else {
                    // 立即执行
                    saveTask(monitorJob);
                }

            } else {
                // 检查任务是否已存在
                List<QuaMonitorJob> existJobs = this.list(new QueryWrapper<QuaMonitorJob>()
                        .eq("name", requestDto.getName()).eq("tenant_id", tenantId).eq("flag", "1").ne("id", requestDto.getId()));
                if (CollectionUtils.isNotEmpty(existJobs)) {
                    throw new RuntimeException("任务名称已存在");
                }

                // 编辑任务
                JobCron jobCron = requestDto.getCronTab();
                QuaMonitorJob monitorJob = getById(requestDto.getId());
                String preTaskType = monitorJob.getExecuteType();
                String preTaskName = monitorJob.getName();
                monitorJob.setName(requestDto.getName());
                monitorJob.setJobDesc(requestDto.getJobDesc());
                monitorJob.setModelId(requestDto.getModelId());
                monitorJob.setExecuteCycle(requestDto.getExecuteCycle());
                monitorJob.setExecuteRuleType(requestDto.getExecuteRuleType());
                monitorJob.setFlowId(requestDto.getFlowId());
                monitorJob.setExecuteType(jobCron == null ? "" : jobCron.getExecuteType());
                monitorJob.setExecuteConfig(JSON.toJSONString(jobCron == null ? "" : jobCron.getExecuteConfig()));
                if (Constant.JOB_ONCE.equals(requestDto.getExecuteCycle())) {
                    monitorJob.setExecuteCron("");
                } else {
                    if ("cron".equals(monitorJob.getExecuteType())) {
                        monitorJob.setExecuteCron(jobCron == null ? "" : jobCron.getExecuteCron());
                    } else {
                        CronUtil.CronDTO cronDTO = CronUtil.transCron(jobCron == null ? "" : jobCron.getExecuteType(), monitorJob.getExecuteConfig());
                        monitorJob.setExecuteCron(cronDTO.getCron());
                    }
                }
                List<JobRule> jobRules = requestDto.getRules();
                List<String> ruleIds = jobRules.stream().map(rule -> {
                    return String.valueOf(rule.getId());
                }).collect(Collectors.toList());
                List<String> ruleWeights = jobRules.stream().map(rule -> {
                    return String.valueOf(rule.getWeight());
                }).collect(Collectors.toList());
                monitorJob.setJobRules(StringUtils.join(ruleIds, ","));
                monitorJob.setRuleWeight(StringUtils.join(ruleWeights, ","));
                monitorJob.setSampleCnt(requestDto.getSampleCnt());
                monitorJob.setUpdateUser(uvo.getUserName());
                this.updateById(monitorJob);

                //重新设置Quartz触发，只修改CRON表达式
                if (Constant.JOB_CYCLE.equals(requestDto.getExecuteCycle())) {
                    Map<String, Object> elementMap = new HashMap<>();
                    elementMap.put("job", monitorJob);
                    BaseTask task = new BaseTask(JobKey.jobKey(monitorJob.getName(), String.valueOf(monitorJob.getId())), monitorJob.getName(),
                            monitorJob.getExecuteCron(), elementMap, QualityMonitorJob.class);
                    if (Constant.JOB_ONCE.equals(preTaskType)) {
                        // 如果是由立即执行改为定时执行，则仅需要新增定时任务
                        quartzJobService.scheduleJob(task);
                    } else {
                        // 如果是修改定时任务的执行时间，则需要更新定时任务
                        JobKey preJobKey = JobKey.jobKey(preTaskName, String.valueOf(monitorJob.getId()));
                        quartzJobService.deleteJob(preJobKey);
                        quartzJobService.scheduleJob(task);
                    }
                } else {
                    saveTask(monitorJob);
                    if (Constant.JOB_CYCLE.equals(preTaskType)) {
                        JobKey jobKey = JobKey.jobKey(preTaskName, String.valueOf(monitorJob.getId()));
                        quartzJobService.deleteJob(jobKey);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public void reRun(Long jobId,String taskNo) {
        taskService.update(new UpdateWrapper<QuaMonitorTask>()
                .set("status",TaskStatusEnum.TODO.getStatus())
                .set("start_time",LocalDateTime.now())
                .set("task_result",null)
                .set("fail_reason",null)
                .set("run_process",null)
                .eq("job_id",jobId)
                .eq("task_no",taskNo));
    }

    @Override
    public void pauseOrResumeJon(JobRequestDto requestDto) {
        try {
            QuaMonitorJob job = jobService.getById(requestDto.getId());
            // 非“执行中”任务无法暂停
            if (TaskStatusEnum.RUNNING.getStatus() != job.getStatus() && TaskStatusEnum.PAUSE.getStatus() == requestDto.getStatus()) {
                throw new RuntimeException("非执行中任务无法暂停");
            }
            job.setStatus(requestDto.getStatus());
            jobService.updateById(job);
            if (TaskStatusEnum.RUNNING.getStatus() == requestDto.getStatus()) {
                quartzJobService.resumeJob(JobKey.jobKey(job.getName(), String.valueOf(job.getId())));
            } else if (TaskStatusEnum.PAUSE.getStatus() == requestDto.getStatus()) {
                quartzJobService.pauseJob(JobKey.jobKey(job.getName(), String.valueOf(job.getId())));
            }
        } catch (Exception e) {
            log.error("启动或暂停JOB异常", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, Object> taskList(String jobId, JobRequestDto requestDto) {
        Map<String, Object> result = new HashMap<>();
        QueryWrapper<QuaMonitorTask> queryWrapper = new QueryWrapper<QuaMonitorTask>().eq("job_id", jobId).orderByDesc("start_time");
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<QuaMonitorTask> page =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(requestDto.getPageNum(), requestDto.getPageSize());
        IPage<QuaMonitorTask> pageResult = taskService.page(page, queryWrapper);
        List<QuaMonitorTask> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            for (QuaMonitorTask task : records) {
                task.setTaskStatus(TaskStatusEnum.getStatusName(task.getStatus()));
                if (task.getTaskResult() != null) {
                    if (1 == task.getTaskResult()) {
                        task.setResult("成功");
                    } else if (0 == task.getTaskResult()) {
                        task.setResult("失败");
                    }
                }
            }
        }
        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);
        return result;
    }

    @Override
    public TaskResultResponseVo taskResult(Long taskId) {
        QuaMonitorTask task = taskService.getById(taskId);
        QuaMonitorJob job = jobService.getById(task.getJobId());
        QuaMonitorResult result = resultService.getOne(new QueryWrapper<QuaMonitorResult>()
                .eq("del_flag",0).eq("task_id", taskId));
        QuaMonitorModel model = monitorModelService.getById(task.getModelId());
        QuaWabElement element = elementService.getById(model.getElementId());

        TaskResultResponseVo vo = new TaskResultResponseVo();
        String[] rules = task.getJobRules().split(",");
        vo.setName(job.getName());
        vo.setJobDesc(job.getJobDesc());
        vo.setRuleCnt(rules.length);
        vo.setExecuteCycle(job.getExecuteCycle());
        if (ElementTypeEnum.ES.getCode().equals(element.getElementType())) {
            vo.setIp(element.getEsIpPort());
        } else {
            vo.setIp(element.getChIp());
        }
        vo.setDatabaseName(model.getDatabaseName());
        vo.setCreateUser(job.getCreateUser());
        vo.setCreateTime(DateUtils.localDateTimeToString(task.getStartTime()));
        vo.setStatus(TaskStatusEnum.getStatusName(task.getStatus()));
        vo.setTaskResult(String.valueOf(task.getTaskResult()));
        vo.setMonitorScore(result.getMonitorScore());
        List<TaskResultResponseVo.Pie> pies = new ArrayList<>();
        if (StringUtils.isNotEmpty(result.getLevelCnt())) {
            JSONObject levelJson = JSON.parseObject(result.getLevelCnt());
            for (String key : levelJson.keySet()) {
                TaskResultResponseVo.Pie pie = new TaskResultResponseVo.Pie();
                pie.setName(RuleLevelEnum.getLevelName(key));
                pie.setValue(levelJson.getInteger(key));

                pies.add(pie);
            }
        }
        vo.setPie(pies);
        return vo;
    }

    @Override
    public BasePageResponse<List<TaskResultDetailVo>> taskDetail(JobRequestDto requestDto) {
        List<TaskResultDetailVo> list = new ArrayList<>();
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<TaskResultDetailVo> page = monitorJobMapper.taskDetail(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        } else {
            List<TaskResultDetailVo> vos = page.getResult();
            for (TaskResultDetailVo vo : vos) {
                vo.setRuleLevel(RuleLevelEnum.getLevelName(vo.getRuleLevel()));
            }
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void delete(JobRequestDto requestDto) throws Exception {
        try {
            List<QuaMonitorJob> jobs = list(new QueryWrapper<QuaMonitorJob>()
                    .in("id", requestDto.getIds()));
            for (QuaMonitorJob job : jobs) {
                job.setFlag("0");
                quartzJobService.deleteJob(JobKey.jobKey(job.getName(), String.valueOf(job.getId())));
            }

            updateBatchById(jobs);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public TaskOverview tableInfo(JobRequestDto requestDto) throws Exception {
        QuaMonitorRuleTemplate ruleTemplate = null;
        QuaMonitorModel model = null;
        QuaWabElement element = null;
        String score = "100";
        if(requestDto.getTaskId() != null) {
            QuaMonitorTask task = taskService.getById(requestDto.getTaskId());
            model = monitorModelService.getById(task.getModelId());
            ruleTemplate = ruleTemplateService.getById(Long.valueOf(task.getJobRules()));
            element = elementService.getById(model.getElementId());
            List<QuaMonitorResult> results = resultService.list(new LambdaQueryWrapper<QuaMonitorResult>()
                    .eq(QuaMonitorResult::getDelFlag,0).eq(QuaMonitorResult::getTaskId, requestDto.getTaskId()));
            if(CollectionUtils.isNotEmpty(results)) {
                score = results.get(0).getMonitorScore();
            }
        } else {
            ruleTemplate = ruleTemplateService.getById(Long.valueOf(requestDto.getTemplateId()));
            model = monitorModelService.getById(ruleTemplate.getModelId());
            element = elementService.getById(model.getElementId());
        }

        TaskOverview taskOverview = new TaskOverview();
        taskOverview.setScore(score);
        taskOverview.setTableName(ruleTemplate.getTableName());
        taskOverview.setDatabaseName(model.getDatabaseName());
        taskOverview.setDatasourceType(ElementTypeEnum.getDescByCode(model.getElementType()));
        if(ElementTypeEnum.ES.getCode().equals(model.getElementType())) {
            String ipPort = element.getEsIpPort();
            if(StringUtils.isNotEmpty(ipPort)) {
                /*String ip = ipPort.substring(ipPort.lastIndexOf("/") + 1, ipPort.lastIndexOf(':'));
                String port = ipPort.substring(ipPort.lastIndexOf(":") + 1);*/
                String ip = "";
                if(ipPort.startsWith("http://")) {
                    ip = ipPort.substring(7, ipPort.lastIndexOf(':'));
                } else {
                    ip = ipPort.substring(0, ipPort.lastIndexOf(':'));
                }
                String port = "";
                if(ipPort.endsWith("/")) {
                    port = ipPort.substring(ipPort.lastIndexOf(":") + 1, ipPort.length() - 1);
                } else {
                    port = ipPort.substring(ipPort.lastIndexOf(":") + 1);
                }
                taskOverview.setIp(ip);
                taskOverview.setPort(port);
            }
        } else {
            taskOverview.setIp(element.getChIp());
            taskOverview.setPort(String.valueOf(element.getChPort()));
        }

        // 查看任务
        return taskOverview;
    }

    @Override
    public TaskScoreTrend scoreTrend(JobRequestDto requestDto) throws Exception {
        Long jobId = null;
        if(requestDto.getTaskId() != null) {
            QuaMonitorTask task = taskService.getById(requestDto.getTaskId());
            jobId = task.getJobId();
        } else {
            QuaMonitorRuleTemplate ruleTemplate = ruleTemplateService.getById(Long.valueOf(requestDto.getTemplateId()));
            List<QuaMonitorJob> jobs = jobService.list(new LambdaQueryWrapper<QuaMonitorJob>()
                    .eq(QuaMonitorJob::getJobRules, ruleTemplate.getId()));
            if(CollectionUtils.isNotEmpty(jobs)) {
                jobId = jobs.get(0).getId();
            }
        }

        String quickTime = requestDto.getQuickTime();
        Map<String, String> timeRange = QuickTime.getQuickTime(quickTime, new Date());
        String startTime = timeRange.get("startTime");
        String endTime = timeRange.get("endTime");
        List<QuaMonitorResult> monitorResults = resultService.list(new LambdaQueryWrapper<QuaMonitorResult>()
                .eq(QuaMonitorResult::getDelFlag,0)
                .eq(QuaMonitorResult::getJobId, jobId)
                .ge(QuaMonitorResult::getCreateTime, startTime)
                .le(QuaMonitorResult::getCreateTime, endTime)
                .orderByAsc(QuaMonitorResult::getCreateTime));
        TaskScoreTrend scoreTrend = new TaskScoreTrend();
        List<String> xData = new ArrayList<>();
        List<Object> yData = new ArrayList<>();
        scoreTrend.setXData(xData);
        scoreTrend.setYData(yData);
        if(CollectionUtils.isNotEmpty(monitorResults)) {
            for (QuaMonitorResult monitorResult : monitorResults) {
                xData.add(DateUtils.localDateTimeToString(monitorResult.getCreateTime()));
                yData.add(monitorResult.getMonitorScore());
            }
        }
        return scoreTrend;
    }

    @Override
    public BasePageResponse<List<TaskScoreDetail>> scoreDetail(JobRequestDto requestDto) throws Exception {
        List<TaskScoreDetail> list = new ArrayList<>();
        if(requestDto.getTaskId() == null) {
            String templateId = requestDto.getTemplateId();
            List<QuaMonitorTask> tasks = taskService.list(new LambdaQueryWrapper<QuaMonitorTask>()
                    .eq(QuaMonitorTask::getJobRules, templateId)
                    .orderByDesc(QuaMonitorTask::getCreateTime));
            if(CollectionUtils.isNotEmpty(tasks)) {
                QuaMonitorTask task = tasks.get(0);
                requestDto.setTaskId(task.getId());
            }
        }
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<TaskScoreDetail> page = monitorJobMapper.scoreDetail(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        } else {
            List<TaskScoreDetail> scoreDetails = page.getResult();
            for (TaskScoreDetail scoreDetail : scoreDetails) {
                scoreDetail.setUnit(RuleTypeEnum.getUnitByCode(scoreDetail.getModelCode()));
            }
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
        }
    }

    @Override
    public void scoreDownload(JobRequestDto requestDto, HttpServletResponse response) throws Exception {
        if(requestDto.getTaskId() == null) {
            String templateId = requestDto.getTemplateId();
            List<QuaMonitorTask> tasks = taskService.list(new LambdaQueryWrapper<QuaMonitorTask>()
                    .eq(QuaMonitorTask::getJobRules, templateId)
                    .orderByDesc(QuaMonitorTask::getCreateTime));
            if(CollectionUtils.isNotEmpty(tasks)) {
                QuaMonitorTask task = tasks.get(0);
                requestDto.setTaskId(task.getId());
            }
        }
        List<TaskScoreDetail> scoreDetails = monitorJobMapper.scoreDownload(requestDto);
        List<List<String>> rows = new ArrayList<>();
        // 设置请求头
        rows.add(ListUtil.toList(scoreDetailHeaders));
        String tableName = "";
        for (TaskScoreDetail scoreDetail : scoreDetails) {
            String date = DateUtils.localDateTimeToString(scoreDetail.getCreateTime());
            String match = scoreDetail.getIsMatch();
            String executeStatus = scoreDetail.getExecuteStatus().toString();
            tableName = scoreDetail.getTableName();
            if("0".equals(match)) {
                match = "是";
            } else {
                match = "否";
            }
            if("0".equals(executeStatus)) {
                executeStatus = "正常";
            } else {
                executeStatus = "异常";
            }
            List<String> row = CollUtil.newArrayList(scoreDetail.getTableName(), scoreDetail.getColumnName(), scoreDetail.getRuleTypeName(), scoreDetail.getThresholdValue(), scoreDetail.getCompareValue(),
                    match, date,executeStatus);
            rows.add(row);
        }

        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.write(rows);

        // 下载文件
        try(OutputStream outputStream = response.getOutputStream()) {
            //设置content—type
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset:utf-8");
            //设置标题
            String fileName = URLEncoder.encode(tableName + "评分详情", "UTF-8");
            //Content-disposition是MIME协议的扩展，MIME协议指示MIME用户代理如何显示附加的文件。
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            //将Writer刷新到OutPut
            writer.flush(outputStream, true);
            writer.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void batchDownload(List<JobRequestDto> requestDtoList, HttpServletResponse response) {
        // 创建临时文件夹
        File tempDir = new File(System.getProperty("java.io.tmpdir"), "excelFiles");
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
        List<File> fileList = new ArrayList<>();
        for (JobRequestDto requestDto : requestDtoList) {
            if(requestDto.getTaskId() == null) {
                String templateId = requestDto.getTemplateId();
                List<QuaMonitorTask> tasks = taskService.list(new LambdaQueryWrapper<QuaMonitorTask>()
                        .eq(QuaMonitorTask::getJobRules, templateId)
                        .orderByDesc(QuaMonitorTask::getCreateTime));
                if(CollectionUtils.isNotEmpty(tasks)) {
                    QuaMonitorTask task = tasks.get(0);
                    requestDto.setTaskId(task.getId());
                }
            }
            List<TaskScoreDetail> scoreDetails = monitorJobMapper.scoreDownload(requestDto);
            List<List<String>> rows = new ArrayList<>();
            // 设置请求头
            rows.add(ListUtil.toList(scoreDetailHeaders));
            String tableName = "";
            for (TaskScoreDetail scoreDetail : scoreDetails) {
                String date = DateUtils.localDateTimeToString(scoreDetail.getCreateTime());
                String match = scoreDetail.getIsMatch();
                tableName = scoreDetail.getTableName();
                if("0".equals(match)) {
                    match = "是";
                } else {
                    match = "否";
                }
                List<String> row = CollUtil.newArrayList(scoreDetail.getTableName(), scoreDetail.getColumnName(), scoreDetail.getRuleTypeName(), scoreDetail.getThresholdValue(), scoreDetail.getCompareValue(),
                        match, date);
                rows.add(row);
            }

            ExcelWriter writer = ExcelUtil.getWriter(true);
            writer.write(rows);
            String fileName = "评分详情_" + tableName + ".xlsx";
            File excelFile = new File(tempDir, fileName);
            writer.flush(excelFile);
            writer.close();
            fileList.add(excelFile);
        }

        File zipFile = new File(tempDir, "评分详情"+System.currentTimeMillis()+".zip");
        try (ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipFile))) {
            for (File file : fileList) {
                try (FileInputStream fis = new FileInputStream(file)) {
                    zipOut.putNextEntry(new ZipEntry(file.getName()));
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = fis.read(buffer)) >= 0) {
                        zipOut.write(buffer, 0, length);
                    }
                    zipOut.closeEntry();
                }
            }
        } catch (IOException e) {
            // 处理异常
            log.error("压缩文件异常",e);
        }

        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment; filename="+zipFile.getName());
        try (FileInputStream fis = new FileInputStream(zipFile);
             OutputStream os = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) >= 0) {
                os.write(buffer, 0, length);
            }
        } catch (IOException e) {
            // 处理异常
            log.error("压缩文件异常",e);
        } finally {
            // 删除临时文件
            for (File file : fileList) {
                file.delete();
            }
            zipFile.delete();
            tempDir.delete();
        }
    }

    @Override
    public Map<String, Object> taskDim(JobRequestDto requestDto) throws Exception {
        if(requestDto.getTaskId() == null) {
            String templateId = requestDto.getTemplateId();
            List<QuaMonitorTask> tasks = taskService.list(new LambdaQueryWrapper<QuaMonitorTask>()
                    .eq(QuaMonitorTask::getJobRules, templateId)
                    .orderByDesc(QuaMonitorTask::getCreateTime));
            if(CollectionUtils.isNotEmpty(tasks)) {
                QuaMonitorTask task = tasks.get(0);
                requestDto.setTaskId(task.getId());
            }
        }
        List<TaskDim> dims = monitorJobMapper.taskDim(requestDto);
        Map<String, TaskDim> dimMap = new HashMap<>();
        for (TaskDim dim : dims) {
            String columns = dim.getColumnName();
            String isMatch = dim.getIsMatch();
            Integer score = dim.getRuleScore();
            if(score == null) {
                score = 0;
            }
            String dimCode = dim.getDimCode();
            int columnCnt = 0;
            if(StringUtils.isNotEmpty(columns)) {
                columnCnt = columns.split(",").length;
            }
            if(dimMap.containsKey(dimCode)) {
                TaskDim taskDim = dimMap.get(dimCode);
                taskDim.setRelateColumn(taskDim.getRelateColumn() + columnCnt);
                if("0".equals(isMatch)) {
                    taskDim.setTriggerColumn(taskDim.getTriggerColumn() + columnCnt);
                }
                taskDim.setTotalScore(taskDim.getTotalScore() + score);
                taskDim.setDimCnt(taskDim.getDimCnt() + 1);
            } else {
                dim.setRelateColumn(dim.getRelateColumn() + columnCnt);
                if("0".equals(isMatch)) {
                    dim.setTriggerColumn(dim.getTriggerColumn() + columnCnt);
                }
                dim.setTotalScore(dim.getTotalScore() + score);
                dim.setDimCnt(dim.getDimCnt() + 1);
                dimMap.put(dimCode, dim);
            }
        }

        List<TaskDim> taskDims = new ArrayList<>();
        Map<String, Integer> dimScore = new HashMap<>();
        for (Map.Entry<String, TaskDim> entry : dimMap.entrySet()) {
            TaskDim taskDim = entry.getValue();
            BigDecimal sumDecimal = new BigDecimal(taskDim.getTotalScore());
            BigDecimal dimDecimal = new BigDecimal(taskDim.getDimCnt());
            BigDecimal avg = sumDecimal.divide(dimDecimal, 2, RoundingMode.HALF_UP);
            taskDim.setRuleScore(avg.intValue());
            taskDim.setDimName(StandardTypeEnum.getNameByCode(taskDim.getDimCode()));
            dimScore.put(taskDim.getDimCode(), avg.intValue());
            taskDims.add(taskDim);
        }

        List<String> radarDims = new ArrayList<>();
        List<Integer> radarValues = new ArrayList<>();
        for (StandardTypeEnum value : StandardTypeEnum.values()) {
            radarDims.add(value.getDesc());
            if(dimScore.containsKey(value.getCode())) {
                radarValues.add(dimScore.get(value.getCode()));
            } else {
                radarValues.add(0);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("dims", taskDims);
        result.put("radarDims", radarDims);
        result.put("radarValues", radarValues);
        return result;
    }

    private void saveTask(QuaMonitorJob monitorJob) {
        String taskNo = "JOB-" + System.currentTimeMillis();
        QuaMonitorTask task = new QuaMonitorTask();
        task.setJobId(monitorJob.getId());
        task.setModelId(monitorJob.getModelId());
        task.setTaskNo(taskNo);
        task.setJobRules(monitorJob.getJobRules());
        task.setRuleWeight(monitorJob.getRuleWeight());
        task.setSampleCnt(monitorJob.getSampleCnt());
        task.setStartTime(LocalDateTime.now());
        task.setRunProcess(0.0);
        task.setStatus(TaskStatusEnum.TODO.getStatus());
        task.setTenantId(monitorJob.getTenantId());
        task.setCreateTime(LocalDateTime.now());
        //保存task
        taskService.save(task);
    }
}
