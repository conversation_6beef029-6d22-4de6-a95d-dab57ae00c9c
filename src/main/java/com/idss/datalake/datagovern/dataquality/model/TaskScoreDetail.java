package com.idss.datalake.datagovern.dataquality.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评分详情
 */
@Data
public class TaskScoreDetail {

    /**
     * 表名
     */
    private String tableName;

    /**
     * 字段名
     */
    private String columnName;

    /**
     * 模板名称
     */
    private String ruleTypeName;

    private String modelCode;

    /**
     * 阈值
     */
    private String thresholdValue;

    /**
     * 是否异常 1-否 0-是
     */
    private String isMatch;

    /**
     * 执行状态: 0正常，1异常
     */
    private Integer executeStatus;

    /**
     * 稽核值
     */
    private String compareValue;

    /**
     * 稽核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String unit;
}
