/************************ CHAN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/16 16:25
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Copyright 2022 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/16 16:25
 */
@Data
public class ModelResponseVo {

    private Long id;

    @ApiModelProperty(value = "模型名称")
    private String modelName;

    @ApiModelProperty("数据库ID")
    private Long databaseId;

    @ApiModelProperty(value = "库名")
    private String databaseName;

    @ApiModelProperty(value = "表数")
    private Integer tableCnt;

    @ApiModelProperty(value = "描述")
    private String modelDesc;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    @ApiModelProperty(value = "模型包含的表")
    private List<ModelTable> tables;

    @ApiModelProperty(value = "资源ID")
    private Long elementId;

    @ApiModelProperty(value = "资源类型")
    private String elementType;

    private String snapshootVersion;

    private String openStatus;
}
