/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/16 16:18
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.controller;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.syslog.annotation.SysLog;
import com.idss.datalake.common.syslog.entity.OptModule;
import com.idss.datalake.common.syslog.entity.OptType;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorJob;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRule;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleTemplate;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorTask;
import com.idss.datalake.datagovern.dataquality.model.*;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorJobService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * Copyright 2022 IDSS
 * <p> 质量监测任务控制器
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/16 16:18
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/dataQuality/job")
public class QualityJobController {

    private static final Logger LOGGER = LoggerFactory.getLogger(QualityJobController.class);

    @Autowired
    private IQuaMonitorJobService monitorJobService;

    /**
     * 分页查询监测任务
     *
     * @param requestDto
     * @return
     */
    @SysLog(logName = "查询任务", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_TASK, switchRedisStatus = true)
    @PostMapping("/page")
    public BasePageResponse<List<JobResponseVo>> page(@RequestBody JobRequestDto requestDto) {
        try {
            return monitorJobService.queryJobPage(requestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查询任务详情
     *
     * @param id
     * @return
     */
    @SysLog(logName = "查询任务详情", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_TASK, switchRedisStatus = true)
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable String id) {
        ResultBean resultBean = new ResultBean();
        try {
            QuaMonitorJob job = monitorJobService.queryJobDetail(id);
            resultBean.setContent(job);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 查询规则
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/rules")
    public ResultBean detail(@RequestBody JobRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            List<QuaMonitorRule> rules = monitorJobService.queryRules(requestDto);
            resultBean.setContent(rules);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 新增或编辑规则
     *
     * @param requestDto
     * @return
     */
    @SysLog(logName = "质量监测任务", checkAddField = "id", optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_TASK, switchRedisStatus = true)
    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody JobRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            monitorJobService.addOrUpdate(requestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail(e.getMessage());
        }
        return resultBean;
    }


    /**
     * 子任务重新执行
     * @param jobId
     * @param taskNo
     * @return
     */
    @GetMapping("/reRun/{jobId}/{taskNo}")
    public ResultBean reRun(@PathVariable("jobId") Long jobId,@PathVariable("taskNo") String taskNo) {
        monitorJobService.reRun(jobId,taskNo);
        return ResultBean.success();
    }

    /**
     * 查看子任务
     *
     * @param jobId
     * @return
     */
    @SysLog(logName = "查看子任务", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_TASK, switchRedisStatus = true)
    @PostMapping("/taskList/{jobId}")
    public ResultBean listTask(@PathVariable String jobId, @RequestBody JobRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean.setContent(monitorJobService.taskList(jobId, requestDto));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 查看子任务
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/task/pauseOrResumeTask")
    public ResultBean pauseOrResumeTask(@RequestBody JobRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            monitorJobService.pauseOrResumeJon(requestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 查看任务结果
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/taskResult")
    public ResultBean taskResult(@RequestBody JobRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            TaskResultResponseVo vo = monitorJobService.taskResult(requestDto.getTaskId());
            resultBean.setContent(vo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 查看任务结果明细
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/taskDetail")
    public BasePageResponse<List<TaskResultDetailVo>> taskDetail(@RequestBody JobRequestDto requestDto) {
        try {
            BasePageResponse<List<TaskResultDetailVo>> vos = monitorJobService.taskDetail(requestDto);
            return vos;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 删除规则
     *
     * @param requestDto
     * @return
     */
    @SysLog(logName = "删除质量监测任务", optType = OptType.DELETE, optModule = OptModule.DATA_LAKE_GOVERNANCE_QUALITY_TASK, switchRedisStatus = true)
    @PostMapping("/delete")
    public ResultBean delete(@RequestBody JobRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            monitorJobService.delete(requestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 查看任务表信息
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/tableInfo")
    public ResultBean tableInfo(@RequestBody JobRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            TaskOverview taskOverview = monitorJobService.tableInfo(requestDto);
            resultBean.setContent(taskOverview);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 查看评分趋势
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/score/trend")
    public ResultBean scoreTrend(@RequestBody JobRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            TaskScoreTrend scoreTrend = monitorJobService.scoreTrend(requestDto);
            resultBean.setContent(scoreTrend);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("操作失败");
        }
        return resultBean;
    }

    /**
     * 查看评分详情
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/score/detail")
    public BasePageResponse<List<TaskScoreDetail>> scoreDetail(@RequestBody JobRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            return monitorJobService.scoreDetail(requestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查看评分详情
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/score/download")
    public void scoreDownload(@RequestBody JobRequestDto requestDto, HttpServletResponse response) {
        try {
            monitorJobService.scoreDownload(requestDto,response);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    /**
     * 批量下载
     * @param requestDto
     * @param response
     */
    @PostMapping("/score/batchDownload")
    public void batchDownload(@RequestBody List<JobRequestDto> requestDto, HttpServletResponse response){
        try {
            monitorJobService.batchDownload(requestDto,response);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    /**
     * 维度统计
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/dim")
    public ResultBean dimInfo(@RequestBody JobRequestDto requestDto) {
        try {
            Map<String, Object> taskDimMap = monitorJobService.taskDim(requestDto);
            return ResultBean.success(taskDimMap, "查询成功");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }
}
