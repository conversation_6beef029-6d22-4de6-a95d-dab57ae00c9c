package com.idss.datalake.datagovern.dataquality.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorResultAppeal;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorResultDetail;
import com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorResultAppealMapper;
import com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorResultDetailMapper;
import com.idss.datalake.datagovern.dataquality.model.AppealDto;
import com.idss.datalake.datagovern.dataquality.model.JobResponseVo;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorResultAppealService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.radar.util.UmsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 质量检测结果申诉 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Service
@Slf4j
public class QuaMonitorResultAppealServiceImpl extends ServiceImpl<QuaMonitorResultAppealMapper, QuaMonitorResultAppeal> implements IQuaMonitorResultAppealService {
    @Resource
    private QuaMonitorResultDetailMapper quaMonitorResultDetailMapper;

    @Override
    public ResultBean getAppealColumns(Long taskId) {
        return ResultBean.success(quaMonitorResultDetailMapper.selectList(new QueryWrapper<QuaMonitorResultDetail>().select("id","column_name").eq("del_flag",0).eq("task_id", taskId)));
    }

    @Override
    public ResultBean remove(Long id) {
        this.getBaseMapper().deleteById(id);
        return ResultBean.success();
    }

    @Override
    public ResultBean add(QuaMonitorResultAppeal appeal) {
        try {
            int tenantId = UmsUtils.getUVO().getTenantId();
            String userName = UmsUtils.getUVO().getUserName();
            appeal.setTenantId(tenantId);
            appeal.setAppealUser(userName);
            appeal.setAppealTime(LocalDateTime.now());
            this.getBaseMapper().insert(appeal);
            return ResultBean.success();
        } catch (Exception e) {
            log.info("保存申诉失败", e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @Override
    public BasePageResponse<List<QuaMonitorResultAppeal>> queryPage(AppealDto requestDto) {
        List<QuaMonitorResultAppeal> list = new ArrayList<>();
        if (!requestDto.getIsHandler()) {
            requestDto.setAppealUser(UmsUtils.getUVO().getUserName());
        }
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<QuaMonitorResultAppeal> page = this.getBaseMapper().queryPage(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        } else {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
        }
    }

    @Override
    public ResultBean handle(Long id) {
        QuaMonitorResultAppeal appeal = this.getBaseMapper().selectById(id);
        appeal.setHandleUser(UmsUtils.getEmptyUVO().getUserName());
        appeal.setHandleTime(LocalDateTime.now());
        appeal.setHandleStatus(true);
        this.getBaseMapper().updateById(appeal);
        return ResultBean.success();
    }
}
