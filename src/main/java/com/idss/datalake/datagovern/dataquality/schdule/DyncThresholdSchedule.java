/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/7/12
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/7/12
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.dataquality.schdule;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.util.ClickhouseUtil;
import com.idss.datalake.common.util.DateUtils;
import com.idss.datalake.common.util.ElasticSearchUtil;
import com.idss.datalake.common.util.HiveUtil;
import com.idss.datalake.common.util.MysqlUtil;
import com.idss.datalake.datagovern.dataquality.entity.QuaDyncResult;
import com.idss.datalake.datagovern.dataquality.entity.QuaDyncThreshold;
import com.idss.datalake.datagovern.dataquality.service.IQuaDyncResultService;
import com.idss.datalake.datagovern.dataquality.service.IQuaDyncThresholdService;
import com.idss.datalake.datagovern.dataquality.utils.MaForecast;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.element.utils.BtoaEncode;
import com.idss.radar.datasource.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Decoder;

import java.io.IOException;
import java.net.URL;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2024/7/12
 */
@Component
@Slf4j
public class DyncThresholdSchedule {
    @Autowired
    private QuaWabElementService elementService;
    @Autowired
    private IQuaDyncResultService quaDyncResultService;
    @Autowired
    private IQuaDyncThresholdService quaDyncThresholdService;


    @Value("${ma.python-path}")
    private String pythonPath;

    @Scheduled(cron = "${ma.schedule}")
    public void calc() {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        //所有待预测的
        List<QuaDyncThreshold> quaDyncThresholds = quaDyncThresholdService.list(new QueryWrapper<QuaDyncThreshold>().eq("status", 1));
        log.info("开始预测任务{}条",quaDyncThresholds.size());
        int index = 0;
        if (CollectionUtils.isNotEmpty(quaDyncThresholds)) {
            for (QuaDyncThreshold quaDyncThreshold : quaDyncThresholds) {
                QuaWabElement element = elementService.getById(quaDyncThreshold.getElementId());
                index ++;
                log.info("开始预测第{}个任务: {}",index,element.getElementName());
                if (ElementTypeEnum.CH.getCode().equals(element.getElementType())) {
                    List<QuaDyncResult> quaDyncResults = quaDyncResultService.list(new QueryWrapper<QuaDyncResult>()
                            .eq("result", 1)
                            .isNotNull("forecast_line")
                            .isNotNull("forecast_space")
                            .eq("dync_id", quaDyncThreshold.getId())
                            .orderByAsc("create_time"));
                    Connection connect = ClickhouseUtil.getConnect("jdbc:clickhouse://" + element.getChIp() + ":" + element.getChPort() + "/" + quaDyncThreshold.getDbName(), element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()));
                    Long line;
                    Long space;
                    QuaDyncResult result = new QuaDyncResult();
                    result.setDyncId(quaDyncThreshold.getId());
                    try {
                        line = ClickhouseUtil.countTableLine(connect, quaDyncThreshold.getTableName());
                        space = ClickhouseUtil.countTableSpace(connect, quaDyncThreshold.getDbName(), quaDyncThreshold.getTableName()).longValue();
                        ClickhouseUtil.close(connect);
                        result.setResult(1);
                        result.setLine(line);
                        result.setSpace(space);
                        if(CollectionUtils.isNotEmpty(quaDyncResults)){
                            QuaDyncResult lastResult = quaDyncResults.get(quaDyncResults.size() - 1);
                            result.setDayLineChange(result.getLine() - lastResult.getLine());
                            result.setDaySpaceChange(result.getSpace() - lastResult.getSpace());
                        }else{
                            result.setDayLineChange(0L);
                            result.setDaySpaceChange(0L);
                        }
                        //判断历史记录是否超过5条，超过则预测，不超过不预测
                        if (CollectionUtils.isNotEmpty(quaDyncResults) && quaDyncResults.size() >= 5) {
                            JSONObject param = new JSONObject();
                            JSONArray dateParam = new JSONArray();
                            JSONArray lineParam = new JSONArray();
                            JSONArray lineDayParam = new JSONArray();
                            JSONArray spaceParam = new JSONArray();
                            JSONArray spaceDayParam = new JSONArray();
                            for (QuaDyncResult quaDyncResult : quaDyncResults) {
                                dateParam.add(DateUtils.localDateTimeToString(quaDyncResult.getCreateTime(), DateUtils.YYYYMMDD_HYPHEN));
                                lineParam.add(quaDyncResult.getLine());
                                lineDayParam.add(quaDyncResult.getDayLineChange());
                                spaceParam.add(quaDyncResult.getSpace());
                                spaceDayParam.add(quaDyncResult.getDaySpaceChange());
                            }
                            param.put("date", dateParam);
                            if (quaDyncThreshold.getModelType().equals(MaForecast.MA)) {
                                param.put("data", lineParam);
                                Long lineForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastLine(lineForecast == null ? line : lineForecast);
                                param.put("data", spaceParam);
                                Long spaceForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastSpace(spaceForecast == null ? space : spaceForecast);
                                param.put("data", lineDayParam);
                                Long dayLineForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastDayLineChange(dayLineForecast == null ? result.getDayLineChange() : dayLineForecast);
                                param.put("data", spaceDayParam);
                                Long daySpaceForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastDaySpaceChange(daySpaceForecast == null ? result.getDaySpaceChange() : daySpaceForecast);
                            } else if (quaDyncThreshold.getModelType().equals(MaForecast.ARIMA)) {
                                param.put("data", lineParam);
                                Long lineForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastLine(lineForecast == null ? line : lineForecast);
                                param.put("data", spaceParam);
                                Long spaceForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastSpace(spaceForecast == null ? space : spaceForecast);
                                param.put("data", lineDayParam);
                                Long dayLineForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastDayLineChange(dayLineForecast == null ? result.getDayLineChange() : dayLineForecast);
                                param.put("data", spaceDayParam);
                                Long daySpaceForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastDaySpaceChange(daySpaceForecast == null ? result.getDaySpaceChange() : daySpaceForecast);
                            } else if (quaDyncThreshold.getModelType().equals(MaForecast.EWMA)) {
                                param.put("data", lineParam);
                                Long lineForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastLine(lineForecast == null ? line : lineForecast);
                                param.put("data", spaceParam);
                                Long spaceForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastSpace(spaceForecast == null ? space : spaceForecast);
                                param.put("data", lineDayParam);
                                Long dayLineForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastDayLineChange(dayLineForecast == null ? result.getDayLineChange() : dayLineForecast);
                                param.put("data", spaceDayParam);
                                Long daySpaceForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastDaySpaceChange(daySpaceForecast == null ? result.getDaySpaceChange() : daySpaceForecast);
                            }
                        } else {
                            //生成记录
                            result.setForecastLine(line);
                            result.setForecastSpace(space);
                            if(CollectionUtils.isNotEmpty(quaDyncResults)){
                                QuaDyncResult lastResult = quaDyncResults.get(quaDyncResults.size() - 1);
                                if(result.getLine() != null && lastResult.getLine() != null){
                                    result.setDayLineChange(result.getLine() - lastResult.getLine());
                                    result.setForecastDayLineChange(result.getLine() - lastResult.getLine());
                                }else{
                                    result.setDayLineChange(0L);
                                    result.setForecastDayLineChange(0L);
                                }
                                if(result.getSpace() != null && lastResult.getSpace() != null){
                                    result.setDaySpaceChange(result.getSpace() - lastResult.getSpace());
                                    result.setForecastDaySpaceChange(result.getSpace() - lastResult.getSpace());
                                }else{
                                    result.setDaySpaceChange(0L);
                                    result.setForecastDaySpaceChange(0L);
                                }
                            }
                        }
                        if (quaDyncThreshold.getForecastTarget().equals("line")) {
                            quaDyncThreshold.setForecastValue(result.getForecastDayLineChange());
                        } else {
                            quaDyncThreshold.setForecastValue(result.getForecastDaySpaceChange());
                        }
                        quaDyncThreshold.setUpdateTime(LocalDateTime.now());
                        quaDyncThresholdService.updateById(quaDyncThreshold);
                    } catch (Exception e) {
                        log.error("统计CH行数错误", e);
                        result.setResult(0);
                        result.setFailError(e.getMessage());
                    }
                    quaDyncResultService.save(result);
                } else if (ElementTypeEnum.MYSQL.getCode().equals(element.getElementType())) {
                    List<QuaDyncResult> quaDyncResults = quaDyncResultService.list(new QueryWrapper<QuaDyncResult>()
                            .eq("result", 1)
                            .isNotNull("forecast_line")
                            .isNotNull("forecast_space")
                            .eq("dync_id", quaDyncThreshold.getId())
                            .orderByAsc("create_time"));
                    Connection connect = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_MYSQL, "jdbc:mysql://" + element.getChIp() + ":" + element.getChPort() + "/" + quaDyncThreshold.getDbName(), element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()));
                    Long line;
                    Long space;
                    QuaDyncResult result = new QuaDyncResult();
                    result.setDyncId(quaDyncThreshold.getId());
                    try {
                        line = MysqlUtil.countTableLine(connect, quaDyncThreshold.getTableName());
                        space = MysqlUtil.countTableSpace(connect, quaDyncThreshold.getDbName(), quaDyncThreshold.getTableName()).longValue();
                        MysqlUtil.close(connect);
                        result.setResult(1);
                        result.setLine(line);
                        result.setSpace(space);
                        if(CollectionUtils.isNotEmpty(quaDyncResults)){
                            QuaDyncResult lastResult = quaDyncResults.get(quaDyncResults.size() - 1);
                            result.setDayLineChange(result.getLine() - lastResult.getLine());
                            result.setDaySpaceChange(result.getSpace() - lastResult.getSpace());
                        }else{
                            result.setDayLineChange(0L);
                            result.setDaySpaceChange(0L);
                        }
                        //判断历史记录是否超过5条，超过则预测，不超过不预测
                        if (CollectionUtils.isNotEmpty(quaDyncResults) && quaDyncResults.size() >= 5) {
                            JSONObject param = new JSONObject();
                            JSONArray dateParam = new JSONArray();
                            JSONArray lineParam = new JSONArray();
                            JSONArray lineDayParam = new JSONArray();
                            JSONArray spaceParam = new JSONArray();
                            JSONArray spaceDayParam = new JSONArray();
                            for (QuaDyncResult quaDyncResult : quaDyncResults) {
                                dateParam.add(DateUtils.localDateTimeToString(quaDyncResult.getCreateTime(), DateUtils.YYYYMMDD_HYPHEN));
                                lineParam.add(quaDyncResult.getLine());
                                lineDayParam.add(quaDyncResult.getDayLineChange());
                                spaceParam.add(quaDyncResult.getSpace());
                                spaceDayParam.add(quaDyncResult.getDaySpaceChange());
                            }
                            param.put("date", dateParam);
                            if (quaDyncThreshold.getModelType().equals(MaForecast.MA)) {
                                param.put("data", lineParam);
                                Long lineForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastLine(lineForecast == null ? line : lineForecast);
                                param.put("data", spaceParam);
                                Long spaceForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastSpace(spaceForecast == null ? space : spaceForecast);
                                param.put("data", lineDayParam);
                                Long dayLineForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastDayLineChange(dayLineForecast == null ? result.getDayLineChange() : dayLineForecast);
                                param.put("data", spaceDayParam);
                                Long daySpaceForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastDaySpaceChange(daySpaceForecast == null ? result.getDaySpaceChange() : daySpaceForecast);
                            } else if (quaDyncThreshold.getModelType().equals(MaForecast.ARIMA)) {
                                param.put("data", lineParam);
                                Long lineForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastLine(lineForecast == null ? line : lineForecast);
                                param.put("data", spaceParam);
                                Long spaceForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastSpace(spaceForecast == null ? space : spaceForecast);
                                param.put("data", lineDayParam);
                                Long dayLineForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastDayLineChange(dayLineForecast == null ? result.getDayLineChange() : dayLineForecast);
                                param.put("data", spaceDayParam);
                                Long daySpaceForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastDaySpaceChange(daySpaceForecast == null ? result.getDaySpaceChange() : daySpaceForecast);
                            } else if (quaDyncThreshold.getModelType().equals(MaForecast.EWMA)) {
                                param.put("data", lineParam);
                                Long lineForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastLine(lineForecast == null ? line : lineForecast);
                                param.put("data", spaceParam);
                                Long spaceForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastSpace(spaceForecast == null ? space : spaceForecast);
                                param.put("data", lineDayParam);
                                Long dayLineForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastDayLineChange(dayLineForecast == null ? result.getDayLineChange() : dayLineForecast);
                                param.put("data", spaceDayParam);
                                Long daySpaceForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastDaySpaceChange(daySpaceForecast == null ? result.getDaySpaceChange() : daySpaceForecast);
                            }else {
                                log.info("模型类型错误:{},{}",quaDyncThreshold.getJobName(),quaDyncThreshold.getModelType());
                            }
                        } else {
                            //生成记录
                            result.setForecastLine(line);
                            result.setForecastSpace(space);
                            if(CollectionUtils.isNotEmpty(quaDyncResults)){
                                QuaDyncResult lastResult = quaDyncResults.get(quaDyncResults.size() - 1);
                                if(result.getLine() != null && lastResult.getLine() != null){
                                    result.setDayLineChange(result.getLine() - lastResult.getLine());
                                    result.setForecastDayLineChange(result.getLine() - lastResult.getLine());
                                }else{
                                    result.setDayLineChange(0L);
                                    result.setForecastDayLineChange(0L);
                                }
                                if(result.getSpace() != null && lastResult.getSpace() != null){
                                    result.setDaySpaceChange(result.getSpace() - lastResult.getSpace());
                                    result.setForecastDaySpaceChange(result.getSpace() - lastResult.getSpace());
                                }else{
                                    result.setDaySpaceChange(0L);
                                    result.setForecastDaySpaceChange(0L);
                                }
                            }
                        }
                        if (quaDyncThreshold.getForecastTarget().equals("line")) {
                            quaDyncThreshold.setForecastValue(result.getForecastDayLineChange());
                        } else {
                            quaDyncThreshold.setForecastValue(result.getForecastDaySpaceChange());
                        }
                        quaDyncThreshold.setUpdateTime(LocalDateTime.now());
                        quaDyncThresholdService.updateById(quaDyncThreshold);
                    } catch (Exception e) {
                        log.error("统计Mysql行数错误", e);
                        result.setResult(0);
                        result.setFailError(e.getMessage());
                    }
                    quaDyncResultService.save(result);
                } else if (ElementTypeEnum.ES.getCode().equals(element.getElementType())) {
                    List<QuaDyncResult> quaDyncResults = quaDyncResultService.list(new QueryWrapper<QuaDyncResult>()
                            .eq("result", 1)
                            .isNotNull("forecast_line")
                            .isNotNull("forecast_space")
                            .eq("dync_id", quaDyncThreshold.getId())
                            .orderByAsc("create_time"));
                    Long line;
                    Long space;
                    QuaDyncResult result = new QuaDyncResult();
                    result.setDyncId(quaDyncThreshold.getId());
                    try {
                        URL url = new URL(element.getEsIpPort());
                        String host = url.getHost();
                        int port = url.getPort(); // 返回端口号，如果未指定则返回 -1
                        line = ElasticSearchUtil.indexLines(
                                host,
                                port,
                                StringUtils.isEmpty(element.getEsUserName()) ? null : element.getEsUserName(),
                                StringUtils.isEmpty(element.getEsUserPassword()) ? null :
                                        BtoaEncode.decrypt(element.getEsUserPassword()),
                                quaDyncThreshold.getIndexName());
                        space = ElasticSearchUtil.indexSpaceInMB(
                                host,
                                port,
                                StringUtils.isEmpty(element.getEsUserName()) ? null : element.getEsUserName(),
                                StringUtils.isEmpty(element.getEsUserPassword()) ? null :
                                        BtoaEncode.decrypt(element.getEsUserPassword()),
                                quaDyncThreshold.getIndexName()).longValue();
                        result.setResult(1);
                        result.setLine(line);
                        result.setSpace(space);
                        if(CollectionUtils.isNotEmpty(quaDyncResults)){
                            QuaDyncResult lastResult = quaDyncResults.get(quaDyncResults.size() - 1);
                            result.setDayLineChange(result.getLine() - lastResult.getLine());
                            result.setDaySpaceChange(result.getSpace() - lastResult.getSpace());
                        }else{
                            result.setDayLineChange(0L);
                            result.setDaySpaceChange(0L);
                        }
                        //判断历史记录是否超过5条，超过则预测，不超过不预测
                        if (CollectionUtils.isNotEmpty(quaDyncResults) && quaDyncResults.size() >= 5) {
                            JSONObject param = new JSONObject();
                            JSONArray dateParam = new JSONArray();
                            JSONArray lineParam = new JSONArray();
                            JSONArray lineDayParam = new JSONArray();
                            JSONArray spaceParam = new JSONArray();
                            JSONArray spaceDayParam = new JSONArray();
                            for (QuaDyncResult quaDyncResult : quaDyncResults) {
                                dateParam.add(DateUtils.localDateTimeToString(quaDyncResult.getCreateTime(), DateUtils.YYYYMMDD_HYPHEN));
                                lineParam.add(quaDyncResult.getLine());
                                lineDayParam.add(quaDyncResult.getDayLineChange());
                                spaceParam.add(quaDyncResult.getSpace());
                                spaceDayParam.add(quaDyncResult.getDaySpaceChange());
                            }
                            param.put("date", dateParam);
                            if (quaDyncThreshold.getModelType().equals(MaForecast.MA)) {
                                param.put("data", lineParam);
                                Long lineForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastLine(lineForecast == null ? line : lineForecast);
                                param.put("data", spaceParam);
                                Long spaceForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastSpace(spaceForecast == null ? space : spaceForecast);
                                param.put("data", lineDayParam);
                                Long dayLineForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastDayLineChange(dayLineForecast == null ? result.getDayLineChange() : dayLineForecast);
                                param.put("data", spaceDayParam);
                                Long daySpaceForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastDaySpaceChange(daySpaceForecast == null ? result.getDaySpaceChange() : daySpaceForecast);
                            } else if (quaDyncThreshold.getModelType().equals(MaForecast.ARIMA)) {
                                param.put("data", lineParam);
                                Long lineForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastLine(lineForecast == null ? line : lineForecast);
                                param.put("data", spaceParam);
                                Long spaceForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastSpace(spaceForecast == null ? space : spaceForecast);
                                param.put("data", lineDayParam);
                                Long dayLineForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastDayLineChange(dayLineForecast == null ? result.getDayLineChange() : dayLineForecast);
                                param.put("data", spaceDayParam);
                                Long daySpaceForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastDaySpaceChange(daySpaceForecast == null ? result.getDaySpaceChange() : daySpaceForecast);
                            } else if (quaDyncThreshold.getModelType().equals(MaForecast.EWMA)) {
                                param.put("data", lineParam);
                                Long lineForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastLine(lineForecast == null ? line : lineForecast);
                                param.put("data", spaceParam);
                                Long spaceForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastSpace(spaceForecast == null ? space : spaceForecast);
                                param.put("data", lineDayParam);
                                Long dayLineForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastDayLineChange(dayLineForecast == null ? result.getDayLineChange() : dayLineForecast);
                                param.put("data", spaceDayParam);
                                Long daySpaceForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastDaySpaceChange(daySpaceForecast == null ? result.getDaySpaceChange() : daySpaceForecast);
                            }
                        } else {
                            //生成记录
                            result.setForecastLine(line);
                            result.setForecastSpace(space);
                            if(CollectionUtils.isNotEmpty(quaDyncResults)){
                                QuaDyncResult lastResult = quaDyncResults.get(quaDyncResults.size() - 1);
                                if(result.getLine() != null && lastResult.getLine() != null){
                                    result.setDayLineChange(result.getLine() - lastResult.getLine());
                                    result.setForecastDayLineChange(result.getLine() - lastResult.getLine());
                                }else{
                                    result.setDayLineChange(0L);
                                    result.setForecastDayLineChange(0L);
                                }
                                if(result.getSpace() != null && lastResult.getSpace() != null){
                                    result.setDaySpaceChange(result.getSpace() - lastResult.getSpace());
                                    result.setForecastDaySpaceChange(result.getSpace() - lastResult.getSpace());
                                }else{
                                    result.setDaySpaceChange(0L);
                                    result.setForecastDaySpaceChange(0L);
                                }
                            }
                        }
                        if (quaDyncThreshold.getForecastTarget().equals("line")) {
                            quaDyncThreshold.setForecastValue(result.getForecastDayLineChange());
                        } else {
                            quaDyncThreshold.setForecastValue(result.getForecastDaySpaceChange());
                        }
                        quaDyncThreshold.setUpdateTime(LocalDateTime.now());
                        quaDyncThresholdService.updateById(quaDyncThreshold);
                    } catch (Exception e) {
                        log.error("统计ES表行数错误", e);
                        result.setResult(0);
                        result.setFailError(e.getMessage());
                    }
                    quaDyncResultService.save(result);
                } else if (ElementTypeEnum.HIVE.getCode().equals(element.getElementType())) {
                    QuaDyncResult result = new QuaDyncResult();
                    result.setDyncId(quaDyncThreshold.getId());
                    List<QuaDyncResult> quaDyncResults = quaDyncResultService.list(new QueryWrapper<QuaDyncResult>()
                            .eq("result", 1)
                            .isNotNull("forecast_line")
                            .isNotNull("forecast_space")
                            .eq("dync_id", quaDyncThreshold.getId())
                            .orderByAsc("create_time"));
                    Connection connect = null;
                    Long line;
                    Long space;
                    try {
                        if (element.getKbsEnable() == 1) {
                            BASE64Decoder decoder = new BASE64Decoder();
                            connect = HiveUtil.getConnect(element.getJdbcUrl(), element.getChUserName(), StringUtils.isEmpty(element.getChUserPassword()) ? "" : BtoaEncode.decrypt(element.getChUserPassword()), "1", element.getChUserName(), new String(decoder.decodeBuffer(element.getEsKeytabFilePath())), new String(decoder.decodeBuffer(element.getEsKrb5FilePath())));
                        } else {
                            connect = HiveUtil.getConnect(
                                    "jdbc:hive2://" + element.getChIp() + ":" + element.getChPort() + "/" + quaDyncThreshold.getDbName(), element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()), "", "", "", "");
                        }
                        line = HiveUtil.countTableLine(connect, quaDyncThreshold.getTableName());
                        space = HiveUtil.countTableSpace(connect, quaDyncThreshold.getDbName(), quaDyncThreshold.getTableName()).longValue();
                        HiveUtil.close(connect);
                        result.setResult(1);
                        result.setLine(line);
                        result.setSpace(space);
                        if(CollectionUtils.isNotEmpty(quaDyncResults)){
                            QuaDyncResult lastResult = quaDyncResults.get(quaDyncResults.size() - 1);
                            result.setDayLineChange(result.getLine() - lastResult.getLine());
                            result.setDaySpaceChange(result.getSpace() - lastResult.getSpace());
                        }else{
                            result.setDayLineChange(0L);
                            result.setDaySpaceChange(0L);
                        }
                        //判断历史记录是否超过5条，超过则预测，不超过不预测
                        if (CollectionUtils.isNotEmpty(quaDyncResults) && quaDyncResults.size() >= 5) {
                            JSONObject param = new JSONObject();
                            JSONArray dateParam = new JSONArray();
                            JSONArray lineParam = new JSONArray();
                            JSONArray lineDayParam = new JSONArray();
                            JSONArray spaceParam = new JSONArray();
                            JSONArray spaceDayParam = new JSONArray();
                            for (QuaDyncResult quaDyncResult : quaDyncResults) {
                                dateParam.add(DateUtils.localDateTimeToString(quaDyncResult.getCreateTime(), DateUtils.YYYYMMDD_HYPHEN));
                                lineParam.add(quaDyncResult.getLine());
                                lineDayParam.add(quaDyncResult.getDayLineChange());
                                spaceParam.add(quaDyncResult.getSpace());
                                spaceDayParam.add(quaDyncResult.getDaySpaceChange());
                            }
                            param.put("date", dateParam);
                            if (quaDyncThreshold.getModelType().equals(MaForecast.MA)) {
                                param.put("data", lineParam);
                                Long lineForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastLine(lineForecast == null ? line : lineForecast);
                                param.put("data", spaceParam);
                                Long spaceForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastSpace(spaceForecast == null ? space : spaceForecast);
                                param.put("data", lineDayParam);
                                Long dayLineForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastDayLineChange(dayLineForecast == null ? result.getDayLineChange() : dayLineForecast);
                                param.put("data", spaceDayParam);
                                Long daySpaceForecast = MaForecast.forecast(pythonPath, MaForecast.MA, param);
                                result.setForecastDaySpaceChange(daySpaceForecast == null ? result.getDaySpaceChange() : daySpaceForecast);
                            } else if (quaDyncThreshold.getModelType().equals(MaForecast.ARIMA)) {
                                param.put("data", lineParam);
                                Long lineForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastLine(lineForecast == null ? line : lineForecast);
                                param.put("data", spaceParam);
                                Long spaceForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastSpace(spaceForecast == null ? space : spaceForecast);
                                param.put("data", lineDayParam);
                                Long dayLineForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastDayLineChange(dayLineForecast == null ? result.getDayLineChange() : dayLineForecast);
                                param.put("data", spaceDayParam);
                                Long daySpaceForecast = MaForecast.forecast(pythonPath, MaForecast.ARIMA, param);
                                result.setForecastDaySpaceChange(daySpaceForecast == null ? result.getDaySpaceChange() : daySpaceForecast);
                            } else if (quaDyncThreshold.getModelType().equals(MaForecast.EWMA)) {
                                param.put("data", lineParam);
                                Long lineForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastLine(lineForecast == null ? line : lineForecast);
                                param.put("data", spaceParam);
                                Long spaceForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastSpace(spaceForecast == null ? space : spaceForecast);
                                param.put("data", lineDayParam);
                                Long dayLineForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastDayLineChange(dayLineForecast == null ? result.getDayLineChange() : dayLineForecast);
                                param.put("data", spaceDayParam);
                                Long daySpaceForecast = MaForecast.forecast(pythonPath, MaForecast.EWMA, param);
                                result.setForecastDaySpaceChange(daySpaceForecast == null ? result.getDaySpaceChange() : daySpaceForecast);
                            }
                        } else {
                            //生成记录
                            result.setForecastLine(line);
                            result.setForecastSpace(space);
                            if(CollectionUtils.isNotEmpty(quaDyncResults)){
                                QuaDyncResult lastResult = quaDyncResults.get(quaDyncResults.size() - 1);
                                if(result.getLine() != null && lastResult.getLine() != null){
                                    result.setDayLineChange(result.getLine() - lastResult.getLine());
                                    result.setForecastDayLineChange(result.getLine() - lastResult.getLine());
                                }else{
                                    result.setDayLineChange(0L);
                                    result.setForecastDayLineChange(0L);
                                }
                                if(result.getSpace() != null && lastResult.getSpace() != null){
                                    result.setDaySpaceChange(result.getSpace() - lastResult.getSpace());
                                    result.setForecastDaySpaceChange(result.getSpace() - lastResult.getSpace());
                                }else{
                                    result.setDaySpaceChange(0L);
                                    result.setForecastDaySpaceChange(0L);
                                }
                            }
                        }
                        if (quaDyncThreshold.getForecastTarget().equals("line")) {
                            quaDyncThreshold.setForecastValue(result.getForecastDayLineChange());
                        } else {
                            quaDyncThreshold.setForecastValue(result.getForecastDaySpaceChange());
                        }
                        quaDyncThreshold.setUpdateTime(LocalDateTime.now());
                        quaDyncThresholdService.updateById(quaDyncThreshold);
                    } catch (Exception e) {
                        log.error("统计Hive行数错误", e);
                        result.setResult(0);
                        result.setFailError(e.getMessage());
                    }
                    quaDyncResultService.save(result);
                }
            }
        }
    }
}
