package com.idss.datalake.datagovern.dataquality.enums;

/**
 * @Author: <PERSON><PERSON>xiaofei
 * @Date: 19/6/2021 13:05
 * @Description:
 */
public enum TaskStatusEnum {
    TODO(1, "待执行"),

    RUNNING(2, "执行中"),

    PAUSE(3, "暂停"),

    COMPLETED(4, "完成"),

    STOP(5, "停止"),

    END(6, "任务结束");

    TaskStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static String getStatusName(int status) {
        for (TaskStatusEnum value : TaskStatusEnum.values()) {
            if(value.getStatus() == status) {
                return value.getDesc();
            }
        }
        return "";
    }

    private int status;
    private String desc;

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
