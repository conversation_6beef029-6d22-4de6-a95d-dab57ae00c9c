/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/16 16:25
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Copyright 2022 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/16 16:25
 */
@Data
public class TaskResultDetailVo {

    private Long id;

    @ApiModelProperty(value = "表名")
    private String tableName;

    @ApiModelProperty(value = "字段名")
    private String columnName;

    @ApiModelProperty(value = "触发规则")
    private String ruleType;

    @ApiModelProperty(value = "规则权重")
    private String ruleWeight;

    @ApiModelProperty(value = "问题级别")
    private String ruleLevel;
}
