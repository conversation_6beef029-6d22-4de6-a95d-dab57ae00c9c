package com.idss.datalake.datagovern.dataquality.service;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorResultAppeal;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.dataquality.model.AppealDto;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import java.util.List;

/**
 * <p>
 * 质量检测结果申诉 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
public interface IQuaMonitorResultAppealService extends IService<QuaMonitorResultAppeal> {
    ResultBean getAppealColumns(Long taskId);
    ResultBean remove(Long id);
    ResultBean add(QuaMonitorResultAppeal appeal);
    BasePageResponse<List<QuaMonitorResultAppeal>> queryPage(AppealDto requestDto);

    ResultBean handle(Long id);
}
