package com.idss.datalake.datagovern.dataquality.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorJob;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.dataquality.model.*;

import java.util.List;

/**
 * <p>
 * 质量监测JOB Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
public interface QuaMonitorJobMapper extends BaseMapper<QuaMonitorJob> {

    Page<JobResponseVo> queryJobPage(JobRequestDto requestDto);

    Page<TaskResultDetailVo> taskDetail(JobRequestDto requestDto);

    Page<TaskScoreDetail> scoreDetail(JobRequestDto requestDto);

    List<TaskScoreDetail> scoreDownload(JobRequestDto requestDto);

    List<TaskDim> taskDim(JobRequestDto requestDto);
}
