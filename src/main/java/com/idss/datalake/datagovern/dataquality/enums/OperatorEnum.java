package com.idss.datalake.datagovern.dataquality.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @Author: xiexiaofei
 * @Date: 19/6/2021 13:05
 * @Description: 操作符
 */
public enum OperatorEnum {
    GE("ge", ">="),
    LE("le", "<="),
    GT("gt", ">"),
    LT("lt", "<"),
    EQ("eq", "="),
    NE("ne", "!="),
    BTW("btw", "介于");

    OperatorEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;
    private String desc;

    public static String getNameByCode(String code) {
        if(StringUtils.isEmpty(code)) {
            return "";
        }
        for (OperatorEnum value : OperatorEnum.values()) {
            if(code.equals(value.getCode())) {
                return value.getDesc();
            }
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
