package com.idss.datalake.datagovern.dataquality.enums;

/**
 * @Author: xiexiaofei
 * @Date: 19/6/2021 13:05
 * @Description: 规范类别
 */
public enum NormTypeEnum {
    idCard("idCard", "身份证"),
    mobileNo("mobileNo", "手机号"),
    email("email", "邮箱"),
    reg("reg", "自定义正则");

    NormTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;
    private String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
