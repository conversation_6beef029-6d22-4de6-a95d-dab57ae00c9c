/************************ CHAN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/7/13
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/7/13
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.dataquality.dto;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2024/7/13
 */
@Data
public class InternalModelPageRequest extends BasePageRequest {
    /**
     * 模版名称
     */
    private String name;


    /**
     * 模版维度:及时性 timeliness、唯一性 uniqueness、完整性 integrality、一致性 uniformity、有效性 effectiveness、准确性 precision
     */
    private String dimensions;
}
