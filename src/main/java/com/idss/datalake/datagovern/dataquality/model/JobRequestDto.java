/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/16 16:21
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.model;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Copyright 2022 IDSS
 * <p> 监测规则查询条件
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/16 16:21
 */
@Data
public class JobRequestDto extends BasePageRequest {

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("模型Id")
    private Long modelId;

    @ApiModelProperty("任务描述")
    private String jobDesc;

    @ApiModelProperty("抽样数量")
    private Integer sampleCnt;

    @ApiModelProperty("执行周期")
    private String executeCycle;

    private JobCron cronTab;

    @ApiModelProperty("任务规则")
    private List<JobRule> rules;

    @ApiModelProperty("任务Id")
    private Long id;

    private List<Long> ids;

    private Long jobId;

    private Long taskId;

    private Integer status;

    /**
     * 01-弱规则执行  02-强规则执行
     */
    private String executeRuleType;

    /**
     * 采集任务ID
     */
    private Long flowId;

    private String templateId;

    private String startTime;

    private String endTime;

    private String quickTime;

    private String columnName;
    private String ruleTypeName;
    private Integer isMatch;

}
