package com.idss.datalake.datagovern.dataquality.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorResultAppeal;
import com.idss.datalake.datagovern.dataquality.model.AppealDto;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorResultAppealService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 质量检测结果申诉 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/dataQuality/appeal")
public class QuaMonitorResultAppealController {
    @Autowired
    private IQuaMonitorResultAppealService appealService;
    @GetMapping("/columns/{taskId}")
    public ResultBean getAppealColumns(@PathVariable("taskId") Long taskId){
        return appealService.getAppealColumns(taskId);
    }
    @GetMapping("/remove/{id}")
    public ResultBean remove(@PathVariable("id")Long id){
        return appealService.remove(id);
    }
    @PostMapping("/add")
    public ResultBean add(@RequestBody QuaMonitorResultAppeal appeal){
        return appealService.add(appeal);
    }

    @PostMapping("/page")
    public BasePageResponse<List<QuaMonitorResultAppeal>> queryPage(@RequestBody AppealDto requestDto){
        return appealService.queryPage(requestDto);
    }

    @GetMapping("/handle/{id}")
    public ResultBean handle(@PathVariable("id")Long id){
        return appealService.handle(id);
    }
}
