package com.idss.datalake.datagovern.dataquality.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorWeight;
import com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorWeightMapper;
import com.idss.datalake.datagovern.dataquality.model.RuleRequestDto;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorWeightService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 数据质量-权重管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Service
public class QuaMonitorWeightServiceImpl extends ServiceImpl<QuaMonitorWeightMapper, QuaMonitorWeight> implements IQuaMonitorWeightService {

    @Override
    public BasePageResponse<List<QuaMonitorWeight>> queryPage(RuleRequestDto requestDto) {
        List<QuaMonitorWeight> list = new ArrayList<>();
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        QueryWrapper queryWrapper = new QueryWrapper<QuaMonitorWeight>();
        if(StringUtils.isNotEmpty(requestDto.getDimName())) {
            queryWrapper.like("dim_name", requestDto.getDimName());
        }
        PageInfo<QuaMonitorWeight> page = new PageInfo(this.list(queryWrapper));
        if (CollectionUtils.isEmpty(page.getList())) {
            return new BasePageResponse(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        } else {
            List<QuaMonitorWeight> weights = page.getList();
            for (QuaMonitorWeight weight : weights) {
                weight.setWeightValueDesc(weight.getWeightValue() + "%");
            }
        }
        return new BasePageResponse(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getList());
    }
}
