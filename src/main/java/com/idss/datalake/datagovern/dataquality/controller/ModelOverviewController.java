/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/16 16:18
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.controller;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datagovern.dataquality.model.ModelOverviewRequestDto;
import com.idss.datalake.datagovern.dataquality.model.ModelRequestDto;
import com.idss.datalake.datagovern.dataquality.service.IModelOverviewService;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorModelService;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datashare.index.entity.TbStatisticsDisk;
import com.idss.datalake.datashare.index.entity.TbStatisticsDiskTenant;
import com.idss.datalake.datashare.index.service.ITbStatisticsDiskTenantService;
import com.idss.datalake.portal.dto.BarYData;
import com.idss.datalake.portal.dto.MultiLineData;
import com.idss.datalake.portal.dto.PieData;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 质量监测概览
 *
 * <AUTHOR>
 * @date 2024/11/14
 * @see
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/dataQuality/overview")
public class ModelOverviewController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModelOverviewController.class);

    @Autowired
    private IModelOverviewService modelOverviewService;

    @Autowired
    private IQuaMonitorModelService monitorModelService;

    @Resource
    ITbStatisticsDiskTenantService diskTenantService;

    @GetMapping("/diskTotal")
    public ResultBean diskTotal() {
        UserValueObject uvo = UmsUtils.getUVO();
        List<TbStatisticsDisk> disks = new ArrayList<>();
        List<TbStatisticsDiskTenant> diskTenants = diskTenantService.list(new QueryWrapper<TbStatisticsDiskTenant>()
                .eq("flag", Constant.FLAG_ABLE)
                .eq("tenant_id", uvo.getTenantId()));
        for (TbStatisticsDiskTenant diskTenant : diskTenants) {
            if (diskTenant.getClickhouseUsed() == null) {
                diskTenant.setClickhouseUsed(0L);
            }
            if (diskTenant.getClickhouseFree() == null) {
                diskTenant.setClickhouseFree(0L);
            }
            if (diskTenant.getMysqlUsed() == null) {
                diskTenant.setMysqlUsed(0L);
            }
            if (diskTenant.getMysqlFree() == null) {
                diskTenant.setMysqlFree(0L);
            }
            if (diskTenant.getHdfsUsed() == null) {
                diskTenant.setHdfsUsed(0L);
            }
            if (diskTenant.getHdfsFree() == null) {
                diskTenant.setHdfsFree(0L);
            }
            if (diskTenant.getElasticsearchUsed() == null) {
                diskTenant.setElasticsearchUsed(0L);
            }
            if (diskTenant.getElasticsearchFree() == null) {
                diskTenant.setElasticsearchFree(0L);
            }
        }

        TbStatisticsDisk clickhouseDisk = new TbStatisticsDisk();
        clickhouseDisk.setResourceType("clickhouse");
        long clickhouseSum = diskTenants.stream().map(TbStatisticsDiskTenant::getClickhouseUsed).mapToLong(Long::longValue).sum();
        clickhouseDisk.setUsedReadable("" + calcMb2Tb(clickhouseSum));
        clickhouseDisk.setFreeReadable("" + calcMb2Tb(diskTenants.stream().map(TbStatisticsDiskTenant::getClickhouseFree).mapToLong(Long::longValue).sum()));
        disks.add(clickhouseDisk);

        TbStatisticsDisk mysqlDisk = new TbStatisticsDisk();
        mysqlDisk.setResourceType("mysql");
        long mysqlSum = diskTenants.stream().map(TbStatisticsDiskTenant::getMysqlUsed).mapToLong(Long::longValue).sum();
        mysqlDisk.setUsedReadable("" + calcMb2Tb(mysqlSum));
        mysqlDisk.setFreeReadable("" + calcMb2Tb(diskTenants.stream().map(TbStatisticsDiskTenant::getMysqlFree).mapToLong(Long::longValue).sum()));
        disks.add(mysqlDisk);

        TbStatisticsDisk hdfsDisk = new TbStatisticsDisk();
        hdfsDisk.setResourceType("hdfs");
        long hdfsSum = diskTenants.stream().map(TbStatisticsDiskTenant::getHdfsUsed).mapToLong(Long::longValue).sum();
        hdfsDisk.setUsedReadable("" + calcMb2Tb(hdfsSum));
        hdfsDisk.setFreeReadable("" + calcMb2Tb(diskTenants.stream().map(TbStatisticsDiskTenant::getHdfsFree).mapToLong(Long::longValue).sum()));
        disks.add(hdfsDisk);

        TbStatisticsDisk elasticsearchDisk = new TbStatisticsDisk();
        elasticsearchDisk.setResourceType("elasticsearch");
        long elasticsearchSum = diskTenants.stream().map(TbStatisticsDiskTenant::getElasticsearchUsed).mapToLong(Long::longValue).sum();
        elasticsearchDisk.setUsedReadable("" + calcMb2Tb(elasticsearchSum));
        elasticsearchDisk.setFreeReadable("" + calcMb2Tb(diskTenants.stream().map(TbStatisticsDiskTenant::getElasticsearchFree).mapToLong(Long::longValue).sum()));
        disks.add(elasticsearchDisk);

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("total", calcMb2Tb(clickhouseSum + hdfsSum + elasticsearchSum + mysqlSum));
        dataMap.put("list", disks);
        return ResultBean.success(dataMap);
    }

    /**
     * 概览
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/general")
    public ResultBean page(@RequestBody ModelOverviewRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            resultBean.setContent(modelOverviewService.general(requestDto));
            return resultBean;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 数据源任务分布
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/task")
    public ResultBean task(@RequestBody ModelOverviewRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            BarYData barYData = modelOverviewService.task(requestDto);
            resultBean.setContent(barYData);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 数据源任务分布
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/risk")
    public ResultBean risk(@RequestBody ModelOverviewRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            List<PieData> pieData = modelOverviewService.risk(requestDto);
            resultBean.setContent(pieData);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 数据源任务分布
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/trend")
    public ResultBean trend(@RequestBody ModelOverviewRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            MultiLineData multiLineData = modelOverviewService.trend(requestDto);
            resultBean.setContent(multiLineData);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }

    /**
     * 数据源任务分布
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/taskDetail")
    public BaseResponse taskDetail(@RequestBody ModelRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            return monitorModelService.queryTaskDetailPage(requestDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return BaseResponse.error("查询失败");
        }
    }

    /**
     * 单位换算：M -> T
     *
     * @param mb
     * @return
     */
    private String calcMb2Tb(long mb) {
        double Tb = NumberUtil.div(mb, 1024 * 1024, 3);
        if (Tb > 0) {
            return String.valueOf(Tb);
        }
        return "0";
    }

    /**
     * 资产统计
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/assetStatistic")
    public ResultBean assetStatistic(@RequestBody ModelOverviewRequestDto requestDto) {
        ResultBean resultBean = new ResultBean();
        try {
            List<Map<String, Object>> mapList = modelOverviewService.assetStatistic(requestDto);
            resultBean.setContent(mapList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            resultBean = ResultBean.fail("查询失败");
        }
        return resultBean;
    }
}
