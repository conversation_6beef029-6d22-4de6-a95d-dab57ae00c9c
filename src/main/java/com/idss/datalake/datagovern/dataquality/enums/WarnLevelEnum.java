package com.idss.datalake.datagovern.dataquality.enums;

/**
 * @Author: <PERSON><PERSON>xiaof<PERSON>
 * @Date: 19/6/2021 13:05
 * @Description: 规则问题级别
 */
public enum WarnLevelEnum {
    severity("1", "高"),
    important("2", "中"),
    general("3", "低");

    WarnLevelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    private String code;

    private String desc;

    public static String getLevelName(String code) {
        for (WarnLevelEnum value : WarnLevelEnum.values()) {
            if(value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
