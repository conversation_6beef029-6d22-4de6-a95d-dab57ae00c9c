package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据质量内置模版
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QuaInternalModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 模版名称
     */
    private String name;

    private String modelCode;

    /**
     * 模版描述
     */
    private String modelDesc;

    /**
     * 计算规则
     */
    private String modelCalc;

    /**
     * 模版维度:及时性 timeliness、唯一性 uniqueness、完整性 integrality、一致性 uniformity、有效性 effectiveness、准确性 precision
     */
    private String dimensions;

    /**
     * 稽核对象: 表table, 字段field
     */
    private String auditObject;

    /**
     * 输入参数
     */
    private String requestParam;

    /**
     * 自定义sql
     */
    private String customSql;

    private String modelType;

    private Integer tenantId;

    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
