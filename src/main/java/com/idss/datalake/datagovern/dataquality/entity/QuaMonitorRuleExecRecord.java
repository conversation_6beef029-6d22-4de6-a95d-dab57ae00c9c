package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 模型试跑记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QuaMonitorRuleExecRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模型id
     */
    private Long ruleId;

    /**
     * 试跑结果 1-触发规则  0-未触发规则 -1-执行失败
     */
    private String execResult;

    /**
     * 执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 租户ID
     */
    private Long tenantId;


}
