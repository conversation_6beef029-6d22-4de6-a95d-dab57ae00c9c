package com.idss.datalake.datagovern.dataquality.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.util.DateUtils;
import com.idss.datalake.datagovern.dataquality.dto.DyncThresholdJobPageRequest;
import com.idss.datalake.datagovern.dataquality.dto.DyncThresholdJobPageVo;
import com.idss.datalake.datagovern.dataquality.dto.DyncThresholdPageRequest;
import com.idss.datalake.datagovern.dataquality.entity.QuaDyncResult;
import com.idss.datalake.datagovern.dataquality.entity.QuaDyncThreshold;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRule;
import com.idss.datalake.datagovern.dataquality.enums.RuleTypeEnum;
import com.idss.datalake.datagovern.dataquality.service.IQuaDyncResultService;
import com.idss.datalake.datagovern.dataquality.service.IQuaDyncThresholdService;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorRuleService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据质量动态阈值管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Slf4j
@RestController
@RequestMapping(Constant.API_PREFIX + "/dataQuality/dyncThreshold")
public class QuaDyncThresholdController {
    @Autowired
    private IQuaDyncThresholdService quaDyncThresholdService;
    @Autowired
    private IQuaDyncResultService quaDyncResultService;
    @Autowired
    private IQuaMonitorRuleService quaMonitorRuleService;


    @GetMapping("/elementDbname/{sourceType}")
    public ResultBean elementDbname(@PathVariable("sourceType") String sourceType) {
        return quaDyncThresholdService.elementDbname(sourceType);
    }

    @PostMapping("/elementTable")
    public ResultBean elementTable(@RequestBody Map<String, Object> param) {
        String sourceType = param.get("sourceType").toString();
        Long elementId = Long.parseLong(param.get("elementId").toString());
        String dbName = param.get("dbName").toString();
        return quaDyncThresholdService.elementTable(sourceType, elementId, dbName);
    }

    @GetMapping("/elementIndex")
    public ResultBean elementIndex() {
        return quaDyncThresholdService.elementIndex();
    }


    /**
     * 新增或修改
     *
     * @param threshold
     * @return
     */
    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody QuaDyncThreshold threshold) {
        UserValueObject uvo = UmsUtils.getUVO();
        if (threshold.getId() == null) {
            int count = quaDyncThresholdService.count(new QueryWrapper<QuaDyncThreshold>().eq("tenant_id", uvo.getTenantId()).eq("job_name", threshold.getJobName()));
            if (count > 1) {
                return ResultBean.fail("名称重复");
            }
            threshold.setTenantId(uvo.getTenantId());
            threshold.setCreateUser(uvo.getUserName());
            threshold.setCreateTime(LocalDateTime.now());
            threshold.setUpdateUser(uvo.getUserName());
            threshold.setUpdateTime(LocalDateTime.now());
            quaDyncThresholdService.save(threshold);
        } else {
            int count = quaDyncThresholdService.count(new QueryWrapper<QuaDyncThreshold>().ne("id", threshold.getId()).eq("tenant_id", uvo.getTenantId()).eq("job_name", threshold.getJobName()));
            if (count > 1) {
                return ResultBean.fail("名称已存在");
            }
            QuaDyncThreshold byId = quaDyncThresholdService.getById(threshold.getId());
            byId.setJobName(threshold.getJobName());
            byId.setRemark(threshold.getRemark());
            byId.setUpdateTime(LocalDateTime.now());
            byId.setUpdateUser(uvo.getUserName());
            quaDyncThresholdService.updateById(byId);
        }
        return ResultBean.success();
    }

    @PostMapping("/page")
    public BasePageResponse<List<QuaDyncThreshold>> page(@RequestBody DyncThresholdPageRequest requestDto) {
        return quaDyncThresholdService.page(requestDto);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    public ResultBean delete(@PathVariable("id") Long id) {
        // 判断动态阈值是否被使用
        List<String> ruleTypes = new ArrayList<>();
        ruleTypes.add(RuleTypeEnum.tableSize.getCode());
        ruleTypes.add(RuleTypeEnum.tableLine.getCode());
        List<QuaMonitorRule> monitorRules = quaMonitorRuleService.list(new LambdaQueryWrapper<QuaMonitorRule>()
                .in(QuaMonitorRule::getRuleType, ruleTypes));
        if(CollectionUtils.isNotEmpty(monitorRules)) {
            for (QuaMonitorRule monitorRule : monitorRules) {
                String ruleDetail = monitorRule.getRuleDetail();
                if(StringUtils.isNotEmpty(ruleDetail)) {
                    JSONObject jsonObject = JSONObject.parseObject(ruleDetail);
                    String valueType = jsonObject.getString("valueType");
                    if("2".equals(valueType)) {
                        // 质量规则使用了动态阈值
                        String value = jsonObject.getString("value");
                        if(String.valueOf(id).equals(value)) {
                            return ResultBean.fail("动态阈值被使用,无法删除");
                        }
                    }
                }
            }
        }
        quaDyncThresholdService.removeById(id);
        return ResultBean.success();
    }

    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        return ResultBean.success(quaDyncThresholdService.getById(id));
    }

    /**
     * 启动或停止任务
     *
     * @param id
     * @param state
     * @return
     */
    @GetMapping("/switchJob/{id}/{state}")
    public ResultBean switchJob(@PathVariable("id") Long id, @PathVariable("state") Integer state) {
        QuaDyncThreshold byId = quaDyncThresholdService.getById(id);
        byId.setStatus(state);
        quaDyncThresholdService.updateById(byId);
        return ResultBean.success();
    }

    /**
     * 任务日志分页
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/jobPage")
    public BasePageResponse<List<DyncThresholdJobPageVo>> jobPage(@RequestBody DyncThresholdJobPageRequest requestDto) {
        return quaDyncThresholdService.queryJobPage(requestDto);
    }

    @GetMapping("/jobResult/{id}")
    public ResultBean jobResult(@PathVariable("id") Long id) {
        JSONObject result = new JSONObject();
        result.put("date", null);
        result.put("data", null);
        QuaDyncThreshold byId = quaDyncThresholdService.getById(id);
        List<QuaDyncResult> list = quaDyncResultService.list(new QueryWrapper<QuaDyncResult>().eq("dync_id", id).orderByAsc("create_time"));
        if (byId.getForecastTarget().equals("line")) {
            if (CollectionUtils.isNotEmpty(list)) {
                List<String> date = new ArrayList<>();
                List<Long> data = new ArrayList<>();
                for (QuaDyncResult quaDyncResult : list) {
                    date.add(DateUtils.localDateTimeToString(quaDyncResult.getCreateTime(), DateUtils.YYYYMMDD_HYPHEN));
                    data.add(quaDyncResult.getForecastLine());
                }
                result.put("date", date);
                result.put("data", data);
            }
        } else {
            if (CollectionUtils.isNotEmpty(list)) {
                List<String> date = new ArrayList<>();
                List<Long> data = new ArrayList<>();
                for (QuaDyncResult quaDyncResult : list) {
                    date.add(DateUtils.localDateTimeToString(quaDyncResult.getCreateTime(), DateUtils.YYYYMMDD_HYPHEN));
                    data.add(quaDyncResult.getForecastSpace());
                }
                result.put("date", date);
                result.put("data", data);
            }
        }
        return ResultBean.success(result);
    }
}
