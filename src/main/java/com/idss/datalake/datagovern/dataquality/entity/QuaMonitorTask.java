package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 质量监测任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuaMonitorTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long modelId;

    /**
     * 任务编号
     */
    private String taskNo;

    /**
     * 任务ID
     */
    private Long jobId;

    /**
     * 规则 多个规则使用逗号分隔
     */
    private String jobRules;

    /**
     * 规则权重 权重和规则一一对应
     */
    private String ruleWeight;

    /**
     * 样例数据条数
     */
    private Integer sampleCnt;

    /**
     * 任务待执行日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 执行完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 执行结果。0：失败；1：成功
     */
    private Integer taskResult;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 执行进度百分比0-100
     */
    private Double runProcess;

    /**
     * 任务状态 1:待执行 2:执行中 3:暂停 4:扫描完成 5:停止 6:任务结束
     */
    private Integer status;

    /**
     * 租户ID
     */
    private Long tenantId;


    @TableField(exist = false)
    private String taskStatus;

    @TableField(exist = false)
    private String result;
}
