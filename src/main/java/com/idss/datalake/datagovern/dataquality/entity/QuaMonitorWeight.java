package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据质量-权重管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QuaMonitorWeight implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 维度名称
     */
    private String dimName;

    /**
     * 维度编码
     */
    private String dimCode;

    /**
     * 权重
     */
    private Integer weightValue;

    /**
     * 备注
     */
    private String weightDesc;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updateUser;

    @TableField(exist = false)
    private String weightValueDesc;
}
