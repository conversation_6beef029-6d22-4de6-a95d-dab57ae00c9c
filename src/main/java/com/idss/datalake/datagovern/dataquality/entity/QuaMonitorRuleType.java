package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 质量监测规则类别
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuaMonitorRuleType implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类别编码
     */
    private String typeCode;

    /**
     * 类别名称
     */
    private String typeName;

    /**
     * 配置项Key
     */
    private String configKey;

    /**
     * 配置项名称
     */
    private String configName;

    /**
     * 配置项类型input、select、date、checkbox
     */
    private String configType;

    /**
     * 是否必填 0-否 1-是
     */
    private String isRequired;

    /**
     * 正则表达式 如果该值不为空则配置的值需要满足该正则
     */
    private String regPattern;

    /**
     * 接口名 如果类型为下拉框，则需要根据该配置调用接口获取下拉框的值
     */
    private String interfaceName;

    /**
     * 排序
     */
    private Integer sortNo;

    private String multiSelect;

    private String typeDesc;

    @TableField(exist = false)
    private String value;
}
