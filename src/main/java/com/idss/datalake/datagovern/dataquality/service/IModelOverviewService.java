package com.idss.datalake.datagovern.dataquality.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorModel;
import com.idss.datalake.datagovern.dataquality.model.*;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.portal.dto.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 质量监测模型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
public interface IModelOverviewService {

    /**
     * 分页查询模型信息
     *
     * @param requestDto
     * @return
     */
    Map<String, Object> general(ModelOverviewRequestDto requestDto) throws Exception;

    BarYData task(ModelOverviewRequestDto requestDto) throws Exception;

    List<PieData> risk(ModelOverviewRequestDto requestDto) throws Exception;
    MultiLineData trend(ModelOverviewRequestDto requestDto) throws Exception;

    List<Map<String, Object>> assetStatistic(ModelOverviewRequestDto requestDto) throws Exception;
}
