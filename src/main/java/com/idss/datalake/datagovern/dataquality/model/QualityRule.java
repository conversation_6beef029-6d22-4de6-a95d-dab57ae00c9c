package com.idss.datalake.datagovern.dataquality.model;

import lombok.Data;

@Data
public class QualityRule {

    /**
     * 模板类型
     */
    private String ruleType;

    /**
     * 模板类型ID
     */
    private Long ruleTypeId;

    /**
     * 及时性检查
     */
    private TemplateTimely timely;

    /**
     * 重复率检查
     */
    private TemplateRepeatRate repeatRate;

    /**
     * 重复行检查
     */
    private TemplateRepeatLine repeatLine;

    /**
     * 空值率检查
     */
    private TemplateNullRate nullRate;

    /**
     * 空值行检查
     */
    private TemplateNullLine nullLine;

    /**
     * 逻辑通过数检查
     */
    private TemplateLogicLine logicLine;

    /**
     * 逻辑通过率检查
     */
    private TemplateLogicRate logicRate;

    /**
     * 长度规范率
     */
    private TemplateLengthRate lengthRate;

    /**
     * 长度规范行
     */
    private TemplateLengthLine lengthLine;

    /**
     * 格式规范率
     */
    private TemplateFormatRate formatRate;

    /**
     * 数据规范行
     */
    private TemplateFormatLine formatLine;

    /**
     * 值域通过数检查
     */
    private TemplateRangeLine rangeLine;

    /**
     * 值域通过率检查
     */
    private TemplateRangeRate rangeRate;

    /**
     * 表大小波动
     */
    private TemplateTableSize tableSize;

    /**
     * 表行数波动
     */
    private TemplateTableLine tableLine;

    /**
     * 自定义
     */
    private TemplateSelfDefine selfDefine;

    /**
     * 阈值
     */
    private String threshold;

    /**
     * 问题级别
     */
    private String ruleLevel;

    /**
     * 备注
     */
    private String ruleDesc;

    private String virtualId;

    private Long id;

    private String templateId;

    private Long modelId;

    private Integer ruleWeight;

    private String thresholdOperator;
}
