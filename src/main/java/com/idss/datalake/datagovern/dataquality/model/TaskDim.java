package com.idss.datalake.datagovern.dataquality.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评分详情
 */
@Data
public class TaskDim {

    /**
     * 维度
     */
    private String dimName;

    private String dimCode;

    /**
     * 关联字段数
     */
    private Integer relateColumn = 0;

    /**
     * 触发字段数
     */
    private Integer triggerColumn = 0;

    /**
     * 分数
     */
    private Integer ruleScore;

    /**
     * 总分
     */
    private Integer totalScore = 0;

    /**
     * 列名
     */
    private String columnName;

    /**
     * 是否触发
     */
    private String isMatch;

    private Integer dimCnt = 0;
}
