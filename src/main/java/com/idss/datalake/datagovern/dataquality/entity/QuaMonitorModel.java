package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 质量监测模型
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuaMonitorModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型描述
     */
    private String modelDesc;

    /**
     * 资源ID
     */
    private Long elementId;

    /**
     * 资源类型 CH、ES、MYSQL、HIVE
     */
    private String elementType;

    private String snapshootVersion;

    private Long databaseId;

    /**
     * 数据库名
     */
    private String databaseName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新时间
     */
    private String updateUser;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 有效标识:1-有效；0-无效
     */
    private String flag;

    private String openStatus;
}
