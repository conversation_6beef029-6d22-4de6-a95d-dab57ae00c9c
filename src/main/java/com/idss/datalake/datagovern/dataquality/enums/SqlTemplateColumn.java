/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2021/2/27 17:50
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.enums;

/**
 * Copyright 2021 IDSS
 * <p> 自定义SQL模板表头信息
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2021/2/27 17:50
 */
public enum SqlTemplateColumn {

    ruleName("ruleName","*模型名称"),
    ruleCode("ruleCode","*模型代码"),
    datasource("datasource","*数据源名称"),
    tableName("tableName","*选择表"),
    columnName("columnName","*检查字段"),
    sql("sql","*自定义sql"),
    compareType("compareType","*比较方式"),
    compareValue("compareValue","*比较值"),
    ruleLevel("ruleLevel","*问题级别");

    private String code;

    private String label;

    private SqlTemplateColumn(String code , String label) {
        this.code = code ;
        this.label = label ;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }
}
