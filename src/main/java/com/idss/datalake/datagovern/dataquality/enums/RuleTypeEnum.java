package com.idss.datalake.datagovern.dataquality.enums;

/**
 * 规则类型
 */
public enum RuleTypeEnum {

    timely("timely", "及时率检查", "%"),
    repeatRate("repeatRate", "重复率检查", "%"),

    repeatLine("repeatLine", "重复行检查", "行"),

    nullRate("nullRate", "空值率检查", "%"),

    nullLine("nullLine", "空值行检查", "行"),

    logicLine("logicLine", "逻辑通过数检查", "行"),

    logicRate("logicRate", "逻辑通过率检查", "%"),

    lengthRate("lengthRate", "长度规范率检查", "%"),

    lengthLine("lengthLine", "长度规范行检查", "行"),

    formatRate("formatRate", "格式规范率检查", "%"),

    formatLine("formatLine", "格式规范行检查", "行"),

    rangeLine("rangeLine", "值域通过数检查", "行"),

    rangeRate("rangeRate", "值域通过率检查", "%"),

    tableSize("tableSize", "表大小波动率检查", "%"),

    tableLine("tableLine", "表行数波动率检查", "%"),

    selfDefine("selfDefine", "自定义", "");

    private String code;

    private String name;

    private String unit;

    RuleTypeEnum(String code, String name, String unit) {
        this.code = code;
        this.name = name;
        this.unit = unit;
    }

    public static String getNameByCode(String code) {
        for (RuleTypeEnum value : RuleTypeEnum.values()) {
            if(code.equals(value.getCode())) {
                return value.getName();
            }
        }
        return code;
    }

    public static String getUnitByCode(String code) {
        for (RuleTypeEnum value : RuleTypeEnum.values()) {
            if(code.equals(value.getCode())) {
                return value.getUnit();
            }
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getUnit() {
        return unit;
    }
}
