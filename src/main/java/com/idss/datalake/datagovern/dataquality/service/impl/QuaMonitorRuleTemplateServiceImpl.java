package com.idss.datalake.datagovern.dataquality.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.HttpUtils;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRule;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleTemplate;
import com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorRuleMapper;
import com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorRuleTemplateMapper;
import com.idss.datalake.datagovern.dataquality.model.ReportColumnInfo;
import com.idss.datalake.datagovern.dataquality.model.ReportColumnVo;
import com.idss.datalake.datagovern.dataquality.model.ReportJobVo;
import com.idss.datalake.datagovern.dataquality.model.ReportRequestDto;
import com.idss.datalake.datagovern.dataquality.model.ReportRuleVo;
import com.idss.datalake.datagovern.dataquality.model.RuleRequestDto;
import com.idss.datalake.datagovern.dataquality.model.SelectVo;
import com.idss.datalake.datagovern.dataquality.model.TaskReport;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorRuleService;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorRuleTemplateService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTableDto;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据质量-模型管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Service
public class QuaMonitorRuleTemplateServiceImpl extends ServiceImpl<QuaMonitorRuleTemplateMapper, QuaMonitorRuleTemplate> implements IQuaMonitorRuleTemplateService {

    @Autowired
    private IQuaMonitorRuleService ruleService;

    @Autowired
    private QuaMonitorRuleMapper monitorRuleMapper;

    @Autowired
    private QuaMonitorRuleTemplateMapper ruleTemplateMapper;

    @Autowired
    private QuaWabElementService elementService;
    @Autowired
    private IQuaMonitorRuleService monitorRuleService;

    @Value("${data.collect.url}")
    private String collectUrl;
    private static final String CHECK_EXEC_RULE_DATA = "/monitor/checkExecRuleData";

    @Override
    public BasePageResponse<List<QuaMonitorRuleTemplate>> queryRulePage(RuleRequestDto requestDto) {
        List<QuaMonitorRuleTemplate> list = new ArrayList<>();
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<QuaMonitorRuleTemplate> page = monitorRuleMapper.queryRulePage(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
    }

    @Override
    public void addOrUpdate(QuaMonitorRuleTemplate ruleTemplate) throws Exception {
        checkFilterByDatetime(ruleTemplate);
        String reqUrl = collectUrl + CHECK_EXEC_RULE_DATA;
        QuaMonitorRuleTemplate template = new QuaMonitorRuleTemplate();
        BeanUtil.copyProperties(ruleTemplate, template);
        if (StringUtils.isBlank(template.getColumnName())) {
            RuleRequestDto requestDto = new RuleRequestDto();
            requestDto.setModelId(template.getModelId());
            requestDto.setTableName(template.getTableName());
            List<SelectVo> columnVos = monitorRuleService.queryColumns(requestDto);
            // 将columnVos的code用英文逗号拼接
            String columnNames = columnVos.stream().map(x -> x.getValue()).collect(Collectors.joining(","));
            template.setColumnName(columnNames);
        }
        String execResult = HttpUtils.sendPost(reqUrl, JSONUtil.toJsonStr(template));
        if ("false".equals(execResult)) {
            throw new ParamInvalidException("当前数据源配置不存在符合条件的数据，请修改时间字段或者时间范围");
        }

        UserValueObject uvo = UmsUtils.getUVO();
        LocalDateTime now = LocalDateTime.now();
        if (ruleTemplate.getId() == null) {
            // 新增规则
            ruleTemplate.setCreateUser(uvo.getUserName());
            ruleTemplate.setCreateTime(now);
            ruleTemplate.setUpdateTime(now);
            ruleTemplate.setUpdateUser(uvo.getUserName());
            ruleTemplate.setTenantId(Long.valueOf(uvo.getTenantId()));
            this.save(ruleTemplate);

            // 更新字段信息
            ruleService.update(new LambdaUpdateWrapper<QuaMonitorRule>()
                    .set(QuaMonitorRule::getTemplateId, ruleTemplate.getId())
                    .eq(QuaMonitorRule::getTemplateId, ruleTemplate.getVirtualId()));
        } else {
            //  如果规则被使用，则无法编辑
            /*if (ruleIsUsed(ruleTemplate.getId())) {
                throw new ParamInvalidException("规则正被使用,无法编辑");
            }*/

            // 编辑规则
            ruleTemplate.setUpdateUser(uvo.getUserName());
            ruleTemplate.setUpdateTime(now);
            this.updateById(ruleTemplate);
        }
    }

    private static void checkFilterByDatetime(QuaMonitorRuleTemplate ruleTemplate) {
        if (ruleTemplate.getFilterByDatetime() != null && ruleTemplate.getFilterByDatetime() == 1) {
            ruleTemplate.setDataFilter("0");
            ruleTemplate.setTimeRangeStart("");
            ruleTemplate.setTimeRangeEnd("");
            ruleTemplate.setDataTime("");
            ruleTemplate.setDatetimeFunction("");
        }
    }

    @Override
    public void addCancel(QuaMonitorRuleTemplate ruleTemplate) throws Exception {
        ruleService.remove(new LambdaQueryWrapper<QuaMonitorRule>()
                .eq(QuaMonitorRule::getTemplateId, ruleTemplate.getVirtualId()));
    }

    @Override
    public QuaMonitorRuleTemplate queryRuleDetail(Long id) {
        return this.getById(id);
    }

    @Override
    public void delete(RuleRequestDto requestDto) throws Exception {
        for (Long id : requestDto.getIds()) {
            if (ruleIsUsed(id)) {
                throw new ParamInvalidException("规则正被使用,无法删除");
            }
        }

        this.update(new LambdaUpdateWrapper<QuaMonitorRuleTemplate>()
                .set(QuaMonitorRuleTemplate::getDeleteFlag, "1")
                .in(QuaMonitorRuleTemplate::getId, requestDto.getIds()));
    }

    @Override
    public BasePageResponse<List<TaskReport>> report(ReportRequestDto requestDto) throws Exception {
        List<TaskReport> taskReports = new ArrayList<>();
        Page<NodeTableDto> tables = null;
        QuaWabElement element = elementService.getById(requestDto.getElementId());
        String ipPort = "";
        if ("TABLE".equals(requestDto.getType())) {
            // 查询clickhouse表相关的字段信息
            //加载某个CH下的某个DB的table列表,,需传 dbId,snapshootVersion
            ipPort = element.getChIp() + ":" + element.getChPort();
            PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
            tables = ruleTemplateMapper.getCHTables(requestDto);
        } else if ("MYSQL_TABLE".equals(requestDto.getType())) {
            //加载某个CH下的某个DB的table列表,,需传 dbId,snapshootVersion
            ipPort = element.getChIp() + ":" + element.getChPort();
            PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
            tables = ruleTemplateMapper.getMysqlTables(requestDto);
        } else if ("INDEX".equals(requestDto.getType())) {
            //加载某个ES下的索引列表,需传 elementId,snapshootVersion
            ipPort = element.getEsIpPort();
            PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
            tables = ruleTemplateMapper.getIndexes(requestDto);
        } else if ("HIVE_TABLE".equals(requestDto.getType())) {
            //加载某个CH下的某个DB的table列表,,需传 dbId,snapshootVersion
            if (StringUtils.isNotEmpty(element.getChIp()) && element.getChPort() != null) {
                ipPort = element.getChIp() + ":" + element.getChPort();
            }
            PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
            tables = ruleTemplateMapper.getHiveTables(requestDto);
        }

        if (tables != null) {
            if (!tables.isEmpty()) {
                List<NodeTableDto> tableNodes = tables.getResult();
                List<String> tableNames = tableNodes.stream().map(data -> data.getTableName()).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(tableNames)) {
                    requestDto.setTableNames(tableNames);
                    requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
                    // 获取稽核字段数、稽核模板数、模型名称
                    Map<String, ReportRuleVo> reportRuleMap = new HashMap<>();
                    List<String> templateIds = new ArrayList<>();
                    List<ReportRuleVo> reportRuleVos = ruleTemplateMapper.reportRule(requestDto);
                    if (CollectionUtils.isNotEmpty(reportRuleVos)) {
                        for (ReportRuleVo reportRuleVo : reportRuleVos) {
                            String tableName = reportRuleVo.getTableName();
                            if (reportRuleMap.containsKey(tableName)) {
                                ReportRuleVo ruleVo = reportRuleMap.get(tableName);
                                String columnName = reportRuleVo.getColumnName();
                                if (StringUtils.isNotEmpty(columnName)) {
                                    String[] columns = columnName.split(",");
                                    for (String column : columns) {
                                        ruleVo.getColumns().add(column);
                                    }
                                }
                                ruleVo.setRuleCount(ruleVo.getRuleCount() + 1);
                            } else {
                                templateIds.add(String.valueOf(reportRuleVo.getTemplateId()));
                                String columnName = reportRuleVo.getColumnName();
                                Set<String> columnSet = new HashSet<>();
                                reportRuleVo.setColumns(columnSet);
                                if (StringUtils.isNotEmpty(columnName)) {
                                    String[] columns = columnName.split(",");
                                    for (String column : columns) {
                                        columnSet.add(column);
                                    }
                                }
                                reportRuleVo.setRuleCount(reportRuleVo.getRuleCount() + 1);
                                reportRuleMap.put(tableName, reportRuleVo);
                            }
                        }
                    }
                    // 获取稽核次数、评分、稽核时间
                    Map<String, ReportJobVo> reportJobMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(templateIds)) {
                        requestDto.setTemplateIds(templateIds);
                        List<ReportJobVo> jobVos = ruleTemplateMapper.reportJob(requestDto);
                        if (CollectionUtils.isNotEmpty(jobVos)) {
                            for (ReportJobVo reportJobVo : jobVos) {
                                String jobRules = reportJobVo.getJobRules();
                                if (reportJobMap.containsKey(jobRules)) {
                                    ReportJobVo jobVo = reportJobMap.get(jobRules);
                                    jobVo.setCheckCnt(jobVo.getCheckCnt() + 1);
                                } else {
                                    reportJobVo.setCheckCnt(reportJobVo.getCheckCnt() + 1);
                                    reportJobMap.put(jobRules, reportJobVo);
                                }
                            }
                        }
                    }

                    // 质量报告信息
                    for (NodeTableDto tableNode : tableNodes) {
                        TaskReport taskReport = new TaskReport();
                        taskReport.setId(tableNode.getTableId());
                        taskReport.setTableName(tableNode.getTableName());
                        taskReport.setColumnCnt(tableNode.getCnt());
                        taskReport.setDbName(requestDto.getDbName());
                        taskReport.setIpPort(ipPort);
                        ReportRuleVo reportRuleVo = reportRuleMap.get(tableNode.getTableName());
                        if (reportRuleVo != null) {
                            taskReport.setTemplateId(String.valueOf(reportRuleVo.getTemplateId()));
                            taskReport.setCheckColumnCnt(reportRuleVo.getColumns().size());
                            taskReport.setCheckTemplateCnt(reportRuleVo.getRuleCount());
                            taskReport.setRuleTypeName(reportRuleVo.getTemplateName());
                            ReportJobVo reportJobVo = reportJobMap.get(String.valueOf(reportRuleVo.getTemplateId()));
                            if (reportJobVo != null) {
                                taskReport.setCheckCnt(reportJobVo.getCheckCnt());
                                taskReport.setScore(reportJobVo.getMonitorScore());
                                taskReport.setCheckTime(reportJobVo.getCreateTime());
                                taskReport.setTaskId(reportJobVo.getId());
                            }
                        }

                        taskReports.add(taskReport);
                    }
                }
            }
        }
        return new BasePageResponse<>(tables.getPages(), tables.getPageNum(), tables.getPageSize(), tables.getTotal(), taskReports);
    }

    @Override
    public BasePageResponse<List<ReportColumnInfo>> reportTable(ReportRequestDto requestDto) throws Exception {
        List<ReportColumnVo> columnVos = ruleTemplateMapper.ruleTemplate(requestDto);
        List<String> ruleColumnNames = new ArrayList<>();
        Map<String, ReportColumnVo> columnRuleMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(columnVos)) {
            for (ReportColumnVo columnVo : columnVos) {
                String columnName = columnVo.getColumnName();
                if (StringUtils.isNotEmpty(columnName)) {
                    String[] columnNames = columnName.split(",");
                    for (String name : columnNames) {
                        if (!ruleColumnNames.contains(name)) {
                            ruleColumnNames.add(name);
                        }
                        if (columnRuleMap.containsKey(name)) {
                            ReportColumnVo vo = columnRuleMap.get(name);
                            vo.getRuleTypeNames().add(columnVo.getRuleTypeName());
                            vo.setRuleTypeCnt(vo.getRuleTypeCnt() + 1);
                        } else {
                            Set<String> ruleTypeNames = new HashSet<>();
                            columnVo.setRuleTypeNames(ruleTypeNames);
                            ruleTypeNames.add(columnVo.getRuleTypeName());
                            columnVo.setRuleTypeCnt(columnVo.getRuleTypeCnt() + 1);
                            columnRuleMap.put(name, columnVo);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(ruleColumnNames)) {
            requestDto.setFields(StringUtils.join(ruleColumnNames, ","));
        }
        if (StringUtils.isNotEmpty(requestDto.getRuleTypeName())) {
            requestDto.setColumnNames(ruleColumnNames);
        }
        Page<ReportColumnInfo> columnInfos = null;
        if ("TABLE".equals(requestDto.getType())) {
            // 查询clickhouse表相关的字段信息
            //加载某个CH下的某个DB的table列表,,需传 dbId,snapshootVersion
            PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
            columnInfos = ruleTemplateMapper.queryCHColumn(requestDto);
        } else if ("MYSQL_TABLE".equals(requestDto.getType())) {
            //加载某个CH下的某个DB的table列表,,需传 dbId,snapshootVersion
            PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
            columnInfos = ruleTemplateMapper.queryMysqlColumn(requestDto);
        } else if ("INDEX".equals(requestDto.getType())) {
            //加载某个ES下的索引列表,需传 elementId,snapshootVersion
            PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
            columnInfos = ruleTemplateMapper.queryESColumn(requestDto);
        } else if ("HIVE_TABLE".equals(requestDto.getType())) {
            //加载某个CH下的某个DB的table列表,,需传 dbId,snapshootVersion
            PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
            columnInfos = ruleTemplateMapper.queryHiveColumn(requestDto);
        }

        if (columnInfos != null) {
            if (!columnInfos.isEmpty()) {
                List<ReportColumnInfo> columnInfoList = columnInfos.getResult();
                for (ReportColumnInfo reportColumnInfo : columnInfoList) {
                    String columnName = reportColumnInfo.getColumnName();
                    ReportColumnVo columnVo = columnRuleMap.get(columnName);
                    if (columnVo != null) {
                        reportColumnInfo.setRuleTypeCnt(columnVo.getRuleTypeCnt());
                        reportColumnInfo.setRuleTypeName(StringUtils.join(columnVo.getRuleTypeNames(), ","));
                    }
                }
            }
        }

        if (columnInfos == null || columnInfos.isEmpty()) {
            return new BasePageResponse<>(columnInfos.getPages(), columnInfos.getPageNum(), columnInfos.getPageSize(), columnInfos.getTotal(),
                    new ArrayList<>());
        }
        return new BasePageResponse<>(columnInfos.getPages(), columnInfos.getPageNum(), columnInfos.getPageSize(), columnInfos.getTotal(),
                columnInfos.getResult());
    }

    /**
     * 判断模型是否被使用
     *
     * @param id
     * @return
     */
    private boolean ruleIsUsed(Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("ruleId", id);
        List<Map<String, Object>> list = monitorRuleMapper.existRule(params);
        if (CollectionUtils.isNotEmpty(list)) {
            return true;
        } else {
            return false;
        }
    }
}
