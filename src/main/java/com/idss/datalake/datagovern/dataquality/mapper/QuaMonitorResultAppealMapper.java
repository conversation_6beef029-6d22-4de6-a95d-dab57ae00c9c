package com.idss.datalake.datagovern.dataquality.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorResultAppeal;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.dataquality.model.AppealDto;
import com.idss.datalake.datagovern.dataquality.model.JobRequestDto;
import com.idss.datalake.datagovern.dataquality.model.JobResponseVo;

/**
 * <p>
 * 质量检测结果申诉 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
public interface QuaMonitorResultAppealMapper extends BaseMapper<QuaMonitorResultAppeal> {
    Page<QuaMonitorResultAppeal> queryPage(AppealDto requestDto);
}
