package com.idss.datalake.datagovern.dataquality.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.datagovern.dataquality.dto.InternalModelPageRequest;
import com.idss.datalake.datagovern.dataquality.entity.QuaInternalModel;
import com.idss.datalake.datagovern.dataquality.mapper.QuaInternalModelMapper;
import com.idss.datalake.datagovern.dataquality.service.IQuaInternalModelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.radar.util.UmsUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 数据质量内置模版 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-13
 */
@Service
public class QuaInternalModelServiceImpl extends ServiceImpl<QuaInternalModelMapper, QuaInternalModel> implements IQuaInternalModelService {
    @Resource
    private QuaInternalModelMapper quaInternalModelMapper;
    @Override
    public BasePageResponse<List<QuaInternalModel>> page(InternalModelPageRequest requestDto) {
        List<QuaInternalModel> list = new ArrayList<>();
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<QuaInternalModel> page = quaInternalModelMapper.page(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
    }
}
