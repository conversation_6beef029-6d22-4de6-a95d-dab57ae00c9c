package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据质量动态阈值管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QuaDyncThreshold implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String jobName;

    /**
     * 数据源类型: mysql,clickhouse,elasticsearch,hive
     */
    private String sourceType;

    /**
     * 元数据ID
     */
    private Integer elementId;

    private String dbName;

    private String tableName;

    private String indexName;

    /**
     * 计算模型: MA , ARIMA ,EWMA
     */
    private String modelType;

    /**
     * 预测对象: 行数 line , 空间 space
     */
    private String forecastTarget;

    /**
     * 备注
     */
    private String remark;

    /**
     * 最新预测值
     */
    private Long forecastValue;

    /**
     * 运行状态: 1运行，0停止
     */
    private Integer status;

    private Integer tenantId;

    private String createUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String updateUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;


}
