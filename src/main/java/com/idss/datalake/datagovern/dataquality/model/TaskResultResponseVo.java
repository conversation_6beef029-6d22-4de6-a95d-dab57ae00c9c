/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/16 16:25
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Copyright 2022 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/16 16:25
 */
@Data
public class TaskResultResponseVo {

    private Long id;

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "任务描述")
    private String jobDesc;

    @ApiModelProperty(value = "执行周期 01:一次性任务;02:周期任务")
    private String executeCycle;

    @ApiModelProperty(value = "IP地址")
    private String ip;

    @ApiModelProperty(value = "库名")
    private String databaseName;

    @ApiModelProperty(value = "任务状态")
    private String status;

    @ApiModelProperty(value = "任务结果")
    private String taskResult;

    @ApiModelProperty(value = "任务分数")
    private String monitorScore;

    private List<Pie> pie;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    @ApiModelProperty(value = "规则个数")
    private Integer ruleCnt;

    @Data
    public static class Pie {

        private String name;

        private Integer value;
    }
}
