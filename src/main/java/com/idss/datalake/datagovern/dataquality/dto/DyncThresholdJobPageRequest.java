/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/7/13
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/7/13
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.dataquality.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2024/7/13
 */
@Data
public class DyncThresholdJobPageRequest extends BasePageRequest {
    private Long id;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    /**
     * 运行结果: 1成功，0失败
     */
    private Integer result;
}
