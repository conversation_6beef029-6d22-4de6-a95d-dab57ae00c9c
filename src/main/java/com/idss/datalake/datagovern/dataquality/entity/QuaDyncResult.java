package com.idss.datalake.datagovern.dataquality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 动态阈值结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QuaDyncResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 动态阈值ID
     */
    private Integer dyncId;

    /**
     * 运行结果: 1成功，0失败
     */
    private Integer result;

    /**
     * 失败原因
     */
    private String failError;

    /**
     * 时间行数
     */
    private Long line;

    /**
     * 预测行数值
     */
    private Long forecastLine;

    /**
     * 实际空间  byte单位
     */
    private Long space;

    /**
     * 预测空间值 byte单位
     */
    private Long forecastSpace;

    private LocalDateTime createTime;

    /**
     * 行数日变化
     */
    private Long dayLineChange;
    /**
     * 预测行数日变化
     */
    private Long forecastDayLineChange;
    /**
     * 空间日变化
     */
    private Long daySpaceChange;
    /**
     * 预测空间日变化
     */
    private Long forecastDaySpaceChange;


}
