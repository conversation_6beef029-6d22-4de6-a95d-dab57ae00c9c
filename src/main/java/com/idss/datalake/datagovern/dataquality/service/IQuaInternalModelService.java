package com.idss.datalake.datagovern.dataquality.service;

import com.idss.datalake.datagovern.dataquality.dto.InternalModelPageRequest;
import com.idss.datalake.datagovern.dataquality.entity.QuaInternalModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import java.util.List;

/**
 * <p>
 * 数据质量内置模版 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-13
 */
public interface IQuaInternalModelService extends IService<QuaInternalModel> {
    BasePageResponse<List<QuaInternalModel>> page(InternalModelPageRequest requestDto);
}
