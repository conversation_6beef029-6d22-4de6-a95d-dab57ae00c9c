/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/16 16:21
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datagovern.dataquality.model;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Copyright 2022 IDSS
 * <p> 监测模型查询条件
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/16 16:21
 */
@Data
public class ModelRequestDto extends BasePageRequest {

    @ApiModelProperty("模型名称")
    private String modelName;

    @ApiModelProperty("模型描述")
    private String modelDesc;

    @ApiModelProperty("资源ID")
    private Long elementId;

    @ApiModelProperty("资源类型")
    private String elementType;

    @ApiModelProperty("数据库ID")
    private Long databaseId;

    @ApiModelProperty("数据库名称")
    private String databaseName;

    private String snapshootVersion;

    @ApiModelProperty("表")
    private List<ModelTable> tables;

    private Long id;

    private List<Long> ids;

    /**
     * 0-关闭 1-启用
     */
    private String openStatus;

    private String taskName;

    private String startTime;

    private String endTime;
}
