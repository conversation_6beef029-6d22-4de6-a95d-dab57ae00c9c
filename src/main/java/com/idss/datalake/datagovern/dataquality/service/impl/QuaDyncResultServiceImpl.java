package com.idss.datalake.datagovern.dataquality.service.impl;

import com.idss.datalake.datagovern.dataquality.entity.QuaDyncResult;
import com.idss.datalake.datagovern.dataquality.mapper.QuaDyncResultMapper;
import com.idss.datalake.datagovern.dataquality.service.IQuaDyncResultService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 动态阈值结果表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Service
public class QuaDyncResultServiceImpl extends ServiceImpl<QuaDyncResultMapper, QuaDyncResult> implements IQuaDyncResultService {

}
