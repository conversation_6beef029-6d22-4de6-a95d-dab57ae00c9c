package com.idss.datalake.datagovern.dataquality.service.impl;

import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleExecRecord;
import com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorRuleExecRecordMapper;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorRuleExecRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 模型试跑记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-03
 */
@Service
public class QuaMonitorRuleExecRecordServiceImpl extends ServiceImpl<QuaMonitorRuleExecRecordMapper, QuaMonitorRuleExecRecord> implements IQuaMonitorRuleExecRecordService {

}
