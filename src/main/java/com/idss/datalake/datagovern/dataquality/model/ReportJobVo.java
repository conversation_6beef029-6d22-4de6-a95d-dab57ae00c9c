package com.idss.datalake.datagovern.dataquality.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

@Data
public class ReportJobVo {

    private Long id;

    private String jobRules;

    private String monitorScore;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private Integer checkCnt = 0;
}
