package com.idss.datalake.datagovern.dataquality.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.dataquality.model.ModelRequestDto;
import com.idss.datalake.datagovern.dataquality.model.ModelResponseVo;
import io.swagger.models.auth.In;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 质量监测模型 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
public interface QuaMonitorModelMapper extends BaseMapper<QuaMonitorModel> {

    Page<ModelResponseVo> queryModelPage(ModelRequestDto requestDto);

    List<Map<String, Object>> taskTrend(Map<String, Object> params);

    Integer warnCount(Map<String, Object> params);

    List<Map<String, Object>> warnRisk(Map<String, Object> params);

    List<Map<String, Object>> jobTrend(Map<String, Object> params);
}
