package com.idss.datalake.datagovern.dataquality.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorWeight;
import com.idss.datalake.datagovern.dataquality.model.RuleRequestDto;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorWeightService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.annotation.RequestScope;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 数据质量-权重管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@RestController
@Slf4j
@RequestMapping(Constant.API_PREFIX + "/dataQuality/rule/weight")
public class QuaMonitorWeightController {

    @Autowired
    private IQuaMonitorWeightService monitorWeightService;

    @PostMapping("/page")
    public BasePageResponse<List<QuaMonitorWeight>> page(@RequestBody RuleRequestDto requestDto) {
        try {
            return monitorWeightService.queryPage(requestDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable Long id) {
        try {
            QuaMonitorWeight weight = monitorWeightService.getById(id);
            return ResultBean.success(weight, "查询成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @PostMapping("/edit")
    public ResultBean edit(@RequestBody QuaMonitorWeight monitorWeight) {
        try {
            UserValueObject uvo = UmsUtils.getUVO();
            monitorWeight.setUpdateUser(uvo.getUserName());
            monitorWeight.setUpdateTime(LocalDateTime.now());
            monitorWeightService.updateById(monitorWeight);
            return ResultBean.success("编辑成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("编辑失败");
        }
    }
}
