package com.idss.datalake.datagovern.datastandard.enums;

/**
 * <AUTHOR>
 * @description <p>数据精度</p>
 * @see
 * @since 2022-08-20
 */
public enum DataPrecisionEnum {
    precision_1(1, "1位"),
    precision_2(2, "2位"),
    precision_3(3, "3位"),
    precision_4(4, "4位"),
    precision_5(5, "5位");

    DataPrecisionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private int code;
    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
