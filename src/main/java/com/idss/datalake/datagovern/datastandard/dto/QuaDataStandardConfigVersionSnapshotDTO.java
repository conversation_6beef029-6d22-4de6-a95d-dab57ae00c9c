/************************ CHAN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-08-25
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-08-25
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.dto;

import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardConfigVersionSnapshot;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>数据标准配置多版本记录 dto类</p>
 * @since 2023-08-25
 */
@Data
public class QuaDataStandardConfigVersionSnapshotDTO extends QuaDataStandardConfigVersionSnapshot {
    private List<Long> ids;
}