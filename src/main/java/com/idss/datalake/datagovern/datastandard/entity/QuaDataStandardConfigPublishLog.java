/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-10-30
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-10-30
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>数据标准发布记录</p>
 * @since 2023-10-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuaDataStandardConfigPublishLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标准主键id
     */
    private Long configId;

    /**
     * 0有效，1无效
     */
    private Integer flag;

    /**
     * 发布状态，0未发布，1已发布,2审核中
     */
    private Integer publishStatus;

    /**
     * 发布名称
     */
    private String publishName;

    /**
     * 发布描述
     */
    private String publishDesc;

    /**
     * 发布时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    /**
     * 发布人
     */
    private String publishUser;

    /**
     * 审批状态，0待审批 1同意 2拒绝
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer approvalStatus;

    /**
     * 审批说明
     */
    private String approvalDesc;

    /**
     * 审批时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approvalTime;

    /**
     * 审批人
     */
    private String approvalUser;
}
