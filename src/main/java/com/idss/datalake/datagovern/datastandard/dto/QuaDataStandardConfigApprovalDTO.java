/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-08-25
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-08-25
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>数据标准版本审批 dto类</p>
 * @since 2023-08-25
 */
@Data
public class QuaDataStandardConfigApprovalDTO {
    /**
     * 审批状态，0待审批 1同意 2拒绝
     */
    private Integer approvalStatus;

    /**
     * 审批说明
     */
    private String approvalDesc;

    /**
     * 数据标准id集合
     */
    private List<Long> configIds;

    private String publishName;
}