package com.idss.datalake.datagovern.datastandard.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.idss.datalake.datagovern.datastandard.dto.QuaDataStandardConfigPublishLogDTO;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardConfigPublishLog;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 数据标准发布记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
public interface QuaDataStandardConfigPublishLogMapper extends BaseMapper<QuaDataStandardConfigPublishLog> {

    @Select("SELECT pl.*, conf.cn_name FROM qua_data_standard_config_publish_log pl " +
            "left join qua_data_standard_config conf on pl.config_id = conf.id ${ew.customSqlSegment}")
    IPage<QuaDataStandardConfigPublishLogDTO> getPublishPage(IPage<QuaDataStandardConfigPublishLog> page,
                                                             @Param(Constants.WRAPPER) QueryWrapper<QuaDataStandardConfigPublishLog> wrapper);

}
