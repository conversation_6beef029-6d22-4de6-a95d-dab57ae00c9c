package com.idss.datalake.datagovern.datastandard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据标准字典
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QuaDataStandardDic implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典名称
     */
    private String dicName;

    /**
     * 字典编号
     */
    private String dicCode;

    /**
     * 类型
     */
    private String dicType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 字段配置: [{"value":"","cnName":"","remark":""}]
     */
    private String dicConfig;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String createUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private String updateUser;

    /**
     * 租户ID
     */
    private Integer tenantId;

    @TableField(exist = false)
    private List<Integer> ids;
}
