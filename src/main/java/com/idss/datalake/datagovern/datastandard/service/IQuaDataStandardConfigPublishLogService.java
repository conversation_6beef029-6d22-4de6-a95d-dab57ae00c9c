package com.idss.datalake.datagovern.datastandard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.datastandard.dto.QuaDataStandardConfigPublishLogDTO;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardConfigPublishLog;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 数据标准发布记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
public interface IQuaDataStandardConfigPublishLogService extends IService<QuaDataStandardConfigPublishLog> {
    IPage<QuaDataStandardConfigPublishLogDTO> getPublishPage(IPage<QuaDataStandardConfigPublishLog> page,
                                                             @Param(Constants.WRAPPER) QueryWrapper<QuaDataStandardConfigPublishLog> wrapper);

}
