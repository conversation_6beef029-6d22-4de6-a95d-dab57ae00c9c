package com.idss.datalake.datagovern.datastandard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.datastandard.dto.QuaDataStandardConfigRestoreLogDTO;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardConfigRestoreLog;
import com.idss.datalake.datashare.zeppelin.dto.NotebookDTO;
import com.idss.datalake.datashare.zeppelin.entity.ZeppelinNotebook;

/**
 * <p>
 * 数据标准版本恢复记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
public interface IQuaDataStandardConfigRestoreLogService extends IService<QuaDataStandardConfigRestoreLog> {

    /**
     * 分页查询
     *
     * @param page
     * @param wrapper
     * @return
     */
    IPage<QuaDataStandardConfigRestoreLogDTO> getPage(Page<QuaDataStandardConfigRestoreLog> page,
                                                      QueryWrapper<QuaDataStandardConfigRestoreLog> wrapper);
}
