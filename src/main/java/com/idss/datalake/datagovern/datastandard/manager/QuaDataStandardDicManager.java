/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-10-22
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-10-22
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardDic;
import com.idss.datalake.datagovern.datastandard.service.IQuaDataStandardDicService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>资产目录 manager处理类</p>
 * @since 2022-10-22
 */
@Component
public class QuaDataStandardDicManager {
    private static final Logger logger = LoggerFactory.getLogger(QuaDataStandardDicManager.class);

    @Autowired
    private IQuaDataStandardDicService quaDataStandardDicService;

    public Map<String, Object> createPageCondition(RequestDTO requestDTO) throws ParamInvalidException {
        if (requestDTO != null && requestDTO.getGlobal() != null) {
            GlobalDTO globalDTO = requestDTO.getGlobal();
            if (globalDTO.getPageNum() == null) {
                globalDTO.setPageNum(Constant.DEFAULT_PAGE_NUM);
            }
            if (globalDTO.getPageSize() == null) {
                globalDTO.setPageSize(Constant.DEFAULT_PAGE_SIZE);
            }
            if (StringUtils.isEmpty(globalDTO.getOrderType())) {
                globalDTO.setOrderType("desc");
            }
            if (StringUtils.isEmpty(globalDTO.getOrderField())) {
                globalDTO.setOrderField("createTime");
            }
        } else {
            throw new ParamInvalidException("入参错误");
        }
        return null;
    }

    /**
     * 分页
     *
     * @param requestDTO
     * @return
     * @throws Exception
     */
    public Map<String, Object> page(RequestDTO requestDTO) throws Exception {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<QuaDataStandardDic> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<QuaDataStandardDic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UserUtil.getCurrentTenantId());
        if (requestDTO.getParam() != null) {
            QuaDataStandardDic dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), QuaDataStandardDic.class);
            if (StringUtils.isNotBlank(dto.getDicName())) {
                queryWrapper.like("dic_name", dto.getDicName());
            }
        }
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }

        IPage<QuaDataStandardDic> pageResult = quaDataStandardDicService.page(page, queryWrapper);

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", pageResult.getRecords());
        return result;
    }

    public void create(QuaDataStandardDic quaDataStandardDic) throws ParamInvalidException {
        if (StringUtils.isBlank(quaDataStandardDic.getDicName())) {
            throw new ParamInvalidException("入参异常");
        }

        //保存
        quaDataStandardDic.setTenantId(UserUtil.getCurrentTenantId());
        quaDataStandardDic.setCreateUser(UserUtil.getCurrentUsername());
        quaDataStandardDic.setUpdateUser(UserUtil.getCurrentUsername());
        quaDataStandardDicService.save(quaDataStandardDic);
    }

    public void delete(List<Integer> ids) throws ParamInvalidException {
        Integer tenantId = UserUtil.getCurrentTenantId();
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        quaDataStandardDicService.remove(new QueryWrapper<QuaDataStandardDic>().in("id", ids).eq("tenant_id", tenantId));
    }

    public void edit(QuaDataStandardDic quaDataStandardDic) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(quaDataStandardDic.getId())) {
            throw new ParamInvalidException("入参异常");
        }

        QuaDataStandardDic dbOne = quaDataStandardDicService.getById(quaDataStandardDic.getId());
        ReflectionUtil.copyLomBokProperties(quaDataStandardDic, dbOne);
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        quaDataStandardDicService.saveOrUpdate(dbOne);
    }

    public QuaDataStandardDic detail(Integer id) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        QueryWrapper<QuaDataStandardDic> wrapper = new QueryWrapper();
        wrapper.eq("tenant_id", UserUtil.getCurrentTenantId()).eq("id", id);
        return quaDataStandardDicService.getOne(wrapper);
    }
}