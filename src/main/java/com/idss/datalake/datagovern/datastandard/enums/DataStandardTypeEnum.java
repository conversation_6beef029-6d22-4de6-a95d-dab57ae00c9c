package com.idss.datalake.datagovern.datastandard.enums;

/**
 * <AUTHOR>
 * @description <p>数据标准类型</p>
 * @see
 * @since 2022-08-20
 */
public enum DataStandardTypeEnum {
    data_length(1, "数据长度"),
    data_column_type(2, "数据类型"),
    data_precision(3, "数据精度"),
    cn_en_verification(4, "中英文"),
    custom_content(5, "自定义");

    DataStandardTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private int code;
    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescription(int code) {
        for (DataStandardTypeEnum value : DataStandardTypeEnum.values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return "";
    }

    public static int getCode(String desc) {
        for (DataStandardTypeEnum value : DataStandardTypeEnum.values()) {
            if (value.getDesc().equals(desc)) {
                return value.getCode();
            }
        }
        throw new RuntimeException("数据标准不存在：" + desc);
    }
}
