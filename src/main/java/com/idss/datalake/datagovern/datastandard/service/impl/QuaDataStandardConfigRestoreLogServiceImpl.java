package com.idss.datalake.datagovern.datastandard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.datastandard.dto.QuaDataStandardConfigRestoreLogDTO;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardConfigRestoreLog;
import com.idss.datalake.datagovern.datastandard.mapper.QuaDataStandardConfigRestoreLogMapper;
import com.idss.datalake.datagovern.datastandard.service.IQuaDataStandardConfigRestoreLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 数据标准版本恢复记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@Service
public class QuaDataStandardConfigRestoreLogServiceImpl extends ServiceImpl<QuaDataStandardConfigRestoreLogMapper, QuaDataStandardConfigRestoreLog> implements IQuaDataStandardConfigRestoreLogService {
    @Resource
    private QuaDataStandardConfigRestoreLogMapper restoreLogMapper;

    @Override
    public IPage<QuaDataStandardConfigRestoreLogDTO> getPage(Page<QuaDataStandardConfigRestoreLog> page,
                                                             QueryWrapper<QuaDataStandardConfigRestoreLog> wrapper) {
        return restoreLogMapper.getPage(page, wrapper);
    }
}
