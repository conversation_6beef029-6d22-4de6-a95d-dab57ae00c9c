/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-08-25
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-08-25
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>数据标准配置版本快照</p>
 * @since 2023-08-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QuaDataStandardConfigVersionSnapshot implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标准中文名
     */
    private String cnName;

    /**
     * 标准英文名
     */
    private String enName;

    /**
     * 标准编号
     */
    private String code;

    /**
     * 业务含义
     */
    private String businessMeaning;

    /**
     * 制定依据
     */
    private String accordance;

    /**
     * 类型，枚举值：1数据长度、2数据类型、3数据精度、4中英文、5自定义
     */
    private Integer standardType;

    /**
     * 数据长度
     */
    private Integer dataLength;

    /**
     * 数据类型
     */
    private String dataColumnType;

    /**
     * 小数长度，1、2、3、4、5
     */
    private Integer dataPrecision;

    /**
     * 中英文校验,1：仅包括中文 2：仅包含英文 3：包含中英文
     */
    private Integer cnEnVerification;

    /**
     * 自定义编辑
     */
    private String customContent;

    /**
     * 备注
     */
    private String remark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private String updateUser;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 标准集id
     */
    private Long setId;

    /**
     * 启停，1启用，0停用
     */
    private Integer status;

    private String encrypt;

    private String dept;

    private String businessSystem;

    private String storeCycle;

    private String databaseName;

    private String sensitiveType;

    private String specification;

    /**
     * 版本号
     */
    private String versionNo;

    /**
     * 数据标准id
     */
    private Long configId;


}
