/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.dto;

import com.idss.datalake.datagovern.datastandard.entity.DataStandardGroup;
import lombok.Data;

/**
 * <AUTHOR>
 * @description <p>数据标准组 dto类</p>
 * @since 2022-08-20
 */
@Data
public class DataStandardGroupDTO extends DataStandardGroup {

}
