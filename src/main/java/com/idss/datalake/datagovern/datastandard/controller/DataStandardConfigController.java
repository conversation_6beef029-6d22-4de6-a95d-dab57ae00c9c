/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.controller;


import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.syslog.annotation.SysLog;
import com.idss.datalake.common.syslog.entity.OptModule;
import com.idss.datalake.common.syslog.entity.OptType;
import com.idss.datalake.datagovern.datastandard.dto.DataStandardConfigDTO;
import com.idss.datalake.datagovern.datastandard.dto.QuaDataStandardConfigPublishLogDTO;
import com.idss.datalake.datagovern.datastandard.dto.QuaDataStandardConfigRestoreLogDTO;
import com.idss.datalake.datagovern.datastandard.enums.CnEnEnum;
import com.idss.datalake.datagovern.datastandard.enums.DataColumnTypeEnum;
import com.idss.datalake.datagovern.datastandard.enums.DataPrecisionEnum;
import com.idss.datalake.datagovern.datastandard.enums.DataStandardTypeEnum;
import com.idss.datalake.datagovern.datastandard.manager.DataStandardConfigManager;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>数据标准配置 前端控制器</p>
 * @since 2022-08-20
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/datastandard/config")
public class DataStandardConfigController {
    private static final Logger logger = LoggerFactory.getLogger(DataStandardConfigController.class);

    @Autowired
    private DataStandardConfigManager dataStandardConfigManager;

    @SysLog(logName = "标准新增", optType = OptType.ADD, optModule = OptModule.DATA_LAKE_GOVERNANCE_STANDARD, switchRedisStatus = true)
    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody DataStandardConfigDTO dataStandardConfigDTO) {
        try {
            dataStandardConfigManager.create(dataStandardConfigDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "标准删除", optType = OptType.DELETE, optModule = OptModule.DATA_LAKE_GOVERNANCE_STANDARD, switchRedisStatus = true)
    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public ResultBean delete(@RequestBody DataStandardConfigDTO dataStandardConfigDTO) {
        try {
            dataStandardConfigManager.delete(dataStandardConfigDTO.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "标准启停", optType = OptType.START_OR_STOP, optModule = OptModule.DATA_LAKE_GOVERNANCE_STANDARD, switchRedisStatus =
            true)
    @ApiOperation(value = "启停")
    @PostMapping(value = "/enable")
    public ResultBean enable(@RequestBody DataStandardConfigDTO dataStandardConfigDTO) {
        try {
            dataStandardConfigManager.enable(dataStandardConfigDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "标准详情", optType = OptType.DETAIL, optModule = OptModule.DATA_LAKE_GOVERNANCE_STANDARD, switchRedisStatus = true)
    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Integer id) {
        try {
            return ResultBean.success(dataStandardConfigManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "标准编辑", optType = OptType.EDIT, optModule = OptModule.DATA_LAKE_GOVERNANCE_STANDARD, switchRedisStatus = true)
    @ApiOperation(value = "编辑")
    @PutMapping(value = "/edit")
    public ResultBean edit(@RequestBody DataStandardConfigDTO dataStandardConfigDTO) {
        try {
            dataStandardConfigManager.edit(dataStandardConfigDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "标准分页查询", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_STANDARD, switchRedisStatus = true)
    @ApiOperation(value = "分页查询列表")
    @PostMapping(value = "/page")
    public ResultBean page(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataStandardConfigManager.page(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "类型列表")
    @GetMapping(value = "/standardType")
    public ResultBean standardType() {
        try {
            List<Map<String, String>> list = new ArrayList<>();
            for (DataStandardTypeEnum typeEnum : DataStandardTypeEnum.values()) {
                Map map = new HashMap();
                map.put("code", typeEnum.getCode());
                map.put("desc", typeEnum.getDesc());
                list.add(map);
            }
            return ResultBean.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "数据精度列表")
    @GetMapping(value = "/dataPrecision")
    public ResultBean dataPrecision() {
        try {
            List<Map<String, String>> list = new ArrayList<>();
            for (DataPrecisionEnum typeEnum : DataPrecisionEnum.values()) {
                Map map = new HashMap();
                map.put("code", typeEnum.getCode());
                map.put("desc", typeEnum.getDesc());
                list.add(map);
            }
            return ResultBean.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "中英文校验列表")
    @GetMapping(value = "/cnEnVerification")
    public ResultBean cnEnVerification() {
        try {
            List<Map<String, String>> list = new ArrayList<>();
            for (CnEnEnum cnEnEnum : CnEnEnum.values()) {
                Map map = new HashMap();
                map.put("code", cnEnEnum.getCode());
                map.put("desc", cnEnEnum.getDesc());
                list.add(map);
            }
            return ResultBean.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "数据类型列表")
    @GetMapping(value = "/columnType")
    public ResultBean columnType() {
        try {
            return ResultBean.success(DataColumnTypeEnum.values());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 导入
     */
    @PostMapping("/import")
    public ResultBean importData(@RequestBody Map<String, Object> params) {
        try {
            dataStandardConfigManager.importData(params);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 下载模板文件
     *
     * @return
     */
    @GetMapping("/downloadTemplate")
    public void downloadTemplateFile(HttpServletResponse response) {
        try {
            dataStandardConfigManager.downloadTemplateFile(response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 下载
     *
     * @param dataStandardConfigDTO
     * @param response
     */
    @PostMapping("/download")
    public void downloadData(@RequestBody DataStandardConfigDTO dataStandardConfigDTO, HttpServletResponse response) {
        try {
            dataStandardConfigManager.downloadData(dataStandardConfigDTO, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    @ApiOperation(value = "注销")
    @PostMapping(value = "/logOff")
    public ResultBean logOff(@RequestBody DataStandardConfigDTO dto) {
        try {
            dataStandardConfigManager.logOff(dto.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "发布列表")
    @PostMapping(value = "/publishPage")
    public ResultBean publishPage(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataStandardConfigManager.publishPage(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "发布操作")
    @PostMapping(value = "/publish")
    public ResultBean publish(@RequestBody QuaDataStandardConfigPublishLogDTO dto) {
        try {
            dataStandardConfigManager.publish(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "审批列表")
    @PostMapping(value = "/approvalPage")
    public ResultBean approvalPage(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataStandardConfigManager.approvalPage(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "审批操作")
    @PostMapping(value = "/approval")
    public ResultBean approval(@RequestBody QuaDataStandardConfigPublishLogDTO dto) {
        try {
            dataStandardConfigManager.approval(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "版本恢复列表")
    @PostMapping(value = "/restoreLogPage")
    public ResultBean restoreLogPage(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataStandardConfigManager.restoreLogPage(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询未发布的标准")
    @GetMapping(value = "/unpublishedStandards/{type}")
    public ResultBean unpublishedStandards(@PathVariable("type") Integer type) {
        try {
            return ResultBean.success(dataStandardConfigManager.unpublishedStandards( type));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询多版本标准")
    @GetMapping(value = "/multiVersionStandards")
    public ResultBean multiVersionStandards() {
        try {
            return ResultBean.success(dataStandardConfigManager.multiVersionStandards());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据标准id查询非当前版本号")
    @GetMapping(value = "/queryVersionsByConfigId/{configId}")
    public ResultBean queryVersionsByConfigId(@PathVariable Long configId) {
        try {
            return ResultBean.success(dataStandardConfigManager.queryVersionsByConfigId(configId));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "新增恢复")
    @PostMapping(value = "/saveRestoreLog")
    public ResultBean saveRestoreLog(@RequestBody QuaDataStandardConfigRestoreLogDTO dto) {
        try {
            boolean success = dataStandardConfigManager.saveRestoreLog(dto);
            if (!success) {
                return ResultBean.fail("恢复失败");
            }
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

}
