/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.datastandard.dto.DataStandardGroupDTO;
import com.idss.datalake.datagovern.datastandard.entity.DataStandardConfig;
import com.idss.datalake.datagovern.datastandard.entity.DataStandardGroup;
import com.idss.datalake.datagovern.datastandard.entity.DataStandardSet;
import com.idss.datalake.datagovern.datastandard.service.IDataStandardConfigService;
import com.idss.datalake.datagovern.datastandard.service.IDataStandardGroupService;
import com.idss.datalake.datagovern.datastandard.service.IDataStandardSetService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>数据标准组 manager处理类</p>
 * @since 2022-08-20
 */
@Component
public class DataStandardGroupManager {
    private static final Logger logger = LoggerFactory.getLogger(DataStandardGroupManager.class);

    @Autowired
    private IDataStandardGroupService iDataStandardGroupService;
    @Autowired
    private IDataStandardSetService standardSetService;
    @Autowired
    private IDataStandardConfigService dataStandardConfigService;

    public Map<String, Object> createPageCondition(RequestDTO requestDTO) throws ParamInvalidException {
        if (requestDTO != null && requestDTO.getGlobal() != null) {
            GlobalDTO globalDTO = requestDTO.getGlobal();
            if (globalDTO.getPageNum() == null) {
                globalDTO.setPageNum(Constant.DEFAULT_PAGE_NUM);
            }
            if (globalDTO.getPageSize() == null) {
                globalDTO.setPageSize(Constant.DEFAULT_PAGE_SIZE);
            }
            if (StringUtils.isEmpty(globalDTO.getOrderType())) {
                globalDTO.setOrderType("desc");
            }
            if (StringUtils.isEmpty(globalDTO.getOrderField())) {
                globalDTO.setOrderField("createTime");
            }
        } else {
            throw new ParamInvalidException("入参错误");
        }
        return null;
    }

    /**
     * 分页
     *
     * @param requestDTO
     * @return
     * @throws Exception
     */
    public Map<String, Object> page(RequestDTO requestDTO) throws Exception {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<DataStandardGroup> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataStandardGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UserUtil.getCurrentTenantId());
        if (requestDTO.getParam() != null) {
            DataStandardGroupDTO dataStandardGroupDTO = JSONUtil.objectMapperToObj(requestDTO.getParam(), DataStandardGroupDTO.class);
            if (StringUtils.isNotBlank(dataStandardGroupDTO.getName())) {
                queryWrapper.eq("name", dataStandardGroupDTO.getName());
            }
        }
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }

        IPage<DataStandardGroup> pageResult = iDataStandardGroupService.page(page, queryWrapper);

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", pageResult.getRecords());
        return result;
    }

    public void create(DataStandardGroupDTO dataStandardGroupDTO) throws ParamInvalidException {
        if (StringUtils.isBlank(dataStandardGroupDTO.getName())) {
            throw new ParamInvalidException("入参异常");
        }
        List<DataStandardGroup> list = iDataStandardGroupService.list(new QueryWrapper<DataStandardGroup>()
                .eq("name", dataStandardGroupDTO.getName())
                .eq("type",dataStandardGroupDTO.getType())
                .eq("tenant_id", UserUtil.getCurrentTenantId()));
        if (CollectionUtils.isNotEmpty(list)) {
            throw new ParamInvalidException("名称已存在");
        }
        //保存
        DataStandardGroup dataStandardGroup = new DataStandardGroup();
        ReflectionUtil.copyLomBokProperties(dataStandardGroupDTO, dataStandardGroup);
        dataStandardGroup.setTenantId(Long.valueOf(UserUtil.getCurrentTenantId()));
        dataStandardGroup.setCreateTime(LocalDateTime.now());
        dataStandardGroup.setUpdateTime(LocalDateTime.now());
        dataStandardGroup.setCreateUser(UserUtil.getCurrentUsername());
        dataStandardGroup.setUpdateUser(UserUtil.getCurrentUsername());
        iDataStandardGroupService.save(dataStandardGroup);
    }

    public void delete(Long id) throws ParamInvalidException {
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        List<DataStandardSet> standardSets = standardSetService.list(new QueryWrapper<DataStandardSet>().eq("group_id", id));
        if (CollectionUtils.isNotEmpty(standardSets)) {
            throw new ParamInvalidException("分组下有标准集，不可删除");
        }
        iDataStandardGroupService.remove(new QueryWrapper<DataStandardGroup>().eq("id", id).eq("tenant_id", tenantId));
    }

    public void edit(DataStandardGroupDTO dataStandardGroupDTO) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(dataStandardGroupDTO.getId())) {
            throw new ParamInvalidException("入参异常");
        }
        List<DataStandardGroup> list = iDataStandardGroupService.list(new QueryWrapper<DataStandardGroup>()
                .eq("name", dataStandardGroupDTO.getName())
                .eq("type", dataStandardGroupDTO.getType())
                .eq("tenant_id", UserUtil.getCurrentTenantId())
                .ne("id", dataStandardGroupDTO.getId()));
        if (CollectionUtils.isNotEmpty(list)) {
            throw new ParamInvalidException("名称已存在");
        }
        DataStandardGroup dbOne = iDataStandardGroupService.getById(dataStandardGroupDTO.getId());
        ReflectionUtil.copyLomBokProperties(dataStandardGroupDTO, dbOne);
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        iDataStandardGroupService.saveOrUpdate(dbOne);
    }

    public DataStandardGroup detail(Long id) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        QueryWrapper<DataStandardGroup> wrapper = new QueryWrapper();
        wrapper.eq("tenant_id", tenantId).eq("id", id);
        return iDataStandardGroupService.getOne(wrapper);
    }

    public List<DataStandardGroup> groupTree(DataStandardGroupDTO dto) {
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        QueryWrapper<DataStandardGroup> queryWrapper = new QueryWrapper<DataStandardGroup>().eq("tenant_id", tenantId);
        queryWrapper.eq("type",dto.getType());
        if (StringUtils.isNotBlank(dto.getName())) {
            queryWrapper.like("name", dto.getName());
        }
        List<DataStandardGroup> groups = iDataStandardGroupService.list(queryWrapper);
        if (CollectionUtils.isEmpty(groups)) {
            return null;
        }
        // 查询分组下标准集
        groups.forEach(group -> {
            List<DataStandardSet> standardSets = standardSetService.list(new QueryWrapper<DataStandardSet>().eq("group_id", group.getId()));
            if (CollectionUtils.isNotEmpty(standardSets)) {
                standardSets.forEach(set -> {
                    set.setNode(group.getId() + "_" + set.getId());
                });
            }
            group.setNode(group.getId().toString());
            group.setStandardSets(standardSets);
        });
        return groups;
    }

    public List<DataStandardGroup> optionTree(Integer type){
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        QueryWrapper<DataStandardGroup> queryWrapper = new QueryWrapper<DataStandardGroup>().eq("tenant_id", tenantId);
        if (ObjectUtils.isNotEmpty(type)) {
            queryWrapper.eq("type", type);
        }
        List<DataStandardGroup> groups = iDataStandardGroupService.list(queryWrapper);
        groups.forEach(group -> {
            List<DataStandardSet> standardSets = standardSetService.list(new QueryWrapper<DataStandardSet>().eq("group_id", group.getId()));
            if (CollectionUtils.isNotEmpty(standardSets)) {
                standardSets.forEach(set -> {
                    set.setNode(group.getId() + "_" + set.getId());
                });
            }
            group.setNode(group.getId().toString());
            for (DataStandardSet standardSet : standardSets) {
                List<DataStandardConfig> dataStandardConfigs = dataStandardConfigService.list(
                        new QueryWrapper<DataStandardConfig>()
                                .in("data_column_type","String","Int8","Int32","Int64","DateTime64","Float32","Float64")
                                .eq("set_id", standardSet.getId())
                                .eq("standard_type", 2)
                                .eq("type", 1));
                standardSet.setConfigList(dataStandardConfigs);
            }
            group.setStandardSets(standardSets);
        });
        return groups;
    }
}