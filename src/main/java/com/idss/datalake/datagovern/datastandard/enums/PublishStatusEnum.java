package com.idss.datalake.datagovern.datastandard.enums;

/**
 * <AUTHOR>
 * @description <p>发布状态</p>
 * @see
 * @since 2023-08-25
 */
public enum PublishStatusEnum {
    UNPUBLISHED(0, "未发布"),
    PUBLISHED(1, "已发布"),
    PUBLISHED_AWAIT(2, "审核中");

    PublishStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private int code;
    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
