/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.datastandard.dto.DataStandardSetDTO;
import com.idss.datalake.datagovern.datastandard.entity.DataStandardConfig;
import com.idss.datalake.datagovern.datastandard.entity.DataStandardSet;
import com.idss.datalake.datagovern.datastandard.service.IDataStandardConfigService;
import com.idss.datalake.datagovern.datastandard.service.IDataStandardSetService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>数据标准集 manager处理类</p>
 * @since 2022-08-20
 */
@Component
public class DataStandardSetManager {
    private static final Logger logger = LoggerFactory.getLogger(DataStandardSetManager.class);

    @Autowired
    private IDataStandardSetService iDataStandardSetService;
    @Autowired
    private IDataStandardConfigService standardConfigService;

    public Map<String, Object> createPageCondition(RequestDTO requestDTO) throws ParamInvalidException {
        if (requestDTO != null && requestDTO.getGlobal() != null) {
            GlobalDTO globalDTO = requestDTO.getGlobal();
            if (globalDTO.getPageNum() == null) {
                globalDTO.setPageNum(Constant.DEFAULT_PAGE_NUM);
            }
            if (globalDTO.getPageSize() == null) {
                globalDTO.setPageSize(Constant.DEFAULT_PAGE_SIZE);
            }
            if (StringUtils.isEmpty(globalDTO.getOrderType())) {
                globalDTO.setOrderType("desc");
            }
            if (StringUtils.isEmpty(globalDTO.getOrderField())) {
                globalDTO.setOrderField("createTime");
            }
        } else {
            throw new ParamInvalidException("入参错误");
        }
        return null;
    }

    /**
     * 分页
     *
     * @param requestDTO
     * @return
     * @throws Exception
     */
    public Map<String, Object> page(RequestDTO requestDTO) throws Exception {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<DataStandardSet> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataStandardSet> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UserUtil.getCurrentTenantId());
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }

        IPage<DataStandardSet> pageResult = iDataStandardSetService.page(page, queryWrapper);

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", pageResult.getRecords());
        return result;
    }

    public void create(DataStandardSetDTO dataStandardSetDTO) throws ParamInvalidException {
        if (StringUtils.isBlank(dataStandardSetDTO.getName()) || ObjectUtils.isEmpty(dataStandardSetDTO.getGroupId())) {
            throw new ParamInvalidException("入参异常");
        }
        List<DataStandardSet> list = iDataStandardSetService.list(new QueryWrapper<DataStandardSet>()
                .eq("name", dataStandardSetDTO.getName())
                .eq("tenant_id", UserUtil.getCurrentTenantId())
                .eq("group_id", dataStandardSetDTO.getGroupId()));
        if (CollectionUtils.isNotEmpty(list)) {
            throw new ParamInvalidException("名称已存在");
        }
        //保存
        DataStandardSet dataStandardSet = new DataStandardSet();
        ReflectionUtil.copyLomBokProperties(dataStandardSetDTO, dataStandardSet);
        dataStandardSet.setTenantId(Long.valueOf(UserUtil.getCurrentTenantId()));
        dataStandardSet.setCreateTime(LocalDateTime.now());
        dataStandardSet.setUpdateTime(LocalDateTime.now());
        dataStandardSet.setCreateUser(UserUtil.getCurrentUsername());
        dataStandardSet.setUpdateUser(UserUtil.getCurrentUsername());
        iDataStandardSetService.save(dataStandardSet);
    }

    public void delete(Long id) throws ParamInvalidException {
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        List<DataStandardConfig> standardConfigs = standardConfigService.list(new QueryWrapper<DataStandardConfig>()
                .eq("set_id", id).eq("tenant_id", tenantId));
        if (CollectionUtils.isNotEmpty(standardConfigs)) {
            throw new ParamInvalidException("标准集下有数据标准，不可删除");
        }
        iDataStandardSetService.remove(new QueryWrapper<DataStandardSet>().eq("id", id).eq("tenant_id", tenantId));
    }

    public void edit(DataStandardSetDTO dataStandardSetDTO) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(dataStandardSetDTO.getId())) {
            throw new ParamInvalidException("入参异常");
        }
        List<DataStandardSet> list = iDataStandardSetService.list(new QueryWrapper<DataStandardSet>()
                .eq("name", dataStandardSetDTO.getName())
                .eq("tenant_id", UserUtil.getCurrentTenantId())
                .eq("group_id", dataStandardSetDTO.getGroupId())
                .ne("id", dataStandardSetDTO.getId()));
        if (CollectionUtils.isNotEmpty(list)) {
            throw new ParamInvalidException("名称已存在");
        }

        DataStandardSet dbOne = iDataStandardSetService.getById(dataStandardSetDTO.getId());
        ReflectionUtil.copyLomBokProperties(dataStandardSetDTO, dbOne);
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        iDataStandardSetService.saveOrUpdate(dbOne);
    }

    public DataStandardSet detail(Integer id) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        QueryWrapper<DataStandardSet> wrapper = new QueryWrapper();
        wrapper.eq("tenant_id", tenantId).eq("id", id);
        return iDataStandardSetService.getOne(wrapper);
    }
}