package com.idss.datalake.datagovern.datastandard.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardConfigVersionSnapshot;
import com.idss.datalake.datagovern.datastandard.mapper.QuaDataStandardConfigVersionSnapshotMapper;
import com.idss.datalake.datagovern.datastandard.service.IQuaDataStandardConfigVersionSnapshotService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据标准配置多版本记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@Service
public class QuaDataStandardConfigVersionSnapshotServiceImpl extends ServiceImpl<QuaDataStandardConfigVersionSnapshotMapper,
        QuaDataStandardConfigVersionSnapshot> implements IQuaDataStandardConfigVersionSnapshotService {

}
