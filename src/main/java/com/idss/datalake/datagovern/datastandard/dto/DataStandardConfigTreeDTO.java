/************************ CHAN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.dto;

import com.idss.datalake.datagovern.datastandard.entity.DataStandardConfig;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>数据标准配置二级树形结构 dto 类</p>
 * @since 2023-08-31
 */
@Data
public class DataStandardConfigTreeDTO {

    /**
     * 标准集名称
     */
    private String setName;

    private List<DataStandardConfig> children;

}
