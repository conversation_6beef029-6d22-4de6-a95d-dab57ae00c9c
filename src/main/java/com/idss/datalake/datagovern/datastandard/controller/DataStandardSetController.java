/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.controller;


import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.syslog.annotation.SysLog;
import com.idss.datalake.common.syslog.entity.OptModule;
import com.idss.datalake.common.syslog.entity.OptType;
import com.idss.datalake.datagovern.datastandard.dto.DataStandardSetDTO;
import com.idss.datalake.datagovern.datastandard.manager.DataStandardSetManager;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description <p>数据标准集 前端控制器</p>
 * @since 2022-08-20
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/datastandard/set")
public class DataStandardSetController {
    private static final Logger logger = LoggerFactory.getLogger(DataStandardSetController.class);

    @Autowired
    private DataStandardSetManager dataStandardSetManager;

    @SysLog(logName = "新增标准集", optType = OptType.ADD, optModule = OptModule.DATA_LAKE_GOVERNANCE_STANDARD, switchRedisStatus = true)
    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody DataStandardSetDTO dataStandardSetDTO) {
        try {
            dataStandardSetManager.create(dataStandardSetDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "删除标准集", optType = OptType.DELETE, optModule = OptModule.DATA_LAKE_GOVERNANCE_STANDARD, switchRedisStatus = true)
    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public ResultBean delete(@RequestBody DataStandardSetDTO dataStandardSetDTO) {
        try {
            dataStandardSetManager.delete(dataStandardSetDTO.getId());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "标准集详情", optType = OptType.DETAIL, optModule = OptModule.DATA_LAKE_GOVERNANCE_STANDARD, switchRedisStatus = true)
    @ApiOperation(value = "")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Integer id) {
        try {
            return ResultBean.success(dataStandardSetManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "标准集编辑", optType = OptType.EDIT, optModule = OptModule.DATA_LAKE_GOVERNANCE_STANDARD, switchRedisStatus = true)
    @ApiOperation(value = "编辑")
    @PutMapping(value = "/edit")
    public ResultBean edit(@RequestBody DataStandardSetDTO dataStandardSetDTO) {
        try {
            dataStandardSetManager.edit(dataStandardSetDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @SysLog(logName = "标准集查询", optType = OptType.QUERY, optModule = OptModule.DATA_LAKE_GOVERNANCE_STANDARD, switchRedisStatus = true)
    @ApiOperation(value = "分页查询列表")
    @PostMapping(value = "/page")
    public ResultBean page(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataStandardSetManager.page(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }
}
