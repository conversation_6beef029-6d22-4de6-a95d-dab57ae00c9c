package com.idss.datalake.datagovern.datastandard.enums;

/**
 * <AUTHOR>
 * @description <p>数据字段类型</p>
 * @see
 * @since 2022-08-20
 */
public enum DataColumnTypeEnum {
    String, Int8, Int32, Int64, DateTime64, Float32, Float64,
    CHAR,

    <PERSON><PERSON><PERSON><PERSON>,

    TIN<PERSON><PERSON>O<PERSON>,

    TEXT,

    DATE,

    TIME,

    YEAR,

    TIMESTAMP,

    DECIMAL,

    DOUBLE
}
