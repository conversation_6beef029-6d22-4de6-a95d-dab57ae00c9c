/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.dto;

import com.idss.datalake.datagovern.datastandard.entity.DataStandardConfig;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>数据标准配置 dto类</p>
 * @since 2022-08-20
 */
@Data
public class DataStandardConfigDTO extends DataStandardConfig {

    private List<Long> ids;

    /**
     * 分组id
     */
    private Long groupId;

    private String orderField;
    private String orderType;

    /**
     * 发布记录的id
     */
    private Long publishLogId;
    /**
     * 审批记录的id
     */
    private Long approvalLogId;

    /**
     * 是否有效，0有效，1无效
     */
    private Integer flag;
}
