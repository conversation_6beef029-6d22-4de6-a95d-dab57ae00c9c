package com.idss.datalake.datagovern.datastandard.enums;

/**
 * <AUTHOR>
 * @description <p>数据标准类型</p>
 * @see
 * @since 2022-08-20
 */
public enum CnEnEnum {
    cn(1, "仅包括中文"),
    en(2, "仅包含英文"),
    cn_en(3, "包含中英文");

    CnEnEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private int code;
    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
