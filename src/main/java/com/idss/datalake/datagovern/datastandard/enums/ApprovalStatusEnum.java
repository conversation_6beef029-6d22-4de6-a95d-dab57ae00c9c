package com.idss.datalake.datagovern.datastandard.enums;

/**
 * <AUTHOR>
 * @description <p>发布状态</p>
 * @see
 * @since 2023-08-25
 */
public enum ApprovalStatusEnum {
    AWAIT(0, "待审批"),
    approve(1, "同意"),
    REFUSE(2, "拒绝");

    ApprovalStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private int code;
    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
