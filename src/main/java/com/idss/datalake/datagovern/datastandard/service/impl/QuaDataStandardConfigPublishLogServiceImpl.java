package com.idss.datalake.datagovern.datastandard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.datastandard.dto.QuaDataStandardConfigPublishLogDTO;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardConfigPublishLog;
import com.idss.datalake.datagovern.datastandard.mapper.QuaDataStandardConfigPublishLogMapper;
import com.idss.datalake.datagovern.datastandard.service.IQuaDataStandardConfigPublishLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 数据标准发布记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
@Service
public class QuaDataStandardConfigPublishLogServiceImpl extends ServiceImpl<QuaDataStandardConfigPublishLogMapper,
        QuaDataStandardConfigPublishLog> implements IQuaDataStandardConfigPublishLogService {

    @Resource
    private QuaDataStandardConfigPublishLogMapper publishLogMapper;

    @Override
    public IPage<QuaDataStandardConfigPublishLogDTO> getPublishPage(IPage<QuaDataStandardConfigPublishLog> page,
                                                                    QueryWrapper<QuaDataStandardConfigPublishLog> wrapper) {
        return publishLogMapper.getPublishPage(page, wrapper);
    }
}
