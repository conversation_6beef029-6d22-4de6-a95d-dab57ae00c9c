/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-10-22
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-10-22
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.dto;

import com.idss.datalake.datagovern.datastandard.entity.QuaAssetResource;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>资产目录 dto类</p>
 * @since 2022-10-22
 */
@Data
public class QuaAssetResourceDTO extends QuaAssetResource {
    private List<Integer> ids;
}