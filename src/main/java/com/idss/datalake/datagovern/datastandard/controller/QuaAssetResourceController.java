/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-10-22
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-10-22
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.controller;


import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.datagovern.datastandard.dto.QuaAssetResourceDTO;
import com.idss.datalake.datagovern.datastandard.manager.QuaAssetResourceManager;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description <p>资产目录 前端控制器</p>
 * @since 2022-10-22
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/datastandard/assetResource")
public class QuaAssetResourceController {
    private static final Logger logger = LoggerFactory.getLogger(QuaAssetResourceController.class);
    @Autowired
    private QuaAssetResourceManager quaAssetResourceManager;

    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody QuaAssetResourceDTO quaAssetResourceDTO) {
        try {
            quaAssetResourceManager.create(quaAssetResourceDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public ResultBean delete(@RequestBody QuaAssetResourceDTO quaAssetResourceDTO) {
        try {
            quaAssetResourceManager.delete(quaAssetResourceDTO.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Integer id) {
        try {
            return ResultBean.success(quaAssetResourceManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "编辑")
    @PutMapping(value = "/edit")
    public ResultBean edit(@RequestBody QuaAssetResourceDTO quaAssetResourceDTO) {
        try {
            quaAssetResourceManager.edit(quaAssetResourceDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询列表")
    @PostMapping(value = "/page")
    public ResultBean page(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(quaAssetResourceManager.page(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }
}
