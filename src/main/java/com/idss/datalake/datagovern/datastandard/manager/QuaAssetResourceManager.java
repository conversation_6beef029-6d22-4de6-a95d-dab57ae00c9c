/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-10-22
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-10-22
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.datastandard.dto.QuaAssetResourceDTO;
import com.idss.datalake.datagovern.datastandard.entity.QuaAssetResource;
import com.idss.datalake.datagovern.datastandard.service.IQuaAssetResourceService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>资产目录 manager处理类</p>
 * @since 2022-10-22
 */
@Component
public class QuaAssetResourceManager {
    private static final Logger logger = LoggerFactory.getLogger(QuaAssetResourceManager.class);

    @Autowired
    private IQuaAssetResourceService iQuaAssetResourceService;

    public Map<String, Object> createPageCondition(RequestDTO requestDTO) throws ParamInvalidException {
        if (requestDTO != null && requestDTO.getGlobal() != null) {
            GlobalDTO globalDTO = requestDTO.getGlobal();
            if (globalDTO.getPageNum() == null) {
                globalDTO.setPageNum(Constant.DEFAULT_PAGE_NUM);
            }
            if (globalDTO.getPageSize() == null) {
                globalDTO.setPageSize(Constant.DEFAULT_PAGE_SIZE);
            }
            if (StringUtils.isEmpty(globalDTO.getOrderType())) {
                globalDTO.setOrderType("desc");
            }
            if (StringUtils.isEmpty(globalDTO.getOrderField())) {
                globalDTO.setOrderField("createTime");
            }
        } else {
            throw new ParamInvalidException("入参错误");
        }
        return null;
    }

    /**
     * 分页
     *
     * @param requestDTO
     * @return
     * @throws Exception
     */
    public Map<String, Object> page(RequestDTO requestDTO) throws Exception {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<QuaAssetResource> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<QuaAssetResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UserUtil.getCurrentTenantId());
        if (requestDTO.getParam() != null) {
            QuaAssetResourceDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), QuaAssetResourceDTO.class);
            if (StringUtils.isNotBlank(dto.getName())) {
                queryWrapper.like("name", dto.getName());
            }
            if (StringUtils.isNotBlank(dto.getEnName())) {
                queryWrapper.like("en_name", dto.getEnName());
            }
        }
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }

        IPage<QuaAssetResource> pageResult = iQuaAssetResourceService.page(page, queryWrapper);

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", pageResult.getRecords());
        return result;
    }

    public void create(QuaAssetResourceDTO quaAssetResourceDTO) throws ParamInvalidException {
        if (StringUtils.isBlank(quaAssetResourceDTO.getName())) {
            throw new ParamInvalidException("入参异常");
        }

        //保存
        QuaAssetResource quaAssetResource = new QuaAssetResource();
        ReflectionUtil.copyLomBokProperties(quaAssetResourceDTO, quaAssetResource);
        quaAssetResource.setTenantId(UserUtil.getCurrentTenantId());
        quaAssetResource.setCreateTime(LocalDateTime.now());
        quaAssetResource.setUpdateTime(LocalDateTime.now());
        quaAssetResource.setCreateUser(UserUtil.getCurrentUsername());
        quaAssetResource.setUpdateUser(UserUtil.getCurrentUsername());
        iQuaAssetResourceService.save(quaAssetResource);
    }

    public void delete(List<Integer> ids) throws ParamInvalidException {
        Integer tenantId = UserUtil.getCurrentTenantId();
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        iQuaAssetResourceService.remove(new QueryWrapper<QuaAssetResource>().in("id", ids).eq("tenant_id", tenantId));
    }

    public void edit(QuaAssetResourceDTO quaAssetResourceDTO) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(quaAssetResourceDTO.getId())) {
            throw new ParamInvalidException("入参异常");
        }

        QuaAssetResource dbOne = iQuaAssetResourceService.getById(quaAssetResourceDTO.getId());
        ReflectionUtil.copyLomBokProperties(quaAssetResourceDTO, dbOne);
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        iQuaAssetResourceService.saveOrUpdate(dbOne);
    }

    public QuaAssetResource detail(Integer id) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        QueryWrapper<QuaAssetResource> wrapper = new QueryWrapper();
        wrapper.eq("tenant_id", UserUtil.getCurrentTenantId()).eq("id", id);
        return iQuaAssetResourceService.getOne(wrapper);
    }
}