package com.idss.datalake.datagovern.datastandard.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.datalake.datagovern.datastandard.dto.QuaDataStandardConfigRestoreLogDTO;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardConfigRestoreLog;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 数据标准版本恢复记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
public interface QuaDataStandardConfigRestoreLogMapper extends BaseMapper<QuaDataStandardConfigRestoreLog> {

    @Select("SELECT conf.cn_name,conf.publish_name,rl.* FROM qua_data_standard_config_restore_log rl " +
            "left join qua_data_standard_config conf on rl.config_id = conf.id ${ew.customSqlSegment}")
    IPage<QuaDataStandardConfigRestoreLogDTO> getPage(Page<QuaDataStandardConfigRestoreLog> page,
                                                      @Param(Constants.WRAPPER) QueryWrapper<QuaDataStandardConfigRestoreLog> wrapper);
}
