package com.idss.datalake.datagovern.datastandard.controller;


import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardDic;
import com.idss.datalake.datagovern.datastandard.manager.QuaDataStandardDicManager;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 数据标准字典 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Slf4j
@RestController
@RequestMapping(Constant.API_PREFIX + "/datastandard/dic")
public class QuaDataStandardDicController {
    @Autowired
    private QuaDataStandardDicManager quaDataStandardDicManager;

    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody QuaDataStandardDic quaDataStandardDic) {
        try {
            quaDataStandardDicManager.create(quaDataStandardDic);
            return ResultBean.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public ResultBean delete(@RequestBody QuaDataStandardDic quaDataStandardDic) {
        try {
            quaDataStandardDicManager.delete(quaDataStandardDic.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Integer id) {
        try {
            return ResultBean.success(quaDataStandardDicManager.detail(id));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "编辑")
    @PostMapping(value = "/edit")
    public ResultBean edit(@RequestBody QuaDataStandardDic quaDataStandardDic) {
        try {
            quaDataStandardDicManager.edit(quaDataStandardDic);
            return ResultBean.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询列表")
    @PostMapping(value = "/page")
    public ResultBean page(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(quaDataStandardDicManager.page(requestDTO));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }
}
