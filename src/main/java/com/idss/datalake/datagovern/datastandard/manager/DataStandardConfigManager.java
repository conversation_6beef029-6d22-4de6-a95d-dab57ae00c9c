/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-20
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.datastandard.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.syslog.entity.OptModule;
import com.idss.datalake.common.syslog.entity.OptType;
import com.idss.datalake.common.syslog.util.LogUtil;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.datalake.common.util.ExcelParser;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRule;
import com.idss.datalake.datagovern.dataquality.model.RuleExtConfig;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorRuleService;
import com.idss.datalake.datagovern.datastandard.dto.DataStandardConfigDTO;
import com.idss.datalake.datagovern.datastandard.dto.DataStandardConfigTreeDTO;
import com.idss.datalake.datagovern.datastandard.dto.QuaDataStandardConfigPublishLogDTO;
import com.idss.datalake.datagovern.datastandard.dto.QuaDataStandardConfigRestoreLogDTO;
import com.idss.datalake.datagovern.datastandard.entity.DataStandardConfig;
import com.idss.datalake.datagovern.datastandard.entity.DataStandardSet;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardConfigPublishLog;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardConfigRestoreLog;
import com.idss.datalake.datagovern.datastandard.entity.QuaDataStandardConfigVersionSnapshot;
import com.idss.datalake.datagovern.datastandard.enums.ApprovalStatusEnum;
import com.idss.datalake.datagovern.datastandard.enums.DataStandardTypeEnum;
import com.idss.datalake.datagovern.datastandard.enums.PublishStatusEnum;
import com.idss.datalake.datagovern.datastandard.service.IDataStandardConfigService;
import com.idss.datalake.datagovern.datastandard.service.IDataStandardSetService;
import com.idss.datalake.datagovern.datastandard.service.IQuaDataStandardConfigPublishLogService;
import com.idss.datalake.datagovern.datastandard.service.IQuaDataStandardConfigRestoreLogService;
import com.idss.datalake.datagovern.datastandard.service.IQuaDataStandardConfigVersionSnapshotService;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import sun.misc.BASE64Decoder;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p>数据标准配置 manager处理类</p>
 * @since 2022-08-20
 */
@Component
public class DataStandardConfigManager {
    private static final Logger logger = LoggerFactory.getLogger(DataStandardConfigManager.class);

    @Autowired
    private IDataStandardConfigService configService;
    @Autowired
    private IDataStandardSetService standardSetService;
    @Autowired
    private IQuaDataStandardConfigVersionSnapshotService versionSnapshotService;
    @Autowired
    private IQuaMonitorRuleService monitorRuleService;
    @Autowired
    private IQuaDataStandardConfigRestoreLogService restoreLogService;
    @Autowired
    private IQuaDataStandardConfigPublishLogService publishLogService;

    public Map<String, Object> createPageCondition(RequestDTO requestDTO) throws ParamInvalidException {
        if (requestDTO != null && requestDTO.getGlobal() != null) {
            GlobalDTO globalDTO = requestDTO.getGlobal();
            if (globalDTO.getPageNum() == null) {
                globalDTO.setPageNum(Constant.DEFAULT_PAGE_NUM);
            }
            if (globalDTO.getPageSize() == null) {
                globalDTO.setPageSize(Constant.DEFAULT_PAGE_SIZE);
            }
            if (StringUtils.isEmpty(globalDTO.getOrderType())) {
                globalDTO.setOrderType("desc");
            }
            if (StringUtils.isEmpty(globalDTO.getOrderField())) {
                globalDTO.setOrderField("createTime");
            }
        } else {
            throw new ParamInvalidException("入参错误");
        }
        return null;
    }

    /**
     * 分页
     *
     * @param requestDTO
     * @return
     * @throws Exception
     */
    public Map<String, Object> page(RequestDTO requestDTO) throws Exception {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        IPage<DataStandardConfig> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataStandardConfig> queryWrapper = new QueryWrapper<>();
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        queryWrapper.eq("tenant_id", tenantId);
        if (requestDTO.getParam() != null) {
            DataStandardConfigDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), DataStandardConfigDTO.class);
            if (ObjectUtils.isNotEmpty(dto.getGroupId())) {
                List<DataStandardSet> standardSets = standardSetService.list(new QueryWrapper<DataStandardSet>().eq("group_id",
                        dto.getGroupId()).eq(
                        "tenant_id", tenantId));
                List<Long> setIds = standardSets.stream().map(x -> x.getId()).collect(Collectors.toList());
                queryWrapper.eq("set_id", setIds);
            } else if (ObjectUtils.isNotEmpty(dto.getSetId())) {
                queryWrapper.eq("set_id", dto.getSetId());
            }

            queryWrapper.eq("type", dto.getType());

            if (StringUtils.isNotBlank(dto.getCnName())) {
                queryWrapper.like("cn_name", dto.getCnName());
            }
            if (StringUtils.isNotBlank(dto.getEnName())) {
                queryWrapper.like("en_name", dto.getEnName());
            }
        }
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }
        IPage<DataStandardConfig> pageResult = configService.page(page, queryWrapper);

        List<DataStandardConfig> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            records.forEach(x -> x.setStandardTypeName(DataStandardTypeEnum.getDescription(x.getStandardType())));
        }
        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public void create(DataStandardConfigDTO dataStandardConfigDTO) throws ParamInvalidException {
        if (StringUtils.isAnyBlank(dataStandardConfigDTO.getCnName(), dataStandardConfigDTO.getEnName(), dataStandardConfigDTO.getCode())
                || ObjectUtils.isEmpty(dataStandardConfigDTO.getSetId())) {
            throw new ParamInvalidException("入参异常");
        }
        List<DataStandardConfig> list = configService.list(new QueryWrapper<DataStandardConfig>()
                .eq("cn_name", dataStandardConfigDTO.getCnName())
                .eq("tenant_id", UserUtil.getCurrentTenantId())
                .eq("set_id", dataStandardConfigDTO.getSetId()));
        if (CollectionUtils.isNotEmpty(list)) {
            throw new ParamInvalidException("中文名称已存在");
        }
        //保存
        DataStandardConfig dataStandardConfig = new DataStandardConfig();
        ReflectionUtil.copyLomBokProperties(dataStandardConfigDTO, dataStandardConfig);
        dataStandardConfig.setTenantId(Long.valueOf(UserUtil.getCurrentTenantId()));
        dataStandardConfig.setCreateTime(LocalDateTime.now());
        dataStandardConfig.setUpdateTime(LocalDateTime.now());
        dataStandardConfig.setCreateUser(UserUtil.getCurrentUsername());
        dataStandardConfig.setUpdateUser(UserUtil.getCurrentUsername());
        configService.save(dataStandardConfig);

        genVersionSnapshot(dataStandardConfig);
    }

    public void delete(List<Long> ids) throws ParamInvalidException {
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        // 启动的标准不能删除
        List<DataStandardConfig> configs = configService.list(new QueryWrapper<DataStandardConfig>().in("id", ids).eq("status", 1));
        if (CollectionUtils.isNotEmpty(configs)) {
            throw new ParamInvalidException("存在启动的标准，不可删除");
        }
        // TODO: 2022/8/20 被质量任务引用了的标准不可删除
        configService.remove(new QueryWrapper<DataStandardConfig>().in("id", ids).eq("tenant_id", tenantId));

        // 删除相应的审核记录
        publishLogService.remove(new QueryWrapper<QuaDataStandardConfigPublishLog>().in("config_id", ids));
        // 删除版本恢复记录
        restoreLogService.remove(new QueryWrapper<QuaDataStandardConfigRestoreLog>().in("config_id", ids));
        // 删除快照记录
        versionSnapshotService.remove(new QueryWrapper<QuaDataStandardConfigVersionSnapshot>().in("config_id", ids));
    }

    /**
     * 启停
     *
     * @param dto
     * @throws ParamInvalidException
     */
    public void enable(DataStandardConfigDTO dto) throws ParamInvalidException {
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        if (CollectionUtils.isEmpty(dto.getIds()) || ObjectUtils.isEmpty(dto.getStatus())) {
            throw new ParamInvalidException("入参异常");
        }
        configService.update(new UpdateWrapper<DataStandardConfig>()
                .in("id", dto.getIds()).eq("tenant_id", tenantId)
                .set("status", dto.getStatus()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void edit(DataStandardConfigDTO dataStandardConfigDTO) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(dataStandardConfigDTO.getId())) {
            throw new ParamInvalidException("入参异常");
        }

        List<DataStandardConfig> list = configService.list(new QueryWrapper<DataStandardConfig>()
                .eq("cn_name", dataStandardConfigDTO.getCnName())
                .eq("tenant_id", UserUtil.getCurrentTenantId())
                .eq("set_id", dataStandardConfigDTO.getSetId())
                .ne("id", dataStandardConfigDTO.getId()));
        if (CollectionUtils.isNotEmpty(list)) {
            throw new ParamInvalidException("中文名称已存在");
        }

        DataStandardConfig dbOne = configService.getById(dataStandardConfigDTO.getId());
        ReflectionUtil.copyLomBokProperties(dataStandardConfigDTO, dbOne);
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        // 2023/8/25 更新为未发布状态
        changeToUnpublished(dbOne);
        configService.saveOrUpdate(dbOne);

        genVersionSnapshot(dbOne);
    }

    /**
     * 产生新的版本快照
     *
     * @param dbOne
     */
    private void genVersionSnapshot(DataStandardConfig dbOne) {
        QuaDataStandardConfigVersionSnapshot versionSnapshot = new QuaDataStandardConfigVersionSnapshot();
        ReflectionUtil.copyLomBokProperties(dbOne, versionSnapshot);
        versionSnapshot.setId(null);
        versionSnapshot.setConfigId(dbOne.getId());
        versionSnapshotService.save(versionSnapshot);
    }

    public DataStandardConfig detail(Integer id) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        QueryWrapper<DataStandardConfig> wrapper = new QueryWrapper();
        wrapper.eq("tenant_id", tenantId).eq("id", id);
        DataStandardConfig one = configService.getOne(wrapper);
        one.setStandardTypeName(DataStandardTypeEnum.getDescription(one.getStandardType()));
        return one;
    }

    /**
     * 导入
     *
     * @param params
     */
    @SneakyThrows
    public void importData(Map<String, Object> params) {
        String result = Constant.STATUS_SUCCESS;
        int count = 0;
        try {
            String filePath = (String) params.get("filePath");
            Integer type = Integer.valueOf(params.get("type").toString()) ;
            BASE64Decoder decoder = new BASE64Decoder();
            filePath = new String(decoder.decodeBuffer(filePath));
            File file = new File(filePath);
            if (!file.exists()) {
                throw new ParamInvalidException("文件不存在");
            }
            Integer setId = (Integer) params.get("setId");
            if (setId == null) {
                throw new ParamInvalidException("请选择标准集");
            }
            Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
            List<DataStandardConfig> dbConfigs = configService.list(new QueryWrapper<DataStandardConfig>().eq("tenant_id", tenantId));
            Set<String> cnNameSets = dbConfigs.stream().map(x -> x.getCnName()).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(cnNameSets)) {
                cnNameSets = new HashSet<>();
            }
            LocalDateTime now = LocalDateTime.now();
            String username = UserUtil.getCurrentUsername();
            List<String[]> dataList = ExcelParser.readExcel(file, 15);
            List<DataStandardConfig> configs = new ArrayList<>();
            for (String[] dataArr : dataList) {
                DataStandardConfig config = new DataStandardConfig();
                String cnName = dataArr[0];
                if(StringUtils.isEmpty(cnName)){
                    continue;
                }
                // 中文名判重，包括数据库的和本次传入的数据
                if (cnNameSets.contains(cnName)) {
                    throw new ParamInvalidException(cnName + "不能重复");
                } else {
                    cnNameSets.add(cnName);
                }
                config.setCnName(cnName);
                config.setEnName(dataArr[1]);
                config.setCode(dataArr[2]);
                config.setBusinessMeaning(dataArr[3]);
                config.setAccordance(dataArr[4]);
                config.setStandardType(DataStandardTypeEnum.getCode(dataArr[5]));
                if (config.getStandardType() == DataStandardTypeEnum.data_length.getCode()) {
                    config.setDataLength(Integer.valueOf(dataArr[6]));
                } else if (config.getStandardType() == DataStandardTypeEnum.data_column_type.getCode()) {
                    config.setDataColumnType(dataArr[6]);
                } else if (config.getStandardType() == DataStandardTypeEnum.data_precision.getCode()) {
                    config.setDataPrecision(Integer.valueOf(dataArr[6]));
                } else if (config.getStandardType() == DataStandardTypeEnum.cn_en_verification.getCode()) {
                    config.setCnEnVerification(Integer.valueOf(dataArr[6]));
                } else if (config.getStandardType() == DataStandardTypeEnum.custom_content.getCode()) {
                    config.setCustomContent(dataArr[6]);
                }

                config.setRemark(dataArr[7]);
                config.setDept(dataArr[8]);
                config.setBusinessSystem(dataArr[9]);
                config.setDatabaseName(dataArr[10]);
                config.setStoreCycle(dataArr[11]);
                config.setEncrypt("是".equals(dataArr[12]) ? "1" : "0");
                config.setSpecification(dataArr[13]);
                config.setSensitiveType(dataArr[14]);

                config.setCreateTime(now);
                config.setCreateUser(username);
                config.setUpdateUser(username);
                config.setUpdateTime(now);
                config.setTenantId(tenantId);
                config.setSetId(Long.valueOf(setId));
                config.setType(type);
                configs.add(config);
            }
            configService.saveBatch(configs);
            count = configs.size();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = Constant.STATUS_FAIL;
            throw new RuntimeException(e);
        } finally {
            String desc = "标准导入，共(" + count + ")条";
            LogUtil.addLog(OptType.IMPORT, OptModule.DATA_LAKE_GOVERNANCE_STANDARD, desc, result, true);
        }

    }

    /**
     * 下载模板
     */
    public void downloadTemplateFile(HttpServletResponse response) throws IOException {
        executeDownload(response, null);
    }

    /**
     * 实际下载
     *
     * @param response
     * @param dataRows
     * @throws IOException
     */
    private void executeDownload(HttpServletResponse response, List<List<String>> dataRows) throws IOException {
        List<String> headers = CollUtil.newArrayList("标准中文名(必填)", "标准英文名(必填)", "标准编号(必填)", "业务含义", "制定依据", "类型(必填)", "类型值(必填)", "备注",
                "所属部门", "所属系统", "所属数据库", "存储周期", "是否加密", "权限说明", "敏感类型");
        List<List<String>> rows = new ArrayList<>();
        rows.add(headers);
        if (CollectionUtils.isNotEmpty(dataRows)) {
            rows.addAll(dataRows);
        }
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.write(rows);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=template.xlsx");
        ServletOutputStream out = response.getOutputStream();

        writer.flush(out, true);
        writer.close();
        IoUtil.close(out);
    }

    /**
     * 下载数据
     *
     * @param response
     */
    public void downloadData(DataStandardConfigDTO dto, HttpServletResponse response) {
        String result = Constant.STATUS_SUCCESS;
        int count = 0;
        try {
            List<DataStandardConfig> dbConfigs;
            Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
            if (!CollectionUtils.isEmpty(dto.getIds())) {
                QueryWrapper<DataStandardConfig> queryWrapper = new QueryWrapper<DataStandardConfig>().eq("tenant_id", tenantId)
                        .in("id", dto.getIds());
                if (!StringUtils.isAnyBlank(dto.getOrderField(), dto.getOrderType())) {
                    queryWrapper.orderBy(true, "asc".equalsIgnoreCase(dto.getOrderType()),
                            CamelToUnderUtil.underField(dto.getOrderField()));
                } else {
                    queryWrapper.orderBy(true, false, "create_time");
                }
                dbConfigs = configService.list(queryWrapper);
            } else if (dto.getSetId() != null) {
                QueryWrapper<DataStandardConfig> queryWrapper = new QueryWrapper<DataStandardConfig>().eq("tenant_id", tenantId)
                        .in("set_id", dto.getSetId());
                if (!StringUtils.isAnyBlank(dto.getOrderField(), dto.getOrderType())) {
                    queryWrapper.orderBy(true, "asc".equalsIgnoreCase(dto.getOrderType()),
                            CamelToUnderUtil.underField(dto.getOrderField()));
                } else {
                    queryWrapper.orderBy(true, false, "create_time");
                }
                dbConfigs = configService.list(queryWrapper);
            } else {
                throw new ParamInvalidException("请选择数据");
            }
            if (dbConfigs == null) {
                throw new ParamInvalidException("请选择数据");
            }
            List<List<String>> dataRows = new ArrayList<>();
            for (DataStandardConfig config : dbConfigs) {
                //logger.info("config is {}", JSONObject.toJSONString(config));
                List<String> row = CollUtil.newArrayList(config.getCnName(), config.getEnName(), config.getCode(),
                        config.getBusinessMeaning(), config.getAccordance(), DataStandardTypeEnum.getDescription(config.getStandardType()),
                        getStandardTypeValue(config), config.getRemark(), config.getDept(),
                        config.getBusinessSystem(), config.getDatabaseName(), config.getStoreCycle(),
                        "1".equals(config.getEncrypt()) ? "是" : "否", config.getSpecification(), config.getSensitiveType());
                dataRows.add(row);
            }
            count = dataRows.size();
            executeDownload(response, dataRows);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            result = Constant.STATUS_FAIL;
        } finally {
            String desc = "标准导出:template.xlsx 共(" + count + ")条";
            LogUtil.addLog(OptType.EXPORT, OptModule.DATA_LAKE_GOVERNANCE_STANDARD, desc, result, true);
        }
    }

    private String getStandardTypeValue(DataStandardConfig config) {
        if (config.getStandardType() == DataStandardTypeEnum.data_length.getCode()) {
            return String.valueOf(config.getDataLength());
        } else if (config.getStandardType() == DataStandardTypeEnum.data_column_type.getCode()) {
            return config.getDataColumnType();
        } else if (config.getStandardType() == DataStandardTypeEnum.data_precision.getCode()) {
            return String.valueOf(config.getDataPrecision());
        } else if (config.getStandardType() == DataStandardTypeEnum.cn_en_verification.getCode()) {
            return String.valueOf(config.getCnEnVerification());
        } else if (config.getStandardType() == DataStandardTypeEnum.custom_content.getCode()) {
            return config.getCustomContent();
        }
        return "";
    }

    /**
     * 注销
     *
     * @param ids
     * @throws ParamInvalidException
     */
    public void logOff(List<Long> ids) throws ParamInvalidException {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        // 1. 未发布的标准不可操作「注销」
        int count = configService.count(new QueryWrapper<DataStandardConfig>().in("id", ids)
                .eq("publish_status", PublishStatusEnum.UNPUBLISHED.getCode()));
        if (count > 0) {
            throw new ParamInvalidException("未发布的标准不可注销");
        }
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        // 2. 已经被数据质量模型引用的标准不可以注销操作
        List<QuaMonitorRule> rules = monitorRuleService.list(new QueryWrapper<QuaMonitorRule>().eq("tenant_id", tenantId).eq("flag", "1")
                .eq("rule_type", "STANDARD"));
        if (CollectionUtils.isNotEmpty(rules)) {
            for (QuaMonitorRule rule : rules) {
                RuleExtConfig ruleExtConfig = JSONObject.parseObject(rule.getRuleDetail(), RuleExtConfig.class);
                Long standardId = Long.valueOf(ruleExtConfig.getStandards());
                if (ids.contains(standardId)) {
                    throw new ParamInvalidException("已被数据质量模型引用的标准不可注销");
                }
            }
        }
        List<DataStandardConfig> configs = configService.list(new QueryWrapper<DataStandardConfig>().in("id", ids));
        for (DataStandardConfig config : configs) {
            changeToUnpublished(config);
        }
        configService.updateBatchById(configs);
    }

    /**
     * 设置成未发布状态
     *
     * @param config
     */
    private void changeToUnpublished(DataStandardConfig config) {
        config.setPublishStatus(PublishStatusEnum.UNPUBLISHED.getCode());
        config.setApprovalStatus(null);
    }

    /**
     * 发布列表
     *
     * @param requestDTO
     * @return
     */
    public Map<String, Object> publishPage(RequestDTO requestDTO) {
        Map<String, Object> result = new HashMap<>();
        IPage<QuaDataStandardConfigPublishLog> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<QuaDataStandardConfigPublishLog> queryWrapper = new QueryWrapper<>();
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        queryWrapper.eq("conf.tenant_id", tenantId);
        if (requestDTO.getParam() != null) {
            QuaDataStandardConfigPublishLogDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(),
                    QuaDataStandardConfigPublishLogDTO.class);
            if (StringUtils.isNotBlank(dto.getPublishName())) {
                queryWrapper.like("conf.publish_name", dto.getPublishName());
            }
        }
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }
        queryWrapper.orderByDesc("pl.publish_time");
        IPage<QuaDataStandardConfigPublishLogDTO> pageResult = publishLogService.getPublishPage(page, queryWrapper);
        List<QuaDataStandardConfigPublishLogDTO> records = pageResult.getRecords();
        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);
        return result;
    }

    /**
     * 发布操作
     *
     * @param dto
     * @throws ParamInvalidException
     */
    @Transactional(rollbackFor = Exception.class)
    public void publish(QuaDataStandardConfigPublishLogDTO dto) throws ParamInvalidException {
        List<DataStandardConfig> configs = configService.list(new QueryWrapper<DataStandardConfig>().in("id", dto.getConfigIds()));
        if (CollectionUtils.isEmpty(configs)) {
            throw new ParamInvalidException("标准不存在");
        }
        List<QuaDataStandardConfigPublishLog> publishLogs = new ArrayList<>();
        for (DataStandardConfig config : configs) {
            QuaDataStandardConfigPublishLog publishLog = new QuaDataStandardConfigPublishLog();
            publishLog.setConfigId(config.getId());
            publishLog.setFlag(0);
            publishLog.setPublishStatus(PublishStatusEnum.PUBLISHED_AWAIT.getCode());
            publishLog.setPublishName(dto.getPublishName());
            publishLog.setPublishDesc(dto.getPublishDesc());
            publishLog.setPublishUser(UserUtil.getCurrentUsername());
            publishLog.setPublishTime(LocalDateTime.now());
            // 发布后变成待审批
            publishLog.setApprovalStatus(ApprovalStatusEnum.AWAIT.getCode());
            publishLogs.add(publishLog);

            config.setPublishStatus(PublishStatusEnum.PUBLISHED_AWAIT.getCode());
            config.setApprovalStatus(ApprovalStatusEnum.AWAIT.getCode());
        }
        // 更新当前的未审批记录为失效状态
        publishLogService.update(new UpdateWrapper<QuaDataStandardConfigPublishLog>().set("flag", 1)
                .in("config_id", dto.getConfigIds()).eq("flag", 0));
        // 增加新的发布/审批记录
        publishLogService.saveBatch(publishLogs);
        // 更新数据标准状态
        configService.updateBatchById(configs);
    }

    /**
     * 审批列表
     *
     * @param requestDTO
     * @return
     */
    public Map<String, Object> approvalPage(RequestDTO requestDTO) {
        Map<String, Object> result = new HashMap<>();
        IPage<QuaDataStandardConfigPublishLog> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<QuaDataStandardConfigPublishLog> queryWrapper = new QueryWrapper<>();
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        queryWrapper.eq("conf.tenant_id", tenantId);
        if (requestDTO.getParam() != null) {
            QuaDataStandardConfigPublishLogDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(),
                    QuaDataStandardConfigPublishLogDTO.class);
            if (StringUtils.isNotBlank(dto.getPublishName())) {
                queryWrapper.like("conf.publish_name", dto.getPublishName());
            }
        }
        /*if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }*/
        queryWrapper.orderByDesc("publish_time");
        IPage<QuaDataStandardConfigPublishLogDTO> pageResult = publishLogService.getPublishPage(page, queryWrapper);
        List<QuaDataStandardConfigPublishLogDTO> records = pageResult.getRecords();
        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);
        return result;
    }

    /**
     * 审批操作
     *
     * @param dto
     * @throws ParamInvalidException
     */
    @Transactional(rollbackFor = Exception.class)
    public void approval(QuaDataStandardConfigPublishLogDTO dto) throws ParamInvalidException {
        List<QuaDataStandardConfigPublishLog> publishLogs = publishLogService.listByIds(dto.getIds());
        if (CollectionUtils.isEmpty(publishLogs)) {
            throw new ParamInvalidException("请选择数据");
        }
        for (QuaDataStandardConfigPublishLog publishLog : publishLogs) {
            publishLog.setApprovalStatus(dto.getApprovalStatus());
            publishLog.setApprovalDesc(dto.getApprovalDesc());
            publishLog.setApprovalUser(UserUtil.getCurrentUsername());
            publishLog.setApprovalTime(LocalDateTime.now());
            if (dto.getApprovalStatus().equals(ApprovalStatusEnum.approve.getCode())) {
                publishLog.setPublishStatus(PublishStatusEnum.PUBLISHED.getCode());
            } else {
                publishLog.setPublishStatus(PublishStatusEnum.UNPUBLISHED.getCode());
            }
        }
        publishLogService.updateBatchById(publishLogs);
        // 更新数据标准状态
        List<Long> configIds = publishLogs.stream().map(x -> x.getConfigId()).collect(Collectors.toList());
        configService.update(new UpdateWrapper<DataStandardConfig>().set("publish_status", publishLogs.get(0).getPublishStatus())
                .set("approval_status", publishLogs.get(0).getApprovalStatus()).in("id", configIds));
    }

    /**
     * 版本恢复列表
     *
     * @param requestDTO
     * @return
     */
    public Map<String, Object> restoreLogPage(RequestDTO requestDTO) {
        Map<String, Object> result = new HashMap<>();
        Page<QuaDataStandardConfigRestoreLog> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<QuaDataStandardConfigRestoreLog> queryWrapper = new QueryWrapper<>();
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        queryWrapper.eq("conf.tenant_id", tenantId);
        if (requestDTO.getParam() != null) {
            QuaDataStandardConfigRestoreLogDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(),
                    QuaDataStandardConfigRestoreLogDTO.class);
            if (StringUtils.isNotBlank(dto.getCnName())) {
                queryWrapper.like("conf.cn_name", dto.getCnName());
            }
        }
        queryWrapper.orderBy(true, false, "rl.restore_time");
        IPage<QuaDataStandardConfigRestoreLogDTO> pageResult = restoreLogService.getPage(page, queryWrapper);
        List<QuaDataStandardConfigRestoreLogDTO> records = pageResult.getRecords();
        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);
        return result;
    }

    /**
     * 查询未发布的标准
     *
     * @return
     */
    public List<DataStandardConfigTreeDTO> unpublishedStandards(Integer type) {
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        QueryWrapper<DataStandardConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type",type).eq("tenant_id", tenantId).eq("publish_status", PublishStatusEnum.UNPUBLISHED.getCode());
        List<DataStandardConfig> configs = configService.list(queryWrapper);
        if (CollectionUtils.isEmpty(configs)) {
            return null;
        }
        Map<Long, List<DataStandardConfig>> standardSetMap = configs.stream().collect(Collectors.groupingBy(DataStandardConfig::getSetId));
        List<DataStandardConfigTreeDTO> trees = new ArrayList<>();
        List<Long> setIds = configs.stream().map(x -> x.getSetId()).distinct().collect(Collectors.toList());
        for (Long setId : setIds) {
            DataStandardSet set = standardSetService.getById(setId);
            DataStandardConfigTreeDTO dto = new DataStandardConfigTreeDTO();
            dto.setSetName(set.getName());
            dto.setChildren(standardSetMap.get(setId));
            trees.add(dto);
        }
        return trees;
    }

    /**
     * 查询多版本标准
     *
     * @return
     */
    public List<DataStandardConfigTreeDTO> multiVersionStandards() {
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        // 查询当前版本
        List<String> currentVersionNos = configService.listObjs(new QueryWrapper<DataStandardConfig>()
                .select("distinct version_no")
                .eq("tenant_id", tenantId)
                .isNotNull("version_no"), (o -> o.toString()));
        // 查询已有多个版本的标准，排除当前版本
        QueryWrapper<QuaDataStandardConfigVersionSnapshot> wrapper = new QueryWrapper<QuaDataStandardConfigVersionSnapshot>()
                .select("distinct config_id")
                .eq("tenant_id", tenantId);
        if(CollectionUtils.isNotEmpty(currentVersionNos)) {
            wrapper.notIn("version_no", currentVersionNos);
        }
        List<Long> configIds = versionSnapshotService.listObjs(wrapper, (o -> Long.valueOf(o.toString())));
        if (CollectionUtils.isEmpty(configIds)) {
            return null;
        }
        QueryWrapper<DataStandardConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", configIds);
        List<DataStandardConfig> configs = configService.list(queryWrapper);
        if (CollectionUtils.isEmpty(configs)) {
            return null;
        }
        Map<Long, List<DataStandardConfig>> standardSetMap = configs.stream().collect(Collectors.groupingBy(DataStandardConfig::getSetId));
        List<DataStandardConfigTreeDTO> trees = new ArrayList<>();
        List<Long> setIds = configs.stream().map(x -> x.getSetId()).distinct().collect(Collectors.toList());
        for (Long setId : setIds) {
            DataStandardSet set = standardSetService.getById(setId);
            DataStandardConfigTreeDTO dto = new DataStandardConfigTreeDTO();
            dto.setSetName(set.getName());
            dto.setChildren(standardSetMap.get(setId));
            trees.add(dto);
        }
        return trees;
    }

    /**
     * 根据标准id查询非当前版本号
     *
     * @param configId
     * @return
     */
    public List<String> queryVersionsByConfigId(Long configId) {
        // 查出当前版本号
        DataStandardConfig config = configService.getById(configId);
        QueryWrapper<QuaDataStandardConfigVersionSnapshot> wrapper = new QueryWrapper<QuaDataStandardConfigVersionSnapshot>()
                .select("version_no")
                .eq("config_id", configId)
                .ne("version_no", config.getVersionNo());
        List<String> versions = versionSnapshotService.listObjs(wrapper, (o -> o.toString()));
        return versions;
    }

    /**
     * 新增恢复
     *
     * @param dto
     * @return 成功或失败
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveRestoreLog(QuaDataStandardConfigRestoreLogDTO dto) {
        DataStandardConfig config = null;
        Integer restoreResult = 0;
        // 数据恢复
        try {
            QuaDataStandardConfigVersionSnapshot snapshot =
                    versionSnapshotService.getOne(new QueryWrapper<QuaDataStandardConfigVersionSnapshot>()
                            .eq("config_id", dto.getConfigId()).eq("version_no", dto.getFromVersionNo()));
            config = configService.getById(dto.getConfigId());
            config.setCnName(snapshot.getCnName());
            config.setEnName(snapshot.getEnName());
            config.setCode(snapshot.getCode());
            config.setBusinessMeaning(snapshot.getBusinessMeaning());
            config.setAccordance(snapshot.getAccordance());
            config.setStandardType(snapshot.getStandardType());
            config.setDataLength(snapshot.getDataLength());
            config.setDataColumnType(snapshot.getDataColumnType());
            config.setDataPrecision(snapshot.getDataPrecision());
            config.setCnEnVerification(snapshot.getCnEnVerification());
            config.setCustomContent(snapshot.getCustomContent());
            config.setRemark(snapshot.getRemark());
            config.setUpdateTime(LocalDateTime.now());
            config.setUpdateUser(UserUtil.getCurrentUsername());
            config.setEncrypt(snapshot.getEncrypt());
            config.setDept(snapshot.getDept());
            config.setBusinessSystem(snapshot.getBusinessSystem());
            config.setStoreCycle(snapshot.getStoreCycle());
            config.setDatabaseName(snapshot.getDatabaseName());
            config.setSensitiveType(snapshot.getSensitiveType());
            config.setSpecification(snapshot.getSpecification());
            config.setVersionNo(dto.getRestoreVersionNo());
            changeToUnpublished(config);
            configService.updateById(config);
        } catch (Exception e) {
            restoreResult = 1;
            logger.error("恢复失败: {}", e.getMessage(), e);
        }
        // 生成恢复日志
        QuaDataStandardConfigRestoreLog restoreLog = new QuaDataStandardConfigRestoreLog();
        restoreLog.setConfigId(dto.getConfigId());
        restoreLog.setFromVersionNo(dto.getFromVersionNo());
        restoreLog.setRestoreVersionNo(dto.getRestoreVersionNo());
        restoreLog.setRestoreResult(restoreResult);
        restoreLog.setRestoreDesc(dto.getRestoreDesc());
        restoreLog.setRestoreTime(LocalDateTime.now());
        restoreLog.setRestoreUser(UserUtil.getCurrentUsername());
        restoreLogService.save(restoreLog);
        // 恢复成功则生成新快照
        if (restoreResult == 0) {
            genVersionSnapshot(config);
        }
        return restoreResult == 0;
    }

}