/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-07
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-07
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadatatag.dto;

import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTag;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>元数据标签值 dto类</p>
 * @since 2024-06-07
 */
@Data
public class DataMetaTagDTO extends DataMetaTag {
    private List<Long> ids;

    /**
     * 导入文件地址
     */
    private String filePath;

}