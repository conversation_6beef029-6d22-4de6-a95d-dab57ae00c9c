/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-14
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-14
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadatatag.dto;

import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTagMaster;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>元数据-标签关联表 dto类</p>
 * @since 2024-06-14
 */
@Data
public class DataMetaTagMasterDTO extends DataMetaTagMaster {
    private List<Long> ids;
    /**
     * 标签id集合
     */
    private List<Long> tagIds;
}