/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-14
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-14
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadatatag.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.MetaDataUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.metadatatag.dto.DataMetaTagMasterDTO;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTagMaster;
import com.idss.datalake.datagovern.metadatatag.service.IDataMetaTagMasterService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description <p>元数据-标签关联表 manager处理类</p>
 * @since 2024-06-14
 */
@Component
public class DataMetaTagMasterManager {
    private static final Logger logger = LoggerFactory.getLogger(DataMetaTagMasterManager.class);

    @Autowired
    private IDataMetaTagMasterService iDataMetaTagMasterService;

    public void create(DataMetaTagMasterDTO dto) {
        if (StringUtils.isAnyBlank(dto.getDatasourceType(), dto.getItemType(), dto.getTableName()) || dto.getElementId() == null) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = UserUtil.getLongCurrentTenantId();
        QueryWrapper<DataMetaTagMaster> wrapper = new QueryWrapper<DataMetaTagMaster>().eq("element_id", dto.getElementId())
                .eq("datasource_type", dto.getDatasourceType())
                .eq("item_type", dto.getItemType())
                .eq("table_name", dto.getTableName())
                .eq("tenant_id", tenantId);
        if (StringUtils.isNotBlank(dto.getDatabaseName())) {
            wrapper.eq("database_name", dto.getDatabaseName());
        }
        iDataMetaTagMasterService.remove(wrapper);
        logger.info("删除标签-元数据关联关系,tenantId:{},table:{}", tenantId, dto.getTableName());
        if (CollectionUtils.isEmpty(dto.getTagIds())) {
            return;
        }

        List<DataMetaTagMaster> tagMasterList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        String username = UserUtil.getCurrentUsername();
        for (Long tagId : dto.getTagIds()) {
            DataMetaTagMaster dataMetaTagMaster = new DataMetaTagMaster();
            dataMetaTagMaster.setTagId(tagId);
            dataMetaTagMaster.setElementId(dto.getElementId());
            dataMetaTagMaster.setDatasourceType(dto.getDatasourceType());
            dataMetaTagMaster.setItemType(dto.getItemType());
            dataMetaTagMaster.setDatabaseName(dto.getDatabaseName());
            dataMetaTagMaster.setTableName(dto.getTableName());
            dataMetaTagMaster.setFieldName(dto.getFieldName());
            dataMetaTagMaster.setItemUniqueId(MetaDataUtil.getTableUniqueId(dto.getElementId(), dto.getDatasourceType(), dto.getItemType(),
                    dto.getDatabaseName(), dto.getTableName()));

            dataMetaTagMaster.setTenantId(tenantId);
            dataMetaTagMaster.setCreateTime(now);
            dataMetaTagMaster.setUpdateTime(now);
            dataMetaTagMaster.setCreateUser(username);
            dataMetaTagMaster.setUpdateUser(username);
            tagMasterList.add(dataMetaTagMaster);
        }
        logger.info("保存标签-元数据关联关系,tenantId:{},table:{}", tenantId, dto.getTableName());
        iDataMetaTagMasterService.saveBatch(tagMasterList);
    }

}