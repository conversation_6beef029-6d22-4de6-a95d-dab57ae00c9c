/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-07
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-07
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadatatag.manager;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.datalake.common.util.FileUtils;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.masterdata.entity.QuaMasterData;
import com.idss.datalake.datagovern.masterdata.enums.MasterDataTypeEnum;
import com.idss.datalake.datagovern.masterdata.model.MasterDataRequestDto;
import com.idss.datalake.datagovern.metadatatag.dto.DataMetaTagDTO;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTag;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTagMaster;
import com.idss.datalake.datagovern.metadatatag.model.DataMetaTagCsvBean;
import com.idss.datalake.datagovern.metadatatag.service.IDataMetaTagMasterService;
import com.idss.datalake.datagovern.metadatatag.service.IDataMetaTagService;
import com.idss.radar.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p>元数据标签值 manager处理类</p>
 * @since 2024-06-07
 */
@Component
public class DataMetaTagManager {
    private static final Logger logger = LoggerFactory.getLogger(DataMetaTagManager.class);

    @Autowired
    private IDataMetaTagService iDataMetaTagService;
    @Autowired
    private IDataMetaTagMasterService iDataMetaTagMasterService;

    private String[] headers = new String[]{"标签名称", "英文缩写", "说明"};

    public Map<String, Object> createPageCondition(RequestDTO requestDTO) {
        if (requestDTO != null && requestDTO.getGlobal() != null) {
            GlobalDTO globalDTO = requestDTO.getGlobal();
            if (globalDTO.getPageNum() == null) {
                globalDTO.setPageNum(Constant.DEFAULT_PAGE_NUM);
            }
            if (globalDTO.getPageSize() == null) {
                globalDTO.setPageSize(Constant.DEFAULT_PAGE_SIZE);
            }
            if (StringUtils.isEmpty(globalDTO.getOrderType())) {
                globalDTO.setOrderType("desc");
            }
            if (StringUtils.isEmpty(globalDTO.getOrderField())) {
                globalDTO.setOrderField("createTime");
            }
        } else {
            throw new ParamInvalidException("入参错误");
        }
        return null;
    }

    /**
     * 分页
     *
     * @param requestDTO
     * @return
     * @throws Exception
     */
    public Map<String, Object> page(RequestDTO requestDTO) throws Exception {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<DataMetaTag> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataMetaTag> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UserUtil.getLongCurrentTenantId());
        if (requestDTO.getParam() != null) {
            DataMetaTagDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), DataMetaTagDTO.class);
            if (dto.getTagTypeId() != null) {
                queryWrapper.eq("tag_type_id", dto.getTagTypeId());
            }
            if (StringUtils.isNotBlank(dto.getName())) {
                queryWrapper.like("name", dto.getName());
            }
            if (StringUtils.isNotBlank(dto.getAbbreviation())) {
                queryWrapper.like("abbreviation", dto.getAbbreviation());
            }
        }
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }

        IPage<DataMetaTag> pageResult = iDataMetaTagService.page(page, queryWrapper);

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", pageResult.getRecords());
        return result;
    }

    public void create(DataMetaTagDTO dto) {
        if (StringUtils.isBlank(dto.getName())) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = UserUtil.getLongCurrentTenantId();
        if (iDataMetaTagService.count(new QueryWrapper<DataMetaTag>().eq("name", dto.getName()).eq("tenant_id", tenantId)) > 0) {
            throw new ParamInvalidException("标签名称已存在");
        }

        DataMetaTag dataMetaTag = new DataMetaTag();
        ReflectionUtil.copyLomBokProperties(dto, dataMetaTag);
        dataMetaTag.setTenantId(tenantId);
        dataMetaTag.setCreateTime(LocalDateTime.now());
        dataMetaTag.setUpdateTime(LocalDateTime.now());
        dataMetaTag.setCreateUser(UserUtil.getCurrentUsername());
        dataMetaTag.setUpdateUser(UserUtil.getCurrentUsername());
        iDataMetaTagService.save(dataMetaTag);
    }

    public void delete(List<Long> ids) {
        Long tenantId = UserUtil.getLongCurrentTenantId();
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        int count = iDataMetaTagMasterService.count(new QueryWrapper<DataMetaTagMaster>().in("tag_id", ids).eq("tenant_id", tenantId));
        if (count > 0) {
            throw new ParamInvalidException("标签已被使用，不可删除");
        }
        iDataMetaTagService.remove(new QueryWrapper<DataMetaTag>().in("id", ids).eq("tenant_id", tenantId));
    }

    public void edit(DataMetaTagDTO dto) {
        if (ObjectUtils.isEmpty(dto.getId())) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = UserUtil.getLongCurrentTenantId();
        if (iDataMetaTagService.count(new QueryWrapper<DataMetaTag>().eq("name", dto.getName()).eq("tenant_id", tenantId)
                .ne("id", dto.getId())) > 0) {
            throw new ParamInvalidException("标签名称已存在");
        }

        DataMetaTag dbOne = iDataMetaTagService.getById(dto.getId());
        ReflectionUtil.copyLomBokProperties(dto, dbOne);
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        iDataMetaTagService.saveOrUpdate(dbOne);
    }

    public DataMetaTag detail(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = UserUtil.getLongCurrentTenantId();
        QueryWrapper<DataMetaTag> wrapper = new QueryWrapper();
        wrapper.eq("tenant_id", tenantId).eq("id", id);
        return iDataMetaTagService.getOne(wrapper);
    }

    /**
     * 导入元数据标签
     *
     * @param dto
     * @throws Exception
     */
    public void importMetaTag(DataMetaTagDTO dto) throws Exception {
        String filePath = dto.getFilePath();
        if (StringUtils.isBlank(filePath) || dto.getTagTypeId() == null) {
            throw new ParamInvalidException("入参异常");
        }
        BASE64Decoder decoder = new BASE64Decoder();
        filePath = new String(decoder.decodeBuffer(filePath));
        File file = new File(filePath);
        if (!file.exists()) {
            throw new ParamInvalidException("文件不存在");
        }
        Long tenantId = UserUtil.getLongCurrentTenantId();
        List<DataMetaTag> tagList = iDataMetaTagService.list(new QueryWrapper<DataMetaTag>().eq("tenant_id", tenantId)
                .eq("tag_type_id", dto.getTagTypeId()));
        Set<String> fieldNameSets = tagList.stream().map(x -> x.getName()).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(fieldNameSets)) {
            fieldNameSets = new HashSet<>();
        }
        CsvReader reader = CsvUtil.getReader();
        List<DataMetaTagCsvBean> csvBeans = reader.read(FileUtil.getReader(file, Charset.forName("gbk")), DataMetaTagCsvBean.class);
        if (CollectionUtils.isEmpty(csvBeans)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        String username = UserUtil.getCurrentUsername();
        List<DataMetaTag> metaTags = new ArrayList<>();
        for (DataMetaTagCsvBean csvBean : csvBeans) {
            String name = csvBean.getName();
            // 中文名判重，包括数据库的和本次传入的数据
            if (fieldNameSets.contains(name)) {
                throw new ParamInvalidException(name + "不能重复");
            } else {
                fieldNameSets.add(name);
            }
            DataMetaTag tag = new DataMetaTag();
            tag.setName(name);
            tag.setAbbreviation(csvBean.getAbbreviation());
            tag.setDescription(csvBean.getDescription());

            tag.setCreateTime(now);
            tag.setCreateUser(username);
            tag.setUpdateTime(now);
            tag.setUpdateUser(username);
            tag.setTenantId(tenantId);
            tag.setTagTypeId(dto.getTagTypeId());

            metaTags.add(tag);
        }

        iDataMetaTagService.saveBatch(metaTags);
    }

    /**
     * 导出元数据标签
     *
     * @param dto
     * @param response
     * @throws ParamInvalidException
     */
    public void exportMetaTag(DataMetaTagDTO dto, HttpServletResponse response) throws ParamInvalidException {
        List<DataMetaTag> tagList = null;
        Long tenantId = Long.valueOf(UserUtil.getCurrentTenantId());
        if (!CollectionUtils.isEmpty(dto.getIds())) {
            QueryWrapper<DataMetaTag> queryWrapper = new QueryWrapper<DataMetaTag>().eq("tenant_id", tenantId).in("id", dto.getIds());
            tagList = iDataMetaTagService.list(queryWrapper);
        } else if (dto.getTagTypeId() != null) {
            QueryWrapper<DataMetaTag> queryWrapper = new QueryWrapper<DataMetaTag>().eq("tenant_id", tenantId).in("tag_type_id", dto.getTagTypeId());
            tagList = iDataMetaTagService.list(queryWrapper);
        }
        if (tagList == null) {
            throw new ParamInvalidException("请选择数据");
        }
        StringBuffer sb = new StringBuffer();
        for (String header : headers) {
            sb.append(StringUtil.createCsvColumn(header)).append(FileUtils.CSV_COLUMN_SEPARATOR);
        }
        sb.append(FileUtils.CSV_ROW_SEPARATOR);

        for (DataMetaTag tag : tagList) {
            sb.append(StringUtil.createCsvColumn(tag.getName())).append(FileUtils.CSV_COLUMN_SEPARATOR);
            sb.append(StringUtil.createCsvColumn(tag.getAbbreviation())).append(FileUtils.CSV_COLUMN_SEPARATOR);
            sb.append(StringUtil.createCsvColumn(tag.getDescription())).append(FileUtils.CSV_COLUMN_SEPARATOR);
            sb.append(FileUtils.CSV_ROW_SEPARATOR);
        }
        FileUtils.outCsvStream(response, sb, "标签_" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + ".csv");
    }
}