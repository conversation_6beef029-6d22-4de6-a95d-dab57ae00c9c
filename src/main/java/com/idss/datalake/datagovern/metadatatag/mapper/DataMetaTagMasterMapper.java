package com.idss.datalake.datagovern.metadatatag.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTag;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTagMaster;
import com.idss.datalake.datagovern.metadatatag.model.DataMetaTagAndTypeModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 元数据-标签关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
public interface DataMetaTagMasterMapper extends BaseMapper<DataMetaTagMaster> {

    List<DataMetaTagMaster> selectTagMasterByTagIds(@Param("tagList") List<Long> tagList, @Param("tenantId") Long tenantId);

    List<DataMetaTagAndTypeModel> selectTagByItemUniqueIds(@Param("uniqueIds") List<String> uniqueIds, @Param("tenantId") Long tenantId);

    List<DataMetaTagAndTypeModel> selectTagByIds(@Param("tagIds") List<Long> tagIds, @Param("tenantId") Long tenantId);

}
