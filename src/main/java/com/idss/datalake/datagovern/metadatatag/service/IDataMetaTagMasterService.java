package com.idss.datalake.datagovern.metadatatag.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTag;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTagMaster;
import com.idss.datalake.datagovern.metadatatag.model.DataMetaTagAndTypeModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 元数据-标签关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
public interface IDataMetaTagMasterService extends IService<DataMetaTagMaster> {

    List<DataMetaTagMaster> selectTagMasterByTagIds(List<Long> tagList, Long tenantId);


    List<DataMetaTagAndTypeModel> selectTagByItemUniqueIds(List<String> uniqueIds, Long tenantId);

    List<DataMetaTagAndTypeModel> selectTagByIds(List<Long> tagIds, Long tenantId);

}
