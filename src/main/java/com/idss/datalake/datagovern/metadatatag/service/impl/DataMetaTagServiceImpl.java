package com.idss.datalake.datagovern.metadatatag.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTag;
import com.idss.datalake.datagovern.metadatatag.mapper.DataMetaTagMapper;
import com.idss.datalake.datagovern.metadatatag.service.IDataMetaTagService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 元数据标签值 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Service
public class DataMetaTagServiceImpl extends ServiceImpl<DataMetaTagMapper, DataMetaTag> implements IDataMetaTagService {

}
