package com.idss.datalake.datagovern.metadatatag.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTagMaster;
import com.idss.datalake.datagovern.metadatatag.mapper.DataMetaTagMasterMapper;
import com.idss.datalake.datagovern.metadatatag.model.DataMetaTagAndTypeModel;
import com.idss.datalake.datagovern.metadatatag.service.IDataMetaTagMasterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 元数据-标签关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Service
public class DataMetaTagMasterServiceImpl extends ServiceImpl<DataMetaTagMasterMapper, DataMetaTagMaster> implements IDataMetaTagMasterService {

    @Autowired
    private DataMetaTagMasterMapper dataMetaTagMasterMapper;

    @Override
    public List<DataMetaTagMaster> selectTagMasterByTagIds(List<Long> tagList, Long tenantId) {
        return dataMetaTagMasterMapper.selectTagMasterByTagIds(tagList, tenantId);
    }

    @Override
    public List<DataMetaTagAndTypeModel> selectTagByItemUniqueIds(List<String> uniqueIds, Long tenantId) {
        return dataMetaTagMasterMapper.selectTagByItemUniqueIds(uniqueIds, tenantId);
    }

    @Override
    public List<DataMetaTagAndTypeModel> selectTagByIds(List<Long> tagIds, Long tenantId) {
        return dataMetaTagMasterMapper.selectTagByIds(tagIds, tenantId);
    }

}
