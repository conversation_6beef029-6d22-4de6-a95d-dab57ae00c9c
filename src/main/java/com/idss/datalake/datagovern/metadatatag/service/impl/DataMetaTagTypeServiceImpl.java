package com.idss.datalake.datagovern.metadatatag.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTagType;
import com.idss.datalake.datagovern.metadatatag.mapper.DataMetaTagTypeMapper;
import com.idss.datalake.datagovern.metadatatag.service.IDataMetaTagTypeService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 元数据标签 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Service
public class DataMetaTagTypeServiceImpl extends ServiceImpl<DataMetaTagTypeMapper, DataMetaTagType> implements IDataMetaTagTypeService {

}
