/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-07
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-07
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadatatag.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.metadatatag.dto.DataMetaTagTypeDTO;
import com.idss.datalake.datagovern.metadatatag.dto.QuickTagDTO;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTag;
import com.idss.datalake.datagovern.metadatatag.entity.DataMetaTagType;
import com.idss.datalake.datagovern.metadatatag.service.IDataMetaTagService;
import com.idss.datalake.datagovern.metadatatag.service.IDataMetaTagTypeService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>元数据标签 manager处理类</p>
 * @since 2024-06-07
 */
@Component
public class DataMetaTagTypeManager {
    private static final Logger logger = LoggerFactory.getLogger(DataMetaTagTypeManager.class);

    @Autowired
    private IDataMetaTagTypeService iDataMetaTagTypeService;
    @Autowired
    private IDataMetaTagService metaTagService;

    public Map<String, Object> createPageCondition(RequestDTO requestDTO) {
        if (requestDTO != null && requestDTO.getGlobal() != null) {
            GlobalDTO globalDTO = requestDTO.getGlobal();
            if (globalDTO.getPageNum() == null) {
                globalDTO.setPageNum(Constant.DEFAULT_PAGE_NUM);
            }
            if (globalDTO.getPageSize() == null) {
                globalDTO.setPageSize(Constant.DEFAULT_PAGE_SIZE);
            }
            if (StringUtils.isEmpty(globalDTO.getOrderType())) {
                globalDTO.setOrderType("desc");
            }
            if (StringUtils.isEmpty(globalDTO.getOrderField())) {
                globalDTO.setOrderField("createTime");
            }
        } else {
            throw new ParamInvalidException("入参错误");
        }
        return null;
    }

    /**
     * 分页
     *
     * @param requestDTO
     * @return
     * @throws Exception
     */
    public Map<String, Object> page(RequestDTO requestDTO) throws Exception {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<DataMetaTagType> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataMetaTagType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UserUtil.getLongCurrentTenantId());
        if (requestDTO.getParam() != null) {
            DataMetaTagTypeDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), DataMetaTagTypeDTO.class);
            if (StringUtils.isNotBlank(dto.getName())) {
                queryWrapper.like("name", dto.getName());
            }
            if (StringUtils.isNotBlank(dto.getAbbreviation())) {
                queryWrapper.like("abbreviation", dto.getAbbreviation());
            }
            if (CollectionUtils.isNotEmpty(dto.getIsQuickTags())) {
                queryWrapper.in("is_quick_tag", dto.getIsQuickTags());
            }
        }
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }

        IPage<DataMetaTagType> pageResult = iDataMetaTagTypeService.page(page, queryWrapper);

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", pageResult.getRecords());
        return result;
    }

    public void create(DataMetaTagTypeDTO dto) {
        if (StringUtils.isBlank(dto.getName())) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = UserUtil.getLongCurrentTenantId();
        if (iDataMetaTagTypeService.count(new QueryWrapper<DataMetaTagType>().eq("name", dto.getName()).eq("tenant_id", tenantId)) > 0) {
            throw new ParamInvalidException("标签类型已存在");
        }

        String username = UserUtil.getCurrentUsername();
        DataMetaTagType dataMetaTagType = new DataMetaTagType();
        ReflectionUtil.copyLomBokProperties(dto, dataMetaTagType);
        dataMetaTagType.setTenantId(tenantId);
        dataMetaTagType.setCreateTime(LocalDateTime.now());
        dataMetaTagType.setUpdateTime(LocalDateTime.now());
        dataMetaTagType.setCreateUser(username);
        dataMetaTagType.setUpdateUser(username);
        iDataMetaTagTypeService.save(dataMetaTagType);
    }

    public void delete(List<Long> ids) {
        Long tenantId = UserUtil.getLongCurrentTenantId();
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        if (metaTagService.count(new QueryWrapper<DataMetaTag>().in("tag_type_id", ids).eq("tenant_id", tenantId)) > 0) {
            throw new ParamInvalidException("该标签类型下存在标签，不可删除");
        }
        iDataMetaTagTypeService.remove(new QueryWrapper<DataMetaTagType>().in("id", ids).eq("tenant_id", tenantId));
    }

    public void edit(DataMetaTagTypeDTO dto) {
        if (ObjectUtils.isEmpty(dto.getId())) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = UserUtil.getLongCurrentTenantId();
        if (iDataMetaTagTypeService.count(new QueryWrapper<DataMetaTagType>().eq("name", dto.getName()).eq("tenant_id", tenantId)
                .ne("id", dto.getId())) > 0) {
            throw new ParamInvalidException("标签类型已存在");
        }
        DataMetaTagType dbOne = iDataMetaTagTypeService.getById(dto.getId());
        ReflectionUtil.copyLomBokProperties(dto, dbOne);
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        iDataMetaTagTypeService.saveOrUpdate(dbOne);
    }

    public DataMetaTagType detail(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        Long tenantId = UserUtil.getLongCurrentTenantId();
        QueryWrapper<DataMetaTagType> wrapper = new QueryWrapper();
        wrapper.eq("tenant_id", tenantId).eq("id", id);
        return iDataMetaTagTypeService.getOne(wrapper);
    }

    /**
     * 查询所有标签类型
     *
     * @return
     * @throws Exception
     */
    public List<DataMetaTagType> queryAll() throws Exception {
        Long tenantId = UserUtil.getLongCurrentTenantId();
        List<DataMetaTagType> tagTypeList = iDataMetaTagTypeService.list(new QueryWrapper<DataMetaTagType>().eq("tenant_id", tenantId));
        if (CollectionUtils.isNotEmpty(tagTypeList)) {
            for (DataMetaTagType tagType : tagTypeList) {
                List<DataMetaTag> tagList = metaTagService.list(new QueryWrapper<DataMetaTag>().eq("tenant_id", tenantId)
                        .eq("tag_type_id", tagType.getId()));
                tagType.setTagList(tagList);
            }
        }

        return tagTypeList;
    }

    /**
     * 查询所有标签类型，分离出快捷标签和基础标签
     *
     * @return
     */
    public QuickTagDTO queryQuickAndBaseTag() throws Exception {
        QuickTagDTO quickTagDTO = new QuickTagDTO();
        List<DataMetaTagType> tagTypeList = queryAll();
        if (CollectionUtils.isEmpty(tagTypeList)) {
            return quickTagDTO;
        }
        for (DataMetaTagType tagType : tagTypeList) {
            if (tagType.getIsQuickTag() == 1) {
                quickTagDTO.getQuickTagList().add(tagType);
            } else {
                quickTagDTO.getBaseTagList().add(tagType);
            }
        }
        return quickTagDTO;
    }
}