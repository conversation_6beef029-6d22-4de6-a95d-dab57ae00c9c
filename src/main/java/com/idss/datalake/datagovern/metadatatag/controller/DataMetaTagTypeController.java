/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-07
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-07
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.metadatatag.controller;


import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datagovern.metadatatag.dto.DataMetaTagTypeDTO;
import com.idss.datalake.datagovern.metadatatag.manager.DataMetaTagTypeManager;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description <p>元数据标签 前端控制器</p>
 * @since 2024-06-07
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/datagovern/data-meta-tag-type")
public class DataMetaTagTypeController {
    private static final Logger logger = LoggerFactory.getLogger(DataMetaTagTypeController.class);
    @Autowired
    private DataMetaTagTypeManager dataMetaTagTypeManager;

    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody DataMetaTagTypeDTO dto) {
        try {
            dataMetaTagTypeManager.create(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public ResultBean delete(@RequestBody DataMetaTagTypeDTO dto) {
        try {
            dataMetaTagTypeManager.delete(dto.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Long id) {
        try {
            return ResultBean.success(dataMetaTagTypeManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "编辑")
    @PostMapping(value = "/edit")
    public ResultBean edit(@RequestBody DataMetaTagTypeDTO dto) {
        try {
            dataMetaTagTypeManager.edit(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询列表")
    @PostMapping(value = "/page")
    public ResultBean page(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataMetaTagTypeManager.page(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询所有标签类型")
    @GetMapping(value = "/queryAll")
    public ResultBean queryAll() {
        try {
            return ResultBean.success(dataMetaTagTypeManager.queryAll());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    @ApiOperation(value = "查询所有标签类型，分离出快捷标签和基础标签")
    @GetMapping(value = "/queryQuickAndBaseTag")
    public ResultBean queryQuickAndBaseTag() {
        try {
            return ResultBean.success(dataMetaTagTypeManager.queryQuickAndBaseTag());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }
}
