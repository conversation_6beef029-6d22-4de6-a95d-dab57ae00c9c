/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-28
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-28
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.blood.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datagovern.blood.dto.CreateLineageRelationDTO;
import com.idss.datalake.datagovern.blood.dto.DataLineageRelationTableIslandDTO;
import com.idss.datalake.datagovern.blood.dto.DeleteLineageRelationDTO;
import com.idss.datalake.datagovern.blood.model.CreateLineageRelationModel;
import com.idss.datalake.datagovern.blood.dto.DataLineageRelationTableDTO;
import com.idss.datalake.datagovern.blood.manager.DataLineageRelationManager;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description <p>元数据-血缘关系表基础信息 前端控制器</p>
 * @since 2024-06-28
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/lineage")
public class DataLineageRelationController {
    private static final Logger logger = LoggerFactory.getLogger(DataLineageRelationController.class);
    @Autowired
    private DataLineageRelationManager dataLineageRelationManager;

    @ApiOperation(value = "获取血缘关系图")
    @PostMapping(value = "/getLineageRelationChart")
    public ResultBean getLineageRelationChart(@RequestBody DataLineageRelationTableDTO dto) {
        try {
            return ResultBean.success(dataLineageRelationManager.getLineageRelationChart(dto));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    @ApiOperation(value = "获取字段血缘关系图")
    @PostMapping(value = "/getColumnLineageRelationChart")
    public ResultBean getColumnLineageRelationChart(@RequestBody DataLineageRelationTableDTO dto) {
        try {
            return ResultBean.success(dataLineageRelationManager.getColumnLineageRelationChart(dto));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    @ApiOperation(value = "获取血缘关系统计")
    @GetMapping(value = "/getRelationStatistic")
    public ResultBean getRelationStatistic() {
        try {
            return ResultBean.success(dataLineageRelationManager.getRelationStatistic());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    @ApiOperation(value = "获取新建血缘关系参数")
    @PostMapping(value = "/getCreateLineageRelationParams")
    public ResultBean getCreateLineageRelationParams(@RequestBody CreateLineageRelationModel dto) {
        try {
            return ResultBean.success(dataLineageRelationManager.getCreateLineageRelationParams(dto));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    @ApiOperation(value = "创建血缘关系")
    @PostMapping(value = "/createLineageRelation")
    public ResultBean createLineageRelation(@RequestBody CreateLineageRelationDTO dto) {
        try {
            dataLineageRelationManager.createLineageRelation(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    @ApiOperation(value = "获取数据孤岛")
    @PostMapping(value = "/getRelationIsland")
    public ResultBean getRelationIsland(@RequestBody DataLineageRelationTableIslandDTO dto) {
        try {
            return ResultBean.success(dataLineageRelationManager.getRelationIsland(dto));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    @ApiOperation(value = "删除血缘关系")
    @PostMapping(value = "/deleteLineageRelation")
    public ResultBean deleteLineageRelation(@RequestBody DeleteLineageRelationDTO dto) {
        try {
            dataLineageRelationManager.deleteLineageRelation(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }
}
