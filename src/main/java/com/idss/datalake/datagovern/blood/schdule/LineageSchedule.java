/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/4/18
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/4/18
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.blood.schdule;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.common.util.MetaDataUtil;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationStatistic;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTable;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableColumn;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableIsland;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableNode;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableSource;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationStatisticService;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableColumnService;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableIslandService;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableNodeService;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableService;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableSourceService;
import com.idss.datalake.datagovern.config.entity.DataScoreCalcConfig;
import com.idss.datalake.datagovern.config.entity.QuaAssetIsolatedIslandStatistics;
import com.idss.datalake.datagovern.config.service.IDataScoreCalcConfigService;
import com.idss.datalake.datagovern.config.service.IQuaAssetIsolatedIslandStatisticsService;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailIndex;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHdfsElementDetailFile;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHiveElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.service.ChElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.detail.service.EsElementDetailIndexService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHdfsElementDetailFileService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHiveElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebMysqlElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datagovern.metadata.model.element.mapper.QuaWabElementMapper;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.datashare.tenant.service.ITbTenantService;
import com.idss.radar.datasource.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p>血缘关系统计</p>
 * @date 2024/6/28
 * @see
 */
@Component
@Slf4j
public class LineageSchedule {
    @Value("${lineage.table-citation-count:3}")
    private int tableCitationCount;
    @Value("${lineage.enable:false}")
    private boolean lineageEnable;

    @Autowired
    private QuaWabElementService quaWabElementService;
    @Autowired
    private QuaWabElementMapper quaWabElementMapper;
    @Autowired
    private ITbTenantService tenantService;
    @Autowired
    private IDataLineageRelationTableService relationTableService;
    @Autowired
    private IDataLineageRelationTableSourceService relationTableSourceService;
    @Autowired
    private IDataLineageRelationTableNodeService relationTableNodeService;
    @Autowired
    private IDataLineageRelationTableColumnService relationTableColumnService;
    @Autowired
    private IDataLineageRelationStatisticService relationStatisticService;
    @Autowired
    private IDataLineageRelationTableIslandService tableIslandService;
    @Autowired
    private IQuaAssetIsolatedIslandStatisticsService islandStatisticsService;

    @Autowired
    private IQuaWebMysqlElementDetailTableService mysqlElementDetailTableService;
    @Autowired
    private IQuaWebHiveElementDetailTableService hiveElementDetailTableService;
    @Autowired
    private ChElementDetailTableService chElementDetailTableService;
    @Autowired
    private EsElementDetailIndexService esElementDetailIndexService;
    @Autowired
    private IDataScoreCalcConfigService scoreCalcConfigService;
    // hdfs
    @Autowired
    private IQuaWebHdfsElementDetailFileService hdfsElementDetailFileService;

    @Scheduled(cron = "${lineage.schedule}")
    // @Scheduled(initialDelay = 5000, fixedDelay = 3600 * 1000 * 24)
    public void doStatistic() {
        if (!lineageEnable) {
            return;
        }
        log.info("开始统计血缘关系");
        // 按照租户轮询统计
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<TbTenant> tenants = tenantService.list(new QueryWrapper<TbTenant>()
                .eq("DEL_FLAG", "0")
                .eq("ACCOUNT_TYPE", "1")
                .eq("RESOURCE_STATUS", "2"));
        if (CollectionUtils.isEmpty(tenants)) {
            log.info("没有租户信息");
            return;
        }
        // 配置优先
        DataScoreCalcConfig calcConfig = scoreCalcConfigService.getOne(new LambdaQueryWrapper<>());
        if (calcConfig != null) {
            Integer highValueAssetScore = calcConfig.getHighValueAssetScore();
            if (highValueAssetScore != null) {
                tableCitationCount = highValueAssetScore;
            }
        }

        // 删除历史数据
        removeHistoryData();

        // 查询元数据采集信息
        List<QuaWabElement> elements = quaWabElementService.list(new QueryWrapper<QuaWabElement>().eq("flag", 1));
        for (TbTenant tenant : tenants) {
            log.info("开始统计租户[{}]血缘关系", tenant.getTenantCode());
            List<QuaWabElement> tenantElements = elements.stream().filter(x -> x.getTenantId().equals(tenant.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tenantElements)) {
                log.info("租户[{}]没有元数据采集信息", tenant.getTenantCode());
                continue;
            }
            // 根据元数据类型分类，ElementTypeEnum
            Map<String, List<QuaWabElement>> elementTypeMap = tenantElements.stream().collect(Collectors.groupingBy(QuaWabElement::getElementType));
            // etl 数据集血缘计算
            relationTableService.statEtl(tenant, elementTypeMap);
            relationTableService.statEtlSingleWeb(tenant, elementTypeMap);

            // api血缘计算
            relationTableService.statApi(tenant, null, elementTypeMap);

            // 数据开发血缘计算
            relationTableService.statDataDev(tenant, elementTypeMap);
            relationTableService.statDataDevGPL(tenant, elementTypeMap);

            // CUBE 血缘计算
            relationTableService.statCube(tenant, null, elementTypeMap);

            // GPL 血缘计算
            relationTableService.statGpl(tenant, null, elementTypeMap);
            relationTableService.statGplDataSet(tenant, null, elementTypeMap);
        }

        // 数澜任务血缘计算
        Map<String, List<QuaWabElement>> allElementTypeMap = elements.stream().collect(Collectors.groupingBy(QuaWabElement::getElementType));
        relationTableService.statDataDevShuLanOffline(allElementTypeMap);
        relationTableService.statDataDevShuLanOnline();

        // 配置文件的single血缘解析
        relationTableService.statEtlSingle(allElementTypeMap);

        for (TbTenant tenant : tenants) {
            List<QuaWabElement> tenantElements = elements.stream().filter(x -> x.getTenantId().equals(tenant.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tenantElements)) {
                continue;
            }

            // 根据元数据类型分类，ElementTypeEnum
            Map<String, List<QuaWabElement>> elementTypeMap = tenantElements.stream().collect(Collectors.groupingBy(QuaWabElement::getElementType));
            // 孤岛表保存
            statTableIsland(tenant, elementTypeMap);

            // 高价值资产统计
            statHighValueAsset(tenant, elementTypeMap);
        }

        // 孤岛资产汇总
        tableIslandStatistic();
        log.info("统计血缘关系完成");
        DatasourceType.clearDataBaseType();
    }

    /**
     * 删除历史数据
     */
    private void removeHistoryData() {
        try {
            String dayBefore = DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyy-MM-dd");
            log.info("删除日期[{}]之前的历史数据", dayBefore);
            DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
            relationTableService.remove(new LambdaQueryWrapper<DataLineageRelationTable>().lt(DataLineageRelationTable::getStatisticsDate,
                    dayBefore));
            relationTableSourceService.remove(new LambdaQueryWrapper<DataLineageRelationTableSource>().lt(DataLineageRelationTableSource::getStatisticsDate, dayBefore));
            relationTableNodeService.remove(new LambdaQueryWrapper<DataLineageRelationTableNode>().lt(DataLineageRelationTableNode::getStatisticsDate,
                    dayBefore));
            relationTableColumnService.remove(new LambdaQueryWrapper<DataLineageRelationTableColumn>().lt(DataLineageRelationTableColumn::getStatisticsDate, dayBefore));
            tableIslandService.remove(new LambdaQueryWrapper<DataLineageRelationTableIsland>().lt(DataLineageRelationTableIsland::getStatisticsDate
                    , dayBefore));
        } catch (Exception e) {
            log.error("删除历史数据异常,{}", e.getMessage(), e);
        }
    }

    /**
     * 孤岛表保存
     *
     * @param tenant
     * @param elementTypeMap
     */
    private void statTableIsland(TbTenant tenant, Map<String, List<QuaWabElement>> elementTypeMap) {
        log.info("开始保存孤岛表");
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        String statisticsDate = DateUtil.today();
        // 先删除当前租户当天的孤岛数据
        tableIslandService.remove(new LambdaQueryWrapper<DataLineageRelationTableIsland>()
                .eq(DataLineageRelationTableIsland::getTenantId, tenant.getId())
                .eq(DataLineageRelationTableIsland::getStatisticsDate, statisticsDate));
        // 再统计当前租户的孤岛数据
        for (Map.Entry<String, List<QuaWabElement>> entry : elementTypeMap.entrySet()) {
            String key = entry.getKey();
            List<QuaWabElement> elementList = entry.getValue();
            if (ElementTypeEnum.MYSQL.getCode().equals(key)) {
                for (QuaWabElement element : elementList) {
                    List<QuaWebMysqlElementDetailTable> detailTables = getMysqlElementDetailTables(element);
                    List<DataLineageRelationTableIsland> tableIslandList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(detailTables)) {
                        for (QuaWebMysqlElementDetailTable detailTable : detailTables) {
                            DataLineageRelationTableIsland tableIsland = new DataLineageRelationTableIsland();
                            String uniqueId = MetaDataUtil.getTableUniqueId(detailTable.getElementId(),
                                    DATA_SOURCE_TYPE_ENUM.MYSQL.getName(), detailTable.getDbName(),
                                    detailTable.getTableName());
                            // 如果在血缘表存在，则不是孤岛表
                            int count = relationTableService.count(new QueryWrapper<DataLineageRelationTable>().eq("tenant_id", tenant.getId())
                                    .eq("unique_id", uniqueId).eq("statistics_date", statisticsDate));
                            if (count > 0) {
                                continue;
                            }
                            tableIsland.setTableName(detailTable.getTableName())
                                    .setElementId(detailTable.getElementId())
                                    .setDatasourceType(DATA_SOURCE_TYPE_ENUM.MYSQL.getName())
                                    .setDatabaseName(detailTable.getDbName())
                                    .setCnName(detailTable.getTableNameCn())
                                    .setOwner(detailTable.getTableOwner())
                                    .setTableModifyTime(detailTable.getUpdateTime())
                                    .setUniqueId(uniqueId)
                                    .setTenantId(tenant.getId())
                                    .setStatisticsDate(statisticsDate);
                            tableIslandList.add(tableIsland);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(tableIslandList)) {
                        tableIslandService.saveBatch(tableIslandList);
                    }
                }
            } else if (ElementTypeEnum.HIVE.getCode().equals(key)) {
                for (QuaWabElement element : elementList) {
                    List<QuaWebHiveElementDetailTable> detailTables = getHiveElementDetailTables(element);
                    List<DataLineageRelationTableIsland> tableIslandList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(detailTables)) {
                        for (QuaWebHiveElementDetailTable detailTable : detailTables) {
                            DataLineageRelationTableIsland tableIsland = new DataLineageRelationTableIsland();
                            String uniqueId = MetaDataUtil.getTableUniqueId(detailTable.getElementId(),
                                    DATA_SOURCE_TYPE_ENUM.HIVE.getName(), detailTable.getDbName(),
                                    detailTable.getTableName());
                            int count = relationTableService.count(new QueryWrapper<DataLineageRelationTable>().eq("tenant_id", tenant.getId())
                                    .eq("unique_id", uniqueId).eq("statistics_date", statisticsDate));
                            if (count > 0) {
                                continue;
                            }
                            tableIsland.setTableName(detailTable.getTableName())
                                    .setElementId(detailTable.getElementId())
                                    .setDatasourceType(DATA_SOURCE_TYPE_ENUM.HIVE.getName())
                                    .setDatabaseName(detailTable.getDbName())
                                    .setCnName(detailTable.getTableNameCn())
                                    .setOwner(detailTable.getTableOwner())
                                    .setTableModifyTime(detailTable.getUpdateTime())
                                    .setUniqueId(uniqueId)
                                    .setTenantId(tenant.getId())
                                    .setStatisticsDate(statisticsDate);
                            tableIslandList.add(tableIsland);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(tableIslandList)) {
                        tableIslandService.saveBatch(tableIslandList);
                    }
                }
            } else if (ElementTypeEnum.CH.getCode().equals(key)) {
                for (QuaWabElement element : elementList) {
                    List<ChElementDetailTable> detailTables = getChElementDetailTables(element);
                    List<DataLineageRelationTableIsland> tableIslandList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(detailTables)) {
                        for (ChElementDetailTable detailTable : detailTables) {
                            DataLineageRelationTableIsland tableIsland = new DataLineageRelationTableIsland();
                            String uniqueId = MetaDataUtil.getTableUniqueId(detailTable.getElementId(),
                                    DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName(), detailTable.getDbName(),
                                    detailTable.getTableName());
                            int count = relationTableService.count(new QueryWrapper<DataLineageRelationTable>().eq("tenant_id", tenant.getId())
                                    .eq("unique_id", uniqueId).eq("statistics_date", statisticsDate));
                            if (count > 0) {
                                continue;
                            }
                            tableIsland.setTableName(detailTable.getTableName())
                                    .setElementId(detailTable.getElementId())
                                    .setDatasourceType(DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName())
                                    .setDatabaseName(detailTable.getDbName())
                                    .setCnName(detailTable.getTableNameCn())
                                    .setOwner(detailTable.getTableOwner())
                                    .setTableModifyTime(detailTable.getUpdateTime())
                                    .setUniqueId(uniqueId)
                                    .setTenantId(tenant.getId())
                                    .setStatisticsDate(statisticsDate);
                            tableIslandList.add(tableIsland);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(tableIslandList)) {
                        tableIslandService.saveBatch(tableIslandList);
                    }
                }
            } else if (ElementTypeEnum.ES.getCode().equals(key)) {
                for (QuaWabElement element : elementList) {
                    List<EsElementDetailIndex> detailTables = getEsIndexList(element);
                    List<DataLineageRelationTableIsland> tableIslandList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(detailTables)) {
                        for (EsElementDetailIndex detailTable : detailTables) {
                            DataLineageRelationTableIsland tableIsland = new DataLineageRelationTableIsland();
                            String uniqueId = MetaDataUtil.getTableUniqueId(detailTable.getElementId(),
                                    DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName(), null,
                                    detailTable.getIndexName());
                            int count = relationTableService.count(new QueryWrapper<DataLineageRelationTable>().eq("tenant_id", tenant.getId())
                                    .eq("unique_id", uniqueId).eq("statistics_date", statisticsDate));
                            if (count > 0) {
                                continue;
                            }
                            tableIsland.setTableName(detailTable.getIndexName())
                                    .setElementId(detailTable.getElementId())
                                    .setDatasourceType(DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName())
                                    .setCnName(detailTable.getIndexNameCn())
                                    .setOwner(detailTable.getIndexOwner())
                                    .setTableModifyTime(detailTable.getUpdateTime())
                                    .setUniqueId(uniqueId)
                                    .setTenantId(tenant.getId())
                                    .setStatisticsDate(statisticsDate);
                            tableIslandList.add(tableIsland);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(tableIslandList)) {
                        tableIslandService.saveBatch(tableIslandList);
                    }
                }
            }
        }

        // hdfs
        List<QuaWebHdfsElementDetailFile> hdfsFileList = hdfsElementDetailFileService.list();
        if (CollectionUtils.isNotEmpty(hdfsFileList)) {
            Map<Long, List<QuaWebHdfsElementDetailFile>> elementIdMap =
                    hdfsFileList.stream().collect(Collectors.groupingBy(QuaWebHdfsElementDetailFile::getElementId));
            for (Map.Entry<Long, List<QuaWebHdfsElementDetailFile>> entry : elementIdMap.entrySet()) {
                List<DataLineageRelationTableIsland> tableIslandList = new ArrayList<>();
                Long elementId = entry.getKey();
                List<QuaWebHdfsElementDetailFile> fileList = entry.getValue();
                for (QuaWebHdfsElementDetailFile file : fileList) {
                    DataLineageRelationTableIsland tableIsland = new DataLineageRelationTableIsland();
                    String tableName = file.getDirPath() + File.separator + file.getFileName();
                    String uniqueId = MetaDataUtil.getTableUniqueId(elementId, DATA_SOURCE_TYPE_ENUM.HDFS.getName(), null, tableName);
                    int count = relationTableService.count(new QueryWrapper<DataLineageRelationTable>().eq("tenant_id", tenant.getId())
                            .eq("unique_id", uniqueId).eq("statistics_date", statisticsDate));
                    if (count > 0) {
                        continue;
                    }
                    tableIsland.setTableName(tableName)
                            .setElementId(elementId)
                            .setDatasourceType(DATA_SOURCE_TYPE_ENUM.HDFS.getName())
                            .setCnName(file.getFileNameCn())
                            .setOwner(file.getFileOwner())
                            .setTableModifyTime(file.getUpdateTime())
                            .setUniqueId(uniqueId)
                            .setTenantId(tenant.getId())
                            .setStatisticsDate(statisticsDate);
                    tableIslandList.add(tableIsland);
                }
                if (CollectionUtils.isNotEmpty(tableIslandList)) {
                    tableIslandService.saveBatch(tableIslandList);
                }
            }
        }
    }

    private List<EsElementDetailIndex> getEsIndexList(QuaWabElement element) {
        String maxVersion = quaWabElementMapper.queryMaxVersion("qua_web_es_task_result_index", element.getId());
        List<EsElementDetailIndex> detailTables = esElementDetailIndexService.list(new QueryWrapper<EsElementDetailIndex>()
                .eq("element_id", element.getId()).eq("last_snapshoot_version", maxVersion));
        return CollectionUtils.isNotEmpty(detailTables) ? detailTables : new ArrayList<>();
    }

    private List<QuaWebMysqlElementDetailTable> getMysqlElementDetailTables(QuaWabElement element) {
        String maxVersion = quaWabElementMapper.queryMaxVersion("qua_web_mysql_task_result_db", element.getId());
        List<QuaWebMysqlElementDetailTable> detailTables =
                mysqlElementDetailTableService.list(new QueryWrapper<QuaWebMysqlElementDetailTable>()
                        .eq("element_id", element.getId()).eq("last_snapshoot_version", maxVersion));
        return CollectionUtils.isNotEmpty(detailTables) ? detailTables : new ArrayList<>();
    }

    private List<QuaWebHiveElementDetailTable> getHiveElementDetailTables(QuaWabElement element) {
        String maxVersion = quaWabElementMapper.queryMaxVersion("qua_web_hive_task_result_db", element.getId());
        List<QuaWebHiveElementDetailTable> detailTables = hiveElementDetailTableService.list(new QueryWrapper<QuaWebHiveElementDetailTable>()
                .eq("element_id", element.getId()).eq("last_snapshoot_version", maxVersion));
        return CollectionUtils.isNotEmpty(detailTables) ? detailTables : new ArrayList<>();
    }

    private List<ChElementDetailTable> getChElementDetailTables(QuaWabElement element) {
        String maxVersion = quaWabElementMapper.queryMaxVersion("qua_web_ch_task_result_db", element.getId());
        List<ChElementDetailTable> detailTables = chElementDetailTableService.list(new QueryWrapper<ChElementDetailTable>()
                .eq("element_id", element.getId()).eq("last_snapshoot_version", maxVersion));
        return CollectionUtils.isNotEmpty(detailTables) ? detailTables : new ArrayList<>();
    }

    /**
     * 孤岛资产统计
     */
    private void tableIslandStatistic() {
        log.info("开始统计-孤岛资产");
        try {
            String maxStatisticDate = tableIslandService.maxIslandStatisticDate();
            List<DataLineageRelationTableIsland> islandList = tableIslandService.list(new LambdaQueryWrapper<DataLineageRelationTableIsland>()
                    .eq(DataLineageRelationTableIsland::getStatisticsDate, maxStatisticDate));
            if (CollectionUtils.isEmpty(islandList)) {
                log.info("没有孤岛资产");
                return;
            }
            List<QuaAssetIsolatedIslandStatistics> statisticsList = new ArrayList<>();
            Map<String, List<DataLineageRelationTableIsland>> dsTypeMap =
                    islandList.stream().collect(Collectors.groupingBy(DataLineageRelationTableIsland::getDatasourceType));
            for (Map.Entry<String, List<DataLineageRelationTableIsland>> entry : dsTypeMap.entrySet()) {
                String dsType = entry.getKey();
                List<DataLineageRelationTableIsland> tables = entry.getValue();
                QuaAssetIsolatedIslandStatistics statistics = new QuaAssetIsolatedIslandStatistics();
                statistics.setDatasourceType(dsType);
                statistics.setNum(Long.valueOf(tables.size()));
                statistics.setStatus(0);
                statistics.setStatisticDate(DateUtil.today());
                statistics.setCreateTime(LocalDateTime.now());
                statisticsList.add(statistics);
            }
            islandStatisticsService.remove(new LambdaUpdateWrapper<QuaAssetIsolatedIslandStatistics>()
                    .eq(QuaAssetIsolatedIslandStatistics::getStatisticDate, DateUtil.today()));
            islandStatisticsService.saveBatch(statisticsList);
            log.info("孤岛资产-统计结束");
        } catch (Exception e) {
            log.error("孤岛资产统计异常,{}", e.getMessage(), e);
        }
    }

    private void statHighValueAsset(TbTenant tenant, Map<String, List<QuaWabElement>> elementTypeMap) {
        log.info("开始租户[{}]数据资产-高价值资产统计", tenant.getTenantCode());
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        DataLineageRelationStatistic relationStatistic = new DataLineageRelationStatistic();

        List<QuaWabElement> chElementList = elementTypeMap.get(ElementTypeEnum.CH.getCode());
        if (CollectionUtils.isNotEmpty(chElementList)) {
            for (QuaWabElement element : chElementList) {
                List<ChElementDetailTable> chTables = getChElementDetailTables(element);
                relationStatistic.setClickhouseTotal(relationStatistic.getClickhouseTotal() + chTables.size());
            }
        }

        List<QuaWabElement> mysqlElementList = elementTypeMap.get(ElementTypeEnum.MYSQL.getCode());
        if (CollectionUtils.isNotEmpty(mysqlElementList)) {
            for (QuaWabElement element : mysqlElementList) {
                List<QuaWebMysqlElementDetailTable> detailTables = getMysqlElementDetailTables(element);
                relationStatistic.setMysqlTotal(relationStatistic.getMysqlTotal() + detailTables.size());
            }
        }

        List<QuaWabElement> hiveElementList = elementTypeMap.get(ElementTypeEnum.HIVE.getCode());
        if (CollectionUtils.isNotEmpty(hiveElementList)) {
            for (QuaWabElement element : hiveElementList) {
                List<QuaWebHiveElementDetailTable> detailTables = getHiveElementDetailTables(element);
                relationStatistic.setHiveTotal(relationStatistic.getHiveTotal() + detailTables.size());
            }
        }

        List<QuaWabElement> esElementList = elementTypeMap.get(ElementTypeEnum.ES.getCode());
        if (CollectionUtils.isNotEmpty(esElementList)) {
            for (QuaWabElement element : esElementList) {
                List<EsElementDetailIndex> esIndexList = getEsIndexList(element);
                relationStatistic.setEsTotal(relationStatistic.getEsTotal() + esIndexList.size());
            }
        }

        Long hdfsTotal = relationTableService.queryHdfsTotal(tenant.getId());
        relationStatistic.setHdfsTotal(hdfsTotal);

        String statisticsDate = DateUtil.today();
        // 高价值资产统计
        List<DataLineageRelationTable> highValueTables = relationTableService.queryCitationTable(tenant.getId(), tableCitationCount, statisticsDate);
        if (CollectionUtils.isNotEmpty(highValueTables)) {
            long chCount = highValueTables.stream().filter(relationTable ->
                    DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().equals(relationTable.getDatasourceType())).count();
            long mysqlCount = highValueTables.stream().filter(relationTable ->
                    DATA_SOURCE_TYPE_ENUM.MYSQL.getName().equals(relationTable.getDatasourceType())).count();
            long hiveCount = highValueTables.stream().filter(relationTable ->
                    DATA_SOURCE_TYPE_ENUM.HIVE.getName().equals(relationTable.getDatasourceType())).count();
            long esCount = highValueTables.stream().filter(relationTable ->
                    DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().equals(relationTable.getDatasourceType())).count();
            long hdfsCount = highValueTables.stream().filter(relationTable ->
                    DATA_SOURCE_TYPE_ENUM.HDFS.getName().equals(relationTable.getDatasourceType())).count();
            relationStatistic.setMysqlHighValue(mysqlCount);
            relationStatistic.setClickhouseHighValue(chCount);
            relationStatistic.setHiveHighValue(hiveCount);
            relationStatistic.setEsHighValue(esCount);
            relationStatistic.setHdfsHighValue(hdfsCount);
            // 从孤岛资产中删除
            List<String> uniqueIds = highValueTables.stream().map(DataLineageRelationTable::getUniqueId).collect(Collectors.toList());
            tableIslandService.remove(new LambdaUpdateWrapper<DataLineageRelationTableIsland>()
                    .in(DataLineageRelationTableIsland::getUniqueId, uniqueIds)
                    .eq(DataLineageRelationTableIsland::getStatisticsDate, statisticsDate));
        }
        // 孤岛资产
        List<DataLineageRelationTableIsland> islandList = tableIslandService.list(new LambdaQueryWrapper<DataLineageRelationTableIsland>()
                .eq(DataLineageRelationTableIsland::getTenantId, tenant.getId())
                .eq(DataLineageRelationTableIsland::getStatisticsDate, statisticsDate));
        if (CollectionUtils.isNotEmpty(islandList)) {
            long chCount = islandList.stream().filter(relationTable ->
                    DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().equals(relationTable.getDatasourceType())).count();
            long mysqlCount = islandList.stream().filter(relationTable ->
                    DATA_SOURCE_TYPE_ENUM.MYSQL.getName().equals(relationTable.getDatasourceType())).count();
            long hiveCount = islandList.stream().filter(relationTable ->
                    DATA_SOURCE_TYPE_ENUM.HIVE.getName().equals(relationTable.getDatasourceType())).count();
            long esCount = islandList.stream().filter(relationTable ->
                    DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().equals(relationTable.getDatasourceType())).count();
            long hdfsCount = islandList.stream().filter(relationTable ->
                    DATA_SOURCE_TYPE_ENUM.HDFS.getName().equals(relationTable.getDatasourceType())).count();
            relationStatistic.setMysqlLowValue(mysqlCount);
            relationStatistic.setClickhouseLowValue(chCount);
            relationStatistic.setHiveLowValue(hiveCount);
            relationStatistic.setEsLowValue(esCount);
            relationStatistic.setHdfsLowValue(hdfsCount);
        }
        relationStatistic.setTenantId(tenant.getId());
        relationStatistic.setTableCitationCount(tableCitationCount);
        relationStatistic.setStatisticsDate(statisticsDate);

        relationStatisticService.remove(new QueryWrapper<DataLineageRelationStatistic>().eq("tenant_id", tenant.getId())
                .eq("statistics_date", statisticsDate));
        relationStatisticService.save(relationStatistic);
        log.info("完成租户[{}]数据资产-高价值资产统计,{}", tenant.getTenantCode(), JSONUtil.toJsonStr(relationStatistic));
    }

}
