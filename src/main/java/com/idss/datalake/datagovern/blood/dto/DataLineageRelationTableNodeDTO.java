/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-28
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-28
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.blood.dto;

import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableNode;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>元数据-血缘关系节点信息 dto类</p>
 * @since 2024-06-28
 */
@Data
public class DataLineageRelationTableNodeDTO extends DataLineageRelationTableNode {
    private List<Long> ids;
}