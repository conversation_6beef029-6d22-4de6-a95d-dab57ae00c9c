/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-28
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-28
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.blood.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>元数据-血缘关系节点信息</p>
 * @since 2024-06-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("data_lineage_relation_table_node")
public class DataLineageRelationTableNode implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private String id;

    /**
     * 前一个节点表id
     */
    @TableField("pre_table_id")
    private String preTableId;

    /**
     * 后一个节点表id
     */
    @TableField("next_table_id")
    private String nextTableId;

    /**
     * 节点类型，1-数据采集，2-数据开发，3-GPL，4-api接口
     */
    @TableField("node_type")
    private Integer nodeType;

    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 接入地址
     */
    @TableField("access_address")
    private String accessAddress;

    /**
     * 接入方式
     */
    @TableField("access_method")
    private String accessMethod;

    /**
     * 执行方式
     */
    @TableField("execution_method")
    private String executionMethod;

    /**
     * 任务创建时间
     */
    @TableField("task_create_time")
    private String taskCreateTime;

    /**
     * 任务创建人
     */
    @TableField("task_create_user")
    private String taskCreateUser;

    /**
     * 所属项目
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 任务类型
     */
    @TableField("task_type")
    private String taskType;

    /**
     * 任务状态
     */
    @TableField("task_status")
    private String taskStatus;

    /**
     * 负责人
     */
    @TableField("owner")
    private String owner;

    /**
     * 执行周期
     */
    @TableField("execution_cycle")
    private String executionCycle;

    /**
     * 数据集分类
     */
    @TableField("dataset_classification")
    private String datasetClassification;

    /**
     * 运行结果
     */
    @TableField("execution_result")
    private String executionResult;

    /**
     * api名称
     */
    @TableField("api_name")
    private String apiName;

    /**
     * 请求类型
     */
    @TableField("request_type")
    private String requestType;

    /**
     * 调用次数
     */
    @TableField("number_of_calls")
    private Integer numberOfCalls;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 统计日期
     */
    @TableField("statistics_date")
    private String statisticsDate;

    /**
     * 有效状态，0-有效，1-无效
     */
    @TableField("enable_state")
    private Integer enableState;

}
