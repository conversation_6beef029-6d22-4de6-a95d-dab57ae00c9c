package com.idss.datalake.datagovern.blood.enums;

/**
 * <AUTHOR>
 * @description <p>时间单位枚举类</p>
 * @date 2024/6/26
 * @see
 */
public enum DateUnitEnum {
    YEAR("year", "年"),
    MONTH("month", "月"),
    <PERSON>EE<PERSON>("week", "周"),
    DAY("day", "天"),
    HOUR("hour", "小时");

    DateUnitEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    private String name;
    private String desc;

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByName(String name) {
        for (DateUnitEnum dateUnitEnum : DateUnitEnum.values()) {
            if (dateUnitEnum.getName().equals(name)) {
                return dateUnitEnum.getDesc();
            }
        }
        return "";
    }
}
