/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-28
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-28
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.blood.dto;

import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableSource;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>元数据-血缘关系源头表 dto类</p>
 * @since 2024-06-28
 */
@Data
public class DataLineageRelationTableSourceDTO extends DataLineageRelationTableSource {
    private List<Long> ids;
}