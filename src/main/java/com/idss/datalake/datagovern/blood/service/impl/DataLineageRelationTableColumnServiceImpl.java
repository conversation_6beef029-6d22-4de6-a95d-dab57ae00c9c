package com.idss.datalake.datagovern.blood.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableColumn;
import com.idss.datalake.datagovern.blood.mapper.DataLineageRelationTableColumnMapper;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableColumnService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 元数据-血缘关系表字段信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-01
 */
@Service
public class DataLineageRelationTableColumnServiceImpl extends ServiceImpl<DataLineageRelationTableColumnMapper, DataLineageRelationTableColumn> implements IDataLineageRelationTableColumnService {

}
