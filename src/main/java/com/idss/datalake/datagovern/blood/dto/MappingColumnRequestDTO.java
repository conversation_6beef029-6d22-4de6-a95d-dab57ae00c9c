package com.idss.datalake.datagovern.blood.dto;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description <p>映射字段查询dto</p>
 * @date 2024/6/26
 * @see
 */
@Data
public class MappingColumnRequestDTO extends BasePageRequest {
    @ApiModelProperty("资源ID")
    private Long elementId;

    @ApiModelProperty("库名称")
    private String dbName;

    @ApiModelProperty("表名称")
    private String tableName;

    @ApiModelProperty("快照版本")
    private String snapshootVersion;

    @ApiModelProperty("数据源类型")
    private String datasourceType;

    private Long tenantId;

}
