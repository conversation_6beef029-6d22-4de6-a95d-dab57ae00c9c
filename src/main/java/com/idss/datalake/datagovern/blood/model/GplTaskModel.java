package com.idss.datalake.datagovern.blood.model;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>GPL任务模型</p>
 * @date 2024/7/4
 * @see
 */
@Data
public class GplTaskModel {
    private Integer id;
    private String taskName;
    private String owner;
    /**
     * 数据集分类名称
     */
    private String datasetClassification;
    /**
     * 任务创建时间
     */
    private LocalDateTime taskCreateTime;
    /**
     * 任务状态,0-失效 1-启用
     */
    private int taskStatus;
    /**
     * GPL内容，包含查询sql
     */
    private String gplContent;

    /**
     * 加速输出表名
     */
    private String tableName;
    /**
     * 输出数据源类型
     */
    private String outDatasourceType;
}
