package com.idss.datalake.datagovern.blood.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableNode;
import com.idss.datalake.datagovern.blood.mapper.DataLineageRelationTableNodeMapper;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableNodeService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 元数据-血缘关系节点信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
public class DataLineageRelationTableNodeServiceImpl extends ServiceImpl<DataLineageRelationTableNodeMapper, DataLineageRelationTableNode> implements IDataLineageRelationTableNodeService {

}
