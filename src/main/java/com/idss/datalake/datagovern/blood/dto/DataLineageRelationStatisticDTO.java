/************************ <PERSON>ANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-07-10
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-07-10
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.blood.dto;

import com.idss.datalake.datagovern.blood.entity.DataLineageRelationStatistic;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>元数据-血缘关系资产统计表 dto类</p>
 * @since 2024-07-10
 */
@Data
public class DataLineageRelationStatisticDTO extends DataLineageRelationStatistic {
    private List<Long> ids;
}