package com.idss.datalake.datagovern.blood.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SqlLineageTable {
    private List<SqlLineageTableModel> sourceTables = new ArrayList<>();
    private List<SqlLineageTableModel> targetTables = new ArrayList<>();
    /**
     * 列血缘
     */
    private List<SqlLineageColumnModel> columnModels = new ArrayList<>();

    /**
     * 是否仅查询，默认是
     */
    private boolean queryFlag = true;
}
