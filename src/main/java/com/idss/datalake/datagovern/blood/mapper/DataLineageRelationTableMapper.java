package com.idss.datalake.datagovern.blood.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationStatistic;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTable;
import com.idss.datalake.datagovern.blood.model.CubeTaskFieldModel;
import com.idss.datalake.datagovern.blood.model.CubeTaskModel;
import com.idss.datalake.datagovern.blood.model.GplTaskModel;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 元数据-血缘关系表基础信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
public interface DataLineageRelationTableMapper extends BaseMapper<DataLineageRelationTable> {

    @Select("update data_lineage_relation_table set downstream_table_num = downstream_table_num + 1 where id = #{id}")
    void updateDownstreamTableNum(String id);

    @Select("update data_lineage_relation_table set upstream_table_num = upstream_table_num + 1 where id = #{id}")
    void updateUpstreamTableNum(String id);

    @Select("select " +
            "t1.id as cube_id, " +
            "t1.name as project_name, " +
            "t1.name as task_name, " +
            "t1.create_user as owner, " +
            "t1.time_interval_unit, " +
            "t1.data_source as source_table, " +
            "t1.table_name as target_table " +
            "from " +
            "model_cube_base_info t1 " +
            "where " +
            " t1.task_result = 1")
    List<CubeTaskModel> queryCubeTaskModels();

    List<CubeTaskModel> queryCubeTaskModelsById(List<Long> ids);

    @Select("select new_field,origin_field from model_cube_cfg where cube_id = #{cubeId}")
    List<CubeTaskFieldModel> queryCubeTaskFields(Long cubeId);

    @Select("select id," +
            "dataset_name as task_name, " +
            "category_name as dataset_classification, " +
            "create_user as owner, " +
            "create_time as task_create_time, " +
            "status as task_status, " +
            "gpl_content  " +
            "from " +
            "dataset_base")
    List<GplTaskModel> queryGplTaskModels();

    /**
     * 数据集加速任务
     *
     * @return
     */
    @Select(" select " +
            "db.id, " +
            "db.dataset_name as task_name," +
            "db.category_name as dataset_classification," +
            "db.create_user as owner," +
            "db.create_time as task_create_time," +
            "db.status as task_status," +
            "db.gpl_content," +
            "dac.table_name," +
            "dac.out_datasource_type " +
            "from " +
            "dataset_base db " +
            "left join dataset_accelerate_config dac on db.id = dac.dataset_id " +
            "WHERE dac.table_name is not NULL ")
    List<GplTaskModel> queryDataSetTaskModels();

    List<GplTaskModel> queryGplTaskModelsById(List<Long> ids);

    @Select("select max(statistics_date) from data_lineage_relation_table where enable_state = 0")
    String queryMaxDay();

    @Select("select count(1) as total from qua_web_hdfs_element_detail_file t where t.tenant_id = #{tenantId}")
    Long queryHdfsTotal(Long tenantId);

    /**
     * 查询引用次数超过指定次数的表
     *
     * @param citationCount
     * @return
     */
    @Select("select * from data_lineage_relation_table " +
            "where tenant_id = #{tenantId} " +
            "and statistics_date = #{statisticsDate} " +
            "and id in " +
            "(select source_table_id from data_lineage_relation_table_reference " +
            "group by source_table_id having count(source_table_id) >= #{citationCount} )")
    List<DataLineageRelationTable> queryCitationTable(Long tenantId, int citationCount, String statisticsDate);

    @Select("select * from data_lineage_relation_table " +
            "where id in " +
            "(select source_table_id from data_lineage_relation_table_reference " +
            "group by source_table_id having count(source_table_id) >= #{citationCount} )")
    List<DataLineageRelationTable> queryCitationTable(int citationCount);

    @Select("select count(1) from data_lineage_relation_table " +
            "where unique_id = #{uniqueId} and id in " +
            "(select source_table_id from data_lineage_relation_table_reference " +
            "group by source_table_id having count(source_table_id) >= #{citationCount} )")
    Long highValueTableCount(String uniqueId, int citationCount);

    /**
     * 查询引用次数不超过指定次数的表
     *
     * @param citationCount
     * @return
     */
    @Select("select * from data_lineage_relation_table " +
            "where id in " +
            "(select source_table_id from data_lineage_relation_table_reference " +
            "where tenant_id = #{tenantId} " +
            "group by source_table_id having count(source_table_id) < #{citationCount} )")
    List<DataLineageRelationTable> queryUnderCitationTable(Long tenantId, int citationCount);

    @Select("select * from data_lineage_relation_statistic " +
            "where tenant_id = #{tenantId} " +
            "and statistics_date = " +
            "(select max(statistics_date) from data_lineage_relation_statistic)")
    DataLineageRelationStatistic queryRelationStatistic(Long tenantId);
}
