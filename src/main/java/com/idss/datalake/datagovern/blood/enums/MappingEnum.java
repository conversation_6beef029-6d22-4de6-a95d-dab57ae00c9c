package com.idss.datalake.datagovern.blood.enums;

/**
 * <AUTHOR>
 * @description <p>映射类型枚举类</p>
 * @date 2024/6/26
 * @see
 */
public enum MappingEnum {
    SAME_DATABASE(1, "同库映射"),
    SAME_TABLE(2, "同表映射"),
    CUSTOM(3, "自定义映射");

    MappingEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private int type;
    private String desc;

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
