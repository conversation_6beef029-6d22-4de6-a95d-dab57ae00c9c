/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-28
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-06-28
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datagovern.blood.manager;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.common.util.MetaDataUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datagovern.blood.dto.CreateLineageRelationDTO;
import com.idss.datalake.datagovern.blood.dto.DataLineageRelationTableDTO;
import com.idss.datalake.datagovern.blood.dto.DataLineageRelationTableIslandDTO;
import com.idss.datalake.datagovern.blood.dto.DeleteLineageRelationDTO;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationStatistic;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTable;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableColumn;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableIsland;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableNode;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableReference;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableSource;
import com.idss.datalake.datagovern.blood.enums.DsTaskTypeEnum;
import com.idss.datalake.datagovern.blood.enums.LineageNodeTypeEnum;
import com.idss.datalake.datagovern.blood.model.CreateLineageDetailModel;
import com.idss.datalake.datagovern.blood.model.CreateLineageRelationModel;
import com.idss.datalake.datagovern.blood.model.CubeTaskModel;
import com.idss.datalake.datagovern.blood.model.GplTaskModel;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationStatisticService;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableColumnService;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableIslandService;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableNodeService;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableReferenceService;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableService;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableSourceService;
import com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest;
import com.idss.datalake.datagovern.dictionary.vo.QueryElementVo;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailIndex;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHdfsElementDetailFile;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHiveElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebKafkaElementDetail;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebPanweiElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHdfsElementDetailFileService;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.mapper.QuaWabElementMapper;
import com.idss.datalake.datamart.enums.ElementTypeEnum;
import com.idss.datalake.datashare.dataapi.entity.ApiInfo;
import com.idss.datalake.datashare.dataapi.service.IApiInfoService;
import com.idss.datalake.datashare.dolphinescheduler.entity.DsDataSource;
import com.idss.datalake.datashare.dolphinescheduler.mapper.DsDataSourceMapper;
import com.idss.datalake.datashare.dolphinescheduler.model.DataDevTaskModel;
import com.idss.datalake.datashare.tenant.entity.TenantDTO;
import com.idss.datalake.datashare.tenant.service.ITbTenantService;
import com.idss.radar.datasource.DatasourceType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p>元数据-血缘关系表基础信息 manager处理类</p>
 * @since 2024-06-28
 */
@Component
public class DataLineageRelationManager {
    private static final Logger logger = LoggerFactory.getLogger(DataLineageRelationManager.class);
    @Autowired
    private ITbTenantService tenantService;
    @Autowired
    private QuaWabElementMapper quaWabElementMapper;
    @Autowired
    private IDataLineageRelationTableService relationTableService;
    @Autowired
    private IDataLineageRelationTableColumnService relationTableColumnService;
    @Autowired
    private IDataLineageRelationTableNodeService relationTableNodeService;
    @Autowired
    private IDataLineageRelationTableSourceService relationTableSourceService;
    @Autowired
    private IDataLineageRelationTableReferenceService relationTableReferenceService;
    @Autowired
    private DataLineageFieldMappingManager bloodFieldMappingManager;
    @Autowired
    private IDataLineageRelationStatisticService relationStatisticService;
    @Autowired
    private IDataLineageRelationTableIslandService tableIslandService;
    @Autowired
    private IApiInfoService apiInfoService;
    // ds相关
    @Autowired
    private DsDataSourceMapper dsDataSourceMapper;
    // hdfs
    @Autowired
    private IQuaWebHdfsElementDetailFileService hdfsElementDetailFileService;

    /**
     * 获取表级血缘关系图
     *
     * @param dto
     * @return
     */
    public Map<String, Object> getLineageRelationChart(DataLineageRelationTableDTO dto) {
        logger.info("getLineageRelationChart:{}", JSONUtil.toJsonStr(dto));
        Map<String, Object> result = new HashMap<>();
        if (dto.getDatasourceType().equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.HDFS.getName()) && StringUtils.isNotBlank(dto.getDatabaseName())
                && StringUtils.isNotBlank(dto.getTableName())) {
            dto.setDirPath(dto.getDatabaseName());
            dto.setFileName(dto.getTableName());
            dto.setDatabaseName("");
            dto.setTableName("");
        }
        String tableName;
        if (StringUtils.isNotBlank(dto.getFileName()) && StringUtils.isNotBlank(dto.getDirPath())) {
            tableName = dto.getDirPath() + File.separator + dto.getFileName();
        } else if (StringUtils.isNotBlank(dto.getIndexName())) {
            tableName = dto.getIndexName();
        } else if (StringUtils.isNotBlank(dto.getTopicName())) {
            tableName = dto.getTopicName();
        } else {
            tableName = dto.getTableName();
        }
        String maxDay = relationTableService.queryMaxDay();
        String uniqueId = MetaDataUtil.getTableUniqueId(dto.getElementId(), dto.getDatasourceType(), dto.getDatabaseName(), tableName);
        DataLineageRelationTable relationTable = relationTableService.getOne(new QueryWrapper<DataLineageRelationTable>()
                .eq("unique_id", uniqueId).eq("statistics_date", maxDay));
        if (relationTable == null) {
            relationTable = new DataLineageRelationTable();
            if (dto.getDatasourceType().equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName())) {
                ChElementDetailTable detailTable = quaWabElementMapper.queryLatestChTable(dto.getElementId(), dto.getDatabaseName(),
                        tableName);
                if (detailTable != null) {
                    relationTable.setId(IdUtil.fastSimpleUUID());
                    relationTable.setElementId(detailTable.getElementId());
                    relationTable.setDatasourceType(DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName());
                    relationTable.setDatabaseName(detailTable.getDbName());
                    relationTable.setTableName(detailTable.getTableName());
                    relationTable.setCnName(detailTable.getTableNameCn());
                    relationTable.setOwner(detailTable.getTableOwner());
                    relationTable.setUpstreamTableNum(0);
                    relationTable.setDownstreamTableNum(0);
                }
            } else if (dto.getDatasourceType().equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.HIVE.getName())) {
                QuaWebHiveElementDetailTable detailTable = quaWabElementMapper.queryLatestHiveTable(dto.getElementId(),
                        dto.getDatabaseName(), tableName);
                if (detailTable != null) {
                    relationTable.setId(IdUtil.fastSimpleUUID());
                    relationTable.setElementId(detailTable.getElementId());
                    relationTable.setDatasourceType(DATA_SOURCE_TYPE_ENUM.HIVE.getName());
                    relationTable.setDatabaseName(detailTable.getDbName());
                    relationTable.setTableName(detailTable.getTableName());
                    relationTable.setCnName(detailTable.getTableNameCn());
                    relationTable.setOwner(detailTable.getTableOwner());
                    relationTable.setUpstreamTableNum(0);
                    relationTable.setDownstreamTableNum(0);
                }
            } else if (dto.getDatasourceType().equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.MYSQL.getName())) {
                QuaWebMysqlElementDetailTable detailTable = quaWabElementMapper.queryLatestMysqlTable(dto.getElementId(),
                        dto.getDatabaseName(), tableName);
                if (detailTable != null) {
                    relationTable.setId(IdUtil.fastSimpleUUID());
                    relationTable.setElementId(detailTable.getElementId());
                    relationTable.setDatasourceType(DATA_SOURCE_TYPE_ENUM.MYSQL.getName());
                    relationTable.setDatabaseName(detailTable.getDbName());
                    relationTable.setTableName(detailTable.getTableName());
                    relationTable.setCnName(detailTable.getTableNameCn());
                    relationTable.setOwner(detailTable.getTableOwner());
                    relationTable.setUpstreamTableNum(0);
                    relationTable.setDownstreamTableNum(0);
                }
            } else if (dto.getDatasourceType().equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName())) {
                EsElementDetailIndex detailTable = quaWabElementMapper.queryLatestEsIndex(dto.getElementId(), tableName);
                if (detailTable != null) {
                    relationTable.setId(IdUtil.fastSimpleUUID());
                    relationTable.setElementId(detailTable.getElementId());
                    relationTable.setDatasourceType(DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName());
                    relationTable.setTableName(detailTable.getIndexName());
                    relationTable.setCnName(detailTable.getIndexNameCn());
                    relationTable.setOwner(detailTable.getIndexOwner());
                    relationTable.setUpstreamTableNum(0);
                    relationTable.setDownstreamTableNum(0);
                }
            } else if (dto.getDatasourceType().equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.HDFS.getName())) {
                QuaWebHdfsElementDetailFile file = hdfsElementDetailFileService.getOne(new LambdaQueryWrapper<QuaWebHdfsElementDetailFile>()
                        .eq(QuaWebHdfsElementDetailFile::getElementId, dto.getElementId())
                        .eq(QuaWebHdfsElementDetailFile::getDirPath, StringUtils.isBlank(dto.getDirPath()) ? dto.getDatabaseName() : dto.getDirPath())
                        .eq(QuaWebHdfsElementDetailFile::getFileName, StringUtils.isBlank(dto.getFileName()) ? dto.getTableName() : dto.getFileName())
                        .last("limit 1"));
                if (file != null) {
                    relationTable.setId(IdUtil.fastSimpleUUID());
                    relationTable.setElementId(file.getElementId());
                    relationTable.setDatasourceType(DATA_SOURCE_TYPE_ENUM.HDFS.getName());
                    relationTable.setTableName(file.getFileName());
                    relationTable.setDirPath(file.getDirPath());
                    relationTable.setAuthority(file.getFileAuthority());
                    relationTable.setCnName(file.getFileNameCn());
                    relationTable.setOwner(file.getFileOwner());
                    relationTable.setUpstreamTableNum(0);
                    relationTable.setDownstreamTableNum(0);
                }
            } else if (dto.getDatasourceType().equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.PANWEI.getName())) {
                QuaWebPanweiElementDetailTable detailTable = quaWabElementMapper.queryLatestPanweiTable(dto.getElementId(),
                        dto.getDatabaseName(), tableName);
                if (detailTable != null) {
                    relationTable.setId(IdUtil.fastSimpleUUID());
                    relationTable.setElementId(detailTable.getElementId());
                    relationTable.setDatasourceType(DATA_SOURCE_TYPE_ENUM.PANWEI.getName());
                    relationTable.setDatabaseName(detailTable.getDbName());
                    relationTable.setTableName(detailTable.getTableName());
                    relationTable.setCnName(detailTable.getTableNameCn());
                    relationTable.setOwner(detailTable.getTableOwner());
                    relationTable.setUpstreamTableNum(0);
                    relationTable.setDownstreamTableNum(0);
                }
            } else if (dto.getDatasourceType().equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.KAFKA.getName())) {
                QuaWebKafkaElementDetail detailTable = quaWabElementMapper.queryLatestKafkaTopic(dto.getElementId(), tableName);
                if (detailTable != null) {
                    relationTable.setId(IdUtil.fastSimpleUUID());
                    relationTable.setElementId(detailTable.getElementId());
                    relationTable.setDatasourceType(DATA_SOURCE_TYPE_ENUM.KAFKA.getName());
                    relationTable.setTableName(detailTable.getTopicName());
                    relationTable.setCnName(detailTable.getTopicNameCn());
                    relationTable.setOwner(detailTable.getTopicOwner());
                    relationTable.setUpstreamTableNum(0);
                    relationTable.setDownstreamTableNum(0);
                }
            }
            Map<String, Object> node = new HashMap<>();
            node.put("id", relationTable.getId());
            node.put("text", relationTable.getTableName());
            node.put("data", relationTable);
            result.put("nodes", ListUtil.of(node));
            result.put("lines", CollectionUtils.emptyCollection());
        } else {
            checkHdfs(relationTable, dto);
            List<Object> nodes = new ArrayList<>();
            List<Object> lines = new ArrayList<>();
            Map<String, Object> node = new HashMap<>();
            node.put("id", relationTable.getId());
            node.put("text", relationTable.getTableName());
            node.put("data", relationTable);
            nodes.add(node);

            // 获取源头表
            if (dto.isShowRoot()) {
                List<String> sourceTableIds = new ArrayList<>();
                List<DataLineageRelationTableSource> sourceTableList =
                        relationTableSourceService.list(new QueryWrapper<DataLineageRelationTableSource>().eq("table_id", relationTable.getId()));
                if (CollectionUtils.isNotEmpty(sourceTableList)) {
                    for (DataLineageRelationTableSource sourceTable : sourceTableList) {
                        sourceTable.setRoot(true);
                        Map<String, Object> rootNode = new HashMap<>();
                        rootNode.put("id", sourceTable.getId());
                        rootNode.put("text", sourceTable.getTableName());
                        rootNode.put("data", sourceTable);
                        nodes.add(rootNode);

                        // 连线数据
                        DataLineageRelationTableNode tableNode = relationTableNodeService.getOne(new QueryWrapper<DataLineageRelationTableNode>()
                                .eq("pre_table_id", sourceTable.getId()).eq("next_table_id", relationTable.getId()));
                        Map<String, Object> lineNode = new HashMap<>();
                        lineNode.put("id", tableNode.getId());
                        lineNode.put("data", tableNode);
                        nodes.add(lineNode);

                        Map<String, Object> preLine = new HashMap<>();
                        preLine.put("from", sourceTable.getId());
                        preLine.put("to", tableNode.getId());
                        preLine.put("isHideArrow", true);
                        lines.add(preLine);

                        Map<String, Object> nextLine = new HashMap<>();
                        nextLine.put("from", tableNode.getId());
                        nextLine.put("to", relationTable.getId());
                        lines.add(nextLine);
                    }
                    sourceTableIds.addAll(sourceTableList.stream().map(sourceTable -> sourceTable.getId())
                            .collect(Collectors.toList()));
                }

                // 获取上游表
                List<DataLineageRelationTableReference> tableReferences =
                        relationTableReferenceService.list(new QueryWrapper<DataLineageRelationTableReference>()
                                .eq("target_table_id", relationTable.getId()));
                if (CollectionUtils.isNotEmpty(tableReferences)) {
                    for (DataLineageRelationTableReference tableReference : tableReferences) {
                        DataLineageRelationTable sourceTable = relationTableService.getOne(new QueryWrapper<DataLineageRelationTable>().eq("id",
                                tableReference.getSourceTableId()));
                        Map<String, Object> rootNode = new HashMap<>();
                        rootNode.put("id", sourceTable.getId());
                        rootNode.put("text", sourceTable.getTableName());
                        rootNode.put("data", sourceTable);
                        nodes.add(rootNode);
                        sourceTableIds.add(sourceTable.getId());

                        // 连线数据
                        DataLineageRelationTableNode tableNode = relationTableNodeService.getOne(new QueryWrapper<DataLineageRelationTableNode>()
                                .eq("pre_table_id", sourceTable.getId()).eq("next_table_id", relationTable.getId()));
                        Map<String, Object> lineNode = new HashMap<>();
                        lineNode.put("id", tableNode.getId());
                        lineNode.put("data", tableNode);
                        nodes.add(lineNode);

                        Map<String, Object> preLine = new HashMap<>();
                        preLine.put("from", sourceTable.getId());
                        preLine.put("to", tableNode.getId());
                        preLine.put("isHideArrow", true);
                        lines.add(preLine);

                        Map<String, Object> nextLine = new HashMap<>();
                        nextLine.put("from", tableNode.getId());
                        nextLine.put("to", relationTable.getId());
                        lines.add(nextLine);
                    }
                }
                if (CollectionUtils.isNotEmpty(sourceTableIds)) {
                    result.put("rootId", sourceTableIds.stream().collect(Collectors.joining(",")));
                }
            }
            // 获取下游表
            List<DataLineageRelationTableReference> tableReferences =
                    relationTableReferenceService.list(new QueryWrapper<DataLineageRelationTableReference>()
                            .eq("source_table_id", relationTable.getId()));
            if (CollectionUtils.isNotEmpty(tableReferences)) {
                relationTable.setCurrentLevel(1);
                getReferenceTable(relationTable, tableReferences, nodes, lines);
            }
            // 获取api等叶子节点
            // 连线节点数据
            List<DataLineageRelationTableNode> leafNodes = relationTableNodeService.list(new QueryWrapper<DataLineageRelationTableNode>()
                    .eq("pre_table_id", relationTable.getId()).isNull("next_table_id"));
            if (CollectionUtils.isNotEmpty(leafNodes)) {
                for (DataLineageRelationTableNode leaf : leafNodes) { // api只能查询，只能是叶子节点
                    Map<String, Object> leafNode = new HashMap<>();
                    leafNode.put("id", leaf.getId());
                    leafNode.put("text", leaf.getTaskName());
                    leafNode.put("data", leaf);
                    nodes.add(leafNode);

                    // 连线数据
                    Map<String, Object> lineNode = new HashMap<>();
                    lineNode.put("from", relationTable.getId());
                    lineNode.put("to", leaf.getId());
                    lines.add(lineNode);
                }
            }
            result.put("nodes", nodes);
            result.put("lines", lines);
        }
        return result;
    }

    private void checkHdfs(DataLineageRelationTable relationTable, DataLineageRelationTableDTO dto) {
        QuaWebHdfsElementDetailFile file = hdfsElementDetailFileService.getOne(new LambdaQueryWrapper<QuaWebHdfsElementDetailFile>()
                .eq(QuaWebHdfsElementDetailFile::getElementId, dto.getElementId())
                .eq(QuaWebHdfsElementDetailFile::getDirPath, dto.getDirPath())
                .eq(QuaWebHdfsElementDetailFile::getFileName, dto.getFileName())
                .last("limit 1"));
        if (file != null) {
            relationTable.setTableName(file.getFileName());
            relationTable.setDirPath(file.getDirPath());
        }
    }

    public void getReferenceTable(DataLineageRelationTable tableSource, List<DataLineageRelationTableReference> tableReferences,
                                  List<Object> nodes, List<Object> lines) {
        if (tableSource.getCurrentLevel() > 9) { // 限制最多查询9层
            return;
        }
        for (DataLineageRelationTableReference tableReference : tableReferences) {
            DataLineageRelationTable tableTarget = relationTableService.getOne(new QueryWrapper<DataLineageRelationTable>().eq("id",
                    tableReference.getTargetTableId()));
            tableTarget.setCurrentLevel(tableSource.getCurrentLevel() + 1);
            Map<String, Object> node = new HashMap<>();
            node.put("id", tableTarget.getId());
            node.put("text", tableTarget.getTableName());
            node.put("data", tableTarget);
            nodes.add(node);

            // 连线节点数据
            DataLineageRelationTableNode tableNode = relationTableNodeService.getOne(new QueryWrapper<DataLineageRelationTableNode>()
                    .eq("pre_table_id", tableSource.getId())
                    .eq("next_table_id", tableTarget.getId())
                    .in("node_type", LineageNodeTypeEnum.getNonApiNodeTypeList()));
            if (tableNode == null) {
                nodes.remove(node);
                return;
            }
            Map<String, Object> lineNode = new HashMap<>();
            lineNode.put("id", tableNode.getId());
            lineNode.put("data", tableNode);
            nodes.add(lineNode);

            Map<String, Object> preLine = new HashMap<>();
            preLine.put("from", tableSource.getId());
            preLine.put("to", tableNode.getId());
            preLine.put("isHideArrow", true);
            lines.add(preLine);

            Map<String, Object> nextLine = new HashMap<>();
            nextLine.put("from", tableNode.getId());
            nextLine.put("to", tableTarget.getId());
            lines.add(nextLine);

            // 继续查询下游表
            List<DataLineageRelationTableReference> tableRef =
                    relationTableReferenceService.list(new QueryWrapper<DataLineageRelationTableReference>()
                            .eq("source_table_id", tableTarget.getId()));
            if (CollectionUtils.isNotEmpty(tableRef)) {
                getReferenceTable(tableTarget, tableRef, nodes, lines);
            }
        }
    }

    /**
     * 获取字段级血缘关系图
     *
     * @param dto
     * @return
     */
    public Map<String, Object> getColumnLineageRelationChart(DataLineageRelationTableDTO dto) {
        logger.info("getColumnLineageRelationChart:{}", JSONUtil.toJsonStr(dto));
        QuaWabElement quaWabElement = quaWabElementMapper.selectById(dto.getElementId());
        Map<String, Object> result = new HashMap<>();
        QueryElementRequest requestDto = new QueryElementRequest();
        requestDto.setTenantId(quaWabElement.getTenantId());
        requestDto.setElementId(dto.getElementId());
        requestDto.setPageSize(1000);
        List<QueryElementVo> list = new ArrayList<>();
        if (dto.getDatasourceType().equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName())) {
            requestDto.setElementType(ElementTypeEnum.CLICKHOUSE_FIELD.getCode());
            requestDto.setDbName(dto.getDatabaseName());
            requestDto.setTableName(dto.getTableName());
            list = bloodFieldMappingManager.queryElement(requestDto);
        } else if (dto.getDatasourceType().equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.HIVE.getName())) {
            requestDto.setElementType(ElementTypeEnum.HIVE_FIELD.getCode());
            requestDto.setDbName(dto.getDatabaseName());
            requestDto.setTableName(dto.getTableName());
            list = bloodFieldMappingManager.queryElement(requestDto);
        } else if (dto.getDatasourceType().equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.MYSQL.getName())) {
            requestDto.setElementType(ElementTypeEnum.MYSQL_FIELD.getCode());
            requestDto.setDbName(dto.getDatabaseName());
            requestDto.setTableName(dto.getTableName());
            list = bloodFieldMappingManager.queryElement(requestDto);
        } else if (dto.getDatasourceType().equalsIgnoreCase(DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName())) {
            requestDto.setElementType(ElementTypeEnum.ELASTICSEARCH_FIELD.getCode());
            requestDto.setTableName(dto.getIndexName());
            list = bloodFieldMappingManager.queryElement(requestDto);
        }
        List<Map<String, Object>> nodes = new ArrayList<>();
        String maxDay = relationTableService.queryMaxDay();
        String uniqueId = MetaDataUtil.getTableUniqueId(dto.getElementId(), dto.getDatasourceType(), dto.getDatabaseName(), dto.getTableName());
        DataLineageRelationTable relationTable = relationTableService.getOne(new QueryWrapper<DataLineageRelationTable>()
                .eq("unique_id", uniqueId).eq("statistics_date", maxDay));
        if (relationTable == null) {
            Map<String, Object> node = new HashMap<>();
            node.put("id", IdUtil.fastSimpleUUID());
            node.put("text", dto.getTableName());
            Map<String, Object> data = new HashMap<>();
            List<Map<String, Object>> columns = new ArrayList<>();
            for (QueryElementVo vo : list) {
                Map<String, Object> column = new HashMap<>();
                column.put("tableName", vo.getTableName());
                column.put("columnName", vo.getElementName());
                columns.add(column);
            }
            data.put("columns", columns);
            node.put("data", data);
            nodes.add(node);

            result.put("nodes", nodes);
            result.put("lines", CollectionUtils.emptyCollection());
            result.put("elementLines", CollectionUtils.emptyCollection());
        } else {
            Map<String, Object> node = new HashMap<>();
            node.put("id", relationTable.getId());
            node.put("text", relationTable.getTableName());
            Map<String, Object> data = new HashMap<>();
            List<Map<String, Object>> columns = new ArrayList<>();
            for (QueryElementVo vo : list) {
                Map<String, Object> column = new HashMap<>();
                column.put("tableName", vo.getTableName());
                column.put("columnName", vo.getElementName());
                columns.add(column);
            }
            data.put("columns", columns);
            node.put("data", data);
            nodes.add(node);

            List<Map<String, Object>> lines = new ArrayList<>();
            // 获取源头表
            List<DataLineageRelationTableSource> sourceTableList = relationTableSourceService.list(new QueryWrapper<DataLineageRelationTableSource>()
                    .eq("table_id", relationTable.getId()));
            if (CollectionUtils.isNotEmpty(sourceTableList)) {
                // 表之间连线
                for (DataLineageRelationTableSource sourceTable : sourceTableList) {
                    Map<String, Object> sourceNode = new HashMap<>();
                    sourceNode.put("id", sourceTable.getId());
                    sourceNode.put("text", sourceTable.getTableName());
                    Map<String, Object> columnData = new HashMap<>();
                    columnData.put("columns", CollectionUtils.emptyCollection());
                    sourceNode.put("data", columnData);
                    sourceTable.setRoot(true);
                    sourceNode.put("sourceData", sourceTable);
                    nodes.add(sourceNode);

                    Map<String, Object> line = new HashMap<>();
                    line.put("from", sourceTable.getId());
                    line.put("to", relationTable.getId());
                    lines.add(line);
                }
            }
            List<Map<String, Object>> elementLines = new ArrayList<>();
            // 获取上游表字段
            List<DataLineageRelationTableColumn> preTableColumns = relationTableColumnService.list(new QueryWrapper<DataLineageRelationTableColumn>()
                    .eq("target_table_id", relationTable.getId()));
            if (CollectionUtils.isNotEmpty(preTableColumns)) {
                getPreReferenceColumn(relationTable, preTableColumns, nodes, elementLines);
            }
            // 获取下游表字段
            List<DataLineageRelationTableColumn> nextTableColumns = relationTableColumnService.list(new QueryWrapper<DataLineageRelationTableColumn>()
                    .eq("source_table_id", relationTable.getId()));
            if (CollectionUtils.isNotEmpty(nextTableColumns)) {
                relationTable.setCurrentLevel(1);
                getNextReferenceColumn(relationTable, nextTableColumns, nodes, lines, elementLines);
            }
            result.put("nodes", nodes);
            result.put("lines", lines);
            result.put("elementLines", elementLines);
        }
        return result;
    }

    /**
     * 获取上游字段
     *
     * @param tableSource
     * @param tableColumns
     * @param nodes
     * @param elementLines
     */
    public void getPreReferenceColumn(DataLineageRelationTable tableSource, List<DataLineageRelationTableColumn> tableColumns,
                                      List<Map<String, Object>> nodes, List<Map<String, Object>> elementLines) {
        Map<String, List<DataLineageRelationTableColumn>> sourceColumnsMap =
                tableColumns.stream().collect(Collectors.groupingBy(DataLineageRelationTableColumn::getSourceTableId));
        Set<String> sourceIdSet = tableColumns.stream().map(tableColumn -> tableColumn.getSourceTableId()).collect(Collectors.toSet());
        List<DataLineageRelationTable> sourceTableList = relationTableService.list(new QueryWrapper<DataLineageRelationTable>()
                .eq("tenant_id", tableSource.getTenantId()).in("id", sourceIdSet));
        if (CollectionUtils.isEmpty(sourceTableList)) { // 当源头表是Kafka时，不需要处理
            return;
        }
        Map<String, DataLineageRelationTable> sourceTableMap =
                sourceTableList.stream().collect(Collectors.toMap(DataLineageRelationTable::getId, Function.identity()));
        for (String targetTableId : sourceIdSet) {
            DataLineageRelationTable sourceRelationTable = sourceTableMap.get(targetTableId);
            List<DataLineageRelationTableColumn> tableColumnList = sourceColumnsMap.get(targetTableId);
            Map<String, Object> node = new HashMap<>();
            node.put("id", sourceRelationTable.getId());
            node.put("text", sourceRelationTable.getTableName());
            Map<String, Object> data = new HashMap<>();
            List<Map<String, Object>> columns = new ArrayList<>();
            for (DataLineageRelationTableColumn tableColumn : tableColumnList) {
                Map<String, Object> column = new HashMap<>();
                column.put("tableName", sourceRelationTable.getTableName());
                column.put("columnName", tableColumn.getSourceColumnName());
                columns.add(column);

                // 字段连线
                Map<String, Object> elementLine = new HashMap<>();
                elementLine.put("from", sourceRelationTable.getId() + "-" + tableColumn.getSourceColumnName());
                elementLine.put("to", tableSource.getId() + "-" + tableColumn.getTargetColumnName());
                elementLines.add(elementLine);
            }
            data.put("columns", columns);
            node.put("data", data);
            nodes.add(node);
        }
    }

    public void getNextReferenceColumn(DataLineageRelationTable tableSource, List<DataLineageRelationTableColumn> tableColumns,
                                       List<Map<String, Object>> nodes, List<Map<String, Object>> lines, List<Map<String, Object>> elementLines) {
        if (tableSource.getCurrentLevel() > 9) { // 限制最多查询9层
            return;
        }
        Map<String, List<DataLineageRelationTableColumn>> targetColumnsMap =
                tableColumns.stream().collect(Collectors.groupingBy(DataLineageRelationTableColumn::getTargetTableId));
        Set<String> targetIdSet = tableColumns.stream().map(tableColumn -> tableColumn.getTargetTableId()).collect(Collectors.toSet());
        List<DataLineageRelationTable> targetTableList = relationTableService.list(new QueryWrapper<DataLineageRelationTable>()
                .eq("tenant_id", tableSource.getTenantId()).in("id", targetIdSet));
        Map<String, DataLineageRelationTable> targetTableMap =
                targetTableList.stream().collect(Collectors.toMap(DataLineageRelationTable::getId, Function.identity()));
        for (String targetTableId : targetIdSet) {
            DataLineageRelationTable targetRelationTable = targetTableMap.get(targetTableId);
            List<DataLineageRelationTableColumn> tableColumnList = targetColumnsMap.get(targetTableId);
            Map<String, Object> node = new HashMap<>();
            node.put("id", targetRelationTable.getId());
            node.put("text", targetRelationTable.getTableName());
            Map<String, Object> data = new HashMap<>();
            List<Map<String, Object>> columns = new ArrayList<>();
            for (DataLineageRelationTableColumn tableColumn : tableColumnList) {
                Map<String, Object> column = new HashMap<>();
                column.put("tableName", targetRelationTable.getTableName());
                column.put("columnName", tableColumn.getTargetColumnName());
                columns.add(column);

                // 字段连线
                Map<String, Object> elementLine = new HashMap<>();
                elementLine.put("from", tableSource.getId() + "-" + tableColumn.getSourceColumnName());
                elementLine.put("to", targetRelationTable.getId() + "-" + tableColumn.getTargetColumnName());
                elementLines.add(elementLine);
            }
            data.put("columns", columns);
            node.put("data", data);
            nodes.add(node);

            // Map<String, Object> line = new HashMap<>();
            // line.put("from", tableSource.getTableName());
            // line.put("to", targetRelationTable.getTableName());
            // lines.add(line);
        }
        for (DataLineageRelationTable relationTable : targetTableList) {
            // 获取下游表字段
            List<DataLineageRelationTableColumn> nextTableColumns = relationTableColumnService.list(new QueryWrapper<DataLineageRelationTableColumn>()
                    .eq("source_table_id", relationTable.getId()));
            if (CollectionUtils.isNotEmpty(nextTableColumns)) {
                relationTable.setCurrentLevel(tableSource.getCurrentLevel() + 1);
                getNextReferenceColumn(relationTable, nextTableColumns, nodes, lines, elementLines);
            }
        }
    }

    /**
     * 获取血缘关系统计
     *
     * @return
     */
    public DataLineageRelationStatistic getRelationStatistic() {
        Long tenantId = UserUtil.getLongCurrentTenantId();
        DataLineageRelationStatistic relationStatistic = relationTableService.queryRelationStatistic(tenantId);
        if (relationStatistic == null) {
            return new DataLineageRelationStatistic();
        }
        return relationStatistic;
    }

    /**
     * 获取新建血缘关系参数
     *
     * @param dto
     * @return
     */
    public CreateLineageRelationModel getCreateLineageRelationParams(CreateLineageRelationModel dto) {
        Long tenantId = UserUtil.getLongCurrentTenantId();
        int type = dto.getType();
        switch (type) {
            case 1:
                int taskType = dto.getTaskType();
                if (taskType == 1) {
                    DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenantId.intValue());
                    List<CubeTaskModel> cubeTaskModels = relationTableService.queryCubeTaskModels();
                    if (CollectionUtils.isNotEmpty(cubeTaskModels)) {
                        List<CreateLineageDetailModel> dataList = new ArrayList<>();
                        for (CubeTaskModel cubeTaskModel : cubeTaskModels) {
                            CreateLineageDetailModel detailDTO = new CreateLineageDetailModel();
                            detailDTO.setId(cubeTaskModel.getCubeId());
                            detailDTO.setName(cubeTaskModel.getTaskName());
                            dataList.add(detailDTO);
                        }
                        dto.setDataList(dataList);
                    }
                } else if (taskType == 2) {
                    // 获取租户数据开发任务信息
                    List<DataDevTaskModel> dataDevTaskModels = dsDataSourceMapper.queryTaskModels(tenantId, DsTaskTypeEnum.SQL.name());
                    if (CollectionUtils.isEmpty(dataDevTaskModels)) {
                        logger.info("租户[{}]没有数据开发任务", tenantId);
                        break;
                    }
                    List<DsDataSource> dsDataSources = dsDataSourceMapper.queryDataSourceByTenantId(tenantId);
                    if (CollectionUtils.isEmpty(dsDataSources)) {
                        logger.info("租户[{}]没有数据开发数据源", tenantId);
                        break;
                    }
                    List<DataDevTaskModel> devTaskModels = new ArrayList<>();
                    for (DsDataSource dsDataSource : dsDataSources) {
                        for (DataDevTaskModel dataDevTaskModel : dataDevTaskModels) {
                            String taskParams = dataDevTaskModel.getTaskParams(); // 任务执行参数
                            JSONObject taskJson = JSONUtil.parseObj(taskParams);
                            // 匹配任务数据源是否是当前数据源
                            if (taskJson.getInt("datasource") == dsDataSource.getId()) {
                                String sql = taskJson.getStr("sql");
                                if (!StringUtils.containsIgnoreCase(sql, "CREATE TABLE")) { // 如果不是建表任务，跳过
                                    continue;
                                }
                                devTaskModels.add(dataDevTaskModel);
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(devTaskModels)) {
                        List<CreateLineageDetailModel> dataList = new ArrayList<>();
                        Map<String, List<DataDevTaskModel>> projectMap =
                                devTaskModels.stream().collect(Collectors.groupingBy(DataDevTaskModel::getProjectName));
                        for (Map.Entry<String, List<DataDevTaskModel>> entry : projectMap.entrySet()) {
                            CreateLineageDetailModel detailDTO = new CreateLineageDetailModel();
                            detailDTO.setName(entry.getKey());
                            List<CreateLineageDetailModel> children = new ArrayList<>();
                            for (DataDevTaskModel model : entry.getValue()) {
                                CreateLineageDetailModel child = new CreateLineageDetailModel();
                                child.setId(model.getTaskId());
                                child.setName(model.getTaskName());
                                children.add(child);
                            }
                            detailDTO.setChildren(children);
                            dataList.add(detailDTO);
                        }
                        dto.setDataList(dataList);
                    }
                }
                break;
            case 2:
                List<ApiInfo> apiList = apiInfoService.list(new QueryWrapper<ApiInfo>().eq("tenant_id", tenantId));
                if (CollectionUtils.isNotEmpty(apiList)) {
                    List<CreateLineageDetailModel> dataList = new ArrayList<>();
                    for (ApiInfo apiInfo : apiList) {
                        CreateLineageDetailModel detailDTO = new CreateLineageDetailModel();
                        detailDTO.setId(Long.valueOf(apiInfo.getId()));
                        detailDTO.setName(apiInfo.getName());
                        dataList.add(detailDTO);
                    }
                    dto.setDataList(dataList);
                }
                break;
            case 3:
                DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenantId.intValue());
                List<GplTaskModel> gplTaskModels = relationTableService.queryGplTaskModels();
                if (CollectionUtils.isNotEmpty(gplTaskModels)) {
                    List<CreateLineageDetailModel> dataList = new ArrayList<>();
                    for (GplTaskModel gplTaskModel : gplTaskModels) {
                        CreateLineageDetailModel detailDTO = new CreateLineageDetailModel();
                        detailDTO.setId(Long.valueOf(gplTaskModel.getId()));
                        detailDTO.setName(gplTaskModel.getTaskName());
                        dataList.add(detailDTO);
                    }
                    dto.setDataList(dataList);
                }
                break;
            default:
                logger.error("不匹配的类型：" + type);
                break;
        }
        return dto;
    }

    /**
     * 创建血缘关系
     *
     * @param dto
     * @return
     */
    public void createLineageRelation(CreateLineageRelationDTO dto) {
        logger.info("开始创建血缘关系，{}", JSONUtil.toJsonStr(dto));
        Long tenantId = UserUtil.getLongCurrentTenantId();
        TenantDTO tenant = tenantService.getById(tenantId);
        int type = dto.getType();
        if (CollectionUtils.isEmpty(dto.getIdList())) {
            logger.error("没有选择任务");
            return;
        }
        Map<String, List<QuaWabElement>> elementTypeMap = getElementMaps(tenant);
        if (MapUtil.isEmpty(elementTypeMap)) {
            logger.error("没有元数据采集任务");
            return;
        }
        switch (type) {
            case 1:
                int taskType = dto.getTaskType();
                if (taskType == 1) {
                    DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenantId.intValue());
                    List<CubeTaskModel> cubeTaskModels = relationTableService.queryCubeTaskModelsById(dto.getIdList());
                    if (CollectionUtils.isEmpty(cubeTaskModels)) {
                        logger.info("租户[{}]没有cube任务，ids:{}", tenantId, dto.getIdList());
                        break;
                    }
                    relationTableService.statCube(tenant, cubeTaskModels, elementTypeMap);
                } else if (taskType == 2) {
                    // 获取租户数据开发任务信息
                    relationTableService.statDataDev(tenant, elementTypeMap);
                }
                break;
            case 2:
                // api统计
                List<ApiInfo> apiInfoList = apiInfoService.list(new QueryWrapper<ApiInfo>().in("id", dto.getIdList()));
                if (CollectionUtils.isEmpty(apiInfoList)) {
                    logger.info("租户[{}]没有api任务，ids:{}", tenantId, dto.getIdList());
                    break;
                }
                relationTableService.statApi(tenant, apiInfoList, elementTypeMap);
                break;
            case 3:
                DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenantId.intValue());
                List<GplTaskModel> gplTaskModels = relationTableService.queryGplTaskModelsById(dto.getIdList());
                if (CollectionUtils.isEmpty(gplTaskModels)) {
                    logger.info("租户[{}]没有gpl任务，ids:{}", tenantId, dto.getIdList());
                    break;
                }
                relationTableService.statGpl(tenant, gplTaskModels, elementTypeMap);
                break;
            default:
                logger.error("不匹配的类型：{}", type);
                break;
        }
    }

    private Map<String, List<QuaWabElement>> getElementMaps(TenantDTO tenant) {
        // 查询元数据采集信息
        List<QuaWabElement> elements = quaWabElementMapper.selectList(new QueryWrapper<QuaWabElement>().eq("flag", 1)
                .eq("tenant_id", tenant.getId()));
        if (CollectionUtils.isEmpty(elements)) {
            logger.info("租户[{}]没有元数据采集信息", tenant.getTenantCode());
            return MapUtil.empty();
        }
        // 根据元数据类型分类，ElementTypeEnum
        Map<String, List<QuaWabElement>> elementTypeMap = elements.stream().collect(Collectors.groupingBy(QuaWabElement::getElementType));
        return elementTypeMap;
    }

    /**
     * 获取数据孤岛
     *
     * @return
     */
    public List<DataLineageRelationTableIsland> getRelationIsland(DataLineageRelationTableIslandDTO dto) {
        Long tenantId = UserUtil.getLongCurrentTenantId();
        String maxStatisticDate = tableIslandService.maxStatisticDate(tenantId);
        if (StringUtils.isBlank(maxStatisticDate)) {
            return new ArrayList<>();
        }
        QueryWrapper<DataLineageRelationTableIsland> wrapper = new QueryWrapper<DataLineageRelationTableIsland>()
                .eq("tenant_id", tenantId).eq("statistics_date", maxStatisticDate).orderByDesc("table_modify_time");
        if (StringUtils.isNotBlank(dto.getDatasourceType())) {
            wrapper.eq("datasource_type", dto.getDatasourceType());
        }
        wrapper.last("limit 50");
        List<DataLineageRelationTableIsland> tableIslandList = tableIslandService.list(wrapper);
        return tableIslandList;
    }

    /**
     * 删除血缘关系
     *
     * @param dto
     */
    public void deleteLineageRelation(DeleteLineageRelationDTO dto) {
        logger.info("开始删除血缘关系，{}", JSONUtil.toJsonStr(dto));
        if (StringUtils.isAnyBlank(dto.getFromId(), dto.getToId())) {
            throw new RuntimeException("id不能为空");
        }
        int type = dto.getType();
        if (type == 2) { // api类型数据
            relationTableNodeService.remove(new QueryWrapper<DataLineageRelationTableNode>().eq("id", dto.getToId()));
            relationTableColumnService.remove(new QueryWrapper<DataLineageRelationTableColumn>().eq("source_table_id", dto.getFromId())
                    .eq("target_table_id", dto.getToId()));
        } else { // 普通数据表
            // fromId是连线上节点的id
            DataLineageRelationTableNode node = relationTableNodeService.getById(dto.getFromId());
            relationTableReferenceService.remove(new QueryWrapper<DataLineageRelationTableReference>().eq("source_table_id", node.getPreTableId())
                    .eq("target_table_id", node.getNextTableId()));
            relationTableColumnService.remove(new QueryWrapper<DataLineageRelationTableColumn>().eq("source_table_id", node.getPreTableId())
                    .eq("target_table_id", node.getNextTableId()));
            relationTableNodeService.removeById(dto.getFromId());
            relationTableService.remove(new QueryWrapper<DataLineageRelationTable>().eq("id", dto.getToId()));
        }
    }
}