package com.idss.datalake.datagovern.blood.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datagovern.blood.entity.DataLineageRelationTableReference;
import com.idss.datalake.datagovern.blood.mapper.DataLineageRelationTableReferenceMapper;
import com.idss.datalake.datagovern.blood.service.IDataLineageRelationTableReferenceService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 元数据-血缘关系表关联 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
public class DataLineageRelationTableReferenceServiceImpl extends ServiceImpl<DataLineageRelationTableReferenceMapper,
        DataLineageRelationTableReference> implements IDataLineageRelationTableReferenceService {

}
