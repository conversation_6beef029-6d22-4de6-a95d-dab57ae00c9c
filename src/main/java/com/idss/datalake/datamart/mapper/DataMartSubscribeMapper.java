package com.idss.datalake.datamart.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.datalake.datamart.dto.request.DataMartSubscribeDTO;
import com.idss.datalake.datamart.entity.DataMartSubscribe;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据集市订阅表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface DataMartSubscribeMapper extends BaseMapper<DataMartSubscribe> {

    @Select("select * from (SELECT dma.asset_name,dma.data_name,dma.asset_desc,dma.asset_type_code,dma.element_id ,dma.asset_type ,dma.asset_path," +
            "dma.create_user," +
            "dms.*,dmrp.name resource_pool_name, " +
            "case\n" +
            "           when dma.asset_type = 'clickhouse_table' then (select key_words\n" +
            "                                                         from qua_web_ch_element_detail_table tt\n" +
            "                                                         where tt.element_id = dma.element_id\n" +
            "                                                           and tt.db_name = dma.db_name\n" +
            "                                                           and tt.table_name = dma.table_name )\n" +
            "           when dma.asset_type = 'mysql_table' then (select key_words\n" +
            "                                                    from qua_web_mysql_element_detail_table tt\n" +
            "                                                    where tt.element_id = dma.element_id\n" +
            "                                                      and tt.db_name = dma.db_name\n" +
            "                                                      and tt.table_name = dma.table_name)\n" +
            "           when dma.asset_type = 'hive_table' then (select key_words\n" +
            "                                                   from qua_web_hive_element_detail_table tt\n" +
            "                                                   where tt.element_id = dma.element_id\n" +
            "                                                     and tt.db_name = dma.db_name\n" +
            "                                                     and tt.table_name = dma.table_name)\n" +
            "           when dma.asset_type = 'elasticsearch_index' then (select key_words\n" +
            "                                                            from qua_web_es_element_detail_index tt\n" +
            "                                                            where tt.element_id = dma.element_id\n" +
            "                                                              and tt.index_name = dma.index_name)\n" +
            "           else '' end as key_words FROM data_mart_asset dma " +
            "inner join data_mart_subscribe dms on dma.id = dms.asset_id " +
            "left join data_mart_resource_pool dmrp on dms.resource_pool_id = dmrp.id ) t0 ${ew.customSqlSegment} ")
    IPage<DataMartSubscribeDTO> getPage(Page<DataMartSubscribe> page, @Param(Constants.WRAPPER) QueryWrapper<DataMartSubscribe> wrapper);

    @Select(" select * from	(select dma.asset_name,dma.data_name,dma.asset_desc,dma.asset_type_code,dma.element_id ,dma.db_name,dma" +
            ".table_name,dma.index_name,dma.asset_type,dma.asset_path,dma.create_user,dms.* " +
            "from data_mart_asset dma inner join data_mart_subscribe dms on dma.id = dms.asset_id) t0 ${ew.customSqlSegment}")
    IPage<DataMartSubscribeDTO> getPageByTableName(Page<DataMartSubscribe> page, @Param(Constants.WRAPPER) QueryWrapper<DataMartSubscribe> wrapper);

    @Select("select date_format(t1.date,'%Y-%m-%d') as date,t1.count  as count from ( SELECT DATE(subscription_time) AS date, COUNT(*) AS count " +
            " FROM data_mart_subscribe where tenant_id = #{tenantId}" +
            " GROUP BY DATE(subscription_time) " +
            " ORDER BY date desc limit 7) t1")
    List<Map<String, Object>> subscribeByDay(Integer tenantId);

    @Select(" select t2.asset_name as assetName , count(t1.id) total" +
            " from data_mart_subscribe t1" +
            "         left join data_mart_asset t2" +
            " on t1.asset_id = t2.id where t1.tenant_id = #{tenantId}" +
            " group by t1.asset_id order by total desc limit 5")
    List<Map<String, Object>> groupByAsset(Integer tenantId);


    @Select("select t1.subscription_user, count(t1.id) total " +
            "from data_mart_subscribe t1 " +
            "where t1.tenant_id = #{tenantId} " +
            "group by t1.subscription_user " +
            "order by total desc " +
            "limit 3")
    List<Map<String, Object>> groupByUser(Integer tenantId);
}
