package com.idss.datalake.datamart.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datamart.dto.request.DataMartAssetPageRequest;
import com.idss.datalake.datamart.dto.request.DiscussPageRequest;
import com.idss.datalake.datamart.entity.DataMartAsset;
import com.idss.datalake.datamart.entity.DataMartDiscuss;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 数据集市资产评论 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
public interface DataMartDiscussMapper extends BaseMapper<DataMartDiscuss> {
    @Select("select * from data_mart_discuss where asset_id = #{assetId} order by create_time desc")
    Page<DataMartDiscuss> page(DiscussPageRequest request);
}
