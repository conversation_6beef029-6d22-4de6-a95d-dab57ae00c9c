package com.idss.datalake.datamart.mapper;

import com.idss.datalake.datamart.entity.DataMartCart;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 资产订购表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
public interface DataMartCartMapper extends BaseMapper<DataMartCart> {
    List<Map<String, Object>> listDataMartAsset(@Param("ids") List<Long> ids);
}
