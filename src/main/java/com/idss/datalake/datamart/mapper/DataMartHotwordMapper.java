package com.idss.datalake.datamart.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datamart.dto.response.HotwordVo;
import com.idss.datalake.datamart.entity.DataMartHotword;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据集市热词 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
public interface DataMartHotwordMapper extends BaseMapper<DataMartHotword> {
    @Select("SELECT hotword,COUNT(*) as hotword_count " +
            "FROM data_mart_hotword group by hotword " +
            "order by hotword_count desc limit 5")
    List<HotwordVo> queryHotwords();
}
