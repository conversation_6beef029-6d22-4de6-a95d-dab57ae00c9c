package com.idss.datalake.datamart.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datamart.dto.DatamartDesensitizationDto;
import com.idss.datalake.datamart.dto.request.*;
import com.idss.datalake.datamart.dto.response.*;
import com.idss.datalake.datamart.entity.DataMartAsset;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据集市资产 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
public interface DataMartAssetMapper extends BaseMapper<DataMartAsset> {
    List<AssetMapGroupVo> assetMapTypeList(AssetMapGroupRequest request);
    Page<AssetMapView> assetMapPage(AssetMapGroupRequest request);


    Page<QueryElementVo> queryClickhouseDbPage(QueryElementRequest requestDto);
    Page<QueryElementVo> queryClickhouseTablePage(QueryElementRequest requestDto);
    Page<QueryElementVo> queryClickhouseFieldPage(QueryElementRequest requestDto);
    Page<QueryElementVo> queryHiveDbPage(QueryElementRequest requestDto);
    Page<QueryElementVo> queryHiveTablePage(QueryElementRequest requestDto);
    Page<QueryElementVo> queryHiveFieldPage(QueryElementRequest requestDto);
    Page<QueryElementVo> queryMysqlDbPage(QueryElementRequest requestDto);
    Page<QueryElementVo> queryMysqlTablePage(QueryElementRequest requestDto);
    Page<QueryElementVo> queryMysqlFieldPage(QueryElementRequest requestDto);
    Page<QueryElementVo> queryElasticsearchIndexPage(QueryElementRequest requestDto);
    Page<QueryElementVo> queryElasticsearchFieldPage(QueryElementRequest requestDto);


    Page<DataMartAsset> page(DataMartAssetPageRequest request);


    AssetDetailVo queryClickhouseDbDetail(AssetDetailRequest request);
    AssetDetailVo queryClickhouseTableDetail(AssetDetailRequest request);
    AssetDetailVo queryHiveDbDetail(AssetDetailRequest request);
    AssetDetailVo queryHiveTableDetail(AssetDetailRequest request);
    AssetDetailVo queryMysqlDbDetail(AssetDetailRequest request);
    AssetDetailVo queryMysqlTableDetail(AssetDetailRequest request);
    AssetDetailVo queryElasticsearchIndexDetail(AssetDetailRequest request);

    Page<AssetDetailPageVo> pageClickhouseDbDetail(AssetDetailPageRequest request);
    Page<AssetDetailPageVo> pageClickhouseTableDetail(AssetDetailPageRequest request);
    Page<AssetDetailPageVo> pageHiveDbDetail(AssetDetailPageRequest request);
    Page<AssetDetailPageVo> pageHiveTableDetail(AssetDetailPageRequest request);
    Page<AssetDetailPageVo> pageMysqlDbDetail(AssetDetailPageRequest request);
    Page<AssetDetailPageVo> pageMysqlTableDetail(AssetDetailPageRequest request);
    Page<AssetDetailPageVo> pageElasticsearchIndexDetail(AssetDetailPageRequest request);

    ElementInfo queryElementInfo(@Param("elementId") Long elementId);


    @Select("select t2.type_name as typeName ," +
            " count(t1.id) as total" +
            " from data_mart_asset t1 " +
            "left join data_mart_type t2 on t1.type_id = t2.id  " +
            "where t1.tenant_id = #{tenantId} group by t1.type_id")
    List<Map<String,Object>> groupByType(Integer tenantId);

    long sensitiveDataCount(@Param("tableName") String tableName,@Param("ids") List<Long> ids);

    @Select("select t1.column_name field_name, t2.type, t2.rule_content\n" +
            "from qua_web_ch_element_detail_column t1\n" +
            "         left join api_data_desensitization t2 on t1.desensitization_id = t2.id\n" +
            "where t1.desensitization_id is not null\n" +
            "  and t1.element_id = #{elementId}\n" +
            "  and t1.db_name = #{dbName}\n" +
            "  and t1.table_name = #{tableame}")
    List<DatamartDesensitizationDto> chDesensitizationField(Long elementId,String dbName,String tableame);
    @Select("select t1.column_name field_name, t2.type, t2.rule_content\n" +
            "from qua_web_mysql_element_detail_column t1\n" +
            "         left join api_data_desensitization t2 on t1.desensitization_id = t2.id\n" +
            "where t1.desensitization_id is not null\n" +
            "  and t1.element_id = #{elementId}\n" +
            "  and t1.db_name = #{dbName}\n" +
            "  and t1.table_name = #{tableame}")
    List<DatamartDesensitizationDto> mysqlDesensitizationField(Long elementId,String dbName,String tableame);
    @Select("select t1.column_name field_name, t2.type, t2.rule_content\n" +
            "from qua_web_hive_element_detail_column t1\n" +
            "         left join api_data_desensitization t2 on t1.desensitization_id = t2.id\n" +
            "where t1.desensitization_id is not null\n" +
            "  and t1.element_id = #{elementId}\n" +
            "  and t1.db_name = #{dbName}\n" +
            "  and t1.table_name = #{tableame}")
    List<DatamartDesensitizationDto> hiveDesensitizationField(Long elementId,String dbName,String tableame);
    @Select("select t1.field_name , t2.type, t2.rule_content\n" +
            "from qua_web_es_element_detail_field t1\n" +
            "         left join api_data_desensitization t2 on t1.desensitization_id = t2.id\n" +
            "where t1.desensitization_id is not null\n" +
            "  and t1.element_id = #{elementId}\n" +
            "  and t1.index_name = #{indexName}")
    List<DatamartDesensitizationDto> esDesensitizationField(Long elementId,String indexName);
}
