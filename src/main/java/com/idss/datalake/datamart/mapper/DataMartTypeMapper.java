package com.idss.datalake.datamart.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datamart.dto.request.DataMartTypePageRequest;
import com.idss.datalake.datamart.dto.request.QueryElementRequest;
import com.idss.datalake.datamart.entity.DataMartType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 数据集市分类 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
public interface DataMartTypeMapper extends BaseMapper<DataMartType> {
    Page<DataMartType> page(DataMartTypePageRequest request);
}
