package com.idss.datalake.datamart.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据集市资源发布
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataMartAssetPublish implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 资源ID
     */
    private Long assetId;

    @TableField(exist = false)
    private Long typeId;

    /**
     * 名称
     */
    private String label;

    /**
     * 上传图片地址
     */
    private String url;

    private Integer tenantId;

    private String userId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


}
