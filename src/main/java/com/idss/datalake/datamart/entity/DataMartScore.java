package com.idss.datalake.datamart.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据集市资产评分
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataMartScore implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long assetId;

    private Integer tenantId;

    private String userName;

    /**
     * 可用性分
     */
    private Integer availableScore;

    /**
     * 可见性分
     */
    private Integer visibleScore;

    /**
     * 可信性分
     */
    private Integer credibleScore;

    private LocalDateTime createTime;


}
