package com.idss.datalake.datamart.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.idss.datalake.datamart.dto.response.QueryElementVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据集市资产
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataMartAsset implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分类ID
     */
    private Long typeId;

    @TableField(exist = false)
    private String typeName;

    /**
     * 分组ID
     */
    private Long groupId;

    @TableField(exist = false)
    private String groupName;

    /**
     * 元数据ID
     */
    private Long elementId;

    /**
     * 库名
     */
    private String dbName;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 索引名称
     */
    private String indexName;
    /**
     * 批次号
     */
    private String batchNo;

    /**
     * (CH,Mysql,Hive)库名/表名/字段名，(ES)索引名/字段名
     */
    private String assetName;

    /**
     * 库表字段索引对应的全路劲
     */
    private String assetPath;

    /**
     * 资产类型:clickhouse_db,clickhouse_table,clickhouse_field,hive_db,hive_table,hive_field,mysql_db,mysql_table,mysql_field,
     * elasticsearch_index,
     * elasticsearch_field
     */
    private String assetType;

    /**
     * 数据类型
     */
    @TableField(exist = false)
    private String dataType;

    /**
     * 类型编码.DB,TABLE,FIELD,INDEX,INDEX_FIELD
     */
    private String assetTypeCode;

    /**
     * 订阅数
     */
    private Integer subscribeNum;

    /**
     * 点赞数
     */
    private Integer thumbsUpNum;

    /**
     * 发布状态:0未发布，1已发布
     */
    private Boolean releaseStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String createUser;

    private Integer tenantId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private LocalDateTime subscribeTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private LocalDateTime thumbsUpTime;

    private String dataName;

    private String assetDesc;

    private String startTime;

    private String endTime;

    /**
     * 选择的资产集合
     */
    private String assetJson;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 当前用户是否订阅
     */
    @TableField(exist = false)
    private boolean subscribeState = false;


    /**
     * 发布状态，0未发布，1已发布
     */
    private Integer publishStatus;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    /**
     * 审批状态，0待审批 1已发布 2已拒绝 3已撤销
     */
    private Integer approvalStatus;

    /**
     * 审批说明
     */
    private String approvalDesc;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approvalTime;

    /**
     * 审批人
     */
    private String approvalUser;

    /**
     * 审批状态名称
     */
    @TableField(exist = false)
    private String approvalStatusName;

    @TableField(exist = false)
    private Long dataSize;

    /**
     * 浏览数
     */
    private Integer browseCount;

    /**
     * 关键词
     */
    @TableField(exist = false)
    private String keyWords;

    /**
     * 发布类型:0原始数据，1中间表
     */
    private Integer releaseType;
    /**
     * 任务类型：0离线，1实时'
     */
    private Integer taskType;
    /**
     * 数澜任务 ID
     */
    private Long flowId;

    /**
     * 实时或离线输入输出
     */
    private String taskInputs;
    /**
     * 实时或离线输入输出
     */
    private String taskOuts;

    /**
     * 是否手工维护: 0否，1是
     */
    private Integer isManual;
}
