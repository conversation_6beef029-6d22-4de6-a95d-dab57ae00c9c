package com.idss.datalake.datamart.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据集市分类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataMartType implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分类名称
     */
    private String typeName;

    /**
     * 分类描述
     */
    private String typeDesc;

    /**
     * 分组个数
     */
    @TableField(exist = false)
    private Integer groupCnt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String createUser;

    private Integer tenantId;


}
