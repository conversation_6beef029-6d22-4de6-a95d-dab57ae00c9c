package com.idss.datalake.datamart.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据集市资产评论
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataMartDiscuss implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long assetId;

    private Integer tenantId;

    private Integer userId;

    private String userName;

    /**
     * 评论
     */
    private String discuss;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


}
