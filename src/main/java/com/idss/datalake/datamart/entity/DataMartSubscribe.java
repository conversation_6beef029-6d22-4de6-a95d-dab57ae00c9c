/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-06-15
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-06-15
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>数据集市订阅表</p>
 * @since 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DataMartSubscribe implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据集市资产id
     */
    private Long assetId;

    /**
     * 租户id
     */
    private Integer tenantId;

    /**
     * 订阅用户id
     */
    private String userId;

    /**
     * 订阅方式，1 数据API，2 Kafka，3 Elasticsearch，4 ClickHouse
     */
    private Integer subscriptionChannel;

    /**
     * 订阅时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime subscriptionTime;

    /**
     * 订阅人
     */
    private String subscriptionUser;

    /**
     * 申请说明
     */
    private String subscriptionRemark;

    /**
     * 审批进度,1 审批中，2 同意 3 拒绝
     */
    private Integer approvalProgress;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approvalTime;

    /**
     * 审批说明
     */
    private String approvalRemark;

    /**
     * 审批人
     */
    private String approvalUser;

    /**
     * 配置进度，1 配置中 2 配置成功 3 配置失败
     */
    private Integer configurationProgress;

    /**
     * 配置人
     */
    private String configurationUser;

    /**
     * 配置时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime configurationTime;

    /**
     * 配置说明
     */
    private String configurationRemark;

    /**
     * 调用次数
     */
    private Long callNum;

    /**
     * 是否限制调用次数，0限制，1不限制
     */
    private Integer callNumLimit;

    /**
     * 使用开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    /**
     * 使用结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 是否限制使用时间，0限制，1不限制
     */
    private Integer timeLimit;

    /**
     * Kafka topic
     */
    private String kafkaTopic;

    /**
     * Kafka集群地址
     */
    private String kafkaAddress;

    /**
     * kafka端口
     */
    private Integer kafkaPort;

    /**
     * elasticsearch索引
     */
    private String elasticsearchIndex;

    /**
     * ClickHouse表名
     */
    private String clickhouseTable;

    /**
     * 数据API订阅通过后的api主键id
     */
    private Long apiId;

    /**
     * 数据用途
     */
    private Integer dataUsage;

    /**
     * 网络域
     */
    private Integer networkDomain;

    /**
     * 服务器业务IP
     */
    private String businessIp;

    /**
     * 服务器承载网IP
     */
    private String hostingNetworkIp;

    /**
     * VPC名称
     */
    private String vpcName;

    /**
     * 业务联系人电话
     */
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 所属资源池id
     */
    private Integer resourcePoolId;

    /**
     * 订阅id，根据规则生成
     */
    private String subscribeRuleId;

    private Integer configurationResourcePoolId;

    private String configurationBusinessIp;

    private String configurationHostingNetworkIp;

    private String configurationVpcName;

    private Integer configurationNetworkDomain;

    private String configurationKafkaTopic;

    private String configurationKafkaConsumerGroup;

    private String configurationHostName;
}
