/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/27
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/27
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.dto.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/2/27
 */
@Data
public class AssetDetailRequest {
    private Long id;
    private Long elementId;
    //资产类型:clickhouse_db,clickhouse_table,clickhouse_field,hive_db,hive_table,hive_field,mysql_db,mysql_table,mysql_field,elasticsearch_index,elasticsearch_field
    private String assetType;

    // dbName , tableName , indexName 必传一个
    private String dbName;
    private String tableName;
    private String indexName;
}
