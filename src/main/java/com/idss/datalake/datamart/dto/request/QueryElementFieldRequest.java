/************************ CHAN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/22
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/22
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.dto.request;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/2/22
 */
@Data
public class QueryElementFieldRequest  {
    /**
     * 元数据类型
     */
    private String elementType;

    /**
     * 数据库名
     */
    private String dbName;
    /**
     * 表名
     */
    private String tableName;

    private String indexName;

    private Long elementId;
}
