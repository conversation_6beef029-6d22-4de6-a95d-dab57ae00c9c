/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/22
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/22
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.dto.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/2/22
 */
@Data
public class QueryElementVo{
    private Long id;

    /**
     * 元数据名称
     */
    private String elementName;
    /**
     * 元数据类型
     */
    private String elementType;
    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 元数据ID
     */
    private Long elementId;

    /**
     * 元数据全路劲
     */
    private String assetPath;

    /**
     * 元数据类型
     */
    private String assetType;

    /**
     * 元数据类型编码
     */
    private String assetTypeCode;

    private String fileName;
    private String fileData;

    /**
     * 最早时间字段
     */
    private String oldTime;
    /**
     * 最新时间字段
     */
    private String newTime;

    /**
     * 数澜 taskId
     */
    private Long taskId;
}
