/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/3/2
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/3/2
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/3/2
 */
@Data
public class ElementInfo {
    private String dbIp;
    private Integer dbPort;
    private String dbUserName;
    private String dbPassword;

    private String esIpPort;
    private String esUserName;
    private String esPassword;

    @ApiModelProperty(value = "keytab文件路径")
    private String keyTabPath;
    @ApiModelProperty(value = "krb5文件路径")
    private String krb5ConfPath;
    /**
     * 是否开启kbs认证，0-不开启(默认)，1-开启
     */
    private Integer kbsEnable;
    /**
     * jdbc地址，当开启kbs认证时，会包含principal
     */
    private String jdbcUrl;

}
