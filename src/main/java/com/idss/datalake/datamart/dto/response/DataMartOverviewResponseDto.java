/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/23
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/23
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.dto.response;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/2/23
 */
@Data
public class DataMartOverviewResponseDto {

    private String sourceName;

    private String sourceType;

    private String storageType;

    private String platform;

    private String dataType;

    private String startTime;

    private String endTime;

    private Integer dataSize;

    private String etlStartTime;

    private Long flowId;
}
