/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/27
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/27
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.dto.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/2/27
 */
@Data
public class AssetDetailVo {
    private String assetJson;
    private String assetType;
    private String dataName;
    private Long assetId;
    private String name;
    private String type;
    private String desc;
    private String version;
    private String addUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private String assetPath;
    private Long dataSize;
    private BigDecimal dataSpace;

    private String startTime;
    private Integer thumbsUpNum;
    private Integer subscribeNum;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private LocalDateTime subscribeTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private LocalDateTime thumbsUpTime;

    private Integer subscribeState;

    private String endTime;

    private Long taskId;
}
