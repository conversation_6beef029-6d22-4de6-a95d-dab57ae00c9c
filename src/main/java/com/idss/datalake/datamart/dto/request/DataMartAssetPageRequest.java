/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/22
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/22
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.dto.request;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/2/22
 */
@Data
public class DataMartAssetPageRequest extends BasePageRequest {
    private String userId;
    private String assetName;
    private String assetType;
    private String assetTypeCode;
    private String dataName;
    private String typeName;
    private String groupName;

    /**
     * 排序类型：create_time,subscribe_num,thumbs_up_num
     */
    private String orderType;

    /**
     * 过滤订阅或点赞:subscribe,thumbs_up
     */
    private String filterType;

    private Long typeId;
    private Long groupId;

    /**
     * 发布状态，0未发布，1已发布
     */
    private Integer publishStatus;

    private String batchNo;

    private String keyWords;
}
