/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/27
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/2/27
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/2/27
 */
@Data
public class AssetDetailPageRequest extends BasePageRequest {
    //资产类型:clickhouse_db,clickhouse_table,clickhouse_field,hive_db,hive_table,hive_field,mysql_db,mysql_table,mysql_field,
    // elasticsearch_index,
    // elasticsearch_field
    private String assetType;
    private Long elementId;

    // dbName , tableName , indexName 必传一个
    private String dbName;
    private String tableName;
    private String fieldName;
    private String indexName;
    private String indexFieldName;
    private String version;

    /**
     * 字段类型
     */
    private String fieldType;
    /**
     * 是否主键，0-否，1-是
     */
    private Integer isPrimaryKey;
    /**
     * 是否必填，0-否，1-是
     */
    private Integer isRequired;


    /**
     * 资产id
     */
    private Long assetId;
    private List<Long> elementIds;
    private List<String> dbNames;
    private List<String> tableNames;
    private List<String> indexNames;

    private String startTime;
    private String endTime;
}
