/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-06-15
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-06-15
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.dto.request;

import com.idss.datalake.datamart.entity.DataMartSubscribe;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>数据集市订阅表 dto类</p>
 * @since 2023-06-15
 */
@Data
public class DataMartSubscribeDTO extends DataMartSubscribe {
    private List<Long> ids;

    /**
     * (CH,Mysql,Hive)库名/表名/字段名，(ES)索引名/字段名
     */
    private String assetName;
    /**
     * 数据名称
     */
    private String dataName;
    /**
     * 资产描述
     */
    private String assetDesc;

    private String assetTypeCode;
    private String elementId;
    private String assetType;
    private String assetPath;

    private String keyWords;

    /**
     * 订阅时间
     */
    private List<String> subscribeTimeList;

    /**
     * 订阅方式
     */
    private List<Integer> subscribeChannelList;
    /**
     * 审批进度
     */
    private List<Integer> approvalProgressList;
    /**
     * 订阅方式名称
     */
    private String subscriptionChannelName;
    /**
     * 配置进度
     */
    private List<Integer> configurationProgressList;
    /**
     * 审批进度名称
     */
    private String approvalProgressName;
    /**
     * 配置进度名称
     */
    private String configurationProgressName;

    /**
     * 使用时间数组
     */
    private List<String> usedTimeList;

    /**
     * 数据用途
     */
    private String dataUsageName;
    /**
     * 所属资源池
     */
    private String resourcePoolName;
    /**
     * 网络域名称
     */
    private String networkDomainName;

    /**
     * 数据库名
     */
    private String dbName;
    /**
     * 表名
     */
    private String tableName;
}