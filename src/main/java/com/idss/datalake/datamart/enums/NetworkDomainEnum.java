package com.idss.datalake.datamart.enums;

/**
 * <AUTHOR>
 * @description <p>网络域枚举类</p>
 * @date 2023年7月18日
 * @see
 */
public enum NetworkDomainEnum {

    VPN(1, "O域话务VPN");

    private int code;
    private String name;

    NetworkDomainEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return "";
        }
        for (NetworkDomainEnum value : NetworkDomainEnum.values()) {
            if (value.getCode() == code) {
                return value.getName();
            }
        }
        return "";
    }

}
