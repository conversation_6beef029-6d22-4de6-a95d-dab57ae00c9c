package com.idss.datalake.datamart.enums;

/**
 * Copyright 2020 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2020/4/23 13:35
 * 数据源类型
 */
public enum SOURCE_TYPE {

    LOCAL_FILE("LOCAL_FILE", "本地文件"),
    STREAM_FILE("STREAM_FILE", "流式文件"),
    KAFKA("KAFKA", "Kafka"),
    RABBITMQ("RABBITMQ", "RabbitMQ"),
    PULSAR("PULSAR", "Pulsar"),
    SYSLOG_UDP("SYSLOG_UDP", "Syslog_UDP"),
    SYSLOG_TCP("SYSLOG_TCP", "Syslog_TCP"),
    IDSS_RPC("IDSS_RPC", "观安RPC"),
    SYSLOG("SYSLOG", "Syslog"),
    DB2("DB2", "DB2"),
    MYSQL("MYSQL", "MySQL"),
    DAMENG("DAMENG", "达梦"),
    <PERSON><PERSON><PERSON>("OR<PERSON>LE", "Oracle"),
    S<PERSON>SERVER("SQLSERVER", "SQLServer"),
    HIVE("HIVE", "Hive"),
    JDBC("JDBC", "JDBC"),
    SCRIPT("SCRIPT", "script"),
    MONGODB("MONGODB", "MongoDB"),
    CLICKHOUSE("CLICKHOUSE", "Clickhouse"),
    ELASTICSEARCH("ELASTICSEARCH", "ElasticSearch"),
    POSTGRE_SQL("POSTGRE_SQL", "PostgreSQL"),
    FTP_SFTP("FTP_SFTP", "ftp"),
    HTTP("HTTP", "HTTP"),
    SNMP("SNMP", "Snmp"),
    WEBSERVICE("WEBSERVICE", "WebService"),
    LDAP("LDAP","LDAP");

    private String sourceType;

    private String sourceTypeName;

    SOURCE_TYPE(String sourceType, String sourceTypeName) {
        this.sourceType = sourceType;
        this.sourceTypeName = sourceTypeName;
    }

    public String getSourceType() {
        return sourceType;
    }

    public String getSourceTypeName() {
        return sourceTypeName;
    }

    public static SOURCE_TYPE getType(String sourceType){
        for(SOURCE_TYPE enums:SOURCE_TYPE.values()){
            if(enums.sourceType.equals(sourceType)){
                return enums;
            }
        }
        return null;
    }

    public static String getName(String sourceType) {
        for(SOURCE_TYPE enums:SOURCE_TYPE.values()){
            if(enums.sourceType.equals(sourceType)){
                return enums.getSourceTypeName();
            }
        }
        return "";
    }
}
