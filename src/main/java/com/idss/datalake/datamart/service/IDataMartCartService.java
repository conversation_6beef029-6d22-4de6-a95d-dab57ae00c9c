package com.idss.datalake.datamart.service;

import com.idss.datalake.datamart.entity.DataMartCart;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 资产订购表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
public interface IDataMartCartService extends IService<DataMartCart> {
    List<Map<String,Object>> listDataMartAsset(List<Long> ids);
}
