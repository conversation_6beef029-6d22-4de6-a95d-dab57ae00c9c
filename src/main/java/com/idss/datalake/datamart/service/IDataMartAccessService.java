package com.idss.datalake.datamart.service;

import java.util.List;
import java.util.Map;

/**
 * 数据集市-数据接入概览-临时
 * 
 * <AUTHOR>
 * @date 2023年11月19日
 */
public interface IDataMartAccessService {
	
	/**
	 * 数据类型统计
	 */
	public List<Map<String,Object>> dataTypeStatistic();
	
	/**
	 * etl数据统计
	 */
	public Map<String,Object> etlSourceDataStatistic();
	
	/**
	 * etl采集情况汇总
	 */
	public Map<String,Object> etlSourceOverview();
}
