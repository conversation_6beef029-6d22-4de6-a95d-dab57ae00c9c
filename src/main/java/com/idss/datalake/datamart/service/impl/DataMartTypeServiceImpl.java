package com.idss.datalake.datamart.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datamart.dto.request.DataMartTypePageRequest;
import com.idss.datalake.datamart.entity.DataMartGroup;
import com.idss.datalake.datamart.entity.DataMartType;
import com.idss.datalake.datamart.mapper.DataMartGroupMapper;
import com.idss.datalake.datamart.mapper.DataMartTypeMapper;
import com.idss.datalake.datamart.service.IDataMartTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.radar.util.UmsUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 数据集市分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@Service
public class DataMartTypeServiceImpl extends ServiceImpl<DataMartTypeMapper, DataMartType> implements IDataMartTypeService {
    @Resource
    private DataMartTypeMapper dataMartTypeMapper;
    @Resource
    private DataMartGroupMapper dataMartGroupMapper;

    @Override
    public ResultBean addOrUpdate(DataMartType dataMartType) {
        int tenantId = UmsUtils.getUVO().getTenantId();
        if (dataMartType.getId() == null) {
            List<DataMartType> dataMartTypes = dataMartTypeMapper.selectList(
                    new QueryWrapper<DataMartType>()
                            .eq("type_name", dataMartType.getTypeName())
                            .eq("tenant_id", tenantId));
            if (CollectionUtils.isNotEmpty(dataMartTypes)) {
                return ResultBean.fail("名称重复");
            }
            dataMartType.setTenantId(tenantId);
            dataMartType.setCreateUser(UmsUtils.getUVO().getUserName());
            dataMartTypeMapper.insert(dataMartType);
        } else {
            List<DataMartType> dataMartTypes = dataMartTypeMapper.selectList(
                    new QueryWrapper<DataMartType>()
                            .ne("id", dataMartType.getId())
                            .eq("type_name", dataMartType.getTypeName())
                            .eq("tenant_id", tenantId));
            if (CollectionUtils.isNotEmpty(dataMartTypes)) {
                return ResultBean.fail("名称重复");
            }
            dataMartType.setTenantId(tenantId);
            dataMartType.setCreateUser(UmsUtils.getUVO().getUserName());
            dataMartTypeMapper.updateById(dataMartType);
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean delete(Long id) {
        dataMartTypeMapper.deleteById(id);
        return ResultBean.success();
    }

    @Override
    public BasePageResponse<List<DataMartType>> page(DataMartTypePageRequest requestDto) {
        Long tenantId = Long.valueOf(UmsUtils.getUVO().getTenantId());
        requestDto.setTenantId(tenantId);
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<DataMartType> page = dataMartTypeMapper.page(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), new ArrayList<>());
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
    }

    @Override
    public ResultBean listAll() {
        List<DataMartType> dataMartTypeList = this.list(new QueryWrapper<DataMartType>().eq("tenant_id", UmsUtils.getUVO().getTenantId()));
        for (DataMartType dataMartType : dataMartTypeList) {
            dataMartType.setGroupCnt(dataMartGroupMapper.selectCount(new QueryWrapper<DataMartGroup>().eq("data_mart_type_id",dataMartType.getId())));
        }
        return ResultBean.success(dataMartTypeList);

    }

    @Override
    public ResultBean listAllByTenant() {
        List<DataMartType> dataMartTypeList = this.list();
        for (DataMartType dataMartType : dataMartTypeList) {
            dataMartType.setGroupCnt(dataMartGroupMapper.selectCount(new QueryWrapper<DataMartGroup>().eq("data_mart_type_id",dataMartType.getId())));
        }
        return ResultBean.success(dataMartTypeList);
    }
}
