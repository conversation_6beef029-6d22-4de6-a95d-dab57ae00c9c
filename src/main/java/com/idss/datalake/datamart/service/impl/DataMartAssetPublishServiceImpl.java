package com.idss.datalake.datamart.service.impl;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datamart.entity.DataMartAssetPublish;
import com.idss.datalake.datamart.mapper.DataMartAssetMapper;
import com.idss.datalake.datamart.mapper.DataMartAssetPublishMapper;
import com.idss.datalake.datamart.service.IDataMartAssetPublishService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.radar.util.UmsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.InetAddress;
import java.util.List;

/**
 * <p>
 * 数据集市资源发布 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@Service
@Slf4j
public class DataMartAssetPublishServiceImpl extends ServiceImpl<DataMartAssetPublishMapper, DataMartAssetPublish> implements IDataMartAssetPublishService {
    @Resource
    private DataMartAssetMapper dataMartAssetMapper;

    @Value("${server.servlet.context-path}")
    private String contextPath;

    @Override
    public ResultBean listPublish() {
        List<DataMartAssetPublish> publishes = null;
        try {
            publishes = this.list(new QueryWrapper<DataMartAssetPublish>().eq("tenant_id", UmsUtils.getUVO().getTenantId()).orderByDesc("create_time"));
            if (CollectionUtils.isNotEmpty(publishes)) {
                BASE64Decoder decoder = new BASE64Decoder();
                for (DataMartAssetPublish publish : publishes) {
                    publish.setTypeId(dataMartAssetMapper.selectById(publish.getAssetId()).getTypeId());
                    publish.setUrl(contextPath+"/upload/"+FileUtil.getName(new String(decoder.decodeBuffer(publish.getUrl()))));
                }
            }
            return ResultBean.success(publishes);
        } catch (IOException e) {
            log.error("decode 失败",e);
            return ResultBean.fail("异常");
        }
    }
}
