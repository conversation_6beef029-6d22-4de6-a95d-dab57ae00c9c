package com.idss.datalake.datamart.service.impl;

import com.idss.datalake.datamart.entity.DataMartCart;
import com.idss.datalake.datamart.mapper.DataMartCartMapper;
import com.idss.datalake.datamart.service.IDataMartCartService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 资产订购表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Service
public class DataMartCartServiceImpl extends ServiceImpl<DataMartCartMapper, DataMartCart> implements IDataMartCartService {

    @Override
    public List<Map<String, Object>> listDataMartAsset(List<Long> ids) {
        return this.baseMapper.listDataMartAsset(ids);
    }
}
