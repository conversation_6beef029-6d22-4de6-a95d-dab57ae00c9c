package com.idss.datalake.datamart.service;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datamart.entity.DataMartAssetPublish;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 数据集市资源发布 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
public interface IDataMartAssetPublishService extends IService<DataMartAssetPublish> {
    /**
     * 列出所有发布
     * @return
     */
    ResultBean listPublish();

}
