package com.idss.datalake.datamart.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datamart.entity.DataMartSubscribeNotice;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 数据集市订阅通知表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
public interface IDataMartSubscribeNoticeService extends IService<DataMartSubscribeNotice> {

    IPage<DataMartSubscribeNotice> getPage(Page<DataMartSubscribeNotice> page,
                                           @Param(Constants.WRAPPER) QueryWrapper<DataMartSubscribeNotice> wrapper);
}
