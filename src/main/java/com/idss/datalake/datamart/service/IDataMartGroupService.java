package com.idss.datalake.datamart.service;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datamart.dto.request.AddDataMartAssetRequest;
import com.idss.datalake.datamart.dto.request.DataMartGroupPageRequest;
import com.idss.datalake.datamart.entity.DataMartGroup;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 数据集市分组 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
public interface IDataMartGroupService extends IService<DataMartGroup> {
    ResultBean addOrUpdate(DataMartGroup dataMartGroup);

    ResultBean delete(Long id);

    BasePageResponse<List<DataMartGroup>> page(DataMartGroupPageRequest request);

    ResultBean listByTypeId(Long typeId, AddDataMartAssetRequest request);
    ResultBean listByTypeId(Long typeId);
}
