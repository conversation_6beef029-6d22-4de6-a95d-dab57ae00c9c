package com.idss.datalake.datamart.service;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datamart.dto.request.*;
import com.idss.datalake.datamart.dto.response.AssetDetailPageVo;
import com.idss.datalake.datamart.dto.response.AssetMapGroupVo;
import com.idss.datalake.datamart.dto.response.AssetMapView;
import com.idss.datalake.datamart.dto.response.QueryElementVo;
import com.idss.datalake.datamart.entity.DataMartAsset;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datamart.entity.DataMartAssetPublish;
import com.idss.datalake.datamart.entity.DataMartDiscuss;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据集市资产 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
public interface IDataMartAssetService extends IService<DataMartAsset> {
    BasePageResponse<List<AssetMapView>> assetMapPage(AssetMapGroupRequest requestDto);

    List<AssetMapGroupVo> assetMapTypeList(AssetMapGroupRequest request);

    List<String> queryElementField(QueryElementFieldRequest request);

    /**
     * 分页查询元数据
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<QueryElementVo>> queryElementPage(QueryElementRequest requestDto);

    /**
     * 新增资源
     *
     * @param request
     * @return
     */
    ResultBean addOrUpdate(AddDataMartAssetRequest request);

    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    BasePageResponse<List<DataMartAsset>> page(DataMartAssetPageRequest request);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    ResultBean delete(Long id);

    /**
     * 详情明细
     *
     * @param id
     * @return
     */
    ResultBean detail(Long id);

    /**
     * 发布
     *
     * @param dataMartAssetPublish
     * @return
     */
    ResultBean release(DataMartAssetPublish dataMartAssetPublish);

    /**
     * 移除发布
     *
     * @param id
     * @return
     */
    ResultBean unRelease(Long id);

    /**
     * 订阅
     *
     * @param id
     * @param addOrCancel 0 取消，1订阅
     * @return
     */
    ResultBean subscribe(Long id, Long addOrCancel);

    /**
     * 点赞
     *
     * @param id
     * @param addOrCancel 0 取消，1 点赞
     * @return
     */
    ResultBean thumbsUp(Long id, Long addOrCancel);

    /**
     * 资产详情
     *
     * @param id
     * @return
     */
    ResultBean assetDetail(Long id);

    Long queryTotal(DataMartAsset dataMartAsset);

    /**
     * 资产详情 分页查询
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<AssetDetailPageVo>> queryAssetDetailPage(AssetDetailPageRequest requestDto);


    /**
     * 预览数据
     *
     * @return
     */
    ResultBean previewData(Long assetId) throws Exception;

    /**
     * 数据条数趋势
     * @param id
     * @return
     */
    ResultBean tableLineTrend(Long id);

    /**
     * 资产评分
     * @param id
     * @return
     */
    ResultBean assetScore(Long id);

    /**
     * 我的评分
     * @param score {"assetId":1,"availableScore":1,"visibleScore":1,"credibleScore":1}
     * @return
     */
    ResultBean myScore(Map<String,Integer> score);

    /**
     * 发表评论
     * @param discuss {"assetId":1,"discuss":"aaa"}
     * @return
     */
    ResultBean discuss(Map<String,Object> discuss);

    /**
     * 评论分页
     * @param request
     * @return
     */
    BasePageResponse<List<DataMartDiscuss>> discussPage(DiscussPageRequest request);

    ResultBean relationChart(Long assetId);

    /**
     * 集市概览
     *
     * @return
     */
    ResultBean overview();
    ResultBean overview(int tenantId);
}
