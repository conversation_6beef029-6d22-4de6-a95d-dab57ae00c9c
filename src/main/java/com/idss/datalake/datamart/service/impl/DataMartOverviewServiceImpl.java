package com.idss.datalake.datamart.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.util.DateFormatUtils;
import com.idss.datalake.common.util.TimeRange;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorJob;
import com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRule;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorJobService;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorRuleService;
import com.idss.datalake.datagovern.dataquality.service.IQuaMonitorTaskService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailIndex;
import com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHiveElementDetailTable;
import com.idss.datalake.datagovern.metadata.model.detail.service.ChElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.detail.service.EsElementDetailIndexService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHiveElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.element.enums.ElementTypeEnum;
import com.idss.datalake.datamart.dto.request.DataMartOverviewRequest;
import com.idss.datalake.datamart.dto.response.DataMartOverviewResponse;
import com.idss.datalake.datamart.dto.response.EtlSummary;
import com.idss.datalake.datamart.dto.response.MartApprovalStatistics;
import com.idss.datalake.datamart.dto.response.MartPublishRankingTop10;
import com.idss.datalake.datamart.dto.response.MartSubscribe;
import com.idss.datalake.datamart.dto.response.MartSummaryStatistics;
import com.idss.datalake.datamart.dto.response.MartTypeOverview;
import com.idss.datalake.datamart.dto.response.MartTypePercent;
import com.idss.datalake.datamart.dto.response.QualityJobInfo;
import com.idss.datalake.datamart.dto.response.QualityOverview;
import com.idss.datalake.datamart.entity.DataMartAsset;
import com.idss.datalake.datamart.entity.DataMartGroup;
import com.idss.datalake.datamart.entity.DataMartSubscribe;
import com.idss.datalake.datamart.entity.DataMartType;
import com.idss.datalake.datamart.enums.ApprovalProgressEnum;
import com.idss.datalake.datamart.enums.ConfigurationProgressEnum;
import com.idss.datalake.datamart.mapper.DataMartOverviewMapper;
import com.idss.datalake.datamart.service.IDataMartAssetService;
import com.idss.datalake.datamart.service.IDataMartGroupService;
import com.idss.datalake.datamart.service.IDataMartOverviewService;
import com.idss.datalake.datamart.service.IDataMartSubscribeService;
import com.idss.datalake.datamart.service.IDataMartTypeService;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.datashare.tenant.service.ITbTenantService;
import com.idss.datalake.portal.dto.BarYData;
import com.idss.datalake.portal.dto.LineData;
import com.idss.datalake.portal.dto.PieData;
import com.idss.radar.datasource.DatasourceType;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 集市概览相关接口实现
 */
@Service
@Slf4j
public class DataMartOverviewServiceImpl implements IDataMartOverviewService {

    @Autowired
    private IDataMartAssetService dataMartAssetService;

    @Autowired
    private IDataMartTypeService dataMartTypeService;

    @Autowired
    private IDataMartGroupService dataMartGroupService;

    @Autowired
    private IDataMartSubscribeService dataMartSubscribeService;

    @Autowired
    private ITbTenantService tenantService;

    @Autowired
    private ChElementDetailTableService chElementDetailTableService;

    @Autowired
    private EsElementDetailIndexService esElementDetailIndexService;

    @Autowired
    private IQuaWebHiveElementDetailTableService hiveElementDetailTableService;

    @Autowired
    private IQuaMonitorJobService monitorJobService;

    @Autowired
    private IQuaMonitorTaskService taskService;

    @Autowired
    private IQuaMonitorRuleService ruleService;

    @Autowired
    private DataMartOverviewMapper dataMartOverviewMapper;

    @Override
    public Map<String, Integer> martCountOverview() throws Exception {
        Map<String, Integer> result = new HashMap<>();
        int groupCount = dataMartGroupService.count();
        int typeCount = dataMartTypeService.count();
        int assetCount = dataMartAssetService.count();
        result.put("groupCount",groupCount);
        result.put("typeCount",typeCount);
        result.put("assetCount",assetCount);
        return result;
    }

    @Override
    public List<MartTypeOverview> martTypeOverview() throws Exception {
        List<MartTypeOverview> overviews = new ArrayList<>();
        List<Map<String, Object>> results = dataMartGroupService.listMaps(new QueryWrapper<DataMartGroup>()
                .select("data_mart_type_id \"typeId\",count(1) cnt")
                .groupBy("data_mart_type_id"));
        if (CollectionUtils.isNotEmpty(results)) {
            for (Map<String, Object> result : results) {
                MartTypeOverview overview = new MartTypeOverview();
                DataMartType dataMartType = dataMartTypeService.getById(Long.valueOf(String.valueOf(result.get("typeId"))));
                if (dataMartType != null) {
                    List<DataMartGroup> dataMartGroups = dataMartGroupService.list(new QueryWrapper<DataMartGroup>()
                            .eq("data_mart_type_id", result.get("typeId")));
                    overview.setMartType(dataMartType.getTypeName());
                    overview.setTypeCount(Integer.valueOf(String.valueOf(result.get("cnt"))));
                    List<String> martDatas = new ArrayList<>();
                    for (DataMartGroup dataMartGroup : dataMartGroups) {
                        martDatas.add(dataMartGroup.getGroupName());
                    }
                    overview.setMartDatas(martDatas);

                    overviews.add(overview);
                }
            }
        }
        return overviews;
    }

    @Override
    public MartTypePercent martProportion() {
        MartTypePercent percent = getMartTypePercent();
        return percent;
    }

    private MartTypePercent getMartTypePercent() {
        MartTypePercent percent = new MartTypePercent();
        List<PieData> pieDatas = new ArrayList<>();
        percent.setPie(pieDatas);
        // 数据集市类型占比
        List<Map<String, Object>> results = dataMartGroupService.listMaps(new QueryWrapper<DataMartGroup>()
                .select("data_mart_type_id \"typeId\",count(1) cnt")
                .groupBy("data_mart_type_id"));
        if (CollectionUtils.isNotEmpty(results)) {
            for (Map<String, Object> result : results) {
                DataMartType dataMartType = dataMartTypeService.getById(Long.valueOf(String.valueOf(result.get("typeId"))));
                if (dataMartType != null) {
                    PieData pie = new PieData();
                    pie.setName(dataMartType.getTypeName());
                    pie.setValue(Integer.valueOf(String.valueOf(result.get("cnt"))));

                    pieDatas.add(pie);
                }

            }
        }

        // 获取订阅情况
        int totalCnt = dataMartAssetService.count();
        int subscribeCnt = dataMartSubscribeService.count(new QueryWrapper<DataMartSubscribe>().select("distinct asset_id"));
        percent.setTotalCnt(totalCnt);
        percent.setSubscribeCnt(subscribeCnt);
        return percent;
    }

    @Override
    public MartTypePercent martProportion(Integer tenantId) {
        MartTypePercent percent = getMartTypePercent();
        return percent;
    }

    @Override
    public BarYData subscribe() throws Exception {
        return getBarYData();
    }

    private BarYData getBarYData() {
        BarYData barYData = new BarYData();
        List<Integer> xData = new ArrayList<>();
        List<String> yData = new ArrayList<>();
        barYData.setXData(xData);
        barYData.setYData(yData);
        List<Map<String, Object>> results = dataMartSubscribeService.listMaps(new QueryWrapper<DataMartSubscribe>()
                .select("asset_id \"assetId\",count(1) cnt")
                .isNotNull("asset_id")
                .groupBy("asset_id")
                .orderByDesc("cnt")
                .last("limit 10"));
        if (CollectionUtils.isNotEmpty(results)) {
            for (Map<String, Object> result : results) {
                DataMartAsset martAsset = dataMartAssetService.getById(Long.valueOf(String.valueOf(result.get("assetId"))));
                if (martAsset != null) {
                    yData.add(martAsset.getDataName());
                    xData.add(Integer.valueOf(String.valueOf(result.get("cnt"))));
                }
            }
        }
        return barYData;
    }

    @Override
    public BarYData subscribe(Integer tenantId) throws Exception {
        return getBarYData();
    }

    @Override
    public MartSubscribe entry() throws Exception {
        return getMartSubscribe();
    }

    private MartSubscribe getMartSubscribe() {
        MartSubscribe martSubscribe = new MartSubscribe();
        int entryCnt = tenantService.count(new QueryWrapper<TbTenant>().eq("DEL_FLAG", "0"));
        martSubscribe.setEntryCnt(entryCnt);

        int subscribeCnt = dataMartSubscribeService.count();
        martSubscribe.setSubscribeCnt(subscribeCnt);

        List<DataMartSubscribe> subscribes = dataMartSubscribeService.list(new QueryWrapper<DataMartSubscribe>().orderByAsc(
                "subscription_time").last("limit 1"));
        if (CollectionUtils.isNotEmpty(subscribes)) {
            LocalDateTime localDateTime = subscribes.get(0).getSubscriptionTime();
            Date date = Date.from(localDateTime.atZone(ZoneOffset.ofHours(8)).toInstant());
            String days = DateFormatUtils.getDaysBetween(date, new Date());
            if (!"0".equals(days)) {
                martSubscribe.setFrequency(String.valueOf(subscribeCnt / Integer.valueOf(days)));
            } else {
                martSubscribe.setFrequency("-");
            }
        }

        // 订阅用信息
        List<MartSubscribe.SubscribeUser> subscribeUsers = new ArrayList<>();
        List<Map<String, Object>> results = dataMartSubscribeService.listMaps(new QueryWrapper<DataMartSubscribe>()
                .select("subscription_user \"subscribeUser\",count(1) cnt")
                .groupBy("subscription_user")
                .orderByDesc("cnt")
                .last("limit 5"));
        if (CollectionUtils.isNotEmpty(results)) {
            for (Map<String, Object> result : results) {
                MartSubscribe.SubscribeUser subscribeUser = martSubscribe.new SubscribeUser();
                subscribeUser.setUser(String.valueOf(result.get("subscribeUser")));
                subscribeUser.setCount(Integer.valueOf(String.valueOf(result.get("cnt"))));

                subscribeUsers.add(subscribeUser);
            }
        }
        martSubscribe.setSubscribe(subscribeUsers);
        return martSubscribe;
    }

    @Override
    public MartSubscribe entry(Integer tenantId) throws Exception {
        return getMartSubscribe();
    }

    @Override
    public List<QualityOverview> qualityOverview() throws Exception {
        List<QualityOverview> result = new ArrayList<>();

        List<Map<String, Object>> qualityJobs = dataMartOverviewMapper.qualityOverview();
        Map<String, Integer> jobTableMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(qualityJobs)) {
            for (Map<String, Object> qualityJob : qualityJobs) {
                jobTableMap.put(String.valueOf(qualityJob.get("elementType")), Integer.valueOf(String.valueOf(qualityJob.get("cnt"))));
            }
        }

        // CH信息总览
        QualityOverview chOverview = new QualityOverview();
        chOverview.setName("离线结构化数据");
        chOverview.setMetaTableCnt(chElementDetailTableService.count(new QueryWrapper<ChElementDetailTable>().select("distinct " +
                "table_name")));
        chOverview.setQualityTableCnt(jobTableMap.get(ElementTypeEnum.CH.getCode()) == null ? 0 :
                jobTableMap.get(ElementTypeEnum.CH.getCode()));
        result.add(chOverview);

        // ES信息总览
        QualityOverview esOverview = new QualityOverview();
        esOverview.setName("日志类数据");
        esOverview.setMetaTableCnt(esElementDetailIndexService.count(new QueryWrapper<EsElementDetailIndex>().select("distinct " +
                "index_name")));
        esOverview.setQualityTableCnt(jobTableMap.get(ElementTypeEnum.ES.getCode()) == null ? 0 :
                jobTableMap.get(ElementTypeEnum.ES.getCode()));
        result.add(esOverview);

        // HIVE信息概览
        QualityOverview hiveOverview = new QualityOverview();
        hiveOverview.setName("离线非结构化数据");
        hiveOverview.setMetaTableCnt(hiveElementDetailTableService.count(new QueryWrapper<QuaWebHiveElementDetailTable>().select(
                "distinct table_name")));
        hiveOverview.setQualityTableCnt(jobTableMap.get(ElementTypeEnum.HIVE.getCode()) == null ? 0 :
                jobTableMap.get(ElementTypeEnum.HIVE.getCode()));
        result.add(hiveOverview);

        return result;
    }

    @Override
    public QualityJobInfo qualityJob() throws Exception {
        QualityJobInfo qualityJobInfo = new QualityJobInfo();
        List<Map<String, Object>> jobInfos = monitorJobService.listMaps(new QueryWrapper<QuaMonitorJob>()
                .select("execute_cycle \"executeCycle\",count(1) cnt")
                .eq("flag", "1")
                .groupBy("execute_cycle"));
        // 一次性任务数
        int onceTask = 0;
        // 周期任务数
        int cycleTask = 0;
        if (CollectionUtils.isNotEmpty(jobInfos)) {
            for (Map<String, Object> jobInfo : jobInfos) {
                String executeCycle = String.valueOf(jobInfo.get("executeCycle"));
                if ("01".equals(executeCycle)) {
                    onceTask = Integer.valueOf(String.valueOf(jobInfo.get("cnt")));
                } else {
                    cycleTask = Integer.valueOf(String.valueOf(jobInfo.get("cnt")));
                }
            }
        }
        qualityJobInfo.setOnceTask(onceTask);
        qualityJobInfo.setCycleTask(cycleTask);
        qualityJobInfo.setJobCnt(onceTask + cycleTask);

        int taskCnt = taskService.count();
        qualityJobInfo.setTaskCnt(taskCnt);

        int modelCnt = ruleService.count(new QueryWrapper<QuaMonitorRule>().eq("flag", "1"));
        qualityJobInfo.setModelCnt(modelCnt);

        return qualityJobInfo;
    }

    @Override
    public LineData jobTrend() throws Exception {
        LineData lineData = new LineData();
        List<String> xData = new ArrayList<>();
        List<Integer> yData = new ArrayList<>();
        lineData.setXData(xData);
        lineData.setYData(yData);

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar startDate = Calendar.getInstance();
        startDate.add(Calendar.DATE, -30);
        Date endDate = new Date();
        String startDateStr = format.format(startDate.getTime());
        String endDateStr = format.format(endDate);
        List<String> times = TimeRange.getDateRange(startDateStr, endDateStr, Calendar.DATE, "yyyy-MM-dd");
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", startDateStr);
        params.put("endDate", endDateStr);
        List<Map<String, Object>> results = dataMartOverviewMapper.jobTrend(params);
        if (CollectionUtils.isNotEmpty(results)) {
            Map<String, Object> jobMap = new HashMap<>();
            for (Map<String, Object> result : results) {
                jobMap.put(String.valueOf(result.get("createTime")), result.get("cnt"));
            }
            int jobCnt = 0;
            for (String time : times) {
                xData.add(time);
                if (jobMap.containsKey(time)) {
                    jobCnt += Integer.valueOf(String.valueOf(jobMap.get(time)));
                }
                yData.add(jobCnt);
            }
        } else {
            for (String time : times) {
                xData.add(time);
                yData.add(0);
            }
        }

        return lineData;
    }

    @Override
    public EtlSummary etlSummary() throws Exception {
        EtlSummary etlSummary = new EtlSummary();
        UserValueObject uvo = UmsUtils.getUVO();
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, uvo.getTenantId());
        List<Map<String, Object>> summaries = dataMartOverviewMapper.etlSummary();

        int onceTask = 0;
        int cycleTask = 0;
        if (CollectionUtils.isNotEmpty(summaries)) {
            for (Map<String, Object> summary : summaries) {
                if ("once".equals(String.valueOf(summary.get("configValue")))) {
                    onceTask = Integer.valueOf(String.valueOf(summary.get("cnt")));
                } else {
                    cycleTask = Integer.valueOf(String.valueOf(summary.get("cnt")));
                }
            }
        }
        etlSummary.setEtlCnt(onceTask + cycleTask);
        etlSummary.setOnceTask(onceTask);
        etlSummary.setCycleTask(cycleTask);

        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE, uvo.getTenantId());
        Map<String, Object> daySumMap = dataMartOverviewMapper.statSum(null);
        Map<String, Object> dayLengthMap = dataMartOverviewMapper.statWriteLength();
        BigInteger currentSum = (BigInteger) daySumMap.get("currentSum");
        BigInteger currentLength = (BigInteger) dayLengthMap.get("currentLength");
        etlSummary.setDataCnt(Integer.parseInt(new DecimalFormat("0").format(currentSum.longValue() / 100000)));
        etlSummary.setUsedCapacity(Integer.parseInt(new DecimalFormat("0").format(currentLength.longValue() / 1024 / 1024 / 1024 / 1024)));
        etlSummary.setTotalCapacity(3027);
        return etlSummary;
    }

    @Override
    public List<PieData> diskPercent() throws Exception {
        List<PieData> pieDatas = new ArrayList<>();

        UserValueObject uvo = UmsUtils.getUVO();
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE, uvo.getTenantId());
        List<Map<String, Object>> disks = dataMartOverviewMapper.diskLength();
        if (CollectionUtils.isNotEmpty(disks)) {
            for (Map<String, Object> disk : disks) {
                PieData pieData = new PieData();
                String name = String.valueOf(disk.get("writerType"));
                BigInteger length = (BigInteger) disk.get("length");
                if (ElementTypeEnum.CH.getCode().equals(name)) {
                    pieData.setName("离线结构化数据");
                    pieData.setValue(Integer.parseInt(new DecimalFormat("0").format(length.longValue() / 1024 / 1024 / 1024)));
                } else {
                    pieData.setName("日志类数据");
                    pieData.setValue(Integer.parseInt(new DecimalFormat("0").format(length.longValue() / 1024 / 1024 / 1024)));
                }

                pieDatas.add(pieData);
            }
        }
        return pieDatas;
    }

    @Override
    public LineData writeTrend() throws Exception {
        LineData lineData = new LineData();
        List<String> xData = new ArrayList<>();
        List<Integer> yData = new ArrayList<>();
        lineData.setXData(xData);
        lineData.setYData(yData);

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar startDate = Calendar.getInstance();
        startDate.add(Calendar.DATE, -30);
        Date endDate = new Date();
        String startDateStr = format.format(startDate.getTime());
        String endDateStr = format.format(endDate);
        List<String> times = TimeRange.getDateRange(startDateStr, endDateStr, Calendar.DATE, "yyyy-MM-dd");
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", startDateStr + " 00:00:00");
        params.put("endTime", endDateStr + " 23:59:59");

        UserValueObject uvo = UmsUtils.getUVO();
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE, uvo.getTenantId());
        List<Map<String, Object>> results = dataMartOverviewMapper.writeTrend(params);
        if (CollectionUtils.isNotEmpty(results)) {
            Map<String, Object> resultMap = new HashMap<>();
            for (Map<String, Object> result : results) {
                resultMap.put(String.valueOf(result.get("endTime")), result.get("outCount"));
            }
            Integer writeCount = 0;
            for (String time : times) {
                if (resultMap.containsKey(time)) {
                    BigInteger currentSum = (BigInteger) resultMap.get(time);
                    writeCount += Integer.parseInt(new DecimalFormat("0").format(currentSum.longValue() / 10000));
                }
                yData.add(writeCount);
            }
        } else {
            for (String time : times) {
                xData.add(time);
                yData.add(0);
            }
        }
        return lineData;
    }

    @Override
    public BasePageResponse<List<DataMartOverviewResponse>> etlDetail(DataMartOverviewRequest request) throws Exception {
        List<Map<String, Object>> results = dataMartOverviewMapper.etlAppendix();
        Map<String, Object> appendixMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(results)) {
            for (Map<String, Object> result : results) {
                appendixMap.put(String.valueOf(result.get("flowId")), result);
            }
        }

        UserValueObject uvo = UmsUtils.getUVO();
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, uvo.getTenantId());
        int beginIndex = (request.getPageNum() - 1) * request.getPageSize();
        request.setBeginIndex(beginIndex);
        //PageInfo<DataMartOverviewResponse> page = new PageInfo<DataMartOverviewResponse>(dataMartOverviewMapper.page(request));
        Map<String, Object> params = new HashMap<>();
        params.put("beginIndex", beginIndex);
        params.put("pageSize", request.getPageSize());
        if (StringUtils.isNotEmpty(request.getName())) {
            params.put("name", request.getName());
        }
        List<DataMartOverviewResponse> list = dataMartOverviewMapper.queryDatasource(params);
        int totalCnt = dataMartOverviewMapper.queryTotalsource(params);
        int totalPage = (totalCnt + request.getPageSize() - 1) / request.getPageSize();
        if (CollectionUtils.isNotEmpty(list)) {
            for (DataMartOverviewResponse dataMartOverviewResponse : list) {
                String storageType = dataMartOverviewResponse.getStorageType();
                String storage = "";
                if (storageType.contains("数仓")) {
                    storage = "ClickHouse";
                }
                if (StringUtils.isNotEmpty(storage) && storageType.contains("索引")) {
                    storage = storage + "/ElasticSearch";
                }
                if (StringUtils.isEmpty(storage) && storageType.contains("索引")) {
                    storage = "ElasticSearch";
                }
                dataMartOverviewResponse.setStorageType(storage);
                if (appendixMap.containsKey(String.valueOf(dataMartOverviewResponse.getFlowId()))) {
                    Map<String, Object> result =
                            (Map<String, Object>) appendixMap.get(String.valueOf(dataMartOverviewResponse.getFlowId()));
                    dataMartOverviewResponse.setPlatform(String.valueOf(result.get("platform")));
                    dataMartOverviewResponse.setDataType(String.valueOf(result.get("dataType")));
                    dataMartOverviewResponse.setStartTime(String.valueOf(result.get("createTime")));
                    dataMartOverviewResponse.setEndTime(String.valueOf(result.get("endTime")));
                }

                // 获取数据量
                DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE, uvo.getTenantId());
                Map<String, Object> param = new HashMap<>();
                param.put("flowId", String.valueOf(dataMartOverviewResponse.getFlowId()));
                Map<String, Object> daySumMap = dataMartOverviewMapper.statSum(param);
                BigInteger currentSum = (BigInteger) daySumMap.get("currentSum");
                dataMartOverviewResponse.setDataSize(Integer.parseInt(new DecimalFormat("0").format(currentSum.longValue() / 100000)));
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            return new BasePageResponse<>(totalPage, request.getPageNum(), request.getPageSize(), totalCnt, new ArrayList<>());
        } else {
            return new BasePageResponse<>(totalPage, request.getPageNum(), request.getPageSize(), totalCnt, list);
        }
    }

    @Override
    public MartSummaryStatistics martSummaryStatistics() {
        MartSummaryStatistics summaryStatistics = doSummaryStatistics();
        return summaryStatistics;
    }

    private MartSummaryStatistics doSummaryStatistics() {
        // 集市汇总统计
        MartSummaryStatistics summaryStatistics = new MartSummaryStatistics();
        int martUserTotal = tenantService.count(new QueryWrapper<TbTenant>().eq("DEL_FLAG", "0")
                .in("ACCOUNT_TYPE", new String[]{"1", "2"}));
        Integer subscriptionTotal = dataMartOverviewMapper.getSubscriptionTotal();
        Integer publishTotal = dataMartOverviewMapper.getPublishTotal();
        summaryStatistics.setMartUserTotal(martUserTotal);
        summaryStatistics.setMartSubscriptionTotal(subscriptionTotal);
        summaryStatistics.setMartPublishTotal(publishTotal);
        return summaryStatistics;
    }

    @Override
    public MartSummaryStatistics martSummaryStatistics(int tenantId) {
        return doSummaryStatistics();
    }

    @Override
    public List<MartPublishRankingTop10> martPublishRankingTop() {
        return getMartPublishRankingTop();
    }

    private List<MartPublishRankingTop10> getMartPublishRankingTop() {
        List<MartPublishRankingTop10> publishRankingTop = dataMartOverviewMapper.getPublishRankingTop();
        return publishRankingTop;
    }

    @Override
    public List<MartPublishRankingTop10> martPublishRankingTop(Integer tenantId) {
        return getMartPublishRankingTop();
    }

    @Override
    public List<MartApprovalStatistics> martApprovalStatistics() {
        List<MartApprovalStatistics> approvalStatisticsList = getApprovalStatisticsList();
        return approvalStatisticsList;
    }

    private List<MartApprovalStatistics> getApprovalStatisticsList() {
        List<MartApprovalStatistics> approvalStatisticsList = new ArrayList<>();
        List<DataMartSubscribe> subscribeList = dataMartSubscribeService.list();
        if (CollectionUtils.isEmpty(subscribeList)) {
            approvalStatisticsList.add(new MartApprovalStatistics("审批中", 0));
            approvalStatisticsList.add(new MartApprovalStatistics("审批同意配置中", 0));
            approvalStatisticsList.add(new MartApprovalStatistics("审批同意配置成功", 0));
            approvalStatisticsList.add(new MartApprovalStatistics("审批拒绝", 0));
        } else {
            long approvalInProgressCount =
                    subscribeList.stream().filter(x -> {
                        Integer progress = x.getApprovalProgress();
                        return progress != null && progress == ApprovalProgressEnum.IN_PROGRESS.getCode();
                    }).count();
            long configurationInProgressCount =
                    subscribeList.stream().filter(x -> {
                        Integer progress = x.getConfigurationProgress();
                        return progress != null && progress == ConfigurationProgressEnum.IN_PROGRESS.getCode();
                    }).count();
            long configurationSucceedCount =
                    subscribeList.stream().filter(x -> {
                        Integer progress = x.getConfigurationProgress();
                        return progress != null && progress == ConfigurationProgressEnum.SUCCEED.getCode();
                    }).count();
            long approvalRefusedCount =
                    subscribeList.stream().filter(x -> {
                        Integer progress = x.getApprovalProgress();
                        return progress != null && progress == ApprovalProgressEnum.REFUSED.getCode();
                    }).count();
            approvalStatisticsList.add(new MartApprovalStatistics("审批中", approvalInProgressCount));
            approvalStatisticsList.add(new MartApprovalStatistics("审批同意配置中", configurationInProgressCount));
            approvalStatisticsList.add(new MartApprovalStatistics("审批同意配置成功", configurationSucceedCount));
            approvalStatisticsList.add(new MartApprovalStatistics("审批拒绝", approvalRefusedCount));
        }
        return approvalStatisticsList;
    }

    @Override
    public List<MartApprovalStatistics> martApprovalStatistics(Integer tenantId) {
        List<MartApprovalStatistics> approvalStatisticsList = getApprovalStatisticsList();
        return approvalStatisticsList;
    }

}
