/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-06-15
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-06-15
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.manager;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.common.util.StringUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datamart.dto.request.DataMartSubscribeDTO;
import com.idss.datalake.datamart.dto.response.SubscribeStatistics;
import com.idss.datalake.datamart.entity.*;
import com.idss.datalake.datamart.enums.ApprovalProgressEnum;
import com.idss.datalake.datamart.enums.ConfigurationProgressEnum;
import com.idss.datalake.datamart.enums.DataUsageEnum;
import com.idss.datalake.datamart.enums.NetworkDomainEnum;
import com.idss.datalake.datamart.enums.SubscriptionChannelEnum;
import com.idss.datalake.datamart.service.*;
import com.idss.datalake.datashare.dataapi.entity.ApiInfo;
import com.idss.datalake.datashare.dataapi.service.IApiInfoService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>数据集市订阅表 manager处理类</p>
 * @since 2023-06-15
 */
@Component
public class DataMartSubscribeManager {
    private static final Logger logger = LoggerFactory.getLogger(DataMartSubscribeManager.class);

    @Resource
    private IDataMartSubscribeService iDataMartSubscribeService;
    @Resource
    private IDataMartAssetService dataMartAssetService;
    @Resource
    private IApiInfoService apiInfoService;
    @Resource
    private IDataMartSubscribeNoticeService noticeService;
    @Resource
    private IDataMartResourcePoolService resourcePoolService;
    @Autowired
    private IDataMartCartService dataMartCartService;

    public Map<String, Object> createPageCondition(RequestDTO requestDTO) throws ParamInvalidException {
        if (requestDTO != null && requestDTO.getGlobal() != null) {
            GlobalDTO globalDTO = requestDTO.getGlobal();
            if (globalDTO.getPageNum() == null) {
                globalDTO.setPageNum(Constant.DEFAULT_PAGE_NUM);
            }
            if (globalDTO.getPageSize() == null) {
                globalDTO.setPageSize(Constant.DEFAULT_PAGE_SIZE);
            }
            if (StringUtils.isEmpty(globalDTO.getOrderType())) {
                globalDTO.setOrderType("desc");
            }
            if (StringUtils.isEmpty(globalDTO.getOrderField())) {
                globalDTO.setOrderField("subscriptionTime");
            }
        } else {
            throw new ParamInvalidException("入参错误");
        }
        return null;
    }

    /**
     * 我的订阅
     *
     * @param requestDTO
     * @return
     */
    public Map<String, Object> mySubscribePage(RequestDTO requestDTO) {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<DataMartSubscribe> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataMartSubscribe> queryWrapper = new QueryWrapper<>();
        // 数据申请人
        queryWrapper.eq("t0.tenant_id", UserUtil.getCurrentTenantId());
        queryWrapper.eq("t0.user_id", UserUtil.getCurrentUserId());
        if (requestDTO.getParam() != null) {
            DataMartSubscribeDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), DataMartSubscribeDTO.class);
            if (StringUtils.isNotBlank(dto.getAssetName())) {
                queryWrapper.like("t0.asset_name", dto.getAssetName());
            }
            if (StringUtils.isNotBlank(dto.getKeyWords())) {
                queryWrapper.like("t0.key_words", dto.getKeyWords());
            }
            if (StringUtils.isNotBlank(dto.getDataName())) {
                queryWrapper.like("t0.data_name", dto.getDataName());
            }
            if (CollectionUtils.isNotEmpty(dto.getSubscribeChannelList())) {
                queryWrapper.in("t0.subscription_channel", dto.getSubscribeChannelList());
            }
            if (CollectionUtils.isNotEmpty(dto.getSubscribeTimeList())) {
                List<String> timeList = dto.getSubscribeTimeList();
                queryWrapper.between("t0.subscription_time", timeList.get(0), timeList.get(1));
            }
            if (CollectionUtils.isNotEmpty(dto.getApprovalProgressList())) {
                queryWrapper.in("t0.approval_progress", dto.getApprovalProgressList());
            }
        }
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }

        IPage<DataMartSubscribeDTO> pageResult = iDataMartSubscribeService.getPage(page, queryWrapper);
        List<DataMartSubscribeDTO> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            records.stream().forEach(record -> {
                record.setSubscriptionChannelName(SubscriptionChannelEnum.getNameByCode(record.getSubscriptionChannel()));
                record.setApprovalProgressName(ApprovalProgressEnum.getNameByCode(record.getApprovalProgress()));
                record.setConfigurationProgressName(ConfigurationProgressEnum.getNameByCode(record.getConfigurationProgress()));
                record.setNetworkDomainName(NetworkDomainEnum.getNameByCode(record.getNetworkDomain()));
                record.setDataUsageName(DataUsageEnum.getNameByCode(record.getDataUsage()));
            });
        }

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);
        return result;
    }

    public Map<String, Object> tableSubscribePage(RequestDTO requestDTO) {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<DataMartSubscribe> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataMartSubscribe> queryWrapper = new QueryWrapper<>();
        if (requestDTO.getParam() != null) {
            DataMartSubscribeDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), DataMartSubscribeDTO.class);
            if (dto.getElementId() != null) {
                queryWrapper.eq("t0.element_id", dto.getElementId());
            }
            if (StringUtils.isNotBlank(dto.getDbName())) {
                queryWrapper.eq("t0.db_name", dto.getDbName()); // 使用eq精准查询
            }
            if (StringUtils.isNotBlank(dto.getTableName())) {
                queryWrapper.and(wrapper -> wrapper.eq("t0.table_name", dto.getTableName())
                        .or().eq("t0.index_name", dto.getTableName())); // 使用eq精准查询
            }
        }
        queryWrapper.in("t0.asset_type_code", "TABLE", "INDEX");
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }

        IPage<DataMartSubscribeDTO> pageResult = iDataMartSubscribeService.getPageByTableName(page, queryWrapper);
        List<DataMartSubscribeDTO> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            records.stream().forEach(record -> {
                record.setSubscriptionChannelName(SubscriptionChannelEnum.getNameByCode(record.getSubscriptionChannel()));
                record.setApprovalProgressName(ApprovalProgressEnum.getNameByCode(record.getApprovalProgress()));
                record.setConfigurationProgressName(ConfigurationProgressEnum.getNameByCode(record.getConfigurationProgress()));
                record.setNetworkDomainName(NetworkDomainEnum.getNameByCode(record.getNetworkDomain()));
                record.setDataUsageName(DataUsageEnum.getNameByCode(record.getDataUsage()));
            });
        }

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);
        return result;
    }

    public SubscribeStatistics mySubscribeStatistics() {
        SubscribeStatistics statistics = new SubscribeStatistics();
        QueryWrapper<DataMartSubscribe> queryWrapper = new QueryWrapper<>();
        // 数据申请人
        queryWrapper.eq("tenant_id", UserUtil.getCurrentTenantId());
        queryWrapper.eq("user_id", UserUtil.getCurrentUserId());
        List<DataMartSubscribe> list = iDataMartSubscribeService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return statistics;
        }
        long approvalInProgressCount =
                list.stream().filter(x -> {
                    Integer progress = x.getApprovalProgress();
                    return progress != null && progress == ApprovalProgressEnum.IN_PROGRESS.getCode();
                }).count();
        long approvalRefusedCount =
                list.stream().filter(x -> {
                    Integer progress = x.getApprovalProgress();
                    return progress != null && progress == ApprovalProgressEnum.REFUSED.getCode();
                }).count();
        long configurationInProgressCount =
                list.stream()
                        .filter(x -> {
                            Integer progress = x.getConfigurationProgress();
                            return progress != null && progress == ConfigurationProgressEnum.IN_PROGRESS.getCode();
                        }).count();
        long configurationProgressSucceedCount =
                list.stream().filter(x -> {
                    Integer progress = x.getConfigurationProgress();
                    return progress != null && progress == ConfigurationProgressEnum.SUCCEED.getCode();
                }).count();
        statistics.setApprovalInProgressCount(approvalInProgressCount);
        statistics.setApprovalRefusedCount(approvalRefusedCount);
        statistics.setConfigurationInProgressCount(configurationInProgressCount);
        statistics.setConfigurationProgressSucceedCount(configurationProgressSucceedCount);
        return statistics;
    }

    /**
     * 订阅管理
     *
     * @param requestDTO
     * @return
     */
    public Map<String, Object> subscribeManagePage(RequestDTO requestDTO) {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<DataMartSubscribe> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataMartSubscribe> queryWrapper = new QueryWrapper<>();
        // 数据拥有人
        queryWrapper.eq("t0.tenant_id", UserUtil.getCurrentTenantId());
        queryWrapper.eq("t0.create_user", UserUtil.getCurrentUsername());
        if (requestDTO.getParam() != null) {
            DataMartSubscribeDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), DataMartSubscribeDTO.class);
            if (StringUtils.isNotBlank(dto.getAssetName())) {
                queryWrapper.like("t0.asset_name", dto.getAssetName());
            }
            if (StringUtils.isNotBlank(dto.getKeyWords())) {
                queryWrapper.like("t0.key_words", dto.getKeyWords());
            }
            if (StringUtils.isNotBlank(dto.getDataName())) {
                queryWrapper.like("t0.data_name", dto.getDataName());
            }
            if (CollectionUtils.isNotEmpty(dto.getSubscribeChannelList())) {
                queryWrapper.in("t0.subscription_channel", dto.getSubscribeChannelList());
            }
            if (CollectionUtils.isNotEmpty(dto.getSubscribeTimeList())) {
                List<String> timeList = dto.getSubscribeTimeList();
                queryWrapper.between("t0.subscription_time", timeList.get(0), timeList.get(1));
            }
            if (CollectionUtils.isNotEmpty(dto.getApprovalProgressList())) {
                queryWrapper.in("t0.approval_progress", dto.getApprovalProgressList());
            }
            if (CollectionUtils.isNotEmpty(dto.getConfigurationProgressList())) {
                queryWrapper.in("t0.configuration_progress", dto.getConfigurationProgressList());
            }
        }

            queryWrapper.orderByDesc("t0.subscription_time");


        IPage<DataMartSubscribeDTO> pageResult = iDataMartSubscribeService.getPage(page, queryWrapper);
        List<DataMartSubscribeDTO> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            records.stream().forEach(record -> {
                record.setSubscriptionChannelName(SubscriptionChannelEnum.getNameByCode(record.getSubscriptionChannel()));
                record.setApprovalProgressName(ApprovalProgressEnum.getNameByCode(record.getApprovalProgress()));
                record.setConfigurationProgressName(ConfigurationProgressEnum.getNameByCode(record.getConfigurationProgress()));
                record.setNetworkDomainName(NetworkDomainEnum.getNameByCode(record.getNetworkDomain()));
                record.setDataUsageName(DataUsageEnum.getNameByCode(record.getDataUsage()));
            });
        }

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);
        return result;
    }

    /**
     * 审批
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void approve(DataMartSubscribeDTO dto) {
        if (ObjectUtils.isEmpty(dto.getId()) || ObjectUtils.isEmpty(dto.getApprovalProgress())) {
            throw new ParamInvalidException("入参异常");
        }
        UpdateWrapper<DataMartSubscribe> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("approval_progress", dto.getApprovalProgress());
        updateWrapper.set("approval_remark", dto.getApprovalRemark());
        updateWrapper.set("approval_time", LocalDateTime.now());
        updateWrapper.set("approval_user", UserUtil.getCurrentUsername());
        if (dto.getApprovalProgress().equals(ApprovalProgressEnum.AGREED.getCode())) {
            // 审批同意，则状态为配置中
            updateWrapper.set("configuration_progress", ConfigurationProgressEnum.IN_PROGRESS.getCode());
        } else {
            updateWrapper.set("configuration_progress", null);
        }
        updateWrapper.eq("id", dto.getId());
        iDataMartSubscribeService.update(updateWrapper);

        // 消息通知
        DataMartSubscribe subscribe = iDataMartSubscribeService.getById(dto.getId());
        DataMartAsset asset = dataMartAssetService.getById(subscribe.getAssetId());
        String content = String.format(Constant.DATAMARK_SUBSCRIBE_APPROVE_NOTICE, asset.getDataName(),
                ApprovalProgressEnum.getNameByCode(dto.getApprovalProgress()));
        DataMartSubscribeNotice notice = new DataMartSubscribeNotice();
        notice.setContent(content);
        notice.setSubscribeId(dto.getId());
        notice.setCreateTime(LocalDateTime.now());
        noticeService.save(notice);
    }

    /**
     * 配置
     *
     * @param dto
     * @throws ParamInvalidException
     */
    @Transactional(rollbackFor = Exception.class)
    public void configuration(DataMartSubscribeDTO dto) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(dto.getId()) || ObjectUtils.isEmpty(dto.getConfigurationProgress())) {
            throw new ParamInvalidException("入参异常");
        }
        UpdateWrapper<DataMartSubscribe> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("configuration_progress", dto.getConfigurationProgress());
        updateWrapper.set("configuration_remark", dto.getConfigurationRemark());
        updateWrapper.set("configuration_time", LocalDateTime.now());
        updateWrapper.set("configuration_user", UserUtil.getCurrentUsername());
        updateWrapper.set("configuration_resource_pool_id", dto.getConfigurationResourcePoolId());
        updateWrapper.set("configuration_business_ip", dto.getConfigurationBusinessIp());
        updateWrapper.set("configuration_hosting_network_ip", dto.getConfigurationHostingNetworkIp());
        updateWrapper.set("configuration_vpc_name", dto.getConfigurationVpcName());
        updateWrapper.set("configuration_network_domain", dto.getConfigurationNetworkDomain());
        updateWrapper.set("configuration_kafka_topic", dto.getConfigurationKafkaTopic());
        updateWrapper.set("configuration_kafka_consumer_group", dto.getConfigurationKafkaConsumerGroup());
        updateWrapper.set("configuration_host_name", dto.getConfigurationHostName());
        if (ObjectUtils.isNotEmpty(dto.getApiId())) {
            updateWrapper.set("api_id", dto.getApiId());
        }
        updateWrapper.eq("id", dto.getId());
        iDataMartSubscribeService.update(updateWrapper);

        // 消息通知
        DataMartSubscribe subscribe = iDataMartSubscribeService.getById(dto.getId());
        DataMartAsset asset = dataMartAssetService.getById(subscribe.getAssetId());
        String content = String.format(Constant.DATAMARK_SUBSCRIBE_CONFIGURATION_NOTICE, asset.getDataName());
        DataMartSubscribeNotice notice = new DataMartSubscribeNotice();
        notice.setContent(content);
        notice.setSubscribeId(dto.getId());
        notice.setCreateTime(LocalDateTime.now());
        noticeService.save(notice);
    }

    /**
     * 新增订阅
     *
     * @param dto
     */
    public void create(DataMartSubscribeDTO dto) {
        UserValueObject uvo = UmsUtils.getUVO();
        List<Long> ids = dto.getIds();
        for (Long id : ids) {
            dto.setAssetId(id);

            //保存
            DataMartSubscribe dataMartSubscribe = null;
            if(dto.getId() != null) {
                dataMartSubscribe = iDataMartSubscribeService.getById(dto.getId());
            }else {
                dataMartSubscribe = new DataMartSubscribe();
            }

            ReflectionUtil.copyLomBokProperties(dto, dataMartSubscribe);
            if (CollectionUtils.isNotEmpty(dto.getUsedTimeList())) {
                List<String> usedTimeList = dto.getUsedTimeList();
                dataMartSubscribe.setBeginTime(DateUtil.parseLocalDateTime(usedTimeList.get(0), "yyyy-MM-dd HH:mm:ss"));
                dataMartSubscribe.setEndTime(DateUtil.parseLocalDateTime(usedTimeList.get(1), "yyyy-MM-dd HH:mm:ss"));
            }
            dataMartSubscribe.setTenantId(UserUtil.getCurrentTenantId());
            dataMartSubscribe.setUserId(UserUtil.getCurrentUserId());
            dataMartSubscribe.setSubscriptionTime(LocalDateTime.now());
            dataMartSubscribe.setSubscriptionUser(UserUtil.getCurrentUsername());
            // 订阅后状态即为审批中
            dataMartSubscribe.setApprovalProgress(ApprovalProgressEnum.IN_PROGRESS.getCode());
            // 设置订阅id
            dataMartSubscribe.setSubscribeRuleId(genSubscribeRuleId(dataMartSubscribe));
            iDataMartSubscribeService.saveOrUpdate(dataMartSubscribe);
            dataMartCartService.remove(new QueryWrapper<DataMartCart>().eq("asset_id", dataMartSubscribe.getAssetId()).eq("tenant_id", uvo.getTenantId()).eq("create_user",uvo.getUserId()));
        }
    }

    /**
     * 生成订阅ID字段，示例：jsapi_20230612_001，生成规则：js+类型（api/kafka)_日期_编号（三位数序号）
     *
     * @param dataMartSubscribe
     * @return
     */
    private String genSubscribeRuleId(DataMartSubscribe dataMartSubscribe) {
        String today = DateFormatUtils.format(new Date(), "yyyyMMdd");
        List<DataMartSubscribe> subscribes = iDataMartSubscribeService.list(new QueryWrapper<DataMartSubscribe>()
                .like("subscribe_rule_id", today));
        StringBuilder builder = new StringBuilder();
        builder.append("js").append(SubscriptionChannelEnum.getRuleByCode(dataMartSubscribe.getSubscriptionChannel()))
                .append("_").append(today).append("_");
        if (CollectionUtils.isEmpty(subscribes)) {
            builder.append("001");
        } else {
            int curMaxNum = 0;
            if (dataMartSubscribe.getSubscriptionChannel() == null) {
                // 入驻式开发
                curMaxNum = subscribes.size();
            } else {
                for (DataMartSubscribe subscribe : subscribes) {
                    if (!dataMartSubscribe.getSubscriptionChannel().equals(subscribe.getSubscriptionChannel())) {
                        continue;
                    }
                    String order = StringUtils.substringAfterLast(subscribe.getSubscribeRuleId(), "_");
                    curMaxNum = NumberUtil.max(curMaxNum, NumberUtil.parseInt(order));
                }
            }
            builder.append(StringUtil.padNumberWithZero(curMaxNum + 1));
        }
        return builder.toString();
    }

    /**
     * 查看详情
     *
     * @param id
     * @return
     * @throws ParamInvalidException
     */
    public DataMartSubscribeDTO detail(Integer id) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        DataMartSubscribeDTO dto = new DataMartSubscribeDTO();
        QueryWrapper<DataMartSubscribe> wrapper = new QueryWrapper();
        wrapper.eq("id", id);
        DataMartSubscribe one = iDataMartSubscribeService.getOne(wrapper);
        if (one != null) {
            ReflectionUtil.copyLomBokProperties(one, dto);
            if (one.getBeginTime() != null && one.getEndTime() != null) {
                List<String> usedTimeList = new ArrayList<>();
                usedTimeList.add(DateUtil.format(one.getBeginTime(), "yyyy-MM-dd HH:mm:ss"));
                usedTimeList.add(DateUtil.format(one.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
                dto.setUsedTimeList(usedTimeList);
            }
            dto.setSubscriptionChannelName(SubscriptionChannelEnum.getNameByCode(one.getSubscriptionChannel()));
            dto.setApprovalProgressName(ApprovalProgressEnum.getNameByCode(one.getApprovalProgress()));
            dto.setConfigurationProgressName(ConfigurationProgressEnum.getNameByCode(one.getConfigurationProgress()));
            dto.setNetworkDomainName(NetworkDomainEnum.getNameByCode(one.getNetworkDomain()));
            dto.setDataUsageName(DataUsageEnum.getNameByCode(one.getDataUsage()));
            DataMartAsset asset = dataMartAssetService.getById(one.getAssetId());
            if (asset != null) {
                dto.setDataName(asset.getDataName());
                dto.setAssetDesc(asset.getAssetDesc());
                dto.setAssetName(asset.getAssetName());
            }
            if (ObjectUtil.isNotEmpty(one.getResourcePoolId())) {
                DataMartResourcePool resourcePool = resourcePoolService.getById(one.getResourcePoolId());
                if (resourcePool != null) {
                    dto.setResourcePoolName(resourcePool.getName());
                }
            }
        }
        return dto;
    }

    /**
     * 配置时查询api
     *
     * @return
     */
    public List<ApiInfo> queryApiList() {
        QueryWrapper<ApiInfo> wrapper = new QueryWrapper();
        wrapper.eq("tenant_id", UserUtil.getCurrentTenantId());
        // 已提交的api
        wrapper.eq("submit_status", 1);
        return apiInfoService.list(wrapper);
    }

    /**
     * 订阅消息分页
     *
     * @param requestDTO
     * @return
     * @throws Exception
     */
    public Map<String, Object> subscribeNoticePage(RequestDTO requestDTO) {
        Map<String, Object> result = new HashMap<>();
        Page<DataMartSubscribeNotice> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<DataMartSubscribeNotice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dms.tenant_id", UserUtil.getCurrentTenantId());
        queryWrapper.eq("dms.user_id", UserUtil.getCurrentUserId());
        queryWrapper.orderByDesc("dmsn.create_time");

        IPage<DataMartSubscribeNotice> pageResult = noticeService.getPage(page, queryWrapper);
        List<DataMartSubscribeNotice> records = pageResult.getRecords();

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", records);
        return result;
    }

    /**
     * 查询资源池树
     *
     * @return
     */
    public List<DataMartResourcePool> resourcePoolTree() {
        List<DataMartResourcePool> rootPools = resourcePoolService.list(new QueryWrapper<DataMartResourcePool>().isNull("parent_id"));
        if (CollectionUtils.isEmpty(rootPools)) {
            return null;
        }
        for (DataMartResourcePool parentPool : rootPools) {
            childLoad(parentPool);
        }
        return rootPools;
    }

    /**
     * 递归查询资源池
     *
     * @param parentPool
     */
    private void childLoad(DataMartResourcePool parentPool) {
        List<DataMartResourcePool> children = resourcePoolService.list(new QueryWrapper<DataMartResourcePool>().eq("parent_id",
                parentPool.getId()));
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        parentPool.setChildren(children);
        for (DataMartResourcePool pool : children) {
            childLoad(pool);
        }
    }

    /**
     * 查询网络域
     *
     * @return
     */
    public List<Map<String, Object>> networkDomains() {
        List<Map<String, Object>> result = new ArrayList<>();
        for (NetworkDomainEnum value : NetworkDomainEnum.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("code", value.getCode());
            map.put("name", value.getName());
            result.add(map);
        }
        return result;
    }
}