/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-12-06
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-12-06
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datamart.dto.request.DataMartAssetPageRequest;
import com.idss.datalake.datamart.dto.response.AssetApprovalStatistics;
import com.idss.datalake.datamart.entity.DataMartAsset;
import com.idss.datalake.datamart.enums.AssetApprovalEnum;
import com.idss.datalake.datamart.enums.AssetPublishEnum;
import com.idss.datalake.datamart.mapper.DataMartAssetMapper;
import com.idss.datalake.datamart.service.IDataMartAssetService;
import com.idss.datalake.datamart.service.IDataMartGroupService;
import com.idss.datalake.datamart.service.IDataMartTypeService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>数据集市资产 manager处理类</p>
 * @since 2023-12-06
 */
@Component
public class DataMartAssetManager {
    private static final Logger logger = LoggerFactory.getLogger(DataMartAssetManager.class);

    @Autowired
    private IDataMartAssetService iDataMartAssetService;
    @Autowired
    private IDataMartGroupService martGroupService;
    @Autowired
    private IDataMartTypeService martTypeService;
    @Resource
    private DataMartAssetMapper dataMartAssetMapper;

    /**
     * 用户发布
     *
     * @param id
     */
    public void userPublish(Long id) throws ParamInvalidException {
        DataMartAsset dataMartAsset = iDataMartAssetService.getById(id);
        if (dataMartAsset == null) {
            throw new ParamInvalidException("数据不存在");
        }
        dataMartAsset.setPublishStatus(AssetPublishEnum.PUBLISHED.getCode());
        dataMartAsset.setPublishTime(LocalDateTime.now());
        dataMartAsset.setApprovalStatus(AssetApprovalEnum.AWAIT.getCode());
        iDataMartAssetService.updateById(dataMartAsset);
    }

    /**
     * 用户撤销，回到数据初始状态
     *
     * @param id
     */
    public void userRevoked(Long id) throws ParamInvalidException {
        DataMartAsset dataMartAsset = iDataMartAssetService.getById(id);
        if (dataMartAsset == null) {
            throw new ParamInvalidException("数据不存在");
        }
        UpdateWrapper<DataMartAsset> wrapper = new UpdateWrapper<>();
        wrapper.set("publish_status", AssetPublishEnum.UNPUBLISHED.getCode());
        wrapper.set("publish_time", null);
        wrapper.set("approval_status", AssetApprovalEnum.REVOKED.getCode());
        wrapper.set("approval_user", "");
        wrapper.set("approval_time", null);
        wrapper.set("approval_desc", "");
        wrapper.eq("id", id);
        iDataMartAssetService.update(wrapper);
    }

    /**
     * 审批管理页面
     *
     * @param
     * @return
     */
    public Map<String, Object> assetApprovalPage(DataMartAssetPageRequest request) {
        Map<String, Object> result = new HashMap<>();
        // 查询所有用户已发布数据，不区分租户
        request.setPublishStatus(AssetPublishEnum.PUBLISHED.getCode());
        request.setOrderType("publish_time");
        Page<DataMartAsset> page = dataMartAssetMapper.page(request);
        if (page.isEmpty()) {
            result.put("pageNum", page.getPageNum());
            result.put("pageSize", page.getPageSize());
            result.put("total", page.getTotal());
            result.put("data", new ArrayList<>());
            return result;
        }
        List<DataMartAsset> records = page.getResult();
        for (DataMartAsset dataMartAsset : records) {
            dataMartAsset.setDataType(dataMartAsset.getAssetType().split("_")[0]);
            if (dataMartAsset.getPublishStatus() == 0) {
                dataMartAsset.setApprovalStatusName(AssetPublishEnum.getNameByCode(dataMartAsset.getPublishStatus()));
            } else {
                dataMartAsset.setApprovalStatusName(AssetApprovalEnum.getNameByCode(dataMartAsset.getApprovalStatus()));
            }
        }

        result.put("pageNum", page.getPageNum());
        result.put("pageSize", page.getPageSize());
        result.put("total", page.getTotal());
        result.put("data", records);
        return result;
    }

    public void adminPublish(DataMartAsset asset) throws ParamInvalidException {
        DataMartAsset dataMartAsset = iDataMartAssetService.getById(asset.getId());
        if (dataMartAsset == null) {
            throw new ParamInvalidException("数据不存在");
        }
        dataMartAsset.setApprovalStatus(asset.getApprovalStatus());
        dataMartAsset.setApprovalUser(UserUtil.getCurrentUsername());
        dataMartAsset.setApprovalTime(LocalDateTime.now());
        dataMartAsset.setApprovalDesc(asset.getApprovalDesc());

        iDataMartAssetService.updateById(dataMartAsset);
    }

    /**
     * 审批撤销
     *
     * @param id
     * @throws ParamInvalidException
     */
    public void adminRevoked(Long id) throws ParamInvalidException {
        DataMartAsset dataMartAsset = iDataMartAssetService.getById(id);
        if (dataMartAsset == null) {
            throw new ParamInvalidException("数据不存在");
        }
        UpdateWrapper<DataMartAsset> wrapper = new UpdateWrapper<>();
        wrapper.set("approval_status", AssetApprovalEnum.REVOKED.getCode());
        wrapper.set("approval_user", "");
        wrapper.set("approval_time", null);
        wrapper.set("approval_desc", "");
        wrapper.eq("id", dataMartAsset.getId());
        iDataMartAssetService.update(wrapper);
    }

    public AssetApprovalStatistics assetApprovalStatistics() {
        AssetApprovalStatistics statistics = new AssetApprovalStatistics();
        QueryWrapper<DataMartAsset> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("tenant_id", UserUtil.getCurrentTenantId());
        List<DataMartAsset> list = iDataMartAssetService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return statistics;
        }
        long awaitCount =
                list.stream().filter(x -> {
                    Integer progress = x.getApprovalStatus();
                    return progress != null && progress == AssetApprovalEnum.AWAIT.getCode();
                }).count();
        long agreedCount =
                list.stream().filter(x -> {
                    Integer progress = x.getApprovalStatus();
                    return progress != null && progress == AssetApprovalEnum.AGREED.getCode();
                }).count();
        long refusedCount =
                list.stream().filter(x -> {
                    Integer progress = x.getApprovalStatus();
                    return progress != null && progress == AssetApprovalEnum.REFUSED.getCode();
                }).count();
        statistics.setAwaitCount(awaitCount);
        statistics.setAgreedCount(agreedCount);
        statistics.setRefusedCount(refusedCount);
        return statistics;
    }
}