/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/4/18
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/4/18
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.schdule;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.util.ClickhouseUtil;
import com.idss.datalake.common.util.DateUtils;
import com.idss.datalake.common.util.ElasticSearchUtil;
import com.idss.datalake.common.util.HiveUtil;
import com.idss.datalake.common.util.MysqlUtil;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.element.utils.BtoaEncode;
import com.idss.datalake.datamart.dto.response.ElementInfo;
import com.idss.datalake.datamart.entity.DataMartAsset;
import com.idss.datalake.datamart.entity.DataMartTableLines;
import com.idss.datalake.datamart.enums.ElementTypeEnum;
import com.idss.datalake.datamart.mapper.DataMartAssetMapper;
import com.idss.datalake.datamart.service.IDataMartAssetService;
import com.idss.datalake.datamart.service.IDataMartTableLinesService;
import com.idss.radar.datasource.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2024/4/18
 */
@Component
@Slf4j
public class TableLineSchedule {
    @Autowired
    private IDataMartAssetService dataMartAssetService;
    @Autowired
    private IDataMartTableLinesService dataMartTableLinesService;
    @Autowired
    private QuaWabElementService quaWabElementService;
    @Resource
    private DataMartAssetMapper dataMartAssetMapper;

//    @Scheduled(cron = "0 0 1 * * ?")
    @Scheduled(fixedDelay = 3600 * 1000 * 24)
    public void doCount() {
        DatasourceType.clearDataBaseType();
        //获取所有资产
        List<DataMartAsset> dataMartAssetList = dataMartAssetService.list();
        List<DataMartTableLines> dataMartTableLinesList = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String now = format.format(new Date());
        for (DataMartAsset dataMartAsset : dataMartAssetList) {
            DataMartTableLines tableLines = new DataMartTableLines();
            tableLines.setAssetId(dataMartAsset.getId());
            tableLines.setCountDay(now);

            JSONObject tableLine = new JSONObject();
            Long tableLineAll = 0L;
            BigDecimal tableSpaceAll = new BigDecimal(0);
            if (StringUtils.isNotEmpty(dataMartAsset.getAssetJson())) {

                JSONArray assetArray = JSONArray.parseArray(dataMartAsset.getAssetJson());
                for (int i = 0; i < assetArray.size(); i++) {
                    JSONObject assetJson = assetArray.getJSONObject(i);
                    String assetType = assetJson.getString("assetType");
                    Long elementId = assetJson.getLong("elementId");
                    String assetPath = assetJson.getString("assetPath");
                    String[] db_table = assetPath.split(",");
                    QuaWabElement element = quaWabElementService.getById(elementId);
                    /*if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(assetType)) {
                        Connection connect = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_MYSQL, "jdbc:mysql://" + element.getChIp() + ":" + element.getChPort() + "/" + db_table[0], element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()));
                        Long line = 0L;
                        try {
                            line = MysqlUtil.countTableLine(connect, db_table[1]);
                            tableLineAll += line;
                            tableSpaceAll = tableSpaceAll.add(MysqlUtil.countTableSpace(connect, db_table[0], db_table[1]));

                        } catch (Exception e) {
                            log.error("查询表行数错误", e);
                        } finally {
                            if (connect != null) {
                                MysqlUtil.close(connect);
                            }
                        }
                        tableLine.put(db_table[1], line);
                    } else*/ if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(assetType)) {
                        Connection connect = ClickhouseUtil.getConnect("jdbc:clickhouse://" + element.getChIp() + ":" + element.getChPort() + "/" + db_table[0], element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()));
                        Long line = 0L;
                        try {
                            line = ClickhouseUtil.countTableLine(connect, db_table[1]);
                            tableLineAll += line;
                            tableSpaceAll = tableSpaceAll.add(ClickhouseUtil.countTableSpace(connect, db_table[0], db_table[1]));
                        } catch (Exception e) {
                            log.error("查询表行数错误", e);
                        } finally {
                            if (connect != null) {
                                ClickhouseUtil.close(connect);
                            }
                        }
                        tableLine.put(db_table[1], line);
                    } else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(assetType)) {
                        Long line = 0L;
                        Connection connect = null;
                        try {
                            ElementInfo elementInfo = dataMartAssetMapper.queryElementInfo(elementId);
                            String url = "";
                            if (elementInfo.getKbsEnable() == 0) {
                                String password = elementInfo.getDbPassword();
                                if (StringUtils.isNotEmpty(password)) {
                                    password = com.idss.datalake.common.util.BtoaEncode.decrypt(password);
                                }
                                url = "jdbc:hive2://" + elementInfo.getDbIp() + ":" + elementInfo.getDbPort() + "/" + db_table[0];
                                connect = HiveUtil.getConnect(url, elementInfo.getDbUserName(), password, "0", null, null, null);
                            } else {
                                BASE64Decoder decoder = new BASE64Decoder();
                                url = elementInfo.getJdbcUrl();
                                String keyTabPath = element.getKeyTabPath();
                                if (StringUtils.isNotEmpty(keyTabPath)) {
                                    keyTabPath = new String(decoder.decodeBuffer(keyTabPath));
                                }
                                String krb5ConfPath = element.getKrb5ConfPath();
                                if (StringUtils.isNotEmpty(krb5ConfPath)) {
                                    krb5ConfPath = new String(decoder.decodeBuffer(krb5ConfPath));
                                }
                                connect = HiveUtil.getConnect(url, null, null, "1", elementInfo.getDbUserName(), keyTabPath, krb5ConfPath);
                            }
                            line = HiveUtil.countTableLine(connect, db_table[1]);
                            tableLineAll += line;
                            tableSpaceAll = tableSpaceAll.add(HiveUtil.countTableSpace(connect, db_table[0], db_table[1]));
                            HiveUtil.close(connect);
                        } catch (Exception e) {
                            log.error("查询表行数错误", e);
                        } finally {
                            if (connect != null) {
                                HiveUtil.close(connect);
                            }
                        }
                        tableLine.put(db_table[1], line);
                    }
                }
            }
            tableLines.setTableLine(tableLine.toJSONString());
            tableLines.setTableLineAll(tableLineAll);
            tableLines.setTableSpaceAll(tableSpaceAll);
            dataMartTableLinesList.add(tableLines);
        }
        dataMartTableLinesService.saveBatch(dataMartTableLinesList);

        updateOldNewTime();
    }

    /**
     * 更新最早最新时间
     */
    private void updateOldNewTime() {
        log.info("开始统计时间更新");
        //获取所有资产
        List<DataMartAsset> dataMartAssetList = dataMartAssetService.list();
        for (DataMartAsset dataMartAsset : dataMartAssetList) {
            List<Date> oldList = new ArrayList<>();
            List<Date> newList = new ArrayList<>();
            JSONArray assetArray = JSONArray.parseArray(dataMartAsset.getAssetJson());
            for (int i = 0; i < assetArray.size(); i++) {
                JSONObject assetJson = assetArray.getJSONObject(i);
                String assetType = assetJson.getString("assetType");
                Long elementId = assetJson.getLong("elementId");
                String assetPath = assetJson.getString("assetPath");
                String oldTime = assetJson.getString("oldTime");
                String newTime = assetJson.getString("newTime");
                if (StringUtils.isEmpty(oldTime) && StringUtils.isEmpty(newTime)) {
                    continue;
                }
                String[] db_table = assetPath.split(",");
                QuaWabElement element = quaWabElementService.getById(elementId);
                if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(assetType)) {
                    Connection connect = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_MYSQL, "jdbc:mysql://" + element.getChIp() + ":" + element.getChPort() + "/" + db_table[0], element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()));
                    try {
                        if (StringUtils.isNotEmpty(oldTime)) {
                            Date oldT = MysqlUtil.queryTime(connect, db_table[0], db_table[1], oldTime, "old");
                            if (oldT != null) {
                                oldList.add(oldT);
                            }
                        }
                        if (StringUtils.isNotEmpty(newTime)) {
                            Date newT = MysqlUtil.queryTime(connect, db_table[0], db_table[1], newTime, "new");
                            if (newT != null) {
                                newList.add(newT);
                            }
                        }

                    } catch (Exception e) {
                        log.error("查询时间错误", e);
                    } finally {
                        if (connect != null) {
                            MysqlUtil.close(connect);
                        }
                    }
                } else if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(assetType)) {
                    Connection connect = ClickhouseUtil.getConnect("jdbc:clickhouse://" + element.getChIp() + ":" + element.getChPort() + "/" + db_table[0], element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()));
                    try {
                        if (StringUtils.isNotEmpty(oldTime)) {
                            Date oldT = ClickhouseUtil.queryTime(connect, db_table[0], db_table[1], oldTime, "old");
                            if (oldT != null) {
                                oldList.add(oldT);
                                log.info("CH_oldT:{}",oldT);
                            }
                        }
                        if (StringUtils.isNotEmpty(newTime)) {
                            Date newT = ClickhouseUtil.queryTime(connect, db_table[0], db_table[1], newTime, "new");
                            if (newT != null) {
                                newList.add(newT);
                                log.info("CH_newT:{}",newT);
                            }
                        }
                    } catch (Exception e) {
                        log.error("查询时间错误", e);
                    } finally {
                        if (connect != null) {
                            ClickhouseUtil.close(connect);
                        }
                    }
                } else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(assetType)) {
                    Connection connect = null;
                    if (element.getKbsEnable() == 1) {
                        BASE64Decoder decoder = new BASE64Decoder();
                        try {
                            connect = HiveUtil.getConnect(element.getJdbcUrl(), element.getChUserName(), StringUtils.isEmpty(element.getChUserPassword()) ? "" : BtoaEncode.decrypt(element.getChUserPassword()), "1", element.getChUserName(), new String(decoder.decodeBuffer(element.getKeyTabPath())), new String(decoder.decodeBuffer(element.getKrb5ConfPath())));
                        } catch (IOException e) {
                            log.error("解密失败", e);
                            return;
                        }
                    } else {
                        connect = HiveUtil.getConnect(
                                "jdbc:hive2://" + element.getChIp() + ":" + element.getChPort() + "/" + db_table[0], element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()), "", "", "", "");
                    }
                    try {
                        if (StringUtils.isNotEmpty(oldTime)) {
                            Date oldT = ClickhouseUtil.queryTime(connect, db_table[0], db_table[1], oldTime, "old");
                            if (oldT != null) {
                                oldList.add(oldT);
                            }
                        }
                        if (StringUtils.isNotEmpty(newTime)) {
                            Date newT = ClickhouseUtil.queryTime(connect, db_table[0], db_table[1], newTime, "new");
                            if (newT != null) {
                                newList.add(newT);
                            }
                        }
                    } catch (Exception e) {
                        log.error("查询时间错误", e);
                    } finally {
                        if (connect != null) {
                            ClickhouseUtil.close(connect);
                        }
                    }
                } else if (ElementTypeEnum.ELASTICSEARCH_INDEX.getCode().equals(assetType)) {
                    URL url = null;
                    try {
                        url = new URL(element.getEsIpPort());
                    } catch (MalformedURLException e) {
                        log.error("解析ES地址错误",e);
                        return;
                    }
                    String host = url.getHost();
                    int port = url.getPort(); // 返回端口号，如果未指定则返回 -1
                    if (StringUtils.isNotEmpty(oldTime)) {
                        Date oldT = ElasticSearchUtil.queryTime(host, port, element.getChUserName(),StringUtils.isEmpty(element.getChUserPassword()) ? "" : BtoaEncode.decrypt(element.getChUserPassword()), db_table[0], oldTime, "old");
                        if (oldT != null) {
                            oldList.add(oldT);
                            log.info("ES_oldT:{}",oldT);
                        }
                    }
                    if (StringUtils.isNotEmpty(newTime)) {
                        Date newT = ElasticSearchUtil.queryTime(host, port, element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()), db_table[0], newTime, "new");
                        if (newT != null) {
                            newList.add(newT);
                            log.info("ES_newT:{}",newT);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(oldList)) {
                log.info("oldList:{}",JSONObject.toJSONString(oldList.stream().sorted(Comparator.comparing(Date::getTime)).collect(Collectors.toList())));
                dataMartAsset.setStartTime(DateUtils.convertToString(oldList.stream().sorted(Comparator.comparing(Date::getTime)).collect(Collectors.toList()).get(0),
                        "yyyy-MM-dd HH:mm:ss"));
            }
            if (CollectionUtils.isNotEmpty(newList)) {
                log.info("newList:{}",JSONObject.toJSONString(newList.stream().sorted(Comparator.comparing(Date::getTime).reversed()).collect(Collectors.toList())));
                dataMartAsset.setEndTime(DateUtils.convertToString(newList.stream().sorted(Comparator.comparing(Date::getTime).reversed()).collect(Collectors.toList()).get(0),
                        "yyyy-MM-dd HH:mm:ss"));
            }



            dataMartAssetService.update(new UpdateWrapper<DataMartAsset>().set("start_time", dataMartAsset.getStartTime()).set("end_time", dataMartAsset.getEndTime()).eq("id", dataMartAsset.getId()));
        }
        log.info("结束统计时间更新");
    }

}
