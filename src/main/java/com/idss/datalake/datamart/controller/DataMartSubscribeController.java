/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-06-15
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2023-06-15
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datamart.controller;


import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datamart.dto.request.DataMartSubscribeDTO;
import com.idss.datalake.datamart.manager.DataMartSubscribeManager;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description <p>数据集市订阅表 前端控制器</p>
 * @since 2023-06-15
 */
@RestController
@RequestMapping("/datamark/subscribe")
public class DataMartSubscribeController {
    private static final Logger logger = LoggerFactory.getLogger(DataMartSubscribeController.class);
    @Autowired
    private DataMartSubscribeManager dataMartSubscribeManager;

    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody DataMartSubscribeDTO dataMartSubscribeDTO) {
        try {
            dataMartSubscribeManager.create(dataMartSubscribeDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Integer id) {
        try {
            return ResultBean.success(dataMartSubscribeManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "我的订阅")
    @PostMapping(value = "/mySubscribePage")
    public ResultBean mySubscribePage(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataMartSubscribeManager.mySubscribePage(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "表/索引的订阅信息")
    @PostMapping(value = "/tableSubscribePage")
    public ResultBean tableSubscribePage(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataMartSubscribeManager.tableSubscribePage(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "我的订阅统计")
    @GetMapping(value = "/mySubscribeStatistics")
    public ResultBean mySubscribeStatistics() {
        try {
            return ResultBean.success(dataMartSubscribeManager.mySubscribeStatistics());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "订阅管理")
    @PostMapping(value = "/subscribeManagePage")
    public ResultBean subscribeManagePage(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataMartSubscribeManager.subscribeManagePage(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "审批")
    @PostMapping(value = "/approve")
    public ResultBean approve(@RequestBody DataMartSubscribeDTO dataMartSubscribeDTO) {
        try {
            dataMartSubscribeManager.approve(dataMartSubscribeDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "配置")
    @PostMapping(value = "/configuration")
    public ResultBean configuration(@RequestBody DataMartSubscribeDTO dataMartSubscribeDTO) {
        try {
            dataMartSubscribeManager.configuration(dataMartSubscribeDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "配置时查询api")
    @GetMapping(value = "/queryApiList")
    public ResultBean queryApiList() {
        try {
            return ResultBean.success(dataMartSubscribeManager.queryApiList());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "订阅消息分页")
    @PostMapping(value = "/subscribeNoticePage")
    public ResultBean subscribeNoticePage(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(dataMartSubscribeManager.subscribeNoticePage(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询资源池")
    @GetMapping(value = "/resourcePoolTree")
    public ResultBean resourcePoolTree() {
        try {
            return ResultBean.success(dataMartSubscribeManager.resourcePoolTree());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询网络域")
    @GetMapping(value = "/networkDomains")
    public ResultBean networkDomains() {
        try {
            return ResultBean.success(dataMartSubscribeManager.networkDomains());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }
}
