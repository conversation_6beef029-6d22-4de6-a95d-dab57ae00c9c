package com.idss.datalake.datamart.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datamart.dto.request.DataMartTypePageRequest;
import com.idss.datalake.datamart.entity.DataMartType;
import com.idss.datalake.datamart.service.IDataMartTypeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 数据集市分类 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@RestController
@RequestMapping("/datamart/type")
public class DataMartTypeController {
    @Autowired
    private IDataMartTypeService dataMartTypeService;

    /**
     * 新增或修改
     *
     * @param dataMartType
     * @return
     */
    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody DataMartType dataMartType) {
        if(StringUtils.isNotEmpty(dataMartType.getTypeDesc()) && dataMartType.getTypeDesc().length() > 100){
            return ResultBean.fail("描述过长,不能超过100个！");
        }
        return dataMartTypeService.addOrUpdate(dataMartType);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    public ResultBean delete(@PathVariable("id") Long id) {
        return dataMartTypeService.delete(id);
    }

    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/page")
    public BasePageResponse<List<DataMartType>> page(@RequestBody DataMartTypePageRequest request){
        return dataMartTypeService.page(request);
    }

    /**
     * 分类下拉
     *
     * @return
     */
    @GetMapping("/listAll")
    public ResultBean listAll(){
        return dataMartTypeService.listAll();
    }

    /**
     * 按租户分类下拉
     *
     * @return
     */
    @GetMapping("/listAllByTenant")
    public ResultBean listAllByTenant(){
        return dataMartTypeService.listAllByTenant();
    }
}
