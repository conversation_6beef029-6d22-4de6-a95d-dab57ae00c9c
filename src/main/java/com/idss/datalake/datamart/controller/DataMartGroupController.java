package com.idss.datalake.datamart.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datamart.dto.request.AddDataMartAssetRequest;
import com.idss.datalake.datamart.dto.request.DataMartGroupPageRequest;
import com.idss.datalake.datamart.entity.DataMartGroup;
import com.idss.datalake.datamart.service.IDataMartGroupService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 数据集市分组 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@RestController
@RequestMapping("/datamart/group")
public class DataMartGroupController {
    @Autowired
    private IDataMartGroupService dataMartGroupService;

    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody DataMartGroup dataMartGroup) {
        if (StringUtils.isNotEmpty(dataMartGroup.getGroupDesc()) && dataMartGroup.getGroupDesc().length() > 100) {
            return ResultBean.fail("描述过长,不能超过100个！");
        }
        return dataMartGroupService.addOrUpdate(dataMartGroup);
    }

    @GetMapping("/delete/{id}")
    public ResultBean delete(@PathVariable("id") Long id) {
        return dataMartGroupService.delete(id);
    }

    @PostMapping("/page")
    public BasePageResponse<List<DataMartGroup>> page(@RequestBody DataMartGroupPageRequest request) {
        return dataMartGroupService.page(request);
    }

    @PostMapping("/listByTypeId/{typeId}")
    public ResultBean listByTypeId(@PathVariable("typeId") Long typeId, @RequestBody AddDataMartAssetRequest request) {
        return dataMartGroupService.listByTypeId(typeId, request);
    }

    @GetMapping("/listByTypeId/{typeId}")
    public ResultBean listByTypeId(@PathVariable("typeId") Long typeId) {
        return dataMartGroupService.listByTypeId(typeId);
    }
}
