package com.idss.datalake.datamart.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datamart.service.IDataMartAssetPublishService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 数据集市资源发布 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@RestController
@RequestMapping("/datamart/publish")
public class DataMartAssetPublishController {
    @Autowired
    public IDataMartAssetPublishService dataMartAssetPublishService;
    /**
     * 列出所有发布
     * @return
     */
    @GetMapping("/all")
    public ResultBean listPublish(){
        return dataMartAssetPublishService.listPublish();
    }
}
