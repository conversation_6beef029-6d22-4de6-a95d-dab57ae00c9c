package com.idss.datalake.datamart.controller;


import com.alibaba.fastjson.JSONArray;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.datagovern.dictionary.dto.DataDictionaryItemDTO;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.model.job.service.ChTaskResultColumnService;
import com.idss.datalake.datagovern.metadata.model.job.service.EsTaskResultIndexService;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebHiveTaskResultColumnService;
import com.idss.datalake.datagovern.metadata.model.job.service.IQuaWebMysqlTaskResultColumnService;
import com.idss.datalake.datamart.dto.MyScoreDto;
import com.idss.datalake.datamart.dto.request.*;
import com.idss.datalake.datamart.dto.response.AssetDetailPageVo;
import com.idss.datalake.datamart.dto.response.AssetMapView;
import com.idss.datalake.datamart.dto.response.QueryElementVo;
import com.idss.datalake.datamart.entity.DataMartAsset;
import com.idss.datalake.datamart.entity.DataMartAssetPublish;
import com.idss.datalake.datamart.entity.DataMartDiscuss;
import com.idss.datalake.datamart.enums.ElementTypeEnum;
import com.idss.datalake.datamart.manager.DataMartAssetManager;
import com.idss.datalake.datamart.service.IDataMartAssetService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据集市资产 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@Slf4j
@RestController
@RequestMapping("/datamart/asset")
public class DataMartAssetController {
    @Autowired
    private IDataMartAssetService dataMartAssetService;
    @Autowired
    private DataMartAssetManager dataMartAssetManager;
    @Autowired
    private ChTaskResultColumnService chTaskResultColumnService;
    @Autowired
    private IQuaWebMysqlTaskResultColumnService mysqlTaskResultColumnService;
    @Autowired
    private IQuaWebHiveTaskResultColumnService hiveTaskResultColumnService;
    @Autowired
    private EsTaskResultIndexService esTaskResultIndexService;

    /**
     * 集市地图分页
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/assetMapPage")
    public BasePageResponse<List<AssetMapView>> assetMapPage(@RequestBody AssetMapGroupRequest requestDto) {
        return dataMartAssetService.assetMapPage(requestDto);
    }

    /**
     * 集市地图左侧类型树
     * @param request
     * @return
     */
    @PostMapping("/assetMapTypeList")
    public ResultBean assetMapTypeList(@RequestBody AssetMapGroupRequest request){
        if(request.getTypeId() == null){
            return ResultBean.fail("类型ID不能为空");
        }
        UserValueObject uvo = UmsUtils.getUVO();
        request.setTenantId(Long.parseLong(uvo.getTenantId()+""));
        request.setUserId(uvo.getUserId());
        return ResultBean.success(dataMartAssetService.assetMapTypeList(request));
    }

    /**
     * 查询表、索引字段
     * @param request
     * @return
     */
    @PostMapping("/queryElementField")
    public ResultBean queryElementField(@RequestBody QueryElementFieldRequest request){
        return ResultBean.success(dataMartAssetService.queryElementField(request));
    }

    /**
     * 分页查询元数据
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/queryElementPage")
    public BasePageResponse<List<QueryElementVo>> queryElementPage(@RequestBody QueryElementRequest requestDto) {
        return dataMartAssetService.queryElementPage(requestDto);
    }

    /**
     * 新增资源
     *
     * @param request
     * @return
     */
    @PostMapping("/add")
    public ResultBean add(@RequestBody AddDataMartAssetRequest request) {
        return dataMartAssetService.addOrUpdate(request);
    }

    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/page")
    public BasePageResponse<List<DataMartAsset>> page(@RequestBody DataMartAssetPageRequest request) {
        return dataMartAssetService.page(request);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    public ResultBean delete(@PathVariable("id") Long id) {
        return dataMartAssetService.delete(id);
    }

    /**
     * 详情明细
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        return dataMartAssetService.detail(id);
    }

    /**
     * 发布
     *
     * @param dataMartAssetPublish
     * @return
     */
    @PostMapping("/release")
    public ResultBean release(@RequestBody DataMartAssetPublish dataMartAssetPublish) {
        return dataMartAssetService.release(dataMartAssetPublish);
    }

    /**
     * 移除发布
     *
     * @param id
     * @return
     */
    @GetMapping("/unRelease/{id}")
    public ResultBean unRelease(@PathVariable("id") Long id) {
        return dataMartAssetService.unRelease(id);
    }

    /**
     * 订阅
     *
     * @param id
     * @param addOrCancel 0 取消，1订阅
     * @return
     */
    @GetMapping("/subscribe/{id}/{addOrCancel}")
    public ResultBean subscribe(@PathVariable("id") Long id, @PathVariable("addOrCancel") Long addOrCancel) {
        return dataMartAssetService.subscribe(id, addOrCancel);
    }

    /**
     * 点赞
     *
     * @param id
     * @param addOrCancel 0 取消，1 点赞
     * @return
     */
    @GetMapping("/thumbsUp/{id}/{addOrCancel}")
    public ResultBean thumbsUp(@PathVariable("id") Long id, @PathVariable("addOrCancel") Long addOrCancel) {
        return dataMartAssetService.thumbsUp(id, addOrCancel);
    }

    /**
     * 资产详情
     *
     * @param id
     * @return
     */
    @GetMapping("/assetDetail/{id}")
    public ResultBean assetDetail(@PathVariable Long id) {
        return dataMartAssetService.assetDetail(id);
    }

    /**
     * 资产详情 分页查询
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryAssetDetailPage")
    public BasePageResponse<List<AssetDetailPageVo>> queryAssetDetailPage(@RequestBody RequestDTO dto) {
        DataDictionaryItemDTO itemDTO = JSONUtil.objectMapperToObj(dto.getParam(), DataDictionaryItemDTO.class);
        String fieldType = itemDTO.getFieldType();
        Long assetId = itemDTO.getId();
        Integer isPrimaryKey = itemDTO.getIsPrimaryKey();
        Integer isRequired = itemDTO.getIsRequired();
        String itemName = itemDTO.getItemName();

        AssetDetailPageRequest requestDto = new AssetDetailPageRequest();
        DataMartAsset byId = dataMartAssetService.getById(assetId);
        requestDto.setPageSize(dto.getGlobal().getPageSize());
        requestDto.setPageNum(dto.getGlobal().getPageNum());
        requestDto.setAssetId(assetId);
        requestDto.setFieldName(itemName);
        requestDto.setFieldType(fieldType);
        requestDto.setIsPrimaryKey(isPrimaryKey);
        requestDto.setIsRequired(isRequired);
        requestDto.setElementId(byId.getElementId());
        requestDto.setDbName(byId.getDbName());
        requestDto.setTableName(byId.getTableName());

        if(ElementTypeEnum.MYSQL_TABLE.getCode().equals(byId.getAssetType())){
            requestDto.setVersion(mysqlTaskResultColumnService.maxSnapshootVersionByElementId(byId.getElementId()));
        } else if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(byId.getAssetType())) {
            requestDto.setVersion(chTaskResultColumnService.maxSnapshootVersionByElementId(byId.getElementId()));
        }else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(byId.getAssetType())) {
            requestDto.setVersion(hiveTaskResultColumnService.maxSnapshootVersionByElementId(byId.getElementId()));
        }else if (ElementTypeEnum.ELASTICSEARCH_INDEX.getCode().equals(byId.getAssetType())) {
            requestDto.setIndexName(byId.getDataName());
            requestDto.setVersion(esTaskResultIndexService.maxSnapshootVersionByElementId(byId.getElementId()));
        }
        return dataMartAssetService.queryAssetDetailPage(requestDto);
    }


    /**
     * 预览数据
     *
     * @param assetId
     * @return
     */
    @GetMapping("/previewData/{assetId}")
    public ResultBean previewData(@PathVariable("assetId") Long assetId) {
        try {
            return dataMartAssetService.previewData(assetId);
        } catch (Exception e) {
            log.error("预览数据错误,{}", e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 数据条数趋势
     * @param id
     * @return
     */
    @GetMapping("/tableLineTrend/{id}")
    public ResultBean tableLineTrend(@PathVariable("id") Long id) {
        try {
            return dataMartAssetService.tableLineTrend(id);
        } catch (Exception e) {
            log.error("数据条数趋势,{}", e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 数据评分
     * @param id
     * @return
     */
    @GetMapping("/assetScore/{id}")
    public ResultBean assetScore(@PathVariable("id") Long id) {
        try {
            return dataMartAssetService.assetScore(id);
        } catch (Exception e) {
            log.error("数据拼分,{}", e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 我的评分
     * @param score {"assetId":1,"availableScore":1,"visibleScore":1,"credibleScore":1}
     * @return
     */
    @PostMapping("/myScore")
    public ResultBean myScore(@RequestBody MyScoreDto score) {
        try {
            Map<String,Integer> param = new HashMap<>();
            param.put("assetId",score.getAssetId());
            param.put("availableScore",score.getAvailableScore());
            param.put("visibleScore",score.getVisibleScore());
            param.put("credibleScore",score.getCredibleScore());
            return dataMartAssetService.myScore(param);
        } catch (Exception e) {
            log.error("数据拼分,{}", e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }


    /**
     * 我的评论
     * @param discuss {"assetId":1,"discuss":"aaa"}
     * @return
     */
    @PostMapping("/discuss")
    public ResultBean discuss(@RequestBody Map<String,Object> discuss) {
        try {
            return dataMartAssetService.discuss(discuss);
        } catch (Exception e) {
            log.error("我的评论,{}", e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/discussPage")
    public BasePageResponse<List<DataMartDiscuss>> discussPage(@RequestBody DiscussPageRequest request) {
        return dataMartAssetService.discussPage(request);
    }
    //关系图

    @GetMapping("/relationChart/{assetId}")
    public ResultBean discussPage(@PathVariable("assetId") Long assetId) {
        return dataMartAssetService.relationChart(assetId);
    }



    @GetMapping("/overview")
    public ResultBean overview() {
        try {
            return dataMartAssetService.overview();
        } catch (Exception e) {
            log.error("数据集市总览错误,{}", e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @GetMapping("/overview/open/{tenantId}")
    public ResultBean overview(@PathVariable("tenantId") Integer tenantId) {
        try {
            return dataMartAssetService.overview(tenantId);
        } catch (Exception e) {
            log.error("数据集市总览错误,{}", e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 用户发布
     *
     * @return
     */
    @GetMapping("/userPublish/{id}")
    public ResultBean userPublish(@PathVariable("id") Long id) {
        try {
            dataMartAssetManager.userPublish(id);
            return ResultBean.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("");
        }
    }

    /**
     * 用户撤销
     *
     * @return
     */
    @GetMapping("/userRevoked/{id}")
    public ResultBean userRevoked(@PathVariable("id") Long id) {
        try {
            dataMartAssetManager.userRevoked(id);
            return ResultBean.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("");
        }
    }

    /**
     * 审批管理页面分页
     *
     * @param request
     * @return
     */
    @PostMapping("/assetApprovalPage")
    public ResultBean assetApprovalPage(@RequestBody DataMartAssetPageRequest request) {
        try {
            return ResultBean.success(dataMartAssetManager.assetApprovalPage(request));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("");
        }
    }

    /**
     * 审批管理-审批
     *
     * @param param
     * @return
     */
    @PostMapping("/adminPublish")
    public ResultBean adminPublish(@RequestBody Map<String,Object> param) {
        try {
            List<Long> ids = JSONArray.parseArray(param.get("ids").toString(),Long.class);
            String approvalDesc = param.get("approvalDesc").toString();
            Integer approvalStatus = Integer.parseInt(param.get("approvalStatus").toString());
            for (Long id : ids) {
                DataMartAsset dataMartAsset = new DataMartAsset();
                dataMartAsset.setId(id);
                dataMartAsset.setApprovalDesc(approvalDesc);
                dataMartAsset.setApprovalStatus(approvalStatus);
                dataMartAssetManager.adminPublish(dataMartAsset);
            }
            return ResultBean.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("");
        }
    }

    /**
     * 管理员审批撤销
     *
     * @return
     */
    @PostMapping("/adminRevoked")
    public ResultBean adminRevoked(@RequestBody Map<String,List<Long>> param) {
        try {
            List<Long> ids = param.get("ids");
            for (Long id : ids) {
                dataMartAssetManager.adminRevoked(id);
            }
            return ResultBean.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("");
        }
    }

    /**
     * 数据集市-数据发布-审批统计
     *
     * @return
     */
    @GetMapping("/assetApprovalStatistics")
    public ResultBean assetApprovalStatistics() {
        try {
            return ResultBean.success(dataMartAssetManager.assetApprovalStatistics());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("");
        }
    }

}
