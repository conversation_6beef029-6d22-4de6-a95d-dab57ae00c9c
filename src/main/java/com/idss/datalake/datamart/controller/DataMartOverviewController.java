package com.idss.datalake.datamart.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datamart.dto.request.DataMartOverviewRequest;
import com.idss.datalake.datamart.dto.response.DataMartOverviewResponse;
import com.idss.datalake.datamart.dto.response.EtlSummary;
import com.idss.datalake.datamart.dto.response.MartSubscribe;
import com.idss.datalake.datamart.dto.response.MartTypeOverview;
import com.idss.datalake.datamart.dto.response.MartTypePercent;
import com.idss.datalake.datamart.dto.response.QualityJobInfo;
import com.idss.datalake.datamart.dto.response.QualityOverview;
import com.idss.datalake.datamart.service.IDataMartOverviewService;
import com.idss.datalake.portal.dto.BarYData;
import com.idss.datalake.portal.dto.LineData;
import com.idss.datalake.portal.dto.PieData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 数据集市资产 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@Slf4j
@RestController
@RequestMapping("/api/datamart/overview")
public class DataMartOverviewController {

    @Autowired
    private IDataMartOverviewService martOverviewService;

    /**
     * 数据数量
     *
     * @return
     */
    @GetMapping("/martCountOverview")
    public ResultBean martCountOverview() {
        try {
            return ResultBean.success(martOverviewService.martCountOverview());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 数据集市类型
     *
     * @return
     */
    @GetMapping("/martType")
    public ResultBean martType() {
        try {
            List<MartTypeOverview> overviews = martOverviewService.martTypeOverview();
            return ResultBean.success(overviews);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 数据集市类型占比及订阅情况
     *
     * @return
     */
    @GetMapping("/proportion")
    public ResultBean proportion() {
        try {
            MartTypePercent percent = martOverviewService.martProportion();
            return ResultBean.success(percent);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @GetMapping("/proportion/open/{tenantId}")
    public ResultBean proportionOpen(@PathVariable("tenantId") Integer tenantId) {
        try {
            MartTypePercent percent = martOverviewService.martProportion(tenantId);
            return ResultBean.success(percent);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 集市排行top10
     *
     * @return
     */
    @GetMapping("/subscribe")
    public ResultBean subscribe() {
        try {
            BarYData barYData = martOverviewService.subscribe();
            return ResultBean.success(barYData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @GetMapping("/subscribe/open/{tenantId}")
    public ResultBean subscribeOpen(@PathVariable("tenantId") Integer tenantId) {
        try {
            BarYData barYData = martOverviewService.subscribe(tenantId);
            return ResultBean.success(barYData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 用户入驻总览及订阅排行
     *
     * @return
     */
    @GetMapping("/entry")
    public ResultBean entry() {
        try {
            MartSubscribe martSubscribe = martOverviewService.entry();
            return ResultBean.success(martSubscribe);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @GetMapping("/entry/open/{tenantId}")
    public ResultBean entryOpen(@PathVariable("tenantId") Integer tenantId) {
        try {
            MartSubscribe martSubscribe = martOverviewService.entry(tenantId);
            return ResultBean.success(martSubscribe);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 数据治理总览
     *
     * @return
     */
    @GetMapping("/qualityOverview")
    public ResultBean qualityOverview() {
        try {
            List<QualityOverview> overviews = martOverviewService.qualityOverview();
            return ResultBean.success(overviews);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 数据治理任务情况
     *
     * @return
     */
    @GetMapping("/qualityJob")
    public ResultBean qualityJob() {
        try {
            QualityJobInfo qualityJobInfo = martOverviewService.qualityJob();
            return ResultBean.success(qualityJobInfo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 数据治理任务数量趋势
     *
     * @return
     */
    @GetMapping("/jobTrend")
    public ResultBean jobTrend() {
        try {
            LineData jobTrend = martOverviewService.jobTrend();
            return ResultBean.success(jobTrend);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 采集任务汇总
     *
     * @return
     */
    @GetMapping("/etlSummary")
    public ResultBean etlSummary() {
        try {
            EtlSummary etlSummary = martOverviewService.etlSummary();
            return ResultBean.success(etlSummary);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @GetMapping("/diskPercent")
    public ResultBean diskPercent() {
        try {
            List<PieData> data = martOverviewService.diskPercent();
            return ResultBean.success(data);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 数据总条数写入趋势
     *
     * @return
     */
    @GetMapping("/writeTrend")
    public ResultBean writeTrend() {
        try {
            LineData lineData = martOverviewService.writeTrend();
            return ResultBean.success(lineData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * 数据采集任务详情
     *
     * @return
     */
    @PostMapping("/etlDetail")
    public BasePageResponse<List<DataMartOverviewResponse>> etlDetail(@RequestBody DataMartOverviewRequest dataMartOverviewRequest) {
        try {
            return martOverviewService.etlDetail(dataMartOverviewRequest);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    @GetMapping("/martSummaryStatistics")
    public ResultBean martSummaryStatistics() {
        try {
            return ResultBean.success(martOverviewService.martSummaryStatistics());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @GetMapping("/martSummaryStatistics/open/{tenantId}")
    public ResultBean martSummaryStatisticsOpen(@PathVariable("tenantId") Integer tenantId) {
        try {
            return ResultBean.success(martOverviewService.martSummaryStatistics(tenantId));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @GetMapping("/martPublishRankingTop")
    public ResultBean martPublishRankingTop() {
        try {
            return ResultBean.success(martOverviewService.martPublishRankingTop());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @GetMapping("/martPublishRankingTop/open/{tenantId}")
    public ResultBean martPublishRankingTopOpen(@PathVariable("tenantId") Integer tenantId) {
        try {
            return ResultBean.success(martOverviewService.martPublishRankingTop(tenantId));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @GetMapping("/martApprovalStatistics")
    public ResultBean martApprovalStatistics() {
        try {
            return ResultBean.success(martOverviewService.martApprovalStatistics());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @GetMapping("/martApprovalStatistics/open/{tenantId}")
    public ResultBean martApprovalStatisticsOpen(@PathVariable("tenantId") Integer tenantId) {
        try {
            return ResultBean.success(martOverviewService.martApprovalStatistics(tenantId));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }
}
