package com.idss.datalake.leak.utils;


import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 功 能： 业务共通Utils
 *
 * <AUTHOR>
 */
@Slf4j
public class CommonBusinessUtils {

    /**
     * @param up   分子
     * @param down 分母
     * @return 计算百分比，只保留整数位(四舍五入)
     */
    public static double getRate(double up, double down) {
        if (down == 0) {
            return 0;
        }
        return BigDecimal.valueOf(up / down * Constants.NUM_100).setScale(Constants.NUM_2,
                BigDecimal.ROUND_HALF_UP).doubleValue();
    }


}
