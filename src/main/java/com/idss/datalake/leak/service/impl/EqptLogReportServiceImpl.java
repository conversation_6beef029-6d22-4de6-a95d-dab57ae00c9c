package com.idss.datalake.leak.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.datalake.leak.mapper.EqptLogReportMapper;
import com.idss.datalake.leak.mapper.clickhouse.EqptLogReportChMapper;
import com.idss.datalake.leak.pojo.AggDoublePojo;
import com.idss.datalake.leak.pojo.AggLongPojo;
import com.idss.datalake.leak.pojo.EqptLogReportListRequestVo;
import com.idss.datalake.leak.pojo.KeyNameValue;
import com.idss.datalake.leak.pojo.MapPoint;
import com.idss.datalake.leak.pojo.StockCount;
import com.idss.datalake.leak.pojo.TrendCount;
import com.idss.datalake.leak.pojo.UebaDictEntity;
import com.idss.datalake.leak.service.IEqptLogReportService;
import com.idss.datalake.leak.service.UebaDictEntityService;
import com.idss.datalake.leak.utils.CommonBusinessUtils;
import com.idss.datalake.leak.utils.Constants;
import com.idss.datalake.leak.utils.DateFormatUtils;
import com.idss.radar.datasource.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import sun.net.util.IPAddressUtil;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 漏报分析service
 * @date 2023/5/93:33 PM
 */
@Service
@Slf4j
public class EqptLogReportServiceImpl implements IEqptLogReportService {


    @Autowired
    private EqptLogReportMapper mapper;

    @Autowired
    private EqptLogReportChMapper chMapper;


    @Autowired
    private UebaDictEntityService uebaDictEntityService;
    
    @Value("${eqpt.tenantId:}")
    private Integer eqptTenantId;

    @Override
    public IPage<Map<String, Object>> list(@RequestBody EqptLogReportListRequestVo query) {


        List<Integer> eqptBelongList = query.getEqptBelongList();
        Integer eqptBelong = 0;
        if (CollectionUtils.isNotEmpty(eqptBelongList)) {
            if (eqptBelongList.size() == 1) {
                eqptBelong = eqptBelongList.get(0);
            }
        }

        QueryWrapper<EqptLogReportListRequestVo> wrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(query.getBranchCode())) {
            wrapper.eq("branch_code", query.getBranchCode());
        }

        if (StringUtils.isNotBlank(query.getVendorName())) {
            wrapper.eq("vendor_name", query.getVendorName());
        }
        if (StringUtils.isNotBlank(query.getEqptType())) {
            wrapper.eq("eqpt_type", query.getEqptType());
        }



        wrapper.eq("collect_type", "kafka");

        wrapper.groupBy("branch_code", "vendor_name", "eqpt_type");

        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT,eqptTenantId);
        IPage<Map<String, Object>> page = mapper.findLogIpWithOutIp(new Page(query.getPageNum(), query.getPageSize()), wrapper);


        if (page != null && page.getTotal() > 0) {

            String timeSearch = DateFormatUtils.convertToString(new Date(Long.parseLong(query.getStartTime())),
                    DateFormatUtils.YYYYMMDDHHMMSS_HYPHEN) +
                    "~" + DateFormatUtils.convertToString(new Date(Long.parseLong(query.getEndTime())),
                    DateFormatUtils.YYYYMMDDHHMMSS_HYPHEN);
            Integer finalEqptBelong = eqptBelong;
            page.getRecords().parallelStream().forEach(item -> {


                String branchCode = (String) item.get("branchCode");
                String vendor = (String) item.get("vendor");
                String eqptType = (String) item.get("eqptType");

                DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT,eqptTenantId);
                List<String> ipList = mapper.findagzxList(branchCode, vendor, eqptType, finalEqptBelong);
                DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT,eqptTenantId);
                List<Long> flowIds = mapper.getKafkaSource();


                if (StringUtils.isNotEmpty(branchCode)) {
                    log.info("branch_code:{}", branchCode);
                    item.put("branchCodeName",
                            uebaDictEntityService.getDictValueByCode(Constants.XTY_BRANCH_CODE_LIST, branchCode,eqptTenantId
                            ));
                }
                if (StringUtils.isNotEmpty(vendor)) {
                    item.put("vendorName",
                            uebaDictEntityService.getDictValueByCode(Constants.XTY_VENDOR, vendor,eqptTenantId));
                }
                try {


                if (StringUtils.isNotEmpty(eqptType)) {
                    item.put("eqptTypeName",
                            uebaDictEntityService.getDictValueByCode(
                                    Constants.XTY_DEVICE_TYPE, eqptType,eqptTenantId));
                }

                }catch (Exception e){
                    e.printStackTrace();
                    log.error("eqptType:{}",eqptType);
                }
                Map<String, Double> currentSum =
                        getSumResMap(Long.parseLong(query.getStartTime()), Long.parseLong(query.getEndTime()), branchCode, vendor, eqptType, ipList);
                item.put("sendCount", currentSum.get("warn_toal_long"));
                item.put("recevieCount", currentSum.get("total_long"));
                item.put("recevieRate",
                        getSubRate(Double.parseDouble(currentSum.get("warn_toal_long").toString()), Double.parseDouble(currentSum.get("total_long").toString())) + "%");


                Map<String, Object> queryMap = new HashMap<>();
                queryMap.put("vendor", vendor);
                queryMap.put("branch_code", branchCode);
                queryMap.put("eqpt_type", getEqptDeviceTypeByEqptType(eqptType));
                queryMap.put("eqpt_belong", finalEqptBelong);
                queryMap.put("start_time", Long.parseLong(query.getStartTime()));
                queryMap.put("end_time", Long.parseLong(query.getEndTime()));
                queryMap.put("ips", ipList);

                Long stockCount = getStockCount(queryMap, flowIds, ipList);

                item.put("stockCount", stockCount);
                item.put("stockRate", getSubRate(Double.parseDouble(currentSum.get("total_long").toString()), stockCount.doubleValue()) + "%");
                item.put("timeSearch", timeSearch);
            });
        }


        return page;
    }


    private Map<String, Long> getStockCount(String vendor, String branchCode, String eqptType, Long startTime, Long endTime, List<Long> flowIds,String eqptIp) {

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("vendor", vendor);
        queryMap.put("branch_code", branchCode);
        queryMap.put("eqpt_type", getEqptDeviceTypeByEqptType(eqptType));
        queryMap.put("start_time", startTime);
        queryMap.put("end_time", endTime);

        List<String> ips = new ArrayList<>();
        ips.add(eqptIp);
        queryMap.put("ips",ips);
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE,eqptTenantId);
        List<StockCount> list = chMapper.getLogCount(queryMap);
        Long kafkaCount = 0l;
        Long noKakfaCount = 0l;

        for (StockCount stockCount : list) {
            if (flowIds.contains(stockCount.getFlowId())) {
                kafkaCount += stockCount.getCount();
            } else {
                noKakfaCount += stockCount.getCount();
            }
        }

        Map<String, Long> returnMap = new HashMap<>();
        returnMap.put("kafkaCount", kafkaCount);
        returnMap.put("noKafkaCount", noKakfaCount);
        returnMap.put("count", kafkaCount + noKakfaCount);
        return returnMap;

    }


    private Long getStockCount(Map<String, Object> query, List<Long> flowIds, List<String> ips) {


        if (ips == null || ips.size() == 0) {
            return 0L;
        }
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE,eqptTenantId);
        List<StockCount> list = chMapper.getLogCount(query);
        Long count = 0L;
        for (StockCount stockCount : list) {
            if (flowIds.contains(stockCount.getFlowId())) {
                count += stockCount.getCount();
            }
        }
        return count;


    }


    private List<AggLongPojo> getLogByEqptIp(Long startTime, Long endTime, String branchCode, String vendor,
                                             String eqptType, List<Long> flowIds, List<String> ips, String eqptIp, Integer finalEqptBelong) {


        if (StringUtils.isNotBlank(eqptIp)) {
            ips.clear();
            ips.add(eqptIp);
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("vendor", vendor);
        queryMap.put("branch_code", branchCode);
        queryMap.put("eqpt_type", getEqptDeviceTypeByEqptType(eqptType));
        queryMap.put("eqpt_belong", finalEqptBelong);
        queryMap.put("start_time", startTime);
        queryMap.put("end_time", endTime);
        queryMap.put("ips", ips);
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE,eqptTenantId);
        List<StockCount> list = chMapper.getLogCountByEqptIp(queryMap);

        Map<String, List<StockCount>> maps = list.stream().collect(Collectors.groupingBy(StockCount::getEqptIp));

        List<AggLongPojo> aggLongPojos = new ArrayList<>();


        for (String ip : maps.keySet()) {
            AggLongPojo pojo = new AggLongPojo();
            pojo.setKey(ip);
            pojo.setName(ip);
            pojo.setValue(0l);
            aggLongPojos.add(pojo);
        }


        for (AggLongPojo pojo : aggLongPojos) {
            for (StockCount count : list) {
                if (flowIds.contains(count.getFlowId()) && pojo.getKey().equals(count.getEqptIp())) {
                    pojo.setValue(pojo.getValue() + count.getCount());
                }
            }
        }


        return aggLongPojos;
    }


    /**
     * 根据设备类型返回要查询的原始日志类型
     *
     * @param eqptType String
     * @return String
     */
    private String getEqptDeviceTypeByEqptType(String eqptType) {
        if(StringUtils.isBlank(eqptType)){
            return null;
        }
        String eqptDeviceType = "/IDS/Network";
        if ("APT".equals(eqptType)) {
            eqptDeviceType = "/NetworkEquipment/NetFlow";
        } else if ("WAF".equals(eqptType)) {
            eqptDeviceType = "/IDS/Network/WAF";
        } else if ("DDOS".equals(eqptType)) {
            eqptDeviceType = "/DDOS";
        } else if ("HONEYPOT".equals(eqptType)) {
            eqptDeviceType = "/IDS/Honeypot";
        } else if ("HOST_PRO".equals(eqptType)) {
            eqptDeviceType = "/IDS/Host";
        } else if ("WEB_PRO".equals(eqptType)) {
            eqptDeviceType = "/WebIntegrity";
        }
        return eqptDeviceType;
    }


    /**
     * 计算漏报率，入库差异率
     *
     * @param total    double
     * @param subTotal double
     * @return double
     */
    private double getSubRate(Double total, Double subTotal) {
        double receiveRate;
        if (total == Constants.NUM_0) {
            return 0.00D;
        } else {
            BigDecimal bg = BigDecimal.valueOf(((total - subTotal) / total) * Constants.NUM_100);
            receiveRate = bg.setScale(Constants.NUM_2, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        return receiveRate;
    }


    /**
     * 获取发送量及接收量统计结果(发送量，接收量同时获取，一览列表使用)
     *
     * @param startTime  long
     * @param endTime    long
     * @param branchCode String
     * @param vendor     String
     * @param eqptType   String
     * @return Map
     */
    private Map<String, Double> getSumResMap(Long startTime, Long endTime, String branchCode, String vendor,
                                             String eqptType, List<String> ips) {

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("ips", ips);
        queryMap.put("vendor_name", vendor);
        queryMap.put("branch_code", branchCode);
        queryMap.put("eqpt_type", eqptType);
        queryMap.put("begin_time", startTime);
        queryMap.put("end_time", endTime);

        //默认返回值设置
        Map<String, Double> dfSumMap = new HashMap<>(Constants.NUM_16);
        dfSumMap.put("total_long", 0D);
        dfSumMap.put("warn_toal_long", 0D);

        //输入条件拼接查询条件

        if (ips.size() < 1) {
            return dfSumMap;
        }

        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT,eqptTenantId);
        Map<String, Object> sumMap = mapper.getSum(queryMap);
        if (sumMap ==null || Constants.NUM_0 == sumMap.size()) {
            return dfSumMap;
        }

        dfSumMap.put("total_long", Double.parseDouble(sumMap.get("totalLong").toString()));
        dfSumMap.put("warn_toal_long", Double.parseDouble(sumMap.get("warnToalLong").toString()));
        return dfSumMap;
    }


    @Override
    public Map<String, Object> receive(Map<String, Object> params) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("receive_time", Long.toString(System.currentTimeMillis()));
        try {
            Map<String, Object> receive = convertReceive(params);
            DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT,eqptTenantId);
            mapper.insertXtyLogReport(receive);
            List<Map<String, Object>> existData = mapper.findExistLogIp(params);
            if (existData == null || existData.isEmpty()) {
                if (mapper.updateEqptLogIp(params) == 0) {
                    mapper.insertReportIp(params);
                }
            }
            resultMap.put("code", "200");
        } catch (Exception e) {
            resultMap.put("code", "400");
            resultMap.put("Message", e.getMessage());
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> summary(Map<String, Object> params) {


        String branchCode = params.get("branchCode") != null ? params.get("branchCode").toString() : "";
        String branchCodeName = "全部";
        String vendor = params.get("vendorName") != null ? params.get("vendorName").toString() : "";
        String vendorName = "全部";
        String eqptType = params.get("eqptType") != null ? params.get("eqptType").toString() : "";
        String eqptTypeName = "全部";
        List<Integer> eqptBelongList = (List<Integer>) params.get("eqptBelongList");


        Long startTime = (Long) params.get("startTime");
        Long endTime = (Long) params.get("endTime");
        Integer eqptBelong = 0;
        if (CollectionUtils.isNotEmpty(eqptBelongList)) {
            if (eqptBelongList.size() == 1) {
                eqptBelong = eqptBelongList.get(0);
            }
        }

        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT,eqptTenantId);
        if (StringUtils.isNotEmpty(branchCode)) {
            branchCodeName = uebaDictEntityService.getDictValueByCode(Constants.XTY_BRANCH_CODE_LIST, branchCode,eqptTenantId);
        }
        if (StringUtils.isNotEmpty(vendor)) {
            vendorName = uebaDictEntityService.getDictValueByCode(Constants.XTY_VENDOR, vendor,eqptTenantId);
        }
        if (StringUtils.isNotEmpty(eqptType)) {
            eqptTypeName = uebaDictEntityService.getDictValueByCode(Constants.XTY_DEVICE_TYPE, eqptType,eqptTenantId);
        }


        List<String> ipList = mapper.findagzxList(branchCode, vendor, eqptType, eqptBelong);

        Map<String, Double> sumMap = getSumResMap(startTime, endTime, branchCode, vendor, eqptType, ipList);

        //获取统计后结果
        Double totalLong = sumMap.get("total_long");
        Double warnTotalLong = sumMap.get("warn_toal_long");

        List<Long> flowIds = mapper.getKafkaSource();

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("vendor", vendor);
        queryMap.put("branch_code", branchCode);
        queryMap.put("eqpt_type", getEqptDeviceTypeByEqptType(eqptType));
        queryMap.put("eqpt_belong", eqptBelong);
        queryMap.put("start_time", startTime);
        queryMap.put("end_time", endTime);
        queryMap.put("ips", ipList);
        Long stockCnt = getStockCount(queryMap, flowIds, ipList);

        return setSummaryResult(branchCodeName, vendorName, eqptTypeName, warnTotalLong, totalLong, stockCnt,
                startTime, endTime);
    }

    @Override
    public List<KeyNameValue<List<KeyNameValue<Double>>>> trend(EqptLogReportListRequestVo vo) {

        List<Integer> eqptBelongList = vo.getEqptBelongList();
        Integer eqptBelong = 0;
        if (CollectionUtils.isNotEmpty(eqptBelongList)) {
            if (eqptBelongList.size() == 1) {
                eqptBelong = eqptBelongList.get(0);
            }
        }
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT,eqptTenantId);
        List<Long> flowIds = mapper.getKafkaSource();
        List<String> ips = mapper.findagzxList(vo.getBranchCode(), vo.getVendorName(), vo.getEqptType(), eqptBelong);
        List<TrendCount> trendCounts = getTrendCount(Long.parseLong(vo.getStartTime()), Long.parseLong(vo.getEndTime()), vo.getBranchCode(), vo.getVendorName(), vo.getEqptType(), ips);
        getLogTrendCount(Long.parseLong(vo.getStartTime()), Long.parseLong(vo.getEndTime()), vo.getBranchCode(), vo.getVendorName(), vo.getEqptType(), ips, eqptBelong, trendCounts, flowIds);

        List<KeyNameValue<Double>> missRate = new ArrayList<>();
        //计算漏报率
        for (int i = 0; i < trendCounts.size(); i++) {
            KeyNameValue<Double> keyNameValue = new KeyNameValue<>();
            keyNameValue.setKey(Long.toString(trendCounts.get(i).getDateTime()));
            keyNameValue.setName(trendCounts.get(i).getDateStr());
            keyNameValue.setValue(getSubRate(trendCounts.get(i).getWarnTotol().doubleValue(), trendCounts.get(i).getTotol().doubleValue()));
            missRate.add(keyNameValue);
        }
        //计算入库差异率
        List<KeyNameValue<Double>> sinkRate = new ArrayList<>();
        for (int i = 0; i < trendCounts.size(); i++) {
            KeyNameValue<Double> keyNameValue = new KeyNameValue<>();
            keyNameValue.setKey(Long.toString(trendCounts.get(i).getDateTime()));
            keyNameValue.setName(trendCounts.get(i).getDateStr());
            keyNameValue.setValue(getSubRate(trendCounts.get(i).getTotol().doubleValue(), trendCounts.get(i).getLogTotol().doubleValue()));
            sinkRate.add(keyNameValue);
        }


        KeyNameValue<List<KeyNameValue<Double>>> missTrend = new KeyNameValue<>();
        missTrend.setKey("漏报率");
        missTrend.setName("漏报率");
        missTrend.setValue(missRate);
        KeyNameValue<List<KeyNameValue<Double>>> sinkTrend = new KeyNameValue<>();
        sinkTrend.setKey("入库差异率");
        sinkTrend.setName("入库差异率");
        sinkTrend.setValue(sinkRate);

        return Arrays.asList(missTrend, sinkTrend);

    }

    @Override
    public IPage<Map<String, Object>> detail(EqptLogReportListRequestVo query) {
        //获取输入参数

        String timeSearch = DateFormatUtils.convertToString(new Date(Long.parseLong(query.getStartTime())),
                DateFormatUtils.YYYYMMDDHHMMSS_HYPHEN) + "~" +
                DateFormatUtils.convertToString(new Date(Long.parseLong(query.getEndTime())), DateFormatUtils.YYYYMMDDHHMMSS_HYPHEN);

        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT,eqptTenantId);

        String branchCode = query.getBranchCode();
        String branchCodeName =
                uebaDictEntityService.getDictValueByCode(Constants.XTY_BRANCH_CODE_LIST, branchCode,eqptTenantId);
        String vendor = query.getVendorName();
        String vendorName = uebaDictEntityService.getDictValueByCode(Constants.XTY_VENDOR, vendor,eqptTenantId);
        String eqptType = query.getEqptType();
        String eqptTypeName =
                uebaDictEntityService.getDictValueByCode(Constants.XTY_DEVICE_TYPE, eqptType,eqptTenantId);
        String eqptIp = query.getEqptIp();
        Integer eqptBelong = 0;
        if (CollectionUtils.isNotEmpty(query.getEqptBelongList())) {
            if (query.getEqptBelongList().size() == 1) {
                eqptBelong = query.getEqptBelongList().get(0);
            }
        }

        QueryWrapper<EqptLogReportListRequestVo> wrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(query.getBranchCode())) {
            wrapper.eq("elri.branch_code", query.getBranchCode());
        }

        if (StringUtils.isNotBlank(query.getVendorName())) {
            wrapper.eq("elri.vendor_name", query.getVendorName());
        }
        if (StringUtils.isNotBlank(query.getEqptType())) {
            wrapper.eq("elri.eqpt_type", query.getEqptType());
        }

        if (StringUtils.isNotBlank(query.getEqptIp())) {
            wrapper.eq("elri.eqpt_ip", query.getEqptIp());
        }

        if (1 == eqptBelong) {
            wrapper.eq("eela.eqpt_belong", "安全管理中心");
        } else if (2 == eqptBelong) {
            wrapper.ne("eela.eqpt_belong", "安全管理中心");
        }


        wrapper.eq("collect_type", "kafka");

        wrapper.groupBy("branchCode", "vendor", "eqptType","eqptIp","collectType");

        IPage<Map<String, Object>> pageData = mapper.findLogIpByCondition(new Page(query.getPageNum(), query.getPageSize()), wrapper);


        if (pageData != null && !pageData.getRecords().isEmpty()) {

            List<Long> flowIds = mapper.getKafkaSource();
            List<Map<String, Object>> resList = pageData.getRecords();
            //设备IP集合
            List<String> eqptIpList =
                    resList.stream().map(item -> item.get("eqptIp").toString()).collect(Collectors.toList());

            Map<String, List<AggDoublePojo>> beginMaps = getSumSingleResMap(Long.parseLong(query.getStartTime()), Long.parseLong(query.getEndTime()), branchCode, vendor, eqptType,
                    eqptIp, eqptIpList);

            //按照指定IP范围查询发送量结果集
            List<AggDoublePojo> warnTotalList = beginMaps.get("warn");
            List<AggDoublePojo> totalList = beginMaps.get("totol");


            List<AggLongPojo> stockList = getLogByEqptIp(Long.parseLong(query.getStartTime()), Long.parseLong(query.getEndTime()), branchCode, vendor, eqptType, flowIds,
                    eqptIpList, eqptIp, eqptBelong);
            //计算时间周期
            long current = System.currentTimeMillis();
            current -= current % (Constants.NUM_3600 * Constants.NUM_1000);
            long preHour = current - Constants.NUM_3600 * Constants.NUM_1000;
            //上一个小时发送量统计
            Map<String, List<AggDoublePojo>> currentMaps = getSumSingleResMap(preHour, current - 1, branchCode, vendor, eqptType, eqptIp, eqptIpList);
            //昨日同时段发送量统计
            Map<String, List<AggDoublePojo>> preDayMaps = getSumSingleResMap(preHour -
                            Constants.NUM_3600 * Constants.NUM_1000 * Constants.NUM_24,
                    current - Constants.NUM_3600 * Constants.NUM_1000 * Constants.NUM_24 - 1,
                    branchCode, vendor, eqptType, eqptIp, eqptIpList);
            //上一周期发送量统计
            Map<String, List<AggDoublePojo>> preCircleMaps = getSumSingleResMap(
                    preHour - Constants.NUM_3600 * Constants.NUM_1000,
                    current - Constants.NUM_3600 * Constants.NUM_1000 - 1, branchCode,
                    vendor, eqptType, eqptIp, eqptIpList);
            List<AggDoublePojo> currentWarnTotalList =
                    currentMaps.get("warn");
            List<AggDoublePojo> preDayWarnTotalList =
                    preDayMaps.get("warn");
            List<AggDoublePojo> preCircleWarnTotalList =
                    preCircleMaps.get("warn");


            List<AggDoublePojo> currentTotalList =
                    currentMaps.get("totol");
            List<AggDoublePojo> preDayTotalList =
                    preDayMaps.get("totol");
            List<AggDoublePojo> preCircleTotalList =
                    preCircleMaps.get("totol");


            //循环设置返回值内容
            for (Map<String, Object> item : resList) {
                item.put("branchCodeName", branchCodeName);
                item.put("vendorName", vendorName);
                item.put("eqptTypeName", eqptTypeName);
                item.put("timeSearch", timeSearch);
                //接入方式
                String collectType = item.get("collectType").toString();
                //当前查询的设备IP
                String singleEqptIp = item.get("eqptIp").toString();
                //判断接入方式是否有值

                //设置接入方式
                item.put("collectType", collectType);
                double receiveCount = getReceiveCount(warnTotalList, totalList, item, singleEqptIp);
                Long stockCount = getResultFromListLong(stockList, singleEqptIp);
                item.put("stockCount", stockCount);
                item.put("stockRate", getSubRate(receiveCount, stockCount.doubleValue()) + "%");
                //设置同比环比计算结果
                item.putAll(getOtherRate(currentWarnTotalList, preDayWarnTotalList, preCircleWarnTotalList,
                        currentTotalList, preDayTotalList, preCircleTotalList, singleEqptIp));
            }
        }
        return pageData;
    }


    private double getReceiveCount(List<AggDoublePojo> warnTotalList, List<AggDoublePojo> totalList, Map<String, Object> item,
                                   String singleEqptIp) {
        double sendCount = getResultFromListDouble(warnTotalList, singleEqptIp);
        double receiveCount = getResultFromListDouble(totalList, singleEqptIp);
        item.put("sendCount", sendCount);
        item.put("recevieCount", receiveCount);
        item.put("recevieRate", getSubRate(sendCount, receiveCount) + "%");
        return receiveCount;
    }


    /**
     * 根据指定IP地址查询结果集返回查询结果
     *
     * @param resDoubleList List
     * @param ip            String
     * @return double
     */
    private double getResultFromListDouble(List<AggDoublePojo> resDoubleList, String ip) {
        double result = 0D;
        for (AggDoublePojo obj : resDoubleList) {
            if (ip.equals(obj.getKey())) {
                result = obj.getValue();
                break;
            }
        }
        return result;
    }

    /**
     * 根据指定IP地址查询结果集返回查询结果
     *
     * @param resLongList List
     * @param ip          String
     * @return long
     */
    private long getResultFromListLong(List<AggLongPojo> resLongList, String ip) {
        long result = 0L;
        for (AggLongPojo obj : resLongList) {
            if (ip.equals(obj.getKey())) {
                result = obj.getValue();
                break;
            }
        }
        return result;
    }


    /**
     * 同比及环比计算
     *
     * @param currentWarnTotalList   List
     * @param preDayWarnTotalList    List
     * @param preCircleWarnTotalList List
     * @param currentTotalList       List
     * @param preDayTotalList        List
     * @param preCircleTotalList     List
     * @param eqptIp                 String
     * @return Map
     */
    private Map<String, Object> getOtherRate(List<AggDoublePojo> currentWarnTotalList, List<AggDoublePojo> preDayWarnTotalList,
                                             List<AggDoublePojo> preCircleWarnTotalList, List<AggDoublePojo> currentTotalList,
                                             List<AggDoublePojo> preDayTotalList, List<AggDoublePojo> preCircleTotalList,
                                             String eqptIp) {
        double currentRate = getSubRate(getResultFromListDouble(currentWarnTotalList, eqptIp),
                getResultFromListDouble(currentTotalList, eqptIp));
        double preDayRate = getSubRate(getResultFromListDouble(preDayWarnTotalList, eqptIp),
                getResultFromListDouble(preDayTotalList, eqptIp));
        double preCircleRate = getSubRate(getResultFromListDouble(preCircleWarnTotalList, eqptIp),
                getResultFromListDouble(preCircleTotalList, eqptIp));
        Map<String, Object> map = new HashMap<>(Constants.NUM_2);
        map.put("sameRate",
                Arrays.asList(new MapPoint<Double>().setName("当前").setValue(currentRate),
                        new MapPoint<Double>().setName("昨日值").setValue(preDayRate),
                        new MapPoint<Double>().setName("比较值").setValue(currentRate - preDayRate)));
        map.put("ringRate",
                Arrays.asList(new MapPoint<Double>().setName("当前").setValue(currentRate),
                        new MapPoint<Double>().setName("上一周期").setValue(preCircleRate),
                        new MapPoint<Double>().setName("比较值").setValue(currentRate - preCircleRate)));
        return map;
    }

    /**
     * 获取发送量及接收量统计结果(仅发送量或是接收量，详细页面使用)
     *
     * @param startTime  long
     * @param endTime    long
     * @param branchCode String
     * @param vendor     String
     * @param eqptType   String
     * @param eqptIp     String
     * @param eqptIpList List
     * @return List
     */
    private Map<String, List<AggDoublePojo>> getSumSingleResMap(Long startTime, Long endTime, String branchCode, String vendor,
                                                                String eqptType, String eqptIp, List<String> eqptIpList
    ) {
        if (StringUtils.isNotBlank(eqptIp)) {
            eqptIpList.clear();
            eqptIpList.add(eqptIp);
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("ips", eqptIpList);
        queryMap.put("vendor_name", vendor);
        queryMap.put("branch_code", branchCode);
        queryMap.put("eqpt_type", eqptType);
        queryMap.put("begin_time", startTime);
        queryMap.put("end_time", endTime);
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT,eqptTenantId);
        List<TrendCount> list = mapper.getSumByDeatil(queryMap);
        List<AggDoublePojo> warnList = new ArrayList<>();
        List<AggDoublePojo> totolList = new ArrayList<>();
        for (TrendCount trendCount : list) {
            warnList.add(new AggDoublePojo(trendCount.getEqptIp(), trendCount.getSumWarnTotol()));
            totolList.add(new AggDoublePojo(trendCount.getEqptIp(), trendCount.getSumTotol()));
        }

        Map<String, List<AggDoublePojo>> map = new HashMap<>();
        map.put("warn", warnList);
        map.put("totol", totolList);
        return map;

    }

    private void getLogTrendCount(Long startTime, Long endTime, String branchCode, String vendor,
                                  String eqptType, List<String> ips, Integer eqptBelong, List<TrendCount> trendCounts, List<Long> flowIds) {
        Map<String, Object> queryMap = new HashMap<>();

        if (ips == null || ips.size() == 0) {
            return;
        }

        queryMap.put("vendor", vendor);
        queryMap.put("branch_code", branchCode);
        queryMap.put("eqpt_type", getEqptDeviceTypeByEqptType(eqptType));
        queryMap.put("eqpt_belong", eqptBelong);
        queryMap.put("start_time", startTime);
        queryMap.put("end_time", endTime);
        queryMap.put("ips", ips);

        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE,eqptTenantId);
        List<StockCount> countList = chMapper.getLogTrendCount(queryMap);
        Map<Long, List<StockCount>> maps = countList.stream().collect(Collectors.groupingBy(StockCount::getFlowId));

        maps.keySet().forEach(flowId -> {
            if (flowIds.contains(flowId)) {
                List<StockCount> counts = maps.get(flowId);
                for (TrendCount trendCount : trendCounts) {
                    for (StockCount stockCount : counts) {
                        if (trendCount.getDateStrmmmm().equals(stockCount.getDateStr())) {
                            trendCount.setLogTotol(trendCount.getLogTotol() + stockCount.getCount());
                        }
                    }
                }
            }

        });


    }


    private List<TrendCount> getTrendCount(Long startTime, Long endTime, String branchCode, String vendor,
                                           String eqptType, List<String> ips) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("ips", ips);
        queryMap.put("vendor_name", vendor);
        queryMap.put("branch_code", branchCode);
        queryMap.put("eqpt_type", eqptType);
        queryMap.put("begin_time", startTime);
        queryMap.put("end_time", endTime);


        List<TrendCount> list = getHourBetween(new Date(startTime), new Date(endTime));


        if (ips.size() < 1) {
            return list;
        }
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT,eqptTenantId);
        List<TrendCount> dataTrendCounts = mapper.getTrendCount(queryMap);

        for (TrendCount data : dataTrendCounts) {
            for (TrendCount returnData : list) {
                if (returnData.getDateStr().equals(data.getDateStr())) {
                    returnData.setTotol(data.getTotol());
                    returnData.setWarnTotol(data.getWarnTotol());
                }
            }
        }
        return list;
    }


    /**
     * @return java.util.List<java.lang.String>
     * @description
     * <AUTHOR>
     * @date 2023/5/16 2:36 PM
     */
    public static List<TrendCount> getHourBetween(Date bdate, Date edate) {
        List<TrendCount> list = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();
        min.setTime(bdate);
        max.setTime(edate);

        Calendar curr = min;
        int count = 0;
        while (curr.before(max)) {
            if (count != 0) {
                TrendCount trendCount = new TrendCount();
                trendCount.setDateStr(format.format(curr.getTime()));
                trendCount.setDateStrmmmm(dateFormat.format(curr.getTime()));
                trendCount.setLogTotol(0L);
                trendCount.setWarnTotol(0L);
                trendCount.setTotol(0L);
                trendCount.setDateTime(curr.getTime().getTime());
                list.add(trendCount);
            }

            curr.add(Calendar.HOUR, 1);
            count++;

        }

        return list;

    }

    /**
     * 设置漏报分析统计返回结果
     *
     * @param branchCodeName String
     * @param vendorName     String
     * @param eqptTypeName   String
     * @param warnTotalLong  double
     * @param totalLong      double
     * @param stockCnt       double
     * @return Map
     */
    private Map<String, Object> setSummaryResult(String branchCodeName, String vendorName, String eqptTypeName,
                                                 Double warnTotalLong, Double totalLong, Long stockCnt, Long startTime, Long endTime) {
        List<Map<String, Object>> leftList = new ArrayList<>();
        Map<String, Object> branchCodeMap = new HashMap<>(Constants.NUM_16);
        branchCodeMap.put("name", "资源池");
        branchCodeMap.put("value", branchCodeName);
        leftList.add(branchCodeMap);
        Map<String, Object> vendorNameMap = new HashMap<>(Constants.NUM_16);
        vendorNameMap.put("name", "厂商");
        vendorNameMap.put("value", vendorName);
        leftList.add(vendorNameMap);
        Map<String, Object> eqptTypeMap = new HashMap<>(Constants.NUM_16);
        eqptTypeMap.put("name", "设备类型");
        eqptTypeMap.put("value", eqptTypeName);
        leftList.add(eqptTypeMap);
        Map<String, Object> timeMap = new HashMap<>(Constants.NUM_16);
        timeMap.put("name", "时间");
        timeMap.put("value", DateFormatUtils.convertToString(new Date(startTime),
                DateFormatUtils.YYYYMMDDHHMMSS_HYPHEN).concat(" ~ ")
                .concat(DateFormatUtils.convertToString(new Date(endTime),
                        DateFormatUtils.YYYYMMDDHHMMSS_HYPHEN)));
        leftList.add(timeMap);

        List<Map<String, Object>> topList = new ArrayList<>();
        Map<String, Object> sendMap = new HashMap<>(Constants.NUM_16);
        sendMap.put("name", "发送量");
        sendMap.put("value", warnTotalLong);
        topList.add(sendMap);

        Map<String, Object> recMap = new HashMap<>(Constants.NUM_16);
        recMap.put("name", "接收量");
        recMap.put("value", totalLong);
        topList.add(recMap);

        Map<String, Object> stockMap = new HashMap<>(Constants.NUM_16);
        stockMap.put("name", "入库量");
        stockMap.put("value", stockCnt);
        topList.add(stockMap);

        List<Map<String, Object>> bottomList = new ArrayList<>();
        Map<String, Object> recRate = new HashMap<>(Constants.NUM_16);
        recRate.put("name", "漏报率");
        recRate.put("value", getSubRate(warnTotalLong, totalLong));
        bottomList.add(recRate);

        Map<String, Object> stockRate = new HashMap<>(Constants.NUM_16);
        stockRate.put("name", "入库差异率");
        stockRate.put("value", getSubRate(totalLong, stockCnt.doubleValue()));
        bottomList.add(stockRate);

        Map<String, Object> resMap = new HashMap<>(Constants.NUM_16);
        resMap.put("left", leftList);
        resMap.put("top", topList);
        resMap.put("bottom", bottomList);
        return resMap;
    }


    /**
     * 安全设备日志数量统计采集接口(数据校验处理)
     *
     * @param params Map
     * @return Map
     */
    private Map<String, Object> convertReceive(Map<String, Object> params) throws RuntimeException {
        Map<String, Object> map = new HashMap<>(Constants.NUM_16);
        String timeId = (String) params.get("time_id");
        if (StringUtils.isEmpty(timeId) || !StringUtils.isNumeric(timeId) || timeId.compareTo("01") < 0 ||
                timeId.compareTo("24") > 0 || timeId.length() != Constants.NUM_2) {
            throw new RuntimeException("time_id必须在01-24之间");
        }
        map.put("time_id", timeId);
        List<UebaDictEntity> vendors = uebaDictEntityService.getDictEntryList(Constants.XTY_VENDOR,eqptTenantId);
        Set<String> vendorSet = vendors.stream()
                .map(UebaDictEntity::getKeyCode).collect(Collectors.toSet());
        String vendorName = (String) params.get("vendor_name");
        if (StringUtils.isEmpty(vendorName) || !vendorSet.contains(vendorName)) {
            throw new RuntimeException("vendor_name必须在指定范围内！");
        }
        map.put("vendor_name", vendorName);

        List<UebaDictEntity> deviceTypes = uebaDictEntityService.getDictEntryList(Constants.XTY_DEVICE_TYPE,eqptTenantId);
        Set<String> eqptSet = deviceTypes.stream()
                .map(UebaDictEntity::getKeyCode).collect(Collectors.toSet());
        String eqptType = (String) params.get("eqpt_type");
        if (StringUtils.isEmpty(eqptType) || !eqptSet.contains(eqptType)) {
            throw new RuntimeException("eqpt_type必须在指定范围内！");
        }
        map.put("eqpt_type", eqptType);
        Integer total = (Integer) params.get("total");
        if (total == null) {
            throw new RuntimeException("total必须！");
        }
        map.put("total_long", total);

        Integer warnToal = (Integer) params.get("warn_toal");
        if (warnToal == null) {
            throw new RuntimeException("warn_toal必须！");
        }
        map.put("warn_toal_long", warnToal);

        String eqptIp = (String) params.get("eqpt_ip");
        try {
            if (!IPAddressUtil.isIPv4LiteralAddress(eqptIp)) {
                throw new RuntimeException("eqpt_ip必须为正确的ipv4格式！");
            }
        } catch (Exception e) {
            throw new RuntimeException("eqpt_ip必须为正确的ipv4格式！");
        }
        map.put("eqpt_ip", eqptIp);

        String branchCode = (String) params.get("branch_code");

        List<UebaDictEntity> branchCodes = uebaDictEntityService.getDictEntryList(Constants.XTY_BRANCH_CODE_LIST,eqptTenantId);
        Set<String> branchSet = branchCodes.stream()
                .map(UebaDictEntity::getKeyCode).collect(Collectors.toSet());
        if (StringUtils.isEmpty(branchCode) || !branchSet.contains(branchCode)) {
            throw new RuntimeException("branch_code必须在指定范围内！");
        }
        map.put("branch_code", branchCode);


        String sendTime = (String) params.get("send_time");
        String sCreateTime = "";
        Long lCreateTime = 0L;
        try {
            Date date = DateFormatUtils.convertToDate(sendTime, DateFormatUtils.YYMMDDHHMMSS_SLASH);
            long lSendTime = date.getTime();
            map.put("send_time", lSendTime);
            int iTimeId = Integer.parseInt(timeId) - Constants.NUM_1;
            if (iTimeId == Constants.NUM_23) {
                //如果是11-12点的数据,先把时间往前推一个钟头(防止跨天）
                lSendTime -= Constants.NUM_3600 * Constants.NUM_1000;
            }
            sCreateTime = DateFormatUtils.convertToString(new Date(lSendTime), DateFormatUtils.YYYYMMDD_HYPHEN);
            sCreateTime = sCreateTime.concat(" ").concat(StringUtils.right("0" + iTimeId, 2)).concat(":00:00");
            lCreateTime = DateFormatUtils.convertToDate(sCreateTime, DateFormatUtils.YYMMDDHHMMSS_SLASH).getTime();
            map.put("create_time", lCreateTime);
        } catch (ParseException e) {
            throw new RuntimeException("send_time必须为yy-MM-dd HH:mm:ss格式！");
        }

        map.put("send_count", warnToal);
        map.put("send_success_count", total);

        long endTime = lCreateTime + Constants.NUM_3600 * Constants.NUM_1000 - 1;
        List<Long> flowIds = mapper.getKafkaSource();
        Map<String, Long> countMaps = getStockCount(vendorName, branchCode, eqptType, lCreateTime, endTime, flowIds, eqptIp);

        Integer kafkaNum = countMaps.get("kafkaCount").intValue();
        Integer stockCount = countMaps.get("count").intValue();
        Integer noKafkaStockCount = countMaps.get("noKafkaCount").intValue();

        map.put("receive_count", kafkaNum);
        map.put("miss_rate", CommonBusinessUtils.getRate(warnToal - kafkaNum, kafkaNum));
        map.put("stock_count", stockCount);
        map.put("nokafka_stock_count", noKafkaStockCount);
        map.put("stock_rate", CommonBusinessUtils.getRate(stockCount, kafkaNum + noKafkaStockCount));
        map.put("raw_log", JSON.toJSONString(params));
        map.put("id", UUID.randomUUID().toString());


        if(kafkaNum > 0 ){
            params.put("collect_type", "kafka");
            map.put("collect_type" , "kafka");
        }else{
            params.put("collect_type", "syslog");
            map.put("collect_type", "syslog");
        }


        return map;
    }


    /**
     * @param up   分子
     * @param down 分母
     * @return 计算百分比，只保留整数位(四舍五入)
     */
    private Double getRate(Double up, Double down) {
        if (down == 0) {
            return 0.0;
        }
        return BigDecimal.valueOf(up / down * Constants.NUM_100).setScale(Constants.NUM_2,
                BigDecimal.ROUND_HALF_UP).doubleValue();
    }
}
