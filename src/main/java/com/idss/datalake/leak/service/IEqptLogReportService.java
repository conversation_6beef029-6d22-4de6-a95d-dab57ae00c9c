package com.idss.datalake.leak.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.idss.datalake.leak.pojo.EqptLogReportListRequestVo;
import com.idss.datalake.leak.pojo.KeyNameValue;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/5/93:32 PM
 */
public interface IEqptLogReportService {



    /**
     * @description 列表查询
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @date 2023/5/9 3:37 PM
     */
    IPage<Map<String, Object>> list(EqptLogReportListRequestVo query);




    /**
     * @description 接收
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * <AUTHOR>
     * @date 2023/5/15 10:12 AM
     */
    Map<String,Object> receive(Map<String,Object> params);




    /**
     * @description 饼图
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * <AUTHOR>
     * @date 2023/5/16 1:55 PM
     */
    Map<String,Object> summary(Map<String,Object> params);



    /**
     * @description 折线图
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * <AUTHOR>
     * @date 2023/5/16 2:08 PM
     */
    List<KeyNameValue<List<KeyNameValue<Double>>>>  trend(EqptLogReportListRequestVo vo);





    /**
     * @description 详细
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @date 2023/5/9 3:37 PM
     */
    IPage<Map<String, Object>> detail(EqptLogReportListRequestVo  query);
}
