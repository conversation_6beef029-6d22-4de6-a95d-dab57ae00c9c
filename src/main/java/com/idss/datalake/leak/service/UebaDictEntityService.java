package com.idss.datalake.leak.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.leak.pojo.UebaDictEntity;

import java.util.List;

public interface UebaDictEntityService extends IService<UebaDictEntity> {
    public String getDictValueByCode(String dictType, String dictCode,Integer tenantId);
    public String getDictCodeByValue(String dictType, String dictValue,Integer tenantId);

    public List getDictEntryList(String dictType,Integer tenantId);


}
