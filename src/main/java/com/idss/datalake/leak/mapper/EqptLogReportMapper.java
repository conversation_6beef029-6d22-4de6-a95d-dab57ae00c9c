package com.idss.datalake.leak.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.idss.datalake.leak.pojo.TrendCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/5/93:41 PM
 */
@Mapper
@Component
public interface EqptLogReportMapper {


    IPage<Map<String, Object>> findLogIpWithOutIp(IPage page, @Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    /**
     * 新增ip数据
     *
     * @param params Map
     */
    void insertReportIp(Map<String, Object> params);


    int updateEqptLogIp(Map<String, Object> params);


    List<Map<String, Object>> findExistLogIp(Map<String, Object> params);


    int insertXtyLogReport(Map<String,Object> params);



    List<String> findagzxList(@Param("branchCode")String branchCode,@Param("vendorName")String vendorName,@Param("eqptType")String eqptType,@Param("eqptBelong")Integer eqptBelong);



    Map<String,Object> getSum(Map<String,Object> params);


    List<Long> getKafkaSource();



    List<TrendCount> getTrendCount(Map<String,Object> params);


    IPage<Map<String,Object>> findLogIpByCondition(IPage page, @Param(Constants.WRAPPER) QueryWrapper queryWrapper);


    List<TrendCount> getSumByDeatil(Map<String,Object> params);
}
