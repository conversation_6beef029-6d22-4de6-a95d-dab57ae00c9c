package com.idss.datalake.leak.mapper.clickhouse;

import com.idss.datalake.leak.pojo.StockCount;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/5/151:25 PM
 */
@Mapper
@Component
public interface EqptLogReportChMapper {


    List<StockCount> getLogCount(Map<String,Object> query);


    List<StockCount> getLogTrendCount(Map<String,Object> query);


    List<StockCount> getLogCountByEqptIp(Map<String,Object> query);

}
