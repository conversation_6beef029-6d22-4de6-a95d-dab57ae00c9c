package com.idss.datalake.leak.controller;



import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.leak.pojo.EqptLogReportListRequestVo;
import com.idss.datalake.leak.service.IEqptLogReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


/**
 * 漏报分析
 *
 * <AUTHOR>
 * @date 2023/4/12
 */
@RestController
@RequestMapping("/api/leak")
@Slf4j
public class EqptLogReportController {


    @Autowired
    private IEqptLogReportService service;

    /**
     * 列表
     * @return
     */
    @PostMapping("/list")
    public ResultBean list(@RequestBody EqptLogReportListRequestVo vo) {
        return ResultBean.success(service.list(vo));
    }


    /**
     * @return java.util.Map<java.lang.String   ,   java.lang.Object>
     * @description 接收
     * <AUTHOR>
     * @date 2023/5/15 11:07 AM
     */
    @PostMapping("/receive")
    public Map<String, Object> receive(@RequestBody Map<String, Object> params) {
        return service.receive(params);
    }


    /**
     * 聚合
     * @return
     */
    @PostMapping("/summary")
    public ResultBean summary(@RequestBody Map<String,Object> query) {
        return ResultBean.success(service.summary(query));
    }



    /**
     * 折线图
     * @return
     */
    @PostMapping("/trend")
    public ResultBean trend(@RequestBody EqptLogReportListRequestVo vo) {
        return ResultBean.success(service.trend(vo));
    }



    /**
     * 详细
     * @return
     */
    @PostMapping("/detail")
    public ResultBean detail(@RequestBody EqptLogReportListRequestVo vo) {
        return ResultBean.success(service.detail(vo));
    }

}
