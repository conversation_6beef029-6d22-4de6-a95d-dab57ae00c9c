package com.idss.datalake.leak.pojo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 情报列表查询条件请求实体
 * @date 2022/8/15
 */
@Data
public class PageListQueryRequest {

    private String startTime;
    private String endTime;
    private String quickTime;
    private List<FilterConditionBean> filterCondition;
    private String orderField = "happen_time";
    // desc 降序  asc  升序
    private String orderType = "desc";
    @ApiModelProperty("ids")
    private List<String> ids;

    @NotNull(message = "页码不能为空")
    @Min(value = 0, message = "页码不能为负数")
    private Long pageNum = 1L;

    @NotNull(message = "每页条数不能为空")
    private Long pageSize = 20L;
}
