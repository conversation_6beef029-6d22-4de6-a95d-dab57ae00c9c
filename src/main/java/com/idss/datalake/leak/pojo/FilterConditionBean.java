package com.idss.datalake.leak.pojo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 搜索条件【过滤】实体类
 * @date 2022/8/1716:20
 */
@Data
public class FilterConditionBean {

    private String code;
    private String startValue;
    private String endValue;
    private String filterMode;
    private String operator;
    private List<String> valueList;

    private String column;
}
