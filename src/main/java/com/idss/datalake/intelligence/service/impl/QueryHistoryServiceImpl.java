package com.idss.datalake.intelligence.service.impl;

import com.idss.datalake.intelligence.entity.QueryHistory;
import com.idss.datalake.intelligence.mapper.QueryHistoryMapper;
import com.idss.datalake.intelligence.service.IQueryHistoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ip信息查询历史 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Service
public class QueryHistoryServiceImpl extends ServiceImpl<QueryHistoryMapper, QueryHistory> implements IQueryHistoryService {

}
