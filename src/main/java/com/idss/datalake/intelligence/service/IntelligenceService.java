package com.idss.datalake.intelligence.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.idss.datalake.common.util.IpUtil;
import com.idss.datalake.intelligence.entity.QueryHistory;
import com.idss.datalake.intelligence.enums.IPMeta;
import com.idss.datalake.intelligence.model.IPQuery;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import io.github.aiwen.ipplus360.AwdbReader;
import io.github.aiwen.ipplus360.impl.AwdbCacheImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedInputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
public class IntelligenceService {

    @Value("${awdb.ipv4:}")
    private String ipv4File;

    @Value("${awdb.ipv6:}")
    private String ipv6File;

    @Autowired
    private IQueryHistoryService queryHistoryService;

    public List<Map<String, Object>> ipInfo(IPQuery ipQuery) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();
        String ipAddr = ipQuery.getIp();
        if(StringUtils.isNotEmpty(ipAddr)) {
            String[] ips = ipAddr.split(",");
            String offlineFile = "";
            for (String ip : ips) {
                Map<String, Object> ipMap = new HashMap<>();
                List<Map<String, String>> ipMetas = new ArrayList<>();
                ipMap.put("ip", ip);
                ipMap.put("list", ipMetas);
                result.add(ipMap);
                if(IpUtil.ipv4Check(ip)) {
                    // ipv4地址
                    offlineFile = ipv4File;
                } else if(IpUtil.ipv6Check(ip)) {
                    // ipv6地址
                    offlineFile = ipv6File;
                } else {
                    // 非法ip地址
                    offlineFile = "";
                }

                if(StringUtils.isNotEmpty(offlineFile)) {
                    try(InputStream stream = new BufferedInputStream(Files.newInputStream(Paths.get(offlineFile)));
                        AwdbReader reader = new AwdbReader(stream, new AwdbCacheImpl())) {
                        JsonNode jsonNode = reader.findIpLocation(ip);
                        Iterator<Map.Entry<String, JsonNode>>  iterable = jsonNode.fields();
                        while (iterable.hasNext()) {
                            Map.Entry<String, JsonNode> entry = iterable.next();
                            Map<String, String> ipMeta = new HashMap<>();
                            ipMeta.put("label", IPMeta.getNameByCode(entry.getKey()));
                            ipMeta.put("value", entry.getValue().asText());

                            ipMetas.add(ipMeta);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }
        // 保存查询条件
        LocalDateTime now = LocalDateTime.now();
        UserValueObject uvo = UmsUtils.getUVO();
        QueryHistory queryHistory = new QueryHistory();
        queryHistory.setQueryCondition(ipQuery.getIp());
        queryHistory.setCreateTime(now);
        queryHistory.setUpdateTime(now);
        queryHistory.setCreateUser(uvo.getUserName());
        queryHistory.setUpdateUser(uvo.getUserName());
        queryHistory.setTenantId(Long.valueOf(uvo.getTenantId()));
        queryHistoryService.save(queryHistory);

        return result;
    }

    public List<QueryHistory> queryHistory() throws Exception {
        UserValueObject uvo = UmsUtils.getUVO();
        List<QueryHistory> queryHistories = queryHistoryService.list(new LambdaQueryWrapper<QueryHistory>()
                        .eq(QueryHistory::getCreateUser, uvo.getUserName())
                        .eq(QueryHistory::getTenantId, uvo.getTenantId())
                        .orderByDesc(QueryHistory::getCreateTime)
                        .last("limit 5"));
        return queryHistories;
    }
}
