package com.idss.datalake.intelligence.controller;



import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.intelligence.entity.QueryHistory;
import com.idss.datalake.intelligence.model.IPQuery;
import com.idss.datalake.intelligence.service.IntelligenceService;
import com.idss.datalake.leak.pojo.EqptLogReportListRequestVo;
import com.idss.datalake.leak.service.IEqptLogReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 漏报分析
 *
 * <AUTHOR>
 * @date 2023/4/12
 */
@RestController
@RequestMapping("/api/intelligence")
@Slf4j
public class IntelligenceController {

    @Autowired
    private IntelligenceService intelligenceService;

    /**
     * ip信息查询
     * @return
     */
    @PostMapping("/ipInfo")
    public ResultBean list(@RequestBody IPQuery ipQuery) {
        try {
            List<Map<String, Object>> ipMetas = intelligenceService.ipInfo(ipQuery);
            return ResultBean.success(ipMetas, "查询成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    /**
     * ip信息查询
     * @return
     */
    @GetMapping("/query/history")
    public ResultBean queryHistory() {
        try {
            List<QueryHistory> queryHistories = intelligenceService.queryHistory();
            return ResultBean.success(queryHistories, "查询成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }
}
