package com.idss.datalake.intelligence.enums;

public enum IPMeta {

    continent("continent", "所属大洲"),
    owner("owner", "所属机构"),
    country("country", "所属国家"),
    adcode("adcode", "行政区划代码"),
    city("city", "所属城市"),
    timezone("timezone", "时区"),
    isp("isp", "运营商"),
    accuracy("accuracy", "定位精度"),
    source("source", "采集方式"),
    asnumber("asnumber", "自治域编码"),
    areacode("areacode", "国家编码"),
    zipcode("zipcode", "邮编"),
    lngwgs("lngwgs", "WGS84坐标系纬度"),
    province("province", "所属省份"),
    district("district", "所属区县"),
    latwgs("latwgs", "WGS84坐标系经度"),
    radius("radius", "定位半径"),
    currency_code("currency_code", "货币代码"),
    currency_name("currency_name", "货币名称"),
    minip("minip", "IP块最小端IP"),
    maxip("maxip", "IP块最大端IP"),
    lngbd("lngbd", "BD09坐标系经度"),
    latbd("latbd", "BD09坐标系纬度");

    private String code;

    private String name;

    IPMeta(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (IPMeta value : IPMeta.values()) {
            if(code.equals(value.getCode())) {
                return value.getName();
            }
        }
        return code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
