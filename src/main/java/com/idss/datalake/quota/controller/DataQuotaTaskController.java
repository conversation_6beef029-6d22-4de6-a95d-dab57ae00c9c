package com.idss.datalake.quota.controller;


import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.datagovern.dictionary.dto.DataDictionaryItemDTO;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datamart.dto.request.AssetDetailPageRequest;
import com.idss.datalake.datamart.dto.request.AssetMapGroupRequest;
import com.idss.datalake.datamart.dto.response.AssetDetailPageVo;
import com.idss.datalake.datamart.entity.DataMartAsset;
import com.idss.datalake.datamart.enums.ElementTypeEnum;
import com.idss.datalake.datamart.service.IDataMartAssetService;
import com.idss.datalake.quota.dto.AddOrUpdateQuota;
import com.idss.datalake.quota.dto.DataQuotaTaskPageRequest;
import com.idss.datalake.quota.dto.FlowDetailDto;
import com.idss.datalake.quota.dto.QuotaTable;
import com.idss.datalake.quota.entity.DataQuotaTask;
import com.idss.datalake.quota.entity.DataQuotaTaskOut;
import com.idss.datalake.quota.service.IBatchTaskViewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 指标开发任务 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@RestController
@RequestMapping("/quota/task")
@Slf4j
public class DataQuotaTaskController {
    @Autowired
    private IBatchTaskViewService batchTaskViewService;

    @GetMapping("/viewCount")
    public ResultBean viewCount() {
        return ResultBean.success(batchTaskViewService.viewCount());
    }


    /**
     * 离线任务下拉
     *
     * @return
     */
    @GetMapping("/flowList")
    public ResultBean flowList() {
        return ResultBean.success(batchTaskViewService.flowList());
    }
    /**
     * 离线详情
     * @param flowId
     * @return
     */
    @GetMapping("/flowDetail/{flowId}")
    public ResultBean flowDetail(@PathVariable("flowId") Long flowId){
        return ResultBean.success(batchTaskViewService.flowDetail(flowId));
    }
    /**
     * 实时任务下拉
     *
     * @return
     */
    @GetMapping("/streamFlowList")
    public ResultBean streamFlowList() {
        return ResultBean.success(batchTaskViewService.streamFlowList());
    }
    /**
     * 实时详情
     * @param flowId
     * @return
     */
    @GetMapping("/streamFlowDetail/{flowId}")
    public ResultBean streamFlowDetail(@PathVariable("flowId") Long flowId){
        return ResultBean.success(batchTaskViewService.streamFlowDetail(flowId));
    }
    /**
     * 任务输入输出表信息
     *
     * @param flowId
     * @return
     */
    @GetMapping("/tableInfo/{flowId}")
    public ResultBean tableInfo(@PathVariable("flowId") Long flowId) {
        QuotaTable quotaTable = null;
        try {
            quotaTable = batchTaskViewService.tableInfo(flowId);
            return ResultBean.success(quotaTable);
        } catch (Exception e) {
            log.error("解析输入输出表错误", e);
            return ResultBean.fail(e.getMessage());
        }
    }

    /**
     * 新增或修改指标开发任务
     *
     * @param request
     * @return
     */
    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdateQuota(@RequestBody AddOrUpdateQuota request) {
        return batchTaskViewService.addOrUpdateQuota(request);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        return batchTaskViewService.detail(id);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @GetMapping("/remove/{id}")
    public ResultBean remove(@PathVariable("id") Long id) {
        return batchTaskViewService.remove(id);
    }

    /**
     * 分页查询
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/page")
    public BasePageResponse<List<DataQuotaTask>> pageQuery(@RequestBody DataQuotaTaskPageRequest requestDto) {
        return batchTaskViewService.pageQuery(requestDto);
    }

    /**
     * 自建表分页
     * @param requestDto
     * @return
     */
    @PostMapping("/selfPage")
    public BasePageResponse<List<DataQuotaTaskOut>> selfPageQuery(@RequestBody AssetMapGroupRequest requestDto){
        return batchTaskViewService.selfPageQuery(requestDto);
    }

    /**
     * 租户统计
     * @param param
     * @return
     */
    @PostMapping("/tenantGroup")
    public ResultBean tenantGroup(@RequestBody Map<String,String> param){
        return batchTaskViewService.tenantGroup(param.get("searchName"));
    }

    /**
     * 用户模型分页
     * @param requestDto
     * @return
     */
    @PostMapping("/tenantPage")
    public BasePageResponse<List<DataQuotaTaskOut>> tenantPage(@RequestBody AssetMapGroupRequest requestDto){
        return batchTaskViewService.tenantPage(requestDto);
    }

    /**
     * 资产详情
     * @param id
     * @return
     */
    @GetMapping("/assetDetail/{id}")
    public ResultBean assetDetail(@PathVariable("id") Long id){
        return batchTaskViewService.assetDetail(id);
    }

    /**
     * 字段信息
     * @param id
     * @return
     */
    @GetMapping("/columnList/{id}")
    public ResultBean columnList(@PathVariable("id")Long id){
        return batchTaskViewService.columnList(id);
    }

    /**
     * 预览数据
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/previewData/{id}")
    public ResultBean previewData(@PathVariable("id")Long id){
        try {
            return batchTaskViewService.previewData(id);
        } catch (Exception e) {
            log.error("预览数据错误",e);
            return ResultBean.fail(e.getMessage());
        }
    }

}
