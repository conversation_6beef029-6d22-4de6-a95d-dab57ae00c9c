package com.idss.datalake.quota.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 指标开发任务
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataQuotaTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型：0离线，1实时
     */
    private Integer taskType;

    /**
     * 数澜任务 ID
     */
    private Long flowId;

    /**
     * 任务描述
     */
    private String taskDesc;

    private Integer tenantId;

    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String updateUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private List<Long> tableIds;

    /**
     * 订阅次数
     */
    @TableField(exist = false)
    private Integer subscribeCnt;
}
