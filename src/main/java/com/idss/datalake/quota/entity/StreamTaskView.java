package com.idss.datalake.quota.entity;

import java.time.LocalDateTime;
import java.sql.Blob;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * VIEW
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StreamTaskView implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工作空间ID
     */
    private Long workspaceId;

    private String workspaceName;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目Code
     */
    private String projectCode;

    /**
     * 目录ID
     */
    private Long cataId;

    /**
     * 目录名称
     */
    private String cataName;

    /**
     * 业务流程ID
     */
    private Long flowId;

    /**
     * 业务流程名称
     */
    private String flowName;

    /**
     * 作业ID,由雪花算法生成
     */
    private Long taskId;

    /**
     * 作业名称
     */
    private String taskName;

    /**
     * 创建时间
     */
    private LocalDateTime ctime;

    /**
     * 登录名
     */
    private String createUser;

    /**
     * 作业类型Code
     */
    private String taskType;

    private Blob source;

    /**
     * 数据源类型
     */
    private Integer storageType;

    /**
     * 配置参数(Json格式)
     */
    private String storageParameter;

    /**
     * 数据源ID
     */
    private String storageId;

    /**
     * 配置参数(Json格式)
     */
    private String parameter;


}
