package com.idss.datalake.quota.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datamart.dto.request.AssetMapGroupRequest;
import com.idss.datalake.quota.dto.DataQuotaTaskPageRequest;
import com.idss.datalake.quota.entity.DataQuotaTask;
import com.idss.datalake.quota.entity.DataQuotaTaskOut;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 指标开发任务输出表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
public interface DataQuotaTaskOutMapper extends BaseMapper<DataQuotaTaskOut> {
    Page<DataQuotaTaskOut> page(AssetMapGroupRequest request);

    List<Map<String,Object>> tenantGroup(String searchName);

    Page<DataQuotaTaskOut> tenantPage(AssetMapGroupRequest request);

    List<Map<String,Object>> trend(@Param("taskIds") List<Long> taskIds);
}
