package com.idss.datalake.quota.service.impl;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.constants.NConst;
import com.idss.datalake.common.database.ClickhouseComponent;
import com.idss.datalake.common.database.HiveComponent;
import com.idss.datalake.common.database.MysqlComponent;
import com.idss.datalake.common.database.entity.DbConnInfo;
import com.idss.datalake.common.database.entity.DbDataInfo;
import com.idss.datalake.common.database.enums.DB_TYP;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.common.util.*;
import com.idss.datalake.datagovern.blood.model.SqlLineageTable;
import com.idss.datalake.datagovern.blood.model.SqlLineageTableModel;
import com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItem;
import com.idss.datalake.datagovern.dictionary.mapper.DataDictionaryItemMapper;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datagovern.metadata.model.detail.entity.*;
import com.idss.datalake.datagovern.metadata.model.detail.mapper.*;
import com.idss.datalake.datagovern.metadata.model.detail.service.ChElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebHiveElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.detail.service.IQuaWebMysqlElementDetailTableService;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.mapper.QuaWabElementMapper;
import com.idss.datalake.datamart.dto.request.AssetMapGroupRequest;
import com.idss.datalake.datamart.dto.request.AssetPreviewRequest;
import com.idss.datalake.datamart.dto.response.ElementInfo;
import com.idss.datalake.datamart.dto.response.QueryElementVo;
import com.idss.datalake.datamart.entity.*;
import com.idss.datalake.datamart.enums.ElementTypeEnum;
import com.idss.datalake.datamart.mapper.DataMartAssetMapper;
import com.idss.datalake.datamart.mapper.DataMartSubscribeMapper;
import com.idss.datalake.datamart.mapper.DataMartTableLinesMapper;
import com.idss.datalake.quota.dto.*;
import com.idss.datalake.quota.entity.*;
import com.idss.datalake.quota.mapper.*;
import com.idss.datalake.quota.service.IBatchTaskViewService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.quota.shulan.mapper.BatchTaskViewMapper;
import com.idss.datalake.quota.shulan.mapper.StreamTaskViewMapper;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.UnknownHostException;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * VIEW 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Service
@Slf4j
public class BatchTaskViewServiceImpl extends ServiceImpl<BatchTaskViewMapper, BatchTaskView> implements IBatchTaskViewService {
    @Resource
    private DataMartTableLinesMapper dataMartTableLinesMapper;
    @Resource
    private DataMartSubscribeMapper dataMartSubscribeMapper;
    @Resource
    private DataMartAssetMapper dataMartAssetMapper;
    @Resource
    private DataQuotaTaskOutMapper dataQuotaTaskOutMapper;
    @Resource
    private DataQuotaTaskInputMapper dataQuotaTaskInputMapper;
    @Resource
    private DataQuotaTaskMapper dataQuotaTaskMapper;
    @Resource
    private ChElementDetailTableMapper chElementDetailTableMapper;
    @Resource
    private ChElementDetailColumnMapper chElementDetailColumnMapper;
    @Resource
    private QuaWebHiveElementDetailTableMapper hiveElementDetailTableMapper;
    @Resource
    private QuaWebHiveElementDetailColumnMapper hiveElementDetailColumnMapper;
    @Resource
    private QuaWebMysqlElementDetailTableMapper mysqlElementDetailTableMapper;
    @Resource
    private QuaWebMysqlElementDetailColumnMapper mysqlElementDetailColumnMapper;
    @Resource
    private QuaWabElementMapper quaWabElementMapper;
    @Resource
    private DataDictionaryItemMapper dictionaryItemMapper;
    @Resource
    private StreamTaskViewMapper streamTaskViewMapper;

    @Autowired
    private ClickhouseComponent clickhouseComponent;
    @Autowired
    private HiveComponent hiveComponent;
    @Autowired
    private MysqlComponent mysqlComponent;


    @Value("${upload.kbs.filePath}")
    private String kbsFilePath;

    @Autowired
    private ChElementDetailTableService chElementDetailTableService;
    @Autowired
    private IQuaWebHiveElementDetailTableService hiveElementDetailTableService;
    @Autowired
    private IQuaWebMysqlElementDetailTableService mysqlElementDetailTableService;


    @Override
    public Map<String, Object> viewCount() {
        Map<String, Object> dataMap = new HashMap<>();
        List<DataQuotaTask> result = this.dataQuotaTaskMapper.selectList(new QueryWrapper<DataQuotaTask>().eq("tenant_id",UmsUtils.getUVO().getTenantId()));
        List<Long> taskIds = result.stream().map(DataQuotaTask::getId).collect(Collectors.toList());
        //订购数
        Integer subscribeCnt = 0;
        BigDecimal tableSpaceCnt = new BigDecimal("0.00");
        Integer tableCnt = dataQuotaTaskOutMapper.selectCount(new QueryWrapper<DataQuotaTaskOut>().in("task_id",taskIds));
        List<Map<String, Object>> trends = CollectionUtils.isEmpty(taskIds) ? new ArrayList<>(): dataQuotaTaskOutMapper.trend(taskIds);
        Collections.reverse(trends);

        for (DataQuotaTask task : result) {
            List<DataQuotaTaskOut> outs = dataQuotaTaskOutMapper.selectList(new QueryWrapper<DataQuotaTaskOut>().eq("task_id", task.getId()));
            Set<Long> assetIds = new HashSet<>();
            for (DataQuotaTaskOut out : outs) {
                List<DataMartAsset> dataMartAssetList = dataMartAssetMapper.selectList(new QueryWrapper<DataMartAsset>().eq("element_id", out.getElementId()).eq("db_name", out.getDbName()).eq("table_name", out.getTableName()));
                if(CollectionUtils.isNotEmpty(dataMartAssetList)){
                    assetIds.addAll(dataMartAssetList.stream().map(DataMartAsset::getId).collect(Collectors.toSet()));
                }
            }
            int count = CollectionUtils.isEmpty(assetIds) ? 0 : dataMartSubscribeMapper.selectCount(new QueryWrapper<DataMartSubscribe>().in("asset_id", assetIds));
            subscribeCnt += count;
            List<DataMartTableLines> tableLines = CollectionUtils.isEmpty(assetIds) ? new ArrayList<>() : dataMartTableLinesMapper.selectList(new QueryWrapper<DataMartTableLines>().in("asset_id", assetIds));
            if(CollectionUtils.isNotEmpty(tableLines)){
                for (DataMartTableLines tableLine : tableLines) {
                    tableSpaceCnt = tableSpaceCnt.add(tableLine.getTableSpaceAll());
                }
            }
        }
        dataMap.put("subscribeCnt", subscribeCnt);
        dataMap.put("tableSpaceCnt", tableSpaceCnt+" MB");
        dataMap.put("tableCnt", tableCnt);
        dataMap.put("trends", trends);

        return dataMap;
    }

    @Override
    public List<QuotaFlow> flowList() {
        return this.baseMapper.flowList();
    }

    @Override
    public FlowDetailDto flowDetail(Long flowId) {
        List<BatchTaskView> taskViews = this.baseMapper.selectList(new QueryWrapper<BatchTaskView>().eq("flow_id", flowId).orderByAsc("task_id"));
        FlowDetailDto dto = new FlowDetailDto();
        if(CollectionUtils.isNotEmpty(taskViews)) {
            dto.setFlowName(taskViews.get(0).getFlowName());
            dto.setTaskName(taskViews.stream().map(BatchTaskView::getTaskName).collect(Collectors.toSet()));
            dto.setTaskType(taskViews.stream().map(BatchTaskView::getTasktypeCode).collect(Collectors.toSet()));
            dto.setCreateUser(taskViews.get(0).getCreateUser());
            dto.setCtime(taskViews.get(0).getCtime());
        }
        return dto;
    }

    @Override
    public List<QuotaFlow> streamFlowList() {
        return this.baseMapper.streamFlowList();
    }

    @Override
    public FlowDetailDto streamFlowDetail(Long flowId) {
        List<StreamTaskView> taskViews = this.streamTaskViewMapper.selectList(new QueryWrapper<StreamTaskView>().eq("flow_id", flowId).orderByAsc("task_id"));
        FlowDetailDto dto = new FlowDetailDto();
        if(CollectionUtils.isNotEmpty(taskViews)) {
            dto.setFlowName(taskViews.get(0).getFlowName());
            dto.setTaskName(taskViews.stream().map(StreamTaskView::getTaskName).collect(Collectors.toSet()));
            dto.setTaskType(taskViews.stream().map(StreamTaskView::getTaskType).collect(Collectors.toSet()));
            dto.setCreateUser(taskViews.get(0).getCreateUser());
            dto.setCtime(taskViews.get(0).getCtime());
        }
        return dto;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public QuotaTable tableInfo(Long flowId) {
        int tenantId = UmsUtils.getUVO().getTenantId();
        QuotaTable result = new QuotaTable();
        List<QueryElementVo> inputTables = new ArrayList<>();
        List<QueryElementVo> outputTables = new ArrayList<>();
        result.setInputTables(inputTables);
        result.setOutputTables(outputTables);

        List<BatchTaskView> batchTaskViews = this.baseMapper.listByFlowId(flowId);
        for (BatchTaskView batchTaskView : batchTaskViews) {
            if (batchTaskView.getTasktypeCode().toLowerCase().contains("clickhouse")) {
                String connectUrl = batchTaskView.getConnectUrl();

                String ip = JdbcUtil.extractHostFromJdbcUrl(connectUrl);
                String port = JdbcUtil.extractPortFromJdbcUrl(connectUrl);
                if (ip != null && !JdbcUtil.isIPAddress(ip)) {
                    //是域名
                    try {
                        ip = JdbcUtil.hostAddress(ip);
                    } catch (UnknownHostException e) {
                        log.error("域名解析异常: ", e);
                        throw new RuntimeException(e);
                    }
                }
                String userName = batchTaskView.getUserName();
                String password = batchTaskView.getPassword();

                //查找元数据
                List<QuaWabElement> elements = quaWabElementMapper.selectList(new QueryWrapper<QuaWabElement>()
                        .eq("element_type", "CH")
                        .eq("ch_ip", ip)
                        .eq("ch_port", port)
                        .eq("flag", 1)
                        .eq("tenant_id", tenantId));
                if (CollectionUtils.isNotEmpty(elements)) {
                    QuaWabElement element = elements.get(0);
                    //解析输入输出表
                    SqlLineageTable parseLineageTables = JdbcUtil.parseLineageTables(batchTaskView.getSource().replaceAll("`",""));
                    List<SqlLineageTableModel> sourceTables = parseLineageTables.getSourceTables();
                    ChElementDetailTable detailTable = chElementDetailTableMapper.selectOne(new QueryWrapper<ChElementDetailTable>()
                            .eq("tenant_id", tenantId)
                            .eq("element_id", element.getId())
                            .last("limit 1"));
                    if (detailTable == null) {
                        throw new RuntimeException("元数据【类型:" + batchTaskView.getTasktypeCode() + ",IP:" + ip + ",PORT:" + port + "】尚未扫描完成，请等待扫描完成!");
                    }
                    String firstSnapshootVersion = detailTable.getFirstSnapshootVersion();
                    String lastSnapshootVersion = detailTable.getLastSnapshootVersion();
                    for (SqlLineageTableModel sourceTable : sourceTables) {
                        ChElementDetailTable orAddCHTable = findOrAddCHTable(sourceTable, element.getId(), batchTaskView.getDbName(), firstSnapshootVersion, lastSnapshootVersion, ip, port, userName, password);
                        QueryElementVo vo = new QueryElementVo();
                        vo.setId(orAddCHTable.getId());
                        vo.setElementId(orAddCHTable.getElementId());
                        vo.setElementName(orAddCHTable.getTableName());

                        vo.setElementType("Clickhouse");
                        vo.setDataType("表");
                        vo.setAssetTypeCode("TABLE");
                        vo.setAssetType("clickhouse_table");
                        vo.setAssetPath(orAddCHTable.getDbName() + "," + orAddCHTable.getTableName());
                        vo.setTaskId(batchTaskView.getTaskId());
                        inputTables.add(vo);
                    }
                    List<SqlLineageTableModel> targetTables = parseLineageTables.getTargetTables();
                    for (SqlLineageTableModel targetTable : targetTables) {
                        ChElementDetailTable orAddCHTable = findOrAddCHTable(targetTable, element.getId(), batchTaskView.getDbName(), firstSnapshootVersion, lastSnapshootVersion, ip, port, userName, password);
                        QueryElementVo vo = new QueryElementVo();
                        vo.setId(orAddCHTable.getId());
                        vo.setElementId(orAddCHTable.getElementId());
                        vo.setElementName(orAddCHTable.getTableName());

                        vo.setElementType("Clickhouse");
                        vo.setDataType("表");
                        vo.setAssetTypeCode("TABLE");
                        vo.setAssetType("clickhouse_table");
                        vo.setAssetPath(orAddCHTable.getDbName() + "," + orAddCHTable.getTableName());
                        vo.setTaskId(batchTaskView.getTaskId());
                        outputTables.add(vo);
                    }

                } else {
                    throw new RuntimeException("元数据【类型:" + batchTaskView.getTasktypeCode() + ",IP:" + ip + ",PORT:" + port + "】不存在，请先添加元数据扫描!");
                }
            } else if (batchTaskView.getTasktypeCode().toLowerCase().contains("hive")) {
                String connectUrl = batchTaskView.getConnectUrl();
                String dbName = batchTaskView.getDbName();
                String defaultQueue = batchTaskView.getDefaultQueue();

                String hiveUrl = connectUrl.replace("sdc_ods", dbName).replace("root.bdoc.user_sdc", defaultQueue);
                List<QuaWabElement> elements = quaWabElementMapper.selectList(new QueryWrapper<QuaWabElement>()
                        .eq("element_type", "HIVE")
                        .eq("flag", 1)
                        .eq("tenant_id", tenantId));
                if (CollectionUtils.isNotEmpty(elements)) {
                    QuaWabElement element = null;
                    for (QuaWabElement item : elements) {
                        if (JdbcUtil.areJdbcUrlsEqual(hiveUrl, item.getJdbcUrl())) {
                            element = item;
                            break;
                        }
                    }
                    if (element != null) {
                        //解析输入输出表
                        SqlLineageTable parseLineageTables = JdbcUtil.parseLineageTables(batchTaskView.getSource().replaceAll("`",""));
                        List<SqlLineageTableModel> sourceTables = parseLineageTables.getSourceTables();
                        QuaWebHiveElementDetailTable detailTable = hiveElementDetailTableMapper.selectOne(new QueryWrapper<QuaWebHiveElementDetailTable>()
                                .eq("tenant_id", tenantId)
                                .eq("element_id", element.getId())
                                .last("limit 1"));
                        if (detailTable == null) {
                            throw new RuntimeException("元数据【类型:" + batchTaskView.getTasktypeCode() + ",hiveURL:" + hiveUrl + "】尚未扫描完成，请等待扫描完成!");
                        }
                        String firstSnapshootVersion = detailTable.getFirstSnapshootVersion();
                        String lastSnapshootVersion = detailTable.getLastSnapshootVersion();
                        for (SqlLineageTableModel sourceTable : sourceTables) {
                            QuaWebHiveElementDetailTable orAddCHTable = findOrAddHiveTable(sourceTable, element.getId(), batchTaskView.getDbName(), firstSnapshootVersion, lastSnapshootVersion, hiveUrl, batchTaskView.getClusterId(), batchTaskView.getClusterUserId());
                            QueryElementVo vo = new QueryElementVo();
                            vo.setId(orAddCHTable.getId());
                            vo.setElementId(orAddCHTable.getElementId());
                            vo.setElementName(orAddCHTable.getTableName());

                            vo.setElementType("Hive");
                            vo.setDataType("表");
                            vo.setAssetTypeCode("TABLE");
                            vo.setAssetType("hive_table");
                            vo.setAssetPath(orAddCHTable.getDbName() + "," + orAddCHTable.getTableName());
                            vo.setTaskId(batchTaskView.getTaskId());
                            inputTables.add(vo);
                        }
                        List<SqlLineageTableModel> targetTables = parseLineageTables.getTargetTables();
                        for (SqlLineageTableModel targetTable : targetTables) {
                            QuaWebHiveElementDetailTable orAddCHTable = findOrAddHiveTable(targetTable, element.getId(), batchTaskView.getDbName(), firstSnapshootVersion, lastSnapshootVersion, hiveUrl, batchTaskView.getClusterId(), batchTaskView.getClusterUserId());
                            QueryElementVo vo = new QueryElementVo();
                            vo.setId(orAddCHTable.getId());
                            vo.setElementId(orAddCHTable.getElementId());
                            vo.setElementName(orAddCHTable.getTableName());

                            vo.setElementType("Hive");
                            vo.setDataType("表");
                            vo.setAssetTypeCode("TABLE");
                            vo.setAssetType("hive_table");
                            vo.setAssetPath(orAddCHTable.getDbName() + "," + orAddCHTable.getTableName());
                            vo.setTaskId(batchTaskView.getTaskId());
                            outputTables.add(vo);
                        }
                    } else {
                        throw new RuntimeException("元数据【类型:" + batchTaskView.getTasktypeCode() + ",hiveURL:" + hiveUrl + "】不存在，请先添加元数据扫描!");
                    }
                } else {
                    throw new RuntimeException("元数据【类型:" + batchTaskView.getTasktypeCode() + ",hiveURL:" + hiveUrl + "】不存在，请先添加元数据扫描!");
                }


            } else if (batchTaskView.getTasktypeCode().toLowerCase().contains("mysql")) {
                String connectUrl = batchTaskView.getConnectUrl();
                String ip = "";
                String port = "";
                if (connectUrl.startsWith("jdbc:mysql")) {
                    ip = JdbcUtil.extractHostFromJdbcUrl(connectUrl);
                    port = JdbcUtil.extractPortFromJdbcUrl(connectUrl);
                } else {
                    String[] split = connectUrl.split(":");
                    ip = split[0];
                    port = split[1];
                }
                String userName = batchTaskView.getUserName();
                String password = batchTaskView.getPassword();

                //查找元数据
                List<QuaWabElement> elements = quaWabElementMapper.selectList(new QueryWrapper<QuaWabElement>()
                        .eq("element_type", "MYSQL")
                        .eq("ch_ip", ip)
                        .eq("ch_port", port)
                        .eq("flag", 1)
                        .eq("tenant_id", tenantId));
                if (CollectionUtils.isNotEmpty(elements)) {
                    QuaWabElement element = elements.get(0);
                    //解析输入输出表
                    SqlLineageTable parseLineageTables = JdbcUtil.parseLineageTables(batchTaskView.getSource().replaceAll("`",""));
                    List<SqlLineageTableModel> sourceTables = parseLineageTables.getSourceTables();
                    QuaWebMysqlElementDetailTable detailTable = mysqlElementDetailTableMapper.selectOne(new QueryWrapper<QuaWebMysqlElementDetailTable>()
                            .eq("tenant_id", tenantId)
                            .eq("element_id", element.getId())
                            .last("limit 1"));
                    if (detailTable == null) {
                        throw new RuntimeException("元数据【类型:" + batchTaskView.getTasktypeCode() + ",IP:" + ip + ",PORT:" + port + "】尚未扫描完成，请等待扫描完成!");
                    }
                    String firstSnapshootVersion = detailTable.getFirstSnapshootVersion();
                    String lastSnapshootVersion = detailTable.getLastSnapshootVersion();
                    for (SqlLineageTableModel sourceTable : sourceTables) {
                        QuaWebMysqlElementDetailTable orAddCHTable = findOrAddMysqlTable(sourceTable, element.getId(), batchTaskView.getDbName(), firstSnapshootVersion, lastSnapshootVersion, ip, port, userName, password);
                        QueryElementVo vo = new QueryElementVo();
                        vo.setId(orAddCHTable.getId());
                        vo.setElementId(orAddCHTable.getElementId());
                        vo.setElementName(orAddCHTable.getTableName());

                        vo.setElementType("Mysql");
                        vo.setDataType("表");
                        vo.setAssetTypeCode("TABLE");
                        vo.setAssetType("mysql_table");
                        vo.setAssetPath(orAddCHTable.getDbName() + "," + orAddCHTable.getTableName());
                        vo.setTaskId(batchTaskView.getTaskId());
                        inputTables.add(vo);
                    }
                    List<SqlLineageTableModel> targetTables = parseLineageTables.getTargetTables();
                    for (SqlLineageTableModel targetTable : targetTables) {
                        QuaWebMysqlElementDetailTable orAddCHTable = findOrAddMysqlTable(targetTable, element.getId(), batchTaskView.getDbName(), firstSnapshootVersion, lastSnapshootVersion, ip, port, userName, password);
                        QueryElementVo vo = new QueryElementVo();
                        vo.setId(orAddCHTable.getId());
                        vo.setElementId(orAddCHTable.getElementId());
                        vo.setElementName(orAddCHTable.getTableName());

                        vo.setElementType("Mysql");
                        vo.setDataType("表");
                        vo.setAssetTypeCode("TABLE");
                        vo.setAssetType("mysql_table");
                        vo.setAssetPath(orAddCHTable.getDbName() + "," + orAddCHTable.getTableName());
                        vo.setTaskId(batchTaskView.getTaskId());
                        outputTables.add(vo);
                    }

                } else {
                    throw new RuntimeException("元数据【类型:" + batchTaskView.getTasktypeCode() + ",IP:" + ip + ",PORT:" + port + "】不存在，请先添加元数据扫描!");
                }
            }
        }
        return result;
    }

    @Override
    public ResultBean addOrUpdateQuota(AddOrUpdateQuota request) {
        UserValueObject uvo = UmsUtils.getUVO();
        if (request.getId() == null) {
            //验证重复
            List<DataQuotaTask> exists = dataQuotaTaskMapper.selectList(new QueryWrapper<DataQuotaTask>().eq("tenant_id",UmsUtils.getUVO().getTenantId()).eq("task_name", request.getTaskName()));
            if(CollectionUtils.isNotEmpty(exists)) {
                return ResultBean.fail("名称重复");
            }
            List<DataQuotaTask> flowList = dataQuotaTaskMapper.selectList(new QueryWrapper<DataQuotaTask>().eq("tenant_id", UmsUtils.getUVO().getTenantId()).eq("task_type", request.getTaskType()).eq("flow_id", request.getFlowId()));
            if(CollectionUtils.isNotEmpty(flowList)) {
                return ResultBean.fail("该任务已发布，不能重复发布");
            }


            DataQuotaTask task = new DataQuotaTask();
            task.setTaskName(request.getTaskName());
            task.setTaskType(request.getTaskType());
            task.setFlowId(request.getFlowId());
            task.setTaskDesc(request.getTaskDesc());
            task.setTenantId(uvo.getTenantId());
            task.setCreateUser(uvo.getUserName());
            task.setCreateTime(LocalDateTime.now());
            task.setUpdateUser(uvo.getUserName());
            task.setUpdateTime(LocalDateTime.now());
            dataQuotaTaskMapper.insert(task);

            for (DataQuotaTaskOut taskOut : request.getTaskOuts()) {
                taskOut.setTaskId(task.getId());
                dataQuotaTaskOutMapper.insert(taskOut);
            }
            for (DataQuotaTaskInput taskInput : request.getTaskInputs()) {
                taskInput.setTaskId(task.getId());
                dataQuotaTaskInputMapper.insert(taskInput);
            }
        } else {
            //验证重复
            List<DataQuotaTask> exists = dataQuotaTaskMapper.selectList(new QueryWrapper<DataQuotaTask>().ne("id",request.getId()).eq("tenant_id",UmsUtils.getUVO().getTenantId()).eq("task_name", request.getTaskName()));
            if(CollectionUtils.isNotEmpty(exists)) {
                return ResultBean.fail("名称重复");
            }
            List<DataQuotaTask> flowList = dataQuotaTaskMapper.selectList(new QueryWrapper<DataQuotaTask>().ne("id",request.getId()).eq("tenant_id", UmsUtils.getUVO().getTenantId()).eq("task_type", request.getTaskType()).eq("flow_id", request.getFlowId()));
            if(CollectionUtils.isNotEmpty(flowList)) {
                return ResultBean.fail("该任务已发布，不能重复发布");
            }

            DataQuotaTask dataQuotaTask = dataQuotaTaskMapper.selectById(request.getId());
            dataQuotaTask.setTaskName(request.getTaskName());
            dataQuotaTask.setTaskType(request.getTaskType());
            dataQuotaTask.setFlowId(request.getFlowId());
            dataQuotaTask.setTaskDesc(request.getTaskDesc());
            dataQuotaTask.setUpdateUser(uvo.getUserName());
            dataQuotaTask.setUpdateTime(LocalDateTime.now());
            dataQuotaTaskMapper.updateById(dataQuotaTask);

            dataQuotaTaskOutMapper.delete(new QueryWrapper<DataQuotaTaskOut>().eq("task_id", request.getId()));
            for (DataQuotaTaskOut taskOut : request.getTaskOuts()) {
                taskOut.setTaskId(dataQuotaTask.getId());
                dataQuotaTaskOutMapper.insert(taskOut);
            }

            dataQuotaTaskInputMapper.delete(new QueryWrapper<DataQuotaTaskInput>().eq("task_id", request.getId()));
            for (DataQuotaTaskInput taskInput : request.getTaskInputs()) {
                taskInput.setTaskId(dataQuotaTask.getId());
                dataQuotaTaskInputMapper.insert(taskInput);
            }
        }
        return ResultBean.success();
    }

    @Override
    public BasePageResponse<List<DataQuotaTask>> pageQuery(DataQuotaTaskPageRequest requestDto) {
        UserValueObject uvo = UmsUtils.getUVO();
        List<DataQuotaTask> list = new ArrayList<>();
        requestDto.setTenantId(Long.parseLong(uvo.getTenantId() + ""));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<DataQuotaTask> page = dataQuotaTaskMapper.page(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        List<DataQuotaTask> result = page.getResult();
        for (DataQuotaTask task : result) {
            List<DataQuotaTaskOut> outs = dataQuotaTaskOutMapper.selectList(new QueryWrapper<DataQuotaTaskOut>().eq("task_id", task.getId()));
            Set<Long> assetIds = new HashSet<>();
            for (DataQuotaTaskOut out : outs) {
                List<DataMartAsset> dataMartAssetList = dataMartAssetMapper.selectList(new QueryWrapper<DataMartAsset>().eq("element_id", out.getElementId()).eq("db_name", out.getDbName()).eq("table_name", out.getTableName()));
                if(CollectionUtils.isNotEmpty(dataMartAssetList)){
                    assetIds.addAll(dataMartAssetList.stream().map(DataMartAsset::getId).collect(Collectors.toSet()));
                }
            }
            Integer count = CollectionUtils.isEmpty(assetIds) ? 0 : dataMartSubscribeMapper.selectCount(new QueryWrapper<DataMartSubscribe>().in("asset_id", assetIds));
            task.setSubscribeCnt(count);
        }


        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), result);
    }

    @Override
    public ResultBean detail(Long id) {
        DataQuotaTask dataQuotaTask = this.dataQuotaTaskMapper.selectById(id);
        List<Long> collect = dataQuotaTaskOutMapper.selectList(new QueryWrapper<DataQuotaTaskOut>().eq("task_id", id)).stream().map(DataQuotaTaskOut::getTableId).collect(Collectors.toList());
        dataQuotaTask.setTableIds(collect);
        return ResultBean.success(dataQuotaTask);
    }

    @Override
    public ResultBean remove(Long id) {
        this.dataQuotaTaskMapper.deleteById(id);
        dataQuotaTaskOutMapper.delete(new QueryWrapper<DataQuotaTaskOut>().eq("task_id", id));
        return ResultBean.success();
    }

    @Override
    public BasePageResponse<List<DataQuotaTaskOut>> selfPageQuery(AssetMapGroupRequest requestDto) {
        UserValueObject uvo = UmsUtils.getUVO();
        List<DataQuotaTaskOut> list = new ArrayList<>();
        requestDto.setTenantId(Long.parseLong(uvo.getTenantId() + ""));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<DataQuotaTaskOut> page = dataQuotaTaskOutMapper.page(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
    }

    @Override
    public ResultBean tenantGroup(String searchName) {
        return ResultBean.success(dataQuotaTaskOutMapper.tenantGroup(searchName));
    }

    @Override
    public BasePageResponse<List<DataQuotaTaskOut>> tenantPage(AssetMapGroupRequest requestDto) {
        List<DataQuotaTaskOut> list = new ArrayList<>();
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<DataQuotaTaskOut> page = dataQuotaTaskOutMapper.tenantPage(requestDto);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
    }

    @Override
    public ResultBean assetDetail(Long id) {

        DataDictionaryItem item = new DataDictionaryItem();
        item.setId(id);

        DataQuotaTaskOut dataQuotaTaskOut = this.dataQuotaTaskOutMapper.selectById(id);
        DataQuotaTask dataQuotaTask = this.dataQuotaTaskMapper.selectById(dataQuotaTaskOut.getTaskId());

        if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(dataQuotaTaskOut.getAssetType())) {
            ChElementDetailTable ch = chElementDetailTableService.getOne(
                    new QueryWrapper<ChElementDetailTable>()
                            .eq("element_id", dataQuotaTaskOut.getElementId())
                            .eq("db_name", dataQuotaTaskOut.getDbName())
                            .eq("table_name", dataQuotaTaskOut.getTableName()));
            item.setItemDesc(ch.getTableDscribe());
            item.setOwner(ch.getTableOwner());
            item.setIsSensitive(ch.getIsSensitive());
            item.setColumnNameCn(ch.getTableNameCn());
            item.setDatabaseName(dataQuotaTaskOut.getDbName());
            item.setDatasourceType(DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName());
            ChElementDetailTable detailTable = chElementDetailTableMapper.selectById(dataQuotaTaskOut.getTableId());
            item.setItemDesc(detailTable.getTableDscribe());
            item.setCnDesc(detailTable.getTableNameCn());
        } else if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(dataQuotaTaskOut.getAssetType())) {
            QuaWebMysqlElementDetailTable mysql = mysqlElementDetailTableService.getOne(
                    new QueryWrapper<QuaWebMysqlElementDetailTable>()
                            .eq("element_id", dataQuotaTaskOut.getElementId())
                            .eq("db_name", dataQuotaTaskOut.getDbName())
                            .eq("table_name", dataQuotaTaskOut.getTableName()));
            item.setItemDesc(mysql.getTableDscribe());
            item.setOwner(mysql.getTableOwner());
            item.setIsSensitive(mysql.getIsSensitive());
            item.setColumnNameCn(mysql.getTableNameCn());
            item.setDatabaseName(dataQuotaTaskOut.getDbName());
            item.setDatasourceType(DATA_SOURCE_TYPE_ENUM.MYSQL.getName());
            QuaWebMysqlElementDetailTable detailTable = mysqlElementDetailTableMapper.selectById(dataQuotaTaskOut.getTableId());
            item.setItemDesc(detailTable.getTableDscribe());
            item.setCnDesc(detailTable.getTableNameCn());
        } else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(dataQuotaTaskOut.getAssetType())) {
            QuaWebHiveElementDetailTable hive = hiveElementDetailTableService.getOne(
                    new QueryWrapper<QuaWebHiveElementDetailTable>()
                            .eq("element_id", dataQuotaTaskOut.getElementId())
                            .eq("db_name", dataQuotaTaskOut.getDbName())
                            .eq("table_name", dataQuotaTaskOut.getTableName()));
            item.setItemDesc(hive.getTableDscribe());
            item.setOwner(hive.getTableOwner());
            item.setIsSensitive(hive.getIsSensitive());
            item.setColumnNameCn(hive.getTableNameCn());
            item.setDatabaseName(dataQuotaTaskOut.getDbName());
            item.setDatasourceType(DATA_SOURCE_TYPE_ENUM.HIVE.getName());
            QuaWebHiveElementDetailTable detailTable = hiveElementDetailTableMapper.selectById(dataQuotaTaskOut.getTableId());
            item.setItemDesc(detailTable.getTableDscribe());
            item.setCnDesc(detailTable.getTableNameCn());
        }
        item.setElementId(dataQuotaTaskOut.getElementId());
        item.setCreateTime(dataQuotaTask.getCreateTime());
        item.setUpdateTime(dataQuotaTask.getUpdateTime());
        item.setTableName(dataQuotaTaskOut.getTableName());

        queryTotal(dataQuotaTaskOut, item);

        // 查询质量任务id
        Long taskId = dictionaryItemMapper.queryTaskId(dataQuotaTaskOut.getElementId(), dataQuotaTaskOut.getDbName(), dataQuotaTaskOut.getTableName());
        item.setTaskId(taskId);

        return ResultBean.success(item);
    }

    @Override
    public ResultBean columnList(Long id) {
        DataQuotaTaskOut dataQuotaTaskOut = this.dataQuotaTaskOutMapper.selectById(id);
        try {
            if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(dataQuotaTaskOut.getAssetType())) {
                List<ChElementDetailColumn> columns = chElementDetailColumnMapper.selectList(new QueryWrapper<ChElementDetailColumn>().eq("element_id", dataQuotaTaskOut.getElementId()).eq("db_name", dataQuotaTaskOut.getDbName()).eq("table_name", dataQuotaTaskOut.getTableName()));

                ElementInfo elementInfo = dataMartAssetMapper.queryElementInfo(dataQuotaTaskOut.getElementId());
                String password = elementInfo.getDbPassword();
                if(StringUtils.isNotEmpty(password)) {
                    password = BtoaEncode.decrypt(elementInfo.getDbPassword());
                }
                String url = "jdbc:clickhouse://" + elementInfo.getDbIp() + ":" + elementInfo.getDbPort() + "/" + dataQuotaTaskOut.getDbName()+"?max_session_lifetime=3600";
                Connection connect = ClickhouseUtil.getConnect(url, elementInfo.getDbUserName(), password);
                try {
                    TableInfo tableInfo = ClickhouseUtil.getTableInfo(connect, dataQuotaTaskOut.getDbName(), dataQuotaTaskOut.getTableName());
                    for (ChElementDetailColumn column : columns) {
                        List<ColumnInfo> collect = tableInfo.getColumns().stream().filter(field -> field.getField().equals(column.getColumnName())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect)) {
                            column.setColumnType(collect.get(0).getType());
                        }
                    }
                } finally {
                    if (connect != null) {
                        ClickhouseUtil.close(connect);
                    }
                }
                return ResultBean.success(columns);
            } else if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(dataQuotaTaskOut.getAssetType())) {
                List<QuaWebMysqlElementDetailColumn> columns = mysqlElementDetailColumnMapper.selectList(new QueryWrapper<QuaWebMysqlElementDetailColumn>().eq("element_id", dataQuotaTaskOut.getElementId()).eq("db_name", dataQuotaTaskOut.getDbName()).eq("table_name", dataQuotaTaskOut.getTableName()));

                ElementInfo elementInfo = dataMartAssetMapper.queryElementInfo(dataQuotaTaskOut.getElementId());
                String url = "jdbc:mysql://" + elementInfo.getDbIp() + ":" + elementInfo.getDbPort() + "/" + dataQuotaTaskOut.getDbName();
                String password = elementInfo.getDbPassword();
                if(StringUtils.isNotEmpty(password)) {
                    password = BtoaEncode.decrypt(elementInfo.getDbPassword());
                }
                Connection connect = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_MYSQL, url, elementInfo.getDbUserName(), password);
                try {
                    TableInfo tableInfo = MysqlUtil.getTableInfo(connect, dataQuotaTaskOut.getDbName(), dataQuotaTaskOut.getTableName());
                    for (QuaWebMysqlElementDetailColumn column : columns) {
                        List<ColumnInfo> collect = tableInfo.getColumns().stream().filter(field -> field.getField().equals(column.getColumnName())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect)) {
                            column.setColumnType(collect.get(0).getType());
                        }
                    }
                } finally {
                    if (connect != null) {
                        MysqlUtil.close(connect);
                    }
                }
                return ResultBean.success(columns);
            } else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(dataQuotaTaskOut.getAssetType())) {
                ElementInfo elementInfo = dataMartAssetMapper.queryElementInfo(dataQuotaTaskOut.getElementId());
                String password = elementInfo.getDbPassword();
                String krb5ConfPath = elementInfo.getKrb5ConfPath();
                String keyTabPath = elementInfo.getKeyTabPath();
                String url = "";
                Connection connect = null;
                if (elementInfo.getKbsEnable() == 0) {
                    if(StringUtils.isNotEmpty(password)) {
                        password = BtoaEncode.decrypt(elementInfo.getDbPassword());
                    }
                    url = "jdbc:hive2://" + elementInfo.getDbIp() + ":" + elementInfo.getDbPort() + "/" + dataQuotaTaskOut.getDbName();
                    connect = HiveUtil.getConnect(url, elementInfo.getDbUserName(), password, "0", null, null, null);
                } else {
                    BASE64Decoder decoder = new BASE64Decoder();
                    url = elementInfo.getJdbcUrl();
                    if(StringUtils.isNotEmpty(keyTabPath)) {
                        keyTabPath = new String(decoder.decodeBuffer(keyTabPath));
                    }
                    if(StringUtils.isNotEmpty(krb5ConfPath)) {
                        krb5ConfPath = new String(decoder.decodeBuffer(krb5ConfPath));
                    }
                    connect = HiveUtil.getConnect(url, null, null, "1", elementInfo.getDbUserName(), keyTabPath, krb5ConfPath);
                }

                List<QuaWebHiveElementDetailColumn> columns = hiveElementDetailColumnMapper.selectList(new QueryWrapper<QuaWebHiveElementDetailColumn>().eq("element_id", dataQuotaTaskOut.getElementId()).eq("db_name", dataQuotaTaskOut.getDbName()).eq("table_name", dataQuotaTaskOut.getTableName()));
                try {
                    TableInfo tableInfo = HiveUtil.getTableInfo(connect, dataQuotaTaskOut.getDbName(), dataQuotaTaskOut.getTableName());
                    for (QuaWebHiveElementDetailColumn column : columns) {
                        List<ColumnInfo> collect = tableInfo.getColumns().stream().filter(field -> field.getField().equals(column.getColumnName())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect)) {
                            column.setColumnType(collect.get(0).getType());
                        }
                    }
                } finally {
                    if (connect != null) {
                        HiveUtil.close(connect);
                    }
                }
                return ResultBean.success(columns);
            }
            return ResultBean.fail("类型错误");
        } catch (Exception e) {
            log.error("查询字段错误",e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @Override
    public ResultBean previewData(Long id) throws Exception {
        log.info("开始预览数据, ID: {}", id);
        DataQuotaTaskOut dataQuotaTaskOut = this.dataQuotaTaskOutMapper.selectById(id);
        List<DbDataInfo> result = new ArrayList<>();
        AssetPreviewRequest req = new AssetPreviewRequest();
        String assetType = dataQuotaTaskOut.getAssetType();
        req.setAssetType(assetType);
        req.setDbName(dataQuotaTaskOut.getDbName());
        req.setTableName(dataQuotaTaskOut.getTableName());
        req.setAssetType(assetType);
        req.setElementId(dataQuotaTaskOut.getElementId());
        DbDataInfo dbDataInfo = queryData(req);
        if (dbDataInfo != null) {
            dbDataInfo.setTableName(StringUtils.isNotBlank(req.getTableName()) ? req.getTableName() : req.getIndexName());
            result.add(dbDataInfo);
        }
        return ResultBean.success(result);
    }

    private DbDataInfo queryData(AssetPreviewRequest request) throws Exception {
        ElementInfo elementInfo = dataMartAssetMapper.queryElementInfo(request.getElementId());
        if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(request.getAssetType())) {
            DbConnInfo dbConnInfo = new DbConnInfo();
            dbConnInfo.setDbType(DB_TYP.CLICKHOUSE.getEnName());
            dbConnInfo.setUser(elementInfo.getDbUserName());
            dbConnInfo.setPwd(BtoaEncode.decrypt(elementInfo.getDbPassword()));
            dbConnInfo.setUrl("jdbc:clickhouse://" + elementInfo.getDbIp() + ":" + elementInfo.getDbPort() + "/"+request.getDbName()+"?max_session_lifetime=3600");
            DbDataInfo sampleData = clickhouseComponent.getSampleData(
                    dbConnInfo,
                    "select * from ".concat(request.getDbName()).concat(".").concat(request.getTableName().concat(" limit 3")),
                    DB_TYP.CLICKHOUSE.getEnName());
            sampleData.setTotal(sampleData.getData().size());
            return sampleData;
        } else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(request.getAssetType())) {
            if (elementInfo.getKbsEnable() == 0) {
                DbConnInfo dbConnInfo = new DbConnInfo();
                dbConnInfo.setDbType(DB_TYP.Hive.getEnName());
                dbConnInfo.setUser(elementInfo.getDbUserName());
                dbConnInfo.setPwd(BtoaEncode.decrypt(elementInfo.getDbPassword()));
                dbConnInfo.setUrl("jdbc:hive2://" + elementInfo.getDbIp() + ":" + elementInfo.getDbPort() + "/");
                DbDataInfo sampleData = hiveComponent.getSampleData(
                        dbConnInfo,
                        "select * from ".concat(request.getDbName()).concat(".").concat(request.getTableName()).concat(" limit 3"),
                        DB_TYP.Hive.getEnName());
                sampleData.setTotal(sampleData.getData().size());
                return sampleData;
            } else {
                BASE64Decoder decoder = new BASE64Decoder();
                DbConnInfo dbConnInfo = new DbConnInfo();
                dbConnInfo.setDbType(DB_TYP.Hive.getEnName());
                dbConnInfo.setEnableKbs("1");
                dbConnInfo.setKbsAccount(elementInfo.getDbUserName());
                dbConnInfo.setKeyTabPath(new String(decoder.decodeBuffer(elementInfo.getKeyTabPath())));
                dbConnInfo.setKrb5ConfPath(new String(decoder.decodeBuffer(elementInfo.getKrb5ConfPath())));
                dbConnInfo.setHiveJdbcUrl(elementInfo.getJdbcUrl());
                DbDataInfo sampleData = hiveComponent.getSampleData(
                        dbConnInfo,
                        "select * from ".concat(request.getDbName()).concat(".").concat(request.getTableName()).concat(" limit 3"),
                        DB_TYP.Hive.getEnName());
                sampleData.setTotal(sampleData.getData().size());
                return sampleData;
            }
        } else if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(request.getAssetType())) {
            DbConnInfo dbConnInfo = new DbConnInfo();
            dbConnInfo.setDbType(DB_TYP.Mysql.getEnName());
            dbConnInfo.setUser(elementInfo.getDbUserName());
            dbConnInfo.setPwd(BtoaEncode.decrypt(elementInfo.getDbPassword()));
            dbConnInfo.setUrl("jdbc:mysql://" + elementInfo.getDbIp() + ":" + elementInfo.getDbPort() + "/" + request.getDbName() +
                    "?useSSL=false");
            DbDataInfo sampleData = mysqlComponent.getSampleData(
                    dbConnInfo,
                    "select * from `".concat(request.getDbName()).concat("`.`").concat(request.getTableName()).concat("` limit 3"),
                    DB_TYP.Mysql.getEnName());
            sampleData.setTotal(sampleData.getData().size());
            return sampleData;
        }
        return null;
    }

    private void queryTotal(DataQuotaTaskOut dataQuotaTaskOut, DataDictionaryItem item) {
        try {

            if (ElementTypeEnum.CLICKHOUSE_TABLE.getCode().equals(dataQuotaTaskOut.getAssetType())) {
                ElementInfo elementInfo = dataMartAssetMapper.queryElementInfo(dataQuotaTaskOut.getElementId());
                String url = "jdbc:clickhouse://" + elementInfo.getDbIp() + ":" + elementInfo.getDbPort() + "/" + dataQuotaTaskOut.getDbName();
                String password = elementInfo.getDbPassword();
                if(StringUtils.isNotEmpty(password)) {
                    password = BtoaEncode.decrypt(password);
                }
                Connection connect = ClickhouseUtil.getConnect(url, elementInfo.getDbUserName(), password);
                item.setDataCount(ClickhouseUtil.countTableLine(connect, dataQuotaTaskOut.getTableName()));
                item.setStorageSize(ClickhouseUtil.countTableSpace(connect, dataQuotaTaskOut.getDbName(), dataQuotaTaskOut.getTableName()) + " M");
                ClickhouseUtil.close(connect);
            } else if (ElementTypeEnum.HIVE_TABLE.getCode().equals(dataQuotaTaskOut.getAssetType())) {
                ElementInfo elementInfo = dataMartAssetMapper.queryElementInfo(dataQuotaTaskOut.getElementId());
                String url = "";
                Connection connect = null;
                if (elementInfo.getKbsEnable() == 0) {
                    url = "jdbc:hive2://" + elementInfo.getDbIp() + ":" + elementInfo.getDbPort() + "/" + dataQuotaTaskOut.getDbName();
                    String password = elementInfo.getDbPassword();
                    if(StringUtils.isNotEmpty(password)) {
                        password = BtoaEncode.decrypt(password);
                    }
                    connect = HiveUtil.getConnect(url, elementInfo.getDbUserName(), password, "0", null, null, null);
                } else {
                    BASE64Decoder decoder = new BASE64Decoder();
                    url = elementInfo.getJdbcUrl();
                    String keyTabPath = elementInfo.getKeyTabPath();
                    String krb5ConfPath = elementInfo.getKrb5ConfPath();
                    if(StringUtils.isNotEmpty(keyTabPath)) {
                        keyTabPath = new String(decoder.decodeBuffer(keyTabPath));
                    }
                    if(StringUtils.isNotEmpty(krb5ConfPath)) {
                        krb5ConfPath = new String(decoder.decodeBuffer(krb5ConfPath));
                    }
                    connect = HiveUtil.getConnect(url, null, null, "1", elementInfo.getDbUserName(), keyTabPath, krb5ConfPath);
                }
                item.setDataCount(HiveUtil.countTableLine(connect, dataQuotaTaskOut.getTableName()));
                item.setStorageSize(HiveUtil.countTableSpace(connect, dataQuotaTaskOut.getDbName(), dataQuotaTaskOut.getTableName()) + " M");
                HiveUtil.close(connect);
            } else if (ElementTypeEnum.MYSQL_TABLE.getCode().equals(dataQuotaTaskOut.getAssetType())) {
                ElementInfo elementInfo = dataMartAssetMapper.queryElementInfo(dataQuotaTaskOut.getElementId());
                String url = "jdbc:mysql://" + elementInfo.getDbIp() + ":" + elementInfo.getDbPort() + "/" + dataQuotaTaskOut.getDbName();
                String password = elementInfo.getDbPassword();
                if(StringUtils.isNotEmpty(password)) {
                    password = BtoaEncode.decrypt(password);
                }
                Connection connect = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_MYSQL, url, elementInfo.getDbUserName(), password);
                item.setDataCount(MysqlUtil.countTableLine(connect, dataQuotaTaskOut.getTableName()));
                item.setStorageSize(MysqlUtil.countTableSpace(connect, dataQuotaTaskOut.getDbName(), dataQuotaTaskOut.getTableName()) + " M");
                MysqlUtil.close(connect);
            }

        } catch (Exception e) {
            log.error("queryTotal 异常，{}", e.getMessage(), e);
        }

    }

    private ChElementDetailTable findOrAddCHTable(SqlLineageTableModel sourceTable, Long elementId, String dbName, String firstSnapshootVersion, String lastSnapshootVersion, String ip, String port, String userName, String password) {
        UserValueObject uvo = UmsUtils.getUVO();
        ChElementDetailTable exits = chElementDetailTableMapper.selectOne(new QueryWrapper<ChElementDetailTable>()
                .eq("tenant_id", uvo.getTenantId())
                .eq("element_id", elementId)
                .eq("db_name", StringUtil.isEmpty(sourceTable.getDatabase()) ? dbName : sourceTable.getDatabase())
                .eq("table_name", sourceTable.getTableName()));
        if (exits == null) {
            ChElementDetailTable detailTable = new ChElementDetailTable();
            detailTable.setElementId(elementId);
            detailTable.setDbName(StringUtil.isEmpty(sourceTable.getDatabase()) ? dbName : sourceTable.getDatabase());
            detailTable.setTableName(sourceTable.getTableName());
            detailTable.setCreateUser(uvo.getUserName());
            detailTable.setUpdateUser(uvo.getUserName());
            detailTable.setCreateTime(LocalDateTime.now());
            detailTable.setFirstSnapshootVersion(firstSnapshootVersion);
            detailTable.setLastSnapshootVersion(lastSnapshootVersion);
            detailTable.setTenantId(Long.parseLong(uvo.getTenantId() + ""));
            chElementDetailTableMapper.insert(detailTable);

            //新增字段
            String url = "jdbc:clickhouse://" + ip + ":" + port + "/" + detailTable.getDbName() + "?socket_timeout=4200000";
            Connection connect = ClickhouseUtil.getConnect(url, userName, password);
            try {
                if (connect != null) {
                    List<String> tableField = ClickhouseUtil.queryTableField(connect, detailTable.getTableName());
                    for (String field : tableField) {
                        ChElementDetailColumn column = new ChElementDetailColumn();
                        column.setElementId(elementId);
                        column.setDbName(detailTable.getDbName());
                        column.setTableName(detailTable.getTableName());
                        column.setColumnName(field);
                        column.setCreateUser(uvo.getUserName());
                        column.setUpdateUser(uvo.getUserName());
                        column.setCreateTime(LocalDateTime.now());
                        column.setUpdateTime(LocalDateTime.now());
                        column.setFirstSnapshootVersion(firstSnapshootVersion);
                        column.setLastSnapshootVersion(lastSnapshootVersion);
                        column.setTenantId(Long.parseLong(uvo.getTenantId() + ""));
                        chElementDetailColumnMapper.insert(column);
                    }
                }
            } catch (Exception e) {
                log.error("查询字段错误", e);
            } finally {
                if (connect != null) {
                    ClickhouseUtil.close(connect);
                }
            }
            return detailTable;
        }
        return exits;
    }

    private QuaWebMysqlElementDetailTable findOrAddMysqlTable(SqlLineageTableModel sourceTable, Long elementId, String dbName, String firstSnapshootVersion, String lastSnapshootVersion, String ip, String port, String userName, String password) {
        UserValueObject uvo = UmsUtils.getUVO();
        QuaWebMysqlElementDetailTable exits = mysqlElementDetailTableMapper.selectOne(new QueryWrapper<QuaWebMysqlElementDetailTable>()
                .eq("tenant_id", uvo.getTenantId())
                .eq("element_id", elementId)
                .eq("db_name", StringUtil.isEmpty(sourceTable.getDatabase()) ? dbName : sourceTable.getDatabase())
                .eq("table_name", sourceTable.getTableName()));
        if (exits == null) {
            QuaWebMysqlElementDetailTable detailTable = new QuaWebMysqlElementDetailTable();
            detailTable.setElementId(elementId);
            detailTable.setDbName(StringUtil.isEmpty(sourceTable.getDatabase()) ? dbName : sourceTable.getDatabase());
            detailTable.setTableName(sourceTable.getTableName());
            detailTable.setCreateUser(uvo.getUserName());
            detailTable.setUpdateUser(uvo.getUserName());
            detailTable.setCreateTime(LocalDateTime.now());
            detailTable.setFirstSnapshootVersion(firstSnapshootVersion);
            detailTable.setLastSnapshootVersion(lastSnapshootVersion);
            detailTable.setTenantId(Long.parseLong(uvo.getTenantId() + ""));
            mysqlElementDetailTableMapper.insert(detailTable);

            //新增字段
            //新增字段
            String url = "jdbc:mysql://" + ip + ":" + port + "/" + detailTable.getDbName() + "?serverTimezone=Asia/Shanghai&connectTimeout=30000&useSSL=false";
            Connection connect = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_MYSQL, url, userName, password);
            try {
                if (connect != null) {
                    List<String> tableField = MysqlUtil.queryTableField(connect, detailTable.getTableName());
                    for (String field : tableField) {
                        QuaWebMysqlElementDetailColumn column = new QuaWebMysqlElementDetailColumn();
                        column.setElementId(elementId);
                        column.setDbName(detailTable.getDbName());
                        column.setTableName(detailTable.getTableName());
                        column.setColumnName(field);
                        column.setCreateUser(uvo.getUserName());
                        column.setUpdateUser(uvo.getUserName());
                        column.setCreateTime(LocalDateTime.now());
                        column.setUpdateTime(LocalDateTime.now());
                        column.setFirstSnapshootVersion(firstSnapshootVersion);
                        column.setLastSnapshootVersion(lastSnapshootVersion);
                        column.setTenantId(Long.parseLong(uvo.getTenantId() + ""));
                        mysqlElementDetailColumnMapper.insert(column);
                    }
                }
            } catch (Exception e) {
                log.error("查询字段错误", e);
            } finally {
                if (connect != null) {
                    MysqlUtil.close(connect);
                }
            }
            return detailTable;
        }
        return exits;
    }

    private QuaWebHiveElementDetailTable findOrAddHiveTable(SqlLineageTableModel sourceTable, Long elementId, String dbName, String firstSnapshootVersion, String lastSnapshootVersion, String hiveUrl, String clusterId, String clusterUserId) {
        UserValueObject uvo = UmsUtils.getUVO();
        QuaWebHiveElementDetailTable exits = hiveElementDetailTableMapper.selectOne(new QueryWrapper<QuaWebHiveElementDetailTable>()
                .eq("tenant_id", uvo.getTenantId())
                .eq("element_id", elementId)
                .eq("db_name", StringUtil.isEmpty(sourceTable.getDatabase()) ? dbName : sourceTable.getDatabase())
                .eq("table_name", sourceTable.getTableName()));
        if (exits == null) {
            QuaWebHiveElementDetailTable detailTable = new QuaWebHiveElementDetailTable();
            detailTable.setElementId(elementId);
            detailTable.setDbName(StringUtil.isEmpty(sourceTable.getDatabase()) ? dbName : sourceTable.getDatabase());
            detailTable.setTableName(sourceTable.getTableName());
            detailTable.setCreateUser(uvo.getUserName());
            detailTable.setUpdateUser(uvo.getUserName());
            detailTable.setCreateTime(LocalDateTime.now());
            detailTable.setFirstSnapshootVersion(firstSnapshootVersion);
            detailTable.setLastSnapshootVersion(lastSnapshootVersion);
            detailTable.setTenantId(Long.parseLong(uvo.getTenantId() + ""));
            hiveElementDetailTableMapper.insert(detailTable);

            //新增字段
            Properties properties = initProperties();
            String kerbFile = properties.getProperty("java.security.krb5.conf");
            String kebtabFile = kebtab(clusterId, clusterUserId);
            Connection connect = HiveUtil.getConnect(hiveUrl, null, null, "1", "hive/_HOST@XTSDCKDC", kebtabFile, kerbFile);
            try {
                if (connect != null) {
                    List<String> tableField = HiveUtil.queryTableField(connect, detailTable.getTableName());
                    for (String field : tableField) {
                        QuaWebHiveElementDetailColumn column = new QuaWebHiveElementDetailColumn();
                        column.setElementId(elementId);
                        column.setDbName(detailTable.getDbName());
                        column.setTableName(detailTable.getTableName());
                        column.setColumnName(field);
                        column.setCreateUser(uvo.getUserName());
                        column.setUpdateUser(uvo.getUserName());
                        column.setCreateTime(LocalDateTime.now());
                        column.setUpdateTime(LocalDateTime.now());
                        column.setFirstSnapshootVersion(firstSnapshootVersion);
                        column.setLastSnapshootVersion(lastSnapshootVersion);
                        column.setTenantId(Long.parseLong(uvo.getTenantId() + ""));
                        hiveElementDetailColumnMapper.insert(column);
                    }
                }
            } catch (Exception e) {
                log.error("查询字段错误", e);
            } finally {
                if (connect != null) {
                    HiveUtil.close(connect);
                }
            }
            return detailTable;
        }
        return exits;
    }

    private String kebtab(String clusterId, String clusterUserId) {
        String fileName = "user.keytab";
        int hashCode = (clusterId + "_" + clusterUserId).hashCode();
        String dirPath = (kbsFilePath + File.separator + hashCode);
        if (FileUtil.exist(dirPath)) {
            FileUtil.mkdir(dirPath);
        }
        String filePath = dirPath + File.separator + fileName;
        if (FileUtil.exist(filePath)) {
            //新建文件并写入
            String keytabBase64 = this.baseMapper.keytab(clusterId, clusterUserId);
            byte[] keytabBytes = Base64.getDecoder().decode(keytabBase64);
            // 将解码后的字节写入文件
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                fos.write(keytabBytes);
            } catch (IOException e) {
                log.error("写入 keytab文件失败", e);
            }
        }
        return filePath;
    }


    private static Properties initProperties() {
        try {
            log.info("kerberos 配置文件：{}", NConst.KRB5_HIVE_PROP);
            File propFile = new File(NConst.KRB5_HIVE_PROP);
            if (!propFile.exists()) {
                log.info("kerberos配置文件不存在");
                return null;
            }
            FileInputStream fis = new FileInputStream(propFile);
            Properties properties = new Properties();
            properties.load(fis);
            fis.close();
            return properties;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    /**
     * 检查元数据数据是否存在
     *
     * @param flowId
     */
    private void checkElement(Long flowId) {

    }

    public static void main(String[] args) throws UnknownHostException {
        String jdbcUrl1 = "**************************************,xtsjzx-sdc-228-41-62:2181,xtsjzx-sdc-228-41-54:2181/mgrz_dwd;principal=hive/_HOST@XTSDCKDC;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2?tez.queue.name=root.bdoc.user_sdc";
        String jdbcUrl2 = "******************************,************:2181,************:2181/sdc_ods;principal=hive/_HOST@XTSDCKDC;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2";

        boolean result = JdbcUtil.areJdbcUrlsEqual(jdbcUrl1, jdbcUrl2);
        System.out.println("这两个 JDBC URL 是否是同一个连接？ " + result);
    }

}
