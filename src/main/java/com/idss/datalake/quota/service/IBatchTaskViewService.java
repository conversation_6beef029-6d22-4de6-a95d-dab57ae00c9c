package com.idss.datalake.quota.service;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datamart.dto.request.AssetMapGroupRequest;
import com.idss.datalake.datamart.dto.response.AssetMapView;
import com.idss.datalake.quota.dto.*;
import com.idss.datalake.quota.entity.BatchTaskView;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.quota.entity.DataQuotaTask;
import com.idss.datalake.quota.entity.DataQuotaTaskOut;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * VIEW 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
public interface IBatchTaskViewService extends IService<BatchTaskView> {

    /**
     * 指标统计
     * @return
     */
    Map<String,Object> viewCount();

    /**
     * 离线任务下拉
     *
     * @return
     */
    List<QuotaFlow> flowList();

    /**
     * 离线详情
     * @param flowId
     * @return
     */
    FlowDetailDto flowDetail(Long flowId);

    /**实时任务下拉
     * @return
     */
    List<QuotaFlow> streamFlowList();

    /**
     * 实时详情
     * @param flowId
     * @return
     */
    FlowDetailDto streamFlowDetail(Long flowId);

    /**
     * 解析所有输入表、输出表
     *
     * @param flowId
     * @return
     */
    QuotaTable tableInfo(Long flowId);

    /**
     * 新增或编辑
     *
     * @param request
     * @return
     */
    ResultBean addOrUpdateQuota(AddOrUpdateQuota request);

    /**
     * 分页查询
     *
     * @param requestDto
     * @return
     */
    BasePageResponse<List<DataQuotaTask>> pageQuery(DataQuotaTaskPageRequest requestDto);

    /**
     * 详情
     * @param id
     * @return
     */
    ResultBean detail(Long id);

    /**
     * 删除
     * @param id
     * @return
     */
    ResultBean remove(Long id);

    /**
     * 自建表分页
     * @param requestDto
     * @return
     */
    BasePageResponse<List<DataQuotaTaskOut>> selfPageQuery(AssetMapGroupRequest requestDto);

    /**
     * 租户统计
     * @param searchName
     * @return
     */
    ResultBean tenantGroup(String  searchName);

    /**
     * 用户表分页
     * @param requestDto
     * @return
     */
    BasePageResponse<List<DataQuotaTaskOut>> tenantPage(AssetMapGroupRequest requestDto);


    /**
     * 资产详情
     *
     * @param id
     * @return
     */
    ResultBean assetDetail(Long id);


    /**
     * 字段信息
     * @param id
     * @return
     */
    ResultBean columnList(Long id);

    /**
     * 预览数据
     * @param id
     * @return
     * @throws Exception
     */
    ResultBean previewData(Long id) throws Exception;
}
