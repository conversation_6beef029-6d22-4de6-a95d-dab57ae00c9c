package com.idss.datalake.quota.dto;

import com.idss.datalake.quota.entity.DataQuotaTaskInput;
import com.idss.datalake.quota.entity.DataQuotaTaskOut;
import lombok.Data;

import java.util.List;

@Data
public class AddOrUpdateQuota {

    private Long id;
    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型：0离线，1实时
     */
    private Integer taskType;

    /**
     * 数澜任务 ID
     */
    private Long flowId;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 输出表选中的数据
     */
    private List<DataQuotaTaskOut> taskOuts;
    /**
     * 输入表数据
     */
    private List<DataQuotaTaskInput> taskInputs;
}
