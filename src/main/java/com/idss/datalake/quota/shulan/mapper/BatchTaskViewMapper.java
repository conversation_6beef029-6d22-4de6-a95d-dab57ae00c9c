package com.idss.datalake.quota.shulan.mapper;

import com.idss.datalake.quota.dto.QuotaFlow;
import com.idss.datalake.quota.entity.BatchTaskView;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * VIEW Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
public interface BatchTaskViewMapper extends BaseMapper<BatchTaskView> {
    /**
     * 任务下拉
     * @return
     */
    List<QuotaFlow> flowList();

    /**
     * 实时任务
     * @return
     */
    List<QuotaFlow> streamFlowList();

    /**
     * 查询任务下的所有脚本
     * @param flowId
     * @return
     */
    List<BatchTaskView> listByFlowId(Long flowId);

    /**
     * 获取 keytab
     * @param clusterId
     * @param clusterUserId
     * @return
     */
    String keytab(String clusterId,String clusterUserId);
}
