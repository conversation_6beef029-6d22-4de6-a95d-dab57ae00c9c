package com.idss.datalake.datasource.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TenantDefineDatasourcesInfo {
    /**
     * 数据源唯一标识符
     */
    private Integer id;

    /**
     * 数据源名称
     */
    private String name;

    /**
     * 数据源类型
     */
    private String type;

    /**
     * 数据源主机地址
     */
    private String host;

    /**
     * 数据源端口号
     */
    private Integer port;

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 数据库模式
     */
    private String schemaName;

    /**
     * Elasticsearch索引前缀
     */
    private String esIndexPrefix;

    /**
     * Kafka主题前缀
     */
    private String kfkTopicPrefix;

    /**
     * Kafka ZK主机地址
     */
    private String kfkZkHost;

    /**
     * Kafka ZK端口号
     */
    private Integer kfkZkPort;

    /**
     * Oracle服务名或SID
     */
    private String oraServiceOrSid;

    /**
     * hive jdbc url
     */
    private String hiveJdbcUrl;

    /**
     * jdbc连接参数
     */
    private String jdbcParam;

    /**
     * 数据源描述信息
     */
    private String description;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 租户名称
     */
    private String tenantName;


    /**
     * 连接状态 `connected` `disconnected` `error`
     */
    private String status;

    /**
     * 认证方式 none , credential凭证认证 , kerberos 认证, certificate SSL认证
     */
    private String authType;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;


    /**
     * 证书内容
     */
    private String certificateContentBase64;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * Keytab文件内容
     */
    private String keytabContentBase64;
    /**
     * krb5文件内容
     */
    private String krb5ContentBase64;
    /**
     * Kerberos主体
     */
    private String principal;
}
