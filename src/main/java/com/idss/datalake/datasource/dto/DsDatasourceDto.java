package com.idss.datalake.datasource.dto;

import lombok.Data;

import java.util.Map;

@Data
public class DsDatasourceDto {
    private Integer id;
    private String connectType;
    private String type;
    private String name;

    private String note;

    private String host;

    private Integer port;

    private String database;

    private String userName;

    private String password;

    private Map<String, String> other;

    private String kbsVersion;
    private String kbsPrincipal;
    private String kbsKeytabOriginFileName;
    private String kbsKeytabFilePath;
    private String kbsKrbOriginFileName;
    private String kbsKrbFilePath;
    private String kbsJaasOriginFileName;
    private String kbsJaasFilePath;

    private String javaSecurityKrb5Conf;
    private String loginUserKeytabPath;
    private String loginUserKeytabUsername;
}
