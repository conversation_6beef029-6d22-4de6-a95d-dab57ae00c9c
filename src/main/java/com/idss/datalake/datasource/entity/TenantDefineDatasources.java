package com.idss.datalake.datasource.entity;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据源管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TenantDefineDatasources implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据源唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 数据源名称
     */
    private String name;

    /**
     * 数据源类型
     */
    private String type;

    /**
     * 数据源主机地址
     */
    private String host;

    /**
     * 数据源端口号
     */
    private Integer port;

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 数据库模式
     */
    private String schemaName;

    /**
     * Elasticsearch索引前缀
     */
    private String esIndexPrefix;

    /**
     * Kafka主题前缀
     */
    private String kfkTopicPrefix;

    /**
     * Kafka ZK主机地址
     */
    private String kfkZkHost;

    /**
     * Kafka ZK端口号
     */
    private Integer kfkZkPort;

    /**
     * Oracle服务名或SID
     */
    private String oraServiceOrSid;

    /**
     * 数据源描述信息
     */
    private String description;

    /**
     * 连接状态 `connected` `disconnected` `error`
     */
    private String status;

    /**
     * 认证方式 none , credential凭证认证 , kerberos 认证, certificate SSL认证
     */
    @TableField(exist = false)
    private String authType;
    /**
     * 认证值
     */
    @TableField(exist = false)
    private Integer authValue;

    /**
     * 是否启用Kerberos认证
     */
    private Boolean kerberosEnabled;

    /**
     * 关联的Kerberos配置ID
     */
    private Integer kerberosConfigId;

    /**
     * 是否启用凭证
     */
    private Boolean credentialsEnabled;

    /**
     * 关联的凭证ID
     */
    private Integer credentialsConfigId;

    /**
     * 是否启用证书
     */
    private Boolean certificatesEnabled;

    /**
     * 关联的证书ID
     */
    private Integer certificatesConfigId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * hive jdbc url
     */
    private String hiveJdbcUrl;

    /**
     * 连接信息
     */
    @TableField(exist = false)
    private String connectInfo;

    /**
     * jdbc连接参数
     */
    private String jdbcParam;
}
