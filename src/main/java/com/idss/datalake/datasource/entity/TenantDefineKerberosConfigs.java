package com.idss.datalake.datasource.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * Kerberos认证配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TenantDefineKerberosConfigs implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Kerberos配置唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 配置名称
     */
    private String name;

    /**
     * Kerberos主体
     */
    private String principal;

    /**
     * Keytab文件
     */
    private String keytabPath;

    private String serviceName;

    @TableField(exist = false)
    private String keytabFileName;

    /**
     * krb5文件
     */
    private String krb5Path;
    @TableField(exist = false)
    private String krb5FileName;;

    /**
     * Keytab是否已上传
     */
    private Boolean keytabUploaded;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 租户名称
     */
    private String tenantName;


    /**
     * 引用次数
     */
    @TableField(exist = false)
    private Integer refCnt;

    /**
     * 引用描述
     */
    @TableField(exist = false)
    private List<String> refDesc;

}
