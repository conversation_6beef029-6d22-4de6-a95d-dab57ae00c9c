package com.idss.datalake.datasource.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * SSL证书管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TenantDefineCertificates implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 证书唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 证书名称
     */
    private String name;

    /**
     * 证书类型 'client' 客户端证书，'server' 服务端证书，'ca' CA根证书
     */
    private String type;

    /**
     * 证书主体
     */
    private String subject;

    /**
     * 证书颁发者
     */
    private String issuer;

    /**
     * 证书生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime validFrom;

    /**
     * 证书过期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime validTo;

    /**
     * 证书指纹
     */
    private String fingerprint;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 证书文件路径
     */
    private String certificatePath;

    @TableField(exist = false)
    private String fileName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 租户名称
     */
    private String tenantName;


    /**
     * 引用次数
     */
    @TableField(exist = false)
    private Integer refCnt;

    /**
     * 引用描述
     */
    @TableField(exist = false)
    private List<String> refDesc;


}
