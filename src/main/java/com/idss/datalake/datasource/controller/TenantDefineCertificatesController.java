package com.idss.datalake.datasource.controller;


import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.util.CertificateUtils;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datasource.dto.TenantDefineCertificatesPageRequest;
import com.idss.datalake.datasource.dto.TenantDefineKerberosConfigsPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineCertificates;
import com.idss.datalake.datasource.entity.TenantDefineDatasources;
import com.idss.datalake.datasource.entity.TenantDefineKerberosConfigs;
import com.idss.datalake.datasource.service.ITenantDefineCertificatesService;
import com.idss.datalake.datasource.service.ITenantDefineDatasourcesService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import sun.misc.BASE64Decoder;

import java.io.IOException;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * SSL证书管理表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@RestController
@RequestMapping("/datasource/certificates")
@Slf4j
public class TenantDefineCertificatesController {
    @Autowired
    private ITenantDefineCertificatesService certificatesService;
    @Autowired
    private ITenantDefineDatasourcesService datasourcesService;

    /**
     * 证书信息
     *
     * @return
     */
    @PostMapping("/certInfo")
    public ResultBean certInfo(@RequestBody Map<String, String> params) {
        String path = params.get("path");
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            String filePath = new String(decoder.decodeBuffer(path));
            X509Certificate certificate = CertificateUtils.loadCertificate(filePath);
            TenantDefineCertificates certificates = new TenantDefineCertificates();
            certificates.setType(CertificateUtils.type(certificate));
            certificates.setSubject(CertificateUtils.subject(certificate));
            certificates.setIssuer(CertificateUtils.issuer(certificate));
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            certificates.setValidFrom(LocalDateTime.parse(CertificateUtils.validTimeStart(certificate), formatter));
            certificates.setValidTo(LocalDateTime.parse(CertificateUtils.validTimeEnd(certificate), formatter));
            certificates.setFingerprint(CertificateUtils.sha1Fingerprint(certificate));
            return ResultBean.success(certificates);
        } catch (Exception e) {
            log.error("文件读取错误", e);
            return ResultBean.fail();
        }
    }

    /**
     * 添加或修改证书信息
     *
     * @param certificates
     * @return
     */
    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody TenantDefineCertificates certificates) {
        try {
            if (certificates.getId() == null) {
                //新增
                UserValueObject uvo = UmsUtils.getUVO();
                if (certificates.getName() == null || certificates.getName().trim().isEmpty()) {
                    return ResultBean.fail("证书名称不能为空");
                }
                if (certificates.getCertificatePath() == null || certificates.getCertificatePath().trim().isEmpty()) {
                    return ResultBean.fail("证书文件路径不能为空");
                }

                certificates.setTenantId(uvo.getTenantId());
                certificates.setTenantName(uvo.getUserName());
                certificates.setCreateTime(LocalDateTime.now());
                certificates.setCreateUser(uvo.getUserName());
                certificates.setUpdateUser(uvo.getUserName());
                certificatesService.save(certificates);
            } else {
                // 修改
                certificates.setUpdateTime(LocalDateTime.now());
                certificates.setUpdateUser(UmsUtils.getUVO().getUserName());
                certificatesService.updateById(certificates);
            }
            return ResultBean.success();
        } catch (Exception e) {
            log.error("操作失败，{}", e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    /**
     * 删除证书信息
     *
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    public ResultBean delete(@PathVariable("id") Long id) {
        // 校验是否被datasource引用
        int count = datasourcesService.count(new QueryWrapper<TenantDefineDatasources>().eq("certificates_config_id", id));
        if (count > 0) {
            return ResultBean.fail("该SSL配置被引用，无法删除");
        }
        certificatesService.removeById(id);
        return ResultBean.success();
    }


    /**
     * 证书详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        TenantDefineCertificates certificates = certificatesService.getById(id);
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            certificates.setFileName(FileUtil.getName(new String(decoder.decodeBuffer(certificates.getCertificatePath()))));
        } catch (IOException e) {
            return ResultBean.fail("解析异常");
        }
        return ResultBean.success(certificates);
    }

    /**
     * 证书分页
     *
     * @param request
     * @return
     */
    @PostMapping("/page")
    public BasePageResponse<List<TenantDefineCertificates>> page(@RequestBody TenantDefineCertificatesPageRequest request) {
        return certificatesService.page(request);
    }

}
