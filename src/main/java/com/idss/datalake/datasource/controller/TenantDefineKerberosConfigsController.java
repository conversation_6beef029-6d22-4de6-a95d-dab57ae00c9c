package com.idss.datalake.datasource.controller;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datashare.cluster.entity.TbCluster;
import com.idss.datalake.datashare.cluster.service.ITbClusterService;
import com.idss.datalake.datashare.tenant.service.ITbDatasourceInfoService;
import com.idss.datalake.datasource.dto.TenantDefineKerberosConfigsPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineDatasources;
import com.idss.datalake.datasource.entity.TenantDefineKerberosConfigs;
import com.idss.datalake.datasource.service.ITenantDefineDatasourcesService;
import com.idss.datalake.datasource.service.ITenantDefineKerberosConfigsService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sun.misc.BASE64Decoder;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Kerberos认证配置表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@RestController
@RequestMapping("/datasource/kerberos")
@Slf4j
public class TenantDefineKerberosConfigsController {
    @Autowired
    private ITenantDefineKerberosConfigsService kerberosConfigsService;
    @Autowired
    private ITenantDefineDatasourcesService datasourcesService;
    @Autowired
    private ITbClusterService clusterService;
    @Autowired
    private ITbDatasourceInfoService tbDatasourceInfoService;

    /**
     * 添加新的Kerberos配置或更新现有的配置.
     *
     * @param configs Kerberos配置对象，其中包含细节，例如主，keytab路径，KRB5路径，租户信息等
     * @return 结果表明操作的成功或失败以及任何相关消息或数据。
     */
    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody TenantDefineKerberosConfigs configs) {
        try {
            if (configs.getId() == null) {
                //新增
                UserValueObject uvo = UmsUtils.getUVO();
                if (configs.getName() == null || configs.getName().trim().isEmpty()) {
                    return ResultBean.fail("配置名称不能为空");
                }
                if (configs.getPrincipal() == null || configs.getPrincipal().trim().isEmpty()) {
                    return ResultBean.fail("Kerberos主体不能为空");
                }
                if (configs.getKeytabPath() == null || configs.getKeytabPath().trim().isEmpty()) {
                    return ResultBean.fail("Keytab文件路径不能为空");
                }
                if (configs.getKrb5Path() == null || configs.getKrb5Path().trim().isEmpty()) {
                    return ResultBean.fail("krb5文件路径不能为空");
                }
                configs.setTenantId(uvo.getTenantId());
                configs.setTenantName(uvo.getUserName());
                configs.setCreateTime(LocalDateTime.now());
                configs.setCreateUser(uvo.getUserName());
                configs.setUpdateUser(uvo.getUserName());
                kerberosConfigsService.save(configs);
            } else {
                // 修改
                configs.setUpdateTime(LocalDateTime.now());
                configs.setUpdateUser(UmsUtils.getUVO().getUserName());

                // 集群修改
                boolean update = kerberosConfigsService.updateById(configs);
                if (update) {
                    // 更新关联的tb_cluster表cluster_username，cluster_password字段，以及tb_datasource_info datasource_username，datasource_password字段
                    List<TbCluster> clusterList = clusterService.list(new LambdaUpdateWrapper<TbCluster>().eq(TbCluster::getClusterAuthType, 2)
                            .eq(TbCluster::getAuthConfigurationId, configs.getId()));
                    if (CollectionUtils.isNotEmpty(clusterList)) {
                        for (TbCluster tbCluster : clusterList) {
                            tbCluster.setClusterUsername(null);
                            tbCluster.setClusterPassword(null);
                            tbCluster.setEnableKbs("1");
                            tbCluster.setKbsAccount(configs.getPrincipal());
                            tbCluster.setKrb5ConfPath(Base64.decodeStr(configs.getKeytabPath()));
                            tbCluster.setKeyTabPath(Base64.decodeStr(configs.getKrb5Path()));
                            String jdbcUrl = Constant.DATASOURCE_URL_HIVE + tbCluster.getNodeAddress();
                            if (StringUtils.isNotBlank(tbCluster.getConnectionParams())) {
                                jdbcUrl += tbCluster.getConnectionParams();
                            }
                            tbCluster.setJdbcUrl(jdbcUrl);
                        }
                        clusterService.updateBatchById(clusterList);
                    }
                }
            }
            return ResultBean.success();
        } catch (Exception e) {
            log.error("kerberosConfigsService.tenantClusterUpdate error: {}", e.getMessage(), e);
            return ResultBean.fail();
        }

    }

    /**
     * 删除Kerberos配置.
     *
     * @param id 要删除的Kerberos配置的ID
     * @return 结果表明操作的成功或失败以及任何相关消息或数据。
     */
    @GetMapping("/delete/{id}")
    public ResultBean delete(@PathVariable("id") Long id) {
        if (id == null) {
            return ResultBean.fail("id不能为空");
        }
        // 校验是否被datasource引用
        int count = datasourcesService.count(new QueryWrapper<TenantDefineDatasources>().eq("kerberos_config_id", id));
        if (count > 0) {
            return ResultBean.fail("该Kerberos配置被引用，无法删除");
        }
        kerberosConfigsService.removeById(id);
        return ResultBean.success();
    }

    /**
     * 获取Kerberos配置的详细信息.
     *
     * @param id 要获取详细信息的Kerberos配置的ID
     * @return 结果表明操作的成功或失败以及任何相关消息或数据。
     */
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        if (id == null) {
            return ResultBean.fail("id不能为空");
        }
        TenantDefineKerberosConfigs configs = kerberosConfigsService.getById(id);
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            configs.setKeytabFileName(FileUtil.getName(new String(decoder.decodeBuffer(configs.getKeytabPath()))));
            configs.setKrb5FileName(FileUtil.getName(new String(decoder.decodeBuffer(configs.getKrb5Path()))));
        } catch (IOException e) {
            return ResultBean.fail("解析异常");
        }
        return ResultBean.success(configs);
    }

    /**
     * 获取Kerberos配置的分页列表.
     *
     * @param request 包含分页信息的请求对象
     * @return 结果表明操作的成功或失败以及任何相关消息或数据。
     */
    @PostMapping("/page")
    public BasePageResponse<List<TenantDefineKerberosConfigs>> page(@RequestBody TenantDefineKerberosConfigsPageRequest request) {
        return kerberosConfigsService.page(request);
    }
}
