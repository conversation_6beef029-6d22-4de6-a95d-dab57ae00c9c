package com.idss.datalake.datasource.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.util.AESUtil;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datashare.cluster.entity.TbCluster;
import com.idss.datalake.datashare.cluster.service.ITbClusterService;
import com.idss.datalake.datashare.tenant.entity.TbDatasourceInfo;
import com.idss.datalake.datashare.tenant.service.ITbDatasourceInfoService;
import com.idss.datalake.datasource.dto.TenantDefineCredentialsPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineCredentials;
import com.idss.datalake.datasource.entity.TenantDefineDatasources;
import com.idss.datalake.datasource.service.ITenantDefineCredentialsService;
import com.idss.datalake.datasource.service.ITenantDefineDatasourcesService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * 用户凭证管理表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@RestController
@RequestMapping("/datasource/credentials")
@Slf4j
public class TenantDefineCredentialsController {
    @Autowired
    private ITenantDefineCredentialsService tenantDefineCredentialsService;
    @Autowired
    private ITenantDefineDatasourcesService datasourcesService;
    @Autowired
    private ITbClusterService clusterService;
    @Autowired
    private ITbDatasourceInfoService tbDatasourceInfoService;

    /**
     * 新增或修改凭证信息
     *
     * @param credentials
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody TenantDefineCredentials credentials) {
        try {
            if (credentials.getId() == null) {
                UserValueObject uvo = UmsUtils.getUVO();
                // 新增
                if (credentials.getName() == null || credentials.getName().trim().isEmpty()) {
                    return ResultBean.fail("配置名称不能为空");
                }
                if (credentials.getUsername() == null || credentials.getUsername().trim().isEmpty()) {
                    return ResultBean.fail("用户名不能为空");
                }
                // 检查名称是否已存在
                TenantDefineCredentials existing = tenantDefineCredentialsService.lambdaQuery()
                        .eq(TenantDefineCredentials::getName, credentials.getName())
                        .eq(TenantDefineCredentials::getTenantId, credentials.getTenantId())
                        .one();
                if (existing != null) {
                    return ResultBean.fail("配置名称已存在");
                }
                credentials.setTenantId(uvo.getTenantId());
                credentials.setTenantName(uvo.getUserName());
                credentials.setCreateTime(LocalDateTime.now());
                credentials.setCreateUser(uvo.getUserName());
                credentials.setUpdateUser(uvo.getUserName());
                return ResultBean.success(tenantDefineCredentialsService.save(credentials));
            } else {
                // 修改
                credentials.setUpdateTime(LocalDateTime.now());
                credentials.setUpdateUser(UmsUtils.getUVO().getUserName());
                boolean update = tenantDefineCredentialsService.updateById(credentials);
                if (update) {
                    // 更新关联的tb_cluster表cluster_username，cluster_password字段，以及tb_datasource_info datasource_username，datasource_password字段
                    List<TbCluster> clusterList = clusterService.list(new LambdaUpdateWrapper<TbCluster>().eq(TbCluster::getClusterAuthType, 1)
                            .eq(TbCluster::getAuthConfigurationId, credentials.getId()));
                    if (CollectionUtils.isNotEmpty(clusterList)) {
                        for (TbCluster tbCluster : clusterList) {
                            tbCluster.setClusterUsername(credentials.getUsername());
                            tbCluster.setClusterPassword(AESUtil.defaultEncrypt(credentials.getPassword()));
                            tbCluster.setEnableKbs("0");
                            tbCluster.setKbsAccount("");
                            tbCluster.setKrb5ConfPath("");
                            tbCluster.setJaasConfPath("");
                            tbCluster.setJdbcUrl("");

                            List<TbDatasourceInfo> datasourceInfoList = tbDatasourceInfoService.list(new LambdaUpdateWrapper<TbDatasourceInfo>()
                                    .eq(TbDatasourceInfo::getClusterId, tbCluster.getId()));
                            if (CollectionUtils.isNotEmpty(datasourceInfoList)) {
                                for (TbDatasourceInfo datasourceInfo : datasourceInfoList) {
                                    datasourceInfo.setDatasourceUsername(credentials.getUsername());
                                    datasourceInfo.setDatasourcePassword(AESUtil.defaultEncrypt(credentials.getPassword()));
                                }
                                tbDatasourceInfoService.updateBatchById(datasourceInfoList);

                                List<Long> relatedTenantIds = datasourceInfoList.stream().map(TbDatasourceInfo::getTenantId).collect(toList());
                                clusterService.tenantClusterUpdate(relatedTenantIds);
                            }
                        }
                        clusterService.updateBatchById(clusterList);
                    }
                }
                return ResultBean.success();
            }
        } catch (Exception e) {
            log.error("kerberosConfigsService.tenantClusterUpdate error: {}", e.getMessage(), e);
            return ResultBean.fail();
        }
    }

    /**
     * 删除凭证信息
     *
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    public ResultBean delete(@PathVariable("id") Long id) {
        // 校验是否被datasource引用
        int count = datasourcesService.count(new QueryWrapper<TenantDefineDatasources>().eq("credentials_config_id", id));
        if (count > 0) {
            return ResultBean.fail("该SSL配置被引用，无法删除");
        }
        return ResultBean.success(tenantDefineCredentialsService.removeById(id));
    }

    /**
     * 凭证详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        return ResultBean.success(tenantDefineCredentialsService.getById(id));
    }

    /**
     * 凭证分页
     *
     * @param request
     * @return
     */
    @PostMapping("/page")
    public BasePageResponse<List<TenantDefineCredentials>> page(@RequestBody TenantDefineCredentialsPageRequest request) {
        return tenantDefineCredentialsService.page(request);
    }

}
