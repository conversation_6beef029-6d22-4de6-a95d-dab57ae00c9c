package com.idss.datalake.datasource.controller;


import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datasource.dto.TenantDefineDatasourcePageRequest;
import com.idss.datalake.datasource.dto.TenantDefineDatasourcesInfo;
import com.idss.datalake.datasource.entity.TenantDefineCertificates;
import com.idss.datalake.datasource.entity.TenantDefineCredentials;
import com.idss.datalake.datasource.entity.TenantDefineDatasources;
import com.idss.datalake.datasource.entity.TenantDefineKerberosConfigs;
import com.idss.datalake.datasource.service.ITenantDefineCertificatesService;
import com.idss.datalake.datasource.service.ITenantDefineCredentialsService;
import com.idss.datalake.datasource.service.ITenantDefineDatasourcesService;
import com.idss.datalake.datasource.service.ITenantDefineKerberosConfigsService;
import com.idss.datalake.datasource.util.DatasourceConnectUtil;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import sun.misc.BASE64Decoder;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * 数据源管理表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@RestController
@RequestMapping("/datasource")
@Slf4j
public class TenantDefineDataSourcesController {
    @Autowired
    private ITenantDefineDatasourcesService datasourcesService;
    @Autowired
    private ITenantDefineKerberosConfigsService kerberosConfigsService;
    @Autowired
    private ITenantDefineCredentialsService credentialsService;
    @Autowired
    private ITenantDefineCertificatesService certificatesService;

    /**
     * 处理数据源管理表 前端控制器
     */
    @GetMapping("/type")
    public ResultBean type() {
        return ResultBean.success(DATA_SOURCE_TYPE_ENUM.values());
    }


    private Properties loadPropertiesFromJson(String jsonString) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        // 将JSON解析为Map
        Map<String, Object> map = mapper.readValue(jsonString, Map.class);

        // 创建Properties对象并填充数据
        Properties properties = new Properties();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            properties.put(entry.getKey(), entry.getValue().toString());
        }

        return properties;
    }


    /**
     * 对外同步数据源
     *
     * @return
     */
    @GetMapping("/sync")
    public ResultBean syncDataSource() {
        List<TenantDefineDatasourcesInfo> result = new ArrayList<>();
        List<TenantDefineDatasources> tenantDefineDatasources = datasourcesService.list(new QueryWrapper<TenantDefineDatasources>().eq("status",
                "connected"));
        if (CollectionUtils.isNotEmpty(tenantDefineDatasources)) {
            for (TenantDefineDatasources datasource : tenantDefineDatasources) {
                TenantDefineDatasourcesInfo info = new TenantDefineDatasourcesInfo();
                info.setId(datasource.getId());
                info.setName(datasource.getName());
                info.setType(datasource.getType());
                info.setHost(datasource.getHost());
                info.setPort(datasource.getPort());
                info.setDbName(datasource.getDbName());
                info.setSchemaName(datasource.getSchemaName());
                info.setEsIndexPrefix(datasource.getEsIndexPrefix());
                info.setKfkTopicPrefix(datasource.getKfkTopicPrefix());
                info.setKfkZkHost(datasource.getKfkZkHost());
                info.setKfkZkPort(datasource.getKfkZkPort());
                info.setOraServiceOrSid(datasource.getOraServiceOrSid());
                info.setHiveJdbcUrl(datasource.getHiveJdbcUrl());
                info.setJdbcParam(datasource.getJdbcParam());
                info.setDescription(datasource.getDescription());
                info.setTenantId(datasource.getTenantId());
                info.setTenantName(datasource.getTenantName());
                info.setStatus(datasource.getStatus());
                info.setAuthType(datasource.getAuthType());
                info.setUpdateTime(datasource.getUpdateTime());

                if (datasource.getKerberosEnabled()) {
                    TenantDefineKerberosConfigs kerberosConfigs = kerberosConfigsService.getById(datasource.getKerberosConfigId());
                    info.setAuthType("kerberos");
                    info.setPrincipal(kerberosConfigs.getPrincipal());
                    String krb5Path = new String(Base64Decoder.decode(kerberosConfigs.getKrb5Path()));
                    String keytabPath = new String(Base64Decoder.decode(kerberosConfigs.getKeytabPath()));
                    info.setKrb5ContentBase64(Base64Encoder.encode(FileUtil.readBytes(krb5Path)));
                    info.setKeytabContentBase64(Base64Encoder.encode(FileUtil.readBytes(keytabPath)));
                } else if (datasource.getCredentialsEnabled()) {
                    TenantDefineCredentials credentials = credentialsService.getById(datasource.getCredentialsConfigId());
                    info.setAuthType("credential");
                    info.setUsername(credentials.getUsername());
                    info.setPassword(credentials.getPassword());
                } else if (datasource.getCertificatesEnabled()) {
                    TenantDefineCertificates certificates = certificatesService.getById(datasource.getCertificatesConfigId());
                    info.setAuthType("certificate");
                    String certificatePath = new String(Base64Decoder.decode(certificates.getCertificatePath()));
                    info.setCertificateContentBase64(Base64Encoder.encode(FileUtil.readBytes(certificatePath)));
                }

                result.add(info);
            }
        }
        return ResultBean.success(result);
    }


    /**
     * 处理数据源测试连接请求的控制器方法
     *
     * @param dataSources 包含数据源信息的请求体，通过@RequestBody注解读取请求体中的数据
     * @return 返回一个ResultBean对象，表示测试连接操作的结果
     */
    @PostMapping("/testConnection")
    public ResultBean testConnection(@RequestBody TenantDefineDatasources dataSources) {
        try {
            // 校验必要参数
            if (dataSources.getType() == null || dataSources.getType().trim().isEmpty()) {
                return ResultBean.fail("数据源类型不能为空");
            }

            if (dataSources.getAuthType().equals("none")) {
                dataSources.setKerberosEnabled(false);
                dataSources.setKerberosConfigId(null);
                dataSources.setCertificatesEnabled(false);
                dataSources.setCertificatesConfigId(null);
                dataSources.setCredentialsEnabled(false);
                dataSources.setCredentialsConfigId(null);
            } else if (dataSources.getAuthType().equals("credential")) {
                dataSources.setKerberosEnabled(false);
                dataSources.setKerberosConfigId(null);
                dataSources.setCertificatesEnabled(false);
                dataSources.setCertificatesConfigId(null);
                dataSources.setCredentialsEnabled(true);
                dataSources.setCredentialsConfigId(dataSources.getAuthValue());
            } else if (dataSources.getAuthType().equals("kerberos")) {
                dataSources.setKerberosEnabled(true);
                dataSources.setKerberosConfigId(dataSources.getAuthValue());
                dataSources.setCertificatesEnabled(false);
                dataSources.setCertificatesConfigId(null);
                dataSources.setCredentialsEnabled(false);
                dataSources.setCredentialsConfigId(null);
            } else if (dataSources.getAuthType().equals("certificate")) {
                dataSources.setKerberosEnabled(false);
                dataSources.setKerberosConfigId(null);
                dataSources.setCertificatesEnabled(true);
                dataSources.setCertificatesConfigId(dataSources.getAuthValue());
                dataSources.setCredentialsEnabled(false);
                dataSources.setCredentialsConfigId(null);
            }

            Properties properties = new Properties();
            if (StringUtils.isNotEmpty(dataSources.getJdbcParam())) {
                properties = loadPropertiesFromJson(dataSources.getJdbcParam());
            }

            if (dataSources.getKerberosEnabled()) {
                // 判断kerberos是否开启
                if (dataSources.getKerberosConfigId() == null) {
                    return ResultBean.fail("kerberosConfigId不能为空");
                }
                TenantDefineKerberosConfigs kerberosConfigs = kerberosConfigsService.getById(dataSources.getKerberosConfigId());
                if(StringUtils.isNotEmpty(kerberosConfigs.getServiceName())){
                    properties.put("sasl.kerberos.service.name",  kerberosConfigs.getServiceName());
                }
                if (kerberosConfigs == null) {
                    return ResultBean.fail("kerberosConfigId不存在");
                }

                BASE64Decoder decoder = new BASE64Decoder();
                String keytabPath = kerberosConfigs.getKeytabPath();
                if (keytabPath != null && !keytabPath.trim().isEmpty()) {
                    keytabPath = new String(decoder.decodeBuffer(keytabPath));
                }
                String krb5Path = kerberosConfigs.getKrb5Path();
                if (krb5Path != null && !krb5Path.trim().isEmpty()) {
                    krb5Path = new String(decoder.decodeBuffer(krb5Path));
                }
                String principal = kerberosConfigs.getPrincipal();
                DatasourceConnectUtil.ConnectionResult connectionResult = DatasourceConnectUtil.testConnection(
                        DATA_SOURCE_TYPE_ENUM.getEnumByName(dataSources.getType()),
                        dataSources.getHost(),
                        dataSources.getPort(),
                        null,
                        null,
                        dataSources.getDbName(),
                        properties,
                        true,
                        krb5Path,
                        keytabPath,
                        principal,
                        dataSources.getHiveJdbcUrl()
                );
                return ResultBean.success(connectionResult);
            } else if (dataSources.getCredentialsEnabled()) {
                // 判断credentials是否开启
                if (dataSources.getCredentialsConfigId() == null) {
                    return ResultBean.fail("credentialsConfigId不能为空");
                }
                TenantDefineCredentials credentials = credentialsService.getById(dataSources.getCredentialsConfigId());
                if (credentials == null) {
                    return ResultBean.fail("credentialsConfigId不存在");
                }
                DatasourceConnectUtil.ConnectionResult connectionResult = DatasourceConnectUtil.testConnection(
                        DATA_SOURCE_TYPE_ENUM.getEnumByName(dataSources.getType()),
                        dataSources.getHost(),
                        dataSources.getPort(),
                        credentials.getUsername(),
                        credentials.getPassword(),
                        dataSources.getDbName(),
                        properties,
                        false,
                        null,
                        null,
                        null,
                        dataSources.getHiveJdbcUrl()
                );
                return ResultBean.success(connectionResult);
            } else if (dataSources.getCertificatesEnabled()) {
                // 判断certificates是否开启
                if (dataSources.getCertificatesConfigId() == null) {
                    return ResultBean.fail("certificatesConfigId不能为空");
                }
                TenantDefineCertificates certificates = certificatesService.getById(dataSources.getCertificatesConfigId());
                if (certificates == null) {
                    return ResultBean.fail("certificatesConfigId不存在");
                }
                BASE64Decoder decoder = new BASE64Decoder();
                String filePath = new String(decoder.decodeBuffer(certificates.getCertificatePath()));

                properties.setProperty("protocol", "https");
                properties.setProperty("caFile", filePath);

                DatasourceConnectUtil.ConnectionResult connectionResult = DatasourceConnectUtil.testConnection(
                        DATA_SOURCE_TYPE_ENUM.getEnumByName(dataSources.getType()),
                        dataSources.getHost(),
                        dataSources.getPort(),
                        properties.getProperty("username"),
                        properties.getProperty("password"),
                        dataSources.getDbName(),
                        properties,
                        false,
                        null,
                        null,
                        null,
                        dataSources.getHiveJdbcUrl()
                );

                return ResultBean.success(connectionResult);
            } else {
                DatasourceConnectUtil.ConnectionResult connectionResult = DatasourceConnectUtil.testConnection(
                        DATA_SOURCE_TYPE_ENUM.getEnumByName(dataSources.getType()),
                        dataSources.getHost(),
                        dataSources.getPort(),
                        null,
                        null,
                        dataSources.getDbName(),
                        properties,
                        false,
                        null,
                        null,
                        null,
                        dataSources.getHiveJdbcUrl()
                );
                return ResultBean.success(connectionResult);
            }
        } catch (Exception e) {
            log.error("连接测试异常", e);
            return ResultBean.success(DatasourceConnectUtil.ConnectionResult.failed("连接测试异常: " + e.getMessage(), e));
        }
    }

    /**
     * 添加或更新数据源信息
     * 该方法用于处理数据源的新增或更新请求如果数据源ID为空，则视为新增，否则视为更新
     *
     * @param dataSources 包含数据源信息的请求体
     * @return 返回操作结果的ResultBean对象
     */
    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody TenantDefineDatasources dataSources, HttpServletRequest request) {
        // 判断数据源ID是否为空，为空则表示新增数据源
        if (dataSources.getId() == null) {
            UserValueObject uvo = UmsUtils.getUVO();
            // 校验数据源名称是否为空
            if (dataSources.getName() == null || dataSources.getName().trim().isEmpty()) {
                return ResultBean.fail("数据源名称不能为空");
            }
            // 校验数据源类型是否为空
            if (dataSources.getType() == null || dataSources.getType().trim().isEmpty()) {
                return ResultBean.fail("数据源类型不能为空");
            }
            // 校验数据源host是否为空
            if (dataSources.getHost() == null || dataSources.getHost().trim().isEmpty()) {
                return ResultBean.fail("数据源host不能为空");
            }
            // 校验数据源端口是否为空
            if (dataSources.getPort() == null) {
                return ResultBean.fail("数据源port不能为空");
            }
            if (dataSources.getAuthType().equals("none")) {
                dataSources.setKerberosEnabled(false);
                dataSources.setKerberosConfigId(null);
                dataSources.setCertificatesEnabled(false);
                dataSources.setCertificatesConfigId(null);
                dataSources.setCredentialsEnabled(false);
                dataSources.setCredentialsConfigId(null);
            } else if (dataSources.getAuthType().equals("credential")) {
                dataSources.setKerberosEnabled(false);
                dataSources.setKerberosConfigId(null);
                dataSources.setCertificatesEnabled(false);
                dataSources.setCertificatesConfigId(null);
                dataSources.setCredentialsEnabled(true);
                dataSources.setCredentialsConfigId(dataSources.getAuthValue());
            } else if (dataSources.getAuthType().equals("kerberos")) {
                dataSources.setKerberosEnabled(true);
                dataSources.setKerberosConfigId(dataSources.getAuthValue());
                dataSources.setCertificatesEnabled(false);
                dataSources.setCertificatesConfigId(null);
                dataSources.setCredentialsEnabled(false);
                dataSources.setCredentialsConfigId(null);
            } else if (dataSources.getAuthType().equals("certificate")) {
                dataSources.setKerberosEnabled(false);
                dataSources.setKerberosConfigId(null);
                dataSources.setCertificatesEnabled(true);
                dataSources.setCertificatesConfigId(dataSources.getAuthValue());
                dataSources.setCredentialsEnabled(false);
                dataSources.setCredentialsConfigId(null);
            }


            // 设置创建者和更新者为当前用户，租户ID和租户名称也为当前用户的
            dataSources.setCreateUser(uvo.getUserName());
            dataSources.setUpdateUser(uvo.getUserName());
            dataSources.setTenantId(uvo.getTenantId());
            dataSources.setTenantName(uvo.getUserName());
            // 调用服务层方法保存或更新数据源信息，并返回操作结果
            boolean update = datasourcesService.saveOrUpdate(dataSources);
            return ResultBean.success(update);
        } else {
            if (dataSources.getAuthType().equals("none")) {
                dataSources.setKerberosEnabled(false);
                dataSources.setKerberosConfigId(null);
                dataSources.setCertificatesEnabled(false);
                dataSources.setCertificatesConfigId(null);
                dataSources.setCredentialsEnabled(false);
                dataSources.setCredentialsConfigId(null);
            } else if (dataSources.getAuthType().equals("credential")) {
                dataSources.setKerberosEnabled(false);
                dataSources.setKerberosConfigId(null);
                dataSources.setCertificatesEnabled(false);
                dataSources.setCertificatesConfigId(null);
                dataSources.setCredentialsEnabled(true);
                dataSources.setCredentialsConfigId(dataSources.getAuthValue());
            } else if (dataSources.getAuthType().equals("kerberos")) {
                dataSources.setKerberosEnabled(true);
                dataSources.setKerberosConfigId(dataSources.getAuthValue());
                dataSources.setCertificatesEnabled(false);
                dataSources.setCertificatesConfigId(null);
                dataSources.setCredentialsEnabled(false);
                dataSources.setCredentialsConfigId(null);
            } else if (dataSources.getAuthType().equals("certificate")) {
                dataSources.setKerberosEnabled(false);
                dataSources.setKerberosConfigId(null);
                dataSources.setCertificatesEnabled(true);
                dataSources.setCertificatesConfigId(dataSources.getAuthValue());
                dataSources.setCredentialsEnabled(false);
                dataSources.setCredentialsConfigId(null);
            }

            // 如果数据源ID不为空，设置更新时间和更新者为当前用户
            dataSources.setUpdateTime(LocalDateTime.now());
            dataSources.setUpdateUser(UmsUtils.getUVO().getUserName());
            // 调用服务层方法保存或更新数据源信息，并返回操作结果
            boolean update = datasourcesService.saveOrUpdate(dataSources);
            return ResultBean.success(update);
        }
    }

    /**
     * 处理数据源删除请求的控制器方法
     * 使用HTTP GET方法访问/delete/{id}路径来删除指定的数据源
     *
     * @param id 要删除的数据源的唯一标识符，通过URL路径变量获取
     * @return 返回一个ResultBean对象，表示删除操作的结果
     */
    @GetMapping("/delete/{id}")
    public ResultBean delete(@PathVariable("id") Long id, HttpServletRequest request) {
        // 调用服务层方法删除数据源信息，并返回操作结果
        boolean remove = datasourcesService.removeById(id);
        return ResultBean.success(remove);
    }

    /**
     * 处理数据源详细信息请求的控制器方法
     * 使用HTTP GET方法访问/detail/{id}路径来获取指定数据源的详细信息
     *
     * @param id 要获取详细信息的数据源的唯一标识符，通过URL路径变量获取
     * @return 返回一个ResultBean对象，表示获取操作的结果
     */
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        // 调用服务层方法获取数据源详细信息，并返回结果
        TenantDefineDatasources byId = datasourcesService.getById(id);
        if (byId.getCredentialsEnabled()) {
            byId.setAuthType("credential");
            byId.setAuthValue(byId.getCredentialsConfigId());
        } else if (byId.getCertificatesEnabled()) {
            byId.setAuthType("certificate");
            byId.setAuthValue(byId.getCertificatesConfigId());
        } else if (byId.getKerberosEnabled()) {
            byId.setAuthType("kerberos");
            byId.setAuthValue(byId.getKerberosConfigId());
        }
        return ResultBean.success(byId);
    }

    /**
     * 处理数据源分页查询请求的控制器方法
     * 使用HTTP POST方法访问/page路径来分页查询数据源信息
     *
     * @param dataSources 包含分页查询条件的请求体，通过@RequestBody注解读取请求体中的数据
     * @return 返回一个ResultBean对象，表示分页查询操作的结果
     */
    @PostMapping("/page")
    public BasePageResponse<List<TenantDefineDatasources>> page(@RequestBody TenantDefineDatasourcePageRequest dataSources) {
        // 调用服务层方法分页查询数据源信息，并返回结果
        return datasourcesService.page(dataSources);
    }

}
