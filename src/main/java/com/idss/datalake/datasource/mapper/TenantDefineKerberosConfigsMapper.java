package com.idss.datalake.datasource.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datamart.dto.request.DataMartAssetPageRequest;
import com.idss.datalake.datamart.entity.DataMartAsset;
import com.idss.datalake.datasource.dto.TenantDefineKerberosConfigsPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineKerberosConfigs;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * Kerberos认证配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface TenantDefineKerberosConfigsMapper extends BaseMapper<TenantDefineKerberosConfigs> {
    Page<TenantDefineKerberosConfigs> page(TenantDefineKerberosConfigsPageRequest request);
}
