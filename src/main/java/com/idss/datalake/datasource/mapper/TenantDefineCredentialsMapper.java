package com.idss.datalake.datasource.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datasource.dto.TenantDefineCertificatesPageRequest;
import com.idss.datalake.datasource.dto.TenantDefineCredentialsPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineCertificates;
import com.idss.datalake.datasource.entity.TenantDefineCredentials;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 用户凭证管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface TenantDefineCredentialsMapper extends BaseMapper<TenantDefineCredentials> {
    Page<TenantDefineCredentials> page(TenantDefineCredentialsPageRequest request);
}
