package com.idss.datalake.datasource.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datasource.dto.TenantDefineDatasourcePageRequest;
import com.idss.datalake.datasource.dto.TenantDefineKerberosConfigsPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineDatasources;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datasource.entity.TenantDefineKerberosConfigs;

/**
 * <p>
 * 数据源管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface TenantDefineDatasourcesMapper extends BaseMapper<TenantDefineDatasources> {
    Page<TenantDefineDatasources> page(TenantDefineDatasourcePageRequest request);
}
