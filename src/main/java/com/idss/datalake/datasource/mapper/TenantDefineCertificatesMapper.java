package com.idss.datalake.datasource.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.datasource.dto.TenantDefineCertificatesPageRequest;
import com.idss.datalake.datasource.dto.TenantDefineKerberosConfigsPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineCertificates;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datasource.entity.TenantDefineKerberosConfigs;

/**
 * <p>
 * SSL证书管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface TenantDefineCertificatesMapper extends BaseMapper<TenantDefineCertificates> {
    Page<TenantDefineCertificates> page(TenantDefineCertificatesPageRequest request);
}
