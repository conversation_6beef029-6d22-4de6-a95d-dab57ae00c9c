package com.idss.datalake.datasource.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.ssl.SSLContexts;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.DescribeClusterResult;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.elasticsearch.action.admin.cluster.health.ClusterHealthRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.net.ssl.SSLContext;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;

/**
 * 数据源连通性测试工具类
 * 支持：MySQL、Oracle、PostgreSQL、ClickHouse、Elasticsearch、Kafka、Hive等数据源
 * 支持Kerberos认证
 *   以下是一些常见的Hive JDBC URL格式，可以直接传入到新增的方法中：
 *   1. **基本连接**:
 *   ```
 *   *************************************
 *   ```
 *   1. **使用ZooKeeper进行HA**:
 *   ```
 *   ****************************,zookeeper2:2181,zookeeper3:2181/;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2
 *   ```
 *   1. **带Kerberos认证**:
 *   ```
 *   *************************************;principal=hive/<EMAIL>
 *   ```
 *   1. **使用SSL**:
 *   ```
 *   *************************************;ssl=true;sslTrustStore=/path/to/truststore;trustStorePassword=password
 *   ```
 *   1. **带有LDAP认证**:
 *   ```
 *   *************************************;auth=ldap
 *   ```
 *   该修改使得工具类更加灵活，能够处理更多复杂的Hive连接场景。
 *
 */
public class DatasourceConnectUtil {


    private static final Logger log = LoggerFactory.getLogger(DatasourceConnectUtil.class);

    // 连接超时时间（秒）
    private static final int CONNECT_TIMEOUT = 10;



    /**
     * 连接结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConnectionResult {
        private boolean success;
        private String message;
        private Exception exception;

        public static ConnectionResult success() {
            return ConnectionResult.builder()
                    .success(true)
                    .message("连接成功")
                    .build();
        }

        public static ConnectionResult failed(String message, Exception e) {
            return ConnectionResult.builder()
                    .success(false)
                    .message(message)
                    .exception(e)
                    .build();
        }
    }

    /**
     * 测试数据源连接（支持JAAS配置文件）
     *
     * @param dataSourceType    数据源类型
     * @param host              主机地址
     * @param port              端口
     * @param username          用户名
     * @param password          密码
     * @param database          数据库名
     * @param params            额外参数
     * @param useKerberos       是否使用Kerberos认证
     * @param kerberosConf      Kerberos配置文件路径
     * @param kerberosKeytab    Kerberos keytab文件路径
     * @param kerberosPrincipal Kerberos principal
     * @param hiveJdbcUrl       Hive JDBC URL (可选)
     * @param jaasConfigPath    JAAS配置文件路径 (可选，主要用于Kafka)
     * @return 连接结果
     */
    public static ConnectionResult testConnection(
            DATA_SOURCE_TYPE_ENUM dataSourceType,
            String host,
            int port,
            String username,
            String password,
            String database,
            Properties params,
            boolean useKerberos,
            String kerberosConf,
            String kerberosKeytab,
            String kerberosPrincipal,
            String hiveJdbcUrl,
            String jaasConfigPath) {
            
        // 对于Kafka数据源，如果提供了JAAS配置文件，使用JAAS配置文件测试连接
        if (dataSourceType == DATA_SOURCE_TYPE_ENUM.KAFKA && jaasConfigPath != null && !jaasConfigPath.isEmpty()) {
            return testKafkaConnectionWithJaas(host, port, username, password, params, jaasConfigPath);
        }
        
        // 否则使用标准方法测试连接
        return testConnection(dataSourceType, host, port, username, password, database, params, 
                useKerberos, kerberosConf, kerberosKeytab, kerberosPrincipal, hiveJdbcUrl);
    }
    
    /**
     * 测试数据源连接
     *
     * @param dataSourceType    数据源类型
     * @param host              主机地址
     * @param port              端口
     * @param username          用户名
     * @param password          密码
     * @param database          数据库名
     * @param params            额外参数
     * @param useKerberos       是否使用Kerberos认证
     * @param kerberosConf      Kerberos配置文件路径
     * @param kerberosKeytab    Kerberos keytab文件路径
     * @param kerberosPrincipal Kerberos principal
     * @param hiveJdbcUrl       Hive JDBC URL (可选)
     * @return 连接结果
     */
    public static ConnectionResult testConnection(
            DATA_SOURCE_TYPE_ENUM dataSourceType,
            String host,
            int port,
            String username,
            String password,
            String database,
            Properties params,
            boolean useKerberos,
            String kerberosConf,
            String kerberosKeytab,
            String kerberosPrincipal,
            String hiveJdbcUrl) {

        // 对于非Kafka的数据源，如果使用Kerberos，先设置Kerberos环境
        if (useKerberos && dataSourceType != DATA_SOURCE_TYPE_ENUM.KAFKA) {
            setupKerberos(kerberosConf, kerberosKeytab, kerberosPrincipal);
        }
        
        // 对于Kafka数据源，如果使用Kerberos，在params中添加Kerberos配置
        if (dataSourceType == DATA_SOURCE_TYPE_ENUM.KAFKA && useKerberos) {
            if (params == null) {
                params = new Properties();
            }
            
            // 检查Kerberos配置文件是否存在
            File confFile = new File(kerberosConf);
            File keytabFile = new File(kerberosKeytab);
            
            if (!confFile.exists() || !confFile.canRead()) {
                return ConnectionResult.failed("Kerberos配置文件不存在或不可读: " + kerberosConf, null);
            }
            
            if (!keytabFile.exists() || !keytabFile.canRead()) {
                return ConnectionResult.failed("Kerberos keytab文件不存在或不可读: " + kerberosKeytab, null);
            }
            
            // 设置Kafka Kerberos认证参数
            params.put("security.protocol", "SASL_PLAINTEXT");
            params.put("sasl.mechanism", "GSSAPI");
            params.put("sasl.kerberos.service.name", "kafka-server"); // 默认服务名，可以从params中覆盖
            params.put("java.security.krb5.conf", kerberosConf);
            params.put("java.security.user.keytab", kerberosKeytab);
            params.put("kerberos.principal", kerberosPrincipal);
        }

        switch (dataSourceType) {
            case MYSQL:
                return testMysqlConnection(host, port, username, password, database, params);
            case ORACLE:
                return testOracleConnection(host, port, username, password, database, params);
            case POSTGRESQL:
            case PANWEI:
                return testPostgresqlConnection(host, port, username, password, database, params);
            case CLICKHOUSE:
                return testClickhouseConnection(host, port, username, password, database, params);
            case ELASTICSEARCH:
                return testElasticsearchConnection(host, port, username, password, params);
            case KAFKA:
                return testKafkaConnection(host, port, username, password, params);
            case HIVE:
                if(StringUtils.isNotEmpty(hiveJdbcUrl)){
                    return testHiveConnectionWithUrl(hiveJdbcUrl, username, password, params, useKerberos, kerberosConf, kerberosKeytab, kerberosPrincipal);
                }
                return testHiveConnection(host, port, username, password, database, params, useKerberos, kerberosPrincipal);
            default:
                return ConnectionResult.failed("不支持的数据源类型: " + dataSourceType, null);
        }
    }

    /**
     * 使用自定义JDBC URL测试Hive连接
     *
     * @param jdbcUrl           自定义的JDBC URL
     * @param username          用户名
     * @param password          密码
     * @param params            额外参数
     * @param useKerberos       是否使用Kerberos认证
     * @param kerberosConf      Kerberos配置文件路径
     * @param kerberosKeytab    Kerberos keytab文件路径
     * @param kerberosPrincipal Kerberos principal
     * @return 连接结果
     */
    public static ConnectionResult testHiveConnectionWithUrl(
            String jdbcUrl,
            String username,
            String password,
            Properties params,
            boolean useKerberos,
            String kerberosConf,
            String kerberosKeytab,
            String kerberosPrincipal) {

        if (useKerberos) {
            setupKerberos(kerberosConf, kerberosKeytab, kerberosPrincipal);
        }

        return testJdbcConnection("org.apache.hive.jdbc.HiveDriver", jdbcUrl, username, password, params);
    }

    /**
     * 设置Kerberos认证
     */
    private static void setupKerberos(String kerberosConf, String kerberosKeytab, String kerberosPrincipal) {
        try {
            log.info("KBS认证, kerberosConf:{}, kerberosKeytab:{} ,kerberosPrincipal:{}",kerberosConf,kerberosKeytab,kerberosPrincipal);
            System.setProperty("java.security.krb5.conf", kerberosConf);
            System.setProperty("javax.security.auth.useSubjectCredsOnly", "false");
            Configuration conf = new Configuration();
            conf.set("hadoop.security.authentication", "kerberos");
            UserGroupInformation.setConfiguration(conf);
            // 检查keytab文件是否存在
            File keytabFile = new File(kerberosKeytab);
            if (!keytabFile.exists()) {
                log.error("Kerberos keytab文件不存在: {}", kerberosKeytab);
                throw new RuntimeException("Kerberos keytab文件不存在: " + kerberosKeytab);
            }

            // 登录
            UserGroupInformation.loginUserFromKeytab(kerberosPrincipal, kerberosKeytab);
            log.info("Kerberos认证成功");
        } catch (Exception e) {
            log.error("Kerberos认证失败", e);
            throw new RuntimeException("Kerberos认证失败", e);
        }
    }

    /**
     * 测试MySQL连接
     */
    private static ConnectionResult testMysqlConnection(
            String host, int port, String username, String password, String database, Properties params) {
        String url = String.format("jdbc:mysql://%s:%d/%s", host, port, database);
        return testJdbcConnection("com.mysql.cj.jdbc.Driver", url, username, password, params);
    }

    /**
     * 测试Oracle连接
     */
    private static ConnectionResult testOracleConnection(
            String host, int port, String username, String password, String database, Properties params) {
        String url = String.format("**************************", host, port, database);
        return testJdbcConnection("oracle.jdbc.driver.OracleDriver", url, username, password, params);
    }

    /**
     * 测试PostgreSQL连接
     */
    private static ConnectionResult testPostgresqlConnection(
            String host, int port, String username, String password, String database, Properties params) {
        String url = String.format("jdbc:postgresql://%s:%d/%s", host, port, database);
        return testJdbcConnection("org.postgresql.Driver", url, username, password, params);
    }

    /**
     * 测试ClickHouse连接
     */
    private static ConnectionResult testClickhouseConnection(
            String host, int port, String username, String password, String database, Properties params) {
        String url = String.format("jdbc:clickhouse://%s:%d/%s", host, port, database);
        return testJdbcConnection("ru.yandex.clickhouse.ClickHouseDriver", url, username, password, params);
    }

    /**
     * 测试Hive连接
     */
    private static ConnectionResult testHiveConnection(
            String host, int port, String username, String password, String database, Properties params, boolean useKerberos,String principal) {
        String url;
        if (useKerberos) {
            url = String.format("*******************************="+principal, host, port, database);
        } else {
            url = String.format("jdbc:hive2://%s:%d/%s", host, port, database);
        }
        return testJdbcConnection("org.apache.hive.jdbc.HiveDriver", url, username, password, params);
    }

    /**
     * 测试JDBC连接
     */
    private static ConnectionResult testJdbcConnection(
            String driverClass, String url, String username, String password, Properties connectionProps) {
        Connection conn = null;
        try {
            Class.forName(driverClass);

            Properties props = new Properties();
            if (connectionProps != null) {
                props.putAll(connectionProps);
            }

            if (username != null && !username.isEmpty()) {
                props.setProperty("user", username);
            }

            if (password != null && !password.isEmpty()) {
                props.setProperty("password", password);
            }

            if (connectionProps != null ) {
                String caFile = connectionProps.getProperty("caFile");
                if(StringUtils.isNotEmpty(caFile)){
                    props.setProperty("ssl", "true");
                    props.setProperty("sslmode", "strict");
                    props.setProperty("sslrootcert", caFile);
                }
            }

            // 设置连接超时
            if (!props.containsKey("connectTimeout")) {
                props.setProperty("connectTimeout", String.valueOf(CONNECT_TIMEOUT * 1000));
            }

            conn = DriverManager.getConnection(url, props);
            return ConnectionResult.success();
        } catch (ClassNotFoundException e) {
            log.error("数据库驱动未找到: {}", driverClass, e);
            return ConnectionResult.failed("数据库驱动未找到: " + driverClass, e);
        } catch (SQLException e) {
            log.error("数据库连接失败: {}", url, e);
            return ConnectionResult.failed("数据库连接失败: " + e.getMessage(), e);
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    log.error("关闭数据库连接失败", e);
                }
            }
        }
    }

    /**
     * 测试Elasticsearch连接
     *
     * @param host 主机地址
     * @param port 端口
     * @param username 用户名
     * @param password 密码
     * @param params 额外参数，可以包含"protocol"指定协议(http/https)和"caFile"指定CA证书路径
     * @return 连接结果
     */
    private static ConnectionResult testElasticsearchConnection(
            String host, int port, String username, String password, Properties params) {
        RestHighLevelClient client = null;
        try {
            // 从params获取协议，默认为http
            String protocol = "http";
            String caFilePath;

            if (params != null) {
                if (params.getProperty("protocol") != null) {
                    protocol = params.getProperty("protocol");
                }
                if (params.getProperty("caFile") != null) {
                    caFilePath = params.getProperty("caFile");
                } else {
                    caFilePath = null;
                }
            } else {
                caFilePath = null;
            }

            RestClientBuilder builder = RestClient.builder(new HttpHost(host, port, protocol));

            // 如果有用户名密码，设置认证
            if (username != null && !username.isEmpty() && password != null) {
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY,
                        new UsernamePasswordCredentials(username, password));

                builder.setHttpClientConfigCallback(httpClientBuilder ->
                        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
            }

            // 如果是https协议且提供了CA证书，配置SSL
            if ("https".equalsIgnoreCase(protocol) && caFilePath != null) {
                builder.setHttpClientConfigCallback(httpClientBuilder -> {
                    try {
                        // 从params获取密钥库信息
                        String keystorePath = params.getProperty("keystorePath");
                        String keystorePassword = params.getProperty("keystorePassword");
                        
                        SSLContext sslContext;
                        if (keystorePath != null && keystorePassword != null) {
                            // 使用PKCS12密钥库
                            KeyStore keyStore = KeyStore.getInstance("PKCS12");
                            try (FileInputStream fis = new FileInputStream(keystorePath)) {
                                keyStore.load(fis, keystorePassword.toCharArray());
                            }
                            sslContext = SSLContexts.custom()
                                    .loadTrustMaterial(keyStore, null)
                                    .build();
                        } else {
                            // 使用CA证书
                            KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
                            trustStore.load(null, null);
                            
                            // 读取PEM证书
                            CertificateFactory cf = CertificateFactory.getInstance("X.509");
                            try (FileInputStream fis = new FileInputStream(caFilePath)) {
                                Certificate cert = cf.generateCertificate(fis);
                                trustStore.setCertificateEntry("ca", cert);
                            }
                            
                            sslContext = SSLContexts.custom()
                                    .loadTrustMaterial(trustStore, null)
                                    .build();
                        }

                        // 配置SSL连接
                        return httpClientBuilder
                                .setDefaultCredentialsProvider(
                                        username != null && !username.isEmpty() && password != null ?
                                                new BasicCredentialsProvider() {{
                                                    setCredentials(AuthScope.ANY,
                                                            new UsernamePasswordCredentials(username, password));
                                                }} : null
                                )
                                .setSSLContext(sslContext);
                    } catch (Exception e) {
                        log.error("配置SSL上下文失败", e);
                        throw new RuntimeException("配置SSL上下文失败: " + e.getMessage(), e);
                    }
                });
            }

            // 设置连接超时
            builder.setRequestConfigCallback(requestConfigBuilder ->
                    requestConfigBuilder.setConnectTimeout(CONNECT_TIMEOUT * 1000)
                            .setSocketTimeout(CONNECT_TIMEOUT * 1000));

            client = new RestHighLevelClient(builder);

            // 检查集群健康状态
            client.cluster().health(new ClusterHealthRequest(), RequestOptions.DEFAULT);

            return ConnectionResult.success();
        } catch (Exception e) {
            log.error("Elasticsearch连接失败: {}:{}", host, port, e);
            return ConnectionResult.failed("Elasticsearch连接失败: " + e.getMessage(), e);
        } finally {
            if (client != null) {
                try {
                    client.close();
                } catch (Exception e) {
                    log.error("关闭Elasticsearch连接失败", e);
                }
            }
        }
    }

    /**
     * 测试Kafka连接（使用自定义JAAS配置文件）
     * 
     * @param host 主机地址
     * @param port 端口
     * @param username 用户名（可选，如果使用JAAS文件可为null）
     * @param password 密码（可选，如果使用JAAS文件可为null）
     * @param customProps 自定义属性，可包含认证相关配置
     * @param jaasConfigPath JAAS配置文件路径（可选）
     * @return 连接结果
     */
    public static ConnectionResult testKafkaConnectionWithJaas(
            String host, int port, String username, String password, 
            Properties customProps, String jaasConfigPath) {
        
        if (jaasConfigPath != null && !jaasConfigPath.isEmpty()) {
            File jaasFile = new File(jaasConfigPath);
            if (!jaasFile.exists() || !jaasFile.canRead()) {
                return ConnectionResult.failed("JAAS配置文件不存在或不可读: " + jaasConfigPath, null);
            }
            
            // 设置JAAS配置文件
            System.setProperty("java.security.auth.login.config", jaasConfigPath);
            
            if (customProps == null) {
                customProps = new Properties();
            }
            
            // 如果没有指定安全协议，默认使用SASL_PLAINTEXT
            if (!customProps.containsKey("security.protocol")) {
                customProps.put("security.protocol", "SASL_PLAINTEXT");
            }
        }
        
        return testKafkaConnection(host, port, username, password, customProps);
    }
    
    /**
     * 测试Kafka连接
     * 
     * @param host 主机地址
     * @param port 端口
     * @param username 用户名
     * @param password 密码
     * @param customProps 自定义属性，可包含认证相关配置
     * @return 连接结果
     */
    private static ConnectionResult testKafkaConnection(
            String host, int port, String username, String password, Properties customProps) {
        AdminClient adminClient = null;
        try {
            Properties props = new Properties();
            props.put("bootstrap.servers", host + ":" + port);
            
            // 设置连接超时和请求超时
            props.put("request.timeout.ms", "60000");
            props.put("default.api.timeout.ms", "60000");
            
            // 如果有自定义属性，添加进来
            if (customProps != null) {
                props.putAll(customProps);
            }
            
            // 检查认证方式
            String securityProtocol = props.getProperty("security.protocol");
            String saslMechanism = props.getProperty("sasl.mechanism");
            
            // 如果没有指定安全协议，但有用户名密码，默认使用SASL_PLAINTEXT和PLAIN机制
            if (securityProtocol == null && username != null && !username.isEmpty() && password != null) {
                securityProtocol = "SASL_PLAINTEXT";
                props.put("security.protocol", securityProtocol);
                
                // 如果没有指定SASL机制，默认使用PLAIN
                if (saslMechanism == null) {
                    saslMechanism = "PLAIN";
                    props.put("sasl.mechanism", saslMechanism);
                }
            }
            
            // 根据安全协议和SASL机制配置认证
            if (securityProtocol != null && securityProtocol.startsWith("SASL")) {
                // 如果是SASL认证
                if (saslMechanism == null) {
                    saslMechanism = "PLAIN"; // 默认使用PLAIN
                    props.put("sasl.mechanism", saslMechanism);
                }
                
                // 根据SASL机制配置JAAS
                switch (saslMechanism) {
                    case "PLAIN":
                        // PLAIN认证需要用户名和密码
                        if (username != null && !username.isEmpty() && password != null) {
                            String jaasConfig = String.format(
                                    "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";",
                                    username, password);
                            props.put("sasl.jaas.config", jaasConfig);
                        }
                        break;
                    case "SCRAM-SHA-256":
                    case "SCRAM-SHA-512":
                        // SCRAM认证也需要用户名和密码
                        if (username != null && !username.isEmpty() && password != null) {
                            String jaasConfig = String.format(
                                    "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"%s\" password=\"%s\";",
                                    username, password);
                            props.put("sasl.jaas.config", jaasConfig);
                        }
                        break;
                    case "GSSAPI":
                        // Kerberos认证
                        // 检查是否已经在customProps中提供了完整的JAAS配置
                        if (!props.containsKey("sasl.jaas.config")) {
                            // 检查是否提供了Kerberos相关配置
                            String kerberosServiceName = props.getProperty("sasl.kerberos.service.name");
                            String kerberosKeytab = props.getProperty("java.security.user.keytab");
                            String kerberosPrincipal = props.getProperty("kerberos.principal");
                            String krb5Conf = props.getProperty("java.security.krb5.conf");
                            
                            // 如果提供了必要的Kerberos配置
                            if (kerberosServiceName != null && kerberosKeytab != null && 
                                kerberosPrincipal != null && krb5Conf != null) {
                                
                                // 设置Kerberos系统属性
                                System.setProperty("java.security.krb5.conf", krb5Conf);
                                System.setProperty("javax.security.auth.useSubjectCredsOnly", "false");
                                
                                // 构建JAAS配置
                                String jaasConfig = String.format(
                                        "com.sun.security.auth.module.Krb5LoginModule required " +
                                        "useKeyTab=true " +
                                        "storeKey=true " +
                                        "keyTab=\"%s\" " +
                                        "principal=\"%s\";",
                                        kerberosKeytab, kerberosPrincipal);
                                props.put("sasl.jaas.config", jaasConfig);
                            } else {
                                log.warn("使用Kerberos认证但缺少必要的配置参数");
                            }
                        }
                        break;
                    default:
                        log.warn("不支持的SASL机制: {}", saslMechanism);
                        break;
                }
            }
            
            // 如果是SSL协议，检查SSL相关配置
            if (securityProtocol != null && securityProtocol.contains("SSL")) {
                // 检查是否提供了SSL信任库和密钥库
                String trustStorePath = props.getProperty("ssl.truststore.location");
                String trustStorePassword = props.getProperty("ssl.truststore.password");
                
                if (trustStorePath != null && !new File(trustStorePath).exists()) {
                    log.warn("SSL信任库文件不存在: {}", trustStorePath);
                }
            }
            
            log.info("创建Kafka AdminClient，配置: {}", props);
            adminClient = AdminClient.create(props);
            
            // 获取并记录Kafka集群描述
            DescribeClusterResult clusterDescription = adminClient.describeCluster();
            String clusterId = clusterDescription.clusterId().get(30, TimeUnit.SECONDS);
            log.info("成功连接到Kafka集群，集群ID: {}", clusterId);
            
            // 获取主题列表，设置超时
            ListTopicsResult topics = adminClient.listTopics();
            // 增加超时时间至30秒以适应更多场景
            topics.names().get(30, TimeUnit.SECONDS);
            
            return ConnectionResult.success();
        } catch (Exception e) {
            log.error("Kafka连接失败: {}:{}. 错误详情:", host, port, e);
            String errorMessage = "Kafka连接失败: " + e.getMessage();
            
            // 提供更具体的错误上下文
            if (e instanceof TimeoutException) {
                errorMessage += ", 可能原因: Kafka服务未启动、网络不通、认证失败或超时设置过短";
            } else if (e.getMessage() != null && e.getMessage().contains("Authentication failed")) {
                errorMessage += ", 认证失败，请检查认证配置";
            } else if (e.getMessage() != null && e.getMessage().contains("SSL")) {
                errorMessage += ", SSL配置错误，请检查SSL相关配置";
            } else if (e.getMessage() != null && e.getMessage().contains("Kerberos")) {
                errorMessage += ", Kerberos认证失败，请检查Kerberos配置";
            }
            
            if (adminClient != null) {
                log.warn("AdminClient仍处于活动状态，但发生错误");
            }
            
            return ConnectionResult.failed(errorMessage, e);
        } finally {
            if (adminClient != null) {
                try {
                    adminClient.close();
                } catch (Exception e) {
                    log.error("关闭Kafka AdminClient时发生错误", e);
                }
            }
        }
    }

    /**
     * 测试Kafka连接（指定认证机制）
     * 
     * @param host 主机地址
     * @param port 端口
     * @param securityProtocol 安全协议 (PLAINTEXT, SASL_PLAINTEXT, SSL, SASL_SSL)
     * @param saslMechanism SASL机制 (PLAIN, SCRAM-SHA-256, SCRAM-SHA-512, GSSAPI)
     * @param username 用户名
     * @param password 密码
     * @param kerberosConf Kerberos配置文件路径 (仅GSSAPI时需要)
     * @param kerberosKeytab Kerberos keytab文件路径 (仅GSSAPI时需要)
     * @param kerberosPrincipal Kerberos principal (仅GSSAPI时需要)
     * @param customProps 其他自定义属性
     * @return 连接结果
     */
    public static ConnectionResult testKafkaConnectionWithAuth(
            String host, 
            int port, 
            String securityProtocol, 
            String saslMechanism, 
            String username, 
            String password, 
            String kerberosConf, 
            String kerberosKeytab, 
            String kerberosPrincipal,
            Properties customProps) {
        
        Properties props = new Properties();
        
        // 设置安全协议
        if (securityProtocol != null && !securityProtocol.isEmpty()) {
            props.put("security.protocol", securityProtocol);
        }
        
        // 设置SASL机制
        if (saslMechanism != null && !saslMechanism.isEmpty() && securityProtocol != null && securityProtocol.startsWith("SASL")) {
            props.put("sasl.mechanism", saslMechanism);
            
            // 根据SASL机制配置认证
            switch (saslMechanism) {
                case "PLAIN":
                    if (username != null && !username.isEmpty() && password != null) {
                        String jaasConfig = String.format(
                                "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";",
                                username, password);
                        props.put("sasl.jaas.config", jaasConfig);
                    }
                    break;
                case "SCRAM-SHA-256":
                case "SCRAM-SHA-512":
                    if (username != null && !username.isEmpty() && password != null) {
                        String jaasConfig = String.format(
                                "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"%s\" password=\"%s\";",
                                username, password);
                        props.put("sasl.jaas.config", jaasConfig);
                    }
                    break;
                case "GSSAPI":
                    if (kerberosConf != null && !kerberosConf.isEmpty() && 
                        kerberosKeytab != null && !kerberosKeytab.isEmpty() && 
                        kerberosPrincipal != null && !kerberosPrincipal.isEmpty()) {
                        
                        // 检查文件是否存在
                        File confFile = new File(kerberosConf);
                        File keytabFile = new File(kerberosKeytab);
                        
                        if (!confFile.exists() || !confFile.canRead()) {
                            return ConnectionResult.failed("Kerberos配置文件不存在或不可读: " + kerberosConf, null);
                        }
                        
                        if (!keytabFile.exists() || !keytabFile.canRead()) {
                            return ConnectionResult.failed("Kerberos keytab文件不存在或不可读: " + kerberosKeytab, null);
                        }
                        
                        // 设置Kerberos系统属性
                        System.setProperty("java.security.krb5.conf", kerberosConf);
                        System.setProperty("javax.security.auth.useSubjectCredsOnly", "false");
                        
                        // 设置Kerberos服务名
                        props.put("sasl.kerberos.service.name", "kafka-server"); // 默认值，可以从customProps中覆盖
                        
                        // 构建JAAS配置
                        String jaasConfig = String.format(
                                "com.sun.security.auth.module.Krb5LoginModule required " +
                                "useKeyTab=true " +
                                "storeKey=true " +
                                "keyTab=\"%s\" " +
                                "principal=\"%s\";",
                                kerberosKeytab, kerberosPrincipal);
                        props.put("sasl.jaas.config", jaasConfig);
                    } else {
                        return ConnectionResult.failed("使用Kerberos认证但缺少必要的配置参数", null);
                    }
                    break;
                default:
                    return ConnectionResult.failed("不支持的SASL机制: " + saslMechanism, null);
            }
        }
        
        // 添加自定义属性
        if (customProps != null) {
            props.putAll(customProps);
        }
        
        return testKafkaConnection(host, port, null, null, props);
    }

    public static void main(String[] args) throws IOException {
        String str = "/home/<USER>/data-lake-webapi-1.0.0/kbsfile/20250317数据资产管理.zip";
        BASE64Encoder encoder = new BASE64Encoder();
        String encode = encoder.encode(str.getBytes());
        System.out.println(encode);
        BASE64Decoder decoder = new BASE64Decoder();
        System.out.println(new String(decoder.decodeBuffer("L2hvbWUvc2RjL2RhdGEtbGFrZS13ZWJhcGktMS4wLjAva2JzZmlsZS8yMDI1MDMxN+aVsOaNrui1\naOS6p+euoeeQhigxKTE3NDUzOTQwNTkxMDIuemlw")));
/*
        // 测试MySQL连接
        ConnectionResult mysqlResult = DatasourceConnectUtil.testConnection(
                DATA_SOURCE_TYPE_ENUM.MYSQL,
                "************",
                3306,
                "root",
                "3hd2eV-^MNSrtQ06",
                "data_lake",
                null,  // 额外参数
                false, // 不使用Kerberos
                null, null, null, null  // Kerberos参数为空
        );

        // 输出结果
        if (mysqlResult.isSuccess()) {
            System.out.println("MySQL连接成功");
        } else {
            System.out.println("MySQL连接失败: " + mysqlResult.getMessage());
            if (mysqlResult.getException() != null) {
                mysqlResult.getException().printStackTrace();
            }
        }

        // 带Kerberos认证的Hive连接
        // 使用自定义JDBC URL测试Hive连接
        String hiveJdbcUrl = "***********************************,hdp01.bigdata.com:2181,hdp02.bigdata.com:2181/test16;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2";

        ConnectionResult hiveResult = DatasourceConnectUtil.testHiveConnectionWithUrl(
                hiveJdbcUrl,
                "",
                "",
                null,  // 额外参数
                true,  // 使用Kerberos
                "/Users/<USER>/Documents/kbs-hive/krb5.conf",  // Kerberos配置文件
                "/Users/<USER>/Documents/kbs-hive/hive.service.keytab",  // Keytab文件
                "hive/<EMAIL>"  // Kerberos Principal
        );

        // 输出结果
        if (hiveResult.isSuccess()) {
            System.out.println("Hive连接成功");
        } else {
            System.out.println("Hive连接失败: " + hiveResult.getMessage());
            if (hiveResult.getException() != null) {
                hiveResult.getException().printStackTrace();
            }
        }
        
        // 测试带Kerberos认证的Kafka连接
        Properties kafkaProps = new Properties();
        kafkaProps.put("sasl.kerberos.service.name", "kafka-server");
        
        ConnectionResult kafkaResult = DatasourceConnectUtil.testKafkaConnectionWithAuth(
                "root",
                9192,
                "SASL_PLAINTEXT",
                "GSSAPI",
                null,
                null,
                "/etc/krb5.conf",
                "/home/<USER>/bin/services/kafka_UDLdN2/config/kafka-server.keytab",
                "kafka-server/<EMAIL>",
                kafkaProps
        );
        
        // 输出结果
        if (kafkaResult.isSuccess()) {
            System.out.println("Kafka连接成功");
        } else {
            System.out.println("Kafka连接失败: " + kafkaResult.getMessage());
            if (kafkaResult.getException() != null) {
                kafkaResult.getException().printStackTrace();
            }
        }
        */
    }
}