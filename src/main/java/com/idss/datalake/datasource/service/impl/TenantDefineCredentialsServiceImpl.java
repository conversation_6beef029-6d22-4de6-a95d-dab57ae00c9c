package com.idss.datalake.datasource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.util.DateUtil;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datashare.cluster.entity.TbCluster;
import com.idss.datalake.datashare.cluster.service.ITbClusterService;
import com.idss.datalake.datasource.dto.TenantDefineCredentialsPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineCredentials;
import com.idss.datalake.datasource.entity.TenantDefineDatasources;
import com.idss.datalake.datasource.mapper.TenantDefineCredentialsMapper;
import com.idss.datalake.datasource.mapper.TenantDefineDatasourcesMapper;
import com.idss.datalake.datasource.service.ITenantDefineCredentialsService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户凭证管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
public class TenantDefineCredentialsServiceImpl extends ServiceImpl<TenantDefineCredentialsMapper, TenantDefineCredentials> implements ITenantDefineCredentialsService {
    @Resource
    private TenantDefineCredentialsMapper credentialsMapper;

    @Resource
    private TenantDefineDatasourcesMapper dataSourcesMapper;

    @Resource
    private ITbClusterService clusterService;

    @Override
    public BasePageResponse<List<TenantDefineCredentials>> page(TenantDefineCredentialsPageRequest request) {
        UserValueObject uvo = UmsUtils.getUVO();
        request.setTenantId(Long.valueOf(uvo.getTenantId()));
        PageHelper.offsetPage(request.getOffset(), request.getPageSize());
        Page<TenantDefineCredentials> page = credentialsMapper.page(request);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), new ArrayList<>());
        } else {
            List<TenantDefineCredentials> result = page.getResult();
            for (TenantDefineCredentials credentials : result) {
                int refCnt = 0;
                List<String> refDesc = new ArrayList<>();
                LocalDateTime updateTime = null;
                List<TenantDefineDatasources> list = dataSourcesMapper.selectList(new QueryWrapper<TenantDefineDatasources>()
                        .eq("credentials_config_id", credentials.getId()));
                if (CollectionUtils.isNotEmpty(list)) {
                    for (TenantDefineDatasources tenantDefineDatasources : list) {
                        if(updateTime == null){
                            updateTime = tenantDefineDatasources.getUpdateTime();
                        }else{
                            updateTime = updateTime.isBefore(tenantDefineDatasources.getUpdateTime())?tenantDefineDatasources.getUpdateTime():updateTime;
                        }
                    }
                    refCnt = list.size();
                    refDesc.addAll(list.stream().map(TenantDefineDatasources::getName).collect(Collectors.toList()));
                }

                List<TbCluster> clusterList = clusterService.list(new LambdaUpdateWrapper<TbCluster>().eq(TbCluster::getClusterAuthType, 1)
                        .eq(TbCluster::getAuthConfigurationId, credentials.getId())
                        .eq(TbCluster::getDelFlag, "0"));
                if (CollectionUtils.isNotEmpty(clusterList)) {
                    for (TbCluster tbCluster : clusterList) {
                        LocalDateTime dateTime = DateUtil.Date2LocalDateTime(tbCluster.getUpdateTime());
                        if(updateTime == null){
                            updateTime = dateTime;
                        }else{
                            updateTime = updateTime.isBefore(dateTime)?dateTime:updateTime;
                        }
                    }
                    refCnt += clusterList.size();
                    refDesc.addAll(clusterList.stream().map(TbCluster::getClusterName).collect(Collectors.toList()));
                }
                credentials.setLastUsed(updateTime);

                credentials.setRefCnt(refCnt);
                credentials.setRefDesc(refDesc);
            }
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), result);
        }
    }
}
