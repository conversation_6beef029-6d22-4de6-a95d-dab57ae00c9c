package com.idss.datalake.datasource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datasource.entity.Queue;
import com.idss.datalake.datasource.entity.Tenant;
import com.idss.datalake.datasource.mapper.DsTenantMapper;
import com.idss.datalake.datasource.mapper.QueueMapper;
import com.idss.datalake.datasource.service.TenantService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class TenantServiceImpl extends ServiceImpl<DsTenantMapper, Tenant> implements TenantService {
    @Resource
    private DsTenantMapper dsTenantMapper;
    @Resource
    private QueueMapper queueMapper;

    private static final Logger logger = LoggerFactory.getLogger(TenantServiceImpl.class);

    @Override
    public Tenant createTenantIfNotExists(Integer originTenantId) throws Exception {
        Tenant tenant = dsTenantMapper.selectOne(new QueryWrapper<Tenant>().eq("origin_tenant_id", originTenantId));
        if (tenant != null) {
            logger.info("ds租户[{}]已存在", tenant.getTenantCode());
            return tenant;
        }
        Queue queue;
        List<Queue> queues = queueMapper.selectList(new QueryWrapper<Queue>().orderByDesc("create_time"));
        if (CollectionUtils.isEmpty(queues)) {
            queue = new Queue("default", "default");
            queueMapper.insert(queue);
        } else {
            queue = queues.get(0);
        }
        String tenantCode = "dolphin_" + originTenantId;
        tenant = new Tenant();
        tenant.setTenantCode(tenantCode);
        tenant.setOriginTenantId(originTenantId);
        tenant.setQueueId(queue.getId());
        tenant.setQueue(queue.getQueue());
        tenant.setCreateTime(new Date());
        tenant.setUpdateTime(new Date());
        // 如果是达梦，必须先：set identity_insert 表名 ON;
        dsTenantMapper.insert(tenant);
        logger.info("保存ds租户[{}]完成", tenantCode);

        // if storage startup
//        if (PropertyUtils.getResUploadStartupState()) {
//            storageOperate.createTenantDirIfNotExists(tenantCode);
//        }
        return tenant;
    }
}
