package com.idss.datalake.datasource.service;

import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datasource.dto.TenantDefineCertificatesPageRequest;
import com.idss.datalake.datasource.dto.TenantDefineCredentialsPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineCertificates;
import com.idss.datalake.datasource.entity.TenantDefineCredentials;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户凭证管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface ITenantDefineCredentialsService extends IService<TenantDefineCredentials> {
    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    BasePageResponse<List<TenantDefineCredentials>> page(TenantDefineCredentialsPageRequest request);
}
