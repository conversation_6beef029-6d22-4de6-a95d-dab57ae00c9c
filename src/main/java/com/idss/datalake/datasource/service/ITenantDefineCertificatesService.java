package com.idss.datalake.datasource.service;

import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datasource.dto.TenantDefineCertificatesPageRequest;
import com.idss.datalake.datasource.dto.TenantDefineKerberosConfigsPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineCertificates;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datasource.entity.TenantDefineKerberosConfigs;

import java.util.List;

/**
 * <p>
 * SSL证书管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface ITenantDefineCertificatesService extends IService<TenantDefineCertificates> {
    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    BasePageResponse<List<TenantDefineCertificates>> page(TenantDefineCertificatesPageRequest request);
}
