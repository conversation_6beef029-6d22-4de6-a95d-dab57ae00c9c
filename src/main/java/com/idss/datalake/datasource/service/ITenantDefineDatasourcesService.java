package com.idss.datalake.datasource.service;

import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datasource.dto.TenantDefineCredentialsPageRequest;
import com.idss.datalake.datasource.dto.TenantDefineDatasourcePageRequest;
import com.idss.datalake.datasource.entity.TenantDefineCredentials;
import com.idss.datalake.datasource.entity.TenantDefineDatasources;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 数据源管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface ITenantDefineDatasourcesService extends IService<TenantDefineDatasources> {
    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    BasePageResponse<List<TenantDefineDatasources>> page(TenantDefineDatasourcePageRequest request);
}
