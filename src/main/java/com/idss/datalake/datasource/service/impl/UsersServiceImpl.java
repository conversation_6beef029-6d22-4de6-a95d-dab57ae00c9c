package com.idss.datalake.datasource.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datasource.entity.User;
import com.idss.datalake.datasource.enums.UserType;
import com.idss.datalake.datasource.mapper.UserMapper;
import com.idss.datalake.datasource.service.UsersService;
import com.idss.datalake.datasource.util.EncryptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class UsersServiceImpl extends ServiceImpl<UserMapper, User> implements UsersService {
    @Resource
    private UserMapper userMapper;
    @Override
    @Transactional
    public User createUser(String userName, String userPassword, String email, int tenantId, String phone, String queue, int state) {
        User user = new User();
        Date now = new Date();

        user.setUserName(userName);
        user.setUserPassword(EncryptionUtils.getMd5(userPassword));
        user.setEmail(email);
        user.setTenantId(tenantId);
        user.setPhone(phone);
        user.setState(state);
        // create general users, administrator users are currently built-in
        user.setUserType(UserType.GENERAL_USER);
        user.setCreateTime(now);
        user.setUpdateTime(now);
        if (StringUtils.isEmpty(queue)) {
            queue = "";
        }
        user.setQueue(queue);

        // save user
        userMapper.insert(user);
        return user;
    }
}
