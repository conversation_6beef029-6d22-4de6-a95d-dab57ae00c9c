package com.idss.datalake.datasource.service;

import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datasource.dto.TenantDefineKerberosConfigsPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineKerberosConfigs;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * Kerberos认证配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface ITenantDefineKerberosConfigsService extends IService<TenantDefineKerberosConfigs> {
    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    BasePageResponse<List<TenantDefineKerberosConfigs>> page(TenantDefineKerberosConfigsPageRequest request);
}
