package com.idss.datalake.datasource.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datasource.dto.TenantDefineDatasourcePageRequest;
import com.idss.datalake.datasource.entity.TenantDefineCredentials;
import com.idss.datalake.datasource.entity.TenantDefineDatasources;
import com.idss.datalake.datasource.mapper.TenantDefineDatasourcesMapper;
import com.idss.datalake.datasource.service.ITenantDefineCertificatesService;
import com.idss.datalake.datasource.service.ITenantDefineCredentialsService;
import com.idss.datalake.datasource.service.ITenantDefineDatasourcesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datasource.service.ITenantDefineKerberosConfigsService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 数据源管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
public class TenantDefineDatasourcesServiceImpl extends ServiceImpl<TenantDefineDatasourcesMapper, TenantDefineDatasources> implements ITenantDefineDatasourcesService {
    @Resource
    private TenantDefineDatasourcesMapper tenantDefineDataSourcesMapper;
    @Autowired
    private ITenantDefineKerberosConfigsService kerberosConfigsService;
    @Autowired
    private ITenantDefineCertificatesService certificatesService;
    @Autowired
    private ITenantDefineCredentialsService credentialsService;
    @Override
    public BasePageResponse<List<TenantDefineDatasources>> page(TenantDefineDatasourcePageRequest request) {
        UserValueObject uvo = UmsUtils.getUVO();
        request.setTenantId(Long.valueOf(uvo.getTenantId()));
        PageHelper.offsetPage(request.getOffset(), request.getPageSize());
        Page<TenantDefineDatasources> page = tenantDefineDataSourcesMapper.page(request);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), new ArrayList<>());
        }
        for (TenantDefineDatasources tenantDefineDatasources : page.getResult()) {
            String info = tenantDefineDatasources.getType() + "://" + tenantDefineDatasources.getHost() + ":" + tenantDefineDatasources.getPort();

            if(tenantDefineDatasources.getKerberosEnabled()){
                info = info.concat("<br />").concat("KERBEROS认证("+kerberosConfigsService.getById(tenantDefineDatasources.getKerberosConfigId()).getName()+")");

            } else if (tenantDefineDatasources.getCertificatesEnabled()) {
                info = info.concat("<br />").concat("SSL认证("+certificatesService.getById(tenantDefineDatasources.getCertificatesConfigId()).getName()+")");
            } else if (tenantDefineDatasources.getCredentialsEnabled()) {
                info = info.concat("<br />").concat("基本认证("+ credentialsService.getById(tenantDefineDatasources.getCredentialsConfigId()).getName()+")");
            }else{
                info = info.concat("<br />").concat("无认证");
            }
            tenantDefineDatasources.setConnectInfo(info);
        }

        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), page.getResult());
    }
}
