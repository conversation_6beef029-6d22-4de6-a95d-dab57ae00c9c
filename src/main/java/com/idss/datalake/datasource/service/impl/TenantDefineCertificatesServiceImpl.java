package com.idss.datalake.datasource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datashare.cluster.entity.TbCluster;
import com.idss.datalake.datashare.cluster.service.ITbClusterService;
import com.idss.datalake.datasource.dto.TenantDefineCertificatesPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineCertificates;
import com.idss.datalake.datasource.entity.TenantDefineDatasources;
import com.idss.datalake.datasource.mapper.TenantDefineCertificatesMapper;
import com.idss.datalake.datasource.mapper.TenantDefineDatasourcesMapper;
import com.idss.datalake.datasource.service.ITenantDefineCertificatesService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * SSL证书管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
public class TenantDefineCertificatesServiceImpl extends ServiceImpl<TenantDefineCertificatesMapper, TenantDefineCertificates> implements ITenantDefineCertificatesService {
    @Resource
    private TenantDefineCertificatesMapper tenantDefineCertificatesMapper;
    @Resource
    private TenantDefineDatasourcesMapper dataSourcesMapper;

    @Resource
    private ITbClusterService clusterService;

    @Override
    public BasePageResponse<List<TenantDefineCertificates>> page(TenantDefineCertificatesPageRequest request) {
        UserValueObject uvo = UmsUtils.getUVO();
        request.setTenantId(Long.valueOf(uvo.getTenantId()));
        PageHelper.offsetPage(request.getOffset(), request.getPageSize());
        Page<TenantDefineCertificates> page = tenantDefineCertificatesMapper.page(request);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), new ArrayList<>());
        } else {
            List<TenantDefineCertificates> result = page.getResult();
            for (TenantDefineCertificates certificates : result) {
                int refCnt = 0;
                List<String> refDesc = new ArrayList<>();

                List<TenantDefineDatasources> list = dataSourcesMapper.selectList(new QueryWrapper<TenantDefineDatasources>()
                        .eq("certificates_config_id", certificates.getId()));
                if (CollectionUtils.isNotEmpty(list)) {
                    refCnt = list.size();
                    refDesc.addAll(list.stream().map(TenantDefineDatasources::getName).collect(Collectors.toList()));
                }

                List<TbCluster> clusterList = clusterService.list(new LambdaUpdateWrapper<TbCluster>().eq(TbCluster::getClusterAuthType, 3)
                        .eq(TbCluster::getAuthConfigurationId, certificates.getId())
                        .eq(TbCluster::getDelFlag, "0"));
                if (CollectionUtils.isNotEmpty(clusterList)) {
                    refCnt += clusterList.size();
                    refDesc.addAll(clusterList.stream().map(TbCluster::getClusterName).collect(Collectors.toList()));
                }
                certificates.setRefCnt(refCnt);
                certificates.setRefDesc(refDesc);
            }
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), result);
        }
    }
}
