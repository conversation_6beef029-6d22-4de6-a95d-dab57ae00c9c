package com.idss.datalake.datasource.service.impl;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datashare.cluster.entity.TbCluster;
import com.idss.datalake.datashare.cluster.service.ITbClusterService;
import com.idss.datalake.datasource.dto.TenantDefineKerberosConfigsPageRequest;
import com.idss.datalake.datasource.entity.TenantDefineDatasources;
import com.idss.datalake.datasource.entity.TenantDefineKerberosConfigs;
import com.idss.datalake.datasource.mapper.TenantDefineDatasourcesMapper;
import com.idss.datalake.datasource.mapper.TenantDefineKerberosConfigsMapper;
import com.idss.datalake.datasource.service.ITenantDefineKerberosConfigsService;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * Kerberos认证配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
public class TenantDefineKerberosConfigsServiceImpl extends ServiceImpl<TenantDefineKerberosConfigsMapper, TenantDefineKerberosConfigs> implements ITenantDefineKerberosConfigsService {
    @Resource
    private TenantDefineKerberosConfigsMapper kerberosConfigsMapper;
    @Resource
    private TenantDefineDatasourcesMapper dataSourcesMapper;

    @Resource
    private ITbClusterService clusterService;

    @Override
    public BasePageResponse<List<TenantDefineKerberosConfigs>> page(TenantDefineKerberosConfigsPageRequest request) {
        UserValueObject uvo = UmsUtils.getUVO();
        request.setTenantId(Long.valueOf(uvo.getTenantId()));
        PageHelper.offsetPage(request.getOffset(), request.getPageSize());
        Page<TenantDefineKerberosConfigs> page = kerberosConfigsMapper.page(request);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), new ArrayList<>());
        } else {
            List<TenantDefineKerberosConfigs> result = page.getResult();
            BASE64Decoder decoder = new BASE64Decoder();
            for (TenantDefineKerberosConfigs tenantDefineKerberosConfigs : result) {
                try {
                    tenantDefineKerberosConfigs.setKeytabFileName(FileUtil.getName(new String(decoder.decodeBuffer(tenantDefineKerberosConfigs.getKeytabPath()))));
                    tenantDefineKerberosConfigs.setKeytabUploaded(true);
                    tenantDefineKerberosConfigs.setKrb5FileName(FileUtil.getName(new String(decoder.decodeBuffer(tenantDefineKerberosConfigs.getKrb5Path()))));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }

                int refCnt = 0;
                List<String> refDesc = new ArrayList<>();

                List<TenantDefineDatasources> list = dataSourcesMapper.selectList(new QueryWrapper<TenantDefineDatasources>()
                        .eq("kerberos_config_id", tenantDefineKerberosConfigs.getId()));
                if (CollectionUtils.isNotEmpty(list)) {
                    refCnt = list.size();
                    refDesc.addAll(list.stream().map(TenantDefineDatasources::getName).collect(Collectors.toList()));
                }

                List<TbCluster> clusterList = clusterService.list(new LambdaUpdateWrapper<TbCluster>().eq(TbCluster::getClusterAuthType, 2)
                        .eq(TbCluster::getAuthConfigurationId, tenantDefineKerberosConfigs.getId())
                        .eq(TbCluster::getDelFlag, "0"));
                if (CollectionUtils.isNotEmpty(clusterList)) {
                    refCnt += clusterList.size();
                    refDesc.addAll(clusterList.stream().map(TbCluster::getClusterName).collect(Collectors.toList()));
                }

                tenantDefineKerberosConfigs.setRefCnt(refCnt);
                tenantDefineKerberosConfigs.setRefDesc(refDesc);
            }
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), result);
        }

    }
}
