/************************ CHAN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/11/6
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/11/6
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.dataencryption.dto;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/11/6
 */
@Data
public class AlgorithmJobPageRequest extends BasePageRequest {
    private String jobName;
}
