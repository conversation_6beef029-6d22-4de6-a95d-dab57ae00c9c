/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/11/8
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/11/8
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.dataencryption.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/11/8
 */
@Data
public class EncryptionAlgorithmJobUpdateDto {
   private Long id;
    /**
     * 任务名称
     */
    private String jobName;
    private String remark;
}
