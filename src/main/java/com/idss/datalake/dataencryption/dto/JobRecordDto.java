/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/11/8
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/11/8
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.dataencryption.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/11/8
 */
@Data
public class JobRecordDto {
    /**
     * 加密前样本
     */
    private String beforeText;

    /**
     * 加密后样本
     */
    private String afterText;
    /**
     * 应加密字段数
     */
    private Long shouldCount = 0L;

    /**
     * 实际加密字段数
     */
    private Long actualCount = 0L;
}
