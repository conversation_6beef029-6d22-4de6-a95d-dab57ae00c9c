/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/11/8
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/11/8
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.dataencryption.dto;

import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJob;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/11/8
 */
@Data
public class EncryptionAlgorithmJobAddDto {
    private DataEncryptionAlgorithmJob job;
    private List<DataEncryptionAlgorithmJobField> fields;
}
