package com.idss.datalake.dataencryption.service.impl;

import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobField;
import com.idss.datalake.dataencryption.mapper.DataEncryptionAlgorithmJobFieldMapper;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobFieldService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据加密算法任务字段 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Service
public class DataEncryptionAlgorithmJobFieldServiceImpl extends ServiceImpl<DataEncryptionAlgorithmJobFieldMapper, DataEncryptionAlgorithmJobField> implements IDataEncryptionAlgorithmJobFieldService {

}
