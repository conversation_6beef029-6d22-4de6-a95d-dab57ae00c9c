package com.idss.datalake.dataencryption.service;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.dataencryption.dto.AlgorithmPageRequest;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithm;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmBackUp;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.dict.bean.UebaDictionary;

import java.util.List;

/**
 * <p>
 * 数据加密算法 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public interface IDataEncryptionAlgorithmService extends IService<DataEncryptionAlgorithm> {
    ResultBean addOrUpdate(DataEncryptionAlgorithm algorithm);

    ResultBean enable(Long id, Integer state);

    BasePageResponse<List<DataEncryptionAlgorithm>> page(AlgorithmPageRequest request);

    ResultBean backup(Long id);

    BasePageResponse<List<DataEncryptionAlgorithmBackUp>> backupPge(AlgorithmPageRequest request);

    /**
     * 获取敏感分级
     *
     * @return
     */
    List<UebaDictionary> getSenLevel();

    /**
     * 获取敏感分类
     *
     * @return
     */
    List<UebaDictionary> getSenType();
}
