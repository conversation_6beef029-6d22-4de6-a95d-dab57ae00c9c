package com.idss.datalake.dataencryption.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.enums.DATA_SOURCE_TYPE_ENUM;
import com.idss.datalake.common.util.*;
import com.idss.datalake.dataencryption.dto.AlgorithmJobPageRequest;
import com.idss.datalake.dataencryption.dto.AlgorithmJobRecordPageRequest;
import com.idss.datalake.dataencryption.dto.EncryptionAlgorithmJobAddDto;
import com.idss.datalake.dataencryption.dto.EncryptionAlgorithmJobUpdateDto;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithm;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJob;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobField;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobRecord;
import com.idss.datalake.dataencryption.enums.SMEnums;
import com.idss.datalake.dataencryption.mapper.DataEncryptionAlgorithmJobMapper;
import com.idss.datalake.dataencryption.mapper.DataEncryptionAlgorithmJobRecordMapper;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobFieldService;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobRecordService;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmService;
import com.idss.datalake.dataencryption.task.CHEncryptTask;
import com.idss.datalake.dataencryption.task.ESEncryptTask;
import com.idss.datalake.dataencryption.task.HiveEncryptTask;
import com.idss.datalake.dataencryption.task.MysqlEncryptTask;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datashare.cluster.entity.TbCluster;
import com.idss.datalake.datashare.cluster.service.ITbClusterService;
import com.idss.datalake.datashare.tenant.entity.TbTenantCluster;
import com.idss.datalake.datashare.tenant.service.ITbTenantClusterService;
import com.idss.datalake.dict.bean.UebaDictionary;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Connection;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据加密算法任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Service
public class DataEncryptionAlgorithmJobServiceImpl extends ServiceImpl<DataEncryptionAlgorithmJobMapper, DataEncryptionAlgorithmJob> implements IDataEncryptionAlgorithmJobService {
    @Autowired
    private ThreadPoolExecutor threadPool;
    @Autowired
    private IDataEncryptionAlgorithmJobFieldService dataEncryptionAlgorithmJobFieldService;
    @Autowired
    private IDataEncryptionAlgorithmService encryptionAlgorithmService;
    @Autowired
    private ITbTenantClusterService tenantClusterService;
    @Autowired
    private ITbClusterService tbClusterService;
    @Autowired
    private IDataEncryptionAlgorithmJobRecordService recordService;
    @Resource
    private DataEncryptionAlgorithmJobRecordMapper recordMapper;

    @Override
    public ResultBean addJob(EncryptionAlgorithmJobAddDto dto) {
        UserValueObject uvo = UmsUtils.getUVO();
        List<DataEncryptionAlgorithmJob> list = this.list(new QueryWrapper<DataEncryptionAlgorithmJob>().eq("tenant_id", uvo.getTenantId()).eq(
                "job_name", dto.getJob().getJobName()));
        if (CollectionUtils.isNotEmpty(list)) {
            return ResultBean.fail("名称重复");
        }

        DataEncryptionAlgorithmJob job = dto.getJob();
        job.setState("1");
        job.setCreateUser(uvo.getUserName());
        job.setCreateTime(LocalDateTime.now());
        job.setUpdateUser(uvo.getUserName());
        job.setUpdateTime(LocalDateTime.now());
        job.setTenantId(uvo.getTenantId());
        List<DataEncryptionAlgorithmJobField> fields = dto.getFields();

        this.save(job);
        for (DataEncryptionAlgorithmJobField field : fields) {
            field.setJobId(job.getId());
            DataEncryptionAlgorithm algorithm = encryptionAlgorithmService.getById(field.getEncryptionAlgorithmId());
            field.setKeyAlgorithm(algorithm.getKeyAlgorithm());
            if (SMEnums.SM2.toString().equals(algorithm.getKeyAlgorithm())) {
                field.setSm2PublicKey(algorithm.getSm2PublicKey());
                field.setSm2PrivateKey(algorithm.getSm2PrivateKey());
            } else if (SMEnums.SM4.toString().equals(algorithm.getKeyAlgorithm())) {
                field.setSm4KeyBase64(algorithm.getSm4KeyBase64());
                field.setSm4IvBase64(algorithm.getSm4IvBase64());
            }
        }
        dataEncryptionAlgorithmJobFieldService.saveBatch(fields);

        String datasourceType = job.getDatasourceType();
        if (DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().equals(datasourceType)) {
            TbTenantCluster one = tenantClusterService.getOne(new QueryWrapper<TbTenantCluster>().eq("TENANT_ID", uvo.getTenantId()).eq(
                    "CLUSTER_TYPE", DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().toLowerCase()));
            TbCluster cluster = tbClusterService.getById(one.getClusterId());
            CHEncryptTask task = new CHEncryptTask();
            task.setUrl("jdbc:clickhouse://" + cluster.getNodeAddress() + "/" + one.getInstance());
            task.setUserName(cluster.getClusterUsername());
            task.setPassword(cluster.getClusterPassword());
            task.setDbName(one.getInstance());
            task.setAlgorithmJob(job);
            task.setFields(fields);
            task.setJobService(this);
            task.setRecordService(recordService);
            threadPool.execute(task);
        } else if (DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().equals(datasourceType)) {
            TbTenantCluster one = tenantClusterService.getOne(new QueryWrapper<TbTenantCluster>().eq("TENANT_ID", uvo.getTenantId()).eq(
                    "CLUSTER_TYPE", DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().toLowerCase()));
            TbCluster cluster = tbClusterService.getById(one.getClusterId());
            ESEncryptTask task = new ESEncryptTask();
            // String node = cluster.getNodeAddress().split(",")[0];
            // String[] split = node.split(":");
            // task.setIp(split[0]);
            // task.setPort(Integer.parseInt(split[1]));
            task.setNodes(cluster.getNodeAddress());
            task.setUserName(cluster.getClusterUsername());
            task.setPassword(cluster.getClusterPassword());
            task.setAlgorithmJob(job);
            task.setFields(fields);
            task.setJobService(this);
            task.setRecordService(recordService);
            threadPool.execute(task);
        } else if (DATA_SOURCE_TYPE_ENUM.MYSQL.getName().equals(datasourceType)) {
            TbTenantCluster one = tenantClusterService.getOne(new QueryWrapper<TbTenantCluster>().eq("TENANT_ID", uvo.getTenantId()).eq(
                    "CLUSTER_TYPE", DATA_SOURCE_TYPE_ENUM.MYSQL.getName().toLowerCase()));
            TbCluster cluster = tbClusterService.getById(one.getClusterId());
            MysqlEncryptTask task = new MysqlEncryptTask();
            task.setUrl("jdbc:mysql://" + cluster.getNodeAddress() + "/" + one.getInstance());
            task.setUserName(cluster.getClusterUsername());
            task.setPassword(cluster.getClusterPassword());
            task.setDbName(one.getInstance());
            task.setAlgorithmJob(job);
            task.setFields(fields);
            task.setJobService(this);
            task.setRecordService(recordService);
            threadPool.execute(task);
        } else if (DATA_SOURCE_TYPE_ENUM.HIVE.getName().equals(datasourceType)) {
            TbTenantCluster one = tenantClusterService.getOne(new QueryWrapper<TbTenantCluster>().eq("TENANT_ID", uvo.getTenantId()).eq(
                    "CLUSTER_TYPE", DATA_SOURCE_TYPE_ENUM.HIVE.getName().toLowerCase()));
            TbCluster cluster = tbClusterService.getById(one.getClusterId());
            HiveEncryptTask task = new HiveEncryptTask();
            task.setUrl("jdbc:hive2://" + cluster.getNodeAddress() + "/" + one.getInstance());
            task.setUserName(cluster.getClusterUsername());
            task.setPassword(cluster.getClusterPassword());
            task.setDbName(one.getInstance());
            task.setAlgorithmJob(job);
            task.setFields(fields);
            task.setJobService(this);
            task.setRecordService(recordService);
            threadPool.execute(task);
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean updateJob(EncryptionAlgorithmJobUpdateDto dto) {
        this.update(new UpdateWrapper<DataEncryptionAlgorithmJob>().set("update_time", LocalDateTime.now()).set("job_name", dto.getJobName()).set(
                "remark", dto.getRemark()).eq("id", dto.getId()));
        return ResultBean.success();
    }

    @Override
    public ResultBean selectJob(Long id) {
        EncryptionAlgorithmJobAddDto dto = new EncryptionAlgorithmJobAddDto();
        dto.setJob(this.getById(id));
        dto.setFields(dataEncryptionAlgorithmJobFieldService.list(new QueryWrapper<DataEncryptionAlgorithmJobField>().eq("job_id", id)));
        return ResultBean.success(dto);
    }

    @Override
    public ResultBean selectTarget(String datasourceType) {
        try {
            UserValueObject uvo = UmsUtils.getUVO();
            if (DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().equals(datasourceType)) {
                TbTenantCluster one = tenantClusterService.getOne(new QueryWrapper<TbTenantCluster>().eq("TENANT_ID", uvo.getTenantId()).eq(
                        "CLUSTER_TYPE", DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().toLowerCase()));
                if (one != null) {
                    TbCluster cluster = tbClusterService.getById(one.getClusterId());
                    Connection connect =
                            ClickhouseUtil.getConnect("jdbc:clickhouse://" + cluster.getNodeAddress() + "/" + one.getInstance(),
                                    cluster.getClusterUsername(), AESUtil.defaultDecrypt(cluster.getClusterPassword()));
                    List<String> tableList = ClickhouseUtil.queryDistributedTable(connect, one.getInstance());
                    connect.close();
                    return ResultBean.success(tableList);
                }
                return ResultBean.fail("该租户未分配此类数据源");
            } else if (DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().equals(datasourceType)) {
                TbTenantCluster one = tenantClusterService.getOne(new QueryWrapper<TbTenantCluster>().eq("TENANT_ID", uvo.getTenantId()).eq(
                        "CLUSTER_TYPE", DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().toLowerCase()));
                if (one != null) {
                    TbCluster cluster = tbClusterService.getById(one.getClusterId());
                    List<String> indexNames = ElasticSearchUtil.indexNames(cluster.getNodeAddress(), cluster.getClusterUsername(),
                            AESUtil.defaultDecrypt(cluster.getClusterPassword()));
                    return ResultBean.success(indexNames);
                }
                return ResultBean.fail("该租户未分配此类数据源");
            } else if (DATA_SOURCE_TYPE_ENUM.MYSQL.getName().equals(datasourceType)) {
                TbTenantCluster one = tenantClusterService.getOne(new QueryWrapper<TbTenantCluster>().eq("TENANT_ID", uvo.getTenantId()).eq(
                        "CLUSTER_TYPE", DATA_SOURCE_TYPE_ENUM.MYSQL.getName().toLowerCase()));
                if (one != null) {
                    TbCluster cluster = tbClusterService.getById(one.getClusterId());
                    Connection connect = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_MYSQL, "jdbc:mysql://" + cluster.getNodeAddress().split(","
                    )[0] + "/" + one.getInstance(), cluster.getClusterUsername(), AESUtil.defaultDecrypt(cluster.getClusterPassword()));
                    List<String> table = MysqlUtil.queryDbTable(connect, one.getInstance());
                    connect.close();
                    return ResultBean.success(table);
                }
                return ResultBean.fail("该租户未分配此类数据源");
            } else if (DATA_SOURCE_TYPE_ENUM.HIVE.getName().equals(datasourceType)) {
                TbTenantCluster one = tenantClusterService.getOne(new QueryWrapper<TbTenantCluster>().eq("TENANT_ID", uvo.getTenantId()).eq(
                        "CLUSTER_TYPE", DATA_SOURCE_TYPE_ENUM.HIVE.getName().toLowerCase()));
                if (one != null) {
                    TbCluster cluster = tbClusterService.getById(one.getClusterId());
                    cluster.setClusterPassword(AESUtil.defaultDecrypt(cluster.getClusterPassword()));

                    Connection connect = HiveUtil.getConnect(cluster, one.getInstance());
                    //                    Connection connect = HiveUtil.getConnect( "jdbc:hive2://" + cluster.getClusterIp().split(",")[0] + ":" +
                    //                    cluster.getClusterPort() + "/" + one.getInstance(), cluster.getClusterUsername(), cluster
                    //                    .getClusterPassword());
                    List<String> table = HiveUtil.queryDbTable(connect);
                    connect.close();
                    return ResultBean.success(table);
                }
                return ResultBean.fail("该租户未分配此类数据源");
            }
            return ResultBean.fail("数据源类型错误");
        } catch (Exception e) {
            log.error("查询数据源错误", e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @Override
    public ResultBean selectField(String datasourceType, String object) {
        try {
            UserValueObject uvo = UmsUtils.getUVO();
            if (DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().equals(datasourceType)) {
                TbTenantCluster one = tenantClusterService.getOne(new QueryWrapper<TbTenantCluster>().eq("TENANT_ID", uvo.getTenantId()).eq(
                        "CLUSTER_TYPE", DATA_SOURCE_TYPE_ENUM.CLICKHOUSE.getName().toLowerCase()));
                if (one != null) {
                    TbCluster cluster = tbClusterService.getById(one.getClusterId());
                    Connection connect =
                            ClickhouseUtil.getConnect("jdbc:clickhouse://" + cluster.getNodeAddress() + "/" + one.getInstance(),
                                    cluster.getClusterUsername(), AESUtil.defaultDecrypt(cluster.getClusterPassword()));
                    List<String> fieldList = ClickhouseUtil.queryTableField(connect, object);
                    connect.close();
                    return ResultBean.success(fieldList);
                }
                return ResultBean.fail("该租户未分配此类数据源");
            } else if (DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().equals(datasourceType)) {
                TbTenantCluster one = tenantClusterService.getOne(new QueryWrapper<TbTenantCluster>().eq("TENANT_ID", uvo.getTenantId()).eq(
                        "CLUSTER_TYPE", DATA_SOURCE_TYPE_ENUM.ELASTICSEARCH.getName().toLowerCase()));
                if (one != null) {
                    TbCluster cluster = tbClusterService.getById(one.getClusterId());
                    List<String> indexFields = ElasticSearchUtil.indexFields(cluster.getNodeAddress(), cluster.getClusterUsername(),
                            AESUtil.defaultDecrypt(cluster.getClusterPassword()), object);
                    return ResultBean.success(indexFields);
                }
                return ResultBean.fail("该租户未分配此类数据源");
            } else if (DATA_SOURCE_TYPE_ENUM.MYSQL.getName().equals(datasourceType)) {
                TbTenantCluster one = tenantClusterService.getOne(new QueryWrapper<TbTenantCluster>().eq("TENANT_ID", uvo.getTenantId()).eq(
                        "CLUSTER_TYPE", DATA_SOURCE_TYPE_ENUM.MYSQL.getName().toLowerCase()));
                if (one != null) {
                    TbCluster cluster = tbClusterService.getById(one.getClusterId());
                    Connection connect = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_MYSQL, "jdbc:mysql://" + cluster.getNodeAddress().split(","
                    )[0] + "/" + one.getInstance(), cluster.getClusterUsername(), AESUtil.defaultDecrypt(cluster.getClusterPassword()));
                    List<String> table = MysqlUtil.queryTableField(connect, object);
                    connect.close();
                    return ResultBean.success(table);
                }
                return ResultBean.fail("该租户未分配此类数据源");
            } else if (DATA_SOURCE_TYPE_ENUM.HIVE.getName().equals(datasourceType)) {
                TbTenantCluster one = tenantClusterService.getOne(new QueryWrapper<TbTenantCluster>().eq("TENANT_ID", uvo.getTenantId()).eq(
                        "CLUSTER_TYPE", DATA_SOURCE_TYPE_ENUM.HIVE.getName().toLowerCase()));
                if (one != null) {
                    TbCluster cluster = tbClusterService.getById(one.getClusterId());
                    cluster.setClusterPassword(AESUtil.defaultDecrypt(cluster.getClusterPassword()));
                    Connection connect = HiveUtil.getConnect(cluster, one.getInstance());
                    //                    Connection connect = HiveUtil.getConnect(Constant.DATASOURCE_DRIVER_HIVE, "jdbc:hive2://" +
                    //                    cluster.getClusterIp().split(",")[0] + ":" + cluster.getClusterPort() + "/" + one.getInstance()
                    //                    , cluster.getClusterUsername(), cluster.getClusterPassword());
                    List<String> fields = HiveUtil.queryTableField(connect, object);
                    connect.close();
                    return ResultBean.success(fields);
                }
                return ResultBean.fail("该租户未分配此类数据源");
            }
            return ResultBean.fail("数据源类型错误");
        } catch (Exception e) {
            log.error("查询数据源错误", e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @Override
    public BasePageResponse<List<DataEncryptionAlgorithmJob>> page(AlgorithmJobPageRequest requestDto) {
        List<DataEncryptionAlgorithmJob> list = new ArrayList<>();
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<DataEncryptionAlgorithmJob> page = this.getBaseMapper().queryPage(requestDto);
        List<DataEncryptionAlgorithmJob> result = page.getResult();
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        } else {
            for (DataEncryptionAlgorithmJob job : result) {
                List<DataEncryptionAlgorithmJobField> fields =
                        dataEncryptionAlgorithmJobFieldService.list(new QueryWrapper<DataEncryptionAlgorithmJobField>().eq("job_id", job.getId()));
                List<Integer> collect = fields.stream().map(DataEncryptionAlgorithmJobField::getEncryptionAlgorithmId).collect(Collectors.toList());
                String keyName =
                        encryptionAlgorithmService.listByIds(collect).stream().map(DataEncryptionAlgorithm::getKeyAlgorithm).collect(Collectors.joining(","));
                job.setKeyName(keyName);
            }
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), result);
    }

    @Override
    public ResultBean record(Long id) {
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> record = new HashMap<>();
        DataEncryptionAlgorithmJob job = this.getById(id);
        List<DataEncryptionAlgorithmJobField> fields =
                dataEncryptionAlgorithmJobFieldService.list(new QueryWrapper<DataEncryptionAlgorithmJobField>().eq("job_id", id));
        int fieldNum = 0;
        for (DataEncryptionAlgorithmJobField field : fields) {
            fieldNum += field.getFieldName().split(",").length;
        }
        record.put("fieldNum", fieldNum);
        record.put("state", "1".equals(job.getState()) ? "执行中" : "完成");
        record.put("createTime", DateUtil.getTimeStr(DateUtil.LocalDateTime2Date(job.getCreateTime()), "yyyy-MM-dd HH:mm:ss"));
        result.add(record);
        return ResultBean.success(result);
    }

    @Override
    public ResultBean detail(Long id) {
        Map<String, Object> result = new HashMap<>();
        DataEncryptionAlgorithmJob job = this.getById(id);
        if (job.getFinishTime() != null) {
            Duration duration = Duration.between(job.getCreateTime(), job.getFinishTime());
            job.setUseSecond(duration.getSeconds());
        }
        List<DataEncryptionAlgorithmJobField> fields =
                dataEncryptionAlgorithmJobFieldService.list(new QueryWrapper<DataEncryptionAlgorithmJobField>().eq("job_id", id));
        int fieldNum = 0;
        for (DataEncryptionAlgorithmJobField field : fields) {
            fieldNum += field.getFieldName().split(",").length;
        }

        result.put("id", job.getId());
        result.put("jobName", job.getJobName());
        result.put("state", "1".equals(job.getState()) ? "执行中" : "完成");
        result.put("datasourceType", job.getDatasourceType());
        result.put("encryptionType", job.getEncryptionType());
        result.put("shouldNum", fieldNum);
        result.put("actualNum", fieldNum);
        result.put("createTime", DateUtil.getTimeStr(DateUtil.LocalDateTime2Date(job.getCreateTime()), "yyyy-MM-dd HH:mm:ss"));
        if (job.getFinishTime() != null) {
            result.put("finishTime", DateUtil.getTimeStr(DateUtil.LocalDateTime2Date(job.getFinishTime()), "yyyy-MM-dd HH:mm:ss"));
        } else {
            result.put("finishTime", "");
        }

        result.put("useSecond", job.getUseSecond());

        return ResultBean.success(result);
    }

    @Override
    public BasePageResponse<List<DataEncryptionAlgorithmJobRecord>> recordPage(AlgorithmJobRecordPageRequest requestDto) {
        List<DataEncryptionAlgorithmJobField> fields =
                dataEncryptionAlgorithmJobFieldService.list(new QueryWrapper<DataEncryptionAlgorithmJobField>().eq("job_id", requestDto.getJobId()));
        List<DataEncryptionAlgorithmJobRecord> list = new ArrayList<>();
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<DataEncryptionAlgorithmJobRecord> page = this.recordMapper.queryPage(requestDto);
        List<DataEncryptionAlgorithmJobRecord> result = page.getResult();
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        } else {
            for (DataEncryptionAlgorithmJobRecord record : result) {
                if (record.getShouldCount() == null || record.getActualCount() == null ||
                        record.getShouldCount() == 0L || record.getActualCount() == 0L) {
                    record.setSuccessRate("0%");
                }
                double percentage = ((double) record.getActualCount() / record.getShouldCount()) * 100.0;
                record.setSuccessRate(Math.round(percentage) + "%");

                List<DataEncryptionAlgorithmJobField> collect =
                        fields.stream().filter(data -> data.getFieldName().contains(record.getFieldName())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    record.setKeyName(encryptionAlgorithmService.getById(collect.get(0).getEncryptionAlgorithmId()).getKeyName());
                }
            }
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), result);
    }
}
