package com.idss.datalake.dataencryption.service;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.dataencryption.dto.AlgorithmJobPageRequest;
import com.idss.datalake.dataencryption.dto.AlgorithmJobRecordPageRequest;
import com.idss.datalake.dataencryption.dto.EncryptionAlgorithmJobAddDto;
import com.idss.datalake.dataencryption.dto.EncryptionAlgorithmJobUpdateDto;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJob;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobRecord;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import java.util.List;

/**
 * <p>
 * 数据加密算法任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public interface IDataEncryptionAlgorithmJobService extends IService<DataEncryptionAlgorithmJob> {
    /**
     * 新增加密任务
     * @param dto
     * @return
     */
    ResultBean addJob(EncryptionAlgorithmJobAddDto dto);

    /**
     * 更新加密任务
     * @param dto
     * @return
     */
    ResultBean updateJob(EncryptionAlgorithmJobUpdateDto dto);

    /**
     * 加密任务查看
     * @param id
     * @return
     */
    ResultBean selectJob(Long id);

    /**
     * 下拉对象
     * @param datasourceType
     * @return
     */
    ResultBean selectTarget(String datasourceType);

    /**
     * 下拉字段
     * @param datasourceType
     * @param object
     * @return
     */
    ResultBean selectField(String datasourceType,String object);


    /**
     * 分页查询
     * @param request
     * @return
     */
    BasePageResponse<List<DataEncryptionAlgorithmJob>> page(AlgorithmJobPageRequest request);


    /**
     * 加密记录
     * @param id
     * @return
     */
    ResultBean record(Long id);

    /**
     * 加密详情
     * @param id
     * @return
     */
    ResultBean detail(Long id);

    /**
     * 加密记录分页查询
     * @param request
     * @return
     */
    BasePageResponse<List<DataEncryptionAlgorithmJobRecord>> recordPage(AlgorithmJobRecordPageRequest request);

}
