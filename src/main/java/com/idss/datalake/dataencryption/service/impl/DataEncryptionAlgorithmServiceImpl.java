package com.idss.datalake.dataencryption.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.dataencryption.dto.AlgorithmPageRequest;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithm;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmBackUp;
import com.idss.datalake.dataencryption.enums.SMEnums;
import com.idss.datalake.dataencryption.mapper.DataEncryptionAlgorithmBackUpMapper;
import com.idss.datalake.dataencryption.mapper.DataEncryptionAlgorithmMapper;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmBackUpService;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.dataencryption.util.BcSm4Util;
import com.idss.datalake.dataencryption.util.KeyUtils;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.dict.bean.UebaDictionary;
import com.idss.datalake.dict.service.IUebaDictionaryService;
import com.idss.radar.datasource.DatasourceType;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据加密算法 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Service
@Slf4j
public class DataEncryptionAlgorithmServiceImpl extends ServiceImpl<DataEncryptionAlgorithmMapper, DataEncryptionAlgorithm> implements IDataEncryptionAlgorithmService {
    @Autowired
    private IUebaDictionaryService uebaDictionaryService;
    @Autowired
    private IDataEncryptionAlgorithmBackUpService dataEncryptionAlgorithmBackUpService;

    @Resource
    private DataEncryptionAlgorithmBackUpMapper dataEncryptionAlgorithmBackUpMapper;

    @Override
    public ResultBean addOrUpdate(DataEncryptionAlgorithm algorithm) {
        UserValueObject uvo = UmsUtils.getUVO();
        try {
            if (algorithm.getId() == null) {
                List<DataEncryptionAlgorithm> list = this.list(new QueryWrapper<DataEncryptionAlgorithm>().eq("tenant_id", uvo.getTenantId()).eq(
                        "key_name", algorithm.getKeyName()));
                if (CollectionUtils.isNotEmpty(list)) {
                    return ResultBean.fail("名称重复");
                }
                if (SMEnums.SM2.toString().equals(algorithm.getKeyAlgorithm())) {
                    String[] keys = KeyUtils.generateSmKey();
                    algorithm.setSm2PublicKey(keys[0]);
                    algorithm.setSm2PrivateKey(keys[1]);
                } else if (SMEnums.SM4.toString().equals(algorithm.getKeyAlgorithm())) {
                    byte[] key = BcSm4Util.generateKey();
                    byte[] iv = new SecureRandom().generateSeed(16);
                    algorithm.setSm4KeyBase64(Base64.getEncoder().encodeToString(key));
                    algorithm.setSm4IvBase64(Base64.getEncoder().encodeToString(iv));
                }
                algorithm.setCreateTime(LocalDateTime.now());
                algorithm.setCreateUser(uvo.getUserName());
                algorithm.setUpdateTime(LocalDateTime.now());
                algorithm.setUpdateUser(uvo.getUserName());
                algorithm.setTenantId(uvo.getTenantId());
                algorithm.setIsEnable(1);
            } else {
                List<DataEncryptionAlgorithm> list = this.list(new QueryWrapper<DataEncryptionAlgorithm>().ne("id", algorithm.getId()).eq(
                        "tenant_id", uvo.getTenantId()).eq("key_name", algorithm.getKeyName()));
                if (CollectionUtils.isNotEmpty(list)) {
                    return ResultBean.fail("名称重复");
                }
                algorithm.setUpdateTime(LocalDateTime.now());
                algorithm.setUpdateUser(uvo.getUserName());
            }
            this.saveOrUpdate(algorithm);
        } catch (Exception e) {
            log.error("保存错误", e);
            return ResultBean.fail(e.getMessage());
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean enable(Long id, Integer state) {
        DataEncryptionAlgorithm byId = this.getById(id);
        byId.setIsEnable(state);
        this.updateById(byId);
        return ResultBean.success();
    }

    @Override
    public BasePageResponse<List<DataEncryptionAlgorithm>> page(AlgorithmPageRequest requestDto) {
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, UmsUtils.getUVO().getTenantId());
        List<UebaDictionary> levelList = uebaDictionaryService.list(new QueryWrapper<UebaDictionary>().eq("type", "ASSET_RESOURCE_LEVEL"));
        List<UebaDictionary> typeList = uebaDictionaryService.list(new QueryWrapper<UebaDictionary>().eq("type", "ASSET_RESOURCE_CLASSIFICATION"));
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<DataEncryptionAlgorithm> list = new ArrayList<>();
        requestDto.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(requestDto.getOffset(), requestDto.getPageSize());
        Page<DataEncryptionAlgorithm> page = this.getBaseMapper().queryPage(requestDto);
        List<DataEncryptionAlgorithm> result = page.getResult();
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        } else {
            for (DataEncryptionAlgorithm algorithm : result) {
                if (algorithm.getLevelId() != null) {
                    List<UebaDictionary> collect =
                            levelList.stream().filter(data -> data.getId().longValue() == algorithm.getLevelId().longValue()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        algorithm.setLevelName(collect.get(0).getValue());
                    }
                }
                if (algorithm.getTypeId() != null) {
                    List<UebaDictionary> collect1 =
                            typeList.stream().filter(data -> data.getId().longValue() == algorithm.getTypeId().longValue()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect1)) {
                        algorithm.setTypeName(collect1.get(0).getValue());
                    }
                }
            }
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), result);
    }

    @Override
    public ResultBean backup(Long id) {
        UserValueObject uvo = UmsUtils.getUVO();
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, uvo.getTenantId());
        List<UebaDictionary> levelList = uebaDictionaryService.list(new QueryWrapper<UebaDictionary>().eq("type", "ASSET_RESOURCE_LEVEL"));
        List<UebaDictionary> typeList = uebaDictionaryService.list(new QueryWrapper<UebaDictionary>().eq("type", "ASSET_RESOURCE_CLASSIFICATION"));
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        DataEncryptionAlgorithm byId = this.getById(id);

        if (byId.getLevelId() != null) {
            List<UebaDictionary> collect =
                    levelList.stream().filter(data -> data.getId().longValue() == byId.getLevelId().longValue()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                byId.setLevelName(collect.get(0).getValue());
            }
        }
        if (byId.getTypeId() != null) {
            List<UebaDictionary> collect1 =
                    typeList.stream().filter(data -> data.getId().longValue() == byId.getTypeId().longValue()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect1)) {
                byId.setTypeName(collect1.get(0).getValue());
            }
        }

        DataEncryptionAlgorithmBackUp backUp = new DataEncryptionAlgorithmBackUp();
        backUp.setEncryptionAlgorithmId(id);
        backUp.setKeyName(byId.getKeyName());
        backUp.setKeyAlgorithm(byId.getKeyAlgorithm());
        backUp.setLevelId(byId.getLevelId());
        backUp.setLevelName(byId.getLevelName());
        backUp.setTypeId(byId.getTypeId());
        backUp.setTypeName(byId.getTypeName());
        backUp.setRemark(byId.getRemark());
        backUp.setSm2PublicKey(byId.getSm2PublicKey());
        backUp.setSm2PrivateKey(byId.getSm2PrivateKey());
        backUp.setSm4KeyBase64(byId.getSm4KeyBase64());
        backUp.setSm4IvBase64(byId.getSm4IvBase64());
        backUp.setBackUpUser(uvo.getUserName());
        backUp.setBackUpTime(LocalDateTime.now());
        backUp.setTenantId(uvo.getTenantId());
        dataEncryptionAlgorithmBackUpService.save(backUp);
        return ResultBean.success();
    }

    @Override
    public BasePageResponse<List<DataEncryptionAlgorithmBackUp>> backupPge(AlgorithmPageRequest request) {
        List<DataEncryptionAlgorithmBackUp> list = new ArrayList<>();
        request.setTenantId(Long.valueOf(UmsUtils.getUVO().getTenantId()));
        PageHelper.offsetPage(request.getOffset(), request.getPageSize());
        Page<DataEncryptionAlgorithmBackUp> page = dataEncryptionAlgorithmBackUpMapper.queryPage(request);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        List<DataEncryptionAlgorithmBackUp> backUps = page.getResult();
        for (DataEncryptionAlgorithmBackUp backUp : backUps) {
            if(StringUtils.isEmpty(backUp.getSm4KeyBase64())) {
                backUp.setSm4KeyBase64(backUp.getSm2PrivateKey());
            }
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), backUps);
    }

    @Override
    public List<UebaDictionary> getSenLevel() {
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, UmsUtils.getUVO().getTenantId());
        List<UebaDictionary> list = uebaDictionaryService.list(new QueryWrapper<UebaDictionary>().eq("type", "ASSET_RESOURCE_LEVEL"));
        return list;
    }

    @Override
    public List<UebaDictionary> getSenType() {
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, UmsUtils.getUVO().getTenantId());
        List<UebaDictionary> list = uebaDictionaryService.list(new QueryWrapper<UebaDictionary>().eq("type", "ASSET_RESOURCE_CLASSIFICATION"));
        return list;
    }
}
