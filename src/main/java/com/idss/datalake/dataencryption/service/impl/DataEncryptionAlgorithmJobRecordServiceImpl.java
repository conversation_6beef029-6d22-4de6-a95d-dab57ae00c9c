package com.idss.datalake.dataencryption.service.impl;

import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobRecord;
import com.idss.datalake.dataencryption.mapper.DataEncryptionAlgorithmJobRecordMapper;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据加密算法任务记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Service
public class DataEncryptionAlgorithmJobRecordServiceImpl extends ServiceImpl<DataEncryptionAlgorithmJobRecordMapper, DataEncryptionAlgorithmJobRecord> implements IDataEncryptionAlgorithmJobRecordService {

}
