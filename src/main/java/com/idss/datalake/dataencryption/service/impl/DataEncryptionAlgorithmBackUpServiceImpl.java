package com.idss.datalake.dataencryption.service.impl;

import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmBackUp;
import com.idss.datalake.dataencryption.mapper.DataEncryptionAlgorithmBackUpMapper;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmBackUpService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据加密算法备份 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Service
public class DataEncryptionAlgorithmBackUpServiceImpl extends ServiceImpl<DataEncryptionAlgorithmBackUpMapper, DataEncryptionAlgorithmBackUp> implements IDataEncryptionAlgorithmBackUpService {

}
