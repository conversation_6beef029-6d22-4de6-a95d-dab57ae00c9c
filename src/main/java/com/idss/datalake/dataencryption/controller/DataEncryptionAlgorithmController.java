package com.idss.datalake.dataencryption.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.util.MD5Util;
import com.idss.datalake.dataencryption.dto.AlgorithmPageRequest;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithm;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmBackUp;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobField;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmBackUpService;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobFieldService;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.datashare.tenant.service.ITbTenantService;
import com.idss.datalake.dict.bean.UebaDictionary;
import com.idss.datalake.dict.service.IUebaDictionaryService;
import com.idss.radar.datasource.DatasourceType;
import com.idss.radar.util.UmsUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 数据加密算法 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/dataencryption/algorithm")
public class DataEncryptionAlgorithmController {
    @Autowired
    private IUebaDictionaryService uebaDictionaryService;

    @Autowired
    private IDataEncryptionAlgorithmService dataEncryptionAlgorithmService;

    @Autowired
    private IDataEncryptionAlgorithmBackUpService dataEncryptionAlgorithmBackUpService;
    @Autowired
    private ITbTenantService tenantService;
    @Autowired
    private IDataEncryptionAlgorithmJobFieldService jobFieldService;

    /**
     * 分级下拉
     *
     * @return
     */
    @GetMapping("/senLevel")
    public ResultBean senLevel() {
        List<UebaDictionary> list = dataEncryptionAlgorithmService.getSenLevel();
        return ResultBean.success(list);
    }

    /**
     * 分类下拉
     *
     * @return
     */
    @GetMapping("/senType")
    public ResultBean senType() {
        List<UebaDictionary> list = dataEncryptionAlgorithmService.getSenType();
        return ResultBean.success(list);
    }

    /**
     * 新增或修改算法
     *
     * @param algorithm
     * @return
     */
    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody DataEncryptionAlgorithm algorithm) {
        return dataEncryptionAlgorithmService.addOrUpdate(algorithm);
    }

    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/page")
    public BasePageResponse<List<DataEncryptionAlgorithm>> page(@RequestBody AlgorithmPageRequest request) {
        return dataEncryptionAlgorithmService.page(request);
    }

    /**
     * 启停
     *
     * @param id
     * @param state
     * @return
     */
    @GetMapping("/enbale/{id}/{state}")
    public ResultBean enbale(@PathVariable("id") Long id, @PathVariable("state") Integer state) {
        return dataEncryptionAlgorithmService.enable(id, state);
    }

    /**
     * 查看
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        return ResultBean.success(dataEncryptionAlgorithmService.getById(id));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @GetMapping("/remove/{id}")
    public ResultBean remove(@PathVariable("id") Long id) {
        List<DataEncryptionAlgorithmJobField> list = jobFieldService.list(new QueryWrapper<DataEncryptionAlgorithmJobField>().eq(
                "encryption_algorithm_id", id));
        if (CollectionUtils.isNotEmpty(list)) {
            return ResultBean.fail("密钥被引用，无法删除");
        }
        dataEncryptionAlgorithmService.removeById(id);
        return ResultBean.success();
    }

    /**
     * 备份
     *
     * @param id
     * @return
     */
    @GetMapping("/backup/{id}")
    public ResultBean backup(@PathVariable("id") Long id) {
        return dataEncryptionAlgorithmService.backup(id);
    }

    /**
     * 备份详情
     *
     * @param id
     * @return
     */
    @GetMapping("/backup/detail/{id}")
    public ResultBean backupDetail(@PathVariable("id") Long id) {
        return ResultBean.success(dataEncryptionAlgorithmBackUpService.getById(id));
    }

    /**
     * 备份删除
     *
     * @param id
     * @return
     */
    @GetMapping("/backup/remove/{id}")
    public ResultBean backupRemove(@PathVariable("id") Long id) {
        return ResultBean.success(dataEncryptionAlgorithmBackUpService.removeById(id));
    }

    /**
     * 备份分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/backupPge")
    public BasePageResponse<List<DataEncryptionAlgorithmBackUp>> backupPge(@RequestBody AlgorithmPageRequest request) {
        return dataEncryptionAlgorithmService.backupPge(request);
    }

    /**
     * 下拉数据
     *
     * @return
     */
    @GetMapping("/listAll")
    public ResultBean listAll() {
        return ResultBean.success(dataEncryptionAlgorithmService.list(new QueryWrapper<DataEncryptionAlgorithm>().eq("tenant_id",
                UmsUtils.getUVO().getTenantId())));
    }

    @GetMapping("/checkPwd/{pwd}")
    public ResultBean checkPwd(@PathVariable("pwd") String pwd) {
        TbTenant tenant = tenantService.getById(UmsUtils.getUVO().getTenantId());
        if (tenant.getAccountPwd().equals(MD5Util.md5Encode(pwd, tenant.getTenantCode()))) {
            return ResultBean.success();
        }
        return ResultBean.fail("密码错误");
    }

}

