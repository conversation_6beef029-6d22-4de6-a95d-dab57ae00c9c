package com.idss.datalake.dataencryption.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.dataencryption.dto.AlgorithmJobPageRequest;
import com.idss.datalake.dataencryption.dto.AlgorithmJobRecordPageRequest;
import com.idss.datalake.dataencryption.dto.EncryptionAlgorithmJobAddDto;
import com.idss.datalake.dataencryption.dto.EncryptionAlgorithmJobUpdateDto;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJob;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobField;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobRecord;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobFieldService;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobRecordService;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobService;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 数据加密算法任务 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/dataencryption/job")
@Slf4j
public class DataEncryptionAlgorithmJobController {
    @Autowired
    private IDataEncryptionAlgorithmJobService jobService;
    @Autowired
    private IDataEncryptionAlgorithmJobFieldService jobFieldService;
    @Autowired
    private IDataEncryptionAlgorithmJobRecordService jobRecordService;

    /**
     * 新增加密任务
     *
     * @param dto
     * @return
     */
    @PostMapping("/addJob")
    public ResultBean addJob(@RequestBody EncryptionAlgorithmJobAddDto dto) {
        return jobService.addJob(dto);
    }

    /**
     * 更新加密任务
     *
     * @param dto
     * @return
     */
    @PostMapping("/updateJob")
    public ResultBean updateJob(@RequestBody EncryptionAlgorithmJobUpdateDto dto) {
        return jobService.updateJob(dto);
    }

    /**
     * 加密任务查看
     *
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public ResultBean selectJob(@PathVariable("id") Long id) {
        return jobService.selectJob(id);
    }

    /**
     * 加密任务删除
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    public ResultBean delete(@PathVariable("id") Long id){
        DataEncryptionAlgorithmJob byId = jobService.getById(id);
        if("2".equals(byId.getState())){
            jobService.removeById(id);
            jobFieldService.remove(new QueryWrapper<DataEncryptionAlgorithmJobField>().eq("job_id",id));
            jobRecordService.remove(new QueryWrapper<DataEncryptionAlgorithmJobRecord>().eq("job_id",id));
            return ResultBean.success();
        }else{
            return ResultBean.fail("加密任务执行中，不能删除");
        }
    }

    /**
     * 加密记录
     *
     * @param id
     * @return
     */
    @GetMapping("/record/{id}")
    public ResultBean record(@PathVariable("id") Long id) {
        return jobService.record(id);
    }

    /**
     * 下拉对象
     *
     * @param datasourceType
     * @return
     */
    @GetMapping("/selectTarget/{datasourceType}")
    public ResultBean selectTarget(@PathVariable("datasourceType") String datasourceType) {
        return jobService.selectTarget(datasourceType);
    }

    /**
     * 下拉字段
     *
     * @param datasourceType
     * @param object
     * @return
     */
    @GetMapping("/selectTarget/{datasourceType}/{object:.+}")
    public ResultBean selectField(@PathVariable("datasourceType") String datasourceType, @PathVariable("object") String object) {
        log.info("查询字段参数:{},{}",datasourceType,object);
        return jobService.selectField(datasourceType, object);
    }

    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/page")
    public BasePageResponse<List<DataEncryptionAlgorithmJob>> page(@RequestBody AlgorithmJobPageRequest request) {
        return jobService.page(request);
    }

    /**
     * 加密详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        return jobService.detail(id);
    }

    /**
     * 加密记录分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/recordPage")
    public BasePageResponse<List<DataEncryptionAlgorithmJobRecord>> recordPage(@RequestBody AlgorithmJobRecordPageRequest request) {
        return jobService.recordPage(request);
    }

}
