/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/11/6
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/11/6
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.dataencryption.task;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.idss.datalake.common.util.ElasticSearchUtil;
import com.idss.datalake.dataencryption.dto.JobRecordDto;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJob;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobField;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobRecord;
import com.idss.datalake.dataencryption.enums.SMEnums;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobRecordService;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobService;
import com.idss.datalake.dataencryption.util.BcSm2Util;
import com.idss.datalake.dataencryption.util.BcSm3Util;
import com.idss.datalake.dataencryption.util.BcSm4Util;
import com.idss.datalake.dataencryption.util.KeyUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.search.SearchHit;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/11/6
 */
@Data
@Slf4j
public class ESEncryptTask implements Runnable {
    private static final String SM4_ALGORITHM = "SM4/ECB/PKCS5PADDING";
    //多个IP逗号分割： ************,************,************
    // private String ip;
    // private Integer port;
    private String nodes;
    private String userName;
    private String password;
    private DataEncryptionAlgorithmJob algorithmJob;
    private List<DataEncryptionAlgorithmJobField> fields;
    private IDataEncryptionAlgorithmJobService jobService;
    private IDataEncryptionAlgorithmJobRecordService recordService;

    @Override
    public void run() {
        String srcIndexName = algorithmJob.getEncryptionObject();
        String targetIndexName = algorithmJob.getTargetObject();

        //获取所有字段
        List<String> srcFields = ElasticSearchUtil.indexFields(nodes, userName, password, srcIndexName);

        //获取源索引信息
        ElasticSearchUtil.IndexInfo indexInfo = ElasticSearchUtil.indexSettings(nodes, userName, password, srcIndexName);

        //获取所有加密字段
        List<String> replaceFields = fields.stream().map(field -> field.getFieldName().split(",")).map(Arrays::asList).reduce((r1, r2) -> {
            r2.addAll(r1);
            return r2;
        }).get();

        String mappingJSON = indexInfo.getMappingJSON();
        JSONObject jsonObject = JSONObject.parseObject(mappingJSON);
        JSONObject properties = jsonObject.getJSONObject("properties");
        for (String replaceField : replaceFields) {
            if (properties.containsKey(replaceField)) {
                properties.getJSONObject(replaceField).clear();
                properties.getJSONObject(replaceField).put("type", "text");
            }
        }
        jsonObject.put("properties", properties);
        mappingJSON = jsonObject.toJSONString();

        //创建目标索引
        log.info("创建目标索引:{}", mappingJSON);
        ElasticSearchUtil.createIndex(nodes, userName, password, targetIndexName, indexInfo.getNumberOfShards(), indexInfo.getNumberOfReplicas(),
                mappingJSON);
        //加密数据
        readAndWrite(srcIndexName, targetIndexName);
    }

    private void readAndWrite(String srcIndex, String targetIndex) {
        String[] split = nodes.split(",");
        HttpHost[] hosts = new HttpHost[split.length];
        for (int i = 0; i < split.length; i++) {
            String[] node = split[i].split(":");
            hosts[i] = new HttpHost(node[0], Integer.parseInt(node[1]), "http");
        }
        try (RestHighLevelClient client = new RestHighLevelClient(
                RestClient.builder(
                        hosts
                ).setHttpClientConfigCallback(httpClientBuilder -> {
                    CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    credentialsProvider.setCredentials(AuthScope.ANY,
                            new UsernamePasswordCredentials(userName, password));
                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                })
        )) {
            List<String> replaceFields = fields.stream().map(field -> field.getFieldName().split(",")).map(Arrays::asList).reduce((r1, r2) -> {
                r2.addAll(r1);
                return r2;
            }).get();
            Map<String, JobRecordDto> recordMap = new HashMap<>();
            for (String replaceField : replaceFields) {
                recordMap.put(replaceField, new JobRecordDto());
            }

            SearchRequest searchRequest = new SearchRequest(srcIndex); // 替换为您的索引名称
            // 设置滚动时间和滚动ID
            searchRequest.scroll(TimeValue.timeValueMinutes(1)); // 设置滚动时间
            searchRequest.source().size(500);

            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = searchResponse.getHits().getHits();
            if (hits != null && hits.length > 0) {
                write(client, searchResponse.getHits().getHits(), targetIndex, recordMap);
            }

            String scrollId = searchResponse.getScrollId();

            while (true) {
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(TimeValue.timeValueMinutes(1));

                searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
                SearchHit[] searchHits = searchResponse.getHits().getHits();

                if (searchHits != null && searchHits.length > 0) {
                    // 处理每一批搜索结果
                    write(client, searchResponse.getHits().getHits(), targetIndex, recordMap);
                } else {
                    break; // 所有数据已经检索完成
                }
            }

            // 清除滚动上下文
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);

            //保存记录
            List<DataEncryptionAlgorithmJobRecord> records = new ArrayList<>();
            recordMap.forEach((field, recordDto) -> {
                DataEncryptionAlgorithmJobRecord record = new DataEncryptionAlgorithmJobRecord();
                record.setJobId(algorithmJob.getId());
                record.setFieldName(field);
                record.setBeforeText(recordDto.getBeforeText());
                record.setAfterText(recordDto.getAfterText());
                record.setShouldCount(recordDto.getShouldCount());
                record.setActualCount(recordDto.getActualCount());
                records.add(record);
            });
            jobService.update(new UpdateWrapper<DataEncryptionAlgorithmJob>().set("finish_time", LocalDateTime.now()).set("state", 2).eq("id",
                    algorithmJob.getId()));
            recordService.saveBatch(records);
        } catch (Exception e) {
            log.error("ES加密任务失败", e);
            jobService.update(new UpdateWrapper<DataEncryptionAlgorithmJob>().set("result", e.getMessage()).set("finish_time", LocalDateTime.now()).set("state", 2).eq("id", algorithmJob.getId()));
        }
    }

    private void write(RestHighLevelClient client, SearchHit[] searchHits, String targetIndex, Map<String, JobRecordDto> recordMap) throws Exception {
        BulkRequest bulkRequest = new BulkRequest();
        for (SearchHit searchHit : searchHits) {
            JSONObject jsonObject = JSONObject.parseObject(searchHit.getSourceAsString());
            for (DataEncryptionAlgorithmJobField jobField : this.fields) {
                String[] split = jobField.getFieldName().split(",");
                for (String field : split) {
                    JobRecordDto jobRecordDto = recordMap.get(field);
                    String before = jsonObject.get(field).toString();
                    if (jobField.getKeyAlgorithm().equals(SMEnums.SM2.toString())) {
                        try {
                            if (StringUtils.isEmpty(jobRecordDto.getBeforeText()) && StringUtils.isNotEmpty(before)) {
                                jobRecordDto.setBeforeText(before);
                            }
                            String after = StringUtils.isEmpty(before) ? "" :
                                    Base64.getEncoder().encodeToString(BcSm2Util.encrypt(before.getBytes(),
                                            KeyUtils.createPublicKey(jobField.getSm2PublicKey())));
                            if (StringUtils.isEmpty(jobRecordDto.getAfterText()) && StringUtils.isNotEmpty(after)) {
                                jobRecordDto.setAfterText(after);
                            }
                            jsonObject.put(field, after);
                            if (StringUtils.isNotEmpty(after)) {
                                jobRecordDto.setActualCount(jobRecordDto.getActualCount() + 1);
                            }
                        } catch (Exception e) {
                            jsonObject.put(field, null);
                        }

                    } else if (jobField.getKeyAlgorithm().equals(SMEnums.SM3.toString())) {
                        try {
                            if (StringUtils.isEmpty(jobRecordDto.getBeforeText()) && StringUtils.isNotEmpty(before)) {
                                jobRecordDto.setBeforeText(before);
                            }
                            String after = StringUtils.isEmpty(before) ? "" : BcSm3Util.sm3Hex(before.getBytes());
                            if (StringUtils.isEmpty(jobRecordDto.getAfterText()) && StringUtils.isNotEmpty(after)) {
                                jobRecordDto.setAfterText(after);
                            }
                            jsonObject.put(field, after);
                            if (StringUtils.isNotEmpty(after)) {
                                jobRecordDto.setActualCount(jobRecordDto.getActualCount() + 1);
                            }
                        } catch (Exception e) {
                            jsonObject.put(field, null);
                        }
                    } else if (jobField.getKeyAlgorithm().equals(SMEnums.SM4.toString())) {
                        try {
                            if (StringUtils.isEmpty(jobRecordDto.getBeforeText()) && StringUtils.isNotEmpty(before)) {
                                jobRecordDto.setBeforeText(before);
                            }
                            String after = StringUtils.isEmpty(before) ? "" : Base64.getEncoder().encodeToString(
                                    BcSm4Util.encrypt(SM4_ALGORITHM,
                                            Base64.getDecoder().decode(jobField.getSm4KeyBase64()),
                                            Base64.getDecoder().decode(jobField.getSm4IvBase64()),
                                            before.getBytes()));
                            if (StringUtils.isEmpty(jobRecordDto.getAfterText()) && StringUtils.isNotEmpty(after)) {
                                jobRecordDto.setAfterText(after);
                            }
                            jsonObject.put(field, after);
                            if (StringUtils.isNotEmpty(after)) {
                                jobRecordDto.setActualCount(jobRecordDto.getActualCount() + 1);
                            }
                        } catch (Exception e) {
                            jsonObject.put(field, null);
                        }
                    }
                    jobRecordDto.setShouldCount(jobRecordDto.getShouldCount() + 1);
                    recordMap.put(field, jobRecordDto);
                }
            }
            bulkRequest.add(new IndexRequest(targetIndex).source(jsonObject.toJSONString(), XContentType.JSON));
        }
        BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
        if (bulkResponse.hasFailures()) {
            // 处理批量操作中的失败
            for (BulkItemResponse item : bulkResponse) {
                if (item.isFailed()) {
                    BulkItemResponse.Failure failure = item.getFailure();
                    log.info("ES加密任务批量写入失败：" + failure.getId() + ": " + failure.getMessage());
                }
            }
        }
    }
}
