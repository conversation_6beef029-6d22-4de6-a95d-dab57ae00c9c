/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/11/6
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2023/11/6
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.dataencryption.task;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.util.HiveUtil;
import com.idss.datalake.common.util.MysqlUtil;
import com.idss.datalake.dataencryption.dto.JobRecordDto;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJob;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobField;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobRecord;
import com.idss.datalake.dataencryption.enums.SMEnums;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobRecordService;
import com.idss.datalake.dataencryption.service.IDataEncryptionAlgorithmJobService;
import com.idss.datalake.dataencryption.util.BcSm2Util;
import com.idss.datalake.dataencryption.util.BcSm3Util;
import com.idss.datalake.dataencryption.util.BcSm4Util;
import com.idss.datalake.dataencryption.util.KeyUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2023/11/6
 */
@Data
@Slf4j
public class HiveEncryptTask implements Runnable {
    private static final String SM4_ALGORITHM = "SM4/ECB/PKCS5PADDING";
    //****************************************
    private String url;
    private String userName;
    private String password;
    private String dbName;
    private DataEncryptionAlgorithmJob algorithmJob;
    private List<DataEncryptionAlgorithmJobField> fields;
    private IDataEncryptionAlgorithmJobService jobService;
    private IDataEncryptionAlgorithmJobRecordService recordService;

    @Override
    public void run() {
        Connection conn = HiveUtil.getConnect(url, userName, password, "", null, null, null);
        try {
            String srcTableName = algorithmJob.getEncryptionObject();
            String targetTableName = algorithmJob.getTargetObject();
            //读取原表建表语句
            String srcTableDDL = HiveUtil.queryTableDDL(conn, srcTableName);
            //读取所有字段
            List<String> srcFields = HiveUtil.queryTableField(conn, srcTableName);
            //准表建表脚本
            List<String> replaceFields = fields.stream().map(field -> field.getFieldName().split(",")).map(Arrays::asList).reduce((r1,
                                                                                                                                   r2) -> {
                r2.addAll(r1);
                return r2;
            }).get();
            String targetTableDDL = StrUtil.replace(srcTableDDL, srcTableName, targetTableName);
            targetTableDDL = replaceField(replaceFields, targetTableDDL);
            String[] rowFormats = targetTableDDL.split("ROW FORMAT");
            targetTableDDL = rowFormats[0].concat(" ROW FORMAT DELIMITED FIELDS TERMINATED BY ',' STORED AS TEXTFILE");
            createTable(conn, targetTableDDL);

            Map<String, JobRecordDto> recordMap = new HashMap<>();
            for (String replaceField : replaceFields) {
                recordMap.put(replaceField, new JobRecordDto());
            }

            //分页读取原表后写入目标表
            int pageNum = 1;
            while (true) {
                List<Map<String, String>> readData = readData(conn, srcTableName, srcFields, pageNum);
                if (CollectionUtils.isNotEmpty(readData)) {
                    writeData(conn, targetTableName, srcFields, readData, recordMap);
                } else {
                    break;
                }
                pageNum++;
            }

            //保存记录
            List<DataEncryptionAlgorithmJobRecord> records = new ArrayList<>();
            recordMap.forEach((field, recordDto) -> {
                DataEncryptionAlgorithmJobRecord record = new DataEncryptionAlgorithmJobRecord();
                record.setJobId(algorithmJob.getId());
                record.setFieldName(field);
                record.setBeforeText(recordDto.getBeforeText());
                record.setAfterText(recordDto.getAfterText());
                record.setShouldCount(recordDto.getShouldCount());
                record.setActualCount(recordDto.getActualCount());
                records.add(record);
            });
            jobService.update(new UpdateWrapper<DataEncryptionAlgorithmJob>().set("finish_time", LocalDateTime.now()).set("state", 2).eq(
                    "id", algorithmJob.getId()));
            recordService.saveBatch(records);
        } catch (Exception e) {
            log.error("HIVE加密任务失败", e);
            jobService.update(new UpdateWrapper<DataEncryptionAlgorithmJob>().set("result", e.getMessage()).set("finish_time",
                    LocalDateTime.now()).set("state", 2).eq("id", algorithmJob.getId()));
        } finally {
            try {
                if (conn != null) {
                    conn.close();
                }

            } catch (SQLException e) {
                log.error("hive加密任务关闭连接失败", e);
            }
        }
    }


    public void writeData(Connection connection, String targetTable, List<String> fields, List<Map<String, String>> data, Map<String,
            JobRecordDto> recordMap) throws Exception {
        StringBuilder sqlBuilder =
                new StringBuilder("insert into " + dbName + "." + targetTable + " (" + fields.stream().collect(Collectors.joining(",")) + " ) values (");
        sqlBuilder.append(getSpace(fields.size())).append(" )");

        PreparedStatement preparedStatement = null;
        for (Map<String, String> record : data) {
            preparedStatement = connection.prepareStatement(sqlBuilder.toString());
            for (int i = 0; i < fields.size(); i++) {
                String field = fields.get(i);
                DataEncryptionAlgorithmJobField algorithmJobField = null;
                for (DataEncryptionAlgorithmJobField jobField : this.fields) {
                    List<String> list = Arrays.asList(jobField.getFieldName().split(","));
                    if (list.contains(field)) {
                        algorithmJobField = jobField;
                        break;
                    }
                }
                if (algorithmJobField != null) {
                    JobRecordDto jobRecordDto = recordMap.get(field);
                    if (algorithmJobField.getKeyAlgorithm().equals(SMEnums.SM2.toString())) {
                        try {
                            String before = record.get(field);
                            if (StringUtils.isEmpty(jobRecordDto.getBeforeText()) && StringUtils.isNotEmpty(before)) {
                                jobRecordDto.setBeforeText(before);
                            }
                            String after = StringUtils.isEmpty(before) ? "" :
                                    Base64.getEncoder().encodeToString(BcSm2Util.encrypt(before.getBytes(),
                                            KeyUtils.createPublicKey(algorithmJobField.getSm2PublicKey())));
                            if (StringUtils.isEmpty(jobRecordDto.getAfterText()) && StringUtils.isNotEmpty(after)) {
                                jobRecordDto.setAfterText(after);
                            }
                            preparedStatement.setString(i + 1, after);
                            if (StringUtils.isNotEmpty(after)) {
                                jobRecordDto.setActualCount(jobRecordDto.getActualCount() + 1);
                            }
                        } catch (Exception e) {
                            preparedStatement.setString(i + 1, "");
                        }
                    } else if (algorithmJobField.getKeyAlgorithm().equals(SMEnums.SM3.toString())) {
                        try {
                            String before = record.get(field);
                            String after = StringUtils.isEmpty(before) ? "" : BcSm3Util.sm3Hex(before.getBytes());
                            if (StringUtils.isEmpty(jobRecordDto.getBeforeText()) && StringUtils.isNotEmpty(before)) {
                                jobRecordDto.setBeforeText(before);
                            }
                            if (StringUtils.isEmpty(jobRecordDto.getAfterText()) && StringUtils.isNotEmpty(after)) {
                                jobRecordDto.setAfterText(after);
                            }
                            if (StringUtils.isNotEmpty(after)) {
                                jobRecordDto.setActualCount(jobRecordDto.getActualCount() + 1);
                            }
                            preparedStatement.setString(i + 1, after);
                        } catch (Exception e) {
                            preparedStatement.setString(i + 1, "");
                        }
                    } else if (algorithmJobField.getKeyAlgorithm().equals(SMEnums.SM4.toString())) {
                        try {
                            String before = record.get(field);
                            String after = StringUtils.isEmpty(before) ? "" : Base64.getEncoder().encodeToString(
                                    BcSm4Util.encrypt(SM4_ALGORITHM,
                                            Base64.getDecoder().decode(algorithmJobField.getSm4KeyBase64()),
                                            Base64.getDecoder().decode(algorithmJobField.getSm4IvBase64()),
                                            before.getBytes()));
                            if (StringUtils.isEmpty(jobRecordDto.getBeforeText()) && StringUtils.isNotEmpty(before)) {
                                jobRecordDto.setBeforeText(before);
                            }
                            if (StringUtils.isEmpty(jobRecordDto.getAfterText()) && StringUtils.isNotEmpty(after)) {
                                jobRecordDto.setAfterText(after);
                            }
                            if (StringUtils.isNotEmpty(after)) {
                                jobRecordDto.setActualCount(jobRecordDto.getActualCount() + 1);
                            }
                            preparedStatement.setString(i + 1, after);
                        } catch (Exception e) {
                            preparedStatement.setString(i + 1, "");
                        }
                    }
                    jobRecordDto.setShouldCount(jobRecordDto.getShouldCount() + 1);
                    recordMap.put(field, jobRecordDto);
                } else {
                    preparedStatement.setString(i + 1, StringUtils.isEmpty(record.get(field)) ? "" : record.get(field));
                }
            }
            preparedStatement.executeUpdate();
        }
        //        // 执行批处理
        //        int[] rowsInserted = preparedStatement.executeBatch();
        //        log.info("插入数据:{}行", rowsInserted.length);
        if (preparedStatement != null) {
            preparedStatement.close();
        }
    }

    private String getSpace(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(" ?,");
        }
        sb.replace(sb.length() - 1, sb.length(), "");
        return sb.toString();
    }

    private List<Map<String, String>> readData(Connection connection, String srcTable, List<String> fields, long pageNumber) throws Exception {
        List<Map<String, String>> result = new ArrayList<>();
        Statement statement = connection.createStatement();
        long pageSize = 1000L; // 每页返回的行数
        long offset = (pageNumber - 1L) * pageSize;
        String query = "SELECT * FROM " + dbName + "." + srcTable + " LIMIT " + pageSize + " OFFSET " + offset;
        ResultSet resultSet = statement.executeQuery(query);

        while (resultSet.next()) {
            // 处理查询结果
            Map<String, String> data = new HashMap<>();
            for (String field : fields) {
                data.put(field, resultSet.getString(field));
            }
            result.add(data);
        }
        resultSet.close();
        statement.close();
        return result;
    }

    private String replaceField(List<String> replaceFields, String originalScript) {
        for (String replaceField : replaceFields) {
            String targetField = "`" + replaceField + "`";

            String targetType = "string";

            // 使用正则表达式匹配字段的定义
            String regex = "`" + Pattern.quote(targetField.substring(1, targetField.length() - 1)) + "`[^,)]+";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(originalScript);

            // 进行替换
            if (matcher.find()) {
                String matchedField = matcher.group();
                matchedField = matchedField + ")";
                originalScript = originalScript.replaceFirst(Pattern.quote(matchedField), targetField + " " + targetType);
            } else {
                throw new RuntimeException("未找到匹配的字段定义");
            }
        }
        return originalScript;
    }

    private void createTable(Connection connection, String ddl) throws Exception {
        Statement statement = connection.createStatement();
        statement.execute(ddl);
        statement.close();
    }
}
