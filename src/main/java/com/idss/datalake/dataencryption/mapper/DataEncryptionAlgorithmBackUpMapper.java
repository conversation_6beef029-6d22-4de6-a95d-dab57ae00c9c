package com.idss.datalake.dataencryption.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.dataencryption.dto.AlgorithmPageRequest;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithm;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmBackUp;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 数据加密算法备份 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public interface DataEncryptionAlgorithmBackUpMapper extends BaseMapper<DataEncryptionAlgorithmBackUp> {
    Page<DataEncryptionAlgorithmBackUp> queryPage(AlgorithmPageRequest requestDto);
}
