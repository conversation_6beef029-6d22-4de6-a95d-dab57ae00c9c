package com.idss.datalake.dataencryption.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.dataencryption.dto.AlgorithmPageRequest;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithm;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datamart.dto.request.QueryElementRequest;
import com.idss.datalake.datamart.dto.response.QueryElementVo;

/**
 * <p>
 * 数据加密算法 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public interface DataEncryptionAlgorithmMapper extends BaseMapper<DataEncryptionAlgorithm> {
    Page<DataEncryptionAlgorithm> queryPage(AlgorithmPageRequest requestDto);
}
