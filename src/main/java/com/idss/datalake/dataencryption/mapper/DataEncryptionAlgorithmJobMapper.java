package com.idss.datalake.dataencryption.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.dataencryption.dto.AlgorithmJobPageRequest;
import com.idss.datalake.dataencryption.dto.AlgorithmPageRequest;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithm;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJob;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 数据加密算法任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public interface DataEncryptionAlgorithmJobMapper extends BaseMapper<DataEncryptionAlgorithmJob> {
    Page<DataEncryptionAlgorithmJob> queryPage(AlgorithmJobPageRequest requestDto);
}
