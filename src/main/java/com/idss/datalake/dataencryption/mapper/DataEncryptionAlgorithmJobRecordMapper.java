package com.idss.datalake.dataencryption.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.dataencryption.dto.AlgorithmJobPageRequest;
import com.idss.datalake.dataencryption.dto.AlgorithmJobRecordPageRequest;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJob;
import com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 数据加密算法任务记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
public interface DataEncryptionAlgorithmJobRecordMapper extends BaseMapper<DataEncryptionAlgorithmJobRecord> {
    Page<DataEncryptionAlgorithmJobRecord> queryPage(AlgorithmJobRecordPageRequest requestDto);
}
