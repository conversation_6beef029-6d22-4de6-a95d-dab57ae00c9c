package com.idss.datalake.dataencryption.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据加密算法任务字段
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataEncryptionAlgorithmJobField implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 任务ID
     */
    private Integer jobId;

    /**
     * 字段名称，多个字段逗号分割
     */
    private String fieldName;

    /**
     * 算法ID
     */
    private Integer encryptionAlgorithmId;

    /**
     * 加密算法:SM2,SM3,SM4
     */
    @TableField(exist = false)
    private String keyAlgorithm;
    /**
     * 公钥
     */
    @TableField(exist = false)
    private String sm2PublicKey;

    /**
     * 私钥
     */
    @TableField(exist = false)
    private String sm2PrivateKey;
    @TableField(exist = false)
    private String sm4KeyBase64;
    @TableField(exist = false)
    private String sm4IvBase64;
}
