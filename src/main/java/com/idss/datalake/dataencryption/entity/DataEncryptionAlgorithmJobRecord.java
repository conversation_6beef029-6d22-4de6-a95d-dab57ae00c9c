package com.idss.datalake.dataencryption.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据加密算法任务记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataEncryptionAlgorithmJobRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 任务ID
     */
    private Integer jobId;

    /**
     * 加密字段
     */
    private String fieldName;

    /**
     * 加密密钥
     */
    private String keyName;

    /**
     * 加密前样本
     */
    private String beforeText;

    /**
     * 加密后样本
     */
    private String afterText;

    /**
     * 加密状态
     */
    private String state;

    /**
     * 应加密字段数
     */
    private Long shouldCount;

    /**
     * 实际加密字段数
     */
    private Long actualCount;

    /**
     * 加密成功率
     */
    private String successRate;


}
