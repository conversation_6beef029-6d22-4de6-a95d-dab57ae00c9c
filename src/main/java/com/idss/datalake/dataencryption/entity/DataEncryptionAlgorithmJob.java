package com.idss.datalake.dataencryption.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据加密算法任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataEncryptionAlgorithmJob implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 任务名称
     */
    private String jobName;

    @TableField(exist = false)
    private String keyName;

    /**
     * 数据源类型：Clickhouse,Elasticsearch,Hive,Mysql
     */
    private String datasourceType;

    /**
     * 加密对象
     */
    private String encryptionObject;

    /**
     * 加密类型
     */
    private String encryptionType;

    /**
     * 目标对象
     */
    private String targetObject;

    private String remark;

    /**
     * 加密状态：1，执行中，2执行完成
     */
    private String state;

    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private Integer tenantId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;

    @TableField(exist = false)
    private Long useSecond;

    private String result;
}
