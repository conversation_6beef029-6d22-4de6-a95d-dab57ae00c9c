package com.idss.datalake.dataencryption.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据加密算法备份
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataEncryptionAlgorithmBackUp implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 密钥ID
     */
    private Long encryptionAlgorithmId;

    /**
     * 密钥名称
     */
    private String keyName;

    /**
     * 加密算法:SM2,SM3,SM4
     */
    private String keyAlgorithm;

    /**
     * 分级ID
     */
    private Long levelId;

    /**
     * 分级
     */
    private String levelName;

    /**
     * 分类ID
     */
    private Long typeId;

    /**
     * 分级
     */
    private String typeName;

    private String remark;

    /**
     * 公钥
     */
    private String sm2PublicKey;

    /**
     * 私钥
     */
    private String sm2PrivateKey;
    private String sm4KeyBase64;
    private String sm4IvBase64;

    private String backUpUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime backUpTime;

    private Integer tenantId;


}
