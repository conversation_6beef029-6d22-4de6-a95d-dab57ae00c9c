package com.idss.datalake.dataencryption.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据加密算法
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataEncryptionAlgorithm implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 密钥名称
     */
    private String keyName;

    /**
     * 加密算法:SM2,SM3,SM4
     */
    private String keyAlgorithm;

    /**
     * 分级ID
     */
    private Long levelId;

    @TableField(exist = false)
    private String levelName;

    /**
     * 分类ID
     */
    private Long typeId;
    @TableField(exist = false)
    private String typeName;

    private String remark;

    /**
     * 公钥
     */
    private String sm2PublicKey;

    /**
     * 私钥
     */
    private String sm2PrivateKey;
    private String sm4KeyBase64;
    private String sm4IvBase64;


    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private Integer tenantId;

    /**
     * 是否启用，0停止，1启用
     */
    private Integer isEnable;


}
