/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/3/26
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/3/26
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.dashboard.manager;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.util.ClickhouseUtil;
import com.idss.datalake.common.util.ElasticSearchUtil;
import com.idss.datalake.common.util.HiveUtil;
import com.idss.datalake.common.util.MysqlUtil;
import com.idss.datalake.dashboard.cache.DashboardCache;
import com.idss.datalake.dashboard.dto.ApiInvokeDto;
import com.idss.datalake.dashboard.dto.DatamartTypeDto;
import com.idss.datalake.dashboard.dto.EtlLogmoudleDto;
import com.idss.datalake.dashboard.dto.EtlResourcePoolCount;
import com.idss.datalake.dashboard.dto.InOutDataCountTrend;
import com.idss.datalake.dashboard.dto.StandardJob;
import com.idss.datalake.dashboard.dto.StandardJobVo;
import com.idss.datalake.dashboard.dto.SubjectAreaDto;
import com.idss.datalake.dashboard.dto.SubjectAreaVo;
import com.idss.datalake.dashboard.dto.SubjectDto;
import com.idss.datalake.dashboard.dto.SubjectVo;
import com.idss.datalake.dashboard.dto.TenantEtlInDatabaseCount;
import com.idss.datalake.dashboard.mapper.DashboardMapper;
import com.idss.datalake.datagovern.metadata.model.detail.service.NodeService;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.element.utils.BtoaEncode;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.datashare.tenant.service.ITbTenantService;
import com.idss.radar.datasource.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.sql.Connection;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2024/3/26
 */
@Component
@Slf4j
public class DashboardManager {
    @Resource
    private DashboardMapper dashboardMapper;
    @Autowired
    private ITbTenantService tenantService;
    @Autowired
    private QuaWabElementService quaWabElementService;
    @Autowired
    private DashboardCache dashboardCache;

    /**
     * 数据采集概览
     *
     * @return
     */
    public Map<String, Long> etlOverview() {
        Map<String, Long> data = new HashMap<>();
        //总采集量
        Long totalCollection = 0L;
        //数据源总数
        Long totalDatasource = 0L;
        //数据库类型
        Long totalDatabase = 0L;
        //协议类型
        Long totalProtocol = 0L;
        //文件类型
        Long totalFile = 0L;
        //脚本类型
        Long totalScript = 0L;
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<TbTenant> tenantList = tenantService.list(new QueryWrapper<TbTenant>().eq("ACCOUNT_TYPE", 1).eq("RESOURCE_STATUS", 2).eq("DEL_FLAG", 0));
        for (TbTenant tbTenant : tenantList) {
            DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE, Integer.parseInt(tbTenant.getTenantId() + ""));
            totalCollection += dashboardMapper.totalCollection();
        }
        for (TbTenant tbTenant : tenantList) {
            DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, Integer.parseInt(tbTenant.getTenantId() + ""));
            totalDatasource += dashboardMapper.datasourceTotal();
            totalDatabase += dashboardMapper.datasourceTypeTotal("数据库");
            totalProtocol += dashboardMapper.datasourceTypeTotal("文件上传");
            totalFile += dashboardMapper.datasourceTypeTotal("协议加载");
        }
        data.put("totalCollection", totalCollection);
        data.put("totalDatabase", totalDatabase);
        data.put("totalDatasource", totalDatasource);
        data.put("totalProtocol", totalProtocol);
        data.put("totalFile", totalFile);
        data.put("totalScript", totalScript);
        return data;
    }

    /**
     * 用户采集入库量统计
     *
     * @return
     */
    public List<TenantEtlInDatabaseCount> etlInDatabaseCount() {
        List<TenantEtlInDatabaseCount> data = new ArrayList<>();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<TbTenant> tenantList = tenantService.list(new QueryWrapper<TbTenant>().eq("ACCOUNT_TYPE", 1).eq("RESOURCE_STATUS", 2).eq("DEL_FLAG", 0));
        for (TbTenant tbTenant : tenantList) {
            DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE, Integer.parseInt(tbTenant.getTenantId() + ""));
            List<Map<String, Object>> maps = dashboardMapper.tenantCollection();
            TenantEtlInDatabaseCount count = new TenantEtlInDatabaseCount();
            count.setTenantName(tbTenant.getTenantName());
            List<Map<String, Object>> chCount = maps.stream().filter(entry -> "CH".equals(entry.get("writerType").toString())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(chCount)) {
                count.setChCount(Long.parseLong(chCount.get(0).get("cnt").toString()));
            } else {
                count.setChCount(0L);
            }
            List<Map<String, Object>> esCount = maps.stream().filter(entry -> "ES".equals(entry.get("writerType").toString())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(esCount)) {
                count.setEsCount(Long.parseLong(esCount.get(0).get("cnt").toString()));
            } else {
                count.setEsCount(0L);
            }
            List<Map<String, Object>> kafkaCount = maps.stream().filter(entry -> "Kafka".equals(entry.get("writerType").toString())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(kafkaCount)) {
                count.setKafkaCount(Long.parseLong(kafkaCount.get(0).get("cnt").toString()));
            } else {
                count.setKafkaCount(0L);
            }
            count.setTotalCount(count.getChCount() + count.getEsCount() + count.getKafkaCount());
            data.add(count);
        }
        return data;
    }

    /**
     * 资源池采集入库量统计
     *
     * @return
     */
    public List<Map<String, String>> etlResourcePoolCount() {
        List<EtlResourcePoolCount> data = new ArrayList<>();
        Map<String, EtlResourcePoolCount> dataMap = new HashMap<>();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<TbTenant> tenantList = tenantService.list(new QueryWrapper<TbTenant>().eq("ACCOUNT_TYPE", 1).eq("RESOURCE_STATUS", 2).eq("DEL_FLAG", 0));
        for (TbTenant tbTenant : tenantList) {
            DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, Integer.parseInt(tbTenant.getTenantId() + ""));
            List<EtlLogmoudleDto> logmoudleDtos = dashboardMapper.etlLogmoudle();
            for (EtlLogmoudleDto logmoudleDto : logmoudleDtos) {
                EtlResourcePoolCount count;
                DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, Integer.parseInt(tbTenant.getTenantId() + ""));
                List<Long> etlSourceIds = dashboardMapper.etlSourceIds(logmoudleDto.getId());
                if (dataMap.containsKey(logmoudleDto.getWorkIp())) {
                    count = dataMap.get(logmoudleDto.getWorkIp());
                    DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE, Integer.parseInt(tbTenant.getTenantId() + ""));
                    count.setOutCount(count.getOutCount() + (CollectionUtils.isEmpty(etlSourceIds) ? 0 : dashboardMapper.etlOutDataCount(etlSourceIds)));
                    count.setInCount(count.getInCount() + (CollectionUtils.isEmpty(etlSourceIds) ? 0 : dashboardMapper.etlInDataCount(etlSourceIds)));
                    dataMap.put(count.getWorkIp(), count);
                } else {
                    count = new EtlResourcePoolCount();
                    DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE, Integer.parseInt(tbTenant.getTenantId() + ""));
                    count.setResourcePool(logmoudleDto.getResourcePool());
                    count.setWorkIp(logmoudleDto.getWorkIp());
                    count.setOutCount(CollectionUtils.isEmpty(etlSourceIds) ? 0 : dashboardMapper.etlOutDataCount(etlSourceIds));
                    count.setInCount(CollectionUtils.isEmpty(etlSourceIds) ? 0 : dashboardMapper.etlInDataCount(etlSourceIds));
                    dataMap.put(count.getWorkIp(), count);
                }
            }
        }
        dataMap.forEach((k, v) -> {
            data.add(v);
        });

        List<Map<String, String>> transData = new ArrayList<>();
        for (EtlResourcePoolCount datum : data) {
            Map<String, String> temp1 = new HashMap<>();
            temp1.put("x", datum.getResourcePool());
            temp1.put("y", datum.getInCount().toString());
            temp1.put("s", "数据采集量");

            Map<String, String> temp2 = new HashMap<>();
            temp2.put("x", datum.getResourcePool());
            temp2.put("y", datum.getOutCount().toString());
            temp2.put("s", "数据入库量");

            transData.add(temp1);
            transData.add(temp2);
        }
        return transData;
    }

    /**
     * 用户数统计
     *
     * @return
     */
    public Map<String, Integer> userCount() {
        Map<String, Integer> data = new HashMap<>();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        int userCount = dashboardMapper.tenantUserCount();
        int datasourceCount = dashboardMapper.datasourceCount();
        int resourcePoolCount = 0;

        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<TbTenant> tenantList = tenantService.list(new QueryWrapper<TbTenant>().eq("ACCOUNT_TYPE", 1).eq("RESOURCE_STATUS", 2).eq("DEL_FLAG", 0));
        for (TbTenant tbTenant : tenantList) {
            DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, Integer.parseInt(tbTenant.getTenantId() + ""));
            resourcePoolCount += dashboardMapper.resourcePoolCount();
        }
        data.put("userCount", userCount);
        data.put("datasourceCount", datasourceCount);
        data.put("resourcePoolCount", resourcePoolCount);
        return data;
    }

    public JSONObject tenantCount(Integer tenantId) {
        JSONObject data = new JSONObject();
        SubjectDto subjectDto = dashboardCache.getCache(tenantId);
        data.put("subjectAreaTypeCount",subjectDto.getSubjectAreaTypeCount());
        data.put("subjectAreaFieldCount",subjectDto.getSubjectAreaFieldCount());
        return data;
    }

    /**
     * 主题域统计-大类型
     *
     * @return
     */
    public JSONArray tenantBigCount(Integer tenantId) {
        SubjectDto subjectDto = dashboardCache.getCache(tenantId);
        List<SubjectAreaVo> areaVoList = subjectDto.getAreaVoList();
        JSONArray array = new JSONArray();
        long total = areaVoList.stream().mapToLong(SubjectAreaVo::getCategoryCount).sum();
        JSONObject all = new JSONObject();
        all.put("s","全部");
        all.put("y",total+"");
        all.put("orderId",-1);
        array.add(all);
        for (int i = 0; i < areaVoList.size(); i++) {
            JSONObject json = new JSONObject();
            json.put("s",areaVoList.get(i).getCategoryName());
            json.put("y",areaVoList.get(i).getCategoryCount().toString());
            json.put("orderId",i);
            array.add(json);
        }
        return array;
    }

    /**
     * 数据分层
     * @param tenantId
     * @return
     */
    public JSONArray subjectVo(Integer tenantId) {
        JSONArray data = new JSONArray();
        DecimalFormat df = new DecimalFormat("0.00%");
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        Long dictId = dashboardMapper.subjectAreaId(tenantId);
        List<SubjectVo> subjectVos = dashboardMapper.subjectVo(dictId);
        int total = subjectVos.stream().mapToInt(SubjectVo::getY).sum();
        for (SubjectVo vo : subjectVos) {
            BigDecimal a = new BigDecimal(vo.getY());
            BigDecimal b = new BigDecimal(total);
            BigDecimal decimal = a.divide(b, 2, RoundingMode.HALF_UP);
            vo.setDecimal(total == 0 ? "0.00%" : df.format(decimal));
            data.add(vo);
        }
        return data;
    }

    /**
     * 主题域统计-小类型
     *
     * @return
     */
    public JSONArray tenantSmallCount(Map<String, Object> param) {
        Integer tenantId = Integer.parseInt(param.get("tenantId").toString());
        String categoryName = param.get("categoryName").toString();
        JSONArray data = new JSONArray();
        SubjectDto subjectDto = dashboardCache.getCache(tenantId);
        List<SubjectAreaVo> areaVoList = subjectDto.getAreaVoList();
        if (StringUtils.isNotEmpty(categoryName)) {
            if ("全部".equals(categoryName)) {
                for (int i = 0; i < areaVoList.size(); i++) {
                    SubjectAreaVo subjectAreaVo = areaVoList.get(i);
                    subjectAreaVo.getSubject().forEach((k, v) -> {
                        JSONObject json = new JSONObject();
                        json.put("categoryName", subjectAreaVo.getCategoryName());
                        json.put("childName", k);
                        json.put("value", v);
                        data.add(json);
                    });
                }
            } else {
                for (int i = 0; i < areaVoList.size(); i++) {
                    if (categoryName.equals(areaVoList.get(i).getCategoryName())) {
                        SubjectAreaVo subjectAreaVo = areaVoList.get(i);
                        subjectAreaVo.getSubject().forEach((k, v) -> {
                            JSONObject json = new JSONObject();
                            json.put("categoryName", subjectAreaVo.getCategoryName());
                            json.put("childName", k);
                            json.put("value", v);
                            data.add(json);
                        });
                    }
                }
            }
        } else {
            for (int i = 0; i < areaVoList.size(); i++) {
                SubjectAreaVo subjectAreaVo = areaVoList.get(i);
                subjectAreaVo.getSubject().forEach((k, v) -> {
                    JSONObject json = new JSONObject();
                    json.put("categoryName", subjectAreaVo.getCategoryName());
                    json.put("childName", k);
                    json.put("value", v);
                    data.add(json);
                });
            }
        }
        return data;
    }


    /**
     * 采集入库趋势
     *
     * @return
     */
    public JSONArray inOutDataCountTrend() {
        JSONArray result = new JSONArray();
        List<InOutDataCountTrend> data = new ArrayList<>();
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 创建日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 存储最近 7 天的日期列表
        List<String> last7Days = new ArrayList<>();
        // 循环获取最近 7 天的日期并添加到列表中
        for (int i = 0; i < 7; i++) {
            // 格式化日期为字符串并添加到列表中
            last7Days.add(currentDate.minusDays(i).format(formatter));
        }
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<TbTenant> tenantList = tenantService.list(new QueryWrapper<TbTenant>().eq("ACCOUNT_TYPE", 1).eq("RESOURCE_STATUS", 2).eq("DEL_FLAG", 0));
        Long inCount = 0L;
        Long outCount = 0L;
        for (String day : last7Days) {
            for (TbTenant tbTenant : tenantList) {
                DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.CLICKHOUSE, Integer.parseInt(tbTenant.getTenantId() + ""));
                inCount += dashboardMapper.inDataSum(day);
                outCount += dashboardMapper.outDataSum(day);
            }
            InOutDataCountTrend trend = new InOutDataCountTrend();
            trend.setDate(day);
            trend.setInCount(inCount);
            trend.setOutCount(outCount);
            data.add(trend);
            inCount = 0L;
            outCount = 0L;
        }

        for (InOutDataCountTrend datum : data) {
            JSONObject json = new JSONObject();
            json.put("x",datum.getDate());
            json.put("y",datum.getInCount());
            json.put("s","数据采集量");

            JSONObject json1 = new JSONObject();
            json1.put("x",datum.getDate());
            json1.put("y",datum.getOutCount());
            json1.put("s","数据入库量");

            result.add(json);
            result.add(json1);
        }

        return result;
    }

    /**
     * 数据治理-数据治理概览
     *
     * @return
     */
    public Map<String, Integer> dataMetaCount() {
        Map<String, Integer> data = new HashMap<>();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        int chCount = quaWabElementService.count(new QueryWrapper<QuaWabElement>().eq("element_type", "CH").eq("flag", 1));
        data.put("Clickhouse", chCount);
        int esCount = quaWabElementService.count(new QueryWrapper<QuaWabElement>().eq("element_type", "ES").eq("flag", 1));
        data.put("Elasticsearch", esCount);
        int mysqlCount = quaWabElementService.count(new QueryWrapper<QuaWabElement>().eq("element_type", "MYSQL").eq("flag", 1));
        data.put("Mysql", mysqlCount);
        int hiveCount = quaWabElementService.count(new QueryWrapper<QuaWabElement>().eq("element_type", "HIVE").eq("flag", 1));
        data.put("Hive", hiveCount);
        data.put("element", chCount + esCount + mysqlCount + hiveCount);

        data.put("dataStandard", dashboardMapper.dataStandardCount());
        data.put("ruleType", dashboardMapper.ruleTypeCount());
        data.put("ruleJob", dashboardMapper.ruleJobCount());
        return data;
    }

    /**
     * 数据治理-质量任务排行 TOP20
     *
     * @return
     */
    public List<StandardJobVo> standardJob() {
        List<StandardJobVo> data = new ArrayList<>();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<StandardJob> standardJobs = dashboardMapper.standardJob();
        for (StandardJob standardJob : standardJobs) {
            StandardJobVo vo = new StandardJobVo();
            vo.setName(standardJob.getName());
            List<Long> ids = Arrays.stream(standardJob.getJobRules().split(",")).map(Long::parseLong).collect(Collectors.toList());
            vo.setRuleName(String.join(",", dashboardMapper.ruleNames(ids)));
            vo.setMonitorScore(dashboardMapper.maxMonitorScore(standardJob.getId()));
            data.add(vo);
        }
        List<StandardJobVo> collect = data.stream().sorted(Comparator.comparing(StandardJobVo::getMonitorScore).reversed()).collect(Collectors.toList());
        return collect.subList(0, Math.min(collect.size(), 20));
    }

    /**
     * 数据治理-数据质量等级分布
     *
     * @return
     */
    public List<JSONObject> standardJobLevel() {
        List<JSONObject> data = new ArrayList<>();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<String> standardLevel = dashboardMapper.standardLevel();
        int important = 0;
        int severity = 0;
        int general = 0;
        for (String level : standardLevel) {
            JSONObject jsonObject = JSONObject.parseObject(level);
            important += jsonObject.getInteger("important");
            severity += jsonObject.getInteger("severity");
            general += jsonObject.getInteger("general");
        }
        int total = important + severity + general;

        JSONObject importantJson = new JSONObject();
        importantJson.put("name", "重要");
        importantJson.put("value", important);
        importantJson.put("percent", total == 0 ? "0.00%" :
                new BigDecimal(important).divide(new BigDecimal(total), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");

        JSONObject severityJson = new JSONObject();
        severityJson.put("name", "严重");
        severityJson.put("value", severity);
        severityJson.put("percent", total == 0 ? "0.00%" :
                new BigDecimal(severity).divide(new BigDecimal(total), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");

        JSONObject generalJson = new JSONObject();
        generalJson.put("name", "一般");
        generalJson.put("value", general);
        generalJson.put("percent", total == 0 ? "0.00%" :
                new BigDecimal(general).divide(new BigDecimal(total), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");

        data.add(importantJson);
        data.add(severityJson);
        data.add(generalJson);
        return data;
    }

    /**
     * 数据共享
     *
     * @return
     */
    public Map<String, Object> dataShare() {
        Map<String, Object> data = new HashMap<>();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        int apiCount = dashboardMapper.apiCount();
        int apiApplyCount = dashboardMapper.apiApplyCount();
        int apiApplySuccessCount = dashboardMapper.apiApplySuccessCount();
        int apiInvokeCount = dashboardMapper.apiInvokeCount();
        data.put("apiCount", apiCount);
        data.put("apiApplyCount", apiApplyCount);
        data.put("apiApplySuccessCount", apiApplySuccessCount);
        data.put("apiInvokeCount", apiInvokeCount);
        return data;
    }

    public JSONArray dataShareLog() {
        JSONArray data = new JSONArray();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<ApiInvokeDto> apiInvokeDtos = dashboardMapper.apiInvoke();
        apiInvokeDtos.stream().collect(Collectors.groupingBy(ApiInvokeDto::getApiId)).forEach((k, v) -> {
            JSONObject itemSuccess = new JSONObject();
            itemSuccess.put("x", v.get(0).getName());
            List<ApiInvokeDto> successLog = v.stream().filter(i -> "true".equals(i.getInvokeFlag())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(successLog)) {
                itemSuccess.put("y", successLog.get(0).getCnt());
            } else {
                itemSuccess.put("y", 0);
            }
            itemSuccess.put("s","成功");
            data.add(itemSuccess);

            List<ApiInvokeDto> failLog = v.stream().filter(i -> "false".equals(i.getInvokeFlag())).collect(Collectors.toList());
            JSONObject itemFail = new JSONObject();
            itemFail.put("x", v.get(0).getName());
            if (CollectionUtils.isNotEmpty(failLog)) {
                itemFail.put("y", successLog.get(0).getCnt());
            } else {
                itemFail.put("y", 0);
            }
            itemFail.put("s","失败");
            data.add(itemSuccess);
        });
        return data;
    }

    /**
     * 用户使用 API
     *
     * @return
     */
    public List<JSONObject> userApi() {
        List<JSONObject> data = new ArrayList<>();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<String> applyUser = dashboardMapper.apiUser("apply_user", "api_apply");
        List<String> auditUser = dashboardMapper.apiUser("audit_user", "api_apply");
        List<String> invokeUser = dashboardMapper.apiUser("create_by", "api_invoke_log");

        Set<String> users = new HashSet<>();
        users.addAll(applyUser);
        users.addAll(auditUser);
        users.addAll(invokeUser);

        for (String user : users) {
            JSONObject item = new JSONObject();
            item.put("user", user);
            item.put("applyCount", dashboardMapper.apiMulCount("apply_user", "api_apply", user));
            item.put("auditCount", dashboardMapper.apiMulCount("audit_user", "api_apply", user));
            item.put("invokeCount", dashboardMapper.apiMulCount("create_by", "api_invoke_log", user));
            data.add(item);
        }
        return data;
    }

    /**
     * 数据集市
     *
     * @return
     */
    public JSONObject datamart() {
        JSONObject data = new JSONObject();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        int datamartCount = dashboardMapper.datamartCount();
        int datamartReleaseCount = dashboardMapper.datamartReleaseCount();
        int datamartSubscribeCount = dashboardMapper.datamartSubscribeCount();
        data.put("datamartCount", datamartCount);
        data.put("datamartReleaseCount", datamartReleaseCount);
        data.put("datamartSubscribeCount", datamartSubscribeCount);
        return data;
    }

    public JSONArray datamartLog() {
        JSONArray data = new JSONArray();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<Map<String, Object>> datamartSubscribeTop10 = dashboardMapper.datamartSubscribeTop10();
        data.addAll(datamartSubscribeTop10);
        return data;
    }

    /**
     * 数据集市类型订阅占比
     *
     * @return
     */
    public List<JSONObject> datamartType() {
        List<JSONObject> data = new ArrayList<>();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<DatamartTypeDto> datamartTypeDtos = dashboardMapper.datamartTypeCount();
        int total = 0;
        if (CollectionUtils.isNotEmpty(datamartTypeDtos)) {
            total = datamartTypeDtos.stream().mapToInt(DatamartTypeDto::getCnt).sum();
            for (DatamartTypeDto dto : datamartTypeDtos) {
                JSONObject item = new JSONObject();
                item.put("typeName", dto.getTypeName());
                item.put("cnt", dto.getCnt());
                item.put("percent", total == 0 ? "0.00%" : new BigDecimal(dto.getCnt()).divide(new BigDecimal(total), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
                data.add(item);
            }
        }
        return data;
    }

    /**
     * 数据字典
     *
     * @return
     */
    public JSONObject dict() {
        JSONObject data = new JSONObject();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        Long chDb = dashboardMapper.dictCount("qua_web_ch_element_detail_db");
        Long chTable = dashboardMapper.dictCount("qua_web_ch_element_detail_table");
        Long chField = dashboardMapper.dictCount("qua_web_ch_element_detail_column");

        Long mysqlDb = dashboardMapper.dictCount("qua_web_mysql_element_detail_db");
        Long mysqlTable = dashboardMapper.dictCount("qua_web_mysql_element_detail_table");
        Long mysqlField = dashboardMapper.dictCount("qua_web_mysql_element_detail_column");

        Long hiveDb = dashboardMapper.dictCount("qua_web_hive_element_detail_db");
        Long hiveTable = dashboardMapper.dictCount("qua_web_hive_element_detail_table");
        Long hiveField = dashboardMapper.dictCount("qua_web_hive_element_detail_column");

        Long esIndex = dashboardMapper.dictCount("qua_web_es_element_detail_index");
        Long esField = dashboardMapper.dictCount("qua_web_es_element_detail_field");

        Long totalDb = chDb + mysqlDb + hiveDb;
        Long totalTable = chTable + mysqlTable + hiveTable;
        Long totalField = chField + mysqlField + hiveField;

        data.put("chDb", chDb);
        data.put("chTable", chTable);
        data.put("chField", chField);

        data.put("mysqlDb", mysqlDb);
        data.put("mysqlTable", mysqlTable);
        data.put("mysqlField", mysqlField);

        data.put("hiveDb", hiveDb);
        data.put("hiveTable", hiveTable);
        data.put("hiveField", hiveField);

        data.put("esIndex", esIndex);
        data.put("esField", esField);

        data.put("totalDb", totalDb);
        data.put("totalTable", totalTable);
        data.put("totalField", totalField);
        return data;
    }
}
