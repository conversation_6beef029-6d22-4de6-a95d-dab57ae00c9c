/************************ CHAN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/3/27
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/3/27
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.dashboard.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2024/3/27
 */
@Data
public class SubjectAreaDto {
    private String datasourceType;
    private String itemName;
    private String databaseName;
    private Long elementId;
    private Long categoryId;
    private String categoryName;
}
