/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/3/26
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/3/26
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.dashboard.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2024/3/26
 */
@Data
public class TenantEtlInDatabaseCount {
    private String tenantName;
    private Long totalCount;
    private Long chCount;
    private Long esCount;
    private Long kafkaCount;
}
