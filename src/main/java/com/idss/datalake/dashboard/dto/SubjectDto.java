/************************ CHAN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/3/28
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/3/28
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.dashboard.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2024/3/28
 */
@Data
public class SubjectDto {
    private int subjectAreaTypeCount;
    private int subjectAreaFieldCount;
    private List<SubjectAreaVo> areaVoList;
}
