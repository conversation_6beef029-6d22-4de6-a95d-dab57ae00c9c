/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/3/26
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/3/26
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.dashboard.cache;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.util.ClickhouseUtil;
import com.idss.datalake.common.util.ElasticSearchUtil;
import com.idss.datalake.common.util.HiveUtil;
import com.idss.datalake.common.util.MysqlUtil;
import com.idss.datalake.dashboard.dto.SubjectAreaDto;
import com.idss.datalake.dashboard.dto.SubjectAreaVo;
import com.idss.datalake.dashboard.dto.SubjectDto;
import com.idss.datalake.dashboard.mapper.DashboardMapper;
import com.idss.datalake.datagovern.metadata.model.element.entity.QuaWabElement;
import com.idss.datalake.datagovern.metadata.model.element.service.QuaWabElementService;
import com.idss.datalake.datagovern.metadata.model.element.utils.BtoaEncode;
import com.idss.radar.datasource.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.thirdparty.org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URL;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2024/3/26
 */
@Component
@Slf4j
public class DashboardCache {
    @Resource
    private DashboardMapper dashboardMapper;
    @Autowired
    private QuaWabElementService quaWabElementService;

    // 缓存一小时
    private static final TimedCache<String, SubjectDto> timedCache = CacheUtil.newTimedCache(3600 * 1000);
    private static final String CACHE_KEY = "SUBJECT_KEY";

    public SubjectDto getCache(Integer tenantId) {
        if (timedCache.containsKey(CACHE_KEY)) {
            return timedCache.get(CACHE_KEY);
        } else {
            SubjectDto data = new SubjectDto();
            DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
            Long areaId = dashboardMapper.subjectAreaId(tenantId);
            //主题域类型个数
            int subjectAreaTypeCount = dashboardMapper.subjectAreaTypeCount(areaId);
            //主题域字段个数
            int subjectAreaFieldCount = dashboardMapper.subjectAreaFieldCount(areaId);
            data.setSubjectAreaTypeCount(subjectAreaTypeCount);
            data.setSubjectAreaFieldCount(subjectAreaFieldCount);

            //主题域明细
            List<SubjectAreaVo> areaVoList = new ArrayList<>();
            List<SubjectAreaDto> subjectAreas = dashboardMapper.subjectAreaDto(areaId);
            Map<Long, List<SubjectAreaDto>> collect = subjectAreas.stream().collect(Collectors.groupingBy(SubjectAreaDto::getCategoryId));
            collect.forEach((k, v) -> {
                SubjectAreaVo vo = new SubjectAreaVo();
                vo.setCategoryName(v.get(0).getCategoryName());
                Map<String, Long> subject = new HashMap<>();
                vo.setSubject(subject);
                for (SubjectAreaDto dto : v) {
                    QuaWabElement element = quaWabElementService.getById(dto.getElementId());
                    if ("CH".equals(element.getElementType())) {
                        String chDescribe = dashboardMapper.chDescribe(dto.getElementId(), dto.getDatabaseName(), dto.getItemName());
                        Connection connect =
                                ClickhouseUtil.getConnect("jdbc:clickhouse://" + element.getChIp() + ":" + element.getChPort() + "/" + dto.getDatabaseName(), element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()));
                        Long count = 0L;
                        try {
                            count = ClickhouseUtil.countTableLine(connect, dto.getItemName());
                            ClickhouseUtil.close(connect);
                        } catch (Exception e) {
                            log.error("统计表行数错误", e);
                        }
                        if (StringUtils.isEmpty(chDescribe)) {
                            if (subject.containsKey("其他")) {
                                subject.put("其他", subject.get("其他") + count);
                            } else {
                                subject.put("其他", count);
                            }
                        } else {
                            if (subject.containsKey(chDescribe)) {
                                subject.put(chDescribe, subject.get(chDescribe) + count);
                            } else {
                                subject.put(chDescribe, count);
                            }
                        }

                    } else if ("MYSQL".equals(element.getElementType())) {
                        String mysqlDescribe = dashboardMapper.mysqlDescribe(dto.getElementId(), dto.getDatabaseName(), dto.getItemName());
                        Connection connect = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_MYSQL,
                                "jdbc:mysql://" + element.getChIp() + ":" + element.getChPort() + "/" + dto.getDatabaseName(),
                                element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()));
                        Long count = 0L;
                        try {
                            count = MysqlUtil.countTableLine(connect, dto.getItemName());
                            MysqlUtil.close(connect);
                        } catch (Exception e) {
                            log.error("统计表行数错误", e);
                        }
                        if (StringUtils.isEmpty(mysqlDescribe)) {
                            if (subject.containsKey("其他")) {
                                subject.put("其他", subject.get("其他") + count);
                            } else {
                                subject.put("其他", count);
                            }
                        } else {
                            if (subject.containsKey(mysqlDescribe)) {
                                subject.put(mysqlDescribe, subject.get(mysqlDescribe) + count);
                            } else {
                                subject.put(mysqlDescribe, count);
                            }
                        }
                    } else if ("ES".equals(element.getElementType())) {
                        String esDescribe = dashboardMapper.esDescribe(dto.getElementId(), dto.getItemName());

                        Long count = 0L;
                        try {
                            URL url = new URL(element.getEsIpPort());
                            String host = url.getHost();
                            int port = url.getPort(); // 返回端口号，如果未指定则返回 -1
                            count = ElasticSearchUtil.indexLines(
                                    host,
                                    port,
                                    StringUtils.isEmpty(element.getEsUserName()) ? null : element.getEsUserName(),
                                    StringUtils.isEmpty(element.getEsUserPassword()) ? null :
                                            BtoaEncode.decrypt(element.getEsUserPassword()),
                                    dto.getItemName());
                        } catch (Exception e) {
                            log.error("统计表行数错误", e);
                        }
                        if (StringUtils.isEmpty(esDescribe)) {
                            if (subject.containsKey("其他")) {
                                subject.put("其他", subject.get("其他") + count);
                            } else {
                                subject.put("其他", count);
                            }
                        } else {
                            if (subject.containsKey(esDescribe)) {
                                subject.put(esDescribe, subject.get(esDescribe) + count);
                            } else {
                                subject.put(esDescribe, count);
                            }
                        }

                    } else if ("HIVE".equals(element.getElementType())) {
                        String hiveDescribe = dashboardMapper.hiveDescribe(dto.getElementId(), dto.getDatabaseName(), dto.getItemName());
                        Connection connect =
                                HiveUtil.getConnect("jdbc:hive2://" + element.getChIp() + ":" + element.getChPort() + "/" + dto.getDatabaseName(),
                                element.getChUserName(), BtoaEncode.decrypt(element.getChUserPassword()),
                                "", null, null, null);
                        Long count = 0L;
                        try {
                            count = HiveUtil.countTableLine(connect, dto.getItemName());
                            HiveUtil.close(connect);
                        } catch (Exception e) {
                            log.error("统计表行数错误", e);
                        }
                        if (StringUtils.isEmpty(hiveDescribe)) {
                            if (subject.containsKey("其他")) {
                                subject.put("其他", subject.get("其他") + count);
                            } else {
                                subject.put("其他", count);
                            }
                        } else {
                            if (subject.containsKey(hiveDescribe)) {
                                subject.put(hiveDescribe, subject.get(hiveDescribe) + count);
                            } else {
                                subject.put(hiveDescribe, count);
                            }
                        }
                    }
                }
                vo.setCategoryCount(vo.getSubject().values().stream().mapToLong(l -> l).sum());
                areaVoList.add(vo);
            });
            data.setAreaVoList(areaVoList);
            timedCache.put(CACHE_KEY, data);
            return data;
        }
    }

}
