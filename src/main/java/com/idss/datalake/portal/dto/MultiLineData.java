/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2023/6/20 13:47
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.portal.dto;

import lombok.Data;

import java.util.List;

/**
 * Copyright 2023 IDSS
 * <p> 折线图数据
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2023/6/20 13:47
 */
@Data
public class MultiLineData {

    /**
     * X轴数据
     */
    private List<String> xData;

    /**
     * Y轴数据
     */
    private List<MultiLine> line;
}
