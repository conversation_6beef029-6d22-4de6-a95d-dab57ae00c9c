/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2023/6/20 14:31
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.portal.mapper;

import java.util.List;
import java.util.Map;

/**
 * Copyright 2023 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2023/6/20 14:31
 */
public interface ApiMapper {

    int totalApi();

    int totalApply(Map<String, Object> params);

    int totalInvoke();

    List<Map<String, Object>> top();

    List<Map<String, Object>> invokeTrend();
}
