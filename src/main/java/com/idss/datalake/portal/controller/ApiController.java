/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2023/6/20 13:36
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.portal.controller;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.portal.dto.*;
import com.idss.datalake.portal.service.IApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright 2023 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2023/6/20 13:36
 */
@Slf4j
@RestController
@RequestMapping("/portal/api")
public class ApiController {

    @Autowired
    private IApiService apiService;

    @PostMapping("/statistics")
    public ResultBean statistics(){
        try {
            ApiStatistics statistics = apiService.queryStatistics();
            return ResultBean.success(statistics);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @PostMapping("/invoke")
    public ResultBean invoke(){
        try {
            BarYData barData = apiService.top10();
            return ResultBean.success(barData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @PostMapping("/invokeTrend")
    public ResultBean invokeTrend(){
        try {
            LineData lineData = apiService.invokeTrend();
            return ResultBean.success(lineData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }
}
