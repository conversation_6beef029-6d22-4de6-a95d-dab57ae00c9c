/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2023/6/20 13:36
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.portal.controller;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.portal.dto.*;
import com.idss.datalake.portal.service.IDatamartService;
import com.idss.datalake.portal.service.IResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright 2023 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2023/6/20 13:36
 */
@Slf4j
@RestController
@RequestMapping("/portal/resource")
public class ResourceController {

    @Autowired
    private IResourceService resourceService;

    @PostMapping("/statistics")
    public ResultBean statistics(){
        try {
            ResourceStatistics statistics = resourceService.queryStatistics();
            return ResultBean.success(statistics);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @PostMapping("/reamin")
    public ResultBean reamin(){
        try {
            ResourceRemain resourceRemain = resourceService.remain();
            return ResultBean.success(resourceRemain);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @PostMapping("/useTrend")
    public ResultBean subscribeTrend(){
        try {
            MultiLineData lineData = resourceService.resourceTrend();
            return ResultBean.success(lineData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }
}
