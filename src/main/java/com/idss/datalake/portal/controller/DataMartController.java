/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2023/6/20 13:36
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.portal.controller;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.datalake.datamart.dto.request.QueryElementRequest;
import com.idss.datalake.datamart.dto.response.QueryElementVo;
import com.idss.datalake.portal.dto.DatamartStatistics;
import com.idss.datalake.portal.dto.LineData;
import com.idss.datalake.portal.dto.PieData;
import com.idss.datalake.portal.service.IDatamartService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright 2023 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2023/6/20 13:36
 */
@Slf4j
@RestController
@RequestMapping("/portal/datamart")
public class DataMartController {

    @Autowired
    private IDatamartService datamartService;

    @PostMapping("/statistics")
    public ResultBean statistics(){
        try {
            DatamartStatistics statistics = datamartService.queryStatistics();
            return ResultBean.success(statistics);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @PostMapping("/category")
    public ResultBean category(){
        try {
            List<PieData> pieDatas = datamartService.category();
            return ResultBean.success(pieDatas);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @PostMapping("/subscribeTrend")
    public ResultBean subscribeTrend(){
        try {
            LineData lineData = datamartService.subscribeTrend();
            return ResultBean.success(lineData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @PostMapping("/groupCnt")
    public ResultBean groupCnt(){
        try {
            LineData lineData = datamartService.groupCnt();
            return ResultBean.success(lineData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }
}
