/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2023/6/20 13:40
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.portal.service.impl;

import com.idss.datalake.portal.dto.*;
import com.idss.datalake.portal.service.IResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Copyright 2023 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2023/6/20 13:40
 */
@Service
@Slf4j
public class ResourceServiceImpl implements IResourceService {

    @Override
    public ResourceStatistics queryStatistics() {
        ResourceStatistics statistics = new ResourceStatistics();
        statistics.setMemoryTotal(64);
        statistics.setMemoryUse(32);
        statistics.setMemoryUsage(50);
        statistics.setDiskUse(2);
        statistics.setDiskUsage(20);
        statistics.setCpuUse(10);
        statistics.setCpuUsage(10);
        statistics.setCpuUse(4);
        statistics.setGpuUsage(20);
        return statistics;
    }

    @Override
    public ResourceRemain remain() {
        ResourceRemain resourceRemain = new ResourceRemain();
        resourceRemain.setRemainMemory(32);
        resourceRemain.setMemoryPercent(50);

        resourceRemain.setRemainDisk(8);
        resourceRemain.setDiskPercent(80);

        resourceRemain.setRemainCPU(40);
        resourceRemain.setCpuPercent(80);

        resourceRemain.setRemainGPU(16);
        resourceRemain.setGpuPercent(80);
        return resourceRemain;
    }

    @Override
    public MultiLineData resourceTrend() {
        MultiLineData multiLineData = new MultiLineData();
        List<String> xData = new ArrayList<>();
        xData.add("2023-06-01");
        xData.add("2023-06-02");
        xData.add("2023-06-03");
        xData.add("2023-06-04");
        xData.add("2023-06-05");

        MultiLine cpu = new MultiLine();
        List<Integer> cpuTrend = new ArrayList<>();
        cpuTrend.add(10);
        cpuTrend.add(15);
        cpuTrend.add(20);
        cpuTrend.add(12);
        cpuTrend.add(25);
        cpu.setName("CPU");
        cpu.setYData(cpuTrend);

        MultiLine disk = new MultiLine();
        List<Integer> diskTrend = new ArrayList<>();
        diskTrend.add(30);
        diskTrend.add(40);
        diskTrend.add(60);
        diskTrend.add(30);
        diskTrend.add(25);
        disk.setName("磁盘");
        disk.setYData(diskTrend);

        MultiLine memory = new MultiLine();
        List<Integer> memoryTrend = new ArrayList<>();
        memoryTrend.add(10);
        memoryTrend.add(35);
        memoryTrend.add(40);
        memoryTrend.add(52);
        memoryTrend.add(35);
        memory.setName("内存");
        memory.setYData(memoryTrend);

        multiLineData.setXData(xData);
        List<MultiLine> lines = new ArrayList<>();
        lines.add(cpu);
        lines.add(disk);
        lines.add(memory);
        multiLineData.setLine(lines);
        return multiLineData;
    }
}
