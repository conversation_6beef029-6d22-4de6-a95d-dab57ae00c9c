/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2023/6/20 16:26
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.portal.service.impl;

import com.idss.datalake.portal.dto.ApiStatistics;
import com.idss.datalake.portal.dto.BarYData;
import com.idss.datalake.portal.dto.LineData;
import com.idss.datalake.portal.mapper.ApiMapper;
import com.idss.datalake.portal.service.IApiService;
import com.idss.radar.datasource.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Copyright 2023 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2023/6/20 16:26
 */
@Service
@Slf4j
public class ApiServiceImpl implements IApiService {

    @Autowired
    private ApiMapper apiMapper;

    @Override
    public ApiStatistics queryStatistics() {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        ApiStatistics statistics = new ApiStatistics();
        // API总个数
        statistics.setTotal(apiMapper.totalApi());
        // 申请个数
        Map<String, Object> params = new HashMap<>();
        statistics.setApplyCount(apiMapper.totalApply(params));
        // 审批个数
        params.put("auditStatus", "2");
        statistics.setApprovalCount(apiMapper.totalApply(params));
        // 调用个数
        statistics.setInvokeCount(apiMapper.totalInvoke());
        return statistics;
    }

    @Override
    public BarYData top10() {
        BarYData barData = new BarYData();
        List<Integer> xData = new ArrayList<>();
        List<String> yData = new ArrayList<>();
        barData.setXData(xData);
        barData.setYData(yData);

        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<Map<String, Object>> invokes = apiMapper.top();
        for (Map<String, Object> invoke : invokes) {
            xData.add(((Long) invoke.get("cnt")).intValue());
            yData.add((String) invoke.get("name"));
        }
        return barData;
    }

    @Override
    public LineData invokeTrend() {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<Map<String, Object>> invokes = apiMapper.invokeTrend();
        LineData lineData = new LineData();
        List<String> xData = new ArrayList<>();
        List<Integer> yData = new ArrayList<>();
        lineData.setXData(xData);
        lineData.setYData(yData);
        for (Map<String, Object> invoke : invokes) {
            xData.add((String) invoke.get("formatDate"));
            yData.add(((Long) invoke.get("cnt")).intValue());
        }
        return lineData;
    }
}
