/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2023/6/20 13:37
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.portal.service;

import com.idss.datalake.portal.dto.DatamartStatistics;
import com.idss.datalake.portal.dto.LineData;
import com.idss.datalake.portal.dto.PieData;

import java.util.List;

/**
 * Copyright 2023 IDSS
 * <p>
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2023/6/20 13:37
 */
public interface IDatamartService {

    /**
     * 集市使用统计
     * @return
     */
    DatamartStatistics queryStatistics();

    /**
     * 订阅集市类别分类
     * @return
     */
    List<PieData> category();

    /**
     * 订阅趋势
     * @return
     */
    LineData subscribeTrend();

    LineData groupCnt();
}
