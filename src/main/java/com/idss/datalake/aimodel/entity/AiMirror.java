package com.idss.datalake.aimodel.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * ai镜像
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AiMirror implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 功能分类
     */
    private String functionClassify;

    /**
     * 仓库ID
     */
    private Integer repositoryId;

    @TableField(exist = false)
    private String repositoryName;

    @TableField(exist = false)
    private String repositoryAddr;

    /**
     * 镜像名称
     */
    private String mirrorName;

    /**
     * 标签
     */
    private String tag;

    /**
     * 镜像描述
     */
    private String mirrorDesc;

    /**
     * 镜像用途：notebook，模型开发
     */
    private String mirrorUse;

    /**
     * 租户
     */
    private String mirrorFilePath;

    private String mirrorFileName;

    /**
     * 推送状态，0推送中，1推送成功，2推送失败
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String errorMessage;

    /**
     * 租户
     */
    private Integer tenantId;

    private String createUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String updateUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;


    /**
     * 镜像地址
     */
    private String mirrorAddr;

    /**
     * 添加方式 1-上传镜像 2-镜像引入
     */
    private String addType;

    /**
     * 发布状态 0-未发布 1-已发布
     */
    private String publishStatus;


    /**
     * 引用次数
     */
    @TableField(exist = false)
    private int quoteCnt;

    @TableField(exist = false)
    private List<String> mirrorUses;
}
