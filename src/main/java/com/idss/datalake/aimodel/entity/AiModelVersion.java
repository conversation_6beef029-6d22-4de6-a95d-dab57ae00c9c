package com.idss.datalake.aimodel.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * ai模型版本管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AiModelVersion implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 模型ID
     */
    private Integer modelId;
    @TableField(exist = false)
    private String modelName;

    /**
     * 模型名称
     */
    private String modelVersion;

    /**
     * 模型类型: 0本地，1离线
     */
    private Integer modelType;

    /**
     * 模型文件，本地和离线都是一个文件
     */
    private String modelFile;

    @TableField(exist = false)
    private String fileName;

    /**
     * 描述
     */
    @TableField("\"desc\"")
    private String desc;

    /**
     * 发布状态：0未发布，1发布，2灰度发布
     */
    private Integer pushStatus;

    private Integer tenantId;

    private String createUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String updateUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;


}
