package com.idss.datalake.aimodel.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * ai_notebook
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AiNotebook implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 项目ID
     */
    private Integer projectId;

    @TableField(exist = false)
    private String projectName;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    @TableField("\"desc\"")
    private String desc;

    /**
     * 仓库ID
     */
    private Integer repositoryId;

    @TableField(exist = false)
    private String repositoryName;

    /**
     * 镜像ID
     */
    private Integer mirrorId;

    @TableField(exist = false)
    private String mirrorName;

    /**
     * 申请内存
     */
    private Integer needMem;

    /**
     * 申请CPU
     */
    private Integer needCpu;

    /**
     * 申请GPU
     */
    private Integer needGpu;

    /**
     * 状态：0分配中，1分配成功，1分配失败
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String errorMessage;

    /**
     * 分配的url
     */
    private String notebookUrl;

    private Integer tenantId;

    private String createUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String updateUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;


}
