package com.idss.datalake.aimodel.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * AI项目管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AiProjectModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 命名空间
     */
    private String nameSpace;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 所属租户
     */
    private Integer tenantId;

    @TableField(exist = false)
    private String tenantName;

    /**
     * CPU限额
     */
    private Integer cpuLimit;

    /**
     * 需求CPU限额
     */
    private Integer needCpuLimit;

    /**
     * 内存限额
     */
    private Integer memLimit;

    /**
     * 需求内存限额
     */
    private Integer needMemLimit;

    /**
     * GPU限额
     */
    private Integer gpuLimit;

    /**
     * 存储限额
     */
    private Integer storageLimit;

    /**
     * 必填，租户在磐基中使用pvc工作目录的大小限制
     * 注意：PVC 工作目录大小+PVC 归档目录大小 <= 存储空间大小
     */
    private Integer pvcStorage;

    /**
     * 必填，租户在磐基中使用pvc工作目录的大小限制
     * 注意：PVC 工作目录大小+PVC 归档目录大小 <= 存储空间大小
     */
    private Integer pvcArchive;



    /**
     * 分配状态，0分配中，1分配成功，2分配失败
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String errorMessage;

    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String updateUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;


}
