package com.idss.datalake.aimodel.controller;


import com.idss.datalake.aimodel.dto.AiProjectDeleteDto;
import com.idss.datalake.aimodel.dto.AiProjectPageRequest;
import com.idss.datalake.aimodel.entity.AiProjectModel;
import com.idss.datalake.aimodel.service.IAiProjectModelService;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * AI项目管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@RestController
@RequestMapping("/aimodel/project")
public class AiProjectModelController {
    @Autowired
    private IAiProjectModelService aiProjectModelService;

    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody AiProjectModel project) {
        return aiProjectModelService.addOrUpdate(project);
    }

    @PostMapping("/remove")
    public ResultBean remove(@RequestBody AiProjectDeleteDto dto) {
        if (dto != null) {
            return aiProjectModelService.remove(dto);
        }
        return ResultBean.fail("参数错误！");
    }

    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        return aiProjectModelService.detail(id);
    }

    @PostMapping("/page")
    public BasePageResponse<List<AiProjectModel>> page(@RequestBody AiProjectPageRequest request) {
        return aiProjectModelService.page(request);
    }
}
