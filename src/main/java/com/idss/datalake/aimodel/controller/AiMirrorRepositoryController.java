package com.idss.datalake.aimodel.controller;


import com.idss.datalake.aimodel.dto.AiMirrorRepositoryPageRequest;
import com.idss.datalake.aimodel.entity.AiMirrorRepository;
import com.idss.datalake.aimodel.service.IAiMirrorRepositoryService;
import com.idss.datalake.aimodel.util.DockerPushUtil;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * ai镜像仓库 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@RestController
@Slf4j
@RequestMapping("/aimodel/mirror/repository")
public class AiMirrorRepositoryController {
    @Autowired
    private IAiMirrorRepositoryService aiMirrorRepositoryService;

    @PostMapping("/testConnect")
    public ResultBean testConnect(@RequestBody AiMirrorRepository repository){
        return DockerPushUtil.testConnect(repository.getRepositoryAddr(), repository.getUserName(), repository.getPassword());
    }

    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody AiMirrorRepository repository) {
        return aiMirrorRepositoryService.addOrUpdate(repository);
    }

    @PostMapping("/remove")
    public ResultBean remove(@RequestBody Map<String, List<Long>> param) {
        if (param != null && param.containsKey("ids")) {
            List<Long> ids = param.get("ids");
            return aiMirrorRepositoryService.remove(ids);
        }
        return ResultBean.fail("参数错误！");
    }

    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        return aiMirrorRepositoryService.detail(id);
    }

    @GetMapping("/select")
    public ResultBean select(){
        return aiMirrorRepositoryService.select();
    }

    @GetMapping("/search")
    public ResultBean search(){
        try {
            List<AiMirrorRepository> repositories = aiMirrorRepositoryService.search();
            return ResultBean.success(repositories);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultBean.fail("查询失败");
        }
    }

    @PostMapping("/page")
    public BasePageResponse<List<AiMirrorRepository>> page(@RequestBody AiMirrorRepositoryPageRequest request) {
        return aiMirrorRepositoryService.page(request);
    }

}
