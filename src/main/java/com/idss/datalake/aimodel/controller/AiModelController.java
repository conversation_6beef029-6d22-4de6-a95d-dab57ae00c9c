package com.idss.datalake.aimodel.controller;


import com.idss.datalake.aimodel.dto.AiModelDto;
import com.idss.datalake.aimodel.dto.AiModelPageRequest;
import com.idss.datalake.aimodel.dto.AiNotebookPageRequest;
import com.idss.datalake.aimodel.entity.AiModel;
import com.idss.datalake.aimodel.entity.AiNotebook;
import com.idss.datalake.aimodel.enums.TrainFramework;
import com.idss.datalake.aimodel.service.IAiModelService;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * ai模型管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@RestController
@RequestMapping("/ai/model")
public class AiModelController {
    @Autowired
    private IAiModelService aiModelService;

    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody AiModelDto dto) {
        return aiModelService.addOrUpdate(dto);
    }

    @PostMapping("/remove")
    public ResultBean remove(@RequestBody Map<String, List<Long>> param) {
        if (param != null && param.containsKey("ids")) {
            List<Long> ids = param.get("ids");
            return aiModelService.remove(ids);
        }
        return ResultBean.fail("参数错误！");
    }

    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        return aiModelService.detail(id);
    }

    @GetMapping("/namespace")
    public ResultBean namespace() {
        return aiModelService.namespace();
    }

    @GetMapping("/framework")
    public ResultBean framework() {
        List<Map<String, String>> result = new ArrayList<>();
        for (TrainFramework value : TrainFramework.values()) {
            Map<String, String> map = new HashMap<>();
            map.put("code", value.getCode());
            map.put("name", value.getName());

            result.add(map);
        }
        return ResultBean.success(result);
    }

    @PostMapping("/page")
    public BasePageResponse<List<AiModel>> page(@RequestBody AiModelPageRequest request) {
        return aiModelService.page(request);
    }
}
