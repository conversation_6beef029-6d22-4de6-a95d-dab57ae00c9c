package com.idss.datalake.aimodel.controller;


import com.idss.datalake.aimodel.dto.AiNotebookPageRequest;
import com.idss.datalake.aimodel.dto.AiProjectPageRequest;
import com.idss.datalake.aimodel.entity.AiNotebook;
import com.idss.datalake.aimodel.service.IAiNotebookService;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * ai_notebook 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@RestController
@RequestMapping("/aimodel/notebook")
public class AiNotebookController {
    @Autowired
    private IAiNotebookService aiNotebookService;

    @PostMapping("/addOrUpdate")
    public ResultBean addOrUpdate(@RequestBody AiNotebook notebook) {
        return aiNotebookService.addOrUpdate(notebook);
    }

    @PostMapping("/remove")
    public ResultBean remove(@RequestBody Map<String, List<Long>> param) {
        if (param != null && param.containsKey("ids")) {
            List<Long> ids = param.get("ids");
            return aiNotebookService.remove(ids);
        }
        return ResultBean.fail("参数错误！");
    }

    @GetMapping("/detail/{id}")
    public ResultBean detail(@PathVariable("id") Long id) {
        return aiNotebookService.detail(id);
    }
    @GetMapping("/namespace")
    public ResultBean namespace(){
        return aiNotebookService.namespace();
    }
    @PostMapping("/page")
    public BasePageResponse<List<AiNotebook>> page(@RequestBody AiNotebookPageRequest request) {
        return aiNotebookService.page(request);
    }
}
