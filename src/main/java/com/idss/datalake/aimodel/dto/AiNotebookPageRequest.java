/************************ CHAN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/8/8
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/8/8
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.aimodel.dto;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2024/8/8
 */
@Data
public class AiNotebookPageRequest extends BasePageRequest {
    /**
     * 名称
     */
    private String name;

    /**
     * 状态：0分配中，1分配成功，1分配失败
     */
    private Integer status;
}
