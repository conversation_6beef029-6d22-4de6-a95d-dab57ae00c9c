/************************ CHANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/8/8
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/8/8
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.aimodel.dto;

import com.idss.datalake.datagovern.metadata.base.BasePageRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2024/8/8
 */
@Data
public class AiPushPageRequest extends BasePageRequest {
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 部署状态:0失败，1成功
     */
    private Integer deployStatus;

    /**
     * 模型版本
     */
    private String modelVersion;

    /**
     * 服务类型
     */
    private String serviceType;
}
