package com.idss.datalake.aimodel.enums;

public enum TrainFramework {

    xgb("xgb", "xgb"),

    tf("tf", "tf"),

    pytorch("pytorch", "pytorch"),

    onnx("onnx", "onnx"),

    tensorrt("tensorrt", "tensorrt"),

    sklearn("sklearn", "sklearn"),

    selfDefine("selfDefine", "自定义");

    private String code;

    private String name;

    TrainFramework(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
