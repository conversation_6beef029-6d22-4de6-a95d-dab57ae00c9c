package com.idss.datalake.aimodel.service;

import com.idss.datalake.aimodel.dto.AiMirrorRepositoryPageRequest;
import com.idss.datalake.aimodel.dto.AiProjectPageRequest;
import com.idss.datalake.aimodel.entity.AiMirrorRepository;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import java.util.List;

/**
 * <p>
 * ai镜像仓库 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
public interface IAiMirrorRepositoryService extends IService<AiMirrorRepository> {
    ResultBean addOrUpdate(AiMirrorRepository repository);

    ResultBean remove(List<Long> ids);

    ResultBean detail(Long id);

    ResultBean select();

    List<AiMirrorRepository> search() throws Exception;

    BasePageResponse<List<AiMirrorRepository>> page(AiMirrorRepositoryPageRequest request);
}
