package com.idss.datalake.aimodel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.aimodel.dto.AiMirrorRepositoryPageRequest;
import com.idss.datalake.aimodel.entity.AiMirror;
import com.idss.datalake.aimodel.entity.AiMirrorRepository;
import com.idss.datalake.aimodel.mapper.AiMirrorRepositoryMapper;
import com.idss.datalake.aimodel.service.IAiMirrorRepositoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.aimodel.service.IAiMirrorService;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.radar.datasource.DatasourceType;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * ai镜像仓库 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Service
public class AiMirrorRepositoryServiceImpl extends ServiceImpl<AiMirrorRepositoryMapper, AiMirrorRepository> implements IAiMirrorRepositoryService {
    @Resource
    private AiMirrorRepositoryMapper aiMirrorRepositoryMapper;
    @Autowired
    private IAiMirrorService aiMirrorService;
    @Override
    public ResultBean addOrUpdate(AiMirrorRepository repository) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        UserValueObject uvo = UmsUtils.getUVO();
        if (repository.getId() == null) {
            int count = this.count(new QueryWrapper<AiMirrorRepository>().eq("repository_name", repository.getRepositoryName()));
            if (count > 0) {
                return ResultBean.fail("名称重复");
            }
            repository.setTenantId(uvo.getTenantId());
            repository.setCreateUser(uvo.getUserName());
            repository.setUpdateUser(uvo.getUserName());

            this.save(repository);
        } else {
            int count = this.count(new QueryWrapper<AiMirrorRepository>().ne("id", repository.getId()).eq("repository_name", repository.getRepositoryName()));
            if (count > 0) {
                return ResultBean.fail("名称重复");
            }
            repository.setUpdateTime(LocalDateTime.now());
            this.updateById(repository);
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean remove(List<Long> ids) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        if(CollectionUtils.isNotEmpty(ids)){
            for (Long id : ids) {
                int cnt = aiMirrorService.count(new QueryWrapper<AiMirror>().eq("repository_id", id));
                if(cnt > 0){
                    return ResultBean.fail(this.getById(id).getRepositoryName()+"已有镜像，不能删除");
                }
            }
            for (Long id : ids) {
                this.removeById(id);
            }
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean detail(Long id) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        return ResultBean.success(this.getById(id));
    }

    @Override
    public ResultBean select() {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        return ResultBean.success(this.list(new QueryWrapper<AiMirrorRepository>().eq("tenant_id",UmsUtils.getUVO().getTenantId())));
    }

    @Override
    public List<AiMirrorRepository> search() throws Exception {
        int tenantId = UmsUtils.getUVO().getTenantId();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<AiMirrorRepository> repositories = this.list(new QueryWrapper<AiMirrorRepository>()
                .eq("tenant_id",tenantId));
        List<Integer> repositoryIds = repositories.stream().map(repository -> repository.getId()).collect(Collectors.toList());
        List<AiMirror> mirrors = aiMirrorService.list(new LambdaQueryWrapper<AiMirror>()
                .eq(AiMirror::getPublishStatus, "1")
                .ne(AiMirror::getTenantId, tenantId));
        if(CollectionUtils.isNotEmpty(mirrors)) {
            List<Integer> ids = mirrors.stream().map(mirror -> mirror.getRepositoryId()).collect(Collectors.toList());
            List<AiMirrorRepository> mirrorRepositories = this.list(new LambdaQueryWrapper<AiMirrorRepository>()
                    .in(AiMirrorRepository::getId, ids));
            for (AiMirrorRepository mirrorRepository : mirrorRepositories) {
                if(!repositoryIds.contains(mirrorRepository.getId())) {
                    repositories.add(mirrorRepository);
                }
            }
        }
        return repositories;
    }

    @Override
    public BasePageResponse<List<AiMirrorRepository>> page(AiMirrorRepositoryPageRequest request) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        UserValueObject uvo = UmsUtils.getUVO();
        request.setTenantId(Long.valueOf(uvo.getTenantId()+""));
        List<AiMirrorRepository> list = new ArrayList<>();
        PageHelper.offsetPage(request.getOffset(), request.getPageSize());
        Page<AiMirrorRepository> page = aiMirrorRepositoryMapper.queryPage(request);
        List<AiMirrorRepository> result = page.getResult();
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), result);
    }
}
