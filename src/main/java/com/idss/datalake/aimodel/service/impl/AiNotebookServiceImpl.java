package com.idss.datalake.aimodel.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.aimodel.dto.AiNotebookPageRequest;
import com.idss.datalake.aimodel.entity.AiMirror;
import com.idss.datalake.aimodel.entity.AiMirrorRepository;
import com.idss.datalake.aimodel.entity.AiNotebook;
import com.idss.datalake.aimodel.entity.AiProjectModel;
import com.idss.datalake.aimodel.mapper.AiNotebookMapper;
import com.idss.datalake.aimodel.service.IAiMirrorRepositoryService;
import com.idss.datalake.aimodel.service.IAiMirrorService;
import com.idss.datalake.aimodel.service.IAiNotebookService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.aimodel.service.IAiProjectModelService;
import com.idss.datalake.aimodel.util.AIUtil;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.radar.datasource.DatasourceType;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * ai_notebook 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Service
@Slf4j
public class AiNotebookServiceImpl extends ServiceImpl<AiNotebookMapper, AiNotebook> implements IAiNotebookService {
    @Resource
    private AiNotebookMapper aiNotebookMapper;
    @Autowired
    private IAiProjectModelService aiProjectModelService;
    @Autowired
    private IAiMirrorRepositoryService repositoryService;
    @Autowired
    private IAiMirrorService mirrorService;

    @Value("${ai.python3-path}")
    private String pythonPath;

    @Value("${ai.script-path}")
    private String scriptPath;

    @Value("${ai.notebook-create}")
    private String notebookCreate;

    @Value("${ai.notebook-delete}")
    private String notebookDelete;

    @Value("${ai.notebook-status}")
    private String notebookStatus;

    @Override
    public ResultBean addOrUpdate(AiNotebook notebook) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        AiProjectModel project = aiProjectModelService.getById(notebook.getProjectId());
        AiMirrorRepository repository = repositoryService.getById(notebook.getRepositoryId());
        AiMirror mirror = mirrorService.getById(notebook.getMirrorId());
        UserValueObject uvo = UmsUtils.getUVO();
        if (notebook.getId() == null) {
            int count = this.count(new QueryWrapper<AiNotebook>().eq("name", notebook.getName()));
            if (count > 0) {
                return ResultBean.fail("名称重复");
            }
            notebook.setTenantId(uvo.getTenantId());
            notebook.setCreateUser(uvo.getUserName());
            notebook.setUpdateUser(uvo.getUserName());
            this.save(notebook);

            String params = "{\n" +
                    "  \"nodebook_id\": "+notebook.getId()+",\n" +
                    "  \"notebook_name\": \""+notebook.getName()+"\",\n" +
                    "  \"create_user\": \""+uvo.getUserName()+"\",\n" +
                    "  \"namespace\": \""+project.getEnName()+"\",\n" +
                    "  \"image\": \""+repository.getRepositoryAddr()+"/"+mirror.getMirrorName()+"\",\n" +
                    "  \"resource_cpu\": "+notebook.getNeedCpu()+",\n" +
                    "  \"resource_memory\": \""+notebook.getNeedMem()+"G\"\n" +
                    "}";
            params = JSON.toJSONString(JSON.parse(params));
            JSONObject json = new JSONObject();
            json.put("path", notebookCreate);
            json.put("params", params);
            try {
                String result = AIUtil.runPython(pythonPath, scriptPath, json);
                log.info("AI新增notebook返回:{}", result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (!(jsonObject.containsKey("code") && jsonObject.getInteger("code") == 200)) {
                    notebook.setStatus(2);
                    notebook.setErrorMessage(jsonObject.getString("message"));
                }
            } catch (Exception e) {
                log.error("报错错误", e);
                return ResultBean.fail("报错错误" + e.getMessage());
            }
            this.updateById(notebook);
        } else {
            int count = this.count(new QueryWrapper<AiNotebook>().ne("id", notebook.getId()).eq("name", notebook.getName()));
            if (count > 0) {
                return ResultBean.fail("名称重复");
            }
            AiNotebook byId = this.getById(notebook.getId());
            byId.setDesc(notebook.getDesc());
            byId.setNeedCpu(notebook.getNeedCpu());
            byId.setNeedMem(notebook.getNeedMem());
            byId.setNeedGpu(notebook.getNeedGpu());
            byId.setUpdateTime(LocalDateTime.now());

            String params = "{\n" +
                    "  \"nodebook_id\": "+notebook.getId()+",\n" +
                    "  \"notebook_name\": \""+notebook.getName()+"\",\n" +
                    "  \"create_user\": \""+uvo.getUserName()+"\",\n" +
                    "  \"namespace\": \""+project.getEnName()+"\",\n" +
                    "  \"image\": \""+repository.getRepositoryAddr()+"/"+mirror.getMirrorName()+"\",\n" +
                    "  \"resource_cpu\": "+notebook.getNeedCpu()+",\n" +
                    "  \"resource_memory\": \""+notebook.getNeedMem()+"G\"\n" +
                    "}";
            params = JSON.toJSONString(JSON.parse(params));
            JSONObject json = new JSONObject();
            json.put("path", notebookCreate);
            json.put("params", params);
            try {
                String result = AIUtil.runPython(pythonPath, scriptPath, json);
                log.info("AI新增notebook返回:{}", result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (!(jsonObject.containsKey("code") && jsonObject.getInteger("code") == 200)) {
                    notebook.setStatus(2);
                    notebook.setErrorMessage(jsonObject.getString("message"));
                }
            } catch (Exception e) {
                log.error("报错错误", e);
                notebook.setStatus(2);
                notebook.setErrorMessage(e.getMessage());
                return ResultBean.fail("报错错误" + e.getMessage());
            }
            this.updateById(notebook);
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean remove(List<Long> ids) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        if (CollectionUtils.isNotEmpty(ids)) {
            for (Long id : ids) {
                AiNotebook byId = this.getById(id);
                AiProjectModel project = aiProjectModelService.getById(byId.getProjectId());
                String params = "{\n" +
                        "  \"namespace\": \"" + project.getEnName() + "\",\n" +
                        "  \"notebook_name\": \"" + byId.getName() + "\" \n" +
                        "}";
                params = JSON.toJSONString(JSON.parse(params));
                JSONObject json = new JSONObject();
                json.put("path", notebookDelete);
                json.put("params", params);
                try {
                    String result = AIUtil.runPython(pythonPath, scriptPath, json);
                    log.info("AI删除notebook返回:{}", result);
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == 200) {
                        this.removeById(id);
                    } else {
                        return ResultBean.fail(byId.getName() + "删除失败,原因:", jsonObject.getString("message"));
                    }
                } catch (Exception e) {
                    log.error("报错错误", e);
                    return ResultBean.fail("报错错误" + e.getMessage());
                }
            }
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean detail(Long id) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        return ResultBean.success(this.getById(id));
    }

    @Override
    public ResultBean namespace() {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        return ResultBean.success(aiProjectModelService.list(new QueryWrapper<AiProjectModel>().eq("tenant_id",UmsUtils.getUVO().getTenantId())));
    }

    @Override
    public BasePageResponse<List<AiNotebook>> page(AiNotebookPageRequest request) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        UserValueObject uvo = UmsUtils.getUVO();
        request.setTenantId(Long.valueOf(uvo.getTenantId() + ""));
        List<AiNotebook> list = new ArrayList<>();
        PageHelper.offsetPage(request.getOffset(), request.getPageSize());
        Page<AiNotebook> page = aiNotebookMapper.queryPage(request);
        List<AiNotebook> result = page.getResult();
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), result);
    }
}
