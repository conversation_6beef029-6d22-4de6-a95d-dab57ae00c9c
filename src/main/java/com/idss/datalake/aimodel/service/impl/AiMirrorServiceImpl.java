package com.idss.datalake.aimodel.service.impl;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.aimodel.dto.AiMirrorPageRequest;
import com.idss.datalake.aimodel.entity.AiMirror;
import com.idss.datalake.aimodel.entity.AiMirrorRepository;
import com.idss.datalake.aimodel.entity.AiNotebook;
import com.idss.datalake.aimodel.mapper.AiMirrorMapper;
import com.idss.datalake.aimodel.service.IAiMirrorRepositoryService;
import com.idss.datalake.aimodel.service.IAiMirrorService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.aimodel.service.IAiNotebookService;
import com.idss.datalake.aimodel.util.DockerPushUtil;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.radar.datasource.DatasourceType;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * ai镜像 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Service
public class AiMirrorServiceImpl extends ServiceImpl<AiMirrorMapper, AiMirror> implements IAiMirrorService {
    @Autowired
    private IAiMirrorRepositoryService aiMirrorRepositoryService;
    @Resource
    private AiMirrorMapper aiMirrorMapper;
    @Autowired
    private IAiNotebookService aiNotebookService;

    @Override
    public ResultBean addOrUpdate(AiMirror aiMirror) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        UserValueObject uvo = UmsUtils.getUVO();
        if (aiMirror.getId() == null) {
            int count = this.count(new QueryWrapper<AiMirror>().eq("mirror_name", aiMirror.getMirrorName()));
            if (count > 0) {
                return ResultBean.fail("名称重复");
            }
            aiMirror.setTenantId(uvo.getTenantId());
            aiMirror.setCreateUser(uvo.getUserName());
            aiMirror.setUpdateUser(uvo.getUserName());
            List<String> mirrorUses = aiMirror.getMirrorUses();
            aiMirror.setMirrorUse(StringUtils.join(mirrorUses, ","));
            BASE64Decoder decoder = new BASE64Decoder();
            try {
                aiMirror.setMirrorFilePath(new String(decoder.decodeBuffer(aiMirror.getMirrorFilePath())));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            aiMirror.setMirrorFileName(FileUtil.getName(aiMirror.getMirrorFilePath()));

            AiMirrorRepository aiMirrorRepository = aiMirrorRepositoryService.getById(aiMirror.getRepositoryId());
            if("1".equals(aiMirror.getAddType())) {
                // 上传镜像才需要推送
                try {
                    DockerPushUtil.uploadImage(aiMirror.getMirrorFilePath(), aiMirrorRepository.getRepositoryAddr(), aiMirror.getMirrorName(), aiMirrorRepository.getUserName(), aiMirrorRepository.getPassword());
                    aiMirror.setStatus(1);
                } catch (Exception e) {
                    log.error("推送镜像失败",e);
                    aiMirror.setStatus(2);
                    aiMirror.setErrorMessage(e.getMessage());
                }
            }

            this.save(aiMirror);
        } else {
            int count = this.count(new QueryWrapper<AiMirror>().ne("id", aiMirror.getId()).eq("mirror_name", aiMirror.getMirrorName()));
            if (count > 0) {
                return ResultBean.fail("名称重复");
            }
            AiMirror byId = this.getById(aiMirror.getId());
            byId.setMirrorDesc(aiMirror.getMirrorDesc());
            byId.setUpdateTime(LocalDateTime.now());
            List<String> mirrorUses = aiMirror.getMirrorUses();
            byId.setMirrorUse(StringUtils.join(mirrorUses, ","));
            byId.setMirrorName(aiMirror.getMirrorName());
            this.updateById(byId);
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean remove(List<Long> ids) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        if (CollectionUtils.isNotEmpty(ids)) {
            for (Long id : ids) {
                int cnt = aiNotebookService.count(new QueryWrapper<AiNotebook>().eq("mirror_id", id));
                if (cnt > 0) {
                    return ResultBean.fail(this.getById(id).getMirrorName() + "已使用，不能删除");
                }
            }
            for (Long id : ids) {
                this.removeById(id);
            }
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean detail(Long id) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        AiMirror aiMirror = this.getById(id);
        String mirrorUse = aiMirror.getMirrorUse();
        if (StringUtils.isNotBlank(mirrorUse)) {
            String[] split = mirrorUse.split(",");
            List<String> list = new ArrayList<>();
            for (String s : split) {
                list.add(s);
            }
            aiMirror.setMirrorUses(list);
        }
        return ResultBean.success(aiMirror);
    }

    @Override
    public ResultBean select(Long repositoryId) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        List<AiMirror> selfMirror = this.list(new QueryWrapper<AiMirror>()
               .eq("tenant_id",UmsUtils.getUVO().getTenantId())
               .eq("repository_id",repositoryId)
               .like("mirror_use","notebook"));
        List<Integer> mirrorIds = selfMirror.stream().map(mirror -> mirror.getId()).collect(Collectors.toList());
        List<AiMirror> publicMirror = this.list(new LambdaQueryWrapper<AiMirror>()
                .eq(AiMirror::getRepositoryId, repositoryId)
                .eq(AiMirror::getPublishStatus, "1")
                .like(AiMirror::getMirrorUse, "notebook"));
        for (AiMirror aiMirror : publicMirror) {
            if(!mirrorIds.contains(aiMirror.getId())) {
                selfMirror.add(aiMirror);
            }
        }
        return ResultBean.success(selfMirror);
    }

    @Override
    public BasePageResponse<List<AiMirror>> page(AiMirrorPageRequest request) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        UserValueObject uvo = UmsUtils.getUVO();
        if("0".equals(request.getStatus())) {
            request.setTenantId(Long.valueOf(uvo.getTenantId() + ""));
        } else {
            request.setPublishStatus("1");
        }
        //request.setTenantId(Long.valueOf(uvo.getTenantId() + ""));
        List<AiMirror> list = new ArrayList<>();
        PageHelper.offsetPage(request.getOffset(), request.getPageSize());
        Page<AiMirror> page = aiMirrorMapper.queryPage(request);
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        } else {
            List<AiMirror> aiMirrors = page.getResult();
            for (AiMirror aiMirror : aiMirrors) {
                int quoteCnt = aiNotebookService.count(new LambdaQueryWrapper<AiNotebook>().eq(AiNotebook::getMirrorId, aiMirror.getId()));
                aiMirror.setQuoteCnt(quoteCnt);
            }
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), aiMirrors);
        }
    }

    @Override
    public void publish(AiMirror aiMirror) {
        this.update(new LambdaUpdateWrapper<AiMirror>()
                .set(AiMirror::getPublishStatus, aiMirror.getPublishStatus())
                .eq(AiMirror::getId, aiMirror.getId()));
    }
}
