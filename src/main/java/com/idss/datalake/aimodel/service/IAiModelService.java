package com.idss.datalake.aimodel.service;

import com.idss.datalake.aimodel.dto.AIVersionPushDto;
import com.idss.datalake.aimodel.dto.AiModelDto;
import com.idss.datalake.aimodel.dto.AiModelPageRequest;
import com.idss.datalake.aimodel.dto.AiNotebookPageRequest;
import com.idss.datalake.aimodel.entity.AiModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.aimodel.entity.AiNotebook;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import java.util.List;

/**
 * <p>
 * ai模型管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface IAiModelService extends IService<AiModel> {

    ResultBean addOrUpdate(AiModelDto aiModelDto);

    ResultBean remove(List<Long> ids);

    ResultBean detail(Long id);

    ResultBean namespace();
    BasePageResponse<List<AiModel>> page(AiModelPageRequest request);

    ResultBean push(AIVersionPushDto dto);
}
