package com.idss.datalake.aimodel.service.impl;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.aimodel.dto.AIVersionPushDto;
import com.idss.datalake.aimodel.dto.AiModelDto;
import com.idss.datalake.aimodel.dto.AiModelPageRequest;
import com.idss.datalake.aimodel.entity.AiModel;
import com.idss.datalake.aimodel.entity.AiModelVersion;
import com.idss.datalake.aimodel.entity.AiNotebook;
import com.idss.datalake.aimodel.entity.AiProjectModel;
import com.idss.datalake.aimodel.mapper.AiModelMapper;
import com.idss.datalake.aimodel.service.IAiMirrorRepositoryService;
import com.idss.datalake.aimodel.service.IAiMirrorService;
import com.idss.datalake.aimodel.service.IAiModelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.aimodel.service.IAiModelVersionService;
import com.idss.datalake.aimodel.service.IAiProjectModelService;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.radar.datasource.DatasourceType;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * ai模型管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Service
public class AiModelServiceImpl extends ServiceImpl<AiModelMapper, AiModel> implements IAiModelService {
    @Autowired
    private IAiModelVersionService modelVersionService;
    @Autowired
    private IAiProjectModelService aiProjectModelService;
    @Resource
    private AiModelMapper aiModelMapper;


    @Override
    public ResultBean addOrUpdate(AiModelDto aiModelDto) {
        UserValueObject uvo = UmsUtils.getUVO();
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        if (aiModelDto.getId() == null) {
            int count = this.count(new QueryWrapper<AiModel>().eq("model_name", aiModelDto.getModelName()));
            if (count > 0) {
                return ResultBean.fail("名称重复");
            }
            AiModel aiModel = new AiModel();
            BeanUtils.copyProperties(aiModelDto, aiModel);
            aiModel.setCreateUser(uvo.getUserName());
            aiModel.setTenantId(uvo.getTenantId());
            aiModel.setCreateTime(LocalDateTime.now());
            aiModel.setUpdateTime(LocalDateTime.now());
            aiModel.setUpdateUser(uvo.getUserName());
            aiModel.setFrameworkName(aiModelDto.getFrameworkName());
            this.save(aiModel);
            AiModelVersion version = new AiModelVersion();
            version.setModelId(aiModel.getId());
            if (StringUtils.isEmpty(aiModelDto.getModelVersion())) {
                SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
                version.setModelVersion(format.format(new Date()) + "1");
            } else {
                version.setModelVersion(aiModelDto.getModelVersion());
            }
            version.setModelType(aiModelDto.getModelType());
            version.setModelFile(aiModelDto.getModelFile());
            version.setTenantId(uvo.getTenantId());
            version.setCreateTime(LocalDateTime.now());
            version.setCreateUser(uvo.getUserName());
            version.setUpdateTime(LocalDateTime.now());
            version.setUpdateUser(uvo.getUserName());
            modelVersionService.save(version);
        } else {
            int count = this.count(new QueryWrapper<AiModel>().eq("model_name", aiModelDto.getModelName()).ne("id", aiModelDto.getId()));
            if (count > 0) {
                return ResultBean.fail("名称重复");
            }

            int pushCount = modelVersionService.count(new QueryWrapper<AiModelVersion>().eq("model_id", aiModelDto.getId()).ne("push_status", 0));
            if (pushCount > 0) {
                return ResultBean.fail("该模型下已有版本发布，不能修改");
            }

            AiModel byId = this.getById(aiModelDto.getId());
            byId.setModelName(aiModelDto.getModelName());
            byId.setDesc(aiModelDto.getDesc());
            byId.setUpdateUser(uvo.getUserName());
            byId.setUpdateTime(LocalDateTime.now());
            this.updateById(byId);
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean remove(List<Long> ids) {
        for (Long id : ids) {
            AiModel byId = this.getById(id);
            int versionCount = modelVersionService.count(new QueryWrapper<AiModelVersion>().eq("model_id", id));
            if (versionCount > 0) {
                return ResultBean.fail("模型【" + byId.getModelName() + "】下已有版本，请先删除版本");
            }
        }

        for (Long id : ids) {
            this.removeById(id);
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean detail(Long id) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        AiModel byId = this.getById(id);
        AiModelVersion version = modelVersionService.getOne(new QueryWrapper<AiModelVersion>().eq("model_id", id).orderByAsc("update_time").last("limit 1"));
        AiModelDto dto = new AiModelDto();
        BeanUtils.copyProperties(byId, dto);
        dto.setModelVersion(version.getModelVersion());
        dto.setModelFile(version.getModelFile());
        dto.setModelType(version.getModelType());

        if(version.getModelType() == 1){
            BASE64Decoder decoder = new BASE64Decoder();
            try {
                dto.setFileName(FileUtil.getName(new String(decoder.decodeBuffer(version.getModelFile()))));
            } catch (IOException e) {
                return ResultBean.fail("解析异常");
            }
        }
        return ResultBean.success(dto);
    }

    @Override
    public ResultBean namespace() {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        return ResultBean.success(aiProjectModelService.list(new QueryWrapper<AiProjectModel>().eq("tenant_id", UmsUtils.getUVO().getTenantId())));
    }

    @Override
    public BasePageResponse<List<AiModel>> page(AiModelPageRequest request) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        UserValueObject uvo = UmsUtils.getUVO();
        request.setTenantId(Long.valueOf(uvo.getTenantId() + ""));
        List<AiModel> list = new ArrayList<>();
        PageHelper.offsetPage(request.getOffset(), request.getPageSize());
        Page<AiModel> page = aiModelMapper.queryPage(request);
        List<AiModel> result = page.getResult();

        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        } else {
            for (AiModel aiModel : result) {
                aiModel.setVersionCnt(modelVersionService.count(new QueryWrapper<AiModelVersion>().eq("model_id", aiModel.getId())));
            }
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), result);
        }
    }

    @Override
    public ResultBean push(AIVersionPushDto dto) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);

        return null;
    }
}
