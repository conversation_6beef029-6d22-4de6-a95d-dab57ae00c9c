package com.idss.datalake.aimodel.service;

import com.idss.datalake.aimodel.dto.AIVersionPushDto;
import com.idss.datalake.aimodel.dto.AiModelDto;
import com.idss.datalake.aimodel.dto.AiModelPageRequest;
import com.idss.datalake.aimodel.dto.AiModelVersionPageRequest;
import com.idss.datalake.aimodel.entity.AiModel;
import com.idss.datalake.aimodel.entity.AiModelVersion;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;

import java.util.List;

/**
 * <p>
 * ai模型版本管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface IAiModelVersionService extends IService<AiModelVersion> {
    ResultBean addOrUpdate(AiModelVersion version);

    ResultBean remove(List<Long> ids);

    ResultBean detail(Long id);

    ResultBean mirror();

    BasePageResponse<List<AiModelVersion>> page(AiModelVersionPageRequest request);
    ResultBean push(AIVersionPushDto dto);
}
