package com.idss.datalake.aimodel.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.idss.datalake.aimodel.dto.AiProjectDeleteDto;
import com.idss.datalake.aimodel.dto.AiProjectPageRequest;
import com.idss.datalake.aimodel.entity.AiModel;
import com.idss.datalake.aimodel.entity.AiNotebook;
import com.idss.datalake.aimodel.entity.AiProjectModel;
import com.idss.datalake.aimodel.mapper.AiProjectModelMapper;
import com.idss.datalake.aimodel.service.IAiModelService;
import com.idss.datalake.aimodel.service.IAiNotebookService;
import com.idss.datalake.aimodel.service.IAiProjectModelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.aimodel.util.AIUtil;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datagovern.metadata.base.BasePageResponse;
import com.idss.radar.datasource.DatasourceType;
import com.idss.radar.util.UmsUtils;
import com.idss.radar.util.UserValueObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * AI项目管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Service
@Slf4j
public class AiProjectModelServiceImpl extends ServiceImpl<AiProjectModelMapper, AiProjectModel> implements IAiProjectModelService {
    @Resource
    private AiProjectModelMapper projectMapper;
    @Autowired
    private IAiNotebookService aiNotebookService;
    @Autowired
    private IAiModelService modelService;

    @Value("${ai.python3-path}")
    private String pythonPath;

    @Value("${ai.script-path}")
    private String scriptPath;

    @Value("${ai.project-create}")
    private String projectCreate;

    @Value("${ai.project-delete}")
    private String projectDelete;

    @Value("${ai.project-update}")
    private String projectUpdate;

    @Override
    public ResultBean addOrUpdate(AiProjectModel project) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        UserValueObject uvo = UmsUtils.getUVO();
        if (project.getId() == null) {
            int count = this.count(new QueryWrapper<AiProjectModel>().eq("name_space", project.getNameSpace()).or().eq("en_name", project.getEnName()));
            if (count > 0) {
                return ResultBean.fail("名称重复");
            }
            project.setCreateUser(uvo.getUserName());
            project.setUpdateUser(uvo.getUserName());

            String params = "{\n" +
                    "  \"namespace\": \"" + project.getEnName() + "\",\n" +
                    "  \"limits\": {\n" +
                    "    \"requests.cpu\": \"" + project.getNeedCpuLimit() + "m\",\n" +
                    "    \"requests.memory\": \"" + project.getNeedMemLimit() + "Mi\",\n" +
                    "    \"limits.cpu\": \"" + project.getCpuLimit() + "m\",\n" +
                    "    \"limits.memory\": \"" + project.getMemLimit() + "Gi\",\n" +
                    "    \"requests.storage\": \"" + project.getStorageLimit() + "Gi\",\n" +
                    "    \"requests.nvidia.com/gpu\": " + project.getGpuLimit() + "\n" +
                    "  },\n" +
                    "  \"ws_storage\": \""+project.getPvcStorage()+"Gi\",\n" +
                    "  \"archive_storage\": \""+project.getPvcArchive()+"Gi\"\n" +
                    "}";

            params = JSON.toJSONString(JSON.parse(params));

            JSONObject json = new JSONObject();
            json.put("path", projectCreate);
            json.put("params", params);
            try {
                String result = AIUtil.runPython(pythonPath, scriptPath, json);
                log.info("AI新增项目返回:{}", result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == 200) {
                    project.setStatus(0);
                } else {
                    project.setStatus(2);
                    project.setErrorMessage(jsonObject.getString("message"));
                }
                this.save(project);
            } catch (Exception e) {
                log.error("报错错误", e);
                return ResultBean.fail("报错错误" + e.getMessage());
            }
        } else {
            /*int count = this.count(new QueryWrapper<AiProject>().ne("id", project.getId()).eq("name_space", project.getNameSpace()).or().eq("en_name", project.getEnName()));
            if (count > 0) {
                return ResultBean.fail("名称重复");
            }*/

            String params = "{\n" +
                    "  \"namespace\": \"" + project.getEnName() + "\",\n" +
                    "  \"limits\": {\n" +
                    "    \"requests.cpu\": \"" + project.getNeedCpuLimit() + "m\",\n" +
                    "    \"requests.memory\": \"" + project.getNeedMemLimit() + "Mi\",\n" +
                    "    \"limits.cpu\": \"" + project.getCpuLimit() + "m\",\n" +
                    "    \"limits.memory\": \"" + project.getMemLimit() + "Gi\",\n" +
                    "    \"requests.storage\": \"" + project.getStorageLimit() + "Gi\",\n" +
                    "    \"requests.nvidia.com/gpu\": " + project.getGpuLimit() + "\n" +
                    "  },\n" +
                    "  \"ws_storage\": \""+project.getPvcStorage()+"Gi\",\n" +
                    "  \"archive_storage\": \""+project.getPvcArchive()+"Gi\"\n" +
                    "}";
            params = JSON.toJSONString(JSON.parse(params));
            JSONObject json = new JSONObject();
            json.put("path", projectUpdate);
            json.put("params", params);
            try {
                String result = AIUtil.runPython(pythonPath, scriptPath, json);
                log.info("AI更新项目返回:{}", result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == 200) {
                    project.setStatus(0);
                } else {
                    project.setStatus(2);
                    project.setErrorMessage(jsonObject.getString("message"));
                }
                project.setUpdateTime(LocalDateTime.now());
                this.updateById(project);
            } catch (Exception e) {
                log.error("报错错误", e);
                return ResultBean.fail("报错错误" + e.getMessage());
            }
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean remove(AiProjectDeleteDto dto) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        if (CollectionUtils.isNotEmpty(dto.getIds())) {
            for (Long id : dto.getIds()) {
                AiProjectModel byId = this.getById(id);
                int cnt = aiNotebookService.count(new QueryWrapper<AiNotebook>().eq("project_id", id));
                if (cnt > 0) {
                    return ResultBean.fail(byId.getNameSpace() + "已被NoteBook使用不能删除");
                }
                int projectId = modelService.count(new QueryWrapper<AiModel>().eq("project_id", id));
                if (projectId > 0) {
                    return ResultBean.fail(byId.getNameSpace() + "已被模型使用不能删除");
                }
            }
            for (Long id : dto.getIds()) {
                AiProjectModel byId = this.getById(id);
                String params = "{\n" +
                        "  \"namespace\": \"" + byId.getEnName() + "\", \n" +
                        "  \"delete_pv\": "+dto.getDeletePv()+"\n" +
                        "}";
                params = JSON.toJSONString(JSON.parse(params));
                JSONObject json = new JSONObject();
                json.put("path", projectDelete);
                json.put("params", params);
                try {
                    String result = AIUtil.runPython(pythonPath, scriptPath, json);
                    log.info("AI删除项目返回:{}", result);
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == 200) {
                        this.removeById(id);
                    } else {
                        return ResultBean.fail(byId.getNameSpace() + "删除失败,原因:", jsonObject.getString("message"));
                    }
                } catch (Exception e) {
                    log.error("报错错误", e);
                    return ResultBean.fail("报错错误" + e.getMessage());
                }
            }
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean detail(Long id) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        return ResultBean.success(this.getById(id));
    }

    @Override
    public BasePageResponse<List<AiProjectModel>> page(AiProjectPageRequest request) {
        DatasourceType.setDataBaseType(DatasourceType.DataBaseType.DEFAULT);
        UserValueObject uvo = UmsUtils.getUVO();
        request.setUserName(uvo.getUserName());
        List<AiProjectModel> list = new ArrayList<>();
        PageHelper.offsetPage(request.getOffset(), request.getPageSize());
        Page<AiProjectModel> page = projectMapper.queryPage(request);
        List<AiProjectModel> result = page.getResult();
        if (page.isEmpty()) {
            return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
        return new BasePageResponse<>(page.getPages(), page.getPageNum(), page.getPageSize(), page.getTotal(), result);
    }
}
