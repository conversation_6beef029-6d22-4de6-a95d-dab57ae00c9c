package com.idss.datalake.aimodel.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.aimodel.dto.AiMirrorPageRequest;
import com.idss.datalake.aimodel.dto.AiNotebookPageRequest;
import com.idss.datalake.aimodel.entity.AiMirror;
import com.idss.datalake.aimodel.entity.AiNotebook;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * ai_notebook Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
public interface AiNotebookMapper extends BaseMapper<AiNotebook> {
    Page<AiNotebook> queryPage(AiNotebookPageRequest requestDto);
}
