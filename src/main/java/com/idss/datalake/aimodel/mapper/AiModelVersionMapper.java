package com.idss.datalake.aimodel.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.aimodel.dto.AiModelPageRequest;
import com.idss.datalake.aimodel.dto.AiModelVersionPageRequest;
import com.idss.datalake.aimodel.dto.AiNotebookPageRequest;
import com.idss.datalake.aimodel.entity.AiModel;
import com.idss.datalake.aimodel.entity.AiModelVersion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.aimodel.entity.AiNotebook;

/**
 * <p>
 * ai模型版本管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface AiModelVersionMapper extends BaseMapper<AiModelVersion> {
    Page<AiModelVersion> queryPage(AiModelVersionPageRequest requestDto);
}
