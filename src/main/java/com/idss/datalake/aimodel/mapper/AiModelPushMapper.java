package com.idss.datalake.aimodel.mapper;

import com.github.pagehelper.Page;
import com.idss.datalake.aimodel.dto.AiModelPageRequest;
import com.idss.datalake.aimodel.dto.AiPushPageRequest;
import com.idss.datalake.aimodel.dto.AiPushPageVo;
import com.idss.datalake.aimodel.entity.AiModel;
import com.idss.datalake.aimodel.entity.AiModelPush;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * ai模型推送 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface AiModelPushMapper extends BaseMapper<AiModelPush> {
    Page<AiPushPageVo> queryPage(AiPushPageRequest requestDto);
}
