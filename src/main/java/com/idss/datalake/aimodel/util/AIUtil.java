/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/8/8
 * create 注释
 *-----------------------------------------------------------------------------*
 * ,xiexiaofei,2024/8/8
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.aimodel.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.util.List;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see
 * @since 2024/8/8
 */
@Slf4j
public class AIUtil {
    public static String runPython(String pythonPath, String scriptPath, JSONObject json) throws Exception {
//        String pythonPath = "/Users/<USER>/anaconda3/envs/trade/bin/python";
        String path = json.getString("path");
        String params = json.getString("params");
        String[] cmd = new String[]{
                pythonPath,
                scriptPath,
                "--path",
                path ,
                "--params",
                params
        };
        // 创建进程
        ProcessBuilder pb = new ProcessBuilder(cmd);
        List<String> command = pb.command();
        log.info("执行命令:{}",String.join(" ",command));

        Process process = pb.start();

        // 读取Python脚本的输出
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        StringBuilder output = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            output.append(line);
        }
        log.info("正常输出:{}",output);

        // 捕获错误输出
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        StringBuilder errorOutput = new StringBuilder();
        String errorLine;
        while ((errorLine = errorReader.readLine()) != null) {
            errorOutput.append(errorLine);
        }
        log.info("错误输出:{}",errorOutput);


        // 等待进程结束
        int exitCode = process.waitFor();
        if (exitCode == 0) {
            // 解析输出的JSON格式预测值
            return output.toString();
        } else {
            log.error("Error: Python script execution failed with exit code " + exitCode);
            log.error("Error Output: " + errorOutput);

            JSONObject errorJSON = new JSONObject();
            errorJSON.put("code",500);
            errorJSON.put("message",errorOutput.toString());
            return errorJSON.toJSONString();
        }
    }
}
