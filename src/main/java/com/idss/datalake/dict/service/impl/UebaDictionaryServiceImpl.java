package com.idss.datalake.dict.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.dict.bean.TreeDictResp;
import com.idss.datalake.dict.bean.UebaDictionary;
import com.idss.datalake.dict.mapper.UebaDictionaryMapper;
import com.idss.datalake.dict.service.IUebaDictionaryService;
import com.idss.radar.datasource.DatasourceType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-25
 */
@Service
public class UebaDictionaryServiceImpl extends ServiceImpl<UebaDictionaryMapper, UebaDictionary> implements IUebaDictionaryService {

    /**
     * 根据字典类型获取字典值
     * @param type 字典类型
     * @return
     */
    @Override
    public Map<String, String> getDictMapByType(String type) {
        List<UebaDictionary> dictionaries = this.list(new QueryWrapper<UebaDictionary>()
                .eq("type", type)
                .eq("enable",1)
                .orderByAsc("sortno"));

        Map<String, String> resultMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(dictionaries)) {
            for(UebaDictionary dictionary : dictionaries) {
                resultMap.put(dictionary.getKeyCode(), dictionary.getValue());
            }
        }
        return resultMap;
    }

    @Override
    public Map<String, String> getDictMapByType(String type, int tenantId) {
        DatasourceType.setTenantDataBaseType(DatasourceType.DataBaseType.DEFAULT, tenantId);
        List<UebaDictionary> dictionaries = this.list(new QueryWrapper<UebaDictionary>()
                .eq("type", type)
                .eq("enable",1)
                .orderByAsc("sortno"));

        Map<String, String> resultMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(dictionaries)) {
            for(UebaDictionary dictionary : dictionaries) {
                resultMap.put(dictionary.getKeyCode(), dictionary.getValue());
            }
        }
        return resultMap;
    }

    /**
     * 根据字段类型获取字典值(以List形式存储)
     * @param type
     * @return
     */
    @Override
    public List<UebaDictionary> getDictListByType(String type) {
        List<UebaDictionary> dictionaries = this.list(new QueryWrapper<UebaDictionary>()
                .eq("type", type)
                .eq("enable",1)
                .orderByAsc("sortno"));
        if(!CollectionUtils.isEmpty(dictionaries)) {
            return dictionaries;
        } else {
            return new ArrayList<UebaDictionary>();
        }
    }

    @Override
    public UebaDictionary getDictByCode(String type, String code) {
        List<UebaDictionary> dictionaries = this.list(new QueryWrapper<UebaDictionary>()
                .eq("type", type)
                .eq("enable",1)
                .eq("key_code", code));
        if(!CollectionUtils.isEmpty(dictionaries)) {
            return dictionaries.get(0);
        } else {
            return new UebaDictionary();
        }
    }

    //region Description 根据类型集合，查询字典
    /**
     * 获取树状字典
     *
     * @return
     */
    @Override
    public List<JSONObject> getTreeDictByTypes(String[] types) {
        if (types == null || types.length == 0) {
            throw new RuntimeException("参数错误");
        }
        List<JSONObject> jsonObjectList = new ArrayList<>();
        // 查询字典数据
        List<String> typeList = Arrays.asList(types);

        QueryWrapper<UebaDictionary> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("type", typeList).orderByAsc("sortno");
        List<UebaDictionary> dictList = this.getBaseMapper().selectList(queryWrapper);
        Map<String, List<UebaDictionary>> dataMap = dictList.stream().collect(Collectors.groupingBy(x -> x.getType()));
        for (String type : dataMap.keySet()) {
            List<UebaDictionary> oneTypeList = dataMap.get(type);
            List<TreeDictResp> respList = new ArrayList<>();
            for (UebaDictionary dict : oneTypeList) {
                TreeDictResp resp = new TreeDictResp();
                resp.setId(dict.getKeyCode());
                resp.setParentId(dict.getParentId());
                resp.setLabel(dict.getValue());

                respList.add(resp);
            }
            JSONObject json = new JSONObject();
            List<TreeDictResp> oneTypeRespList = buildTree(respList);
            json.put("type", type);
            json.put("dicts", oneTypeRespList);
            jsonObjectList.add(json);

        }

        return jsonObjectList;
    }

    /**
     * 获取通用字典
     *
     * @param types
     * @return
     */
    @Override
    public List<JSONObject> getDictByTypes(String[] types) {
        if (types == null || types.length == 0) {
            throw new RuntimeException("参数错误");
        }
        List<JSONObject> ret = new ArrayList<>();
        // 查询字典数据
        List<String> typeList = Arrays.asList(types);
        QueryWrapper<UebaDictionary> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("type", typeList).orderByAsc("sortno");
        List<UebaDictionary> dictList = this.getBaseMapper().selectList(queryWrapper);

        // 组装格式
        Map<String, List<UebaDictionary>> dictMap = dictList.stream().collect(Collectors.groupingBy(UebaDictionary::getType));
        for (Map.Entry<String, List<UebaDictionary>> entry : dictMap.entrySet()) {
            JSONObject json = new JSONObject();
            json.put("type", entry.getKey());

            List<UebaDictionary> oneTypeDictList = entry.getValue();
            List<JSONObject> oneTypeJsonList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(oneTypeDictList)) {
                for (UebaDictionary dict : oneTypeDictList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("key", dict.getKeyCode());
                    jsonObject.put("value", dict.getValue());
                    oneTypeJsonList.add(jsonObject);
                }
            }

            json.put("dicts", oneTypeJsonList);
            ret.add(json);
        }
        return ret;
    }

    /**
     * 树结构封装
     *
     * @param treeDictRespList
     * @return
     */
    public List<TreeDictResp> buildTree(List<TreeDictResp> treeDictRespList) {
        List<TreeDictResp> retList = new ArrayList<>();

        List<TreeDictResp> rootList = treeDictRespList.stream().filter(item ->
                StringUtils.isEmpty(item.getParentId())).collect(toList());
        for (TreeDictResp node : rootList) {
            node = buildChildTree(node, treeDictRespList);
            retList.add(node);
        }
        return retList;

    }


    /**
     * 树结构封装
     *
     * @param pNode
     * @param treeTypeList
     * @return
     */
    private TreeDictResp buildChildTree(TreeDictResp pNode, List<TreeDictResp> treeTypeList) {
        List<TreeDictResp> childTypes = new ArrayList<>();
        for (TreeDictResp node : treeTypeList) {
            if (node.getParentId() != null && node.getParentId().equals(pNode.getId())) {
                childTypes.add(buildChildTree(node, treeTypeList));
            }
        }
        pNode.setChildren(childTypes.size() == 0 ? null : childTypes);
        return pNode;

    }

    //endregion
}
