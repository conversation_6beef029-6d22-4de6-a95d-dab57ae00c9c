package com.idss.datalake.dict.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-25
 */
@Data
@Accessors(chain = true)
public class UebaDictionary {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 字典编码
	 */
	private String keyCode;

	/**
	 * 字典值
	 */
	private String value;

	/**
	 * 是否可用 (0-不可用 1-可用)
	 */
	private Integer enable;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 排序
	 */
	private Integer sortno;

	/**
	 * 字典类型
	 */
	private String type;

	/**
	 * 字典父节点
	 */
	private String parentId;

	@TableField(exist = false)
	private String status;

	@TableField(exist = false)
	private String parentValue;
}
