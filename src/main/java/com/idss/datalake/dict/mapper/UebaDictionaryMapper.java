
/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
** Product VERSION,UPDATED BY,UPDATE DATE                                     *
*   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* create 注释
*-----------------------------------------------------------------------------*
* V3.0.11,idss,2021-06-24
* M  {modifyComment}
* -  {delComment}
* +  {addCommnet}
\*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.dict.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.dict.bean.UebaDictionary;
import org.apache.ibatis.annotations.Mapper;


/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-25
 */
@Mapper
public interface UebaDictionaryMapper extends BaseMapper<UebaDictionary> {
}
