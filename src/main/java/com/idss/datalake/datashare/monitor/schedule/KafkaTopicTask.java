package com.idss.datalake.datashare.monitor.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.redis.RedissonLockUtil;
import com.idss.datalake.common.util.KafkaUtil;
import com.idss.datalake.common.util.StringUtil;
import com.idss.datalake.datashare.cluster.entity.TbCluster;
import com.idss.datalake.datashare.cluster.service.ITbClusterService;
import com.idss.datalake.datashare.monitor.entity.TbClusterKafkaTopic;
import com.idss.datalake.datashare.monitor.service.ITbClusterKafkaTopicService;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.datashare.tenant.entity.TbTenantCluster;
import com.idss.datalake.datashare.tenant.mapper.TbTenantMapper;
import com.idss.datalake.datashare.tenant.service.ITbTenantClusterService;
import com.idss.radar.datasource.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description <p>集群管理 > Kafka详情统计任务</p>
 * @see
 * @since 2022-10-22
 */
@Component
@Slf4j
public class KafkaTopicTask {
    private static final String LOCK_KEY_PRE = "DATA_LAKE_KAFKA_TOPIC_KEY_";
    private static final Logger logger = LoggerFactory.getLogger(KafkaTopicTask.class);

    @Resource
    private ITbClusterKafkaTopicService kafkaTopicService;
    @Resource
    private ITbClusterService clusterService;
    @Resource
    private TbTenantMapper tenantMapper;
    @Resource
    private ITbTenantClusterService tenantClusterService;

    @Scheduled(initialDelay = 5000, fixedDelay = 3600000)
    public void doStat() {
        try {
            if (RedissonLockUtil.tryLock(LOCK_KEY_PRE)) {
                Thread.sleep(3000L);


                logger.info("统计Kafka详情开始");
                try {
                    // kafka集群信息
                    List<TbCluster> clusters = clusterService.list(new QueryWrapper<TbCluster>().eq("cluster_type", "kafka")
                            .eq("del_flag", "0"));
                    if (CollectionUtils.isEmpty(clusters)) {
                        return;
                    }
                    QueryWrapper<TbTenantCluster> queryWrapper = new QueryWrapper<TbTenantCluster>().eq("cluster_type", "kafka");
                    List<TbTenantCluster> tenantClusters = tenantClusterService.list(queryWrapper);
                    //租户id-名称对应map
                    final List<Map<String, Object>> list = this.tenantMapper.selectMaps(new QueryWrapper<TbTenant>()
                            .select("id", "tenant_name as name").eq("del_flag", Constant.DEL_FLAG_UNABLE));
                    Map<Object, String> tenant = new HashMap<>();
                    for (Map map : list) {
                        tenant.put(map.get("id"), String.valueOf(map.get("name")));
                    }
                    Map<String, Long> topicTenantIdMap = tenantClusters.stream().collect(Collectors.toMap(TbTenantCluster::getTopic,
                            TbTenantCluster::getTenantId));
                    List<TbClusterKafkaTopic> kafkaTopics = new ArrayList<>();
                    for (TbCluster cluster : clusters) {
                        // kafka broker 信息
                        String url = cluster.getNodeAddress();
                        logger.info("正在统计地址：{}", url);
                        AdminClient adminClient = KafkaUtil.getClient(url);
                        //topic相关信息
                        List<String> topics = KafkaUtil.topics(adminClient);
                        //获取所有topic积压信息
                        Map<String, Long> lags = KafkaUtil.getLags(adminClient, url, topics);
                        //新建消费者、连接
                        KafkaConsumer<String, String> consumer = KafkaUtil.createConsumer(url, "data-lake");
                        try {
                            List<String> groups1 = KafkaUtil.getGroups(adminClient);
                            Map<String, TopicDescription> topicDescriptionMap = KafkaUtil.topicPartitionAndReplicas(adminClient, topics);
                            for (String topic : topics) {
                                Integer active = KafkaUtil.getActiveBroker(consumer, topic);
                                Integer groups = KafkaUtil.getTopicGroups(groups1, topic, url);
                                TbClusterKafkaTopic kafkaTopic = new TbClusterKafkaTopic();
                                kafkaTopic.setClusterId(cluster.getId());
                                kafkaTopic.setTopic(topic);
                                // 分区，分片，积压量
                                TopicDescription topicDescription = topicDescriptionMap.get(topic);
                                kafkaTopic.setPartitions(topicDescription.partitions().size());
                                kafkaTopic.setReplicas(topicDescription.partitions().get(0).replicas().size());
                                kafkaTopic.setLags(StringUtil.isNotEmpty(lags.get(topic)) ? lags.get(topic) : 0L);
                                kafkaTopic.setBrokers(url.split(",").length);
                                kafkaTopic.setSpread(String.format("%.1f", 100.0 * active / url.split(",").length) + "%");
                                kafkaTopic.setGroups(groups);
                                kafkaTopic.setFlowId("");
                                kafkaTopic.setDataName("");
                                kafkaTopic.setTenantName(tenant.get(topicTenantIdMap.get(topic)));
                                kafkaTopic.setCreateTime(LocalDateTime.now());

                                kafkaTopics.add(kafkaTopic);
                            }
                        } catch (InterruptedException | ExecutionException e) {
                            logger.error("getTopic error,{}", e.getMessage(), e);
                        } finally {
                            KafkaUtil.close(adminClient);
                            consumer.close();
                        }
                    }
                    if (CollectionUtils.isNotEmpty(kafkaTopics)) {
                        kafkaTopicService.update(new UpdateWrapper<TbClusterKafkaTopic>().set("is_deleted", 1).eq("is_deleted", 0));
                        kafkaTopicService.saveBatch(kafkaTopics);
                    }
                    logger.info("统计Kafka详情结束");
                } catch (Exception e) {
                    log.error("Kafka详情统计任务失败: {}", e.getMessage(), e);
                }

            }
        } catch (Exception e) {
            log.error("DATA_LAKE_KAFKA_TOPIC_KEY_执行失败:", e);
        } finally {
            if (RedissonLockUtil.isLocked(LOCK_KEY_PRE)) {
                RedissonLockUtil.unlock(LOCK_KEY_PRE);
            }
            DatasourceType.clearDataBaseType();
        }

    }


}
