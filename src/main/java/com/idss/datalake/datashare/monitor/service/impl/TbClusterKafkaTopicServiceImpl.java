package com.idss.datalake.datashare.monitor.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datashare.monitor.entity.TbClusterKafkaTopic;
import com.idss.datalake.datashare.monitor.mapper.TbClusterKafkaTopicMapper;
import com.idss.datalake.datashare.monitor.service.ITbClusterKafkaTopicService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 集群管理-kafka详情-定时统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Service
public class TbClusterKafkaTopicServiceImpl extends ServiceImpl<TbClusterKafkaTopicMapper, TbClusterKafkaTopic> implements ITbClusterKafkaTopicService {

}
