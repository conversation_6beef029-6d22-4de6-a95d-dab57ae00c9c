package com.idss.datalake.datashare.monitor.controller;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datashare.monitor.entity.LagsDTO;
import com.idss.datalake.datashare.monitor.entity.TopicDTO;
import com.idss.datalake.datashare.monitor.service.KafkaMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * KafkaMonitor
 * </p>
 *
 * <AUTHOR>
 * @since 2022/8/2 0002
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/monitor-kafka")
public class KafkaMonitorController {

    @Autowired
    private KafkaMonitorService monitorService;


    @GetMapping("/{id}")
    public ResultBean kafka(@PathVariable("id") String id) {
        return this.monitorService.getKafka(Long.valueOf(id));
    }

    @PostMapping("/{id}/topic")
    public ResultBean topic(@PathVariable("id") String id, @RequestBody TopicDTO dto) {
        return this.monitorService.getTopic(Long.valueOf(id),dto);
    }

    @PostMapping("/{id}/lags")
    public ResultBean lags(@PathVariable("id") String id, @RequestBody LagsDTO lagsDTO) {
        return this.monitorService.getLags(Long.valueOf(id),lagsDTO);
    }

    @GetMapping("/{id}/topics")
    public ResultBean alltopic(@PathVariable("id") String id) {
        return this.monitorService.getTopics(Long.valueOf(id));
    }

}
