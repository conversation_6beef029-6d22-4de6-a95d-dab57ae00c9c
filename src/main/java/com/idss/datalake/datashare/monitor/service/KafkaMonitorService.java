package com.idss.datalake.datashare.monitor.service;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datashare.monitor.entity.LagsDTO;
import com.idss.datalake.datashare.monitor.entity.TopicDTO;

/**
 * <p>
 * kafka监控
 * </p>
 *
 * <AUTHOR>
 * @since 2022/8/2 0002
 */

public interface KafkaMonitorService {


    ResultBean getKafka(Long id);

    ResultBean getTopic(Long id, TopicDTO dto);

    ResultBean getLags(Long id, LagsDTO lagsDTO);

    ResultBean getTopics(Long id);
}
