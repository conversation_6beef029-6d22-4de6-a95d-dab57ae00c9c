package com.idss.datalake.datashare.zeppelin.enums;

/**
 * <AUTHOR>
 * @description : <p>不同数据源驱动类枚举类</p>
 * @see：
 * @since 2022/7/29
 */
public enum DriveClassEnum {
    clickhouse("ru.yandex.clickhouse.ClickHouseDriver"),
    mysql("com.mysql.cj.jdbc.Driver"),
    hive("org.apache.hive.jdbc.HiveDriver"),
    presto("com.facebook.presto.jdbc.PrestoDriver"),
    kafka("");

    DriveClassEnum(String driveClassName) {
        this.driveClassName = driveClassName;
    }

    public String getDriveClassName() {
        return driveClassName;
    }

    public void setDriveClassName(String driveClassName) {
        this.driveClassName = driveClassName;
    }

    private String driveClassName;
}
