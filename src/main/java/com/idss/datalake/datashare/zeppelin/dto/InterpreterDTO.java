/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-07-15
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-07-15
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.zeppelin.dto;

import com.idss.datalake.datashare.zeppelin.entity.ZeppelinInterpreter;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p> dto类</p>
 * @see InterpreterDTO
 * @since 2022-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class InterpreterDTO extends ZeppelinInterpreter {

    private List<Integer> ids;
}
