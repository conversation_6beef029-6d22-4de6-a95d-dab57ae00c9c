package com.idss.datalake.datashare.zeppelin.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.datalake.datashare.zeppelin.dto.NotebookDTO;
import com.idss.datalake.datashare.zeppelin.entity.ZeppelinNotebook;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-15
 */
public interface TenantNotebookMapper extends BaseMapper<ZeppelinNotebook> {

    @Select("SELECT tn.*,ti.interpreter_alias ,ti.interpreter_type FROM zeppelin_notebook tn " +
            "inner join zeppelin_interpreter ti on tn.interpreter_id = ti.id ${ew.customSqlSegment}")
    IPage<NotebookDTO> getPage(Page<ZeppelinNotebook> page, @Param(Constants.WRAPPER) QueryWrapper<ZeppelinNotebook> wrapper);
}
