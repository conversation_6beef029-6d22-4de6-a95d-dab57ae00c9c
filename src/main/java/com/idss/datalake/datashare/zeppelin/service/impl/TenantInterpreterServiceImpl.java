package com.idss.datalake.datashare.zeppelin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datashare.zeppelin.entity.ZeppelinInterpreter;
import com.idss.datalake.datashare.zeppelin.mapper.TenantInterpreterMapper;
import com.idss.datalake.datashare.zeppelin.service.ITenantInterpreterService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-15
 */
@Service
public class TenantInterpreterServiceImpl extends ServiceImpl<TenantInterpreterMapper, ZeppelinInterpreter> implements ITenantInterpreterService {

}
