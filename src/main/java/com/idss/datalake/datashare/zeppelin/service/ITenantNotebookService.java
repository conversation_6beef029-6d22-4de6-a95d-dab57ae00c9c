package com.idss.datalake.datashare.zeppelin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datashare.zeppelin.dto.NotebookDTO;
import com.idss.datalake.datashare.zeppelin.entity.ZeppelinNotebook;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-15
 */
public interface ITenantNotebookService extends IService<ZeppelinNotebook> {
    /**
     * 分页查询
     *
     * @param page
     * @param wrapper
     * @return
     */
    IPage<NotebookDTO> getPage(Page<ZeppelinNotebook> page, QueryWrapper<ZeppelinNotebook> wrapper);
}
