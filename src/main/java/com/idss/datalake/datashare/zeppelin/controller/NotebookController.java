/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-07-15
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-07-15
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.zeppelin.controller;


import com.alibaba.fastjson.JSONObject;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.syslog.annotation.SysLog;
import com.idss.datalake.common.syslog.entity.OptModule;
import com.idss.datalake.common.syslog.entity.OptType;
import com.idss.datalake.datashare.zeppelin.dto.NotebookDTO;
import com.idss.datalake.datashare.zeppelin.manager.TenantNotebookManager;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description <p> 前端控制器</p>
 * @see NotebookController
 * @since 2022-07-15
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/notebook")
public class NotebookController {
    private static final Logger logger = LoggerFactory.getLogger(NotebookController.class);

    @Autowired
    private TenantNotebookManager notebookManager;

    @ApiOperation(value = "分页查询列表")
    @PostMapping(value = "/page")
    @SysLog(logName = "分页查询", optType = OptType.QUERY, optModule = OptModule.ZEPPELIN_NOTEBOOK, switchRedisStatus = true)
    public ResultBean page(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(notebookManager.page(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "List of the notes")
    @GetMapping()
    public ResultBean list() {
        try {
            return ResultBean.success(notebookManager.list());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "Get an existing note information")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Integer id) {
        try {
            return ResultBean.success(notebookManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "Create a new note")
    @PostMapping(value = "/add")
    @SysLog(logName = "新增Notebook", optType = OptType.ADD, optModule = OptModule.ZEPPELIN_NOTEBOOK, switchRedisStatus = true)
    public ResultBean create(@RequestBody JSONObject noteDTO) {
        try {
            return ResultBean.success(notebookManager.create(noteDTO));
        } catch (Exception e) {
            logger.error("创建note失败,{}", e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "Rename a note")
    @PutMapping(value = "/{id}/rename")
    @SysLog(logName = "编辑Notebook", optType = OptType.EDIT, optModule = OptModule.ZEPPELIN_NOTEBOOK, switchRedisStatus = true)
    public ResultBean rename(@PathVariable Integer id, @RequestBody JSONObject noteDTO) {
        try {
            notebookManager.rename(id, noteDTO);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error("更新note失败,{}", e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "Delete notes")
    @PostMapping(value = "/delete")
    @SysLog(logName = "删除Notebook", optType = OptType.DELETE, optModule = OptModule.ZEPPELIN_NOTEBOOK, switchRedisStatus = true)
    public ResultBean delete(@RequestBody NotebookDTO notebookDTO) {
        try {
            notebookManager.delete(notebookDTO.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error("删除note失败,{}", e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "exist a note alias")
    @PostMapping(value = "/exist")
    public ResultBean exist(@RequestParam("noteAlias") String noteAlias) {
        try {
            return ResultBean.success(notebookManager.exist(noteAlias));
        } catch (Exception e) {
            logger.error("exist note,{}", e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }
}
