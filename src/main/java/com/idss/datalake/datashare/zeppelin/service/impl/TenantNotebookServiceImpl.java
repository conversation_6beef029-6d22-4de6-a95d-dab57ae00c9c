package com.idss.datalake.datashare.zeppelin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datashare.zeppelin.dto.NotebookDTO;
import com.idss.datalake.datashare.zeppelin.entity.ZeppelinNotebook;
import com.idss.datalake.datashare.zeppelin.mapper.TenantNotebookMapper;
import com.idss.datalake.datashare.zeppelin.service.ITenantNotebookService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-15
 */
@Service
public class TenantNotebookServiceImpl extends ServiceImpl<TenantNotebookMapper, ZeppelinNotebook> implements ITenantNotebookService {

    @Resource
    private TenantNotebookMapper tenantNotebookMapper;

    @Override
    public IPage<NotebookDTO> getPage(Page<ZeppelinNotebook> page, QueryWrapper<ZeppelinNotebook> wrapper) {
        return tenantNotebookMapper.getPage(page, wrapper);
    }
}
