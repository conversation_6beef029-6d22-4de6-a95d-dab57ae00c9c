/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-07-15
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-07-15
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.zeppelin.manager;

import com.alibaba.fastjson.JSON;
import com.idss.datalake.common.util.HttpUtils;
import com.idss.datalake.datashare.zeppelin.constant.UrlConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description <p>paragraph处理类</p>
 * @see ParagraphManager
 * @since 2022-07-28
 */
@Component
public class ParagraphManager extends AbstractManager {
    private static final Logger logger = LoggerFactory.getLogger(ParagraphManager.class);

    public void createNewParagraph(String noteId) {
        Map<String, String> params = new HashMap<>();
        params.put("title", "");
        params.put("text", "\n");
        String result = HttpUtils.sendPost(String.format(UrlConstant.PARAGRAPH_CREATE_POST, noteId), JSON.toJSONString(params));
        check(result);
    }
}