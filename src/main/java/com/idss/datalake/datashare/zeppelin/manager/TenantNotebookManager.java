/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-07-15
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-07-15
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.zeppelin.manager;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.datalake.common.util.HttpUtils;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.common.util.UuidUtil;
import com.idss.datalake.datashare.tenant.enums.ServerStatisticType;
import com.idss.datalake.datashare.tenant.service.IQuaServerUsedStatisticService;
import com.idss.datalake.datashare.zeppelin.constant.UrlConstant;
import com.idss.datalake.datashare.zeppelin.dto.NotebookDTO;
import com.idss.datalake.datashare.zeppelin.entity.ZeppelinInterpreter;
import com.idss.datalake.datashare.zeppelin.entity.ZeppelinNotebook;
import com.idss.datalake.datashare.zeppelin.service.ITenantInterpreterService;
import com.idss.datalake.datashare.zeppelin.service.ITenantNotebookService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p> manager处理类</p>
 * @see TenantNotebookManager
 * @since 2022-07-15
 */
@Component
public class TenantNotebookManager extends AbstractManager {
    private static final Logger logger = LoggerFactory.getLogger(TenantNotebookManager.class);

    @Autowired
    private ITenantNotebookService notebookService;
    @Autowired
    private ITenantInterpreterService interpreterService;
    @Autowired
    private ParagraphManager paragraphManager;
    @Resource
    IQuaServerUsedStatisticService serverUsedStatisticService;

    /**
     * 分页
     *
     * @param requestDTO
     * @return
     */
    public Map<String, Object> page(RequestDTO requestDTO) throws ParamInvalidException {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<ZeppelinNotebook> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<ZeppelinNotebook> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tn.tenant_id", UserUtil.getCurrentTenantId());
        if (requestDTO.getParam() != null) {
            NotebookDTO notebookDTO = JSONUtil.objectMapperToObj(requestDTO.getParam(), NotebookDTO.class);
            if (StringUtils.isNotBlank(notebookDTO.getNoteAlias())) {
                queryWrapper.like("tn.note_alias", notebookDTO.getNoteAlias());
            }
            if (StringUtils.isNotBlank(notebookDTO.getInterpreterType())) {
                queryWrapper.like("ti.interpreter_type", notebookDTO.getInterpreterType());
            }
        }
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    "tn." + CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }

        IPage<NotebookDTO> pageResult = notebookService.getPage(page, queryWrapper);

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", pageResult.getRecords());
        return result;
    }

    public Map<String, Object> createPageCondition(RequestDTO requestDTO) throws ParamInvalidException {
        if (requestDTO != null && requestDTO.getGlobal() != null) {
            GlobalDTO globalDTO = requestDTO.getGlobal();
            if (globalDTO.getPageNum() == null) {
                globalDTO.setPageNum(Constant.DEFAULT_PAGE_NUM);
            }
            if (globalDTO.getPageSize() == null) {
                globalDTO.setPageSize(Constant.DEFAULT_PAGE_SIZE);
            }
            if (StringUtils.isEmpty(globalDTO.getOrderType())) {
                globalDTO.setOrderType("desc");
            }
            if (StringUtils.isEmpty(globalDTO.getOrderField())) {
                globalDTO.setOrderField("createTime");
            }
        } else {
            throw new ParamInvalidException("入参错误");
        }
        return null;
    }

    public Object list() throws Exception {
        // 查询当前租户是否已创建notebook，未创建直接返回
        int tenantId = UserUtil.getCurrentTenantId();
        List<ZeppelinNotebook> currentZeppelinNotebooks = notebookService.list(new QueryWrapper<ZeppelinNotebook>().eq("tenant_id", tenantId));
        if (CollectionUtils.isEmpty(currentZeppelinNotebooks)) {
            return null;
        }

        String allNotebooks = HttpUtils.sendGet(UrlConstant.NOTEBOOK_LIST_GET);
        Map<String, ZeppelinNotebook> currentMap = currentZeppelinNotebooks.stream().collect(Collectors.toMap(ZeppelinNotebook::getNoteRemoteId,
                Function.identity()));
        JSONArray currentResult = new JSONArray();
        JSONArray body = getBodyArray(allNotebooks);
        for (int i = 0; i < body.size(); i++) {
            JSONObject note = body.getJSONObject(i);
            if (currentMap.containsKey(note.getString("id"))) {
                ZeppelinNotebook ZeppelinNotebook = currentMap.get(note.getString("id"));
                addFields(note, ZeppelinNotebook);
                currentResult.add(note);
            }
        }
        return currentResult;
    }

    /**
     * 增加额外字段
     *
     * @param notebook
     * @param dbNotebook
     */
    private void addFields(JSONObject notebook, ZeppelinNotebook dbNotebook) {
        notebook.put("createUser", dbNotebook.getCreateUser());
        notebook.put("createTime", dbNotebook.getCreateTime());
        notebook.put("updateUser", dbNotebook.getUpdateUser());
        notebook.put("updateTime", dbNotebook.getUpdateTime());
        notebook.put("noteAlias", dbNotebook.getNoteAlias());
        notebook.put("interpreterId", dbNotebook.getInterpreterId());
    }

    public Object detail(Integer id) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        int tenantId = UserUtil.getCurrentTenantId();
        QueryWrapper<ZeppelinNotebook> wrapper = new QueryWrapper();
        wrapper.eq("tenant_id", tenantId).eq("id", id);
        ZeppelinNotebook one = notebookService.getOne(wrapper);
        if (one == null) {
            throw new ParamInvalidException("note不存在");
        }
        String remoteNote = HttpUtils.sendGet(String.format(UrlConstant.NOTEBOOK_EXISTING_NOTE_GET, one.getNoteRemoteId()));
        JSONObject bodyObj = getBodyObj(remoteNote);
        addFields(bodyObj, one);
        return bodyObj;
    }

    public Object create(JSONObject noteDTO) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(noteDTO)) {
            throw new ParamInvalidException("入参异常");
        }
        String noteAlias = noteDTO.getString("noteAlias").trim();
        int tenantId = UserUtil.getCurrentTenantId();
        List<ZeppelinNotebook> list = notebookService.list(new QueryWrapper<ZeppelinNotebook>().eq("tenant_id", tenantId)
                .eq("note_alias", noteAlias));
        if (CollectionUtils.isNotEmpty(list)) {
            throw new ParamInvalidException(noteAlias + "已存在");
        }
        // 随机生成name值
        String realName = noteAlias + "_" + UuidUtil.getUUID();
        noteDTO.put("name", realName);
        Integer interpreterId = noteDTO.getInteger("interpreterId");
        ZeppelinInterpreter one = interpreterService.getOne(new QueryWrapper<ZeppelinInterpreter>().eq("tenant_id", tenantId).eq("id",
                interpreterId));
        if (one == null) {
            throw new ParamInvalidException("数据源不存在");
        }
        // 设置数据源
        noteDTO.put("defaultInterpreterGroup", one.getInterpreterName());

        // 远程接口创建notebook
        String result = HttpUtils.sendPost(UrlConstant.NOTEBOOK_CREATE_POST, noteDTO.toJSONString());
        String noteId = getBodyStr(result);

        // it's an empty note. so add one paragraph
        paragraphManager.createNewParagraph(noteId);

        ZeppelinNotebook notebook = new ZeppelinNotebook();
        notebook.setTenantId(String.valueOf(tenantId));
        notebook.setNoteRemoteId(noteId);
        notebook.setNoteAlias(noteAlias);
        notebook.setInterpreterId(noteDTO.getInteger("interpreterId"));
        notebook.setCreateTime(LocalDateTime.now());
        notebook.setUpdateTime(LocalDateTime.now());
        notebook.setCreateUser(UserUtil.getCurrentUsername());
        notebook.setUpdateUser(UserUtil.getCurrentUsername());
        notebookService.save(notebook);

        serverUsedStatisticService.saveQuaServerUsedStatistic(ServerStatisticType.adhoc.getServerType());
        return notebook;
    }

    @Transactional(rollbackFor = Exception.class)
    public void rename(Integer id, JSONObject noteDTO) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(id) || ObjectUtils.isEmpty(noteDTO)) {
            throw new ParamInvalidException("入参异常");
        }
        String noteAlias = noteDTO.getString("noteAlias").trim();
        int tenantId = UserUtil.getCurrentTenantId();
        ZeppelinNotebook one = notebookService.getOne(new QueryWrapper<ZeppelinNotebook>().eq("tenant_id", tenantId)
                .eq("note_alias", noteAlias).ne("id", id));
        if (ObjectUtils.isNotEmpty(one)) {
            throw new ParamInvalidException(noteAlias + "已存在");
        }

        notebookService.update(new UpdateWrapper<ZeppelinNotebook>()
                .set("note_alias", noteAlias)
                .set("update_user", UserUtil.getCurrentUsername())
                .set("update_time", LocalDateTime.now())
                .eq("tenant_id", tenantId)
                .eq("id", id));
    }

    /**
     * 批量删除
     *
     * @param ids
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Integer> ids) throws Exception {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        int tenantId = UserUtil.getCurrentTenantId();
        List<ZeppelinNotebook> list = notebookService.list(new QueryWrapper<ZeppelinNotebook>().in("id", ids).eq("tenant_id", tenantId));
        // 删除本地数据
        notebookService.removeByIds(ids);
        //删除远程数据
        for (int i = 0; i < list.size(); i++) {
            HttpUtils.sendDelete(String.format(UrlConstant.NOTEBOOK_DELETE, list.get(i).getNoteRemoteId()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByInterpreterId(int interpreterId) throws Exception {
        int tenantId = UserUtil.getCurrentTenantId();
        QueryWrapper<ZeppelinNotebook> qw = new QueryWrapper<ZeppelinNotebook>().eq("tenant_id", tenantId).eq("interpreter_id", interpreterId);
        List<ZeppelinNotebook> notebooks = notebookService.list(qw);
        if (CollectionUtils.isEmpty(notebooks)) {
            return;
        }
        this.delete(notebooks.stream().map(note -> note.getId()).collect(Collectors.toList()));
    }

    /**
     * 查询租户下note是否存在，true：已存在，false：不存在
     *
     * @param alias
     * @return
     */
    public boolean exist(String alias) throws ParamInvalidException {
        if (StringUtils.isBlank(alias)) {
            throw new ParamInvalidException("入参异常");
        }
        int tenantId = UserUtil.getCurrentTenantId();
        List<ZeppelinNotebook> list = notebookService.list(new QueryWrapper<ZeppelinNotebook>()
                .eq("note_alias", alias).eq("tenant_id", tenantId));
        return CollectionUtils.isNotEmpty(list);
    }

    public Object allParagraphs(Integer noteId) throws Exception {
        ZeppelinNotebook one = checkNoteIdExist(noteId);
        String result = HttpUtils.sendGet(String.format(UrlConstant.NOTEBOOK_PARAGRAPHS_LIST_GET, one.getNoteRemoteId()));
        return getBodyArray(result);
    }

    public void runAllParagraphs(Integer noteId) throws ParamInvalidException {
        ZeppelinNotebook one = checkNoteIdExist(noteId);
        String result = HttpUtils.sendPost(String.format(UrlConstant.NOTEBOOK_RUN_ALL_PARAGRAPHS_POST, one.getNoteRemoteId()), "");
        check(result);
    }

    public void stopAllParagraphs(Integer noteId) throws ParamInvalidException {
        ZeppelinNotebook one = checkNoteIdExist(noteId);
        String result = HttpUtils.sendDelete(String.format(UrlConstant.NOTEBOOK_STOP_ALL_PARAGRAPHS_DELETE, one.getNoteRemoteId()));
        check(result);
    }

    public void clearAllParagraphResult(Integer noteId) throws ParamInvalidException {
        ZeppelinNotebook one = checkNoteIdExist(noteId);
        String result = HttpUtils.sendPut(String.format(UrlConstant.NOTEBOOK_CLEAR_ALL_PARAGRAPH_RESULTS_PUT, one.getNoteRemoteId()), "");
        check(result);
    }

    private ZeppelinNotebook checkNoteIdExist(Integer id) throws ParamInvalidException {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        int tenantId = UserUtil.getCurrentTenantId();
        ZeppelinNotebook one = notebookService.getOne(new QueryWrapper<ZeppelinNotebook>().eq("id", id).eq("tenant_id", tenantId));
        if (ObjectUtils.isEmpty(one)) {
            throw new ParamInvalidException("note不存在");
        }
        return one;
    }
}