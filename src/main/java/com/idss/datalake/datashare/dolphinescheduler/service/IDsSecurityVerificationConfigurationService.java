package com.idss.datalake.datashare.dolphinescheduler.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datashare.dolphinescheduler.entity.DsSecurityVerificationConfiguration;

/**
 * <p>
 * 安全验证配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface IDsSecurityVerificationConfigurationService extends IService<DsSecurityVerificationConfiguration> {

}
