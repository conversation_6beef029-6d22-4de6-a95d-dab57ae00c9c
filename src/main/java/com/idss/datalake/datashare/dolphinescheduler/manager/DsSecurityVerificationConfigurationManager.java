/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-03-06
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-03-06
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.dolphinescheduler.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.util.AESUtil;
import com.idss.datalake.common.util.MD5Util;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datashare.dolphinescheduler.dto.DsSecurityVerificationConfigurationDTO;
import com.idss.datalake.datashare.dolphinescheduler.entity.DsSecurityVerificationConfiguration;
import com.idss.datalake.datashare.dolphinescheduler.service.IDsSecurityVerificationConfigurationService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>安全验证配置 manager处理类</p>
 * @since 2025-03-06
 */
@Component
public class DsSecurityVerificationConfigurationManager {
    private static final Logger logger = LoggerFactory.getLogger(DsSecurityVerificationConfigurationManager.class);

    @Autowired
    private IDsSecurityVerificationConfigurationService verificationConfigurationService;

    public void save(DsSecurityVerificationConfigurationDTO dto) {
        if (dto.getPasswordEnable() != null && dto.getPasswordEnable() == 1 && StringUtils.isBlank(dto.getVerifyPassword())) {
            throw new RuntimeException("密码不能为空");
        }
        Long tenantId = UserUtil.getLongCurrentTenantId();
        String username = UserUtil.getCurrentUsername();
        LocalDateTime now = LocalDateTime.now();
        String verifyPassword = getVerifyPassword(dto.getVerifyPassword(), tenantId);
        DsSecurityVerificationConfiguration one = verificationConfigurationService.getOne(new QueryWrapper<DsSecurityVerificationConfiguration>()
                .eq("tenant_id", tenantId));
        if (one == null) {
            DsSecurityVerificationConfiguration configuration = new DsSecurityVerificationConfiguration();
            configuration.setPasswordEnable(dto.getPasswordEnable());
            configuration.setSm4Enable(dto.getSm4Enable());
            configuration.setVerifyPassword(verifyPassword);
            configuration.setTenantId(tenantId);
            configuration.setCreateTime(now);
            configuration.setUpdateTime(now);
            configuration.setCreateUser(username);
            configuration.setUpdateUser(username);
            verificationConfigurationService.save(configuration);
            return;
        }
        one.setPasswordEnable(dto.getPasswordEnable());
        one.setSm4Enable(dto.getSm4Enable());
        one.setVerifyPassword(verifyPassword);
        one.setUpdateUser(username);
        one.setUpdateTime(now);
        verificationConfigurationService.updateById(one);
    }

    private static String getVerifyPassword(String verifyPassword, Long tenantId) {
        if (StringUtils.isBlank(verifyPassword)) {
            return null;
        }
        String pwd = AESUtil.defaultDecrypt(verifyPassword);
        String realVerifyPassword = MD5Util.md5Encode(pwd, String.valueOf(tenantId));
        return realVerifyPassword;
    }

    public void verify(DsSecurityVerificationConfigurationDTO dto) {
        if (StringUtils.isBlank(dto.getVerifyPassword())) {
            throw new RuntimeException("密码不能为空");
        }
        Long tenantId = UserUtil.getLongCurrentTenantId();
        DsSecurityVerificationConfiguration one = verificationConfigurationService.getOne(new QueryWrapper<DsSecurityVerificationConfiguration>()
                .eq("tenant_id", tenantId));
        if (one == null) {
            logger.info("安全验证未配置，无需验证密码");
            return;
        }
        if (one.getPasswordEnable() == 0) {
            logger.info("安全验证未开启，无需验证密码");
            return;
        }
        String verifyPassword = getVerifyPassword(dto.getVerifyPassword(), tenantId);
        if (!one.getVerifyPassword().equals(verifyPassword)) {
            throw new RuntimeException("密码错误");
        }
    }


    public DsSecurityVerificationConfiguration detail() {
        Long tenantId = UserUtil.getLongCurrentTenantId();
        DsSecurityVerificationConfiguration one = verificationConfigurationService.getOne(new QueryWrapper<DsSecurityVerificationConfiguration>()
                .eq("tenant_id", tenantId));
        if (one == null) {
            one = new DsSecurityVerificationConfigurationDTO();
            one.setPasswordEnable(0);
            one.setSm4Enable(0);
            one.setTenantId(tenantId);
        }
        one.setVerifyPassword(null);
        return one;
    }
}