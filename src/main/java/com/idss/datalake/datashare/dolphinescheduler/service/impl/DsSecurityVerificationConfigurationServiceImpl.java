package com.idss.datalake.datashare.dolphinescheduler.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datashare.dolphinescheduler.entity.DsSecurityVerificationConfiguration;
import com.idss.datalake.datashare.dolphinescheduler.mapper.DsSecurityVerificationConfigurationMapper;
import com.idss.datalake.datashare.dolphinescheduler.service.IDsSecurityVerificationConfigurationService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 安全验证配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@Service
public class DsSecurityVerificationConfigurationServiceImpl extends ServiceImpl<DsSecurityVerificationConfigurationMapper,
        DsSecurityVerificationConfiguration> implements IDsSecurityVerificationConfigurationService {

}
