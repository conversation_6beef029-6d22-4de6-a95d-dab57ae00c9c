package com.idss.datalake.datashare.cluster.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datashare.cluster.entity.TbCluster;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 集群管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/8 0008
 */
public interface TbClusterMapper extends BaseMapper<TbCluster> {

    List<Map<String, Object>> page(Map<String, Object> map);

    Integer pageCount(Map<String, Object> map);

    List<Map<String, Object>> clusterPage(Map<String, Object> map);

    List<Map<String, Object>> select(@Param("tenantId") Long tenantId);

    /**
     * 查询集群最新创建时间
     *
     * @param map
     * @return
     */
    String queryLastTime(Map<String, Object> map);

    List<Map<String, Object>> selectTenantIndex();
}