/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-04-23
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-04-23
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.cluster.controller;


import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datashare.cluster.dto.TbClusterNodeDTO;
import com.idss.datalake.datashare.cluster.manager.TbClusterNodeManager;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 集群节点控制器
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/cluster/node")
public class TbClusterNodeController {
    private static final Logger logger = LoggerFactory.getLogger(TbClusterNodeController.class);
    @Autowired
    private TbClusterNodeManager tbClusterNodeManager;

    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody TbClusterNodeDTO dto) {
        try {
            tbClusterNodeManager.create(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public ResultBean delete(@RequestBody TbClusterNodeDTO dto) {
        try {
            tbClusterNodeManager.delete(dto.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Long id) {
        try {
            return ResultBean.success(tbClusterNodeManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "编辑")
    @PostMapping(value = "/edit")
    public ResultBean edit(@RequestBody TbClusterNodeDTO dto) {
        try {
            tbClusterNodeManager.edit(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询列表")
    @PostMapping(value = "/list")
    public ResultBean list(@RequestBody TbClusterNodeDTO dto) {
        try {
            return ResultBean.success(tbClusterNodeManager.list(dto.getClusterId()));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }
}
