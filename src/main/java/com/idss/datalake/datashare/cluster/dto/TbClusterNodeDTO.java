/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
** Product VERSION,UPDATED BY,UPDATE DATE *
* DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
*-----------------------------------------------------------------------------*
* V1.0, wangjun, 2025-04-23
* create
*-----------------------------------------------------------------------------*
* V1.0, wangjun, 2025-04-23
* M {modifyComment}
* - {delComment}
* + {addCommnet}
\*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.cluster.dto;
import java.util.List;

import com.idss.datalake.datashare.cluster.entity.TbClusterNode;
import lombok.Data;

/**
* @description <p> dto类</p>
* <AUTHOR>
* @since 2025-04-23
*/
@Data
public class TbClusterNodeDTO extends TbClusterNode {
    private List<Long> ids;
}