package com.idss.datalake.datashare.cluster.controller;

import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.syslog.annotation.SysLog;
import com.idss.datalake.common.syslog.entity.OptModule;
import com.idss.datalake.common.syslog.entity.OptType;
import com.idss.datalake.datagovern.metadata.base.BaseResponse;
import com.idss.datalake.datashare.cluster.entity.ClusterRequest;
import com.idss.datalake.datashare.cluster.entity.TbCluster;
import com.idss.datalake.datashare.cluster.enums.NodeRoleType;
import com.idss.datalake.datashare.cluster.service.ITbClusterService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.UUID;

/**
 * 集群管理
 *
 * <AUTHOR>
 * @since 2022/7/8 0008
 */
@RestController
@Slf4j
@RequestMapping(Constant.API_PREFIX + "/cluster")
public class TbClusterController {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ITbClusterService clusterService;

    @Value("${file.uploadPath}")
    private String kbsFilePath;


    @PostMapping("/save")
    @SysLog(logName = "集群保存", optType = OptType.ADD, optModule = OptModule.CLUSTER_RESOURCE_MANAGE, switchStatus = true)
    public ResultBean save(@RequestBody TbCluster cluster) {
        return this.clusterService.saveDatalakeSysCluster(cluster);
    }

    @GetMapping("/{id}")
    public ResultBean info(@PathVariable("id") String id) {
        return this.clusterService.queryCluster(id);
    }

    @PostMapping("/{id}")
    public ResultBean info(@PathVariable("id") String id, @RequestBody ClusterRequest clusterRequest) {
        return this.clusterService.queryClusterByPage(id, clusterRequest);
    }

    @PostMapping("/page")
    public ResultBean page(@RequestBody ClusterRequest clusterRequest) {
        return this.clusterService.queryByPage(clusterRequest);
    }

    @GetMapping("/getAll")
    public ResultBean getAll() {
        return this.clusterService.getAll();
    }

    @PostMapping("/connect")
    public ResultBean connect(@RequestBody TbCluster cluster) {
        return this.clusterService.connect(cluster);
    }

    @PutMapping("/{id}")
    @SysLog(logName = "集群编辑", optType = OptType.EDIT, optModule = OptModule.CLUSTER_RESOURCE_MANAGE, switchStatus = true)
    public ResultBean update(@PathVariable("id") String id, @RequestBody TbCluster tbCluster) {
        return this.clusterService.updateCluster(id, tbCluster);
    }

    @DeleteMapping("/{id}")
    @SysLog(logName = "集群删除", optType = OptType.DELETE, optModule = OptModule.CLUSTER_RESOURCE_MANAGE, switchStatus = true)
    public ResultBean delete(@PathVariable("id") String id) {
        return this.clusterService.deleteCluster(id);
    }

    @PostMapping("/kbs/upload")
    public BaseResponse<String> upload(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return BaseResponse.error("上传失败，请选择文件");
        }
        String uuid = UUID.randomUUID().toString();
        String uploadPath = kbsFilePath + File.separator + uuid;
        // 如果路径不存在则生成路径
        File uploadFile = new File(uploadPath);
        if (!uploadFile.exists()) {
            uploadFile.mkdirs();
        }
        String localFilePath = uploadPath + File.separator + file.getOriginalFilename();
        File dest = new File(localFilePath);
        try {
            file.transferTo(dest);
            return BaseResponse.success("上传成功", localFilePath);
        } catch (Exception e) {
            log.error("上传成功失败", e);
            return BaseResponse.error();
        }
    }

    /**
     * 测试连通性
     *
     * @param cluster
     * @return
     */
    @PostMapping("/testConnect")
    public ResultBean testConnect(@RequestBody TbCluster cluster) {
        boolean connectable = clusterService.connectable(cluster);
        return ResultBean.success(connectable);
    }

    @GetMapping("/getClusterNodeType")
    public ResultBean getClusterNodeType() {
        return ResultBean.success(NodeRoleType.getAllNames());

    }
}
