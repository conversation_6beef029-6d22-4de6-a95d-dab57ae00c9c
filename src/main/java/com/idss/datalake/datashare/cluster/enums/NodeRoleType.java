package com.idss.datalake.datashare.cluster.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 节点角色类型
 *
 * <AUTHOR>
 * @date 2025/4/23
 * @see
 */
public enum NodeRoleType {
    MASTER("主节点", "负责集群管理、协调和元数据操作"),
    SLAVE("从节点", "执行主节点分配的任务或存储数据"),
    CANDIDATE_MASTER("候选主节点", "参与主节点选举，尝试成为主节点"),
    DATA("数据节点", "存储和处理实际数据，执行查询或计算"),
    CLIENT("客户端节点", "与集群交互，发送请求或接收结果"),
    PROXY("代理节点", "转发请求，处理负载均衡或安全验证"),
    COORDINATOR("协调节点", "协调分布式任务或查询，汇总结果"),
    OBSERVER("观察者节点", "监控集群状态，不参与核心操作"),
    COLD("冷节点", "存储不常访问的数据，优化存储成本"),
    HOT("热节点", "存储高频访问的数据，优化查询性能");

    private final String name;
    private final String description;

    NodeRoleType(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return name + ": " + description;
    }

    public static List<String> getAllNames() {
        return Arrays.stream(values())
                .map(NodeRoleType::getName)
                .collect(Collectors.toList());
    }

}
