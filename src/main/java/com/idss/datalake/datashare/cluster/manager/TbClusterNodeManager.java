/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-04-23
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2025-04-23
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.cluster.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datashare.cluster.dto.TbClusterNodeDTO;
import com.idss.datalake.datashare.cluster.entity.TbClusterNode;
import com.idss.datalake.datashare.cluster.service.ITbClusterNodeService;
import com.idss.datalake.datashare.cluster.service.ITbClusterService;
import com.idss.datalake.datashare.tenant.entity.TbTenantCluster;
import com.idss.datalake.datashare.tenant.service.ITbTenantClusterService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p> manager处理类</p>
 * @since 2025-04-23
 */
@Component
public class TbClusterNodeManager {
    private static final Logger logger = LoggerFactory.getLogger(TbClusterNodeManager.class);

    @Autowired
    private ITbClusterNodeService clusterNodeService;
    @Autowired
    private ITbClusterService clusterService;
    @Autowired
    private ITbTenantClusterService tenantClusterService;


    public List<TbClusterNode> list(Long clusterId) {
        return clusterNodeService.list(new QueryWrapper<TbClusterNode>().eq("cluster_id", clusterId));
    }

    public void create(TbClusterNodeDTO dto) {
        if (StringUtils.isBlank(dto.getHost())) {
            throw new ParamInvalidException("入参异常");
        }

        TbClusterNode tbClusterNode = new TbClusterNode();
        ReflectionUtil.copyLomBokProperties(dto, tbClusterNode);
        tbClusterNode.setCreateTime(LocalDateTime.now());
        tbClusterNode.setUpdateTime(LocalDateTime.now());
        tbClusterNode.setCreateUser(UserUtil.getCurrentUsername());
        tbClusterNode.setUpdateUser(UserUtil.getCurrentUsername());
        clusterNodeService.save(tbClusterNode);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        // List<TbClusterNode> nodeList = clusterNodeService.list(new QueryWrapper<TbClusterNode>().in("id", ids));
        // if (CollectionUtils.isEmpty(nodeList)) {
        //     throw new ParamInvalidException("入参异常");
        // }
        // List<Long> clusterIds = nodeList.stream().map(TbClusterNode::getClusterId).collect(Collectors.toList());
        // int refTenantCount = tenantClusterService.count(new QueryWrapper<TbTenantCluster>().in("cluster_id", clusterIds));
        // if (refTenantCount > 0) {
        //     throw new ParamInvalidException("该集群节点已被引用，请先解除引用");
        // }
        clusterNodeService.remove(new QueryWrapper<TbClusterNode>().in("id", ids));
    }

    public void edit(TbClusterNodeDTO dto) {
        if (ObjectUtils.isEmpty(dto.getId())) {
            throw new ParamInvalidException("入参异常");
        }

        TbClusterNode dbOne = clusterNodeService.getById(dto.getId());
        ReflectionUtil.copyLomBokProperties(dto, dbOne);
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        clusterNodeService.updateById(dbOne);
    }

    public TbClusterNode detail(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        return clusterNodeService.getById(id);
    }
}