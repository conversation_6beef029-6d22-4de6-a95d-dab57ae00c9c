/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-01-07
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-01-07
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.dataapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p></p>
 * @since 2022-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ApiDataSource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 数据源名称
     */
    private String dataSourceName;

    /**
     * 数据源类型
     */
    private String dataSourceType;

    /**
     * 数据源版本号
     */
    private String dataSourceVersion;

    /**
     * 描述
     */
    private String dataSourceDesc;

    /**
     * 集群地址（多个用逗号分开）
     */
    private String clusterAddress;
    /**
     * jdbc地址
     */
    private String jdbcUrl;
    /**
     * 集群名称
     */
    private String clusterName;
    /**
     * 数据库（mongo）
     */
    private String databaseName;

    /**
     * 连接用户名
     */
    private String linkUser;

    /**
     * 连接密码
     */
    private String linkPassword;

    /**
     * 连接状态，0：未连接，1：正常，2：失败
     */
    private Integer linkStatus;

    /**
     * 创建人名称
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新人名称
     */
    private String updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 状态，0：无效，1：有效
     */
    private Integer flag;

    /**
     * 列表页面显示的类型
     */
    @TableField(exist = false)
    private String datasourceLabel;

    /**
     * keytab文件路径
     */
    private String keyTabPath;
    /**
     * krb5文件路径
     */
    private String krb5ConfPath;
    /**
     * jaas文件路径
     */
    private String jaasConfPath;

    /**
     * keytab文件内容(base64编码)
     */
    private String keyTabContent;
    /**
     * krb5文件内容(base64编码)
     */
    private String krb5ConfContent;
    /**
     * jaas文件内容(base64编码)
     */
    private String jaasConfContent;

    /**
     * 租户自定义数据源id
     */
    private Integer tenantDefineDatasourcesId;
}
