package com.idss.datalake.datashare.dataapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datashare.dataapi.entity.ApiInfo;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
public interface ApiInfoMapper extends BaseMapper<ApiInfo> {

    @Select("select count(1) from api_info ")
    Long apiInfoTotal();

    @Select("select count(1) from api_info where publish_status in (1,2)")
    Long apiReleaseTotal();

    @Select("select count(1) from api_apply ")
    Long apiApplyTotal();

    @Select("select count(1) from api_invoke_log where api_id in (select id from api_info)")
    Long apiInvokeTotal();

    @Select("select count(1) from (select a.id from api_info a left join api_apply b on a.id = b.api_id WHERE a.publish_status = #{auditStatus} or b.audit_status = #{auditStatus})")
    Long apiInvokeStatus(Integer auditStatus);

    @Select("SELECT TO_CHAR(invoke_time, 'HH24:00') AS \"timeInterval\", COUNT(1) AS count " +
            "FROM api_invoke_log " +
            "where api_id in (select id from api_info) and invoke_time >= (CURRENT_TIMESTAMP - INTERVAL '24 HOURS') " +
            "GROUP BY \"timeInterval\" order by \"timeInterval\"")
    List<Map<String ,Object>> least24HourInvoke();

    @Select("SELECT invoke_flag as \"invokeFlag\", COUNT(1) AS count " +
            "FROM api_invoke_log " +
            "where api_id in (select id from api_info) and invoke_time >= (CURRENT_TIMESTAMP - INTERVAL '24 HOURS') and invoke_flag is not null " +
            "GROUP BY invoke_flag")
    List<Map<String ,Object>> least24HourInvokeSuccessPercent();

    @Select("SELECT create_by as \"createBy\", COUNT(1) AS count " +
            "FROM api_invoke_log " +
            "where api_id in (select id from api_info) and  create_by is not null " +
            "GROUP BY create_by order by count desc limit 10 ")
    List<Map<String ,Object>> tenantInvokeTop10();

    @Select("SELECT api_id as \"apiId\", COUNT(1) AS count , invoke_flag as \"invokeFlag\" " +
            "FROM api_invoke_log " +
            "where api_id in (select id from api_info) and  invoke_flag is not null " +
            "GROUP BY api_id ,invoke_flag order by count desc limit 20 ")
    List<Map<String ,Object>> apiInvokeTop10();

    @Select("SELECT TO_CHAR(invoke_time, 'MM-DD') AS \"timeInterval\", COUNT(1) AS count " +
            "FROM api_invoke_log " +
            "where api_id in (select id from api_info) and invoke_time >= (CURRENT_TIMESTAMP - INTERVAL '30 DAYS') " +
            "GROUP BY \"timeInterval\"")
    List<Map<String, Object>> least30DayInvoke();

    @Select("select audit_status from api_apply where api_id = #{apiId}")
    List<Long> apiAuditStatus(Integer apiId);

    @Select("select count(*) from api_invoke_log where api_id = #{apiId}")
    Long apiInvokeCount(Integer apiId);
}
