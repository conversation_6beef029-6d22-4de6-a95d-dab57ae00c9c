package com.idss.datalake.datashare.dataapi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * api调用日志信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Data
@TableName("api_invoke_log")
@ApiModel(value = "ApiInvokeLog", description = "api调用日志表")
public class ApiInvokeLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("api主键")
    private String apiId;

    @ApiModelProperty("客户端主键")
    private String clientId;

    @ApiModelProperty("调用时间")
    private LocalDateTime invokeTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建日期")
    private LocalDateTime createDate;

    @ApiModelProperty("日志类型")
    private String bizType;

    @ApiModelProperty("日志内容")
    private String content;

}
