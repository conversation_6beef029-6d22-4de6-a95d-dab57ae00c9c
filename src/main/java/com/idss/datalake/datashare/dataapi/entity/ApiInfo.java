/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-01-07
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-01-07
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.dataapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p></p>
 * @see ApiInfo
 * @since 2022-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ApiInfo extends ApiBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 数据源名称
     */
    @TableField(exist = false)
    private String dataSourceName;

    /**
     * 是否分页，0：否，1：是
     */
    private Integer allowPaging;

    /**
     * 返回内容是否包含分页信息，0：否，1：是
     */
    private Integer containPage;

    /**
     * 返回内容是否包含header信息，0：否，1：是
     */
    private Integer containHeader;

    /**
     * 是否忽略拼写检查
     */
    private Integer ignoreSyntaxCheck;

    /**
     * 提交状态，0：未提交，1：已提交
     */
    private Integer submitStatus;

    /**
     * 发布状态，0：未发布，1：已发布
     */
    private Integer publishStatus;

    /**
     * 创建人名称
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新人名称
     */
    private String updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 所属类目
     */
    private Long catalogId;

    /**
     * 返回JSON样例是否保存 0不选中，1选中
     */
    private Integer resultSampleFlag;
    /**
     * 返回JSON样例
     */
    private String resultSample;

    /**
     * 返回结果中携带第三方 API 状态码，0：不包含，1：包含
     */
    private Integer containOriginalStatus;
}
