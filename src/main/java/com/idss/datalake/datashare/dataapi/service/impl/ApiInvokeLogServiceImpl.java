package com.idss.datalake.datashare.dataapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datashare.dataapi.dto.ApiInvokeLogDto;
import com.idss.datalake.datashare.dataapi.dto.ApiInvokeLogPageRequest;
import com.idss.datalake.datashare.dataapi.dto.TenantInvokeLog;
import com.idss.datalake.datashare.dataapi.entity.ApiInvokeLog;
import com.idss.datalake.datashare.dataapi.mapper.ApiInvokeLogMapper;
import com.idss.datalake.datashare.dataapi.service.IApiInvokeLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * api调用日志信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Service
public class ApiInvokeLogServiceImpl extends ServiceImpl<ApiInvokeLogMapper, ApiInvokeLog> implements IApiInvokeLogService {

    @Resource
    ApiInvokeLogMapper apiInvokeLogMapper;

    @Override
    public List<TenantInvokeLog> queryTenantIdByInvokeTime(String invokeTime) {
        return apiInvokeLogMapper.queryTenantIdByInvokeTime(invokeTime);
    }

    @Override
    public IPage<ApiInvokeLogDto> getPage(Page<ApiInvokeLogPageRequest> page, QueryWrapper<ApiInvokeLogPageRequest> wrapper) {
        return apiInvokeLogMapper.getPage(page,wrapper);
    }

}
