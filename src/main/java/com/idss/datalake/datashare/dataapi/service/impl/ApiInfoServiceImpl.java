package com.idss.datalake.datashare.dataapi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datashare.dataapi.entity.ApiInfo;
import com.idss.datalake.datashare.dataapi.mapper.ApiInfoMapper;
import com.idss.datalake.datashare.dataapi.service.IApiInfoService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@Service
public class ApiInfoServiceImpl extends ServiceImpl<ApiInfoMapper, ApiInfo> implements IApiInfoService {
    @Override
    public Long apiInfoTotal() {
        return this.getBaseMapper().apiInfoTotal();
    }

    @Override
    public Long apiReleaseTotal() {
        return this.getBaseMapper().apiReleaseTotal();
    }

    @Override
    public Long apiApplyTotal() {
        return this.getBaseMapper().apiApplyTotal();
    }

    @Override
    public Long apiInvokeTotal() {
        return this.getBaseMapper().apiInvokeTotal();
    }

    @Override
    public Long apiInvokeStatus(Integer auditStatus) {
        return this.getBaseMapper().apiInvokeStatus(auditStatus);
    }

    @Override
    public List<Map<String, Object>> least24HourInvoke() {
        return this.getBaseMapper().least24HourInvoke();
    }

    @Override
    public List<Map<String, Object>> least24HourInvokeSuccessPercent() {

        return this.getBaseMapper().least24HourInvokeSuccessPercent();
    }

    @Override
    public List<Map<String, Object>> tenantInvokeTop10() {
        return this.getBaseMapper().tenantInvokeTop10();
    }

    @Override
    public List<Map<String, Object>> apiInvokeTop10() {
        return this.getBaseMapper().apiInvokeTop10();
    }

    @Override
    public List<Map<String, Object>> least30DayInvoke() {
        return this.getBaseMapper().least30DayInvoke();
    }

    @Override
    public List<Long> apiAuditStatus(Integer apiId) {
        return this.getBaseMapper().apiAuditStatus(apiId);
    }

    @Override
    public Long apiInvokeCount(Integer apiId) {
        return this.getBaseMapper().apiInvokeCount(apiId);
    }
}
