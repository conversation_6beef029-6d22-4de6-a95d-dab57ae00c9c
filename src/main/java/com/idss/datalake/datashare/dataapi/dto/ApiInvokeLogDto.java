package com.idss.datalake.datashare.dataapi.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description : <p>租户调用日志</p>
 * @see：
 * @since 2022/10/24
 */
@Data
public class ApiInvokeLogDto {
    private String id;
    private String name;
    private String user;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime invokeTime;
    private String flag;
    /**
     * 请求状态
     */
    private String requestFlag;

    private Long duration;
    private String reqType;
    private String apiType;
}
