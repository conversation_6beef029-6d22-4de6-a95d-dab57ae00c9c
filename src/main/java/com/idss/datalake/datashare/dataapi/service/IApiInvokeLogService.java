package com.idss.datalake.datashare.dataapi.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.datashare.dataapi.dto.ApiInvokeLogDto;
import com.idss.datalake.datashare.dataapi.dto.ApiInvokeLogPageRequest;
import com.idss.datalake.datashare.dataapi.dto.TenantInvokeLog;
import com.idss.datalake.datashare.dataapi.entity.ApiInvokeLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * api调用日志信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface IApiInvokeLogService extends IService<ApiInvokeLog> {
    /**
     * 查询租户id
     *
     * @param invokeTime 日志调用时间
     * @return
     */
    List<TenantInvokeLog> queryTenantIdByInvokeTime(String invokeTime);


    IPage<ApiInvokeLogDto> getPage(Page<ApiInvokeLogPageRequest> page,
                                   QueryWrapper<ApiInvokeLogPageRequest> wrapper);

}
