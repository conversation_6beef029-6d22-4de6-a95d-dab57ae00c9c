package com.idss.datalake.datashare.dataapi.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @description <p>api基本属性</p>
 * @see
 * @since 2022-10-24
 */
@Data
public class ApiBaseInfo {

    /**
     * API中文名称
     */
    private String cnName;

    /**
     * 描述
     */
    private String apiDesc;

    /**
     * api路径
     */
    private String apiPath;

    /**
     * 协议，HTTP/HTTPS
     */
    private String protocol;

    /**
     * 请求类型，GET,POST,PUT
     */
    private String reqType;

    /**
     * 返回类型，JSON
     */
    private String responseType;

    /**
     * 查询超时时间，单位s
     */
    private Integer queryTimeout;

    /**
     * 单位时间内请求限制次数
     */
    private Integer reqLimit;

    /**
     * 传输加密类型，0：不加密
     */
    private Integer transferEncryptType;

    /**
     * 数据源id
     */
    private Integer dataSourceId;

    /**
     * 0：生成api，1：注册api
     */
    private Integer apiType;

    /**
     * 执行的sql语句
     */
    private String apiSql;

    /**
     * 查询的表名
     */
    private String tableName;

    /**
     * api名称
     */
    private String name;

    /**
     * 生成api类型，0：简单模板，1：自定义
     */
    private Integer apiCreateType;

    /**
     * API-TOKEN值
     */
    private String apiToken;

    /**
     * 后端host，http(s)://host:port
     */
    private String targetHost;

    /**
     * 后端服务path，示例：/user/abc
     */
    private String targetPath;

    /**
     * 后端服务端口
     */
    private Integer targetPort;
    /**
     * 后端服务请求方式
     */
    private String targetMethod;

    /**
     * 参数传输方式：0：json，1：form表单
     */
    private Integer contentType;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 所属项目
     */
    private String projectId;

    /**
     * 请求Body描述
     */
    private String bodyDescJson;

    /**
     * 正常返回示例 // TODO: 2022/3/28 gateway暂未使用
     */
    private String normalReturnExample;
    /**
     * 错误返回示例 // TODO: 2022/3/28 gateway暂未使用
     */
    private String errorReturnExample;

}
