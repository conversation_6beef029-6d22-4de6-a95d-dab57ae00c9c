package com.idss.datalake.datashare.index.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description : <p>数据源类型(资源类型)</p>
 * @see：
 * @since 2022/8/12
 */
public enum DatasourceType {
    Clickhouse(true),
    elasticsearch(true),
    Mysql(true),
    hdfs(true),
    Hive(false),
    kafka(false);

    DatasourceType(boolean statistical) {
        this.statistical = statistical;
    }

    public boolean isStatistical() {
        return statistical;
    }

    public void setStatistical(boolean statistical) {
        this.statistical = statistical;
    }

    private boolean statistical;

    /**
     * 获取首页可统计的类型
     *
     * @return
     */
    public static List<String> getStatisticsType() {
        return Arrays.stream(DatasourceType.values()).filter(x -> x.isStatistical()).map(x -> x.name().toLowerCase()).collect(Collectors.toList());
    }

}
