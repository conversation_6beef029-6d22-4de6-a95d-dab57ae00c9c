package com.idss.datalake.datashare.index.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.idss.datalake.datashare.index.entity.TbStatisticsClusterInstanceDiskSync;

/**
 * <p>
 * 租户关联集群实例的磁盘使用量同步（mysql,ch是库，hdfs是目录） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
public interface ITbStatisticsClusterInstanceDiskSyncService extends IService<TbStatisticsClusterInstanceDiskSync> {

}
