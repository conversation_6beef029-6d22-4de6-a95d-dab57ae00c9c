/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
** Product VERSION,UPDATED BY,UPDATE DATE *
* DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
*-----------------------------------------------------------------------------*
* V1.0, wj, 2022-08-11
* create
*-----------------------------------------------------------------------------*
* V1.0, wj, 2022-08-11
* M {modifyComment}
* - {delComment}
* + {addCommnet}
\*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.index.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* @description <p>数据采集量统计表 dto类</p>
* @see com.idss.index.entity.TbStatisticsEtlDataDTO
* <AUTHOR>
* @since 2022-08-11
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    public class TbStatisticsEtlDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer id;
            /**
            * 采集日志名称
            */
    private String etlName;
            /**
            * 采集数量
            */
    private Long etlCount;
            /**
            * 入库时间
            */
    private LocalDateTime createTime;
            /**
            * 1有效，0无效
            */
    private Integer flag;


}
