/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-17
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-17
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.index.dto;

import com.idss.datalake.datashare.index.entity.TbStatisticsHdfsSync;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>hdfs磁盘空间同步表 dto类</p>
 * @since 2022-08-17
 */
@Data
public class TbStatisticsHdfsSyncDTO extends TbStatisticsHdfsSync {

}
