/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-12
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-12
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.index.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description <p>租户使用磁盘空间统计表</p>
 * @see TbStatisticsDiskTenant
 * @since 2022-08-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TbStatisticsDiskTenant implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 已用，单位m
     */
    private Long clickhouseUsed;

    /**
     * 空闲，单位m
     */
    private Long clickhouseFree;

    /**
     * 已用，单位m
     */
    private Long mysqlUsed;

    /**
     * 空闲，单位m
     */
    private Long mysqlFree;

    /**
     * 已用，单位m
     */
    private Long hdfsUsed;

    /**
     * 空闲，单位m
     */
    private Long hdfsFree;

    /**
     * 已用，单位m
     */
    private Long elasticsearchUsed;

    /**
     * 空闲，单位m
     */
    private Long elasticsearchFree;

    /**
     * 入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 1有效，0无效
     */
    private Integer flag;

    /**
     * 容量预警
     */
    @TableField(exist = false)
    private String alarm;

    /**
     * 方便页面显示
     */
    @TableField(exist = false)
    private String clickhouseDisplay;
    @TableField(exist = false)
    private String hdfsDisplay;
    @TableField(exist = false)
    private String mysqlDisplay;
    @TableField(exist = false)
    private String elasticsearchDisplay;
}
