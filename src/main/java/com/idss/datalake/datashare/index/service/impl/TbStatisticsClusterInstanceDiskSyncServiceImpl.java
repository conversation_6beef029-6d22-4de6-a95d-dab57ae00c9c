package com.idss.datalake.datashare.index.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.datashare.index.entity.TbStatisticsClusterInstanceDiskSync;
import com.idss.datalake.datashare.index.mapper.TbStatisticsClusterInstanceDiskSyncMapper;
import com.idss.datalake.datashare.index.service.ITbStatisticsClusterInstanceDiskSyncService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 租户关联集群实例的磁盘使用量同步（mysql,ch是库，hdfs是目录） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Service
public class TbStatisticsClusterInstanceDiskSyncServiceImpl extends ServiceImpl<TbStatisticsClusterInstanceDiskSyncMapper, TbStatisticsClusterInstanceDiskSync> implements ITbStatisticsClusterInstanceDiskSyncService {

}
