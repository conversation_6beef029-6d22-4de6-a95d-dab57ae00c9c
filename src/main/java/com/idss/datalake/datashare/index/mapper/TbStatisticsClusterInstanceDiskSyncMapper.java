package com.idss.datalake.datashare.index.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datashare.index.entity.TbStatisticsClusterInstanceDiskSync;

/**
 * <p>
 * 租户关联集群实例的磁盘使用量同步（mysql,ch是库，hdfs是目录） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
public interface TbStatisticsClusterInstanceDiskSyncMapper extends BaseMapper<TbStatisticsClusterInstanceDiskSync> {

}
