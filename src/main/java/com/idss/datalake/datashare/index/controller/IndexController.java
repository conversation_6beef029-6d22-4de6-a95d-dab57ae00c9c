/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-11
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-08-11
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.index.controller;


import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datashare.index.dto.TbStatisticsDiskSyncDTO;
import com.idss.datalake.datashare.index.dto.TbStatisticsDiskTenantDTO;
import com.idss.datalake.datashare.index.dto.TbStatisticsHdfsSyncDTO;
import com.idss.datalake.datashare.index.entity.TbStatisticsDisk;
import com.idss.datalake.datashare.index.entity.TbStatisticsDiskSync;
import com.idss.datalake.datashare.index.entity.TbStatisticsDiskTenant;
import com.idss.datalake.datashare.index.entity.TbStatisticsEtlData;
import com.idss.datalake.datashare.index.entity.TbStatisticsEtlTask;
import com.idss.datalake.datashare.index.entity.TbStatisticsEtlTaskDay;
import com.idss.datalake.datashare.index.entity.TbStatisticsHdfsSync;
import com.idss.datalake.datashare.index.entity.TbStatisticsSubscription;
import com.idss.datalake.datashare.index.service.ITbStatisticsDiskService;
import com.idss.datalake.datashare.index.service.ITbStatisticsDiskSyncService;
import com.idss.datalake.datashare.index.service.ITbStatisticsDiskTenantService;
import com.idss.datalake.datashare.index.service.ITbStatisticsEtlDataService;
import com.idss.datalake.datashare.index.service.ITbStatisticsEtlTaskDayService;
import com.idss.datalake.datashare.index.service.ITbStatisticsEtlTaskService;
import com.idss.datalake.datashare.index.service.ITbStatisticsHdfsSyncService;
import com.idss.datalake.datashare.index.service.ITbStatisticsSubscriptionService;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.datashare.tenant.entity.TbTenantCluster;
import com.idss.datalake.datashare.tenant.service.ITbTenantClusterService;
import com.idss.datalake.datashare.tenant.service.ITbTenantService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p>首页图表统计表 前端控制器</p>
 * @see IndexController
 * @since 2022-08-11
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/lake-index")
public class IndexController {
    private static final Logger logger = LoggerFactory.getLogger(IndexController.class);

    @Resource
    ITbStatisticsDiskService diskService;
    @Resource
    ITbStatisticsDiskSyncService diskSyncService;
    @Resource
    ITbStatisticsHdfsSyncService hdfsSyncService;
    @Resource
    ITbStatisticsDiskTenantService diskTenantService;
    @Resource
    ITbStatisticsEtlDataService etlDataService;
    @Resource
    ITbStatisticsEtlTaskDayService etlTaskDayService;
    @Resource
    ITbStatisticsEtlTaskService etlTaskService;
    @Resource
    ITbStatisticsSubscriptionService subscriptionService;
    @Resource
    ITbTenantService tenantService;
    @Resource
    ITbTenantClusterService tenantClusterService;

    @ApiOperation(value = "总数据量(总使用量)，磁盘空间")
    @GetMapping("/diskTotal")
    public ResultBean diskTotal() {
        List<TbStatisticsDisk> disks = new ArrayList<>();
        List<TbStatisticsDiskTenant> diskTenants = diskTenantService.list(new QueryWrapper<TbStatisticsDiskTenant>().eq("flag", Constant.FLAG_ABLE));
        for (TbStatisticsDiskTenant diskTenant : diskTenants) {
            if(diskTenant.getClickhouseUsed() == null){
                diskTenant.setClickhouseUsed(0L);
            }
            if(diskTenant.getClickhouseFree() == null){
                diskTenant.setClickhouseFree(0L);
            }
            if(diskTenant.getMysqlUsed() == null){
                diskTenant.setMysqlUsed(0L);
            }
            if(diskTenant.getMysqlFree() == null){
                diskTenant.setMysqlFree(0L);
            }
            if(diskTenant.getHdfsUsed() == null){
                diskTenant.setHdfsUsed(0L);
            }
            if(diskTenant.getHdfsFree() == null){
                diskTenant.setHdfsFree(0L);
            }
            if(diskTenant.getElasticsearchUsed() == null){
                diskTenant.setElasticsearchUsed(0L);
            }
            if(diskTenant.getElasticsearchFree() == null){
                diskTenant.setElasticsearchFree(0L);
            }
        }

        TbStatisticsDisk clickhouseDisk = new TbStatisticsDisk();
        clickhouseDisk.setResourceType("clickhouse");
        long clickhouseSum = diskTenants.stream().map(TbStatisticsDiskTenant::getClickhouseUsed).mapToLong(Long::longValue).sum();
        clickhouseDisk.setUsedReadable(""+ calcMb2Tb(clickhouseSum));
        clickhouseDisk.setFreeReadable(""+calcMb2Tb(diskTenants.stream().map(TbStatisticsDiskTenant::getClickhouseFree).mapToLong(Long::longValue).sum()));
        disks.add(clickhouseDisk);

        TbStatisticsDisk mysqlDisk = new TbStatisticsDisk();
        mysqlDisk.setResourceType("mysql");
        long mysqlSum = diskTenants.stream().map(TbStatisticsDiskTenant::getMysqlUsed).mapToLong(Long::longValue).sum();
        mysqlDisk.setUsedReadable(""+ calcMb2Tb(mysqlSum));
        mysqlDisk.setFreeReadable(""+calcMb2Tb(diskTenants.stream().map(TbStatisticsDiskTenant::getMysqlFree).mapToLong(Long::longValue).sum()));
        disks.add(mysqlDisk);

        TbStatisticsDisk hdfsDisk = new TbStatisticsDisk();
        hdfsDisk.setResourceType("hdfs");
        long hdfsSum = diskTenants.stream().map(TbStatisticsDiskTenant::getHdfsUsed).mapToLong(Long::longValue).sum();
        hdfsDisk.setUsedReadable(""+ calcMb2Tb(hdfsSum));
        hdfsDisk.setFreeReadable(""+calcMb2Tb(diskTenants.stream().map(TbStatisticsDiskTenant::getHdfsFree).mapToLong(Long::longValue).sum()));
        disks.add(hdfsDisk);

        TbStatisticsDisk elasticsearchDisk = new TbStatisticsDisk();
        elasticsearchDisk.setResourceType("elasticsearch");
        long elasticsearchSum = diskTenants.stream().map(TbStatisticsDiskTenant::getElasticsearchUsed).mapToLong(Long::longValue).sum();
        elasticsearchDisk.setUsedReadable(""+ calcMb2Tb(elasticsearchSum));
        elasticsearchDisk.setFreeReadable(""+calcMb2Tb(diskTenants.stream().map(TbStatisticsDiskTenant::getElasticsearchFree).mapToLong(Long::longValue).sum()));
        disks.add(elasticsearchDisk);

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("total", calcMb2Tb(clickhouseSum+hdfsSum+elasticsearchSum+mysqlSum));
        dataMap.put("list", disks);
        return ResultBean.success(dataMap);
    }

    /**
     * 单位换算：M -> T
     *
     * @param mb
     * @return
     */
    private String calcMb2Tb(long mb) {
        double Tb = NumberUtil.div(mb, 1024 * 1024, 3);
        if (Tb > 0) {
            return String.valueOf(Tb);
        }
        return "0";
    }


    @ApiOperation(value = "采集任务数，采集占比")
    @GetMapping("/etlTask")
    public ResultBean etlTask() {
        List<TbStatisticsEtlTask> tasks = etlTaskService.list(new QueryWrapper<TbStatisticsEtlTask>().eq("flag", Constant.FLAG_ABLE));
        long total = tasks.stream().mapToLong(x -> x.getTotal()).sum();
        for (TbStatisticsEtlTask task : tasks) {
            task.setTotalPercent(calcPercent(task.getTotal(), total));
        }
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("total", total);
        dataMap.put("list", tasks);
        return ResultBean.success(dataMap);
    }

    /**
     * 计算百分比
     *
     * @param x
     * @param y
     * @return
     */
    private String calcPercent(long x, long y) {
        double d1 = x * 1.0;
        double d2 = y * 1.0;
        NumberFormat percentInstance = NumberFormat.getPercentInstance();
        // 保留两位小数
        percentInstance.setMinimumFractionDigits(2);
        return percentInstance.format(d1 / d2);
    }

    @ApiOperation(value = "租户总数，租户订阅服务排行")
    @GetMapping("/subscription")
    public ResultBean subscription() {
        int tenantTotal = tenantService.count(new QueryWrapper<TbTenant>().eq("del_flag", Constant.DEL_FLAG_UNABLE).eq("account_type", 1));
        List<TbStatisticsSubscription> subscriptions = subscriptionService.list(new QueryWrapper<TbStatisticsSubscription>().eq("flag",
                Constant.FLAG_ABLE));
        // 数据聚合
        Map<String, Long> map = subscriptions.stream().collect(Collectors.groupingBy(TbStatisticsSubscription::getSubscriptionService,
                Collectors.counting()));
        // 转换成list
        List<Map.Entry<String, Long>> entries =
                map.entrySet().stream().sorted(Comparator.comparing(Map.Entry::getValue)).collect(Collectors.toList());
        List<Map<String, Object>> list = new ArrayList<>();
        // 组装数据
        for (int i = entries.size() - 1; i >= 0; i--) {
            Map.Entry<String, Long> x = entries.get(i);
            Map<String, Object> m = new HashMap<>();
            m.put("name", x.getKey());
            m.put("value", x.getValue());
            list.add(m);
        }
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("total", tenantTotal);
        dataMap.put("list", list);
        return ResultBean.success(dataMap);
    }

    @ApiOperation(value = "数据采集量top10")
    @GetMapping("/etlDataTop10")
    public ResultBean etlDataTop10() {
        List<TbStatisticsEtlData> etlDataList =
                etlDataService.list(new QueryWrapper<TbStatisticsEtlData>().eq("flag", Constant.FLAG_ABLE).orderByDesc("etl_count"));
        return ResultBean.success(etlDataList);
    }

    @ApiOperation(value = "数据采集任务趋势")
    @GetMapping("/etlDay")
    public ResultBean etlDay() {
        List<TbStatisticsEtlTaskDay> etlDataDayList =
                etlTaskDayService.list(new QueryWrapper<TbStatisticsEtlTaskDay>().eq("flag", Constant.FLAG_ABLE).orderByAsc("day"));
        return ResultBean.success(etlDataDayList);
    }

    @ApiOperation(value = "租户资源使用情况")
    @PostMapping("/tenantResource")
    public ResultBean resource(@RequestBody TbStatisticsDiskTenantDTO dto) {
        IPage<TbStatisticsDiskTenant> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<TbStatisticsDiskTenant> diskTenants = diskTenantService.page(page, new QueryWrapper<TbStatisticsDiskTenant>().eq("flag",
                Constant.FLAG_ABLE));

        for (TbStatisticsDiskTenant diskTenant : diskTenants.getRecords()) {
            if(diskTenant.getClickhouseUsed() == null){
                diskTenant.setClickhouseUsed(0L);
            }
            if(diskTenant.getClickhouseFree() == null){
                diskTenant.setClickhouseFree(0L);
            }
            if(diskTenant.getMysqlUsed() == null){
                diskTenant.setMysqlUsed(0L);
            }
            if(diskTenant.getMysqlFree() == null){
                diskTenant.setMysqlFree(0L);
            }
            if(diskTenant.getHdfsUsed() == null){
                diskTenant.setHdfsUsed(0L);
            }
            if(diskTenant.getHdfsFree() == null){
                diskTenant.setHdfsFree(0L);
            }
            if(diskTenant.getElasticsearchUsed() == null){
                diskTenant.setElasticsearchUsed(0L);
            }
            if(diskTenant.getElasticsearchFree() == null){
                diskTenant.setElasticsearchFree(0L);
            }
        }

        final List<TbTenantCluster> list = tenantClusterService.list(new QueryWrapper<TbTenantCluster>()
                .in("tenant_id", diskTenants.getRecords().stream().map(TbStatisticsDiskTenant::getTenantId).collect(Collectors.toList())));
        JSONObject jsonObject= getJSONObject(list);
        diskTenants.getRecords().forEach(record -> {
            record.setClickhouseDisplay(calcMb2Tb(record.getClickhouseUsed()) + "/" + calcMb2Tb(record.getClickhouseFree()));
            record.setHdfsDisplay(calcMb2Tb(record.getHdfsUsed()) + "/" + calcMb2Tb(record.getHdfsFree()));
            record.setMysqlDisplay(calcMb2Tb(record.getMysqlUsed()) + "/" + calcMb2Tb(record.getMysqlFree()));
            record.setElasticsearchDisplay(calcMb2Tb(record.getElasticsearchUsed()) + "/" + calcMb2Tb(record.getElasticsearchFree()));
            record.setAlarm(getAlarmInfo(record,jsonObject.getJSONObject(record.getTenantId().toString())));
        });
        return ResultBean.success(diskTenants);
    }

    private String getAlarmInfo(TbStatisticsDiskTenant record, JSONObject json) {
        if(json != null){
            List<String> list = new ArrayList<>();

            if (json.get("clickhouse")!=null&&Double.valueOf(calcMb2Tb(record.getClickhouseUsed()))>json.getDouble("clickhouse")){
                list.add("clickhouse");
            }
            if (json.get("hdfs")!=null&&Double.valueOf(calcMb2Tb(record.getHdfsUsed()))>json.getDouble("hdfs")){
                list.add("hdfs");
            }
            if (json.get("mysql")!=null&&Double.valueOf(calcMb2Tb(record.getMysqlUsed()))>json.getDouble("mysql")){
                list.add("mysql");
            }
            if (json.get("elasticsearch")!=null&&Double.valueOf(calcMb2Tb(record.getElasticsearchUsed()))>json.getDouble("elasticsearch")){
                list.add("elasticsearch");
            }

            return list.size()>0?StringUtils.join(list,"、")+"容量已超预期":"/";
        }else{
            return "-";
        }

    }

    private JSONObject getJSONObject(List<TbTenantCluster> list) {
        JSONObject jsonObject= new JSONObject();
        for (TbTenantCluster tbTenantCluster : list) {
            if (StringUtils.isEmpty(tbTenantCluster.getQuota())){
                continue;
            }
            if ("0".equals(tbTenantCluster.getQuota())){
                continue;
            }
            JSONObject json=new JSONObject();
            json.put(tbTenantCluster.getClusterType(),tbTenantCluster.getQuota());
            if (jsonObject.getJSONObject(tbTenantCluster.getTenantId().toString())!=null){
                jsonObject.getJSONObject(tbTenantCluster.getTenantId().toString()).putAll(json);
            }else {

                jsonObject.put(tbTenantCluster.getTenantId().toString(),json);
            }
        }
        return jsonObject;
    }

    @ApiOperation(value = "接收服务器磁盘信息")
    @PostMapping("/diskInfoSync")
    public ResultBean diskInfoSync(@RequestBody List<TbStatisticsDiskSyncDTO> dtos) {
        List<TbStatisticsDiskSync> diskSyncs = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (TbStatisticsDiskSyncDTO dto : dtos) {
            TbStatisticsDiskSync diskSync = new TbStatisticsDiskSync();
            BeanUtils.copyProperties(dto, diskSync);
            diskSync.setCreateTime(now);
            diskSyncs.add(diskSync);
        }
        // 先更新原来的数据为无效
        diskSyncService.update(new UpdateWrapper<TbStatisticsDiskSync>()
                .set("flag", Constant.FLAG_UNABLE).eq("ip", diskSyncs.get(0).getIp()).eq("flag", Constant.FLAG_ABLE));
        // 保存新数据
        diskSyncService.saveBatch(diskSyncs);
        logger.info("同步服务器磁盘信息成功：{}", JSON.toJSONString(dtos));
        return ResultBean.success();
    }

    @ApiOperation(value = "接收服务器磁盘信息")
    @PostMapping("/hdfsInfoSync")
    public ResultBean hdfsInfoSync(@RequestBody List<TbStatisticsHdfsSyncDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return ResultBean.fail("无数据");
        }
        List<TbStatisticsHdfsSync> diskSyncs = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (TbStatisticsHdfsSyncDTO dto : dtos) {
            TbStatisticsHdfsSync hdfsSync = new TbStatisticsHdfsSync();
            BeanUtils.copyProperties(dto, hdfsSync);
            hdfsSync.setCreateTime(now);
            diskSyncs.add(hdfsSync);
        }
        // 先更新原来的数据为无效
        hdfsSyncService.update(new UpdateWrapper<TbStatisticsHdfsSync>()
                .set("flag", Constant.FLAG_UNABLE).eq("ip", diskSyncs.get(0).getIp()).eq("flag", Constant.FLAG_ABLE));
        // 保存新数据
        hdfsSyncService.saveBatch(diskSyncs);
        logger.info("同步服务器hdfs磁盘信息成功：{}", JSON.toJSONString(dtos));
        return ResultBean.success();
    }

}
