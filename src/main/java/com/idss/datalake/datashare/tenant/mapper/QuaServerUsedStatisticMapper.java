package com.idss.datalake.datashare.tenant.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.idss.datalake.datashare.tenant.entity.QuaServerUsedStatistic;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.idss.datalake.datashare.tenant.vo.ServerStatisticVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 服务使用频率统计 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface QuaServerUsedStatisticMapper extends BaseMapper<QuaServerUsedStatistic> {

    @Select("SELECT server_type serverType,create_date createDate ,COUNT(*) serverCount FROM qua_server_used_statistic" +
            " ${ew.customSqlSegment}" +
            " group by server_type ,create_date ")
    List<ServerStatisticVO> queryQuaServerUsedStatistic(@Param(Constants.WRAPPER) QueryWrapper<QuaServerUsedStatistic> wrapper);
}
