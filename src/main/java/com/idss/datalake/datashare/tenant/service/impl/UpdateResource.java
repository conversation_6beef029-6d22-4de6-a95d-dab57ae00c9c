//package com.idss.datalake.datashare.tenant.service.impl;
//
//import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
//import com.idss.datalake.common.constants.Constant;
//import com.idss.datalake.common.enums.ResourceStatusEnum;
//import com.idss.datalake.common.util.ClickhouseUtil;
//import com.idss.datalake.common.util.SpringUtil;
//import com.idss.datalake.datashare.cluster.entity.TbCluster;
//import com.idss.datalake.datashare.tenant.entity.TbTenant;
//import com.idss.datalake.datashare.tenant.service.ITbTenantService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.sql.Connection;
//
///**
// * <p>
// * 实例变更
// * </p>
// *
// * <AUTHOR>
// * @since 2022/8/8 0008
// */
//public class UpdateResource implements Runnable {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateResource.class);
//
//    private TbCluster tbCluster;
//    private String datasourceUrl;
//    private String db;
//    private TbCluster mysql;
//    private long tenantId;
//
//    public UpdateResource(TbCluster tbCluster, String datasourceUrl, String db, TbCluster mysql, Long tenantId) {
//        this.tbCluster = tbCluster;
//        this.datasourceUrl = datasourceUrl;
//        this.db = db;
//        this.mysql = mysql;
//        this.tenantId = tenantId;
//    }
//
//    @Override
//    public void run() {
//        ITbTenantService tenantService = (ITbTenantService) SpringUtil.getBean("tbTenantServiceImpl");
//
//        Connection dbConnection = null;
//        dbConnection = ClickhouseUtil.getConnect(String.format(datasourceUrl, db), tbCluster.getClusterUsername(), tbCluster.getClusterPassword());
//        if (dbConnection != null) {
//            try {
//                ClickhouseUtil.initTenantScript(dbConnection, tenantId, db, mysql, tbCluster.getId());
//            } catch (Exception e) {
//                tenantService.update(new UpdateWrapper<TbTenant>()
//                        .set("RESOURCE_STATUS", ResourceStatusEnum.ALLOT_FAIL.getCode())
//                        .eq("id",tenantId)
//                        .eq("del_flag", Constant.DEL_FLAG_UNABLE));
//            } finally {
//                ClickhouseUtil.close(dbConnection);
//            }
//        }
//        tenantService.update(new UpdateWrapper<TbTenant>()
//                .set("RESOURCE_STATUS", ResourceStatusEnum.ALLOT_SUCCESS.getCode())
//                .eq("id",tenantId)
//                .eq("del_flag", Constant.DEL_FLAG_UNABLE));
//
//    }
//
//}
