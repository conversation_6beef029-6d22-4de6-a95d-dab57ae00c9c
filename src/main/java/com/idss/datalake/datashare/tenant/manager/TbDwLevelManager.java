/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-01
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-01
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.tenant.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idss.common.util.ReflectionUtil;
import com.idss.datalake.common.bean.GlobalDTO;
import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.exception.ParamInvalidException;
import com.idss.datalake.common.util.CamelToUnderUtil;
import com.idss.datalake.common.util.JSONUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datashare.tenant.dto.TbDwLevelDTO;
import com.idss.datalake.datashare.tenant.entity.TbDwLevel;
import com.idss.datalake.datashare.tenant.entity.TbDwLevelTenant;
import com.idss.datalake.datashare.tenant.service.ITbDwLevelService;
import com.idss.datalake.datashare.tenant.service.ITbDwLevelTenantService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description <p>层级管理 manager处理类</p>
 * @since 2024-11-01
 */
@Component
public class TbDwLevelManager {
    private static final Logger logger = LoggerFactory.getLogger(TbDwLevelManager.class);

    @Autowired
    private ITbDwLevelService iTbDwLevelService;
    @Autowired
    private ITbDwLevelTenantService dwLevelTenantService;

    public Map<String, Object> createPageCondition(RequestDTO requestDTO) {
        if (requestDTO != null && requestDTO.getGlobal() != null) {
            GlobalDTO globalDTO = requestDTO.getGlobal();
            if (globalDTO.getPageNum() == null) {
                globalDTO.setPageNum(Constant.DEFAULT_PAGE_NUM);
            }
            if (globalDTO.getPageSize() == null) {
                globalDTO.setPageSize(Constant.DEFAULT_PAGE_SIZE);
            }
            if (StringUtils.isEmpty(globalDTO.getOrderType())) {
                globalDTO.setOrderType("desc");
            }
            if (StringUtils.isEmpty(globalDTO.getOrderField())) {
                globalDTO.setOrderField("createTime");
            }
        } else {
            throw new ParamInvalidException("入参错误");
        }
        return null;
    }

    /**
     * 分页
     *
     * @param requestDTO
     * @return
     * @throws Exception
     */
    public Map<String, Object> page(RequestDTO requestDTO) throws Exception {
        Map<String, Object> result = new HashMap<>();
        createPageCondition(requestDTO);
        Page<TbDwLevel> page = new Page<>(requestDTO.getGlobal().getPageNum(), requestDTO.getGlobal().getPageSize());
        QueryWrapper<TbDwLevel> queryWrapper = new QueryWrapper<>();
        if (requestDTO.getParam() != null) {
            TbDwLevelDTO dto = JSONUtil.objectMapperToObj(requestDTO.getParam(), TbDwLevelDTO.class);
            if (StringUtils.isNotBlank(dto.getCnName())) {
                queryWrapper.like("cn_name", dto.getCnName());
            }
        }
        if (requestDTO.getGlobal() != null) {
            queryWrapper.orderBy(true, "asc".equalsIgnoreCase(requestDTO.getGlobal().getOrderType()),
                    CamelToUnderUtil.underField(requestDTO.getGlobal().getOrderField()));
        }

        IPage<TbDwLevel> pageResult = iTbDwLevelService.page(page, queryWrapper);

        result.put("pageNum", pageResult.getCurrent());
        result.put("pageSize", pageResult.getSize());
        result.put("total", pageResult.getTotal());
        result.put("data", pageResult.getRecords());
        return result;
    }

    public void create(TbDwLevelDTO dto) {
        if (StringUtils.isBlank(dto.getCnName())) {
            throw new ParamInvalidException("入参异常");
        }

        TbDwLevel tbDwLevel = new TbDwLevel();
        ReflectionUtil.copyLomBokProperties(dto, tbDwLevel);
        tbDwLevel.setCreateTime(LocalDateTime.now());
        tbDwLevel.setUpdateTime(LocalDateTime.now());
        tbDwLevel.setCreateUser(UserUtil.getCurrentUsername());
        tbDwLevel.setUpdateUser(UserUtil.getCurrentUsername());
        iTbDwLevelService.save(tbDwLevel);
    }

    public void delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ParamInvalidException("入参异常");
        }
        int count = dwLevelTenantService.count(new LambdaUpdateWrapper<TbDwLevelTenant>().in(TbDwLevelTenant::getDwLevelId, ids));
        if (count > 0) {
            throw new ParamInvalidException("当前层级已被租户使用，请先取消用户层级使用之后，再来删除层级");
        }

        iTbDwLevelService.remove(new QueryWrapper<TbDwLevel>().in("id", ids));
    }

    public void edit(TbDwLevelDTO dto) {
        if (ObjectUtils.isEmpty(dto.getId())) {
            throw new ParamInvalidException("入参异常");
        }

        TbDwLevel dbOne = iTbDwLevelService.getById(dto.getId());
        ReflectionUtil.copyLomBokProperties(dto, dbOne);
        dbOne.setUpdateTime(LocalDateTime.now());
        dbOne.setUpdateUser(UserUtil.getCurrentUsername());
        iTbDwLevelService.updateById(dbOne);
    }

    public TbDwLevel detail(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new ParamInvalidException("入参异常");
        }
        return iTbDwLevelService.getById(id);
    }

    /**
     * 获取所有层级
     *
     * @return
     */
    public List<TbDwLevel> list() {
        return iTbDwLevelService.list();
    }

    /**
     * 获取当前租户下所有层级
     *
     * @return
     */
    public List<TbDwLevel> tenantList() {
        Long tenantId = UserUtil.getLongCurrentTenantId();
        List<TbDwLevelTenant> dwLevelTenants = dwLevelTenantService.list(new QueryWrapper<TbDwLevelTenant>().eq("tenant_id", tenantId));
        if (CollectionUtils.isNotEmpty(dwLevelTenants)) {
            List<Long> dwLevelIds = dwLevelTenants.stream().map(x -> x.getDwLevelId()).collect(Collectors.toList());
            return iTbDwLevelService.list(new QueryWrapper<TbDwLevel>().in("id", dwLevelIds));
        }
        return null;
    }

}