/************************ <PERSON>AN<PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-10-20
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wj, 2022-10-20
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.tenant.dto;

import com.idss.datalake.datashare.tenant.entity.QuaServerUsedStatistic;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description <p>服务使用频率统计 dto类</p>
 * @since 2022-10-20
 */
@Data
public class QuaServerUsedStatisticDTO extends QuaServerUsedStatistic {
    private List<Integer> ids;
    private List<String> createTimes;
}