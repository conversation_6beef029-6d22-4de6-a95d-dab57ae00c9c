/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-01
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-01
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.tenant.controller;


import com.idss.datalake.common.bean.RequestDTO;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datashare.tenant.dto.TbDwLevelDTO;
import com.idss.datalake.datashare.tenant.manager.TbDwLevelManager;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 层级管理
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/tb-dw-level")
public class TbDwLevelController {
    private static final Logger logger = LoggerFactory.getLogger(TbDwLevelController.class);
    @Autowired
    private TbDwLevelManager tbDwLevelManager;

    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public ResultBean create(@RequestBody TbDwLevelDTO dto) {
        try {
            tbDwLevelManager.create(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public ResultBean delete(@RequestBody TbDwLevelDTO dto) {
        try {
            tbDwLevelManager.delete(dto.getIds());
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}")
    public ResultBean detail(@PathVariable Long id) {
        try {
            return ResultBean.success(tbDwLevelManager.detail(id));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "编辑")
    @PostMapping(value = "/edit")
    public ResultBean edit(@RequestBody TbDwLevelDTO dto) {
        try {
            tbDwLevelManager.edit(dto);
            return ResultBean.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询列表")
    @PostMapping(value = "/page")
    public ResultBean page(@RequestBody RequestDTO requestDTO) {
        try {
            return ResultBean.success(tbDwLevelManager.page(requestDTO));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询全部")
    @GetMapping(value = "/list")
    public ResultBean list() {
        try {
            return ResultBean.success(tbDwLevelManager.list());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取当前租户下所有层级")
    @GetMapping(value = "/tenantList")
    public ResultBean tenantList() {
        try {
            return ResultBean.success(tbDwLevelManager.tenantList());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail(e.getMessage());
        }
    }
}
