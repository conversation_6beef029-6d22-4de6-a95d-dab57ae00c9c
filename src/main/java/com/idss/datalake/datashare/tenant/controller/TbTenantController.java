package com.idss.datalake.datashare.tenant.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.syslog.annotation.SysLog;
import com.idss.datalake.common.syslog.entity.OptModule;
import com.idss.datalake.common.syslog.entity.OptType;
import com.idss.datalake.datashare.tenant.dto.QuaServerUsedStatisticDTO;
import com.idss.datalake.datashare.tenant.entity.QueryTenantBean;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.datashare.tenant.entity.TenantDTO;
import com.idss.datalake.datashare.tenant.service.IQuaServerUsedStatisticService;
import com.idss.datalake.datashare.tenant.service.ITbTenantService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 租户管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-30
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/tenant")
public class TbTenantController {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ITbTenantService sysTenantService;
    @Autowired
    private IQuaServerUsedStatisticService standardService;

    @PostMapping("/save")
    @Transactional
    @SysLog(logName = "租户保存", optType = OptType.ADD, optModule = OptModule.TENANT_MANAGE, switchStatus = true)
    public ResultBean save(@RequestBody TenantDTO tenantDTO) throws CloneNotSupportedException {
        logger.info("保存租户信息:{}", JSONObject.toJSONString(tenantDTO));
        return this.sysTenantService.saveDatalakeSysTenant(tenantDTO);
    }

    @PutMapping("/{id}")
    @SysLog(logName = "租户编辑", optType = OptType.EDIT, optModule = OptModule.TENANT_MANAGE, switchStatus = true)
    public ResultBean update(@PathVariable("id") String id, @RequestBody TenantDTO tenantDTO) throws CloneNotSupportedException {
        return this.sysTenantService.updateDatalakeSysTenant(id, tenantDTO);
    }

    @GetMapping("/{id}")
    public ResultBean info(@PathVariable("id") String id) {
        return ResultBean.success(this.sysTenantService.getById(Long.valueOf(id)));
    }

    @PostMapping("/delete/{id}")
    @SysLog(logName = "租户删除", optType = OptType.DELETE, optModule = OptModule.TENANT_MANAGE, switchStatus = true)
    public ResultBean delete(@PathVariable("id") String id) {
        return this.sysTenantService.deleteTenant(Long.valueOf(id));
    }

    @PostMapping("/page")
    public ResultBean page(@RequestBody QueryTenantBean queryTenantBean) {
        return this.sysTenantService.page(queryTenantBean);
    }

    @GetMapping("/succTenant")
    public ResultBean succTenant() {
        List<TbTenant> tenants = sysTenantService.list(new QueryWrapper<TbTenant>().eq("DEL_FLAG", Constant.DEL_FLAG_UNABLE)
                .eq("RESOURCE_STATUS", 2)
                .eq("account_type", "1"));
        return ResultBean.success(tenants);
    }

    @GetMapping("/gpl")
    public List<Map<String, Object>> gpl() {
        return this.sysTenantService.gpl();
    }

    /**
     * 查询服务频率统计
     *
     * @param dto
     * @return
     */
    @PostMapping("/serverStatistic")
    public ResultBean queryServerStatistic(@RequestBody QuaServerUsedStatisticDTO dto) {
        try {
            return ResultBean.success(standardService.queryQuaServerUsedStatistic(dto));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultBean.fail("查询服务频率异常");
        }
    }

    /**
     * 查看连接方式
     *
     * @param id
     * @return
     */
    @GetMapping("/showClusterAddress/{id}")
    public ResultBean showClusterAddress(@PathVariable("id") Long id) {
        return ResultBean.success(this.sysTenantService.showClusterAddress(id));
    }
}
