package com.idss.datalake.datashare.tenant.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idss.datalake.common.util.DateUtils;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datashare.tenant.dto.QuaServerUsedStatisticDTO;
import com.idss.datalake.datashare.tenant.entity.QuaServerUsedStatistic;
import com.idss.datalake.datashare.tenant.enums.ServerStatisticType;
import com.idss.datalake.datashare.tenant.mapper.QuaServerUsedStatisticMapper;
import com.idss.datalake.datashare.tenant.service.IQuaServerUsedStatisticService;
import com.idss.datalake.datashare.tenant.vo.ServerStatisticVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务使用频率统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Slf4j
@Service
public class QuaServerUsedStatisticServiceImpl extends ServiceImpl<QuaServerUsedStatisticMapper, QuaServerUsedStatistic> implements IQuaServerUsedStatisticService {

    @Resource
    QuaServerUsedStatisticMapper statisticMapper;

    @Override
    public void saveQuaServerUsedStatistic(List<QuaServerUsedStatisticDTO> dtos) {
        try {
            List<QuaServerUsedStatistic> stats = new ArrayList<>();
            for (QuaServerUsedStatisticDTO dto : dtos) {
                QuaServerUsedStatistic statistic = new QuaServerUsedStatistic();
                statistic.setTenantId(dto.getTenantId());
                statistic.setServerType(dto.getServerType());
                statistic.setCreateTime(dto.getCreateTime());
                statistic.setCreateDate(DateUtils.localDateTimeToString(statistic.getCreateTime(), DateUtils.YYYYMMDD_HYPHEN));
                stats.add(statistic);
            }
            log.info("data-api服务使用频率数据：{}", JSON.toJSONString(stats));
            this.saveBatch(stats);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void main(String[] args) {
        String s = DateUtils.localDateTimeToString(LocalDateTime.now(), DateUtils.YYYYMMDD_HYPHEN);
        System.out.println(s);
    }

    @Override
    public void saveQuaServerUsedStatistic(String serverType) {
        try {
            QuaServerUsedStatistic statistic = new QuaServerUsedStatistic();
            statistic.setTenantId(UserUtil.getCurrentTenantId());
            statistic.setServerType(serverType);
            statistic.setCreateTime(LocalDateTime.now());
            statistic.setCreateDate(DateUtils.localDateTimeToString(statistic.getCreateTime(), DateUtils.YYYYMMDD_HYPHEN));
            this.save(statistic);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    @Override
    public List<ServerStatisticVO> queryQuaServerUsedStatistic(QuaServerUsedStatisticDTO dto) {
        List<String> createTimes = dto.getCreateTimes();
        if (CollectionUtils.isEmpty(createTimes)) {
            throw new RuntimeException("日期不可为空");
        }
        String dateStartStr = createTimes.get(0);
        String dateEndStr = createTimes.get(1);
        QueryWrapper<QuaServerUsedStatistic> wrapper = new QueryWrapper<>();
        wrapper.eq("tenant_id", UserUtil.getCurrentTenantId());
        wrapper.between("create_time", dateStartStr + " 00:00:00", createTimes.get(1) + " 23:59:59");
        List<ServerStatisticVO> statisticVOS = statisticMapper.queryQuaServerUsedStatistic(wrapper);
        // 计算所有日期
        List<String> dateList = new ArrayList<>();
        Date dateStart = DateUtils.convertToDate(dateStartStr, DateUtils.YYYYMMDD_HYPHEN);
        dateList.add(dateStartStr);
        Date dateEnd = DateUtils.convertToDate(dateEndStr, DateUtils.YYYYMMDD_HYPHEN);
        int daysBetween = Integer.valueOf(DateUtils.getDaysBetween(dateStart, dateEnd));
        for (int i = 1; i < daysBetween; i++) {
            Date perDate = DateUtils.addDay(dateStart, i);
            dateList.add(DateUtils.convertToString(perDate, DateUtils.YYYYMMDD_HYPHEN));
        }
        dateList.add(dateEndStr);

        statisticVOS.stream().forEach(vo -> {
            vo.setServerName(ServerStatisticType.getNameByType(vo.getServerType()));
        });
        // 按类型过滤
        Map<String, List<ServerStatisticVO>> serverTypeGroupMap =
                statisticVOS.stream().collect(Collectors.groupingBy(ServerStatisticVO::getServerType));
        for (ServerStatisticType statisticType : ServerStatisticType.values()) {
            // 是否存在此类型
            if (serverTypeGroupMap.containsKey(statisticType.getServerType())) {
                List<ServerStatisticVO> serverStatisticVOS = serverTypeGroupMap.get((statisticType.getServerType()));
                List<String> dbCreateDates = serverStatisticVOS.stream().map(vo -> vo.getCreateDate()).collect(Collectors.toList());
                // 取未统计的日期
                List<String> noStatisticDate = dateList.stream().filter(item -> !dbCreateDates.contains(item)).collect(Collectors.toList());
                // 填充未统计日期
                fillStatisticDate(statisticVOS, noStatisticDate, statisticType);
            } else {
                fillStatisticDate(statisticVOS, dateList, statisticType);
            }
        }
        return statisticVOS;
    }

    /**
     * 填充未统计日期
     *
     * @param statisticVOS
     * @param dateList
     * @param statisticType
     */
    private void fillStatisticDate(List<ServerStatisticVO> statisticVOS, List<String> dateList, ServerStatisticType statisticType) {
        for (String statisticsDate : dateList) {
            ServerStatisticVO vo = new ServerStatisticVO();
            vo.setServerType(statisticType.getServerType());
            vo.setCreateDate(statisticsDate);
            vo.setServerCount(0);
            vo.setServerName(statisticType.getServerName());
            statisticVOS.add(vo);
        }
    }

}
