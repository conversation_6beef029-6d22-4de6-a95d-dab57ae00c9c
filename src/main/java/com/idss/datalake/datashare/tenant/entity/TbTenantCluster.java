package com.idss.datalake.datashare.tenant.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description : <p>租户集群关联表</p>
 * @see：
 * @since 2022/7/7 0007
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_tenant_cluster")
public class TbTenantCluster implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 集群id
     */
    private Long clusterId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 集群类型
     */
    private String clusterType;

    /**
     * 实例名
     */
    private String instance;

    /**
     * topic
     */
    private String topic;

    /**
     * 配额
     */
    private String quota;

    /**
     * 分片数
     */
    private String shards;

    /**
     * 副本数
     */
    private String replicas;

    /**
     * 租户账号
     */
    private String tenantAccount;
    /**
     * 租户密码
     */
    private String tenantPassword;

}

