package com.idss.datalake.datashare.tenant.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.idss.datalake.common.bean.ResultBean;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.datashare.tenant.entity.DicDTO;
import com.idss.datalake.datashare.tenant.entity.TbDic;
import com.idss.datalake.datashare.tenant.mapper.TbDicMapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 字典
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/13 0013
 */
@RestController
@RequestMapping(Constant.API_PREFIX + "/dic")
public class TbDicController {


    @Resource
    private TbDicMapper dicMapper;

    @GetMapping("/get/{dic}")
    public ResultBean get(@PathVariable("dic") String dic) {
        final TbDic tbDic = this.dicMapper.selectOne(
                new QueryWrapper<TbDic>()
                        .eq("alias", dic)
                        .eq("parent_id", 0));
        return ResultBean.success(getChildren(tbDic));
    }

    private List<DicDTO> getChildren(TbDic tbDic) {
        List<DicDTO> list = new ArrayList<>();
        final List<TbDic> tbDicList = this.dicMapper.selectList(
                new QueryWrapper<TbDic>()
                        .eq("parent_id", tbDic.getId()));
        if (tbDicList.size() > 0) {
            for (TbDic x : tbDicList) {
                DicDTO dicDTO = new DicDTO();
                dicDTO.setId(x.getId());
                dicDTO.setName(x.getName());
                dicDTO.setAlias(x.getAlias());
                dicDTO.setHidden(x.getHidden());
                dicDTO.setUniqueCheckCode(x.getUniqueCheckCode());
                dicDTO.setParentId(x.getParentId());
                dicDTO.setChildren(getChildren(x));
                list.add(dicDTO);
            }
        }
        return list;
    }
}
