/************************ <PERSON>ANGE REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE *
 * DESCRIPTION OF CHANGE: modify(M),add(+),del(-) *
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-01
 * create
 *-----------------------------------------------------------------------------*
 * V1.0, wangjun, 2024-11-01
 * M {modifyComment}
 * - {delComment}
 * + {addCommnet}
 \*************************** END OF CHANGE REPORT HISTORY ********************/

package com.idss.datalake.datashare.tenant.dto;

import com.idss.datalake.datashare.tenant.entity.TbDwLevel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description <p>层级管理 dto类</p>
 * @since 2024-11-01
 */
@Data
public class TbDwLevelDTO extends TbDwLevel {
    private List<Long> ids;
}