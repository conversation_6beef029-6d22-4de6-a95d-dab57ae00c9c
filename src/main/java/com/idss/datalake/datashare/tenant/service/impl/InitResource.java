/************************ <PERSON><PERSON><PERSON> REPORT HISTORY ******************************\
 ** Product VERSION,UPDATED BY,UPDATE DATE                                     *
 *   DESCRIPTION OF CHANGE: modify(M),add(+),del(-)                             *
 *-----------------------------------------------------------------------------*
 * V1.0,李茂杰,2022/8/4 11:35
 * create 注释
 \*************************** END OF CHANGE REPORT HISTORY ********************/
package com.idss.datalake.datashare.tenant.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.idss.datalake.common.constants.Constant;
import com.idss.datalake.common.enums.ResourceStatusEnum;
import com.idss.datalake.common.util.AESUtil;
import com.idss.datalake.common.util.ClickhouseUtil;
import com.idss.datalake.common.util.HdfsUtil;
import com.idss.datalake.common.util.HiveUtil;
import com.idss.datalake.common.util.KafkaUtil;
import com.idss.datalake.common.util.MysqlUtil;
import com.idss.datalake.common.util.SpringUtil;
import com.idss.datalake.common.util.StringUtil;
import com.idss.datalake.common.util.UserUtil;
import com.idss.datalake.datashare.cluster.entity.TbCluster;
import com.idss.datalake.datashare.cluster.service.ITbClusterService;
import com.idss.datalake.datashare.tenant.entity.TbDatasourceInfo;
import com.idss.datalake.datashare.tenant.entity.TbTenant;
import com.idss.datalake.datashare.tenant.entity.TbTenantCluster;
import com.idss.datalake.datashare.tenant.entity.TenantDTO;
import com.idss.datalake.datashare.tenant.service.ITbDatasourceInfoService;
import com.idss.datalake.datashare.tenant.service.ITbTenantClusterService;
import com.idss.datalake.datashare.tenant.service.ITbTenantService;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.fs.FileSystem;
import org.apache.kafka.clients.admin.AdminClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * Copyright 2022 IDSS
 * <p> 资源初始化(初始化租户的数据库、表、初始化数据)
 * All right reserved.
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version Create on: 2022/8/4 11:35
 */
public class InitResource implements Runnable {

    private static final Logger LOGGER = LoggerFactory.getLogger(InitResource.class);

    private TenantDTO tenantDTO;

    public InitResource(TenantDTO tenantDTO) {
        this.tenantDTO = tenantDTO;
    }

    @Override
    public void run() {
        LOGGER.info("集中处理初始化资源, {}", JSONObject.toJSONString(tenantDTO));
        ITbTenantClusterService tenantClusterService = (ITbTenantClusterService) SpringUtil.getBean("tbTenantClusterServiceImpl");
        ITbTenantService tenantService = (ITbTenantService) SpringUtil.getBean("tbTenantServiceImpl");
        String username = UserUtil.getCurrentUsername();
        long tenantId = tenantDTO.getId();
        List<TbTenantCluster> clusterList = tenantDTO.getClusterList();
        TbCluster mysql = new TbCluster();
        try {
            for (int i = clusterList.size() - 1; i >= 0; i--) {
                TbTenantCluster x = clusterList.get(i);
                if (!StringUtil.isNotEmpty(x.getClusterId())) {
                    continue;
                }
                x.setTenantId(tenantId);
                String instance = x.getInstance();
                if (StringUtil.isEmpty(instance) && !"kafka".equals(x.getClusterType())) {
                    instance = tenantDTO.getTenantCode();
                    x.setInstance(tenantDTO.getTenantCode());
                }
                LOGGER.info("初始化{}资源, instance:{}", x.getClusterType(), x.getInstance());
                switch (x.getClusterType()) {
                    case "panwei":
                    case "mysql": {
                        mysql = mysqlInit(tenantId, instance, username, x);
                        // remoteMysqlInit(tenantId, username, x, instance);
                    }
                    break;
                    case "clickhouse": {
                        clickhouseInit(tenantId, username, x, instance, mysql);
                    }
                    break;
                    case "kafka": {
                        if (StringUtil.isEmpty(x.getTopic())) {
                            x.setTopic(tenantDTO.getTenantCode());
                            kafkaInit(x, x.getTopic());
                        }
                    }
                    break;
                    case "hdfs": {
                        hdfsInit(x, instance);
                    }
                    break;
                    case "hive": {
                        hiveInit(tenantId, x, instance, username);
                    }
                    break;
                    default: {
                        tenantClusterService.save(x);
                    }
                }
            }
            LOGGER.info("分配租户资源成功");
            tenantService.update(new UpdateWrapper<TbTenant>()
                    .set("RESOURCE_STATUS", ResourceStatusEnum.ALLOT_SUCCESS.getCode())
                    .set("db_name", mysql.getDbName())
                    .eq("id", tenantDTO.getId()));
        } catch (Exception e) {
            LOGGER.error("初始化租户资源错误", e);
            tenantService.update(new UpdateWrapper<TbTenant>()
                    .set("RESOURCE_STATUS", ResourceStatusEnum.ALLOT_FAIL.getCode())
                    .eq("id", tenantDTO.getId()));
        }
    }

    private void hiveInit(Long tenantId, TbTenantCluster tenantCluster, String instance, String username) {
        ITbClusterService clusterService = (ITbClusterService) SpringUtil.getBean("tbClusterServiceImpl");
        ITbTenantClusterService tenantClusterService = (ITbTenantClusterService) SpringUtil.getBean("tbTenantClusterServiceImpl");
        TbCluster tbCluster = clusterService.getById(tenantCluster.getClusterId());
        if (tbCluster != null) {
            String url = "";
            String instanceUrl = "";
            if (StringUtils.equals(tbCluster.getEnableKbs(), "1")) {
                //eg. *****************************************************/<EMAIL>
                //eg. ***********************************,hdp01.bigdata.com:2181,hdp02.bigdata.com:2181/;serviceDiscoveryMode=zooKeeper;
                // zooKeeperNamespace=hiveserver2
                //eg. ***********************************,hdp01.bigdata.com:2181,hdp02.bigdata.com:2181/default;serviceDiscoveryMode=zooKeeper;
                // zooKeeperNamespace=hiveserver2
                url = tbCluster.getJdbcUrl();
                // 移除 "jdbc:hive2://" 前缀
                String uriPart = url.replace("jdbc:hive2://", "");

                // 分割地址和参数部分
                String[] parts = uriPart.split(";", 2);
                String addressAndDb = parts[0];
                String params = parts.length > 1 ? parts[1] : "";

                // 分割地址和数据库
                int lastSlashIndex = addressAndDb.lastIndexOf("/");
                String addresses = lastSlashIndex > 0 ? addressAndDb.substring(0, lastSlashIndex) : addressAndDb;
                instanceUrl = "jdbc:hive2://" + addresses + "/" + instance + ";" + params;
            } else {
                String strTmp = Constant.DATASOURCE_URL_HIVE + tbCluster.getNodeAddress() + "/%s";
                url = String.format(strTmp, "default");
                instanceUrl = String.format(strTmp, instance);
            }
            LOGGER.info("instanceUrl: {}", instanceUrl);
            try {
                Connection conn = HiveUtil.getConnect(url, tbCluster.getClusterUsername(), AESUtil.defaultDecrypt(tbCluster.getClusterPassword()),
                        tbCluster.getEnableKbs(), tbCluster.getKbsAccount(), tbCluster.getKeyTabPath(), tbCluster.getKrb5ConfPath());
                HiveUtil.createDb(conn, instance);
                insertDatasource(tenantId, username, tbCluster, Constant.DATASOURCE_TYPE_HIVE, Constant.DATASOURCE_DRIVER_HIVE,
                        instanceUrl, tenantCluster.getTenantAccount(), tenantCluster.getTenantPassword());
                tenantCluster.setInstance(instance);
            } catch (Exception e) {
                LOGGER.error("hive连接失败,{}", e.getMessage(), e);
            }
            tenantClusterService.save(tenantCluster);
        }
    }

    private void hdfsInit(TbTenantCluster tenantCluster, String instance) {
        ITbClusterService clusterService = (ITbClusterService) SpringUtil.getBean("tbClusterServiceImpl");
        ITbTenantClusterService tenantClusterService = (ITbTenantClusterService) SpringUtil.getBean("tbTenantClusterServiceImpl");
        TbCluster tbCluster = clusterService.getById(tenantCluster.getClusterId());
        if (tbCluster != null && StringUtils.isNotEmpty(tbCluster.getNodeAddress())) {
            String firstNode = tbCluster.getNodeAddress().split(",")[0];
            String url = Constant.DATASOURCE_URL_HDFS + firstNode;
            try {
                FileSystem hdfs = HdfsUtil.getHdfs(url);
                String path = instance;
                int num = 1;
                while (HdfsUtil.exists(hdfs, path)) {
                    path = instance + "00" + num;
                    num++;
                }
                HdfsUtil.mkdir(hdfs, path);
                hdfs.close();
                tenantCluster.setInstance(path);
            } catch (Exception e) {
                LOGGER.error("hdfs连接失败");
                LOGGER.error(e.getMessage(), e);
            }

            tenantClusterService.save(tenantCluster);
        }
    }

    private void kafkaInit(TbTenantCluster tenantCluster, String code) {
        ITbClusterService clusterService = (ITbClusterService) SpringUtil.getBean("tbClusterServiceImpl");
        ITbTenantClusterService tenantClusterService = (ITbTenantClusterService) SpringUtil.getBean("tbTenantClusterServiceImpl");
        TbCluster tbCluster = clusterService.getById(tenantCluster.getClusterId());
        if (tbCluster != null && StringUtils.isNotEmpty(tbCluster.getNodeAddress())) {
            AdminClient client = KafkaUtil.getClient(tbCluster.getNodeAddress(), tbCluster.getClusterAuthType(), tbCluster.getAuthConfigurationId());
            try {

                if (client != null) {
                    String topic = code;
                    int num = 1;
                    while (KafkaUtil.topicExist(client, topic)) {
                        topic = code + "_00" + num;
                        num++;
                    }
                    Integer partitions = 3;
                    int i = 1;
                    if (StringUtil.isNotEmpty(tenantCluster.getShards())) {
                        partitions = Integer.valueOf(tenantCluster.getShards());
                    }
                    if (StringUtil.isNotEmpty(tenantCluster.getReplicas())) {
                        i = Integer.valueOf(tenantCluster.getReplicas());
                    }
                    KafkaUtil.createTopic(client, topic, partitions, (short) i);
                    KafkaUtil.close(client);
                }
            } catch (ExecutionException | InterruptedException e) {
                LOGGER.error("kafka连接失败");
                LOGGER.error(e.getMessage(), e);
            }
            tenantClusterService.save(tenantCluster);
        }
    }

    /**
     * @description mysql初始化
     * <AUTHOR>
     * @date 2022/7/20 0020 14:00
     */
    private TbCluster mysqlInit(Long tenantId, String instance, String username, TbTenantCluster tenantCluster) throws Exception {
        LOGGER.info("开始创建磐维资源......");
        ITbClusterService clusterService = (ITbClusterService) SpringUtil.getBean("tbClusterServiceImpl");
        ITbTenantClusterService tenantClusterService = (ITbTenantClusterService) SpringUtil.getBean("tbTenantClusterServiceImpl");
        TbCluster tbCluster = clusterService.getById(tenantCluster.getClusterId());
        String dbUsername = tbCluster.getClusterUsername();
        String password = tbCluster.getClusterPassword();
        String datasourceUrl = Constant.DATASOURCE_URL_DATABASE + tbCluster.getNodeAddress() + "/%s";
        Connection connection = null;
        Connection dbConnection = null;
        String db = instance;
        String schema = instance;
        TbCluster cluster = new TbCluster();
        try {
            connection = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_DATABASE, String.format(datasourceUrl,
                    Constant.DATASOURCE_DRIVER_DEFAULT_DB), dbUsername, AESUtil.defaultDecrypt(password));
            if (connection != null) {
                if (!MysqlUtil.queryDb(connection, db)) {
                    LOGGER.info("磐维数据库{}/{}已存在", tbCluster.getNodeAddress(), instance);
                } else {
                    MysqlUtil.createDb(connection, db);
                }
                // 初始化数据库脚本
                datasourceUrl = Constant.DATASOURCE_URL_DATABASE + tbCluster.getNodeAddress() + "/%s?currentSchema=%s";
                dbConnection = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_DATABASE, String.format(datasourceUrl, db, schema), dbUsername,
                        AESUtil.defaultDecrypt(password));
                if (dbConnection != null) {
                    if (!MysqlUtil.querySchema(dbConnection, db, schema)) {
                        LOGGER.info("schema{}已存在", schema);
                    } else {
                        MysqlUtil.createSchema(dbConnection, schema);
                    }
                    MysqlUtil.initTenantScript(dbConnection, tenantId, tenantCluster.getClusterId());
                } else {
                    throw new Exception("集群连接失败");
                }

                cluster.setId(tenantCluster.getClusterId());
                // cluster.setClusterIp(tbCluster.getClusterIp());
                // cluster.setClusterPort(tbCluster.getClusterPort());
                cluster.setNodeAddress(tbCluster.getNodeAddress());
                cluster.setDbName(db);
                cluster.setClusterUsername(dbUsername);
                cluster.setClusterPassword(tbCluster.getClusterPassword());
                insertDatasource(tenantId, username, cluster, Constant.DATASOURCE_TYPE_DEFAULT, Constant.DATASOURCE_DRIVER_DATABASE,
                        String.format(datasourceUrl, db, schema), tenantCluster.getTenantAccount(), tenantCluster.getTenantPassword());

                tenantCluster.setInstance(db);
                tenantClusterService.save(tenantCluster);
                LOGGER.info("磐维资源创建创建成功");
            } else {
                throw new Exception("集群连接失败");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception(e.getMessage());
        } finally {
            if (connection != null) {
                MysqlUtil.close(connection);
            }
            if (dbConnection != null) {
                MysqlUtil.close(dbConnection);
            }
        }
        return cluster;
    }

    // /**
    //  * @description mysql初始化
    //  * <AUTHOR>
    //  * @date 2022/7/20 0020 14:00
    //  */
    // private void remoteMysqlInit(Long tenantId, String username, TbTenantCluster tenantCluster, String instance) throws Exception {
    //     LOGGER.info("开始创建Mysql资源......");
    //     ITbClusterService clusterService = (ITbClusterService) SpringUtil.getBean("tbClusterServiceImpl");
    //     ITbTenantClusterService tenantClusterService = (ITbTenantClusterService) SpringUtil.getBean("tbTenantClusterServiceImpl");
    //     TbCluster tbCluster = clusterService.getById(tenantCluster.getClusterId());
    //     String datasourceUrl = Constant.DATASOURCE_URL_MYSQL +
    //             tbCluster.getClusterIp().split(",")[0] +
    //             ":" +
    //             tbCluster.getClusterPort() +
    //             "/%s?autoReconnect=true&useUnicode=true&characterEncoding=utf-8&allowMultiQueries=true&&useSSL=false";
    //     Connection connection = null;
    //     try {
    //         connection = MysqlUtil.getConnect(Constant.DATASOURCE_DRIVER_MYSQL, String.format(datasourceUrl, "sys"),
    //                 tbCluster.getClusterUsername(), AESUtil.defaultDecrypt(tbCluster.getClusterPassword()));
    //         if (connection != null) {
    //             if (checkInstance(instance.charAt(0))) {
    //                 LOGGER.error("Mysql数据库名称{}必须已字母开头", instance);
    //             }
    //             if (!MysqlUtil.queryDb(connection, instance)) {
    //                 LOGGER.error("Mysql数据库{}:{}/{}已存在", tbCluster.getClusterIp().split(",")[0], tbCluster.getClusterPort(), instance);
    //                 throw new Exception("数据库" + instance + "已存在");
    //             }
    //             MysqlUtil.createDb(connection, instance);
    //             tbCluster.setDbName(instance);
    //
    //             insertDatasource(tenantId, username, tbCluster, Constant.DATASOURCE_TYPE_MYSQL, Constant.DATASOURCE_DRIVER_MYSQL,
    //                     String.format(datasourceUrl, instance));
    //             tenantCluster.setInstance(instance);
    //             tenantClusterService.save(tenantCluster);
    //             LOGGER.info("Mysql资源创建创建成功");
    //         } else {
    //             LOGGER.error(tbCluster.getClusterName() + "集群连接失败");
    //             throw new Exception("集群连接失败");
    //         }
    //     } catch (Exception e) {
    //         LOGGER.error(e.getMessage(), e);
    //         throw new Exception(e.getMessage());
    //     } finally {
    //         if (connection != null) {
    //             MysqlUtil.close(connection);
    //         }
    //     }
    // }

    /**
     * @description clickhouse初始化
     * <AUTHOR>
     * @date 2022/7/20 0020 14:00
     */
    private void clickhouseInit(Long tenantId, String username, TbTenantCluster tenantCluster, String instance, TbCluster cluster) throws Exception {
        if (cluster != null) {
            LOGGER.info("开始创建Clickhouse资源......");
            ITbClusterService clusterService = (ITbClusterService) SpringUtil.getBean("tbClusterServiceImpl");
            ITbTenantClusterService tenantClusterService = (ITbTenantClusterService) SpringUtil.getBean("tbTenantClusterServiceImpl");
            // clickhouse中需要创建mysql外挂表,需要保证mysql可以
            TbCluster tbCluster = clusterService.getById(tenantCluster.getClusterId());
            String datasourceUrl = Constant.DATASOURCE_URL_CLICKHOUSE + tbCluster.getNodeAddress() + "/%s";
            if ("1".equals(tbCluster.getStatus())) {
                //加密连接设置
                datasourceUrl = datasourceUrl + "?ssl=true&sslmode=none";
            }
            Connection connection = null;
            Connection dbConnection = null;
            try {
                connection = ClickhouseUtil.getConnect(String.format(datasourceUrl, "system"), tbCluster.getClusterUsername(),
                        AESUtil.defaultDecrypt(tbCluster.getClusterPassword()));
                if (connection != null) {
                    if (checkInstance(instance.charAt(0))) {
                        instance = "tb" + instance;
                    }
                    String db = instance;
                    int num = 1;
                    while (!ClickhouseUtil.queryDb(connection, db)) {
                        db = instance + "_00" + num;
                        num++;
                    }
                    ClickhouseUtil.createDb(connection, db);

                    // 初始化数据库脚本
                    dbConnection = ClickhouseUtil.getConnect(String.format(datasourceUrl, db), tbCluster.getClusterUsername(),
                            AESUtil.defaultDecrypt(tbCluster.getClusterPassword()));
                    if (dbConnection != null) {
                        ClickhouseUtil.initTenantScript(dbConnection, tenantId, db, cluster, tenantCluster.getClusterId(), cluster.getDbName());
                    }

                    ClickhouseUtil.createUser(dbConnection, tenantCluster.getTenantAccount(),
                            AESUtil.defaultDecrypt(tenantCluster.getTenantPassword()));
                    String roleName = "role_" + tenantCluster.getTenantAccount();
                    ClickhouseUtil.createRole(dbConnection, roleName);
                    ClickhouseUtil.grantPrivilegesToRole(dbConnection, db, roleName);
                    ClickhouseUtil.assignRoleToUser(dbConnection, roleName, tenantCluster.getTenantAccount());

                    insertDatasource(tenantId, username, tbCluster, Constant.DATASOURCE_TYPE_CLICKHOUSE,
                            Constant.DATASOURCE_DRIVER_CLICKHOUSE, String.format(datasourceUrl, db),
                            tenantCluster.getTenantAccount(), tenantCluster.getTenantPassword());
                    tenantCluster.setInstance(db);
                    tenantClusterService.save(tenantCluster);
                    LOGGER.info("Clickhouse资源创建创建成功");
                } else {
                    LOGGER.error(tbCluster.getClusterName() + "集群连接失败");
                    throw new Exception("集群连接失败");
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
                throw new Exception(e.getMessage());
            } finally {
                if (connection != null) {
                    ClickhouseUtil.close(connection);
                }
                if (dbConnection != null) {
                    ClickhouseUtil.close(dbConnection);
                }
            }
        }
    }

    //检查实例名是否为字母开头
    private boolean checkInstance(char charAt) {
        if ((charAt >= 'a' && charAt <= 'z') || (charAt >= 'A' && charAt <= 'Z')) {
            return false;
        }
        return true;
    }

    private void insertDatasource(Long tenantId, String username, TbCluster tbCluster, String datasourceType, String datasourceDriver,
                                  String datasourceUrl, String tenantAccount, String tenantPassword) {
        //创建数据库连接信息
        TbDatasourceInfo datasourceInfo = new TbDatasourceInfo();
        datasourceInfo.setTenantId(tenantId);

        if (!StringUtils.isAllBlank(tenantAccount, tenantPassword)) {
            datasourceInfo.setDatasourceUsername(tenantAccount);
            datasourceInfo.setDatasourcePassword(tenantPassword);
        } else {
            datasourceInfo.setDatasourceUsername(tbCluster.getClusterUsername());
            datasourceInfo.setDatasourcePassword(tbCluster.getClusterPassword());
        }

        datasourceInfo.setClusterId(tbCluster.getId());
        datasourceInfo.setUpdateTime(new Date());
        datasourceInfo.setUpdateUser(username);
        datasourceInfo.setCreateTime(new Date());
        datasourceInfo.setCreateUser(username);
        datasourceInfo.setDatasourceType(datasourceType);
        datasourceInfo.setDatasourceDriver(datasourceDriver);
        datasourceInfo.setDatasourceUrl(datasourceUrl);
        ITbDatasourceInfoService datasourceInfoService = (ITbDatasourceInfoService) SpringUtil.getBean("tbDatasourceInfoServiceImpl");
        datasourceInfoService.save(datasourceInfo);
    }
}
