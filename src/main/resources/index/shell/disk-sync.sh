#!/bin/sh

# modify ip , request_url , output
# 0 */1 * * * /home/<USER>/data-lake-webapi-1.0.0/shell/disk_sync.sh
currentTime=$(date "+%Y-%m-%d %H:%M:%S")
local_ip="***********"
disk_request_url="http://***********/lake/api/lake-index/diskInfoSync"
hdfs_request_url="http://***********/lake/api/lake-index/hdfsInfoSync"
output="/root/test.txt"

add_json_field() {
    json="$1"
    field_name="$2"
    field_value="$3"
    new_json="$(echo "$json" | sed 's/\(}\)$/, "'"$field_name"'": "'"$field_value"'"\1/')"
    echo "$new_json"
}

function syncDiskInfo() {
  json_arr=()
  data_value=""
  for line in $(df -m | awk '{ if (NR > 1){print "{\"fileSystem\":\"" $1 "\",\"total\":\"" $2 "\",\"used\":\"" $3 "\",\"free\":\"" $4 "\",\"used_rate\":\"" $5 "\",\"mountPoint\":\"" $6 "\"}"}}'); do
    data_value="$(add_json_field "$line" "ip" "$local_ip")"
    json_arr+=($data_value ,)
  done
  data="${json_arr[@]}"
  json="["${data:0:-1}"]"
  echo ${json}
  #request data-lake api
  response=$(curl -H "Content-Type: application/json; charset=UTF-8" -X POST -d "$json" "$disk_request_url")
  echo $currentTime ${response} >> ${output}
}
syncDiskInfo

function syncHdfsInfo() {
  if ! command -v hdfs >/dev/null 2>&1; then
      echo $currentTime "hdfs not exists" >> ${output}
      return
  else
      echo $currentTime "hdfs exists" >> ${output}
  fi

  json_arr=()
  data_value=""
  for line in $(hdfs dfs -du / | awk '{ {print "{\"size\":\"" $1 "\",\"dir\":\"" $2 "\"}"}}'); do
    data_value="$(add_json_field "$line" "ip" "$local_ip")"
    json_arr+=($data_value ,)
  done
  data="${json_arr[@]}"
  json="'["${data:0:-1}"]'"
  echo ${json}

  response=$(curl -H "Content-Type: application/json; charset=UTF-8" -X POST -d "$json" "$disk_request_url")
  echo $currentTime ${response} >>  ${output}
}
syncHdfsInfo

exit 0
