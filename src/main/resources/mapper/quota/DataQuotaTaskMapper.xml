<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.quota.mapper.DataQuotaTaskMapper">

    <select id="page" resultType="com.idss.datalake.quota.entity.DataQuotaTask">
        select * from data_quota_task where tenant_id = #{tenantId}
        <if test="taskName != null and taskName != ''">
            and task_name like concat('%',#{taskName},'#')
        </if>
        order by create_time desc
    </select>
</mapper>
