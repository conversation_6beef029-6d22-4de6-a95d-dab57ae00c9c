<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.quota.mapper.DataQuotaTaskOutMapper">

    <select id="page" resultType="com.idss.datalake.quota.entity.DataQuotaTaskOut">
        select * from (select t1.*,
               t2.tenant_id,
               t2.create_user,
               COALESCE(t3.key_words, t4.key_words, t4.key_words, '')             AS key_words,
               COALESCE(t3.table_name_cn, t4.table_name_cn, t5.table_name_cn, '') AS table_name_cn,
               COALESCE(t3.table_dscribe, t4.table_dscribe, t5.table_dscribe, '') AS table_dscribe
        from data_quota_task_out t1
                 left join data_quota_task t2 on t1.task_id = t2.id
                 left join qua_web_mysql_element_detail_table t3
                           on (t1.table_id = t3.id and t1.element_id = t3.element_id and t1.db_name = t3.db_name and
                               t1.table_name = t3.table_name)
                 left join qua_web_ch_element_detail_table t4
                           on (t1.table_id = t4.id and t1.element_id = t4.element_id and t1.db_name = t4.db_name and
                               t1.table_name = t4.table_name)
                 left join qua_web_hive_element_detail_table t5
                           on (t1.table_id = t5.id and t1.element_id = t5.element_id and t1.db_name = t5.db_name and
                               t1.table_name = t5.table_name)) a
                where a.tenant_id = #{tenantId}
                <if test="searchName != null and searchName != ''">
                    and (a.table_name like concat('%',#{searchName},'%') or a.table_name_cn like concat('%',#{searchName},'%') or a.asset_type like concat('%',#{searchName},'%') or a.key_words like concat('%',#{searchName},'%'))
                </if>
                order by a.create_time desc
    </select>
    <select id="tenantGroup" resultType="java.util.Map">
        select a.tenant_id as tenantId,
               a.create_user as createUser,
               count(1) cnt
        from ( select * from (select t1.*,
                   t2.tenant_id,
                   t2.create_user,
                   COALESCE(t3.key_words, t4.key_words, t4.key_words, '')             AS key_words,
                   COALESCE(t3.table_name_cn, t4.table_name_cn, t5.table_name_cn, '') AS table_name_cn,
                   COALESCE(t3.table_dscribe, t4.table_dscribe, t5.table_dscribe, '') AS table_dscribe
            from data_quota_task_out t1
                     left join data_quota_task t2 on t1.task_id = t2.id
                     left join qua_web_mysql_element_detail_table t3
                               on (t1.table_id = t3.id and t1.element_id = t3.element_id and t1.db_name = t3.db_name and
                                   t1.table_name = t3.table_name)
                     left join qua_web_ch_element_detail_table t4
                               on (t1.table_id = t4.id and t1.element_id = t4.element_id and t1.db_name = t4.db_name and
                                   t1.table_name = t4.table_name)
                     left join qua_web_hive_element_detail_table t5
                               on (t1.table_id = t5.id and t1.element_id = t5.element_id and t1.db_name = t5.db_name and
                                   t1.table_name = t5.table_name)) t
        <where>
            <if test="searchName != null and searchName != ''">
                (t.table_name like concat('%',#{searchName},'%') or t.table_name_cn like concat('%',#{searchName},'%') or t.asset_type like concat('%',#{searchName},'%') or t.key_words like concat('%',#{searchName},'%'))
            </if>
        </where>
         ) a group by a.tenant_id, a.create_user

    </select>
    <select id="tenantPage" resultType="com.idss.datalake.quota.entity.DataQuotaTaskOut">
        select * from (select t1.*,
        t2.tenant_id,
        t2.create_user,
        COALESCE(t3.key_words, t4.key_words, t4.key_words, '')             AS key_words,
        COALESCE(t3.table_name_cn, t4.table_name_cn, t5.table_name_cn, '') AS table_name_cn,
        COALESCE(t3.table_dscribe, t4.table_dscribe, t5.table_dscribe, '') AS table_dscribe
        from data_quota_task_out t1
        left join data_quota_task t2 on t1.task_id = t2.id
        left join qua_web_mysql_element_detail_table t3
        on (t1.table_id = t3.id and t1.element_id = t3.element_id and t1.db_name = t3.db_name and
        t1.table_name = t3.table_name)
        left join qua_web_ch_element_detail_table t4
        on (t1.table_id = t4.id and t1.element_id = t4.element_id and t1.db_name = t4.db_name and
        t1.table_name = t4.table_name)
        left join qua_web_hive_element_detail_table t5
        on (t1.table_id = t5.id and t1.element_id = t5.element_id and t1.db_name = t5.db_name and
        t1.table_name = t5.table_name)) a
        where 1 = 1
          <if test="tenantId != null">
              and a.tenant_id = #{tenantId}
          </if>
        <if test="createUser != null and createUser != ''">
            and a.create_user = #{createUser}
        </if>
        <if test="searchName != null and searchName != ''">
            and (a.table_name like concat('%',#{searchName},'%') or a.table_name_cn like concat('%',#{searchName},'%') or a.asset_type like concat('%',#{searchName},'%') or a.key_words like concat('%',#{searchName},'%'))
        </if>
        order by a.create_time desc
    </select>
    <select id="trend" resultType="java.util.Map">
        SELECT
            to_char(create_time, 'YYYY-MM-DD') AS date,  -- 将日期格式化为字符串
            COUNT(*) AS count
        FROM
            data_quota_task_out
        where task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        GROUP BY
            TO_CHAR(create_time, 'YYYY-MM-DD')
        ORDER BY
            date DESC
        LIMIT 30
    </select>
</mapper>
