<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.etl.mapper.EtlSourceMapper">

    <select id="queryDatasource" parameterType="map" resultType="map">
        select a.id as "sourceId",
        a.id as "flowId",
        a.name as "sourceType",
        a.source_name as "sourceName",
        a.source_desc as "sourceDesc",
        CASE
        WHEN a.ACCESS_STATUS = 1 THEN
        '已生效'
        ELSE
        '未生效'
        END as "status",
        CONCAT_WS('/',a.table_name,a.index_name,a.topic,a.origin_to_topic) "dataBase",
        TO_CHAR(a.create_date, 'YYYY-MM-DD HH:MI:SS') AS "createDate",
        TO_CHAR(a.update_date, 'YYYY-MM-DD HH:MI:SS') AS "updateDate",
        a.update_user as "updateUser",
        a.source_switch as "switch",
        a.src_device_type as "srcDeviceType"
        from (
        select a.*,e.name,CONCAT(b.table_name,'(数仓)') table_name,CONCAT(c.index_name,'(索引)') index_name,case when d.topic != '' then
        CONCAT(d.topic,'(Kafka)') else null end topic,case when d.origin_to_topic != '' then CONCAT(d.origin_to_topic,'(Kafka)') else null end
        origin_to_topic ,f.config_value src_device_type
        from etl_source a
        left join etl_writer_ch_config b on a.id = b.source_id
        left join etl_writer_es_config c on a.id = c.source_id
        left join etl_writer_kafka_config d on a.id = d.source_id
        left join etl_source_type e ON a.source_type = e.source_type
        left join (select * from etl_reader_param WHERE config_key = 'generic_datasource_type') f on f.source_id = a.id
        ) a
        WHERE a.status = '1'
        <if test="sourceName != null and sourceName != ''">
            and a.source_name like concat('%',#{sourceName},'%')
        </if>
        <if test="status != null and status != ''">
            and a.ACCESS_STATUS = #{status}
        </if>
        <if test="flowId != null and flowId != ''">
            and a.id like concat('%',#{flowId},'%')
        </if>
        <if test="dataBase != null and dataBase != ''">
            and (a.table_name like concat('%',#{dataBase},'%') or a.index_name like concat('%',#{dataBase},'%') or a.topic like concat('%',#{dataBase},'%') or a.origin_to_topic like concat('%',#{dataBase},'%'))
        </if>
        <if test="sourceType != null and sourceType.size > 0">
            and a.source_type in
            <foreach collection="sourceType" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="createStart != null and createStart != ''">
            and a.create_date &gt;= STR_TO_DATE(#{createStart}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="createEnd != null and createEnd != ''">
            and a.create_date &lt;= STR_TO_DATE(#{createEnd}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="updateStart != null and updateStart != ''">
            and a.update_date &gt;= STR_TO_DATE(#{updateStart}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="updateEnd != null and updateEnd != ''">
            and a.update_date &lt;= STR_TO_DATE(#{updateEnd}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="sourceSwitch != null and sourceSwitch.size > 0">
            and a.source_switch in
            <foreach collection="sourceSwitch" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="srcDeviceType != null and srcDeviceType.size > 0">
            and a.src_device_type in
            <foreach collection="srcDeviceType" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderField != null and orderField != ''">
            order by ${orderField} ${orderType}
        </if>
        <if test="orderField == null or orderField == ''">
            order by a.create_date desc
        </if>
        LIMIT #{pageSize} offset #{beginIndex}
    </select>

    <select id="queryAssetResource" parameterType="map" resultType="map">
        select a.id as "etlSourceId",
        a.id as "flowId",
        a.name as "sourceType",
        a.source_name as "sourceName",
        a.source_desc as "sourceDesc",
        CASE
        WHEN a.ACCESS_STATUS = 1 THEN
        '已生效'
        ELSE
        '未生效'
        END as "status",
        CONCAT_WS('/',a.table_name,a.index_name,a.topic,a.origin_to_topic) "dataBase",
        TO_CHAR(a.create_date, 'YYYY-MM-DD HH:MI:SS') AS "createDate",
        TO_CHAR(a.update_date, 'YYYY-MM-DD HH:MI:SS') AS "updateDate",
        a.update_user as "updateUser",
        a.source_switch as "switch",
        a.deviceCode as "deviceCode",
        a.deviceType as "deviceType",
        a.deviceId as "deviceType",
        a.del_flag as "delFlag",
        a.enName as "enName",
        a.assetPurposeId as "assetPurposeId",
        a.classificationId as "classificationId",
        a.assetLevelId as "assetLevelId",
        a.description
        from (
        select a.*,e.name,eprc.asset_code deviceCode,eprc.asset_label deviceType,erp.id deviceId,ear.del_flag,
        ear.en_name enName,ear.asset_purpose_id assetPurposeId,ear.classification_id classificationId,ear.asset_level_id assetLevelId,ear.description,
        CONCAT(b.table_name,'(数仓)') table_name,CONCAT(c.index_name,'(索引)') index_name,case when d.topic != '' then CONCAT(d.topic,'(Kafka)') else null
        end topic,case when d.origin_to_topic != '' then CONCAT(d.origin_to_topic,'(Kafka)') else null end origin_to_topic
        from etl_source a
        left join etl_writer_ch_config b on a.id = b.source_id
        left join etl_writer_es_config c on a.id = c.source_id
        left join etl_writer_kafka_config d on a.id = d.source_id
        left join etl_source_type e ON a.source_type = e.source_type
        left join etl_reader_param erp ON a.id = erp.source_id AND erp.config_key = 'generic_datasource_type'
        left join etl_parser_rule_category eprc ON erp.config_value = eprc.asset_code
        left join etl_asset_resource ear ON a.id = ear.etl_source_id
        ) a
        WHERE a.status = '1' AND (a.del_flag = 0 or a.del_flag is NULL)
        <if test="sourceName != null and sourceName != ''">
            and a.source_name like concat('%',#{sourceName},'%')
        </if>
        <if test="assetPurposeId != null and assetPurposeId != ''">
            and a.assetPurposeId = #{assetPurposeId}
        </if>
        <if test="deviceType != null and deviceType != ''">
            and a.deviceCode = #{deviceType}
        </if>
        <if test="orderField != null and orderField != ''">
            order by ${orderField} ${orderType}
        </if>
        <if test="orderField == null or orderField == ''">
            order by a.create_date desc
        </if>
        LIMIT #{pageSize} offset #{beginIndex}
    </select>

    <select id="queryDatasourceTotal" parameterType="map" resultType="map">
        SELECT flow_id etlSourceId, sum(in_count) total,sum(`length`) dataLength
        FROM  mv_flow_metric_interval_1day
        WHERE node_type = 'parser'
        <if test="etlSourceIds != null and etlSourceIds.size > 0">
            AND flow_id in
            <foreach collection="etlSourceIds" index="index" item="item" open="(" separator="," close=")">
                toString(#{item})
            </foreach>
        </if>
        group by etlSourceId
    </select>

    <select id="queryDatasourceTotalAll" parameterType="map" resultType="long">
        SELECT SUM(b.in_count) total
        FROM (SELECT * FROM etl_source WHERE status = 1) a
        join mv_flow_metric_interval_1day_local b on toString(a.id) = b.flow_id
        WHERE b.node_type = 'gate'
        <if test="createDate != null and createDate != ''">
            and a.create_date >= toString(#{createDate})
        </if>
    </select>

    <select id="queryDatasourceLengthAll" parameterType="map" resultType="long">
        SELECT SUM(b.`length`) totalLength
        FROM (SELECT * FROM etl_source WHERE status = 1) a
        join mv_flow_metric_interval_1day_local b on toString(a.id) = b.flow_id
        WHERE b.node_type = 'gate'
        <if test="createDate != null and createDate != ''">
            and a.create_date >= toString(#{createDate})
        </if>
    </select>

    <select id="queryAssetTotal" parameterType="map" resultType="com.idss.datalake.etl.dto.AssetTotalDTO">
        SELECT a.source_name sourceName, sum(b.in_count) total
        FROM (SELECT * FROM etl_source WHERE status = 1) a
        join mv_flow_metric_interval_1day_local b on toString(a.id) = b.flow_id
        WHERE b.node_type = 'gate'
        <if test="createDate != null and createDate != ''">
            and a.create_date >= toString(#{createDate})
        </if>
        group by a.source_name order by total desc limit 20
    </select>

    <select id="queryAssetDiskTotal" resultType="com.idss.datalake.etl.dto.AssetDiskDTO">
        SELECT a.source_name sourceName, sum(b.`length`) diskTotal
        FROM (SELECT * FROM etl_source WHERE status = 1) a
                 join mv_flow_metric_interval_1day_local b on toString(a.id) = b.flow_id
        WHERE b.node_type = 'gate' group by a.source_name order by diskTotal desc limit 10
    </select>

    <select id="queryTotalAssetResource" parameterType="map" resultType="int">
        SELECT
        COUNT(1)
        FROM
        (
        select a.id as "etlSourceId"
        from (
        select a.*,e.name,erp.config_value deviceType,erp.id deviceId,ear.del_flag,
        ear.en_name enName,ear.asset_purpose_id assetPurposeId,ear.classification_id classificationId,
        ear.asset_level_id assetLevelId,ear.description
        from etl_source a
        left join etl_writer_ch_config b on a.id = b.source_id
        left join etl_writer_es_config c on a.id = c.source_id
        left join etl_writer_kafka_config d on a.id = d.source_id
        left join etl_source_type e ON a.source_type = e.source_type
        left join etl_reader_param erp ON a.id = erp.source_id AND erp.config_key = 'generic_datasource_type'
        left join etl_asset_resource ear ON a.id = ear.etl_source_id
        ) a
        WHERE a.status = '1' AND (a.del_flag = 0 or a.del_flag is NULL)
        <if test="sourceName != null and sourceName != ''">
            and a.source_name like concat('%',#{sourceName},'%')
        </if>
        <if test="assetPurposeId != null and assetPurposeId != ''">
            and a.assetPurposeId = #{assetPurposeId}
        </if>
        <if test="deviceType != null and deviceType != ''">
            and a.deviceType = #{deviceType}
        </if>
        ) c
    </select>

    <select id="queryTotalsource" parameterType="map" resultType="int">
        SELECT
        COUNT(c.sourceId)
        FROM
        (
        select a.id as "sourceId"
        from (
        select a.*,e.name,f.config_value src_device_type,CONCAT(b.table_name,'(数仓)') table_name,CONCAT(c.index_name,'(索引)') index_name,CONCAT(d.topic,'(Kafka)') topic,CONCAT(d.origin_to_topic,'(Kafka)') origin_to_topic from etl_source a
        left join etl_writer_ch_config b on a.id = b.source_id
        left join etl_writer_es_config c on a.id = c.source_id
        left join etl_writer_kafka_config d on a.id = d.source_id
        left join etl_source_type e ON a.source_type = e.source_type
        left join (select * from etl_reader_param WHERE config_key = 'generic_datasource_type') f on f.source_id = a.id
        ) a
        WHERE a.status = '1'
        <if test="sourceName != null and sourceName != ''">
            and a.source_name like concat('%',#{sourceName},'%')
        </if >
        <if test="status != null and status != ''">
            and a.ACCESS_STATUS = #{status}
        </if >
        <if test="flowId != null and flowId != ''">
            and a.id like concat('%',#{flowId},'%')
        </if >
        <if test="dataBase != null and dataBase != ''">
            and (a.table_name like concat('%',#{dataBase},'%') or a.index_name like concat('%',#{dataBase},'%') or a.topic like concat('%',#{dataBase},'%') or a.origin_to_topic like concat('%',#{dataBase},'%'))
        </if >
        <if test="sourceType != null and sourceType.size > 0">
            and a.source_type in
            <foreach collection="sourceType" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if >
        <if test="createStart != null and createStart != ''">
            and a.create_date &gt;= STR_TO_DATE(#{createStart}, '%Y-%m-%d %H:%i:%s')
        </if >
        <if test="createEnd != null and createEnd != ''">
            and a.create_date &lt;= STR_TO_DATE(#{createEnd}, '%Y-%m-%d %H:%i:%s')
        </if >
        <if test="updateStart != null and updateStart != ''">
            and a.update_date &gt;= STR_TO_DATE(#{updateStart}, '%Y-%m-%d %H:%i:%s')
        </if >
        <if test="updateEnd != null and updateEnd != ''">
            and a.update_date &lt;= STR_TO_DATE(#{updateEnd}, '%Y-%m-%d %H:%i:%s')
        </if >
        <if test="sourceSwitch != null and sourceSwitch.size > 0">
            and a.source_switch in
            <foreach collection="sourceSwitch" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if >
        <if test="srcDeviceType != null and srcDeviceType.size > 0">
            and a.src_device_type in
            <foreach collection="srcDeviceType" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) c
    </select>

    <select id="statSwitchStatus" parameterType="map" resultType="int">
        select a.source_switch as "sourceSwitch"
        from (
        select a.*,e.name,CONCAT(b.table_name,'(数仓)') table_name,CONCAT(c.index_name,'(索引)') index_name,CONCAT(d.topic,'(Kafka)') topic,CONCAT(d.origin_to_topic,'(Kafka)') origin_to_topic from etl_source a
        left join etl_writer_ch_config b on a.id = b.source_id
        left join etl_writer_es_config c on a.id = c.source_id
        left join etl_writer_kafka_config d on a.id = d.source_id
        left join etl_source_type e ON a.source_type = e.source_type
        ) a
        WHERE a.status = '1'
        <if test="sourceName != null and sourceName != ''">
            and a.source_name like concat('%',#{sourceName},'%')
        </if>
        <if test="status != null and status != ''">
            and a.ACCESS_STATUS = #{status}
        </if>
        <if test="flowId != null and flowId != ''">
            and a.id like concat('%',#{flowId},'%')
        </if>
        <if test="dataBase != null and dataBase != ''">
            and (a.table_name like concat('%',#{dataBase},'%') or a.index_name like concat('%',#{dataBase},'%') or a.topic like concat('%',#{dataBase},'%') or a.origin_to_topic like concat('%',#{dataBase},'%'))
        </if>
        <if test="sourceType != null and sourceType.size > 0">
            and a.source_type in
            <foreach collection="sourceType" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="createStart != null and createStart != ''">
            and a.create_date &gt;= STR_TO_DATE(#{createStart}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="createEnd != null and createEnd != ''">
            and a.create_date &lt;= STR_TO_DATE(#{createEnd}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="updateStart != null and updateStart != ''">
            and a.update_date &gt;= STR_TO_DATE(#{updateStart}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="updateEnd != null and updateEnd != ''">
            and a.update_date &lt;= STR_TO_DATE(#{updateEnd}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="sourceSwitch != null and sourceSwitch.size > 0">
            and a.source_switch in
            <foreach collection="sourceSwitch" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateSwitch" parameterType="map">
        UPDATE etl_source SET source_switch = #{switch}
        <if test="updateUser != null and updateUser != ''">
            ,update_user = #{updateUser}
        </if>
        <if test="updateDate != null">
            ,update_date = #{updateDate}
        </if>
        WHERE id IN
        <foreach collection="sourceIds" item="id" index="index" open="(" close=")" separator=",">
            #{id,jdbcType=VARCHAR}
        </foreach>

    </update>

    <update id="deleteSource" parameterType="map">
        UPDATE etl_source SET status = '0'
        WHERE id IN
        <foreach collection="sourceIds" item="id" index="index" open="(" close=")" separator=",">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateRuleStatus" parameterType="map">
        UPDATE etl_transform_rule SET status = #{switch}
        WHERE source_id IN
        <foreach collection="sourceIds" item="id" index="index" open="(" close=")" separator=",">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="querySourceLog" parameterType="map" resultType="map">
        SELECT
        TO_CHAR(opt_time, 'YYYY-MM-DD HH:MI:SS') AS "optTime",
        CASE WHEN opt_user IS NULL THEN ''
        ELSE opt_user
        END as "optUser",
        CASE
        WHEN opt_type = 1 THEN
        '修改配置'
        WHEN opt_type = 2 THEN
        '开启配置'
        WHEN opt_type = 3 THEN
        '关闭配置'
        WHEN opt_type = 4 THEN
        '删除配置'
        WHEN opt_type = 6 THEN
        '新增配置'
        WHEN opt_type = 7 THEN
        '导入配置'
        WHEN opt_type = 8 THEN
        '导出配置'
        ELSE
        '查看配置'
        END as "optType"
        FROM
        etl_source_optlog
        WHERE source_id = #{sourceId}
        <if test="optUser != null and optUser != ''">
            and opt_user like concat('%',#{optUser},'%')
        </if>
        <if test="optType != null and optType != ''">
            and opt_type = #{optType}
        </if>
        <if test="optTimeStart != null and optTimeStart != ''">
            and opt_time &gt;= STR_TO_DATE(#{optTimeStart}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="optTimeeEnd != null and optTimeeEnd != ''">
            and opt_time &lt;= STR_TO_DATE(#{optTimeeEnd}, '%Y-%m-%d %H:%i:%s')
        </if>
        order by opt_time desc
        LIMIT #{pageSize} offset #{beginIndex}
    </select>

    <select id="queryTotalLog" parameterType="map" resultType="int">
        SELECT
        COUNT(c.optTime)
        FROM
        (
        SELECT
        TO_CHAR(opt_time, 'YYYY-MM-DD HH:MI:SS') AS "optTime",
        opt_user as "optUser",
        CASE
        WHEN opt_type = 1 THEN
        '修改配置'
        WHEN opt_type = 2 THEN
        '开启配置'
        WHEN opt_type = 3 THEN
        '关闭配置'
        WHEN opt_type = 4 THEN
        '删除配置'
        ELSE
        '查看配置'
        END as "optType"
        FROM
        etl_source_optlog
        WHERE source_id = #{sourceId}
        <if test="optUser != null and optUser != ''">
            and opt_user like concat('%',#{optUser},'%')
        </if>
        <if test="optType != null and optType != ''">
            and opt_type = #{optType}
        </if>
        <if test="optTimeStart != null and optTimeStart != ''">
            and opt_time &gt;= STR_TO_DATE(#{optTimeStart}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="optTimeeEnd != null and optTimeeEnd != ''">
            and opt_time &lt;= STR_TO_DATE(#{optTimeeEnd}, '%Y-%m-%d %H:%i:%s')
        </if>
        ) c
    </select>

    <select id="getTransField" parameterType="map" resultType="map">
        SELECT
            a.dest_field AS "destField",
            a.dest_table AS "destTable",
            a.dest_column AS "destColumn",
            a.longitude as "longitude",
            a.latitude AS "latitude",
            a.country AS "country",
            a.province AS "province",
            a.city AS "city",
            a.isp AS "isp",
            a.format_to AS "formatTo",
            a.script_content AS "scriptContent",
            b.rule_type AS "ruleType",
            b.rule_order
        FROM
            etl_transform_action a
                LEFT JOIN etl_transform_rule b ON a.rule_id = b.id
        WHERE b.source_id = #{sourceId}
          AND b.rule_type IN ('DYNAMIC','STATIC','CONCAT','IP2REGION',
                              'SYSLOGPRI','REMOVE', 'MASK','JAVATRANSFORM',
                              'KeyValue','SUBDOMAIN', 'JSON','SQL',
                              'IP2SEGMENT','SPLIT','STR_REPLACE','RENAME',
                              'URL','BASE64_DECODE','URL_DECODE_ADD',
                              'SHORT_REG','NUMBER_TO_IP','IP_LABEL')
        ORDER BY b.rule_order
    </select>

    <select id="getUniqTransField" parameterType="map" resultType="map">
        SELECT
            a.id AS "id",
            a.dest_field AS "destField",
            a.dest_value AS "destValue",
            b.source_id AS "sourceId",
            b.rule_type AS "ruleType"
        FROM
            etl_transform_action a
                LEFT JOIN etl_transform_rule b ON a.rule_id = b.id
                LEFT JOIN etl_source c ON b.source_id = c.id
        WHERE b.rule_type IN ('SRC_DEVICE_UNIQUE','DST_DEVICE_UNIQUE','DST_APP_UNIQUE')
          <if test="sourceId != null">
              AND b.source_id = #{sourceId}
          </if>
          <if test="sourceId == null">
              AND c.status = 1
          </if>
    </select>

    <update id="updateParam" parameterType="map">
        UPDATE etl_parser_param SET paraser_reg_id = #{newRegId} WHERE paraser_reg_id = #{oldRegId} AND source_id != #{sourceId}
    </update>

    <update id="updateTemplateParam" parameterType="map">
        UPDATE etl_parser_result SET reg_id = #{newRegId} WHERE reg_id = #{oldRegId}
    </update>

    <!--动态补全SQL-->
    <select id="dynamic" resultType="map" parameterType="String">
        ${dynamicSQL}
    </select>

    <insert id="saveRoleChEs" parameterType="map">
        INSERT INTO ums_sys_role_ch_es
        (
            type,
            role_id,
            table_name,
            query_condition,
            create_date,
            create_user,
            update_date,
            update_user,
            flag
        ) VALUES
            (
                #{type},
                #{roleId},
                #{tableName},
                #{queryCondition},
                #{createDate},
                #{createUser},
                #{updateDate},
                #{updateUser},
                #{flag}
            )
    </insert>

    <select id="queryRoleChEs" parameterType="map" resultType="map">
        select type,role_id,table_name from ums_sys_role_ch_es
        where type = #{type} and role_id = #{roleId} and table_name = #{tableName}
    </select>

    <select id="querySourceConfig" resultType="map">
        select a.id,a.source_name,b.config_key,b.config_value
        from etl_source a
                 left join etl_reader_param b
                           on a.id = b.source_id
        where a.source_type = #{sourceType}
          and source_id != #{sourceId}
          and config_key = #{configKey}
          and config_value = #{configValue}
          and a.source_switch = 1
          and a.status = 1
    </select>

    <select id="querySyslogSourceIpList" resultType="map">
        select
            a.source_name,
            b.config_value
        from etl_source a
                 left join etl_reader_param b
                           on a.id = b.source_id
        where a.source_type = 'SYSLOG_UDP'
          and a.source_switch = 1
          and a.status = 1
          and config_key = 'sourceIpList'
          and source_id != #{sourceId}
    </select>
    <select id="queryDatasourceForIndex" parameterType="map" resultType="map">
        select
               t1.source_name as source_name,
               t1.id as flow_id,
               t1.source_desc as source_desc,
               t1.source_type as source_type,
               t3.config_value as src_device_type,
               toString(t1.create_date) AS "createDate"
        from etl_source t1
            join (select distinct flowid from logmodule_flow_errlog) t2 on toString(t1.id) = t2.flowid
            left join (select * from etl_reader_param WHERE config_key = 'generic_datasource_type') t3 on t3.source_id = t1.id
            where t1.status = 1 order by t1.create_date desc LIMIT #{pageSize} offset #{beginIndex}
    </select>
    <select id="queryTotalsourceForIndex" resultType="java.lang.Integer">
        select count(1) from etl_source t1 join (select distinct flowid from logmodule_flow_errlog) t2 on toString(t1.id) = t2.flowid where t1.status = 1
    </select>
</mapper>