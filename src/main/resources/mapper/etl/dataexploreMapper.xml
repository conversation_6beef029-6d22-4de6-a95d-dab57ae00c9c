<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.etl.mapper.DataexploreMapper">

    <select id="queryCHTables" parameterType="map" resultType="map">
        SELECT distinct table_name as "tableName"
        FROM
          etl_writer_ch_config
        WHERE 1 = 1
        <if test="tableName != null and tableName != ''">
            and table_name like concat('%',#{tableName},'%')
        </if >
        LIMIT #{pageSize} offset #{beginIndex}
    </select>

    <select id="queryCHTablesCnt" parameterType="map" resultType="int">
        SELECT
          COUNT(c.tableName)
        FROM
        (
            SELECT distinct table_name as "tableName"
            FROM
            etl_writer_ch_config
            WHERE 1 = 1
            <if test="tableName != null and tableName != ''">
                and table_name like concat('%',#{tableName},'%')
            </if >
        ) c
    </select>

    <select id="queryAllCHTables" parameterType="map" resultType="map">
        SELECT distinct table_name as "tableName"
        FROM
        etl_writer_ch_config
        WHERE 1 = 1
        <if test="tableName != null and tableName != ''">
            and table_name like concat('%',#{tableName},'%')
        </if >
    </select>

    <select id="queryCHTableFields" parameterType="map" resultType="map">
        SELECT column_name as columnName
        FROM
        etl_writer_table_config
        WHERE source_id = #{flowId} AND write_type = 'CH'
    </select>

    <select id="queryESIndexs" parameterType="map" resultType="map">
        SELECT distinct index_name as "indexName"
        FROM
        etl_writer_es_config
        WHERE 1 = 1
        <if test="indexName != null and indexName != ''">
            and index_name like concat('%',#{indexName},'%')
        </if >
        LIMIT #{pageSize} offset #{beginIndex}
    </select>

    <select id="queryESIndexsCnt" parameterType="map" resultType="int">
        SELECT
        COUNT(c.indexName)
        FROM
        (
        SELECT distinct index_name as "indexName"
        FROM
        etl_writer_es_config
        WHERE 1 = 1
        <if test="indexName != null and indexName != ''">
            and index_name like concat('%',#{indexName},'%')
        </if >
        ) c
    </select>

    <select id="queryAllESIndexs" parameterType="map" resultType="map">
        SELECT distinct index_name as "indexName"
        FROM
        etl_writer_es_config
        WHERE 1 = 1
        <if test="indexName != null and indexName != ''">
            and index_name like concat('%',#{indexName},'%')
        </if >
    </select>

    <select id="querySystemTables" resultType="string">
        select name from `system`.tables where database=#{database}
    </select>

    <select id="queryMaterialzed" resultType="string">
        select name from `system`.tables where database=#{database} and (name = #{viewName} or name = #{clusterViewName})
    </select>

    <select id="queryDatasourceType" resultType="map" parameterType="map">
      select asset_code,asset_label from etl_parser_rule_category
      <where>
          <choose>
              <when test="parentCode != null and parentCode != ''">
                  AND parent_code = #{parentCode}
              </when>
              <otherwise>
                  AND parent_code is null
              </otherwise>
          </choose>
      </where>
    </select>

    <select id="queryDatasourceTypeTest" resultType="map" parameterType="map">
        select asset_code,asset_label from etl_parser_rule_category
        <where>
            <choose>
                <when test="parentCode != null and parentCode != ''">
                    AND parent_code = #{parentCode}
                </when>
                <otherwise>
                    AND parent_code is null
                </otherwise>
            </choose>
            <if test="parentCode != null and parentCode != ''">
                AND parent_code = #{parentCode}
            </if>
        </where>
    </select>

    <select id="selectedDatasource" resultType="map">
        select config_value from etl_reader_param
        where config_key = 'generic_datasource_type'
        group by config_value
    </select>

    <select id="queryDataLimitRoleId" resultType="string">
        select data_limit_extend_role_id from ums_sys_user
        where user_id = #{userId}
    </select>

    <select id="queryRoleResourceSearchLimit" resultType="long">
        select role_resource_search_limit from ums_sys_role
        where role_id = #{roleId}
    </select>

    <select id="queryUserResourceSearchLimit" resultType="long">
        select user_resource_search_limit from ums_sys_role
        where role_id = #{roleId}
    </select>
</mapper>