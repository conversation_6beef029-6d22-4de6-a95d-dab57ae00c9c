<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.etl.mapper.EtlAssetResourceMapper">

    <select id="deviceTypeAgg" parameterType="map" resultType="com.idss.datalake.etl.dto.DeviceTypeAggDTO">
        SELECT eprc.asset_code assetCode, eprc.asset_label deviceType, COUNT(es.id) total
        FROM etl_source es
        left join etl_reader_param erp ON es.id = erp.source_id AND erp.config_key = 'generic_datasource_type'
        left join etl_parser_rule_category eprc ON erp.config_value = eprc.asset_code
        left join etl_asset_resource ear ON es.id = ear.etl_source_id
        WHERE es.status = '1' AND (ear.del_flag = 0 or ear.del_flag is NULL)
        <if test="sourceName != null and sourceName != ''">
            and es.source_name like concat('%',#{sourceName},'%')
        </if>
        <if test="assetPurposeId != null and assetPurposeId != ''">
            and ear.asset_purpose_id = #{assetPurposeId}
        </if>
        GROUP BY eprc.asset_code,eprc.asset_label
    </select>

</mapper>