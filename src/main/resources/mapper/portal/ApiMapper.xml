<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.portal.mapper.ApiMapper">

    <select id="totalApi" parameterType="map" resultType="int">
        select count(1) cnt from api_info
    </select>

    <select id="totalApply" parameterType="map" resultType="int">
        select count(1) cnt from api_apply
        <where>
            <if test="auditStatus != null and auditStatus !=''">
              and audit_status = ${auditStatus}
            </if>
        </where>
    </select>

    <select id="totalInvoke" parameterType="map" resultType="int">
        select count(1) cnt from api_invoke_log
    </select>

    <select id="top" parameterType="map" resultType="map">
        SELECT
            b.name,
            count(1) cnt
        FROM
            api_invoke_log a
        LEFT JOIN api_info b ON a.api_id = b.id
        WHERE b.name IS NOT NULL
        GROUP BY name
        order by cnt
        limit 10
    </select>

    <select id="invokeTrend" parameterType="map" resultType="map">
        SELECT
            TO_CHAR(invoke_time,'YYYY-MM-DD') "formatDate",
            count(1) cnt
        FROM
            api_invoke_log
        group by "formatDate"
        order by "formatDate"
    </select>
</mapper>
