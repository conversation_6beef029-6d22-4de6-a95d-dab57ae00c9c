<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.portal.mapper.DatamartMapper">

    <select id="subscribeCategory" parameterType="map" resultType="map">
        SELECT
            c.type_name groupName,
            count(1) cnt
        FROM
            data_mart_subscribe a
        LEFT JOIN data_mart_asset b ON a.asset_id = b.id
        LEFT JOIN data_mart_type c ON b.type_id = c.id
        WHERE
            c.type_name IS NOT NULL
        GROUP BY
            c.type_name
    </select>

    <select id="subscribeTrend" parameterType="map" resultType="map">
        SELECT
            TO_CHAR(subscription_time,'YYYY-MM-DD') formatDate,
            count(1) cnt
        FROM
            data_mart_subscribe
        WHERE
            asset_id IS NOT NULL
        GROUP BY
            formatDate
        order by formatDate
    </select>

    <select id="groupCnt" parameterType="map" resultType="map">
        SELECT
            b.group_name groupName,
            count(1) cnt
        FROM
            data_mart_asset a
        LEFT JOIN data_mart_group b ON a.group_id = b.id
        WHERE
            b.group_name IS NOT NULL
        GROUP BY
            b.group_name
        ORDER BY
            cnt DESC
        LIMIT 10
    </select>
</mapper>
