<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.quota.shulan.mapper.BatchTaskViewMapper">

    <select id="flowList" resultType="com.idss.datalake.quota.dto.QuotaFlow">
        select flow_id,flow_name from batch_task_view where flow_name is not null group by flow_id, flow_name
    </select>
    <select id="listByFlowId" resultType="com.idss.datalake.quota.entity.BatchTaskView">
        select * from batch_task_view where flow_id = #{flowId} and source is not null
    </select>
    <select id="keytab" resultType="java.lang.String">
        select user_keytab from resource.bas_cluster_user where cluster_id = #{clusterId} and cluster_user_id = #{clusterUserId}
    </select>
    <select id="streamFlowList" resultType="com.idss.datalake.quota.dto.QuotaFlow">
        select flow_id,flow_name from stream_task_view where flow_name is not null group by flow_id, flow_name
    </select>
</mapper>
