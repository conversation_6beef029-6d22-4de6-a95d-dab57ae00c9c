<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.idss.datalake.datashare.cluster.mapper.TbClusterMapper">
    <select id="page" parameterType="java.util.Map" resultType="java.util.Map">
        select id,
               cluster_name "clusterName",
               cluster_type "clusterType",
               countNum,
               nodeNum,
               running_status "runningStatus",
               TO_CHAR(create_time,'YYYY-MM-DD HH:MI:SS') as "createTime"
        from tb_cluster
        left join (select cluster_id,count(cluster_id) as countNum from tb_tenant_cluster
        <if test="tenantId != null and tenantId != ''">
            WHERE tenant_id = #{tenantId}
        </if>
        group by cluster_id ) as t on tb_cluster.id=t.cluster_id
        left join (select cluster_id,count(cluster_id) as nodeNum from tb_cluster_node
        group by cluster_id ) as t_node on tb_cluster.id=t_node.cluster_id
        where del_flag = '0'
        <if test="clusterName != null and clusterName != ''">
            AND tb_cluster.cluster_name LIKE CONCAT('%',#{clusterName},'%')
        </if>
        <if test="clusterType != null and clusterType != ''">
            AND tb_cluster.cluster_type LIKE CONCAT('%',#{clusterType},'%')
        </if>
        <!-- 租户只查询自己关联的集群 -->
        <if test="tenantId != null and tenantId != ''">
            AND countNum IS NOT NULL
        </if>
        order by create_time desc
        LIMIT #{pageSize} offset #{beginIndex}
    </select>
    <select id="pageCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(*) as total
        from tb_cluster
        left join (select cluster_id,count(cluster_id) as countNum from tb_tenant_cluster
        <if test="tenantId != null and tenantId != ''">
            WHERE tenant_id = #{tenantId}
        </if>
        group by cluster_id ) as t
        on tb_cluster.id=t.cluster_id
        where del_flag = '0'
        <if test="clusterName != null and clusterName != ''">
            AND tb_cluster.cluster_name LIKE CONCAT('%',#{clusterName},'%')
        </if>
        <if test="clusterType != null and clusterType != ''">
            AND tb_cluster.cluster_type LIKE CONCAT('%',#{clusterType},'%')
        </if>
    </select>
    <select id="clusterPage" parameterType="java.util.Map" resultType="java.util.Map">
        select tb_cluster.cluster_name as clusterName , tb_tenant.tenant_name as tenantName ,tb_tenant_cluster.instance as
        instance,tb_tenant_cluster.topic as topic, TO_CHAR(tb_tenant.create_time,'YYYY-MM-DD HH:MI:SS') as createTime
        from tb_tenant
        left join tb_tenant_cluster on tb_tenant.id = tb_tenant_cluster.tenant_id
        left join tb_cluster on tb_cluster.id = tb_tenant_cluster.cluster_id
        where tb_tenant.del_flag = '0' AND tb_cluster.del_flag = '0' AND tb_cluster.id = #{id}
        <if test="instance != null and instance != ''">
            AND tb_tenant_cluster.instance LIKE CONCAT('%',#{instance},'%')
        </if>
        <if test="topic != null and topic != ''">
            AND tb_tenant_cluster.topic LIKE CONCAT('%',#{topic},'%')
        </if>
        <if test="tenantName != null and tenantName != ''">
            AND tb_tenant.tenant_name LIKE CONCAT('%',#{tenantName},'%')
        </if>
        order by createTime
        LIMIT #{pageSize} offset #{beginIndex}
    </select>
    <select id="select" resultType="java.util.Map">
`        select ttc.INSTANCE         as instance,
               ttc.CLUSTER_TYPE     as type,
               tc.NODE_ADDRESS      as node_address,
               tc.CLUSTER_USERNAME  as username,
               tc.CLUSTER_PASSWORD  as password,
               tc.status           as status
        from tb_tenant_cluster ttc
             left join tb_cluster tc on ttc.CLUSTER_ID = tc.ID
        where ttc.CLUSTER_TYPE in ('elasticsearch', 'clickhouse')
          and ttc.TENANT_ID = #{tenantId}
    </select>

    <select id="queryLastTime" parameterType="java.util.Map" resultType="java.lang.String">
        select TO_CHAR(MAX(c.create_time),'YYYY-MM-DD HH:MI:SS') as createTime
        from tb_cluster c
        join tb_tenant_cluster tc
        on c.id = tc.cluster_id
        where c.del_flag = '0'
        <if test="tenantId != null and tenantId != ''">
            AND tc.tenant_id = #{tenantId}
        </if>
        <if test="clusterType != null and clusterType != ''">
            AND c.cluster_type = #{clusterType}
        </if>
    </select>

    <select id="selectTenantIndex" resultType="map">
        select index_name indexName from etl_writer_es_config where status = 1
    </select>
</mapper>