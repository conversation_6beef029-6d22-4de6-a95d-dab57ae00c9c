<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.leak.mapper.clickhouse.EqptLogReportChMapper">



    <select id="getLogCount" resultType="com.idss.datalake.leak.pojo.StockCount">

        select ueba_flow_id as flowId,count(1) as count from security_log

        where   generic_create_time >= toDateTime64(#{start_time}/1000,3) and toDateTime64(#{end_time}/1000,3) >= generic_create_time

        <if test="branch_code != null and branch_code != ''">
            and eqpt_network_domain = #{branch_code}
        </if>
        <if test="vendor != null and vendor != ''">
            and eqpt_vendor = #{vendor}
        </if>
        <if test="eqpt_type != null and eqpt_type != ''">
            and eqpt_asset_type = #{eqpt_type}
        </if>




        <if test="ips !=null ">
            and eqpt_ip in
            <foreach item="item" collection="ips" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <choose>
            <when test="eqptBelong != null and eqptBelong == 1">
                and  eqpt_asset_dept = '安全管理中心'
            </when>
            <when test="eqptBelong != null and eqptBelong == 2">
                and  eqpt_asset_dept != '安全管理中心'
            </when>
        </choose>
          group by flowId
    </select>



    <select id="getLogTrendCount" resultType="com.idss.datalake.leak.pojo.StockCount">

        select ueba_flow_id as flowId,count(1) as count,toStartOfHour(generic_create_time) as dateStr  from security_log

        where
          generic_create_time >= toDateTime64(#{start_time}/1000,3) and toDateTime64(#{end_time}/1000,3) >= generic_create_time


        <if test="branch_code != null and branch_code != ''">
            and eqpt_network_domain = #{branch_code}
        </if>
        <if test="vendor != null and vendor != ''">
            and eqpt_vendor = #{vendor}
        </if>
        <if test="eqpt_type != null and eqpt_type != ''">
            and eqpt_asset_type = #{eqpt_type}
        </if>

        <if test="ips !=null ">
            and eqpt_ip in
            <foreach item="item" collection="ips" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <choose>
            <when test="eqptBelong != null and eqptBelong == 1">
                and  eqpt_asset_dept = '安全管理中心'
            </when>
            <when test="eqptBelong != null and eqptBelong == 2">
                and  eqpt_asset_dept != '安全管理中心'
            </when>
        </choose>
        group by flowId,dateStr
    </select>





    <select id="getLogCountByEqptIp" resultType="com.idss.datalake.leak.pojo.StockCount">

        select ueba_flow_id as flowId,count(1) as count,eqpt_ip  as eqptIp from security_log

        where
          generic_create_time >= toDateTime64(#{start_time}/1000,3) and toDateTime64(#{end_time}/1000,3) >= generic_create_time

        <if test="branch_code != null and branch_code != ''">
            and eqpt_network_domain = #{branch_code}
        </if>
        <if test="vendor != null and vendor != ''">
            and eqpt_vendor = #{vendor}
        </if>
        <if test="eqpt_type != null and eqpt_type != ''">
            and eqpt_asset_type = #{eqpt_type}
        </if>


        <if test="ips !=null ">
            and eqpt_ip in
            <foreach item="item" collection="ips" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <choose>
            <when test="eqptBelong != null and eqptBelong == 1">
                and  eqpt_asset_dept = '安全管理中心'
            </when>
            <when test="eqptBelong != null and eqptBelong == 2">
                and  eqpt_asset_dept != '安全管理中心'
            </when>
        </choose>
        group by flowId,eqpt_ip
    </select>




</mapper>