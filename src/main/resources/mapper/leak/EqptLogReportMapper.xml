<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.idss.datalake.leak.mapper.EqptLogReportMapper">


    <select id="findLogIpWithOutIp" resultType="map">

        select branch_code as branchCode,vendor_name as vendor,eqpt_type as eqptType from xty_eqpt_log_report_ip

        <if test="ew != null">
            ${ew.customSqlSegment}
        </if>

    </select>


    <insert id="insertReportIp" parameterType="map">
        insert into xty_eqpt_log_report_ip(branch_code,vendor_name,eqpt_type,eqpt_ip,collect_type)value (#{branch_code},#{vendor_name},#{eqpt_type},#{eqpt_ip},#{collect_type})
    </insert>


    <update id="updateEqptLogIp" parameterType="map">
        update xty_eqpt_log_report_ip set eqpt_type = #{eqpt_type},vendor_name = #{vendor_name}
        <if test="collect_type !=null and collect_type != ''">
            ,collect_type = #{collect_type}
        </if>
        where branch_code = #{branch_code} and eqpt_ip = #{eqpt_ip}
    </update>


    <select id="findExistLogIp" resultType="map" parameterType="map">
        select id from xty_eqpt_log_report_ip where branch_code = #{branch_code} and vendor_name = #{vendor_name} and eqpt_type = #{eqpt_type} and eqpt_ip = #{eqpt_ip}
    </select>


    <insert id="insertXtyLogReport" parameterType="map">
        insert into xty_log_report(id,time_id,vendor_name,eqpt_type,total_long,warn_toal_long,eqpt_ip,branch_code,send_time,create_time,raw_log,receive_count,miss_rate,stock_count,nokafka_stock_count,stock_rate,send_count,send_success_count)
        values(#{id},#{time_id},#{vendor_name},#{eqpt_type},#{total_long},#{warn_toal_long},#{eqpt_ip},#{branch_code},#{send_time},#{create_time},#{raw_log},#{receive_count},#{miss_rate},#{stock_count},#{nokafka_stock_count},#{stock_rate},#{send_count},#{send_success_count})
    </insert>


    <select id="findagzxList" resultType="string">
        select elri.eqpt_ip from xty_eqpt_log_report_ip elri left join xty_etl_eqpt_list_aqglzx eela on elri.branch_code
        =
        eela.branch_code
        and elri.vendor_name = eela.vendor and elri.eqpt_type = eela.eqpt_device_type and elri.eqpt_ip = eela.ip
        <where>
            elri.collect_type = 'kafka'
            <choose>
                <when test="eqptBelong != null and eqptBelong == 1">
                    and eela.eqpt_belong = '安全管理中心'
                </when>
                <when test="eqptBelong != null and eqptBelong == 2">
                    and eela.eqpt_belong != '安全管理中心'
                </when>
            </choose>
            <if test="branchCode != null and branchCode != ''">
                and elri.branch_code = #{branchCode}
            </if>
            <if test="vendorName != null and vendorName != ''">
                and elri.vendor_name = #{vendorName}
            </if>
            <if test="eqptType != null and eqptType != ''">
                and elri.eqpt_type = #{eqptType}
            </if>
        </where>
    </select>


    <select id="getSum" resultType="map">
        select sum(total_long) as total_long,sum(warn_toal_long) as warn_toal_long from xty_log_report where create_time
        >= #{begin_time} and #{end_time} >= create_time

        <if test="branch_code != null and branch_code != ''">
            and branch_code = #{branch_code}
        </if>
        <if test="vendor_name != null and vendor_name != ''">
            and vendor_name = #{vendor_name}
        </if>
        <if test="eqpt_type != null and eqpt_type != ''">
            and eqpt_type = #{eqpt_type}
        </if>


        and eqpt_ip in
        <foreach item="ip" collection="ips" separator="," open="(" close=")" index="">
            #{ip}
        </foreach>
    </select>


    <select id="getKafkaSource" resultType="long">
        select id from etl_source where source_type = 'KAFKA'
    </select>


    <select id="getTrendCount" resultType="com.idss.datalake.leak.pojo.TrendCount">
        select TO_CHAR(TO_TIMESTAMP(create_time/1000),'YYYY-MM-DD HH24') as dateStr,sum(total_long) as
        totol,sum(warn_toal_long) as warnTotol from xty_log_report where create_time >= #{begin_time} and #{end_time} >=
        create_time

        <if test="branch_code != null and branch_code != ''">
            and branch_code = #{branch_code}
        </if>
        <if test="vendor_name != null and vendor_name != ''">
            and vendor_name = #{vendor_name}
        </if>
        <if test="eqpt_type != null and eqpt_type != ''">
            and eqpt_type = #{eqpt_type}
        </if>


        <if test="ips !=null ">


            and eqpt_ip in
            <foreach item="ip" collection="ips" separator="," open="(" close=")" index="">
                #{ip}
            </foreach>
        </if>
        group by dateStr
    </select>


    <select id="findLogIpByCondition"
            resultType="map">

        select  elri.branch_code as branchCode,
                elri.vendor_name as vendor,
                elri.eqpt_type as eqptType,
                elri.eqpt_ip as eqptIp,
        IFNULL(collect_type, '') as collectType
        from xty_eqpt_log_report_ip elri left join xty_etl_eqpt_list_aqglzx eela
        on
        elri.branch_code = eela.branch_code
        and elri.vendor_name = eela.vendor and elri.eqpt_type = eela.eqpt_device_type and elri.eqpt_ip = eela.ip
        <if test="ew != null">
            ${ew.customSqlSegment}
        </if>

    </select>


    <select id="getSumByDeatil" resultType="com.idss.datalake.leak.pojo.TrendCount">
        select eqpt_ip as eqptIp, sum(total_long) as sumTotol,sum(warn_toal_long) as sumWarnTotol from xty_log_report
        where create_time
        >= #{begin_time} and #{end_time} >= create_time
        and vendor_name = #{vendor_name} and eqpt_type = #{eqpt_type} and branch_code = #{branch_code}
        and eqpt_ip in
        <foreach item="ip" collection="ips" separator="," open="(" close=")" index="">
            #{ip}
        </foreach>
        group by eqpt_ip
    </select>


</mapper>