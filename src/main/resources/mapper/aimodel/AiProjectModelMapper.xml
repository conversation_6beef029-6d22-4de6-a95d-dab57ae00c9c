<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.aimodel.mapper.AiProjectModelMapper">

    <select id="queryPage" parameterType="com.idss.datalake.aimodel.dto.AiProjectPageRequest" resultType="com.idss.datalake.aimodel.entity.AiProjectModel">
        select t1.*,t2.TENANT_NAME from ai_project_model t1 join  tb_tenant t2 on t1.tenant_id = t2.ID  where t1.create_user = #{userName}
        <if test="nameSpace != null and nameSpace !=''">
            and t1.name_space like concat('%',#{nameSpace},'%')
        </if>
        <if test="enName != null and enName !=''">
            and t1.en_name like concat('%',#{enName},'%')
        </if>
        <if test="tenantId != null">
            and t1.tenant_id = #{tenantId}
        </if>
        <if test="status != null">
            and t1.status = #{status}
        </if>
        order by t1.update_time desc
    </select>
</mapper>
