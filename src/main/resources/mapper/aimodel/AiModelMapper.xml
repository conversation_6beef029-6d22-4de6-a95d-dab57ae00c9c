<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.aimodel.mapper.AiModelMapper">

    <select id="queryPage" resultType="com.idss.datalake.aimodel.entity.AiModel" parameterType="com.idss.datalake.aimodel.dto.AiModelPageRequest">
        select * from ai_model t1 where t1.tenant_id = #{tenantId}
        <if test="modelName != null and modelName !=''">
            and t1.model_name like concat('%',#{modelName},'%')
        </if>
        <if test="trainingFramework != null and trainingFramework !=''">
            and t1.training_framework like concat('%',#{trainingFramework},'%')
        </if>
        <if test="serviceType != null and serviceType !=''">
            and t1.service_type like concat('%',#{serviceType},'%')
        </if>
        order by t1.update_time desc
    </select>
</mapper>
