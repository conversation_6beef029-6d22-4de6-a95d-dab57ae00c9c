<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.aimodel.mapper.AiModelVersionMapper">

    <select id="queryPage" resultType="com.idss.datalake.aimodel.entity.AiModelVersion"
    parameterType="com.idss.datalake.aimodel.dto.AiModelVersionPageRequest">
        select * from ai_model_version t1 where t1.model_id = #{modelId}
        <if test="modelVersion != null and modelVersion !=''">
            and t1.model_version like concat('%',#{modelVersion},'%')
        </if>
        <if test="modelType != null">
            and t1.model_type = #{modelType}
        </if>
        order by t1.update_time desc
    </select>
</mapper>
