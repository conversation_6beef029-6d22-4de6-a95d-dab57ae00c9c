<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.aimodel.mapper.AiMirrorMapper">

    <select id="queryPage" parameterType="com.idss.datalake.aimodel.dto.AiMirrorPageRequest" resultType="com.idss.datalake.aimodel.entity.AiMirror">
        select t1.*,t2.repository_name,t2.repository_addr
        from ai_mirror t1 join ai_mirror_repository t2 on t1.repository_id = t2.id
        where 1=1
        <if test="tenantId != null and tenantId !=''">
            and t1.tenant_id = #{tenantId}
        </if>
        <if test="mirrorName != null and mirrorName !=''">
            and t1.mirror_name like concat('%',#{mirrorName},'%')
        </if>
        <if test="createUser != null and createUser !=''">
            and t1.create_user like concat('%',#{createUser},'%')
        </if>
        <if test="repositoryAddr != null and repositoryAddr !=''">
            and t2.repository_addr like concat('%',#{repositoryAddr},'%')
        </if>
        <if test="mirrorUse != null and mirrorUse !=''">
            and t1.mirror_use like concat('%',#{mirrorUse},'%')
        </if>
        <if test="repositoryId != null">
            and t1.repository_id = #{repositoryId}
        </if>
        <if test="functionClassify != null and functionClassify !=''">
            and t1.function_classify = #{functionClassify}
        </if>
        <if test="publishStatus != null and publishStatus !=''">
            and t1.publish_status = #{publishStatus}
        </if>
        order by create_time desc
    </select>
</mapper>
