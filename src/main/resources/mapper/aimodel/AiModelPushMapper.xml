<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.aimodel.mapper.AiModelPushMapper">

    <select id="queryPage" resultType="com.idss.datalake.aimodel.dto.AiPushPageVo"
     parameterType="com.idss.datalake.aimodel.dto.AiPushPageRequest">
        select t1.id,
               t2.model_name,
               t2.service_type,
               t3.model_version,
               t1.canary,
               t1.deploy_url,
               t1.max_replica,
               t1.min_replica,
               t1.deploy_status,
               t1.resource_cpu,
               t1.resource_gpu,
               t1.resource_memory,
               t1.create_user,
               t1.create_time
        from ai_model_push t1
                 join ai_model t2 on t1.model_id = t2.id
                 join ai_model_version t3 on t1.version_id = t3.id
        where t1.tenant_id = #{tenantId}
        <if test="modelName != null and modelName !=''">
            and t2.model_name like  concat('%',#{modelName},'%')
        </if>
        <if test="modelVersion != null and modelVersion !=''">
            and t3.model_version like   concat('%',#{modelVersion},'%')
        </if>
        <if test="serviceType != null and serviceType !=''">
            and t2.service_type like  concat('%',#{serviceType},'%')
        </if>
        <if test="deployStatus != null">
            and t1.deploy_status = #{deployStatus}
        </if>
        order by t1.create_time desc
    </select>
</mapper>
