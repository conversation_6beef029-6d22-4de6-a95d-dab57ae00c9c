<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.aimodel.mapper.AiMirrorRepositoryMapper">

    <select id="queryPage" parameterType="com.idss.datalake.aimodel.dto.AiMirrorRepositoryPageRequest" resultType="com.idss.datalake.aimodel.entity.AiMirrorRepository">
        select * from ai_mirror_repository where tenant_id = #{tenantId}
        <if test="repositoryName != null and repositoryName !=''">
            and repository_name like concat('%',#{repositoryName},'%')
        </if>
        <if test="userName != null and userName !=''">
            and user_name like concat('%',#{userName},'%')
        </if>
        <if test="k8sHubsecret != null and k8sHubsecret !=''">
            and k8s_hubsecret like concat('%',#{k8sHubsecret},'%')
        </if>
        order by update_time desc
    </select>
</mapper>
