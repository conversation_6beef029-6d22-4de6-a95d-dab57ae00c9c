<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.aimodel.mapper.AiNotebookMapper">

    <select id="queryPage" parameterType="com.idss.datalake.aimodel.dto.AiNotebookPageRequest"
            resultType="com.idss.datalake.aimodel.entity.AiNotebook">
        select t1.*,t2.repository_name,t3.mirror_name,t4.name_space as project_name
        from ai_notebook t1
        join ai_mirror_repository t2 on t1.repository_id = t2.id
        join ai_project_model t4 on t1.project_id = t4.id
        join ai_mirror t3 on t1.mirror_id = t3.id where t1.tenant_id = #{tenantId}
        <if test="name != null and name !=''">
            and t1.name like concat('%',#{name},'%')
        </if>
        <if test="status != null">
            and t1.status = #{status}
        </if>
        order by t1.update_time desc
    </select>
</mapper>
