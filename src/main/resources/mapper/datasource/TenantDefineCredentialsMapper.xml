<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datasource.mapper.TenantDefineCredentialsMapper">

    <select id="page" resultType="com.idss.datalake.datasource.entity.TenantDefineCredentials">
        select
        t.id,
        t.name,
        t.username,
        t.password,
        t.description,
        t.last_used,
        t.create_time,
        t.create_user,
        t.update_time,
        t.update_user,
        t.tenant_id,
        t.tenant_name
        from
        tenant_define_credentials t where t.tenant_id = #{tenantId}
        <where>
            <if test="name != null and name != ''">
                and t.name like concat('%', #{name}, '%')
            </if>
        </where>
        order by t.id desc
    </select>
</mapper>
