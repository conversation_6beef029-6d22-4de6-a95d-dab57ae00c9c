<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datasource.mapper.TenantDefineDatasourcesMapper">

    <select id="page" resultType="com.idss.datalake.datasource.entity.TenantDefineDatasources">
        select * from tenant_define_datasources where tenant_id = #{tenantId}
        <if test="name != null and name != ''">
            and name like concat('%', #{name}, '%')
        </if>
        order by id desc
    </select>
</mapper>
