<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datasource.mapper.TenantDefineKerberosConfigsMapper">

    <select id="page" resultType="com.idss.datalake.datasource.entity.TenantDefineKerberosConfigs">
        select * from tenant_define_kerberos_configs where tenant_id = #{tenantId}
        <if test="name != null and name != ''">
            and name like concat('%',#{name},'%')
        </if>
        <if test="enabled != null">
            and enabled = #{enabled}
        </if>
        order by id desc
    </select>
</mapper>
