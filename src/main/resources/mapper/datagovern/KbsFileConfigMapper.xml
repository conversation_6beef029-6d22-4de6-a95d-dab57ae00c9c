<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.kbs.mapper.KbsFileConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.idss.datalake.datagovern.metadata.model.kbs.entity.KbsFileConfig">
        <id column="id" property="id"/>
        <result column="template_name" property="templateName"/>
        <result column="kbs_account" property="kbsAccount"/>
        <result column="keytab_file_path" property="keytabFilePath"/>
        <result column="krb5_file_path" property="krb5FilePath"/>
        <result column="jaas_file_path" property="jaasFilePath"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="flag" property="flag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, template_name, kbs_account, keytab_file_path, krb5_file_path, jaas_file_path, remark, create_time, create_user, update_time, tenant_id, flag
    </sql>

    <select id="queryPage" parameterType="com.idss.datalake.datagovern.metadata.model.kbs.pojo.dto.KbsTemplatePageRequest"
            resultType="com.idss.datalake.datagovern.metadata.model.kbs.entity.KbsFileConfig">
        select * from qua_web_kbs_file_config t where t.tenant_id = #{tenantId} and t.flag = 1
        <if test="nameOrAccount != null and nameOrAccount !=''">
            and t.template_name like concat('%',#{nameOrAccount},'%') or t.kbs_account like
            concat('%',#{nameOrAccount},'%')
        </if>
        order by t.update_time desc
    </select>

</mapper>
