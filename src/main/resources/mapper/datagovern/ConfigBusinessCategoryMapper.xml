<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.config.mapper.QuaConfigBusinessCategoryMapper">

    <select id="queryCategoryPage" parameterType="com.idss.datalake.datagovern.config.model.CategoryRequestDto"
            resultType="com.idss.datalake.datagovern.config.entity.QuaConfigBusinessCategory">
        SELECT
            id,
            category_name,
            category_desc,
            create_user,
            create_time
        FROM
            qua_config_business_category
        <where>
            tenant_id = #{tenantId}
            and flag = '1'
            <if test="categoryName != null and categoryName != ''">
                and category_name like concat('%',#{categoryName},'%')
            </if>
        </where>
    </select>
</mapper>
