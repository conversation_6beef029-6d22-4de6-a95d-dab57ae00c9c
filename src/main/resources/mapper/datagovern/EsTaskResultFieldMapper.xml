<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.job.mapper.EsTaskResultFieldMapper">
    <select id="exportEs" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.EsExportDto">
        select t1.es_ip_port,
               t1.es_user_name,
               t1.es_user_password,
               t1.es_kbs_template_id,

               t3.create_time                snapshoot_create_time,
               t3.create_user                         as snapshoot_create_user,
               t3.snapshoot_version,

               t2.id                      as detail_index_id,
               t2.index_name_cn           as detail_index_name_cn,
               t2.index_dscribe           as detail_index_dscribe,
               t2.index_owner             as detail_index_owner,
               t2.is_sensitive            as detail_index_is_sensitive,
               t2.create_time             as detail_index_create_time,
               t2.create_user             as detail_index_create_user,
               t2.update_time             as detail_index_update_time,
               t2.update_user             as detail_index_update_user,
               t2.is_available            as detail_index_is_available,
               t2.first_snapshoot_version as detail_index_first_snapshoot_version,
               t2.last_snapshoot_version  as detail_index_last_snapshoot_version,
               t2.host_address            as detail_index_host_address,
               t2.port                    as detail_index_port,
               t2.index_name              as detail_index_index_name,
               t2.type_name               as detail_index_type_name,

               t4.id                      as detail_field_id,
               t4.field_name              as detail_field_field_name,
               t4.field_name_cn           as detail_field_field_name_cn,
               t4.is_sensitive            as detail_field_is_sensitive,
               t4.create_time             as detail_field_create_time,
               t4.create_user             as detail_field_create_user,
               t4.update_time             as detail_field_update_time,
               t4.update_user             as detail_field_update_user,
               t4.is_available            as detail_field_is_available,
               t4.first_snapshoot_version as detail_field_first_snapshoot_version,
               t4.last_snapshoot_version  as detail_field_last_snapshoot_version,

               t3.id                      as snapshoot_index_id,
               t3.cluster_name            as snapshoot_index_cluster_name,
               t3.cluster_uuid            as snapshoot_index_cluster_uuid,
               t3.index_state             as snapshoot_index_index_state,
               t3.index_settings          as snapshoot_index_index_settings,
               t3.aliases                 as snapshoot_index_aliases,
               t3.primary_terms           as snapshoot_index_primary_terms,
               t3.in_sync_allocations     as snapshoot_index_in_sync_allocations,

               t5.id                      as snapshoot_field_id,
               t5.field_data_type         as snapshoot_field_field_data_type,
               t5.analyzer                as snapshoot_field_analyzer,
               t5.boost                   as snapshoot_field_boost,
               t5.coerce                  as snapshoot_field_coerce,
               t5.copy_to                 as snapshoot_field_copy_to,
               t5.doc_values              as snapshoot_field_doc_values,
               t5.dynamic                 as snapshoot_field_dynamic,
               t5.eager_global_ordinals   as snapshoot_field_eager_global_ordinals,
               t5.enabled                 as snapshoot_field_enabled,
               t5.fielddata               as snapshoot_field_fielddata,
               t5.fields                  as snapshoot_field_fields,
               t5.format                  as snapshoot_field_format,
               t5.ignore_above            as snapshoot_field_ignore_above,
               t5.ignore_malformed        as snapshoot_field_ignore_malformed,
               t5.index_options           as snapshoot_field_index_options,
               t5.index_phrases           as snapshoot_field_index_phrases,
               t5.index_prefixes          as snapshoot_field_index_prefixes,
               t5.index                 as snapshoot_field_index,
               t5.meta                    as snapshoot_field_meta,
               t5.normalizer              as snapshoot_field_normalizer,
               t5.norms                   as snapshoot_field_norms,
               t5.null_value              as snapshoot_field_null_value,
               t5.position_increment_gap  as snapshoot_field_position_increment_gap,
               t5.properties              as snapshoot_field_properties,
               t5.search_analyzer         as snapshoot_field_search_analyzer,
               t5.similarity              as snapshoot_field_similarity,
               t5.store                   as snapshoot_field_store,
               t5.term_vector             as snapshoot_field_term_vector,
               t5.field_length            as snapshoot_field_field_length,
               t5.field_precision         as snapshoot_field_field_precision,
               t5.snapshoot_version       as snapshoot_field_snapshoot_version
        from (select * from qua_web_es_task_result_field t5 where t5.element_id = #{elementId} and t5.snapshoot_version = #{version}) t5,
             (select * from qua_web_es_element_detail_field t4 where t4.element_id = #{elementId}) t4,
             (select * from qua_web_es_task_result_index t3 where t3.element_id = #{elementId} and t3.snapshoot_version = #{version} and t3.task_id = #{taskId}) t3,
             (select * from qua_web_es_element_detail_index t2 where t2.element_id = #{elementId}) t2,
             (select * from qua_wab_element t1 where t1.id = #{elementId}) t1
        where t5.index_name = t4.index_name
          and t5.field_name = t4.field_name
          and t4.index_name = t3.index_name
          and t3.index_name = t2.index_name
          and t5.index_id = t3.id
    </select>

</mapper>
