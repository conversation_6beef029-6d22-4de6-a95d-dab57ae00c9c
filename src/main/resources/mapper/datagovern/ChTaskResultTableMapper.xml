<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.job.mapper.ChTaskResultTableMapper">
    <select id="getTableNode" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTableDto">
        select t1.id as table_id, t1.table_name, count(t2.id) cnt
        from qua_web_ch_task_result_table t1
        left join qua_web_ch_task_result_column t2 on (t1.id = t2.table_id)
        where t1.db_id = #{dbId}
        and t1.snapshoot_version = #{snapshootVersion}
        group by t1.id, t1.table_name
        order by table_name
    </select>

    <select id="getMysqlTableNode" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTableDto">
        select t1.id as table_id, t1.table_name, count(t2.id) cnt
        from qua_web_mysql_task_result_table t1
        left join qua_web_mysql_task_result_column t2 on (t1.id = t2.table_id)
        where t1.db_id = #{dbId}
        and t1.snapshoot_version = #{snapshootVersion}
        group by t1.id, t1.table_name
        order by table_name
    </select>

    <select id="getHiveTableNode" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTableDto">
        select t1.id as table_id, t1.table_name, count(t2.id) cnt
        from qua_web_hive_task_result_table t1
        left join qua_web_hive_task_result_column t2 on (t1.id = t2.table_id)
        where t1.db_id = #{dbId}
        and t1.snapshoot_version = #{snapshootVersion}
        group by t1.id, t1.table_name
        order by table_name
    </select>

    <select id="getMysqlTablePage" resultType="com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeTablePage">
        select * from qua_web_mysql_element_detail_table
        where element_id = #{elementId}
        and db_name = #{dbName}
        and last_snapshoot_version = #{snapshootVersion}
        <if test="tableName != null and tableName != ''">
            and table_name like concat('%',#{tableName},'%')
        </if>
        <if test="tableNameCn != null and tableNameCn != ''">
            and table_name_cn like concat('%',#{tableNameCn},'%')
        </if>
        <if test="tableOwner != null and tableOwner != ''">
            and table_owner like concat('%',#{tableOwner},'%')
        </if>
    </select>

    <select id="getChTablePage" resultType="com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeTablePage">
        select * from qua_web_ch_element_detail_table
        where element_id = #{elementId}
        and db_name = #{dbName}
        and last_snapshoot_version = #{snapshootVersion}
        <if test="tableName != null and tableName != ''">
            and table_name like concat('%',#{tableName},'%')
        </if>
        <if test="tableNameCn != null and tableNameCn != ''">
            and table_name_cn like concat('%',#{tableNameCn},'%')
        </if>
        <if test="tableOwner != null and tableOwner != ''">
            and table_owner like concat('%',#{tableOwner},'%')
        </if>
    </select>

    <select id="getHiveTablePage" resultType="com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeTablePage">
        select * from qua_web_hive_element_detail_table
        where element_id = #{elementId}
        and db_name = #{dbName}
        and last_snapshoot_version = #{snapshootVersion}
        <if test="tableName != null and tableName != ''">
            and table_name like concat('%',#{tableName},'%')
        </if>
        <if test="tableNameCn != null and tableNameCn != ''">
            and table_name_cn like concat('%',#{tableNameCn},'%')
        </if>
        <if test="tableOwner != null and tableOwner != ''">
            and table_owner like concat('%',#{tableOwner},'%')
        </if>
    </select>

    <select id="getPanweiTableNode" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTableDto">
        select t1.id as table_id, t1.table_name, count(t2.id) cnt
        from qua_web_panwei_task_result_table t1
        left join qua_web_panwei_task_result_column t2 on (t1.id = t2.table_id)
        where t1.db_id = #{dbId}
        and t1.snapshoot_version = #{snapshootVersion}
        group by t1.id, t1.table_name
    </select>
</mapper>
