<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.dataquality.mapper.QuaDyncThresholdMapper">

    <select id="jobLogPage" resultType="com.idss.datalake.datagovern.dataquality.dto.DyncThresholdJobPageVo"
            parameterType="com.idss.datalake.datagovern.dataquality.dto.DyncThresholdJobPageRequest">
        select t2.job_name,
        t1.create_time,
        t1.result,
        t1.fail_error
        from qua_dync_result t1
        join qua_dync_threshold t2 on t1.dync_id = t2.id
        where t1.dync_id = #{id}
        <if test="startTime != null and endTime != null">
            and t1.create_time between #{startTime} and #{endTime}
        </if>
        <if test="result != null">
            and t1.result = #{result}
        </if>
        order by t1.create_time desc
    </select>
    <select id="page" parameterType="com.idss.datalake.datagovern.dataquality.dto.DyncThresholdPageRequest"
            resultType="com.idss.datalake.datagovern.dataquality.entity.QuaDyncThreshold">
        select * from qua_dync_threshold where tenant_id = #{tenantId}
        <if test="jobName != null and jobName != ''">
            and job_name like concat('%',#{jobName},'%')
        </if>
        <if test="dataName != null and dataName != ''">
            and (table_name like concat('%',#{dataName},'%') or index_name like ('%',#{dataName},'%'))
        </if>
        order by create_time desc
    </select>
</mapper>
