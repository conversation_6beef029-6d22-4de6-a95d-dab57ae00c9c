<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.job.mapper.QuaTaskMapper">

    <select id="getMapToJobLeastVersion" resultType="java.lang.String">
        select t3.snapshoot_version
        from qua_wab_element t1
        left join qua_web_job t2 on t1.id = t2.element_id
        left join qua_web_task t3 on t2.id = t3.job_id
        where t1.id = #{elementId}
        and t2.flag = 1
        and t3.status = 2
        order by t3.create_time desc
        limit 1
    </select>

    <select id="queryDetailFieldPage" parameterType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.EsJobFieldRequestDto"
            resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob.EsFieldInfoVo">
        select t1.*,
        t2.id as snapshoot_id,
        t2.create_time as snapshoot_create_time,
        t2.create_user as snapshoot_create_user,
        t2.snapshoot_version,
        t2.field_data_type,
        t2.analyzer,
        t2.boost,
        t2.coerce,
        t2.copy_to,
        t2.doc_values,
        t2.dynamic,
        t2.eager_global_ordinals,
        t2.enabled,
        t2.fielddata,
        t2.fields,
        t2.format,
        t2.ignore_above,
        t2.ignore_malformed,
        t2.index_options,
        t2.index_phrases,
        t2.index_prefixes,
        t2.index,
        t2.meta,
        t2.normalizer,
        t2.norms,
        t2.null_value,
        t2.position_increment_gap,
        t2.properties,
        t2.search_analyzer,
        t2.similarity,
        t2.store,
        t2.term_vector,
        t2.field_length,
        t2.field_precision
        from qua_web_es_element_detail_field t1
        left join qua_web_es_task_result_field t2 on t1.element_id = t2.element_id
        where t1.element_id = #{elementId}
        and t2.index_id = #{indexId}
        and t1.index_name = #{indexName}
        and t1.index_name = t2.index_name
        and t1.field_name = t2.field_name
        and t2.snapshoot_version = #{snapshootVersion}
        <if test="fieldNameCn != null and fieldNameCn != ''">
            and (t1.field_name_cn like concat('%',#{fieldNameCn},'%') or t1.field_name like
            concat('%',#{fieldNameCn},'%'))
        </if>
        order by t1.sort asc,t1.id asc
    </select>

    <select id="queryPage" parameterType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.VersionPageRequestDto"
            resultType="com.idss.datalake.datagovern.metadata.model.job.entity.QuaTask">
        select t.*
        from qua_web_task t
        where t.element_id = #{elementId}
        and t.status = 2
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.snapshoot_version like concat('%',#{snapshootVersion},'%')
        </if>
        <if test="startTime != null and endTime != null">
            and t.start_time <![CDATA[ >= ]]> #{startTime} and t.start_time <![CDATA[ <= ]]> #{endTime}
        </if>
        order by t.snapshoot_version desc
    </select>

    <select id="queryEsTaskPage" parameterType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.TaskPageRequestDto"
            resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.vo.esjob.EsTaskVo">
        select t1.id as job_id,
        t1.job_state,
        t1.job_name,
        t1.config_date_time,
        t2.id as task_id,
        t2.task_no,
        t2.snapshoot_version,
        t1.job_type,
        t2.start_time,
        t2.end_time,
        COALESCE(t2.task_progress, '0%') as task_progress,
        t2.status,
        t2.result
        from qua_web_job t1
        join qua_web_task t2 on t1.id = t2.job_id
        where t1.element_id = #{elementId}
        and t1.flag = 1
        <if test="jobType != null">
            and t1.job_type = #{jobType}
        </if>
        <if test="startTime != null and endTime != null">
            and t2.start_time <![CDATA[ >= ]]> #{startTime} and t2.start_time <![CDATA[ <= ]]> #{endTime}
        </if>
        order by t2.create_time desc
    </select>

    <select id="queryDetailColumnPage" parameterType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.ChJobColumnRequestDto"
            resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.vo.chjob.ChColumnInfoVo">
        select t1.*,
        t2.id as snapshoot_id,
        t2.create_time as snapshoot_create_time,
        t2.create_user as snapshoot_create_user,
        t2.snapshoot_version,
        t2.type,
        t2.position,
        t2.default_kind,
        t2.default_expression,
        t2.data_compressed_bytes,
        t2.data_uncompressed_bytes,
        t2.marks_bytes,
        t2.comment,
        t2.is_in_partition_key,
        t2.is_in_sorting_key,
        t2.is_in_primary_key,
        t2.is_in_sampling_key,
        t2.compression_codec,
        t2.is_nullable,
        t2.column_length,
        t2.column_precision
        from qua_web_ch_element_detail_column t1
        left join qua_web_ch_task_result_column t2 on t1.element_id = t2.element_id
        where t1.element_id = #{elementId}
        and t1.db_name = t2.db_name
        and t1.table_name = t2.table_name
        and t1.column_name = t2.column_name
        and t2.table_id = #{tableId}
        and t2.table_name = #{tableName}
        and t2.snapshoot_version = #{snapshootVersion}
        <if test="columnNameCn != null and columnNameCn != ''">
            and (t1.column_name_cn like concat('%',#{columnNameCn},'%') or t1.column_name like
            concat('%',#{columnNameCn},'%'))
        </if>
        order by t1.sort asc,t1.id asc
    </select>

    <select id="queryMysqlDetailColumnPage" parameterType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.ChJobColumnRequestDto"
            resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.vo.mysqljob.MysqlColumnInfoVo">
        select t1.*,
        t2.id as snapshoot_id,
        t2.create_time as snapshoot_create_time,
        t2.create_user as snapshoot_create_user,
        t2.snapshoot_version,
        t2.type,
        t2.column_key,
        t2.comment,
        t2.is_nullable,
        t2.column_length,
        t2.column_precision
        from qua_web_mysql_element_detail_column t1
        left join qua_web_mysql_task_result_column t2 on t1.element_id = t2.element_id
        where t1.element_id = #{elementId}
        and t1.db_name = t2.db_name
        and t1.table_name = t2.table_name
        and t1.column_name = t2.column_name
        and t2.table_id = #{tableId}
        and t2.table_name = #{tableName}
        and t2.snapshoot_version = #{snapshootVersion}
        <if test="columnNameCn != null and columnNameCn != ''">
            and (t1.column_name_cn like concat('%',#{columnNameCn},'%') or t1.column_name like
            concat('%',#{columnNameCn},'%'))
        </if>
        order by t1.sort asc,t1.id asc
    </select>

    <select id="queryHiveDetailColumnPage" parameterType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.ChJobColumnRequestDto"
            resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.vo.hivejob.HiveColumnInfoVo">
        select t1.*,
        t2.id as snapshoot_id,
        t2.create_time as snapshoot_create_time,
        t2.create_user as snapshoot_create_user,
        t2.snapshoot_version,
        t2.type,
        t2.comment,
        t2.is_nullable,
        t2.column_length,
        t2.column_precision
        from qua_web_hive_element_detail_column t1
        left join qua_web_hive_task_result_column t2 on t1.element_id = t2.element_id
        where t1.element_id = #{elementId}
        and t1.db_name = t2.db_name
        and t1.table_name = t2.table_name
        and t1.column_name = t2.column_name
        and t2.table_id = #{tableId}
        and t2.table_name = #{tableName}
        and t2.snapshoot_version = #{snapshootVersion}
        <if test="columnNameCn != null and columnNameCn != ''">
            and (t1.column_name_cn like concat('%',#{columnNameCn},'%') or t1.column_name like
            concat('%',#{columnNameCn},'%'))
        </if>
        order by t1.sort asc,t1.id asc
    </select>

    <select id="queryPanweiDetailColumnPage" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.vo.panweijob.PanweiColumnInfoVo">
        select t1.*,
        t2.id as snapshoot_id,
        t2.create_time as snapshoot_create_time,
        t2.create_user as snapshoot_create_user,
        t2.snapshoot_version,
        t2.column_type,
        t2.column_length,
        t2.is_not_null,
        t2.default_value,
        t2.column_comment
        from qua_web_panwei_element_detail_column t1
        left join qua_web_panwei_task_result_column t2 on t1.element_id = t2.element_id
        where t1.element_id = #{elementId}
        and t1.db_name = t2.db_name
        and t1.table_name = t2.table_name
        and t1.column_name = t2.column_name
        and t2.table_id = #{tableId}
        and t2.table_name = #{tableName}
        and t2.snapshoot_version = #{snapshootVersion}
        <if test="columnNameCn != null and columnNameCn != ''">
            and (t1.column_name_cn like concat('%',#{columnNameCn},'%') or t1.column_name like
            concat('%',#{columnNameCn},'%'))
        </if>
        order by t1.sort asc,t1.id asc
    </select>
</mapper>
