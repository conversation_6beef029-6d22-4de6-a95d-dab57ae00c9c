<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.detail.mapper.QuaWebHiveElementDetailColumnMapper">


    <select id="queryDetailColumnPage"
            resultType="com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementDetailVo">
        select t1.id,
        t1.column_name,
        t1.column_name_cn,
        t1.is_required,
        t2.type as column_type
        from qua_web_hive_element_detail_column t1
        left join qua_web_hive_task_result_column t2 on (t1.element_id = t2.element_id and t1.db_name = t2.db_name and t1.table_name = t2.table_name and t1.column_name = t2.column_name)
        join (select max(snapshoot_version) as snapshoot_version from qua_web_hive_task_result_column where element_id = #{elementId}) as t3 on t2.snapshoot_version = t3.snapshoot_version
        where t1.element_id = #{elementId} and t1.db_name = #{dbName} and t1.table_name = #{tableName}
        <if test="columnName != null and columnName != ''">
            and t1.column_name like concat('%',#{columnName},'%')
        </if>

    </select>
</mapper>
