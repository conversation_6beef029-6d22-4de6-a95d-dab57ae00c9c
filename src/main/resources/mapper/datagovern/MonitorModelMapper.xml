<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorModelMapper">

    <select id="queryModelPage" parameterType="com.idss.datalake.datagovern.dataquality.model.ModelRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.ModelResponseVo">
        SELECT
            id,
            model_name,
            database_name,
            model_desc,
            create_user,
            create_time,
            a.element_type,
            a.open_status,
            b.table_cnt
        FROM
          qua_monitor_model a
        LEFT JOIN (
            SELECT
            model_id,
            count(1) table_cnt
            FROM
            qua_monitor_model_resource
            GROUP BY
            model_id
        ) b ON a.id = b.model_id
        <where>
            a.tenant_id = #{tenantId}
            and a.flag = '1'
            <if test="modelName != null and modelName != ''">
                and a.model_name like concat('%',#{modelName},'%')
            </if>
        </where>
        order by a.create_time desc
    </select>
    
    <select id="taskTrend" parameterType="map" resultType="map">
        select TO_CHAR(start_time, 'YYYY-MM-DD') "createTime", count(1) cnt
        from qua_monitor_task
        where model_id = #{modelId}
        and start_time <![CDATA[ >= ]]> to_timestamp(#{startDate}, 'YYYY-MM-DD HH24:MI:SS')
        and start_time <![CDATA[ <= ]]> to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
        group by "createTime"
        order by "createTime" desc
    </select>

    <select id="warnCount" resultType="int" parameterType="map">
        select count(1) cnt
        from insight_alarm_platform_record
        where alarm_type = 'dataQualityAlarm'
        and alarm_time <![CDATA[ >= ]]> to_timestamp(#{startDate}, 'YYYY-MM-DD HH24:MI:SS')
        and alarm_time <![CDATA[ <= ]]> to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
    </select>

    <select id="warnRisk" resultType="map" parameterType="map">
        select alarm_serious "alarmSerious", count(1) cnt
        from insight_alarm_platform_record
        where alarm_type = 'dataQualityAlarm'
        and alarm_time <![CDATA[ >= ]]> to_timestamp(#{startDate}, 'YYYY-MM-DD HH24:MI:SS')
        and alarm_time <![CDATA[ <= ]]> to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
        group by alarm_serious
    </select>

    <select id="jobTrend" parameterType="map" resultType="map">
        select create_time "createTime", count(1) cnt
        from (select TO_CHAR(a.start_time, 'YYYY-MM-DD') create_time, b.element_type
              from qua_monitor_task a
                       left join qua_monitor_model b on a.model_id = b.id
              where b.element_type = #{elementType}
                and a.tenant_id = #{tenantId}
                and start_time <![CDATA[ >= ]]> to_timestamp(#{startDate},'YYYY-MM-DD HH24:MI:SS')
                and start_time <![CDATA[ <= ]]> to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')) c
        group by "createTime", element_type
    </select>
</mapper>
