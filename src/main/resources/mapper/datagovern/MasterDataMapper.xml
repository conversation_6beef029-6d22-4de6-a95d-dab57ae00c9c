<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.masterdata.mapper.QuaMasterDataMapper">

    <select id="queryMasterDataPage" parameterType="com.idss.datalake.datagovern.masterdata.model.MasterDataRequestDto"
            resultType="com.idss.datalake.datagovern.masterdata.model.MasterDataResponseVo">
        SELECT
            id,
            field_name,
            field_code,
            field_desc,
            data_type,
            field_length,
            field_precision,
            is_null,
            is_unique,
            create_time,
            create_user,
            open_status,
            data_status
        FROM
          qua_master_data
        <where>
            tenant_id = #{tenantId}
            and model_id = #{modelId}
            and flag = '1'
            <if test="fieldName != null and fieldName != ''">
                and field_name like concat('%',#{fieldName},'%')
            </if>
        </where>
    </select>
</mapper>
