<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorRuleTemplateMapper">

    <select id="getCHTables" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTableDto">
        select a.*,d.template_name, case when d.template_name is null or d.template_name = '' then 0 else 1 end as cnt
        from (select t1.id as table_id, t1.element_id, t1.db_id, t1.table_name, count(t2.id) cnt
              from qua_web_ch_task_result_table t1
                       left join qua_web_ch_task_result_column t2 on (t1.id = t2.table_id)
              where t1.db_id = #{dbId}
                and t1.snapshoot_version = #{snapshootVersion}
              group by t1.id, t1.table_name, t1.element_id, t1.db_id
              order by table_name) a
                left join (select b.element_id, b.database_id, c.table_name, c.template_name
                from qua_monitor_model b
                left join (select * from qua_monitor_rule_template where delete_flag = '0') c
                on b.id = c.model_id) d
                on a.element_id = d.element_id and a.db_id = d.database_id and a.table_name = d.table_name
        <where>
            <if test="tableName != null and tableName != ''">
                and a.table_name like concat('%',#{tableName},'%')
            </if>
            <if test="templateName != null and templateName != ''">
                and d.template_name like concat('%',#{templateName},'%')
            </if>
        </where>
        order by a.cnt desc
    </select>

    <select id="getMysqlTables" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTableDto">
        select a.*,d.template_name, case when d.template_name is null or d.template_name = '' then 0 else 1 end as cnt
       from (select t1.id as table_id, t1.element_id, t1.db_id, t1.table_name, count(t2.id) cnt
          from qua_web_mysql_task_result_table t1
                   left join qua_web_mysql_task_result_column t2 on (t1.id = t2.table_id)
          where t1.db_id = #{dbId}
            and t1.snapshoot_version = #{snapshootVersion}
          group by t1.id, t1.table_name,t1.element_id, t1.db_id
          order by table_name
        ) a
        left join (select b.element_id, b.database_id, c.table_name, c.template_name
        from qua_monitor_model b
        left join (select * from qua_monitor_rule_template where delete_flag = '0') c
        on b.id = c.model_id) d
        on a.element_id = d.element_id and a.db_id = d.database_id and a.table_name = d.table_name
        <where>
            <if test="tableName != null and tableName != ''">
                and a.table_name like concat('%',#{tableName},'%')
            </if>
            <if test="templateName != null and templateName != ''">
                and d.template_name like concat('%',#{templateName},'%')
            </if>
        </where>
       order by a.cnt desc
    </select>

    <select id="getIndexes" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTableDto">
        select a.*,d.template_name, case when d.template_name is null or d.template_name = '' then 0 else 1 end as cnt
        from (select t1.id as table_id, t1.element_id,t1.index_name as table_name, count(t2.id) cnt
            from qua_web_es_task_result_index t1
                     left join qua_web_es_task_result_field t2 on (t1.id = t2.index_id)
            where t1.element_id = #{elementId}
              and t1.snapshoot_version = #{snapshootVersion}
            group by t1.id, t1.index_name,t1.element_id
            order by t1.index_name
        ) a
        left join (select b.element_id, b.database_id, c.table_name, c.template_name
        from qua_monitor_model b
        left join (select * from qua_monitor_rule_template where delete_flag = '0') c
        on b.id = c.model_id) d
        on a.element_id = d.element_id and a.table_name = d.table_name
        <where>
            <if test="tableName != null and tableName != ''">
                and a.table_name like concat('%',#{tableName},'%')
            </if>
            <if test="templateName != null and templateName != ''">
                and d.template_name like concat('%',#{templateName},'%')
            </if>
        </where>
        order by a.cnt desc
    </select>

    <select id="getHiveTables" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTableDto">
        select a.*,d.template_name, case when d.template_name is null or d.template_name = '' then 0 else 1 end as cnt
        from (select t1.id as table_id, t1.element_id, t1.db_id, t1.table_name, count(t2.id) cnt
              from qua_web_hive_task_result_table t1
                       left join qua_web_hive_task_result_column t2 on (t1.id = t2.table_id)
              where t1.db_id = #{dbId}
                and t1.snapshoot_version = #{snapshootVersion}
              group by t1.id, t1.table_name,t1.element_id, t1.db_id
              order by table_name
        ) a
        left join (select b.element_id, b.database_id, c.table_name, c.template_name
        from qua_monitor_model b
        left join (select * from qua_monitor_rule_template where delete_flag = '0') c
        on b.id = c.model_id) d
        on a.element_id = d.element_id and a.db_id = d.database_id and a.table_name = d.table_name
        <where>
            <if test="tableName != null and tableName != ''">
                and a.table_name like concat('%',#{tableName},'%')
            </if>
            <if test="templateName != null and templateName != ''">
                and d.template_name like concat('%',#{templateName},'%')
            </if>
        </where>
        order by a.cnt desc
    </select>

    <select id="reportRule" parameterType="com.idss.datalake.datagovern.dataquality.model.ReportRequestDto" resultType="com.idss.datalake.datagovern.dataquality.model.ReportRuleVo">
        select a.id templateId, a.template_name, a.table_name, c.column_name
        from (
            select * from qua_monitor_rule_template where delete_flag = '0' and tenant_id = #{tenantId}
            <if test="tableNames != null and tableNames.size() > 0">
                and table_name IN
                <foreach item="tableName" collection="tableNames" open="(" separator="," close=")">
                    #{tableName}
                </foreach>
            </if>
        ) a
                left join (select id from qua_monitor_model where element_id = 1 and database_name = #{dbName}) b
                           on a.model_id = b.id
                 left join (select column_name, rule_type, template_id from qua_monitor_rule) c on c.template_id = cast(a.id as varchar)
    </select>

    <select id="reportJob" parameterType="com.idss.datalake.datagovern.dataquality.model.ReportRequestDto" resultType="com.idss.datalake.datagovern.dataquality.model.ReportJobVo">
        select a.job_id, a.id, a.job_rules, b.monitor_score, b.create_time
        from (
            select * from qua_monitor_task
                <where>
                    <if test="templateIds != null and templateIds.size() > 0">
                        and job_rules IN
                        <foreach item="templateId" collection="templateIds" open="(" separator="," close=")">
                            #{templateId}
                        </foreach>
                    </if>
                </where>
        ) a
                 left join qua_monitor_result b on a.id = b.task_id where b.del_flag = 0
        order by a.job_rules, b.create_time desc
    </select>
    
    <select id="ruleTemplate" parameterType="com.idss.datalake.datagovern.dataquality.model.ReportRequestDto" resultType="com.idss.datalake.datagovern.dataquality.model.ReportColumnVo">
        select a.*, b.name as ruleTypeName
        from (select column_name, rule_type_id from qua_monitor_rule where template_id = #{templateId}) a
                 left join qua_internal_model b on a.rule_type_id = b.id
        <where>
            <if test="ruleTypeName != null and ruleTypeName != ''">
                b.name like concat('%',#{ruleTypeName},'%')
            </if>
        </where>
    </select>

    <select id="queryCHColumn" parameterType="com.idss.datalake.datagovern.dataquality.model.ReportRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.ReportColumnInfo">
        select column_name from  qua_web_ch_task_result_column
        where element_id = #{elementId}
        and table_id = #{tableId}
        and table_name = #{tableName}
        and snapshoot_version = #{snapshootVersion}
        <if test="columnNames != null and columnNames.size() > 0">
            and column_name IN
            <foreach item="columnName" collection="columnNames" open="(" separator="," close=")">
                #{columnName}
            </foreach>
        </if>
        <if test="columnName != null and columnName != ''">
            and column_name like concat('%',#{columnName},'%')
        </if>
        <if test="fields != null and fields != ''">
            order by find_in_set(column_name, #{fields}) desc
        </if>
        <if test="fields == null or fields == ''">
            order by column_name
        </if>
    </select>

    <select id="queryMysqlColumn" parameterType="com.idss.datalake.datagovern.dataquality.model.ReportRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.ReportColumnInfo">
        select column_name
        from qua_web_mysql_task_result_column
        where element_id = #{elementId}
        and table_id = #{tableId}
        and table_name = #{tableName}
        and snapshoot_version = #{snapshootVersion}
        <if test="columnNames != null and columnNames.size() > 0">
            and column_name IN
            <foreach item="columnName" collection="columnNames" open="(" separator="," close=")">
                #{columnName}
            </foreach>
        </if>
        <if test="columnName != null and columnName != ''">
            and column_name like concat('%',#{columnName},'%')
        </if>
        <if test="fields != null and fields != ''">
            order by find_in_set(column_name, #{fields}) desc
        </if>
        <if test="fields == null or fields == ''">
            order by column_name
        </if>
    </select>

    <select id="queryESColumn" parameterType="com.idss.datalake.datagovern.dataquality.model.ReportRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.ReportColumnInfo">
        select field_name as columnName
        from qua_web_es_task_result_field
        where element_id = #{elementId}
        and index_id = #{tableId}
        and index_name = #{tableName}
        and snapshoot_version = #{snapshootVersion}
        <if test="columnNames != null and columnNames.size() > 0">
            and field_name IN
            <foreach item="columnName" collection="columnNames" open="(" separator="," close=")">
                #{columnName}
            </foreach>
        </if>
        <if test="columnName != null and columnName != ''">
            and field_name like concat('%',#{columnName},'%')
        </if>
        <if test="fields != null and fields != ''">
            order by find_in_set(field_name, #{fields}) desc
        </if>
        <if test="fields == null or fields == ''">
            order by field_name
        </if>
    </select>

    <select id="queryHiveColumn" parameterType="com.idss.datalake.datagovern.dataquality.model.ReportRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.ReportColumnInfo">
        select column_name
        from qua_web_hive_task_result_column
        where element_id = #{elementId}
        and table_id = #{tableId}
        and table_name = #{tableName}
        and snapshoot_version = #{snapshootVersion}
        <if test="columnNames != null and columnNames.size() > 0">
            and column_name IN
            <foreach item="columnName" collection="columnNames" open="(" separator="," close=")">
                #{columnName}
            </foreach>
        </if>
        <if test="columnName != null and columnName != ''">
            and column_name like concat('%',#{columnName},'%')
        </if>
        <if test="fields != null and fields != ''">
            order by find_in_set(column_name, #{fields}) desc
        </if>
        <if test="fields == null or fields == ''">
            order by column_name
        </if>
    </select>
</mapper>
