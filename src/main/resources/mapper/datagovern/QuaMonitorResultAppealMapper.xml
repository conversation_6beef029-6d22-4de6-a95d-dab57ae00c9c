<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorResultAppealMapper">

    <select id="queryPage"
            resultType="com.idss.datalake.datagovern.dataquality.entity.QuaMonitorResultAppeal"
            parameterType="com.idss.datalake.datagovern.dataquality.model.AppealDto">
        select t1.*,
               t2.column_name,
               t2.rule_type,
               t3.name as job_name
        from qua_monitor_result_appeal t1
                 left join qua_monitor_result_detail t2 on t1.result_detail_id = t2.id
                 left join qua_monitor_job t3 on t2.job_id = t3.id
       where t1.tenant_id = #{tenantId} and t1.del_flag = 0 and t2.del_flag = 0
       <if test="appealUser != null and appealUser !=''">
           and t1.appeal_user = #{appealUser}
       </if>
        <if test="columnName != null and columnName !=''">
            and t1.columnName like concat('%',#{appealUser},'%')
        </if>
        <if test="handleStatus != null and handleStatus.size() > 0">
            AND t1.handle_status IN
            <foreach collection="handleStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
       <choose>
           <when test="isHandler">
               order by t1.handle_time desc
           </when>
           <otherwise>
               order by t1.appeal_time desc
           </otherwise>
       </choose>
    </select>
</mapper>
