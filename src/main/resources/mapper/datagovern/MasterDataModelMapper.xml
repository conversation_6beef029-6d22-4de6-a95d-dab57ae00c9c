<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.masterdata.mapper.QuaMasterDataModelMapper">

    <select id="queryMasterDataModelPage" parameterType="com.idss.datalake.datagovern.masterdata.model.MasterDataModelRequestDto"
            resultType="com.idss.datalake.datagovern.masterdata.entity.QuaMasterDataModel">
        SELECT
            a.id,
            model_name,
            model_code,
            model_status,
            open_status,
            a.create_time,
            a.create_user,
            b.type_name as model_type_name
        FROM
            qua_master_data_model a
        LEFT JOIN qua_config_master_data_type b ON a.model_type_id = b.id
        <where>
            a.tenant_id = #{tenantId}
            and a.flag = '1'
            <if test="modelName != null and modelName != ''">
                and a.model_name like concat('%',#{modelName},'%')
            </if>
            <if test="modelCode != null and modelCode != ''">
                and a.model_code like concat('%',#{modelCode},'%')
            </if>
        </where>
    </select>
</mapper>
