<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorJobMapper">

    <select id="queryJobPage" parameterType="com.idss.datalake.datagovern.dataquality.model.JobRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.JobResponseVo">
        SELECT
        a.id,
        a.model_id,
        NAME,
        b.model_name,
        b.element_type,
        job_desc,
        execute_cycle,
        create_user,
        job_rules,
        status,
        create_time
        FROM
        qua_monitor_job a
        LEFT JOIN (
          select id,model_name,element_type from qua_monitor_model
        ) b on a.model_id = b.id
        <where>
            a.tenant_id = #{tenantId}
            and a.flag = '1'
            <if test="modelId != null and modelId != ''">
                and a.model_id = #{modelId}
            </if>
            <if test="name != null and name != ''">
                and a.name like concat('%',#{name},'%')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="taskDetail" parameterType="com.idss.datalake.datagovern.dataquality.model.JobRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.TaskResultDetailVo">
        SELECT id,
               table_name,
               column_name,
               rule_type,
               rule_weight,
               rule_level
        FROM qua_monitor_result_detail a
        where del_flag = 0 and task_id = #{taskId}
    </select>

    <select id="scoreDetail" parameterType="com.idss.datalake.datagovern.dataquality.model.JobRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.TaskScoreDetail">
        select a.table_name,
               a.column_name,
               a.threshold_value,
               a.is_match,
               a.compare_value,
               a.create_time,
               a.execute_status,
               c.name ruleTypeName,
               c.model_code
        from (select * from qua_monitor_result_detail where del_flag = 0 and task_id = #{taskId}) a
                 left join qua_monitor_rule b on a.rule_type_id = b.id
                 left join qua_internal_model c on b.rule_type_id = c.id
        <where>
            <if test="columnName != null and columnName != ''">
                and a.column_name like concat('%',#{columnName},'%')
            </if>
            <if test="ruleTypeName != null and ruleTypeName != ''">
                and c.name like concat('%',#{ruleTypeName},'%')
            </if>
            <if test="isMatch != null">
                and  a.is_match = #{isMatch}
            </if>
            <if test="startTime != null and startTime != ''">
                and a.create_time <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and a.create_time <![CDATA[ <= ]]> #{endTime}
            </if>
        </where>
    </select>

    <select id="scoreDownload" parameterType="com.idss.datalake.datagovern.dataquality.model.JobRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.TaskScoreDetail">
        select a.table_name,
        a.column_name,
        a.threshold_value,
        a.is_match,
        a.compare_value,
        a.create_time,
        a.execute_status,
        c.name ruleTypeName,
        c.model_code
        from (select * from qua_monitor_result_detail where del_flag = 0 and task_id = #{taskId}) a
        left join qua_monitor_rule b on a.rule_type_id = b.id
        left join qua_internal_model c on b.rule_type_id = c.id
        <where>
            <if test="columnName != null and columnName != ''">
                and a.column_name like concat('%',#{columnName},'%')
            </if>
            <if test="ruleTypeName != null and ruleTypeName != ''">
                and c.name like concat('%',#{ruleTypeName},'%')
            </if>
            <if test="isMatch != null">
                and  a.is_match = #{isMatch}
            </if>
            <if test="startTime != null and startTime != ''">
                and a.create_time <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and a.create_time <![CDATA[ <= ]]> #{endTime}
            </if>
        </where>
    </select>

    <select id="taskDim" parameterType="com.idss.datalake.datagovern.dataquality.model.JobRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.TaskDim">
        select a.column_name,
               a.rule_score,
               a.is_match,
               c.dimensions dimCode
        from (select * from qua_monitor_result_detail where del_flag = 0 and task_id = #{taskId}) a
                 left join qua_monitor_rule b on a.rule_type_id = b.id
                 left join qua_internal_model c on b.rule_type_id = c.id
    </select>
</mapper>
