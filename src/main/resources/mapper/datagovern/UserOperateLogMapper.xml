<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.operate.mapper.UserOperateLogMapper">

    <select id="queryPage"
            resultType="com.idss.datalake.datagovern.metadata.model.operate.entity.UserOperateLog"
            parameterType="com.idss.datalake.datagovern.metadata.model.operate.pojo.OptLogPageReqDto">
        select *
        from qua_web_user_operate_log t
        where t.tenant_id = #{tenantId}
        <if test="userNameOrIp != null and userNameOrIp != ''">
            and (t.user_name like concat('%',#{userNameOrIp},'%') or t.request_ip like concat('%',#{userNameOrIp},'%'))
        </if>
        <if test="startTime != null and endTime != null">
            and t.create_time <![CDATA[ >= ]]> #{startTime} and t.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        order by t.create_time desc
    </select>

    <select id="queryAll" parameterType="com.idss.datalake.datagovern.metadata.model.operate.pojo.OptLogReqDto"
            resultType="com.idss.datalake.datagovern.metadata.model.operate.entity.UserOperateLog">
        select *
        from qua_web_user_operate_log t
        where t.tenant_id = #{tenantId}
        <if test="userNameOrIp != null and userNameOrIp != ''">
            and (t.user_name like concat('%',#{userNameOrIp},'%') or t.request_ip like concat('%',#{userNameOrIp},'%'))
        </if>
        <if test="startTime != null and endTime != null">
            and t.create_time <![CDATA[ >= ]]> #{startTime} and t.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        order by t.create_time desc
    </select>
</mapper>
