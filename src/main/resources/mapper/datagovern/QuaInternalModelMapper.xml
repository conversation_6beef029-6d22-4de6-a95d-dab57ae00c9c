<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.dataquality.mapper.QuaInternalModelMapper">

    <select id="page" parameterType="com.idss.datalake.datagovern.dataquality.dto.InternalModelPageRequest" resultType="com.idss.datalake.datagovern.dataquality.entity.QuaInternalModel">
        select * from qua_internal_model where model_type = '1'
         <if test="name != null and name != ''">
             and name like concat('%',#{name},'%')
         </if>
        <if test="dimensions != null and dimensions != ''">
            and dimensions = #{dimensions}
        </if>
    </select>
</mapper>
