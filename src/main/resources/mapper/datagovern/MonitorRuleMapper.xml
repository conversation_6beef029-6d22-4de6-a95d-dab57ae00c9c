<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.dataquality.mapper.QuaMonitorRuleMapper">

    <select id="queryRulePage" parameterType="com.idss.datalake.datagovern.dataquality.model.RuleRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.entity.QuaMonitorRuleTemplate">
        SELECT * FROM qua_monitor_rule_template
        <where>
            tenant_id = #{tenantId}
            and delete_flag = '0'
            <if test="templateName != null and templateName != ''">
                and template_name like concat('%',#{templateName},'%')
            </if>
            <if test="tableName != null and tableName != ''">
                and table_name like concat('%',#{tableName},'%')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="queryColumnPage" parameterType="com.idss.datalake.datagovern.dataquality.model.RuleRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.RuleResponseVo">
        SELECT
        a.id,
        a.model_id,
        a.column_name,
        a.rule_desc,
        a.rule_type,
        a.rule_weight,
        c.name as model_name,
        create_user,
        create_time
        FROM
        qua_monitor_rule a
        LEFT JOIN (
        select id,name,dimensions,audit_object,custom_sql,tenant_id from qua_internal_model
        ) c on a.rule_type_id = c.id
        <where>
            a.template_id = #{templateId}
            <if test="columnName != null and columnName != ''">
                and a.column_name like concat('%',#{columnName},'%')
            </if>
            <if test="modelName != null and modelName != ''">
                and c.name like concat('%',#{modelName},'%')
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="queryRuleTypePage" parameterType="com.idss.datalake.datagovern.dataquality.model.RuleRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.RuleTypeResponseVo">
        select DISTINCT type_code,type_name,type_desc from qua_monitor_rule_type
        <where>
            <if test="typeName != null and typeName != ''">
                and type_name like concat('%',#{typeName},'%')
            </if>
        </where>
    </select>
    
    <select id="existRule" parameterType="map" resultType="map">
        select * from qua_monitor_job where flag = '1' and #{ruleId} = any (string_to_array(job_rules, ',')) = '1'
    </select>

    <select id="queryRule" parameterType="com.idss.datalake.datagovern.dataquality.model.RuleRequestDto"
            resultType="com.idss.datalake.datagovern.dataquality.model.RuleResponseVo">
        SELECT
            a.id,
            a.model_id,
            a.rule_name,
            a.rule_code,
            b.type_name AS rule_type,
            c.model_name,
            c.element_type,
            a.table_name,
            a.column_name,
            a.rule_detail AS detail,
            a.rule_level,
            create_user,
            create_time
        FROM
            qua_monitor_rule a
        LEFT JOIN (
            SELECT
                type_code,
                type_name
            FROM
                qua_monitor_rule_type
            GROUP BY
                type_code,
                type_name
        ) b ON a.rule_type = b.type_code
        LEFT JOIN (
            SELECT id,model_name,element_type FROM qua_monitor_model
        ) c on a.model_id = c.id
        <where>
            a.tenant_id = #{tenantId}
            and a.flag = '1'
            <if test="modelId != null and modelId != ''">
                and a.model_id = #{modelId}
            </if>
            <if test="ruleName != null and ruleName != ''">
                and a.rule_name like concat('%',#{ruleName},'%')
            </if>
            <if test="ruleTypeList != null and ruleTypeList.size() > 0">
                AND a.rule_type IN
                <foreach collection="ruleTypeList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="ruleType != null and ruleType != ''">
                and a.rule_type = #{ruleType}
            </if>
            <if test="ids != null and ids.size() > 0">
                AND a.id IN
                <foreach collection="ids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by a.create_time desc
    </select>
</mapper>
