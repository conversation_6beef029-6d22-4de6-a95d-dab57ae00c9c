<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.config.mapper.QuaConfigMasterDataTypeMapper">

    <select id="queryMasterTypePage" parameterType="com.idss.datalake.datagovern.config.model.MasterDataRequestDto"
            resultType="com.idss.datalake.datagovern.config.entity.QuaConfigMasterDataType">
        SELECT
            id,
            type_name,
            type_desc,
            create_user,
            create_time
        FROM
            qua_config_master_data_type
        <where>
            tenant_id = #{tenantId}
            and flag = '1'
            <if test="typeName != null and typeName != ''">
                and type_name like concat('%',#{typeName},'%')
            </if>
        </where>
    </select>
</mapper>
