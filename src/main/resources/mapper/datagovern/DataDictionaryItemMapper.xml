<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.dictionary.mapper.DataDictionaryItemMapper">

    <select id="queryClickhouseDbPage" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.db_name as element_name,
        'Clickhouse' as element_type,
        '库' as data_type,
        t.element_id,
        t.db_name as asset_path,
        'clickhouse_db' as asset_type,
        'DB' as asset_type_code
        from qua_web_ch_element_detail_db t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.db_name like concat('%',#{elementName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryClickhouseTablePage" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.table_name as element_name,
        'Clickhouse' as element_type,
        '表' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name) as asset_path,
        'clickhouse_table' as asset_type,
        'TABLE' as asset_type_code,
        t.table_dscribe as elementDesc,
        t.last_snapshoot_version as snapshootVersion,
        t.db_name,
        t.table_name,
        t.table_owner as owner
        from qua_web_ch_element_detail_table t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.table_name like concat('%',#{elementName},'%')
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name like concat('%',#{dbName},'%')
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        order by create_time desc
    </select>

    <select id="queryClickhouseFieldPage" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.db_name as db_name,
        t.table_name as table_name,
        t.column_name as element_name,
        'Clickhouse' as element_type,
        '字段' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name,t.column_name) as asset_path,
        'clickhouse_field' as asset_type,
        'FIELD' as asset_type_code,
        t.create_time,
        t.cn_desc,
        t.enum_value,
        t.mapping_fields,
        t.sort
        from qua_web_ch_element_detail_column t
        WHERE t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.column_name like concat('%',#{elementName},'%')
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name like concat('%',#{dbName},'%')
        </if>
        <if test="tableName != null and tableName !=''">
            and t.table_name like concat('%',#{tableName},'%')
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        order by t.create_time desc
    </select>

    <select id="queryClickhouseField" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select DISTINCT t.id,
        t.db_name as db_name,
        t.table_name as table_name,
        t.column_name as element_name,
        'Clickhouse' as element_type,
        '字段' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name,t.column_name) as asset_path,
        'clickhouse_field' as asset_type,
        'FIELD' as asset_type_code,
        t.create_time,
        t.cn_desc,
        t.enum_value,
        t.mapping_fields,
        t.sort,
        t2.is_in_primary_key as is_primary_key
        from qua_web_ch_element_detail_column t
        left join qua_web_ch_task_result_column t2
        on t.element_id = t2.element_id and t.db_name = t2.db_name and t.table_name = t2.table_name and t.column_name = t2.column_name
        WHERE t.tenant_id = #{tenantId}
        <if test="dbName != null and dbName !=''">
            and t.db_name = #{dbName}
        </if>
        <if test="tableName != null and tableName !=''">
            and t.table_name = #{tableName}
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
    </select>

    <select id="queryHiveDbPage" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.db_name as element_name,
        'Hive' as element_type,
        '库' as data_type,
        t.element_id,
        t.db_name as asset_path,
        'hive_db' as asset_type,
        'DB' as asset_type_code
        from qua_web_hive_element_detail_db t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.db_name like concat('%',#{elementName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryHiveTablePage" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.table_name as element_name,
        'Hive' as element_type,
        '表' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name) as asset_path,
        'hive_table' as asset_type,
        'TABLE' as asset_type_code,
        t.table_dscribe as elementDesc,
        t.last_snapshoot_version as snapshootVersion,
        t.db_name,
        t.table_name,
        t.table_owner as owner
        from qua_web_hive_element_detail_table t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.table_name like concat('%',#{elementName},'%')
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name like concat('%',#{dbName},'%')
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        order by create_time desc
    </select>

    <select id="queryHiveFieldPage" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select DISTINCT t.id,
        t.db_name as db_name,
        t.table_name as table_name,
        t.column_name as element_name,
        'Hive' as element_type,
        '字段' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name,t.column_name) as asset_path,
        'hive_field' as asset_type,
        'FIELD' as asset_type_code,
        t.create_time,
        t.cn_desc,
        t.enum_value,
        t.mapping_fields,
        t.sort
        from qua_web_hive_element_detail_column t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.column_name like concat('%',#{elementName},'%')
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name like concat('%',#{dbName},'%')
        </if>
        <if test="tableName != null and tableName !=''">
            and t.table_name like concat('%',#{tableName},'%')
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        order by t.create_time desc
    </select>

    <select id="queryHiveField" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select DISTINCT t.id,
        t.db_name as db_name,
        t.table_name as table_name,
        t.column_name as element_name,
        'Hive' as element_type,
        '字段' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name,t.column_name) as asset_path,
        'hive_field' as asset_type,
        'FIELD' as asset_type_code,
        t.create_time,
        t.cn_desc,
        t.enum_value,
        t.mapping_fields,
        t.sort
        from qua_web_hive_element_detail_column t
        where t.tenant_id = #{tenantId}
        <if test="dbName != null and dbName !=''">
            and t.db_name = #{dbName}
        </if>
        <if test="tableName != null and tableName !=''">
            and t.table_name = #{tableName}
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
    </select>

    <select id="queryMysqlDbPage" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.db_name as element_name,
        'Mysql' as element_type,
        '库' as data_type,
        t.element_id,
        t.db_name as asset_path,
        'mysql_db' as asset_type,
        'DB' as asset_type_code
        from qua_web_mysql_element_detail_db t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.db_name like concat('%',#{elementName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryMysqlTablePage" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.table_name as element_name,
        'Mysql' as element_type,
        '表' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name) as asset_path,
        'mysql_table' as asset_type,
        'TABLE' as asset_type_code,
        t.table_dscribe as elementDesc,
        t.last_snapshoot_version as snapshootVersion,
        t.db_name,
        t.table_name,
        t.table_owner as owner
        from qua_web_mysql_element_detail_table t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.table_name like concat('%',#{elementName},'%')
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name like concat('%',#{dbName},'%')
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        order by create_time desc
    </select>

    <select id="queryMysqlFieldPage" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.db_name as db_name,
        t.table_name as table_name,
        t.column_name as element_name,
        'Mysql' as element_type,
        '字段' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name,t.column_name) as asset_path,
        'mysql_field' as asset_type,
        'FIELD' as asset_type_code,
        t.create_time,
        t.cn_desc,
        t.enum_value,
        t.mapping_fields,
        t.sort
        from qua_web_mysql_element_detail_column t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.column_name like concat('%',#{elementName},'%')
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name like concat('%',#{dbName},'%')
        </if>
        <if test="tableName != null and tableName !=''">
            and t.table_name like concat('%',#{tableName},'%')
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        order by t.create_time desc
    </select>

    <select id="queryMysqlField" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select distinct t.id,
        t.db_name as db_name,
        t.table_name as table_name,
        t.column_name as element_name,
        'Mysql' as element_type,
        '字段' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name,t.column_name) as asset_path,
        'mysql_field' as asset_type,
        'FIELD' as asset_type_code,
        t.create_time,
        t.cn_desc,
        t.enum_value,
        t.mapping_fields,
        t.sort,
        CASE
        WHEN t2.column_key = 'PRI' THEN 1
        ELSE 0
        END AS is_primary_key
        from qua_web_mysql_element_detail_column t
        left join qua_web_mysql_task_result_column t2
        on t.element_id = t2.element_id and t.db_name = t2.db_name and t.table_name = t2.table_name and t.column_name = t2.column_name
        where t.tenant_id = #{tenantId}
        <if test="dbName != null and dbName !=''">
            and t.db_name = #{dbName}
        </if>
        <if test="tableName != null and tableName !=''">
            and t.table_name = #{tableName}
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
    </select>

    <select id="queryElasticsearchIndexPage" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.index_name as element_name,
        'Elasticsearch' as element_type,
        '索引' as data_type,
        t.element_id,
        t.index_name as asset_path,
        'elasticsearch_index' as asset_type,
        'INDEX' as asset_type_code,
        t.index_dscribe as elementDesc,
        t.last_snapshoot_version as snapshootVersion,
        t.index_owner as owner
        from qua_web_es_element_detail_index t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.index_name like concat('%',#{elementName},'%')
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        order by create_time desc
    </select>

    <select id="queryElasticsearchFieldPage" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.index_name as table_name,
        t.field_name as element_name,
        'Elasticsearch' as element_type,
        '索引字段' as data_type,
        t.element_id,
        concat_ws(',',t.index_name,t.field_name) as asset_path,
        'elasticsearch_field' as asset_type,
        'INDEX_FIELD' as asset_type_code,
        t.create_time,
        t.last_snapshoot_version as snapshootVersion,
        t.cn_desc,
        t.enum_value,
        t.mapping_fields,
        t.sort
        from qua_web_es_element_detail_field t
        WHERE t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.field_name like concat('%',#{elementName},'%')
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        order by t.create_time desc
    </select>

    <select id="queryElasticsearchField" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.index_name as table_name,
        t.field_name as element_name,
        'Elasticsearch' as element_type,
        '索引字段' as data_type,
        t.element_id,
        concat_ws(',',t.index_name,t.field_name) as asset_path,
        'elasticsearch_field' as asset_type,
        'INDEX_FIELD' as asset_type_code,
        t.create_time,
        t.cn_desc,
        t.enum_value,
        t.mapping_fields,
        t.sort
        from qua_web_es_element_detail_field t
        WHERE t.tenant_id = #{tenantId}
        <if test="tableName != null and tableName !=''">
            and t.index_name = #{tableName}
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
    </select>

    <select id="queryDatasourceTypeList" resultType="java.lang.String">
        select distinct datasource_type from data_dictionary_item
        <if test="tenantId != null">
            WHERE tenant_id = #{tenantId}
        </if>
    </select>

    <select id="queryDatasourceTypeCount" resultType="long">
        SELECT COUNT(DISTINCT datasource_type) FROM data_dictionary_item
        WHERE 1 = 1
        <if test="tenantId != null">
            and tenant_id = #{tenantId}
        </if>
    </select>

    <select id="queryDatabaseCount" resultType="long">
        SELECT COUNT(*)
        FROM (
            SELECT DISTINCT element_id,database_name FROM data_dictionary_item
            WHERE 1 = 1
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="datasourceType != null and datasourceType !=''">
                and datasource_type = #{datasourceType}
            </if>
        ) AS item_tmp;
    </select>

    <select id="queryTableCount" resultType="Long">
        SELECT COUNT(*)
        FROM (
            SELECT DISTINCT element_id,database_name,table_name FROM data_dictionary_item
            WHERE 1 = 1
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="datasourceType != null and datasourceType !=''">
                and datasource_type = #{datasourceType}
            </if>
        ) AS item_tmp;
    </select>

    <select id="queryColumnCount" resultType="Long">
        SELECT COUNT(*) FROM data_dictionary_item WHERE item_type = 'field'
        <if test="tenantId != null">
            and tenant_id = #{tenantId}
        </if>
        <if test="datasourceType != null and datasourceType !=''">
            and datasource_type = #{datasourceType}
        </if>
    </select>

    <select id="queryMaxVersion" resultType="java.lang.String">
        SELECT MAX(snapshoot_version) from ${table} where tenant_id = #{tenantId}
    </select>

    <select id="queryVersionById" resultType="java.lang.String">
        SELECT last_snapshoot_version from ${table} where id = #{id}
    </select>

    <select id="queryLatestTaskByElementId" resultType="com.idss.datalake.datagovern.metadata.model.job.entity.QuaTask">
        SELECT qwt.* FROM qua_scan_list qsl join qua_web_task qwt on qsl.task_no = qwt.task_no
        WHERE qwt.element_id = #{elementId} AND qsl.scan_state = 2
        ORDER BY qwt.create_time desc limit 1
    </select>

    <select id="querySubDomainCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT t2.category_id) as domain_count
        FROM data_dictionary_item t1 inner join data_dictionary_item t2
        on t1.datasource_type = t2.datasource_type AND t1.item_id = t2.item_id
        where t1.dict_id != t2.dict_id
        and t1.category_id = #{categoryId}
    </select>

    <select id="querySubDomainBase" resultType="com.idss.datalake.datagovern.dictionary.entity.DataDictionaryBase">
        SELECT * FROM data_dictionary_base WHERE id IN
        (SELECT DISTINCT t2.dict_id
        FROM data_dictionary_item t1 inner join data_dictionary_item t2
        on t1.datasource_type = t2.datasource_type AND t1.item_id = t2.item_id
        where t1.dict_id != t2.dict_id
        and t1.category_id = #{categoryId})
    </select>

    <select id="querySubDomainCategory" resultType="com.idss.datalake.datagovern.dictionary.entity.DataDictionaryCategory">
        SELECT * FROM data_dictionary_category WHERE id IN
        (SELECT DISTINCT t2.category_id
        FROM data_dictionary_item t1 inner join data_dictionary_item t2
        on t1.datasource_type = t2.datasource_type AND t1.item_id = t2.item_id
        where t1.dict_id != t2.dict_id
        and t1.category_id = #{categoryId}
        and t2.dict_id=#{dictId})
    </select>

    <select id="pageWithTag" parameterType="com.idss.datalake.datagovern.dictionary.dto.DataDictionaryItemDTO"
            resultType="com.idss.datalake.datagovern.dictionary.entity.DataDictionaryItem">
        SELECT t1.*,t2.meta_tag_ids
        FROM data_dictionary_item t1
        LEFT JOIN
        (select element_id,datasource_type,database_name,table_name,string_agg(tag_id::text, ',' ) as meta_tag_ids
        FROM data_meta_tag_master
        <if test="tagIds != null and tagIds.size() > 0">
            WHERE tag_id IN
            <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
                #{tagId}
            </foreach>
        </if>
        group by element_id,datasource_type,database_name,table_name) t2
        ON t1.element_id = t2.element_id
        AND t1.datasource_type = t2.datasource_type
        AND t1.database_name = t2.database_name
        AND t1.table_name = t2.table_name
        WHERE t1.tenant_id = #{tenantId}
        and t1.item_type in ('table','index')
        <if test="dictId != null">
            and t1.dict_id = #{dictId}
        </if>
        <if test="categoryId != null">
            and t1.category_id = #{categoryId}
        </if>
        <if test="datasourceType != null">
            and t1.datasource_type = #{datasourceType}
        </if>
        <if test="itemName != null and itemName !=''">
            and t1.item_name like concat('%',#{itemName},'%')
        </if>
        <if test="tableName != null and tableName !=''">
            and t1.table_name like concat('%',#{tableName},'%')
        </if>
        <if test="owner != null and owner !=''">
            and t1.owner like concat('%',#{owner},'%')
        </if>
        <if test="tagIds != null and tagIds.size() > 0">
            and t2.meta_tag_ids is not NULL
        </if>
        order by t1.create_time desc
        limit #{offset},#{pageSize}
    </select>

    <select id="pageWithTagCount" resultType="java.lang.Long">
        SELECT count(*)
        FROM data_dictionary_item t1
        LEFT JOIN
        (select element_id,datasource_type,database_name,table_name,string_agg(tag_id::text, ',' ) as meta_tag_ids
        FROM data_meta_tag_master
        <if test="tagIds != null and tagIds.size() > 0">
            WHERE tag_id IN
            <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
                #{tagId}
            </foreach>
        </if>
        group by element_id,datasource_type,database_name,table_name) t2
        ON t1.element_id = t2.element_id
        AND t1.datasource_type = t2.datasource_type
        AND t1.database_name = t2.database_name
        AND t1.table_name = t2.table_name
        WHERE t1.tenant_id = #{tenantId}
        and t1.item_type in ('table','index')
        <if test="dictId != null">
            and t1.dict_id = #{dictId}
        </if>
        <if test="categoryId != null">
            and t1.category_id = #{categoryId}
        </if>
        <if test="datasourceType != null">
            and t1.datasource_type = #{datasourceType}
        </if>
        <if test="itemName != null and itemName !=''">
            and t1.item_name like concat('%',#{itemName},'%')
        </if>
        <if test="tableName != null and tableName !=''">
            and t1.table_name like concat('%',#{tableName},'%')
        </if>
        <if test="owner != null and owner !=''">
            and t1.owner like concat('%',#{owner},'%')
        </if>
        <if test="tagIds != null and tagIds.size() > 0">
            and t2.meta_tag_ids is not NULL
        </if>
    </select>

    <select id="queryTotalStorageSize" resultType="java.lang.Long">
        select sum(data_storage_size) as totalSize from data_dictionary_item ddi
        where 1 = 1
        <if test="datasourceType != null and datasourceType !=''">
            and datasource_type = #{datasourceType}
        </if>
    </select>

    <select id="queryYesterdayAddTableCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM (
            SELECT DISTINCT element_id,database_name,table_name FROM data_dictionary_item
            WHERE create_time BETWEEN #{startDate} AND #{endDate}
            <if test="datasourceType != null and datasourceType !=''">
                and datasource_type = #{datasourceType}
            </if>
        ) AS item_tmp;
    </select>

    <select id="queryDwLevelIdList" resultType="java.lang.Long">
        select distinct dw_level_id from data_dictionary_item
        <if test="tenantId != null">
            WHERE tenant_id = #{tenantId}
        </if>
    </select>

    <select id="queryBusinessSectorCount" resultType="long">
        select count(distinct business_sector_id) from data_dictionary_item
        WHERE 1 = 1
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        <if test="dwLevelId != null">
            AND dw_level_id = #{dwLevelId}
        </if>
    </select>

    <select id="queryDataDomainCount" resultType="long">
        select count(distinct data_domain_id) from data_dictionary_item
        WHERE 1 = 1
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        <if test="dwLevelId != null">
            AND dw_level_id = #{dwLevelId}
        </if>
    </select>

    <select id="queryTableCountByDwLevel" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM (
            SELECT DISTINCT element_id,database_name,table_name FROM data_dictionary_item
            WHERE 1 = 1
            <if test="tenantId != null">
                AND tenant_id = #{tenantId}
            </if>
            <if test="dwLevelId != null">
                AND dw_level_id = #{dwLevelId}
            </if>
        ) AS item_tmp;
    </select>

    <select id="queryBusinessSectorName" resultType="java.lang.String">
        select sector_name_cn from data_model_business_sector
        where id = #{businessSectorId}
    </select>

    <select id="queryDataDomainName" resultType="java.lang.String">
        select domain_name_cn from data_model_data_domain
        where id = #{dataDomainId}
    </select>

    <select id="queryDwLevelName" resultType="java.lang.String">
        select cn_name from tb_dw_level
        where id = #{dwLevelId}
    </select>

    <select id="queryBusinessProcessName" resultType="java.lang.String">
        select process_name_cn from data_model_business_process
        where id = #{businessProcessId}
    </select>

    <select id="queryTaskId" resultType="java.lang.Long">
        select qmt.id as task_id
        from
        qua_monitor_model qmm
        left join qua_monitor_model_resource qmmr on qmm.id = qmmr.model_id
        left join qua_monitor_task qmt on qmm.id = qmt.model_id
        where qmm.flag = '1'
        and qmt.task_result = 1
        and qmm.element_id = #{elementId}
        and qmm.database_name = #{databaseName}
        and qmmr.table_name = #{tableName}
        order by qmt.update_time desc limit 1
    </select>

    <select id="queryTaskScore" resultType="java.lang.Double">
        select qmr.monitor_score as score
        from
        qua_monitor_model qmm
        left join qua_monitor_model_resource qmmr on qmm.id = qmmr.model_id
        left join qua_monitor_task qmt on qmm.id = qmt.model_id
        left join qua_monitor_result qmr on qmt.id = qmr.task_id
        where qmm.flag = '1'
        and qmt.task_result = 1
        and qmm.element_id = #{elementId}
        and qmm.database_name = #{databaseName}
        and qmmr.table_name = #{tableName}
        order by qmt.update_time desc limit 1
    </select>

</mapper>
