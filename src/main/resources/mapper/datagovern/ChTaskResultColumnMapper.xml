<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.job.mapper.ChTaskResultColumnMapper">

    <select id="exportCh" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.ChExportDto">
        select t6.ch_ip,
               t6.ch_port,
               t6.ch_user_name,
               t6.ch_user_password,

               t5.create_time                as snapshoot_create_time,
               t5.create_user                as snapshoot_create_user,
               t5.snapshoot_version,
               t5.db_name,

               t4.id                         as detail_table_id,
               t4.table_name_cn              as detail_table_table_name_cn,
               t4.table_dscribe              as detail_table_table_dscribe,
               t4.table_owner                as detail_table_table_owner,
               t4.is_sensitive               as detail_table_is_sensitive,
               t4.create_time                as detail_table_create_time,
               t4.create_user                as detail_table_create_user,
               t4.update_time                as detail_table_update_time,
               t4.update_user                as detail_table_update_user,
               t4.is_available               as detail_table_is_available,
               t4.first_snapshoot_version    as detail_table_first_snapshoot_version,
               t4.last_snapshoot_version     as detail_table_last_snapshoot_version,
               t6.ch_ip                      as detail_table_ch_ip,
               t6.ch_port                    as detail_table_ch_port,
               t4.table_name                 as detail_table_table_name,

               t2.id                         as detail_column_id,
               t2.column_name                as detail_column_column_name,
               t2.column_name_cn             as detail_column_column_name_cn,
               t2.is_sensitive               as detail_column_is_sensitive,
               t2.create_time                as detail_column_create_time,
               t2.create_user                as detail_column_create_user,
               t2.update_time                as detail_column_update_time,
               t2.update_user                as detail_column_update_user,
               t2.is_available               as detail_column_is_available,
               t2.first_snapshoot_version    as detail_column_first_snapshoot_version,
               t2.last_snapshoot_version     as detail_column_last_snapshoot_version,

               t3.id                         as task_result_id,
               t3.uuid                       as task_result_uuid,
               t3.engine                     as task_result_engine,
               t3.is_temporary               as task_result_is_temporary,
               t3.data_paths                 as task_result_data_paths,
               t3.metadata_path              as task_result_metadata_path,
               t3.metadata_modification_time as task_result_metadata_modification_time,
               t3.dependencies_database      as task_result_dependencies_database,
               t3.dependencies_table         as task_result_dependencies_table,
               t3.create_table_query         as task_result_create_table_query,
               t3.engine_full                as task_result_engine_full,
               t3.partition_key              as task_result_partition_key,
               t3.sorting_key                as task_result_sorting_key,
               t3.primary_key                as task_result_primary_key,
               t3.sampling_key               as task_result_sampling_key,
               t3.storage_policy             as task_result_storage_policy,
               t3.total_rows                 as task_result_total_rows,
               t3.total_bytes                as task_result_total_bytes,
               t3.lifetime_rows              as task_result_lifetime_rows,
               t3.lifetime_bytes             as task_result_lifetime_bytes,

               t1.id                         as task_column_id,
               t1.column_name                as task_column_column_name,
               t1.type                       as task_column_type,
               t1.position                   as task_column_position,
               t1.default_kind               as task_column_default_kind,
               t1.default_expression         as task_column_default_expression,
               t1.data_compressed_bytes      as task_column_data_compressed_bytes,
               t1.data_uncompressed_bytes    as task_column_data_uncompressed_bytes,
               t1.marks_bytes                as task_column_marks_bytes,
               t1.comment                    as task_column_comment,
               t1.is_in_partition_key        as task_column_is_in_partition_key,
               t1.is_in_sorting_key          as task_column_is_in_sorting_key,
               t1.is_in_primary_key          as task_column_is_in_primary_key,
               t1.is_in_sampling_key         as task_column_is_in_sampling_key,
               t1.compression_codec          as task_column_compression_codec,
               t1.is_nullable                as task_column_is_nullable,
               t1.column_length              as task_column_column_length,
               t1.column_precision           as task_column_column_precision

        from qua_web_ch_task_result_column t1
                 left join qua_web_ch_element_detail_column t2
                           on t1.element_id = t2.element_id and t1.db_name = t2.db_name and
                              t1.table_name = t2.table_name and
                              t1.column_name = t2.column_name
                 left join qua_web_ch_task_result_table t3
                           on t1.table_id = t3.id
                 left join qua_web_ch_element_detail_table t4
                           on t4.element_id = t1.element_id and t4.db_name = t1.db_name and
                              t4.table_name = t1.table_name
                 left join qua_web_ch_task_result_db t5 on t5.element_id = t4.element_id and t5.db_name = t1.db_name and
                                                           t5.snapshoot_version = #{version} and t5.task_id = #{taskId}
                 left join qua_wab_element t6 on t6.id = t1.element_id
        where t1.element_id = #{elementId}
          and t1.snapshoot_version = #{version}
    </select>
</mapper>
