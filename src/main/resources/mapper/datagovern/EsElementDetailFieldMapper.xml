<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.detail.mapper.EsElementDetailFieldMapper">


    <select id="queryDetailColumnPage"
            resultType="com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementDetailVo">
        select t1.id,
        t1.field_name as column_name,
        t1.field_name_cn as column_name_cn,
        t1.is_required,
        t2.field_data_type as column_type
        from qua_web_es_element_detail_field t1
        left join qua_web_es_task_result_field t2 on (t1.element_id = t2.element_id and t1.index_name = t2.index_name and t1.field_name = t2.field_name)
        join (select max(snapshoot_version) as snapshoot_version from qua_web_es_task_result_field where element_id  = #{elementId}) as t3 on t2.snapshoot_version = t3.snapshoot_version
        where t1.element_id  = #{elementId}  and t1.index_name = #{indexName}
        <if test="columnName != null and columnName != ''">
          and t1.field_name like concat('%',#{columnName},'%')
        </if>
    </select>
</mapper>
