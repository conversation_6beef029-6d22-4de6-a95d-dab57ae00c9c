<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.job.mapper.KafkaTopicMapper">
    <select id="getKafkaTopicNode" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeTopicDto">
        select t1.id as topic_id, t1.topic_name
        from qua_web_kafka_task_result t1
        where t1.element_id = #{elementId}
        and t1.snapshoot_version = #{snapshootVersion}
        order by t1.topic_name
    </select>
</mapper> 