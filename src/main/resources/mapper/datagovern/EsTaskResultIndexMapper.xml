<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.job.mapper.EsTaskResultIndexMapper">
    <select id="getIndexNode" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeIndexDto">
        select t1.id as index_id, t1.index_name, count(t2.id) cnt
        from qua_web_es_task_result_index t1
        left join qua_web_es_task_result_field t2 on (t1.id = t2.index_id)
        where t1.element_id = #{elementId}
        and t1.snapshoot_version = #{snapshootVersion}
        group by t1.id, t1.index_name
        order by index_name
    </select>

    <select id="getIndexNodePage" resultType="com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeTablePage">
        select index_name as table_name, index_name_cn as table_name_cn, index_owner as table_owner,is_sensitive,index_dscribe as table_dscribe
        from qua_web_es_element_detail_index
        where element_id = #{elementId}
        and last_snapshoot_version = #{snapshootVersion}
        <if test="tableName != null and tableName != ''">
            and index_name like concat('%',#{tableName},'%')
        </if>
        <if test="tableNameCn != null and tableNameCn != ''">
            and index_name_cn like concat('%',#{tableNameCn},'%')
        </if>
        <if test="tableOwner != null and tableOwner != ''">
            and index_owner like concat('%',#{tableOwner},'%')
        </if>
    </select>
</mapper>
