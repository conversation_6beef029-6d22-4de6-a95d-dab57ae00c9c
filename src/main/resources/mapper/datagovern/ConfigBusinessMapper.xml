<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.config.mapper.QuaConfigBusinessMapper">

    <select id="queryBusinessPage" parameterType="com.idss.datalake.datagovern.config.model.BusinessRequestDto"
            resultType="com.idss.datalake.datagovern.config.entity.QuaConfigBusiness">
        SELECT
            a.id,
            business_name,
            business_desc,
            a.create_user,
            a.create_time,
            b.category_name AS category
        FROM
          qua_config_business a
        LEFT JOIN qua_config_business_category b ON a.category_id = b.id
        <where>
            a.tenant_id = #{tenantId}
            and a.flag = '1'
            <if test="businessName != null and businessName != ''">
                and a.business_name like concat('%',#{businessName},'%')
            </if>
            <if test="categoryName != null and categoryName != ''">
                and b.category_name like concat('%',#{categoryName},'%')
            </if>
        </where>
    </select>
</mapper>
