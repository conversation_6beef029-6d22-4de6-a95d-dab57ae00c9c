<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadatatag.mapper.DataMetaTagMasterMapper">

    <select id="selectTagMasterByTagIds" resultType="com.idss.datalake.datagovern.metadatatag.entity.DataMetaTagMaster">
        SELECT DISTINCT a.element_id,a.datasource_type,a.database_name,a.table_name
        FROM data_meta_tag_master a
        <foreach collection="tagList" item="tagId" index="index" separator=" ">
            <if test="index > 0">
                JOIN data_meta_tag_master b${index} ON a.item_unique_id = b${index}.item_unique_id
            </if>
        </foreach>
        WHERE a.tag_id = #{tagList[0]}
        <foreach collection="tagList" item="tagId" index="index" separator=" ">
            <if test="index > 0">
                AND b${index}.tag_id = #{tagId}
            </if>
        </foreach>
        AND a.tenant_id = #{tenantId}
    </select>

    <select id="selectTagByItemUniqueIds" resultType="com.idss.datalake.datagovern.metadatatag.model.DataMetaTagAndTypeModel">
        SELECT DISTINCT dmt.name as tag_name,dmtt.name as tag_type_name,dmtm.item_unique_id
        FROM data_meta_tag dmt
        LEFT JOIN data_meta_tag_type dmtt on dmt.tag_type_id = dmtt.id
        LEFT JOIN data_meta_tag_master dmtm on dmt.id = dmtm.tag_id
        WHERE dmtm.item_unique_id IN
        <foreach collection="uniqueIds" item="uniqueId" index="index" separator="," open="(" close=")">
            #{uniqueId}
        </foreach>
        AND dmt.tenant_id = #{tenantId}
        GROUP BY dmt.name ,dmtt.name ,dmtm.item_unique_id
    </select>

    <select id="selectTagByIds" resultType="com.idss.datalake.datagovern.metadatatag.model.DataMetaTagAndTypeModel">
        SELECT DISTINCT dmt.name as tag_name,dmtt.name as tag_type_name
        FROM data_meta_tag dmt
        LEFT JOIN data_meta_tag_type dmtt on dmt.tag_type_id = dmtt.id
        WHERE dmt.id IN
        <foreach collection="tagIds" item="tagId" index="index" separator="," open="(" close=")">
            #{tagId}
        </foreach>
        AND dmt.tenant_id = #{tenantId}
        GROUP BY dmt.name ,dmtt.name
    </select>
</mapper>
