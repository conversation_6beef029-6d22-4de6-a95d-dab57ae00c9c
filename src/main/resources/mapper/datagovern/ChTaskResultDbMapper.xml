<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.job.mapper.ChTaskResultDbMapper">
    <select id="getDbNode" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeDbDto">
        select t1.id as db_id, t1.db_name, count(t2.id) cnt
        from qua_web_ch_task_result_db t1
        left join qua_web_ch_task_result_table t2 on (t1.id = t2.db_id)
        where t1.element_id = #{elementId}
        and t1.snapshoot_version = #{snapshootVersion}
        group by t1.id, t1.db_name
        order by db_name
    </select>

    <select id="getMysqlDbNode" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeDbDto">
        select t1.id as db_id, t1.db_name, count(t2.id) cnt
        from qua_web_mysql_task_result_db t1
        left join qua_web_mysql_task_result_table t2 on (t1.id = t2.db_id)
        where t1.element_id = #{elementId}
        and t1.snapshoot_version = #{snapshootVersion}
        group by t1.id, t1.db_name
        order by db_name
    </select>

    <select id="getHiveDbNode" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeDbDto">
        select t1.id as db_id, t1.db_name, count(t2.id) cnt
        from qua_web_hive_task_result_db t1
        left join qua_web_hive_task_result_table t2 on (t1.id = t2.db_id)
        where t1.element_id = #{elementId}
        and t1.snapshoot_version = #{snapshootVersion}
        group by t1.id, t1.db_name
        order by db_name
    </select>

    <select id="getChDbPage" resultType="com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeDbPage">
        select * from
        (select t1.id as db_id, t1.db_name, count(t2.id) table_count
        from qua_web_ch_task_result_db t1
        left join qua_web_ch_task_result_table t2 on (t1.id = t2.db_id)
        where t1.element_id = #{elementId}
        and t1.snapshoot_version = #{snapshootVersion}
        group by t1.id, t1.db_name
        ) tmp
        where 1 = 1
        <if test="dbName != null and dbName !='' ">
            and db_name like concat('%',#{dbName},'%')
        </if>
        <if test="tableCount != null">
            and table_count = #{tableCount}
        </if>
        order by db_name
    </select>

    <select id="getMysqlDbPage" resultType="com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeDbPage">
        select * from
        (select t1.id as db_id, t1.db_name, count(t2.id) table_count
        from qua_web_mysql_task_result_db t1
        left join qua_web_mysql_task_result_table t2 on (t1.id = t2.db_id)
        where t1.element_id = #{elementId}
        and t1.snapshoot_version = #{snapshootVersion}
        group by t1.id, t1.db_name
        ) tmp
        where 1 = 1
        <if test="dbName != null and dbName !='' ">
            and db_name like concat('%',#{dbName},'%')
        </if>
        <if test="tableCount != null">
            and table_count = #{tableCount}
        </if>
        order by db_name
    </select>

    <select id="getHiveDbPage" resultType="com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.ElementNodeDbPage">
        select * from
        (select t1.id as db_id, t1.db_name, count(t2.id) table_count
        from qua_web_hive_task_result_db t1
        left join qua_web_hive_task_result_table t2 on (t1.id = t2.db_id)
        where t1.element_id = #{elementId}
        and t1.snapshoot_version = #{snapshootVersion}
        group by t1.id, t1.db_name
        ) tmp
        where 1 = 1
        <if test="dbName != null and dbName !='' ">
            and db_name like concat('%',#{dbName},'%')
        </if>
        <if test="tableCount != null">
            and table_count = #{tableCount}
        </if>
        order by db_name
    </select>

    <select id="getPanweiDbNode" resultType="com.idss.datalake.datagovern.metadata.model.job.pojo.dto.NodeDbDto">
        select t1.id as db_id, t1.db_name, count(t2.id) cnt
        from qua_web_panwei_task_result_db t1
        left join qua_web_panwei_task_result_table t2 on (t1.id = t2.db_id)
        where t1.element_id = #{elementId}
        and t1.snapshoot_version = #{snapshootVersion}
        group by t1.id, t1.db_name
    </select>
</mapper>
