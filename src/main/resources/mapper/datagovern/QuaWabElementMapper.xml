<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.element.mapper.QuaWabElementMapper">

    <select id="countConnect" resultType="com.idss.datalake.datagovern.metadata.model.element.pojo.dto.CountConnectDto">
        select t1.is_connect connect, count(t1.id) cnt
        from qua_wab_element t1
        left join qua_web_job t2 on t1.id = t2.element_id
        where t2.is_map_to_job = 1
        and t1.tenant_id = #{tenantId}
        and t1.element_type = #{elementType}
        and t1.flag = 1
        group by t1.is_connect
    </select>

    <select id="countJobState" resultType="com.idss.datalake.datagovern.metadata.model.element.pojo.dto.CountJobStateDto">
        select t2.job_state, count(t1.id) cnt
        from qua_wab_element t1
        left join qua_web_job t2 on t1.id = t2.element_id
        where t2.is_map_to_job = 1
        and t1.tenant_id = #{tenantId}
        and t1.element_type = #{elementType}
        and t1.flag = 1
        group by t2.job_state
    </select>

    <select id="queryPage" parameterType="com.idss.datalake.datagovern.metadata.model.element.pojo.dto.ElementPageRequestDto"
            resultType="com.idss.datalake.datagovern.metadata.model.element.pojo.dto.ElementPageResponseDto">
        select t1.id,
        t1.element_name,
        t1.element_type,
        t1.es_ip_port,
        t1.ch_ip,
        t1.ch_port,
        t1.create_time,
        t1.is_connect,
        t1.fail_connect_reason,
        t2.job_state,
        t1.late_scan_time as late_ent_time,
        t1.kbs_enable,
        t1.jdbc_url
        from qua_wab_element t1
        left join qua_web_job t2 on t1.id = t2.element_id
        where t2.is_map_to_job = 1
        and t1.tenant_id = #{tenantId}
        and t1.flag = 1
        <if test="elementType != null and elementType != ''">
            and t1.element_type = #{elementType}
        </if>
        <if test="connectState != null">
            and t1.is_connect = #{connectState}
        </if>
        <if test="jobState != null">
            and t2.job_state = #{jobState}
        </if>
        order by t2.late_ent_time desc
    </select>

    <select id="getEsNode" resultType="com.idss.datalake.datagovern.metadata.model.element.pojo.dto.EsOrChNodeDto">
        select t1.id as element_id, t1.element_name, t3.cnt
        from qua_wab_element t1
        left join
        (select ta.element_id, ta.snapshoot_version, count(ta.id) cnt
        from qua_web_es_task_result_index ta
        left join
        (select tt.element_id, max(tt.snapshoot_version) as version
        from qua_web_es_task_result_index tt
        group by tt.element_id) tb on ta.element_id = tb.element_id and ta.snapshoot_version = tb.version
        where tb.element_id IS NOT NULL
        and tb.version IS NOT NULL
        group by ta.element_id, ta.snapshoot_version) t3 on t1.id = t3.element_id
        where t1.element_type = 'ES'
        and t1.tenant_id = #{tenantId}
        order by t3.cnt desc
    </select>

    <select id="getChNode" resultType="com.idss.datalake.datagovern.metadata.model.element.pojo.dto.EsOrChNodeDto">
        select t1.id as element_id, t1.element_name, t3.cnt
        from qua_wab_element t1
        left join
        (select ta.element_id, ta.snapshoot_version, count(ta.id) cnt
        from qua_web_ch_task_result_db ta
        left join
        (select tt.element_id, max(tt.snapshoot_version) as version
        from qua_web_ch_task_result_db tt
        group by tt.element_id) tb on ta.element_id = tb.element_id and ta.snapshoot_version = tb.version
        where tb.element_id IS NOT NULL
        and tb.version IS NOT NULL
        group by ta.element_id, ta.snapshoot_version) t3 on t1.id = t3.element_id
        where t1.element_type = 'CH'
        and t1.tenant_id = #{tenantId}
        order by t3.cnt desc
    </select>

    <select id="getMysqlNode" resultType="com.idss.datalake.datagovern.metadata.model.element.pojo.dto.EsOrChNodeDto">
        select t1.id as element_id, t1.element_name, t3.cnt
        from qua_wab_element t1
        left join
        (select ta.element_id, ta.snapshoot_version, count(ta.id) cnt
        from qua_web_mysql_task_result_db ta
        left join
        (select tt.element_id, max(tt.snapshoot_version) as version
        from qua_web_mysql_task_result_db tt
        group by tt.element_id) tb on ta.element_id = tb.element_id and ta.snapshoot_version = tb.version
        where tb.element_id IS NOT NULL
        and tb.version IS NOT NULL
        group by ta.element_id, ta.snapshoot_version) t3 on t1.id = t3.element_id
        where t1.element_type = 'MYSQL'
        and t1.tenant_id = #{tenantId}
        order by t3.cnt desc
    </select>

    <select id="getHiveNode" resultType="com.idss.datalake.datagovern.metadata.model.element.pojo.dto.EsOrChNodeDto">
        select t1.id as element_id, t1.element_name, t3.cnt
        from qua_wab_element t1
        left join
        (select ta.element_id, ta.snapshoot_version, count(ta.id) cnt
        from qua_web_hive_task_result_db ta
        left join
        (select tt.element_id, max(tt.snapshoot_version) as version
        from qua_web_hive_task_result_db tt
        group by tt.element_id) tb on ta.element_id = tb.element_id and ta.snapshoot_version = tb.version
        where tb.element_id IS NOT NULL
        and tb.version IS NOT NULL
        group by ta.element_id, ta.snapshoot_version) t3 on t1.id = t3.element_id
        where t1.element_type = 'HIVE'
        and t1.tenant_id = #{tenantId}
        order by t3.cnt desc
    </select>

    <select id="getKafkaNode" resultType="com.idss.datalake.datagovern.metadata.model.element.pojo.dto.EsOrChNodeDto">
        select t1.id as element_id, t1.element_name, t3.cnt
        from qua_wab_element t1
        left join
        (select ta.element_id, ta.snapshoot_version, count(ta.id) cnt
        from qua_web_kafka_task_result ta
        left join
        (select tt.element_id, max(tt.snapshoot_version) as version
        from qua_web_kafka_task_result tt
        group by tt.element_id) tb on ta.element_id = tb.element_id and ta.snapshoot_version = tb.version
        where tb.element_id IS NOT NULL
        and tb.version IS NOT NULL
        group by ta.element_id, ta.snapshoot_version) t3 on t1.id = t3.element_id
        where t1.element_type = 'KAFKA'
        and t1.tenant_id = #{tenantId}
        order by t3.cnt desc
    </select>

    <select id="queryClickhouseDb" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.db_name as element_name,
        'Clickhouse' as element_type,
        '库' as data_type,
        t.element_id,
        t.db_name as asset_path,
        'clickhouse_db' as asset_type,
        'DB' as asset_type_code,
        t.last_snapshoot_version as snapshoot_version
        from qua_web_ch_element_detail_db t
        where t.tenant_id = #{tenantId}
        and (
        <foreach collection="elementSnapshootModels" item="param" separator=" OR ">
            <choose>
                <when test="param.elementId != null and param.snapshootVersion != null">
                    (t.element_id = #{param.elementId} AND t.last_snapshoot_version = #{param.snapshootVersion})
                </when>
            </choose>
        </foreach>
        )
        order by create_time desc
    </select>

    <select id="queryClickhouseTable" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.table_name as element_name,
        'Clickhouse' as element_type,
        '表' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name) as asset_path,
        'clickhouse_table' as asset_type,
        'TABLE' as asset_type_code,
        t.table_dscribe as element_desc,
        t.last_snapshoot_version as snapshoot_version,
        t.db_name,
        t.table_name
        from qua_web_ch_element_detail_table t
        where t.tenant_id = #{tenantId}
        <if test="elementId != null">
            and t.element_id = #{elementId}
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name = #{dbName}
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        <if test="elementName != null and elementName !=''">
            and t.table_name like concat('%',#{elementName},'%')
        </if>
        order by create_time desc
    </select>
    <select id="queryClickhouseField" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.db_name as db_name,
        t.table_name as table_name,
        t.column_name as element_name,
        'Clickhouse' as element_type,
        '字段' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name,t.column_name) as asset_path,
        'clickhouse_field' as asset_type,
        'FIELD' as asset_type_code,
        t.create_time
        from qua_web_ch_element_detail_column t
        WHERE t.tenant_id = #{tenantId}
        <if test="elementId != null">
            and t.element_id = #{elementId}
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name = #{dbName}
        </if>
        <if test="tableName != null and tableName !=''">
            and t.table_name = #{tableName}
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        <if test="elementName != null and elementName !=''">
            and t.column_name like concat('%',#{elementName},'%')
        </if>
        order by t.create_time desc
    </select>

    <select id="queryHiveDb" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.db_name as element_name,
        'Hive' as element_type,
        '库' as data_type,
        t.element_id,
        t.db_name as asset_path,
        'hive_db' as asset_type,
        'DB' as asset_type_code,
        t.last_snapshoot_version as snapshoot_version
        from qua_web_hive_element_detail_db t
        where t.tenant_id = #{tenantId}
        and (
        <foreach collection="elementSnapshootModels" item="param" separator=" OR ">
            <choose>
                <when test="param.elementId != null and param.snapshootVersion != null">
                    (t.element_id = #{param.elementId} AND t.last_snapshoot_version = #{param.snapshootVersion})
                </when>
            </choose>
        </foreach>
        )
        order by create_time desc
    </select>

    <select id="queryHiveTable" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.table_name as element_name,
        'Hive' as element_type,
        '表' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name) as asset_path,
        'hive_table' as asset_type,
        'TABLE' as asset_type_code,
        t.table_dscribe as elementDesc,
        t.last_snapshoot_version as snapshoot_version,
        t.db_name,
        t.table_name
        from qua_web_hive_element_detail_table t
        where t.tenant_id = #{tenantId}
        <if test="elementId != null">
            and t.element_id = #{elementId}
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name = #{dbName}
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>

        <if test="elementName != null and elementName !=''">
            and t.table_name like concat('%',#{elementName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryHiveField" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select DISTINCT t.id,
        t.db_name as db_name,
        t.table_name as table_name,
        t.column_name as element_name,
        'Hive' as element_type,
        '字段' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name,t.column_name) as asset_path,
        'hive_field' as asset_type,
        'FIELD' as asset_type_code,
        t.create_time
        from qua_web_hive_element_detail_column t
        where t.tenant_id = #{tenantId}
        <if test="elementId != null">
            and t.element_id = #{elementId}
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name = #{dbName}
        </if>
        <if test="tableName != null and tableName !=''">
            and t.table_name = #{tableName}
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        <if test="elementName != null and elementName !=''">
            and t.column_name like concat('%',#{elementName},'%')
        </if>
        order by t.create_time desc
    </select>

    <select id="queryMysqlDb" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.db_name as element_name,
        'Mysql' as element_type,
        '库' as data_type,
        t.element_id,
        t.db_name as asset_path,
        'mysql_db' as asset_type,
        'DB' as asset_type_code,
        t.last_snapshoot_version as snapshoot_version
        from qua_web_mysql_element_detail_db t
        where t.tenant_id = #{tenantId}
        and (
        <foreach collection="elementSnapshootModels" item="param" separator=" OR ">
            <choose>
                <when test="param.elementId != null and param.snapshootVersion != null">
                    (t.element_id = #{param.elementId} AND t.last_snapshoot_version = #{param.snapshootVersion})
                </when>
            </choose>
        </foreach>
        )
        order by create_time desc
    </select>

    <select id="queryMysqlTable" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.table_name as element_name,
        'Mysql' as element_type,
        '表' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name) as asset_path,
        'mysql_table' as asset_type,
        'TABLE' as asset_type_code,
        t.table_dscribe as element_desc,
        t.last_snapshoot_version as snapshoot_version,
        t.db_name,
        t.table_name
        from qua_web_mysql_element_detail_table t
        where t.tenant_id = #{tenantId}
        <if test="elementId != null">
            and t.element_id = #{elementId}
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name = #{dbName}
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>

        <if test="elementName != null and elementName !=''">
            and t.table_name like concat('%',#{elementName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryMysqlField" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.db_name as db_name,
        t.table_name as table_name,
        t.column_name as element_name,
        'Mysql' as element_type,
        '字段' as data_type,
        t.element_id,
        concat_ws(',',t.db_name,t.table_name,t.column_name) as asset_path,
        'mysql_field' as asset_type,
        'FIELD' as asset_type_code,
        t.create_time
        from qua_web_mysql_element_detail_column t
        where t.tenant_id = #{tenantId}
        <if test="elementId != null">
            and t.element_id = #{elementId}
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name = #{dbName}
        </if>
        <if test="tableName != null and tableName !=''">
            and t.table_name = #{tableName}
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        <if test="elementName != null and elementName !=''">
            and t.column_name like concat('%',#{elementName},'%')
        </if>
        order by t.create_time desc
    </select>

    <select id="queryElasticsearchIndex" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.index_name as element_name,
        'Elasticsearch' as element_type,
        '索引' as data_type,
        t.element_id,
        t.index_name as asset_path,
        'elasticsearch_index' as asset_type,
        'INDEX' as asset_type_code,
        t.index_dscribe as elementDesc,
        t.last_snapshoot_version as snapshoot_version
        from qua_web_es_element_detail_index t
        where t.tenant_id = #{tenantId}
        and (
        <foreach collection="elementSnapshootModels" item="param" separator=" OR ">
            <choose>
                <when test="param.elementId != null and param.snapshootVersion != null">
                    (t.element_id = #{param.elementId} AND t.last_snapshoot_version = #{param.snapshootVersion})
                </when>
            </choose>
        </foreach>
        )
        order by create_time desc
    </select>

    <select id="queryElasticsearchField" parameterType="com.idss.datalake.datagovern.dictionary.dto.QueryElementRequest"
            resultType="com.idss.datalake.datagovern.dictionary.vo.QueryElementVo">
        select t.id,
        t.index_name as table_name,
        t.field_name as element_name,
        'Elasticsearch' as element_type,
        '索引字段' as data_type,
        t.element_id,
        concat_ws(',',t.index_name,t.field_name) as asset_path,
        'elasticsearch_field' as asset_type,
        'INDEX_FIELD' as asset_type_code,
        t.create_time,
        t.last_snapshoot_version as snapshoot_version
        from qua_web_es_element_detail_field t
        WHERE t.tenant_id = #{tenantId}
        <if test="elementId != null">
            and t.element_id = #{elementId}
        </if>
        <if test="tableName != null and tableName !=''">
            and t.index_name = #{tableName}
        </if>
        <if test="snapshootVersion != null and snapshootVersion !=''">
            and t.last_snapshoot_version = #{snapshootVersion}
        </if>
        <if test="elementName != null and elementName !=''">
            and t.field_name like concat('%',#{elementName},'%')
        </if>
        order by t.create_time desc
    </select>

    <select id="queryClickhouseElementSnapshoot" resultType="com.idss.datalake.datagovern.blood.model.ElementSnapshootModel">
        select element_id ,max(last_snapshoot_version) as snapshoot_version from qua_web_ch_element_detail_db
        where tenant_id = #{tenantId}
        group by element_id ,last_snapshoot_version
    </select>

    <select id="queryHiveElementSnapshoot" resultType="com.idss.datalake.datagovern.blood.model.ElementSnapshootModel">
        select element_id ,max(last_snapshoot_version) as snapshoot_version from qua_web_hive_element_detail_db
        where tenant_id = #{tenantId}
        group by element_id ,last_snapshoot_version
    </select>

    <select id="queryMysqlElementSnapshoot" resultType="com.idss.datalake.datagovern.blood.model.ElementSnapshootModel">
        select element_id ,max(last_snapshoot_version) as snapshoot_version from qua_web_mysql_element_detail_db
        where tenant_id = #{tenantId}
        group by element_id ,last_snapshoot_version
    </select>

    <select id="queryElasticsearchElementSnapshoot" resultType="com.idss.datalake.datagovern.blood.model.ElementSnapshootModel">
        select element_id ,max(last_snapshoot_version) as snapshoot_version from qua_web_es_element_detail_index
        where tenant_id = #{tenantId}
        group by element_id ,last_snapshoot_version
    </select>

    <select id="queryLatestMysqlTable" resultType="com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebMysqlElementDetailTable">
        select t1.*
        from qua_web_mysql_element_detail_table t1
        where t1.element_id = #{elementId}
        and t1.db_name = #{dbName}
        and t1.table_name = #{tableName}
        and t1.last_snapshoot_version =
        (select MAX(snapshoot_version) from qua_web_mysql_task_result_db where element_id = #{elementId})
    </select>

    <select id="queryLatestChTable" resultType="com.idss.datalake.datagovern.metadata.model.detail.entity.ChElementDetailTable">
        select t1.*
        from qua_web_ch_element_detail_table t1
        where t1.element_id = #{elementId}
        and t1.db_name = #{dbName}
        and t1.table_name = #{tableName}
        and t1.last_snapshoot_version =
        (select MAX(snapshoot_version) from qua_web_ch_task_result_db where element_id = #{elementId})
    </select>

    <select id="queryLatestHiveTable" resultType="com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebHiveElementDetailTable">
        select t1.*
        from qua_web_hive_element_detail_table t1
        where t1.element_id = #{elementId}
        and t1.db_name = #{dbName}
        and t1.table_name = #{tableName}
        and t1.last_snapshoot_version =
        (select MAX(snapshoot_version) from qua_web_hive_task_result_db where element_id = #{elementId})
    </select>

    <select id="queryLatestEsIndex" resultType="com.idss.datalake.datagovern.metadata.model.detail.entity.EsElementDetailIndex">
        select t1.*
        from qua_web_es_element_detail_index t1
        where t1.element_id = #{elementId}
        and t1.index_name = #{tableName}
        and t1.last_snapshoot_version =
        (select MAX(snapshoot_version) from qua_web_es_task_result_index where element_id = #{elementId})
    </select>

    <select id="queryMaxVersion" resultType="java.lang.String">
        SELECT MAX(snapshoot_version) from ${table} where element_id = #{elementId}
    </select>

    <select id="getPanWeiNode" resultType="com.idss.datalake.datagovern.metadata.model.element.pojo.dto.EsOrChNodeDto">
        select t1.id as element_id, t1.element_name, t3.cnt
        from qua_wab_element t1
        left join
        (select ta.element_id, ta.snapshoot_version, count(ta.id) cnt
        from qua_web_panwei_task_result_db ta
        left join
        (select tt.element_id, max(tt.snapshoot_version) as version
        from qua_web_panwei_task_result_db tt
        group by tt.element_id) tb on ta.element_id = tb.element_id and ta.snapshoot_version = tb.version
        where tb.element_id IS NOT NULL
        and tb.version IS NOT NULL
        group by ta.element_id, ta.snapshoot_version) t3 on t1.id = t3.element_id
        where t1.element_type = 'PANWEI'
        and t1.tenant_id = #{tenantId}
        order by t3.cnt desc
    </select>

    <select id="queryLatestPanweiTable" resultType="com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebPanweiElementDetailTable">
        select t1.*
        from qua_web_panwei_element_detail_table t1
        where t1.element_id = #{elementId}
        and t1.db_name = #{dbName}
        and t1.table_name = #{tableName}
        and t1.last_snapshoot_version =
        (select MAX(snapshoot_version) from qua_web_panwei_task_result_db where element_id = #{elementId})
    </select>

    <select id="queryLatestKafkaTopic" resultType="com.idss.datalake.datagovern.metadata.model.detail.entity.QuaWebKafkaElementDetail">
        select t1.*
        from qua_web_kafka_element_detail t1
        where t1.element_id = #{elementId}
        and t1.topic_name = #{topicName}
        and t1.last_snapshoot_version =
        (select MAX(snapshoot_version) from qua_web_kafka_task_result where element_id = #{elementId})
    </select>
</mapper>
