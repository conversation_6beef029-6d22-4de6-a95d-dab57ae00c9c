<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datagovern.metadata.model.detail.mapper.QuaWebHdfsElementDetailDirMapper">


    <select id="queryPage" resultType="com.idss.datalake.datagovern.metadata.model.detail.pojo.vo.HdfsPageVo">
        select *
        from
        (
        select
        id,
        element_id ,
        dir_name as name,
        'dir' as file_type,
        dir_authority as authority,
        dir_owner as owner,
        dir_group as group_name,
        storage_size,
        dir_path
        from qua_web_hdfs_element_detail_dir
        where element_id = #{elementId}
        <if test="dirPath != null and dirPath != ''">
            and parent_dir_path = #{dirPath}
        </if>
        union
        select
        id,
        element_id ,
        file_name as name,
        'file' as file_type,
        file_authority as authority,
        file_owner as owner,
        file_group as group_name,
        storage_size,
        dir_path
        from qua_web_hdfs_element_detail_file
        where element_id = #{elementId}
        <if test="dirPath != null and dirPath != ''">
            and dir_path = #{dirPath}
        </if>
        ) a
        where 1 = 1
        <if test="name != null and name != ''">
            and name like concat('%',#{name},'%')
        </if>
        limit #{offset},#{pageSize}
    </select>

    <select id="queryPageCount" resultType="long">
        select count(*)
        from
        (
        select
        element_id ,
        dir_name as name,
        'dir' as file_type,
        dir_authority as authority,
        dir_owner as owner,
        dir_group as group_name,
        storage_size
        from qua_web_hdfs_element_detail_dir
        where element_id = #{elementId}
        <if test="dirPath != null and dirPath != ''">
            and parent_dir_path = #{dirPath}
        </if>
        union
        select
        element_id ,
        file_name as name,
        'file' as file_type,
        file_authority as authority,
        file_owner as owner,
        file_group as group_name,
        storage_size
        from qua_web_hdfs_element_detail_file
        where element_id = #{elementId}
        <if test="dirPath != null and dirPath != ''">
            and dir_path = #{dirPath}
        </if>
        ) a
        where 1 = 1
        <if test="name != null and name != ''">
            and name like concat('%',#{name},'%')
        </if>
    </select>
</mapper>
