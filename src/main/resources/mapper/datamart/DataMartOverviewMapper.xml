<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datamart.mapper.DataMartOverviewMapper">

    <select id="qualityOverview" resultType="map">
        select b.element_type "elementType", count(1) cnt
        from qua_monitor_job a
                 left join qua_monitor_model b on a.model_id = b.id
                 left join qua_monitor_model_resource c on c.model_id = b.id
        where a.flag = '1'
        group by b.element_type
    </select>

    <select id="jobTrend" resultType="map">
        select * from (select TO_CHAR(create_time, 'YYYY-MM-DD') "createTime", count(1) cnt
                       from qua_monitor_job
                       group by create_time
                       order by create_time) a
        where a.createTime <![CDATA[ >= ]]> #{startDate}
        and a.createTime <![CDATA[ <= ]]> #{endDate}
    </select>

    <select id="etlSummary" resultType="map">
        select c.config_value "configValue", count(1) cnt
        from (select a.id, a.source_name, b.config_key, case when b.config_value is null then 'once' else b.config_value end config_value
              from etl_source a
                       left join (select * from etl_reader_param where config_key = 'runType') b on a.id = b.source_id
              where a.status = 1) c
        group by c.config_value
    </select>

    <select id="statSum" resultType="map">
        select sum(out_count) "currentSum"
        from mv_flow_metric_interval_1day
        where node_type = 'parser'
        <if test="flowId != null and flowId != ''">
            and flow_id = #{flowId}
        </if>
    </select>

    <select id="statWriteLength" resultType="map">
        select sum(length) "currentLength" from mv_flow_metric_interval_1day where node_type = 'parser' and writer_type = 'CH'
    </select>

    <select id="diskLength" resultType="map">
        select writer_type "writerType", sum(length) length
        from mv_flow_metric_interval_1day
        where node_type = 'parser'
          and writer_type in ('CH', 'ES')
        group by writer_type
    </select>

    <select id="writeTrend" resultType="map">
        select to_char(a.interval_end_time, 'YYYY-MM-DD') as "endTime", sum(a.out_count) "outCount"
        from (
                 select interval_end_time, out_count from mv_flow_metric_interval_1day where node_type = 'parser' and interval_end_time between #{startTime} and #{endTime}
             ) a
        group by "endTime";
    </select>

    <select id="queryDatasource"  parameterType="map" resultType="com.idss.datalake.datamart.dto.response.DataMartOverviewResponse">
        select a.id as "sourceId",
        a.id as "flowId",
        a."name" as "sourceType",
        a.source_name as "sourceName",
        a.source_desc as "sourceDesc",
        CASE
        WHEN a.ACCESS_STATUS = 1 THEN
        '已生效'
        ELSE
        '未生效'
        END as "status",
        CONCAT_WS('/',a.table_name,a.index_name,a.topic,a.origin_to_topic) "storageType",
        to_char(a.create_date, 'YYYY-MM-DD HH24:MI:SS') AS "etlStartTime",
        to_char(a.update_date, 'YYYY-MM-DD HH24:MI:SS') AS "updateDate",
        a.update_user as "updateUser",
        a.source_switch as "switch",
        a.src_device_type as "srcDeviceType"
        from (
        select a.*,e.`name`,CONCAT(b.table_name,'(数仓)') table_name,CONCAT(c.index_name,'(索引)') index_name,case when d.topic != '' then
        CONCAT(d.topic,'(Kafka)') else null end topic,case when d.origin_to_topic != '' then CONCAT(d.origin_to_topic,'(Kafka)') else null end
        origin_to_topic ,f.config_value src_device_type
        from etl_source a
        left join etl_writer_ch_config b on a.id = b.source_id
        left join etl_writer_es_config c on a.id = c.source_id
        left join etl_writer_kafka_config d on a.id = d.source_id
        left join etl_source_type e ON a.source_type = e.source_type
        left join (select * from etl_reader_param WHERE config_key = 'generic_datasource_type') f on f.source_id = a.id
        ) a
        WHERE a.status = '1'
        <if test="name != null and name != ''">
            and a.source_name like concat('%',#{name},'%')
        </if>
        order by a.create_date desc
        LIMIT #{beginIndex},#{pageSize}
    </select>
    <select id="queryTotalsource" parameterType="map" resultType="int">
        SELECT
        COUNT(c.sourceId)
        FROM
        (
        select a.id as "sourceId",
        a.id as "flowId",
        a."name" as "sourceType",
        a.source_name as "sourceName",
        a.source_desc as "sourceDesc",
        CASE
        WHEN a.ACCESS_STATUS = 1 THEN
        '已生效'
        ELSE
        '未生效'
        END as "status",
        CONCAT_WS('/',a.table_name,a.index_name,a.topic,a.origin_to_topic) "storageType",
        to_char(a.create_date, 'YYYY-MM-DD HH24:MI:SS') AS "etlStartTime",
        to_char(a.update_date, 'YYYY-MM-DD HH24:MI:SS') AS "updateDate",
        a.update_user as "updateUser",
        a.source_switch as "switch",
        a.src_device_type as "srcDeviceType"
        from (
        select a.*,e."name",CONCAT(b.table_name,'(数仓)') table_name,CONCAT(c.index_name,'(索引)') index_name,case when d.topic != '' then
        CONCAT(d.topic,'(Kafka)') else null end topic,case when d.origin_to_topic != '' then CONCAT(d.origin_to_topic,'(Kafka)') else null end
        origin_to_topic ,f.config_value src_device_type
        from etl_source a
        left join etl_writer_ch_config b on a.id = b.source_id
        left join etl_writer_es_config c on a.id = c.source_id
        left join etl_writer_kafka_config d on a.id = d.source_id
        left join etl_source_type e ON a.source_type = e.source_type
        left join (select * from etl_reader_param WHERE config_key = 'generic_datasource_type') f on f.source_id = a.id
        ) a
        WHERE a.status = '1'
        <if test="name != null and name != ''">
            and a.source_name like concat('%',#{name},'%')
        </if>
        ) c
    </select>

    <select id="etlAppendix" parameterType="map" resultType="map">
        select flow_id "flowId", data_type "dataType", platform, create_time "createTime",end_time "endTime" from data_mart_etl_source_appendix
    </select>
</mapper>
