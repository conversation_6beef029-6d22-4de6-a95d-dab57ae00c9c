<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datamart.mapper.DataMartGroupMapper">

    <select id="page" parameterType="com.idss.datalake.datamart.dto.request.DataMartGroupPageRequest"
            resultType="com.idss.datalake.datamart.entity.DataMartGroup">
        select t1.* ,
               t2.type_name as data_mart_type_name
        from data_mart_group t1,data_mart_type t2
        where t1.data_mart_type_id = t2.id and t1.tenant_id = #{tenantId}
        <if test="groupName != null">
            and group_name like concat('%',#{groupName},'%')
        </if>
        order by t1.create_time desc
    </select>
</mapper>
