<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datamart.mapper.DataMartAssetMapper">

    <select id="queryClickhouseDbPage" parameterType="com.idss.datalake.datamart.dto.request.QueryElementRequest"
            resultType="com.idss.datalake.datamart.dto.response.QueryElementVo">
        select t.id,
               t.db_name as element_name,
               'Clickhouse' as element_type,
               '库' as data_type,
               t.element_id,
               t.db_name as asset_path,
               'clickhouse_db' as asset_type,
               'DB' as asset_type_code
        from qua_web_ch_element_detail_db t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.db_name like concat('%',#{elementName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryClickhouseTablePage" parameterType="com.idss.datalake.datamart.dto.request.QueryElementRequest"
            resultType="com.idss.datalake.datamart.dto.response.QueryElementVo">
        select t.id,
               t.table_name as element_name,
               'Clickhouse' as element_type,
               '表' as data_type,
               t.element_id,
               concat_ws(',',t.db_name,t.table_name) as asset_path,
               'clickhouse_table' as asset_type,
               'TABLE' as asset_type_code
        from qua_web_ch_element_detail_table t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.table_name like concat('%',#{elementName},'%')
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name like concat('%',#{dbName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryClickhouseFieldPage" parameterType="com.idss.datalake.datamart.dto.request.QueryElementRequest"
            resultType="com.idss.datalake.datamart.dto.response.QueryElementVo">
        select t.id,
               t.column_name as element_name,
               'Clickhouse' as element_type,
               '字段' as data_type,
               t.element_id,
               concat_ws(',',t.db_name,t.table_name,t.column_name) as asset_path,
               'clickhouse_field' as asset_type,
               'FIELD' as asset_type_code
        from qua_web_ch_element_detail_column t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.column_name like concat('%',#{elementName},'%')
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name like concat('%',#{dbName},'%')
        </if>
        <if test="tableName != null and tableName !=''">
            and t.table_name like concat('%',#{tableName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryHiveDbPage" parameterType="com.idss.datalake.datamart.dto.request.QueryElementRequest"
            resultType="com.idss.datalake.datamart.dto.response.QueryElementVo">
        select t.id,
               t.db_name as element_name,
               'Hive' as element_type,
               '库' as data_type,
               t.element_id,
               t.db_name as asset_path,
               'hive_db' as asset_type,
               'DB' as asset_type_code
        from qua_web_hive_element_detail_db t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.db_name like concat('%',#{elementName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryHiveTablePage" parameterType="com.idss.datalake.datamart.dto.request.QueryElementRequest"
            resultType="com.idss.datalake.datamart.dto.response.QueryElementVo">
        select t.id,
               t.table_name as element_name,
               'Hive' as element_type,
               '表' as data_type,
               t.element_id,
               concat_ws(',',t.db_name,t.table_name) as asset_path,
               'hive_table' as asset_type,
               'TABLE' as asset_type_code
        from qua_web_hive_element_detail_table t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.table_name like concat('%',#{elementName},'%')
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name like concat('%',#{dbName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryHiveFieldPage" parameterType="com.idss.datalake.datamart.dto.request.QueryElementRequest"
            resultType="com.idss.datalake.datamart.dto.response.QueryElementVo">
        select t.id,
               t.column_name as element_name,
               'Hive' as element_type,
               '字段' as data_type,
               t.element_id,
               concat_ws(',',t.db_name,t.table_name,t.column_name) as asset_path,
               'hive_field' as asset_type,
               'FIELD' as asset_type_code
        from qua_web_hive_element_detail_column t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.column_name like concat('%',#{elementName},'%')
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name like concat('%',#{dbName},'%')
        </if>
        <if test="tableName != null and tableName !=''">
            and t.table_name like concat('%',#{tableName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryMysqlDbPage" parameterType="com.idss.datalake.datamart.dto.request.QueryElementRequest"
            resultType="com.idss.datalake.datamart.dto.response.QueryElementVo">
        select t.id,
               t.db_name as element_name,
               'Mysql' as element_type,
               '库' as data_type,
               t.element_id,
               t.db_name as asset_path,
               'mysql_db' as asset_type,
               'DB' as asset_type_code
        from qua_web_mysql_element_detail_db t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.db_name like concat('%',#{elementName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryMysqlTablePage" parameterType="com.idss.datalake.datamart.dto.request.QueryElementRequest"
            resultType="com.idss.datalake.datamart.dto.response.QueryElementVo">
        select t.id,
               t.table_name as element_name,
               'Mysql' as element_type,
               '表' as data_type,
               t.element_id,
               concat_ws(',',t.db_name,t.table_name) as asset_path,
               'mysql_table' as asset_type,
               'TABLE' as asset_type_code
        from qua_web_mysql_element_detail_table t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.table_name like concat('%',#{elementName},'%')
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name like concat('%',#{dbName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryMysqlFieldPage" parameterType="com.idss.datalake.datamart.dto.request.QueryElementRequest"
            resultType="com.idss.datalake.datamart.dto.response.QueryElementVo">
        select t.id,
               t.column_name as element_name,
               'Mysql' as element_type,
               '字段' as data_type,
               t.element_id,
               concat_ws(',',t.db_name,t.table_name,t.column_name) as asset_path,
               'mysql_field' as asset_type,
               'FIELD' as asset_type_code
        from qua_web_mysql_element_detail_column t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.column_name like concat('%',#{elementName},'%')
        </if>
        <if test="dbName != null and dbName !=''">
            and t.db_name like concat('%',#{dbName},'%')
        </if>
        <if test="tableName != null and tableName !=''">
            and t.table_name like concat('%',#{tableName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryElasticsearchIndexPage" parameterType="com.idss.datalake.datamart.dto.request.QueryElementRequest"
            resultType="com.idss.datalake.datamart.dto.response.QueryElementVo">
        select t.id,
               t.index_name as element_name,
               'Elasticsearch' as element_type,
               '索引' as data_type,
               t.element_id,
               t.index_name as asset_path,
               'elasticsearch_index' as asset_type,
               'INDEX' as asset_type_code
        from qua_web_es_element_detail_index t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.index_name like concat('%',#{elementName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="queryElasticsearchFieldPage" parameterType="com.idss.datalake.datamart.dto.request.QueryElementRequest"
            resultType="com.idss.datalake.datamart.dto.response.QueryElementVo">
        select t.id,
               t.field_name as element_name,
               'Elasticsearch' as element_type,
               '索引字段' as data_type,
               t.element_id,
               concat_ws(',',t.index_name,t.field_name) as asset_path,
               'elasticsearch_field' as asset_type,
               'INDEX_FIELD' as asset_type_code
        from qua_web_es_element_detail_field t
        where t.tenant_id = #{tenantId}
        <if test="elementName != null and elementName !=''">
            and t.field_name like concat('%',#{elementName},'%')
        </if>
        order by create_time desc
    </select>

    <select id="page" parameterType="com.idss.datalake.datamart.dto.request.DataMartAssetPageRequest" resultType="com.idss.datalake.datamart.entity.DataMartAsset">
        select * from (select t1.*,
        case
        when t1.asset_type = 'clickhouse_table' then (select key_words
        from qua_web_ch_element_detail_table tt
        where tt.element_id = t1.element_id
        and tt.db_name = t1.db_name
        and tt.table_name = t1.table_name)
        when t1.asset_type = 'mysql_table' then (select key_words
        from qua_web_mysql_element_detail_table tt
        where tt.element_id = t1.element_id
        and tt.db_name = t1.db_name
        and tt.table_name = t1.table_name)
        when t1.asset_type = 'hive_table' then (select key_words
        from qua_web_hive_element_detail_table tt
        where tt.element_id = t1.element_id
        and tt.db_name = t1.db_name
        and tt.table_name = t1.table_name)
        when t1.asset_type = 'elasticsearch_index' then (select key_words
        from qua_web_es_element_detail_index tt
        where tt.element_id = t1.element_id
        and tt.index_name = t1.index_name)
        else '' end as key_words,
        t2.type_name,
        t3.group_name
        from data_mart_asset t1,
        data_mart_type t2,
        data_mart_group t3
        where t1.type_id = t2.id
        and t1.group_id = t3.id) a where 1=1
        <if test="keyWords != null">
            and a.key_words like concat('%',#{keyWords},'%')
        </if>
        <if test="batchNo != null">
            and a.batch_no like concat('%',#{batchNo},'%')
        </if>
        <if test="assetName != null">
            and a.asset_name like concat('%',#{assetName},'%')
        </if>
        <if test="assetType != null">
            and a.asset_type = #{assetType}
        </if>
        <if test="assetTypeCode != null">
            and a.asset_type_code = #{assetTypeCode}
        </if>
        <if test="typeId != null">
            and a.type_id = #{typeId}
        </if>
        <if test="groupId != null">
            and a.group_id = #{groupId}
        </if>
        <if test="typeName != null">
            and a.type_name like concat('%',#{typeName},'%')
        </if>
        <if test="groupName != null">
            and a.group_name like concat('%',#{groupName},'%')
        </if>
        <if test="dataName != null">
            and a.data_name like concat('%',#{dataName},'%')
        </if>
        <if test="filterType == 'subscribe'">
            and a.id in (select asset_id from data_mart_asset_subscribe where user_id = #{userId})
        </if>
        <if test="filterType == 'thumbs_up'">
            and a.id in (select asset_id from data_mart_asset_thumbs_up where user_id = #{userId})
        </if>
        <if test="filterType == 'dataMartSubscribe'">
            and a.id in (select asset_id from data_mart_subscribe where user_id = #{userId})
        </if>
        <if test="tenantId != null">
            and a.tenant_id = #{tenantId}
        </if>
        <if test="publishStatus != null">
            and a.publish_status = #{publishStatus}
        </if>
        order by ${orderType} desc
    </select>

    <select id="queryClickhouseDbDetail" parameterType="com.idss.datalake.datamart.dto.request.AssetDetailRequest" resultType="com.idss.datalake.datamart.dto.response.AssetDetailVo">
        select 'Clickhouse' as type,
               t.db_name as name,
               t.create_user,
               t.create_time,
               t.update_time,
               t.db_name    as asset_path
        from qua_web_ch_element_detail_db t
        where t.element_id = #{elementId} and t.db_name = #{dbName}
    </select>
    <select id="queryClickhouseTableDetail" parameterType="com.idss.datalake.datamart.dto.request.AssetDetailRequest"  resultType="com.idss.datalake.datamart.dto.response.AssetDetailVo">
        select 'Clickhouse' as type,
               t.table_name as name,
               t.create_user,
               t.create_time,
               t.update_time,
               concat_ws(',',t.db_name,t.table_name)    as asset_path
        from qua_web_ch_element_detail_table t
        where t.element_id = #{elementId} and t.db_name = #{dbName} and t.table_name = #{tableName}
    </select>
    <select id="queryHiveDbDetail" parameterType="com.idss.datalake.datamart.dto.request.AssetDetailRequest"  resultType="com.idss.datalake.datamart.dto.response.AssetDetailVo">
        select 'Hive' as type,
               t.db_name as name,
               t.create_user,
               t.create_time,
               t.update_time,
               t.db_name    as asset_path
        from qua_web_hive_element_detail_db t
        where t.element_id = #{elementId} and t.db_name = #{dbName}
    </select>
    <select id="queryHiveTableDetail"  parameterType="com.idss.datalake.datamart.dto.request.AssetDetailRequest"  resultType="com.idss.datalake.datamart.dto.response.AssetDetailVo">
        select 'Hive' as type,
               t.table_name as name,
               t.create_user,
               t.create_time,
               t.update_time,
               concat_ws(',',t.db_name,t.table_name)    as asset_path
        from qua_web_hive_element_detail_table t
        where t.element_id = #{elementId} and t.db_name = #{dbName} and t.table_name = #{tableName}
    </select>
    <select id="queryMysqlDbDetail" parameterType="com.idss.datalake.datamart.dto.request.AssetDetailRequest"  resultType="com.idss.datalake.datamart.dto.response.AssetDetailVo">
        select 'Mysql' as type,
               t.db_name as name,
               t.create_user,
               t.create_time,
               t.update_time,
               t.db_name    as asset_path
        from qua_web_mysql_element_detail_db t
        where t.element_id = #{elementId} and t.db_name = #{dbName}
    </select>
    <select id="queryMysqlTableDetail" parameterType="com.idss.datalake.datamart.dto.request.AssetDetailRequest"  resultType="com.idss.datalake.datamart.dto.response.AssetDetailVo">
        select 'Mysql' as type,
               t.table_name as name,
               t.create_user,
               t.create_time,
               t.update_time,
               concat_ws(',',t.db_name,t.table_name)   as asset_path
        from qua_web_mysql_element_detail_table t
        where t.element_id = #{elementId} and t.db_name = #{dbName} and t.table_name = #{tableName}
    </select>
    <select id="queryElasticsearchIndexDetail" parameterType="com.idss.datalake.datamart.dto.request.AssetDetailRequest"  resultType="com.idss.datalake.datamart.dto.response.AssetDetailVo">
        select 'Elasticsearch' as type,
               t.index_name as name,
               t.create_user,
               t.create_time,
               t.update_time,
               t.index_name    as asset_path
        from qua_web_es_element_detail_index t
        where t.element_id = #{elementId} and t.index_name = #{indexName}
    </select>
    <select id="pageClickhouseDbDetail" parameterType="com.idss.datalake.datamart.dto.request.AssetDetailPageRequest" resultType="com.idss.datalake.datamart.dto.response.AssetDetailPageVo">
        select
            #{elementId} as element_id,
            'clickhouse_table' as asset_type,
            t.db_name ,
            t.table_name as name,
            t.table_name ,
            t.table_name_cn as name_cn,
            t.id as detail_id,
            t.create_time,
            t.update_time
        from qua_web_ch_element_detail_table t
        where 1 = 1
        <if test="elementId != '' and elementId != null">
            and t.element_id = #{elementId}
        </if>
        <if test="elementIds != null and elementIds.size() > 0">
            and element_id IN
            <foreach item="elementId" collection="elementIds" open="(" separator="," close=")">
                #{elementId}
            </foreach>
        </if>
        <if test="dbName != '' and dbName != null">
            and t.db_name = #{dbName}
        </if>
        <if test="dbNames != null and dbNames.size() > 0">
            and db_name IN
            <foreach item="dbName" collection="dbNames" open="(" separator="," close=")">
                #{dbName}
            </foreach>
        </if>
        <if test="tableName != '' and tableName != null">
            and t.table_name_cn like concat ('%',#{tableName},'%')
        </if>
        <if test="startTime != null and endTime != null">
            and t.create_time <![CDATA[ >= ]]> #{startTime} and t.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        order by t.create_time desc
    </select>
    <select id="pageClickhouseTableDetail" parameterType="com.idss.datalake.datamart.dto.request.AssetDetailPageRequest"
            resultType="com.idss.datalake.datamart.dto.response.AssetDetailPageVo">
        select t.column_name        as itemName,
                t.column_name_cn     as columnNameCn,
                tt.type              as fieldType,
                t.enum_value         as enumValue,
                tt.is_in_primary_key as isPrimaryKey,
                tt.is_nullable       as isRequired,
                '-'                  as owner,
                t.is_encrypted       as isEncrypted
        from qua_web_ch_element_detail_column t
            join (select *
                    from qua_web_ch_task_result_column
                    where element_id = #{elementId}
                    and snapshoot_version = #{version}) tt
                    on t.element_id = tt.element_id and t.db_name = tt.db_name and t.table_name = tt.table_name and
                        t.column_name = tt.column_name
        where t.db_name = #{dbName}
        and t.table_name = #{tableName}
        <if test="fieldName != '' and fieldName != null">
            and t.column_name like concat('%', #{fieldName}, '%')
        </if>
        <if test="fieldType != '' and fieldType != null">
            and tt.type like concat('%', #{fieldType}, '%')
        </if>

        <if test="isPrimaryKey != null ">
            and tt.is_in_primary_key = #{isPrimaryKey}
        </if>
        <if test="isRequired != null ">
            and tt.is_nullable = #{isRequired}
        </if>
        order by t.sort
    </select>
    <select id="pageHiveDbDetail" parameterType="com.idss.datalake.datamart.dto.request.AssetDetailPageRequest" resultType="com.idss.datalake.datamart.dto.response.AssetDetailPageVo">
        select
        #{elementId} as element_id,
        'hive_table' as asset_type,
        t.db_name ,
        t.table_name ,
        t.table_name as name,
        t.table_name_cn as name_cn,
        t.id as detail_id,
        t.create_time,
        t.update_time
        from qua_web_hive_element_detail_table t
        where 1 = 1
        <if test="elementId != '' and elementId != null">
            and t.element_id = #{elementId}
        </if>
        <if test="elementIds != null and elementIds.size() > 0">
            and t.element_id IN
            <foreach item="elementId" collection="elementIds" open="(" separator="," close=")">
                #{elementId}
            </foreach>
        </if>
        <if test="dbName != '' and dbName != null">
            and t.db_name = #{dbName}
        </if>
        <if test="dbNames != null and dbNames.size() > 0">
            and t.db_name IN
            <foreach item="dbName" collection="dbNames" open="(" separator="," close=")">
                #{dbName}
            </foreach>
        </if>
        <if test="tableName != '' and tableName != null">
            and t.table_name_cn like concat ('%',#{tableName},'%')
        </if>
        <if test="startTime != null and endTime != null">
            and t.create_time <![CDATA[ >= ]]> #{startTime} and t.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        order by t.create_time desc
    </select>
    <select id="pageHiveTableDetail" parameterType="com.idss.datalake.datamart.dto.request.AssetDetailPageRequest" resultType="com.idss.datalake.datamart.dto.response.AssetDetailPageVo">
        select t.column_name        as itemName,
        t.column_name_cn     as columnNameCn,
        tt.type              as fieldType,
        t.enum_value         as enumValue,
        0 as isPrimaryKey,
        tt.is_nullable       as isRequired,
        '-'                  as owner,
        t.is_encrypted       as isEncrypted
        from qua_web_hive_element_detail_column t
        join (select *
        from qua_web_hive_task_result_column
        where element_id = #{elementId}
        and snapshoot_version = #{version}) tt
        on t.element_id = tt.element_id and t.db_name = tt.db_name and t.table_name = tt.table_name and
        t.column_name = tt.column_name
        where t.db_name = #{dbName}
        and t.table_name = #{tableName}
        <if test="fieldName != '' and fieldName != null">
            and t.column_name like concat('%', #{fieldName}, '%')
        </if>
        <if test="fieldType != '' and fieldType != null">
            and tt.type like concat('%', #{fieldType}, '%')
        </if>
        <if test="isRequired != null ">
            and tt.is_nullable = #{isRequired}
        </if>
        order by t.sort
    </select>
    <select id="pageMysqlDbDetail"  parameterType="com.idss.datalake.datamart.dto.request.AssetDetailPageRequest" resultType="com.idss.datalake.datamart.dto.response.AssetDetailPageVo">
        select
        #{elementId} as element_id,
        'mysql_table' as asset_type,
        t.db_name,
        t.table_name ,
        t.table_name as name,
        t.table_name_cn as name_cn,
        t.id as detail_id,
        t.create_time,
        t.update_time
        from qua_web_mysql_element_detail_table t
        where 1 = 1
        <if test="elementId != '' and elementId != null">
            and t.element_id = #{elementId}
        </if>
        <if test="elementIds != null and elementIds.size() > 0">
            and t.element_id IN
            <foreach item="elementId" collection="elementIds" open="(" separator="," close=")">
                #{elementId}
            </foreach>
        </if>
        <if test="dbName != '' and dbName != null">
            and t.db_name = #{dbName}
        </if>
        <if test="dbNames != null and dbNames.size() > 0">
            and t.db_name IN
            <foreach item="dbName" collection="dbNames" open="(" separator="," close=")">
                #{dbName}
            </foreach>
        </if>
        <if test="tableName != '' and tableName != null">
            and t.table_name_cn like concat ('%',#{tableName},'%')
        </if>
        <if test="startTime != null and endTime != null">
            and t.create_time <![CDATA[ >= ]]> #{startTime} and t.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        order by t.create_time desc
    </select>
    <select id="pageMysqlTableDetail" parameterType="com.idss.datalake.datamart.dto.request.AssetDetailPageRequest" resultType="com.idss.datalake.datamart.dto.response.AssetDetailPageVo">
        select t.column_name        as itemName,
        t.column_name_cn     as columnNameCn,
        tt.type              as fieldType,
        t.enum_value         as enumValue,
        0 as isPrimaryKey,
        tt.is_nullable       as isRequired,
        '-'                  as owner,
        t.is_encrypted       as isEncrypted
        from qua_web_mysql_element_detail_column t
        join (select *
        from qua_web_mysql_task_result_column
        where element_id = #{elementId}
        and snapshoot_version = #{version}) tt
        on t.element_id = tt.element_id and t.db_name = tt.db_name and t.table_name = tt.table_name and
        t.column_name = tt.column_name
        where t.db_name = #{dbName}
        and t.table_name = #{tableName}
        <if test="fieldName != '' and fieldName != null">
            and t.column_name like concat('%', #{fieldName}, '%')
        </if>
        <if test="fieldType != '' and fieldType != null">
            and tt.type like concat('%', #{fieldType}, '%')
        </if>
        <if test="isRequired != null ">
            and tt.is_nullable = #{isRequired}
        </if>
        order by t.sort
    </select>
    <select id="pageElasticsearchIndexDetail" parameterType="com.idss.datalake.datamart.dto.request.AssetDetailPageRequest" resultType="com.idss.datalake.datamart.dto.response.AssetDetailPageVo">
        select t.field_name       as itemName,
        t.field_name_cn    as columnNameCn,
        tt.field_data_type as fieldType,
        t.enum_value       as enumValue,
        0                  as isPrimaryKey,
        0                  as isRequired,
        '-'                as owner,
        t.is_encrypted     as isEncrypted
        from data_lake.qua_web_es_element_detail_field t
        join (select *
        from qua_web_es_task_result_field
        where element_id = #{elementId}
        and snapshoot_version = #{version}) tt
        on t.element_id = tt.element_id and t.index_name = tt.index_name and t.field_name = tt.field_name
        where t.index_name = #{indexName}

        <if test="fieldName != '' and fieldName != null">
            and t.field_name like concat('%', #{fieldName}, '%')
        </if>
        <if test="fieldType != '' and fieldType != null">
            and tt.field_data_type like concat('%', #{fieldType}, '%')
        </if>
        order by t.sort
    </select>

    <select id="queryElementInfo" resultType="com.idss.datalake.datamart.dto.response.ElementInfo">
        select
            t.ch_ip as "dbIp",
            t.ch_port as "dbPort",
            t.ch_user_name as "dbUserName",
            t.ch_user_password as "dbPassword",

            t.es_ip_port as "esIpPort",
            t.es_user_name as "esUserName",
            t.es_user_password as "esPassword",

            t.kbs_enable as "kbsEnable",
            t.key_tab_path as "keyTabPath",
            t.krb5_conf_path as "krb5ConfPath",
            t.jdbc_url as "jdbcUrl"
        from qua_wab_element t where t.id = #{elementId}
    </select>

    <select id="sensitiveDataCount" resultType="long">
        SELECT COUNT(*) FROM ${tableName} WHERE is_sensitive = 1
        AND id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="assetMapTypeList" parameterType="com.idss.datalake.datamart.dto.request.AssetMapGroupRequest" resultType="com.idss.datalake.datamart.dto.response.AssetMapGroupVo">
        select t.group_id,
               t.group_name,
               count(t.id) cnt
        from asset_map_view t
        where t.type_id = #{typeId}
          <if test="searchName != null and searchName != ''">
              and (t.data_name like concat('%',#{searchName},'%') or t.table_name_cn like concat('%',#{searchName},'%') or t.asset_type like concat('%',#{searchName},'%') or t.key_words like concat('%',#{searchName},'%'))
          </if>
        <if test="subscribeType != null">
            <choose>
                <when test="subscribeType == 0">
                    and t.id not in (select asset_id from data_mart_subscribe where tenant_id = #{tenantId} and user_id = #{userId})
                </when>
                <otherwise>
                    and t.id in (select asset_id from data_mart_subscribe where tenant_id =  #{tenantId} and user_id = #{userId})
                </otherwise>
            </choose>
        </if>
        group by t.group_id, t.group_name
    </select>
    <select id="assetMapPage" parameterType="com.idss.datalake.datamart.dto.request.AssetMapGroupRequest" resultType="com.idss.datalake.datamart.dto.response.AssetMapView">
        select *
        from asset_map_view t
        where 1 = 1
        <if test="typeId != null">
            and t.type_id = #{typeId}
        </if>
        <if test="groupId != null">
            and t.group_id = #{groupId}
        </if>
        <if test="searchName != null and searchName != ''">
            and (t.data_name like concat('%',#{searchName},'%') or t.table_name_cn like concat('%',#{searchName},'%') or t.asset_type like concat('%',#{searchName},'%') or t.key_words like concat('%',#{searchName},'%'))
        </if>
        <if test="subscribeType != null">
            <choose>
                <when test="subscribeType == 0">
                    and t.id not in (select asset_id from data_mart_subscribe where tenant_id = #{tenantId} and user_id = #{userId})
                </when>
                <otherwise>
                    and t.id in (select asset_id from data_mart_subscribe where tenant_id =  #{tenantId} and user_id = #{userId})
                </otherwise>
            </choose>
        </if>
    </select>

</mapper>
