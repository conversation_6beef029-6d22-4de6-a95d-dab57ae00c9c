<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datamart.mapper.DataMartCartMapper">

    <select id="listDataMartAsset" resultType="java.util.Map">
        select id,data_name,table_name_cn from asset_map_view where 1=1

        <if test="ids != null and ids.size() > 0">
            and id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
