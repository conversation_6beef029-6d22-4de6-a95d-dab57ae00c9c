<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datamart.mapper.DataMartTypeMapper">

    <select id="page" parameterType="com.idss.datalake.datamart.dto.request.DataMartTypePageRequest"
            resultType="com.idss.datalake.datamart.entity.DataMartType">
        select * from data_mart_type where tenant_id = #{tenantId}
        <if test="typeName != null">
            and type_name like concat('%',#{typeName},'%')
        </if>
        <if test="createUser != null">
            and create_user like concat('%',#{createUser},'%')
        </if>
        order by create_time desc
    </select>
</mapper>
