<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.datamart.mapper.DataMartAccessMapper">

    <select id="dataTypeStatistic" 
            resultType="map">
          select dma.name,dma.status,dms.data_child_type,dms.count from data_mart_access_data_type dma left join data_mart_data_type_statistic dms
	on dma.id = dms.data_type_id order by dms.create_time desc;
    </select>
    
    <select id="etlSourceDataStatistic" parameterType="map"
            resultType="map">
        select id,source_name,to_char(create_date,'YYYY-MM-DD HH:MI:SS') as create_date,source_switch from etl_source
		WHERE 
		status = 1
		<if test="timeflg =='last7days'">
		AND create_date BETWEEN TO_CHAR(CURRENT_DATE - INTERVAL '6 days', 'YYYY-MM-DD 00:00:00') AND now()
		          AND now() 
		</if>
		<if test="timeflg =='last30days'">
		AND create_date BETWEEN TO_CHAR(CURRENT_DATE - INTERVAL '29 days', 'YYYY-MM-DD 00:00:00') AND now()
		          AND now() 
		</if>
		<if test="timeflg =='last90days'">
		AND create_date BETWEEN TO_CHAR(CURRENT_DATE - INTERVAL '89 days', 'YYYY-MM-DD 00:00:00') AND now()
		          AND now() 
		</if>
		<if test="timeflg =='last1years'">
		AND create_date BETWEEN TO_CHAR((CURRENT_DATE - INTERVAL '1 year' + INTERVAL '1 day'), 'YYYY-MM-DD 00:00:00') AND now()
		          AND now() 
		</if>
		order by create_date desc;
    </select>
    
    <select id="etlSourceAppendixQuery"
            resultType="map">
        select * from data_mart_etl_source_appendix 
    </select>
</mapper>
