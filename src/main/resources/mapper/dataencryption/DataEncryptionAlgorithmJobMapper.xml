<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.dataencryption.mapper.DataEncryptionAlgorithmJobMapper">

    <select id="queryPage" resultType="com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJob">
        select * from data_encryption_algorithm_job where tenant_id = #{tenantId}
        <if test="jobName != null and jobName !=''">
            and job_name like concat('%',#{jobName},'%')
        </if>
        order by update_time desc
    </select>
</mapper>
