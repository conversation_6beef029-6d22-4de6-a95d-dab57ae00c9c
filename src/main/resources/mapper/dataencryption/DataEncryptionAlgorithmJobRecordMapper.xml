<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.dataencryption.mapper.DataEncryptionAlgorithmJobRecordMapper">

    <select id="queryPage"
            resultType="com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithmJobRecord">
        select * from data_encryption_algorithm_job_record where job_id = #{jobId}
        <if test="fieldName != null and fieldName !=''">
            and field_name like concat('%',#{fieldName},'%')
        </if>
    </select>
</mapper>
