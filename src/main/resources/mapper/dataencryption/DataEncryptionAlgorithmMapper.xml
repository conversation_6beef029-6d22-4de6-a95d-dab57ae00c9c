<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.idss.datalake.dataencryption.mapper.DataEncryptionAlgorithmMapper">

    <select id="queryPage" resultType="com.idss.datalake.dataencryption.entity.DataEncryptionAlgorithm">
        select * from data_encryption_algorithm where tenant_id = #{tenantId}
        <if test="keyName != null and keyName !=''">
            and key_name like concat('%',#{keyName},'%')
        </if>
        order by update_time desc
    </select>
</mapper>
