# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import json
from statsmodels.tsa.arima.model import ARIMA
import sys
import argparse

def load_data_from_json(json_data):
    # 解析 JSON 数据并转换为 DataFrame
    data_dict = json.loads(json_data)
    dates = pd.to_datetime(data_dict['date'])
    values = data_dict['data']
    data = pd.DataFrame({'value': values}, index=dates)
    return data

def fit_arima(series, p, d, q):
    model = ARIMA(series, order=(p, d, q))
    fitted_model = model.fit()
    return fitted_model

def forecast(model, steps):
    forecast = model.forecast(steps=steps)
    return forecast

def main(args):
    # 从标准输入读取 JSON 数据
    input_json = sys.stdin.read()
    data = load_data_from_json(input_json)
    time_series = data['value']
    
    p = args.p
    d = args.d
    q = args.q
    
    fitted_model = fit_arima(time_series, p, d, q)
    
    forecast_steps = args.steps
    forecast_values = forecast(fitted_model, forecast_steps)
    forecast_index = pd.date_range(start=time_series.index[-1] + pd.Timedelta(days=1), periods=forecast_steps, freq='D')
    forecast_series = pd.Series(forecast_values, index=forecast_index)
    
    # 将日期格式化为 'yyyy-MM-dd'
    forecast_series.index = forecast_series.index.strftime('%Y-%m-%d')
    
    # 输出预测值为 JSON
    result = {
            'forecast': forecast_series.to_json()
    }
    print(result)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='ARIMA Time Series Forecasting')
    parser.add_argument('--p', type=int, default=1, help='AR parameter')
    parser.add_argument('--d', type=int, default=1, help='Differencing order')
    parser.add_argument('--q', type=int, default=1, help='MA parameter')
    parser.add_argument('--steps', type=int, default=10, help='Number of steps to forecast')
    
    args = parser.parse_args()
    main(args)
