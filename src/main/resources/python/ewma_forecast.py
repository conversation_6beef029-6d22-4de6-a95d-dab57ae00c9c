# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import json
import argparse
import sys

def load_data_from_json(json_data):
    # 解析 JSON 数据并转换为 DataFrame
    data_dict = json.loads(json_data)
    dates = pd.to_datetime(data_dict['date'])
    values = data_dict['data']
    data = pd.DataFrame({'value': values}, index=dates)
    return data

def ewma_forecast(series, alpha, forecast_steps):
    # 计算 EWMA
    ewma_series = series.ewm(alpha=alpha).mean()
    
    # 预测未来的值
    forecast_values = [ewma_series.iloc[-1]] * forecast_steps
    forecast_index = pd.date_range(start=series.index[-1] + pd.Timedelta(days=1), periods=forecast_steps, freq='D')
    forecast_series = pd.Series(forecast_values, index=forecast_index)
    
    return ewma_series, forecast_series

def main(args):
    # 从标准输入读取 JSON 数据
    input_json = sys.stdin.read()
    data = load_data_from_json(input_json)
    time_series = data['value']
    
    alpha = args.alpha
    forecast_steps = args.steps
    
    ewma_series, forecast_series = ewma_forecast(time_series, alpha, forecast_steps)
    
    # 将日期格式化为 'yyyy-MM-dd'
    ewma_series.index = ewma_series.index.strftime('%Y-%m-%d')
    forecast_series.index = forecast_series.index.strftime('%Y-%m-%d')
    
    # 输出 EWMA 和预测值为 JSON
    result = {
    #    'ewma': ewma_series.to_dict(),
        'forecast': forecast_series.to_dict()
    }
    print(json.dumps(result))

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='EWMA Time Series Forecasting')
    parser.add_argument('--alpha', type=float, default=0.3, help='Smoothing factor')
    parser.add_argument('--steps', type=int, default=10, help='Number of steps to forecast')
    
    args = parser.parse_args()
    main(args)
