INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '元数据分值计算配置', 'meta-weight', NULL, '0', '0', 'data-lake-governance-meta', '1', 60, 60, '元数据分值计算配置', '1', 'data-lake-governance-meta', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '字典总览', 'dict-general-view', NULL, '0', '0', 'dict', '1', 50, 50, '字典总览', '1', 'dict', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0', '0', 0);
UPDATE ums_sys_menus SET hidden='1'	WHERE menu_code='business-process';
UPDATE ums_sys_menus SET menu_code='business-segments' WHERE menu_code='business-sector';

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '血缘查询', 'data-lake-governance-meta-blood-search', NULL, '0', '0', 'data-lake-governance-meta', '1', 30, 30, '血缘查询', '1', 'data-lake-governance-meta', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0', '0', 0);
