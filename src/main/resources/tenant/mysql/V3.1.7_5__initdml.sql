UPDATE ums_sys_menus SET menu_name='API首页',default_name='API首页',menu_order=0,default_order=0,parent_name = 'manage-api',default_parent='manage-api' WHERE menu_code = 'data-api-home';

UPDATE ums_sys_menus SET parent_name='dataMart',default_name='dataMart',root_parent='data',application='data',menu_order=50,default_order=50 WHERE menu_code = 'ai_bi_dashboard';
UPDATE ums_sys_menus SET parent_name='dataMart',default_name='dataMart',root_parent='data',application='data' WHERE menu_code = 'ai_dashboard';
UPDATE ums_sys_menus SET parent_name='dataMart',default_name='dataMart',root_parent='data',application='data' WHERE menu_code = 'ai_widget_editor';
