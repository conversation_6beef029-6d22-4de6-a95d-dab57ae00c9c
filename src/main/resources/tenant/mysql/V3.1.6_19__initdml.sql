UPDATE ums_sys_menus SET menu_code='data-lake-governance-standard-manage' WHERE menu_code='data-lake-governance-standard';

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '标准管理', 'data-lake-governance-standard', NULL, '0', '0', 'data-lake-governance-standard-manage', '1', 1, 10, '标准管理', '1', 'data-lake-governance-standard-manage', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '发布申请', 'data-lake-governance-release-application', NULL, '0', '0', 'data-lake-governance-standard-manage', '1', 1, 20, '发布申请', '1', 'data-lake-governance-standard-manage', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '发布审批', 'data-lake-governance-release-approval', NULL, '0', '0', 'data-lake-governance-standard-manage', '1', 1, 30, '发布审批', '1', 'data-lake-governance-standard-manage', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '版本恢复', 'data-lake-governance-release-recovery', NULL, '0', '0', 'data-lake-governance-standard-manage', '1', 1, 40, '版本恢复', '1', 'data-lake-governance-standard-manage', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');

UPDATE ums_sys_role_function SET function_id = 'data-lake-governance-standard-manage' WHERE function_id = 'data-lake-governance-standard';