INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 2, '数据标签', 'tag-management', NULL, '0', '0', 'data-lake-governance', '1', 80, 80, '数据标签', '1', 'data-lake-governance', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 4, '标签类型管理', 'tag-type-management', NULL, '0', '0', 'tag-management', '1', 1, 40, '标签类型管理', '1', 'tag-management', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 4, '标签值管理', 'tag-value-management', NULL, '0', '1', 'tag-management', '1', 1, 40, '标签值管理', '1', 'tag-management', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '表指标项查询概览', 'dict-indicator-items', NULL, '0', '0', 'dict', '1', 30, 30, '表指标项查询概览', '1', 'dict', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '指标项明细', 'dict-indicator-items-detail', NULL, '0', '1', 'dict-indicator-items', '1', 10, 10, '指标项明细', '1', 'dict-indicator-items', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0', '0', 0);