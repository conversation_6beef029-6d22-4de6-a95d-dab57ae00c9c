CREATE TABLE  IF NOT EXISTS `ums_sys_application`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `name`        varchar(100) DEFAULT NULL COMMENT '应用名称(中文)',
    `code`        varchar(100) DEFAULT NULL COMMENT '应用编码(英文)',
    `type`        varchar(100) DEFAULT NULL COMMENT '应用类型',
    `is_active`   varchar(1)   DEFAULT '0' COMMENT '是否启用（0-不启用 1-启用）',
    `sso`         varchar(100) DEFAULT NULL COMMENT '单点登录',
    `app_desc`    varchar(100) DEFAULT NULL COMMENT '应用标识',
    `is_edit`     varchar(1)   DEFAULT NULL COMMENT '是否可编辑（0-不可编辑 1-可编辑）',
    `app_release` varchar(100) DEFAULT NULL COMMENT '版本号',
    `app_path`    varchar(255) DEFAULT NULL,
    `app_owner`   varchar(64)  DEFAULT NULL,
    `create_time` datetime     DEFAULT NULL,
    `version`     varchar(100) DEFAULT NULL COMMENT 'build号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

alter table `ums_sys_menus` add `application_code` varchar(100)  ;
alter table `ums_sys_menus` add `application` varchar(100)  ;
alter table `ums_sys_menus` add `level1` varchar(100)  ;
