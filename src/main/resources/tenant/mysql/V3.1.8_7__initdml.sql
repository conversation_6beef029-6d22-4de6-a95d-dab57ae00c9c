DELETE FROM ums_sys_menus WHERE menu_code IN ('layout-quality-report','quality-weight');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '质量报告', 'layout-quality-report', null, '0', '0', 'data-lake-governance-quality', '1', 20, 20, '质量报告', '1', 'data-lake-governance-quality', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ( 'builtIn', '1', 3, '数据元标准', 'data-lake-governance-meta-standard', null, '0', '0', 'data-lake-governance-standard-manage', '1', 1, 10, '数据元标准', '1', 'data-lake-governance-standard-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);

DELETE FROM ums_sys_menus WHERE menu_code IN ('quality-warn-platform-rule','quality-warn-platform-rule-create','quality-system-notice','quality-report');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '告警信息', 'quality-system-notice', null, '0', '0', 'layout-quality-report', '1', 20, 20, '告警信息', '1', 'layout-quality-report', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '告警规则管理', 'quality-warn-platform-rule', null, '0', '0', 'layout-quality-report', '1', 30, 30, '告警规则管理', '1', 'layout-quality-report', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '告警规则创建', 'quality-warn-platform-rule-create', null, '0', '0', 'quality-warn-platform-rule', '1', 10, 10, '告警规则创建', '1', 'quality-warn-platform-rule', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '质量报告', 'quality-report', null, '0', '0', 'layout-quality-report', '1', 10, 10, '质量报告', '1', 'layout-quality-report', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);

update ums_sys_menus set menu_name = '数据源管理' where menu_code = 'data-lake-governance-quality-model';
delete from ums_sys_menus where menu_code = 'data-lake-governance-rule-manage';
update ums_sys_menus set menu_name = '质量任务' where menu_code = 'data-lake-governance-quality-task';
update ums_sys_menus set menu_order = 10,default_order=10 where menu_code = 'layout-quality-report';
update ums_sys_menus set menu_order = 20,default_order=20 where menu_code = 'data-lake-governance-quality-task';
update ums_sys_menus set menu_order = 30,default_order=30 where menu_code = 'data-lake-governance-quality-rule';
update ums_sys_menus set menu_order = 40,default_order=40 where menu_code = 'quality-template';
update ums_sys_menus set menu_order = 50,default_order=50 where menu_code = 'data-lake-governance-quality-model';

update ums_sys_menus set menu_name  ='数据标准',default_name='数据标准' where menu_code = 'data-lake-governance-standard-manage';
update ums_sys_menus set menu_name  ='数据标准',default_name='数据标准',menu_code = 'layout-standard-manage' where menu_code = 'data-lake-governance-standard';
delete from ums_sys_menus where menu_code in( 'standard-dict','data-lake-governance-standard');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据字典', 'standard-dict', null, '0', '0', 'data-lake-governance-standard-manage', '1', 50, 10, '数据字典', '1', 'data-lake-governance-standard-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus ( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ( 'builtIn', '1', 3, '基础数据标准', 'data-lake-governance-standard', null, '0', '0', 'layout-standard-manage', '1', 50, 10, '基础数据标准', '1', 'layout-standard-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
update ums_sys_menus set parent_name = 'layout-standard-manage',default_parent = 'layout-standard-manage' where menu_code in('standard-dict','data-lake-governance-meta-standard','data-lake-governance-standard');

delete from ums_sys_menus where menu_code in( 'quality-template','built-in-custom-template','dynamic-threshold');
INSERT INTO ums_sys_menus ( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ( 'builtIn', '1', 3, '质量模版', 'quality-template', null, '0', '0', 'data-lake-governance-quality', '1', 20, 20, '质量模版', '1', 'data-lake-governance-quality', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ( 'builtIn', '1', 4, '模板管理', 'built-in-custom-template', '', '0', '0', 'quality-template', '1', 1, 10, '模板管理', '1', 'quality-template', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ( 'builtIn', '1', 4, '动态阈值', 'dynamic-threshold', '', '0', '0', 'quality-template', '1', 1, 10, '动态阈值', '1', 'quality-template', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);

