INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 2, '数据字典', 'dict', NULL, '0', '0', 'data-lake-governance', '1', 80, 80, '数据字典', '1', 'data-lake-governance', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '字典概览', 'dict-overview', NULL, '0', '0', 'dict', '1', 1, 10, '字典概览', '1', 'dict', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '全部数据', 'dict-search', NULL, '0', '0', 'dict', '1', 1, 20, '全部数据', '1', 'dict', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '字典目录', 'dict-dir', NULL, '0', '0', 'dict', '1', 1, 30, '字典目录', '1', 'dict', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 4, '字典目录详情', 'dict-dir-detail', NULL, '0', '1', 'dict-dir', '1', 1, 40, '字典目录详情', '1', 'dict-dir', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');

DELETE FROM etl_source_type WHERE source_type = 'ROCKETMQ';
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('ROCKETMQ', 'RocketMQ', '分布式消息系统', '协议加载', 11, 2, NULL, 1, NULL, NULL, NULL, NULL);

CREATE TABLE IF NOT EXISTS intranet_configuration (
    id               bigint(20) NOT NULL AUTO_INCREMENT,
    safe_domain_type text         null comment '类型,json格式',
    origin_param     text         null comment '原始数据类型',
    is_del           tinyint(1)   null comment '是否删除,1是,0否',
    create_time      datetime     null comment '创建时间',
    create_user      varchar(128) null comment '创建人',
    update_time      datetime     null comment '更新时间',
    update_user      varchar(128) null comment '更新人',
    member           longtext     null comment 'IP集合',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资产安全域';