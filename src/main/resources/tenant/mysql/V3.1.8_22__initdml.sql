CREATE TABLE if not exists `etl_config`  (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT,
                               `read_conf` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '读源配置',
                               `structured_conf` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '结构化配置，sql采集忽略',
                               `expansion_conf` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '扩展化配置',
                               `load_conf` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '写入配置',
                               `example_json` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '原始数据',
                               `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
                               `create_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
                               `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                               `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                               `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                               `data_source` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据来源',
                               `source_type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接入方式',
                               `status` int(11) NOT NULL COMMENT '状态（0 表示禁用，1表示启用）',
                               `update_user` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
                               `copy_cnt` int(11) NOT NULL DEFAULT 0 COMMENT '复制次数',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;