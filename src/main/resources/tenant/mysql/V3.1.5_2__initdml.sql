INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '2', '系统日志', 'sys-log', NULL, '0', '0', 'system', '1', 40, 40, '系统日志', '1', 'system', NULL, NULL, NULL, NULL, 'system');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '3', '操作审计', 'operation-audit', NULL, '0', '0', 'sys-log', '1', 1, 1, '操作审计', '1', 'sys-log', NULL, NULL, NULL, NULL, 'system');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '2', 'EasyGo', 'easygo', '', '0', '0', 'system', '1', 80, 80, 'EasyGo', '1', 'system', NULL, NULL, NULL, NULL, 'system');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '1', '首页', 'data-lake-home-tenant', NULL, '0', '0', NULL, '1', 10, 10, '首页', '1', NULL, NULL, NULL, NULL, NULL, 'data-lake-home-tenant');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '3', '规则管理', 'data-lake-governance-rule-manage', NULL, '0', '0', 'data-lake-governance-quality', '1', '40', '40', '规则管理', '1', 'data-lake-governance-quality', NULL, NULL, NULL, NULL, 'data-lake-governance');


INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, `type`, parent_id) VALUES('query', '查询', 1, NULL, 26, 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, `type`, parent_id) VALUES('execute', '执行', 1, NULL, 27, 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, `type`, parent_id) VALUES('freeze', '冻结和解冻', 1, NULL, 28, 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, `type`, parent_id) VALUES('restore', '恢复', 1, NULL, 29, 'SYSTEM_LOG_OPT_TYPE', NULL);