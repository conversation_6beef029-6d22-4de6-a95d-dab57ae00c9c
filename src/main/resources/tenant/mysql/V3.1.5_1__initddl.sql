CREATE TABLE IF NOT EXISTS `asset_net_district`
(
    `id`           bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`         varchar(256)        DEFAULT NULL COMMENT '网络域名称',
    `ip_location`  varchar(256)        DEFAULT NULL COMMENT '物理位置',
    `ip_section`   varchar(256)        DEFAULT NULL COMMENT 'ip范围',
    `net_describe` text COMMENT '描述',
    `create_time`  datetime   NOT NULL COMMENT '创建时间',
    `create_user`  varchar(256)        DEFAULT NULL COMMENT '创建者',
    `update_time`  datetime            DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user`  varchar(256)        DEFAULT NULL COMMENT '更新者',
    `flag`         int(1)     NOT NULL DEFAULT '1' COMMENT '有效标识（1-有效；0-无效）',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='网络域';

-- 资产目录表
CREATE TABLE IF NOT EXISTS `etl_asset_resource`
(
    `id`                int(11)    NOT NULL AUTO_INCREMENT,
    `etl_source_id`     bigint(20) NOT NULL COMMENT '数据源ID',
    `en_name`           varchar(100)  DEFAULT NULL COMMENT '资源英文名',
    `asset_purpose_id`  bigint(20)    DEFAULT NULL COMMENT '资产用途，字典表id',
    `classification_id` bigint(20)    DEFAULT NULL COMMENT '敏感分类，字典表id',
    `asset_level_id`    bigint(20)    DEFAULT NULL COMMENT '敏感分级，字典表id',
    `description`       varchar(1000) DEFAULT NULL COMMENT '资源描述',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `create_user`       varchar(255)  DEFAULT NULL COMMENT '创建人',
    `update_time`       datetime      DEFAULT NULL COMMENT '更新时间',
    `update_user`       varchar(255)  DEFAULT NULL COMMENT '更新人',
    `del_flag`          int(11)       DEFAULT '0' COMMENT '是否已删除，0否，1是',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2
  DEFAULT CHARSET = utf8mb4 COMMENT ='资产目录';

-- 资产汇总
CREATE TABLE `etl_asset_summary`
(
    `id`                    int(11) NOT NULL AUTO_INCREMENT,
    `asset_total`           int(11)    DEFAULT NULL,
    `asset_purpose_json`    text COMMENT '资产用途',
    `device_type_json`      text COMMENT '资产类型',
    `data_total_all`        int(11)    DEFAULT NULL COMMENT '数据累计总数量',
    `disk_all`              bigint(20) DEFAULT NULL COMMENT '占用磁盘总空间',
    `last_7days_data_total` int(11)    DEFAULT NULL COMMENT '近7日新增数据量',
    `last_7days_disk`       bigint(20) DEFAULT NULL COMMENT '近7日新增使用磁盘空间',
    `asset_list_json`       text COMMENT '资产数据量',
    `last_7days_asset_json` text COMMENT '近7日新增资产数量排行',
    `disk_total_list_json`  text COMMENT '资产磁盘空间占比',
    `del_flag`              int(11)    DEFAULT '0' COMMENT '是否已删除，0否1是',
    `create_time`           datetime   DEFAULT NULL COMMENT '入库时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 11
  DEFAULT CHARSET = utf8 COMMENT ='资产汇总';