CREATE TABLE IF NOT EXISTS `early_warn_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `classification` varchar(64) DEFAULT NULL COMMENT '预警分类',
  `warn_level` varchar(64) DEFAULT NULL COMMENT '预警级别',
  `warn_name` varchar(128) DEFAULT NULL COMMENT '预警名称',
  `warn_time` datetime DEFAULT NULL COMMENT '预警时间',
  `warn_target` varchar(64) DEFAULT NULL COMMENT '预警对象',
  `department` varchar(64) DEFAULT NULL COMMENT '预警部门',
  `warn_content` varchar(512) DEFAULT NULL COMMENT '预警内容',
  `source_type` varchar(32) DEFAULT NULL COMMENT '来源类型',
  `warn_source` varchar(32) DEFAULT NULL COMMENT '预警来源',
  `warn_status` varchar(32) DEFAULT NULL COMMENT '预警状态',
  `cve_id` varchar(64) DEFAULT NULL COMMENT 'CVEID',
  `bug_type` varchar(32) DEFAULT NULL COMMENT '漏洞类型',
  `attack_type` varchar(32) DEFAULT NULL COMMENT '攻击类型',
  `warn_effect` varchar(512) DEFAULT NULL COMMENT '预警后果',
  `warn_solution` varchar(512) DEFAULT NULL COMMENT '预警解决方案',
  `warn_file_path` varchar(1024) DEFAULT NULL COMMENT '创建预警上传文件',
  `device_ref` varchar(512) DEFAULT NULL COMMENT '关联设备',
  `service_def` varchar(512) DEFAULT NULL COMMENT '关联服务',
  `audit_submit_user` varchar(128) DEFAULT NULL COMMENT '提交审核人',
  `audit_submit_time` datetime DEFAULT NULL COMMENT '提交审核时间',
  `audit_submit_result` varchar(512) DEFAULT NULL COMMENT '提交审核原因',
  `audit_submit_file_path` varchar(1024) DEFAULT NULL COMMENT '提交审核附件路径',
  `audit_user` varchar(128) DEFAULT NULL COMMENT '审核人',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_result` varchar(128) DEFAULT NULL COMMENT '审核结果',
  `audit_desc` varchar(512) DEFAULT NULL COMMENT '审核描述',
  `audit_file_path` varchar(1024) DEFAULT NULL COMMENT '审核附件路径',
  `release_user` varchar(128) DEFAULT NULL COMMENT '发布人',
  `release_time` datetime DEFAULT NULL COMMENT '发布时间',
  `type` varchar(8) DEFAULT NULL COMMENT '类型',
  `start_time` varchar(32) DEFAULT NULL COMMENT '开始时间',
  `sync_id` varchar(64) DEFAULT NULL COMMENT '预警id',
  `doc_url` varchar(256) DEFAULT NULL COMMENT '预警通报报告',
  `warn_flag` varchar(2) DEFAULT NULL COMMENT '预警删除状态(0-删除 1-正常)',
  `create_user` varchar(64) DEFAULT NULL COMMENT '过滤条件所属用户',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(64) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预警信息表';

CREATE TABLE IF NOT EXISTS `early_warn_opt_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `early_warn_id` bigint(20) NOT NULL COMMENT '预警ID',
  `opt_user` varchar(128) DEFAULT NULL COMMENT '操作人',
  `opt_time` datetime DEFAULT NULL COMMENT '操作时间',
  `opt_type` varchar(128) DEFAULT NULL COMMENT '操作类型',
  `opt_content` varchar(512) DEFAULT NULL COMMENT '操作说明',
  `create_user` varchar(64) DEFAULT NULL COMMENT '过滤条件所属用户',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(64) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预警规则操作记录表';


CREATE TABLE IF NOT EXISTS `early_warn_agg` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `agg_field` varchar(128) DEFAULT NULL COMMENT '汇总列名',
  `agg_name` varchar(128) DEFAULT NULL COMMENT '汇总项名称',
  `dict_type` varchar(64) DEFAULT NULL COMMENT '字典项',
  `enum_type` varchar(512) DEFAULT NULL COMMENT '枚举项',
  `create_user` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(64) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='预警规则汇总项';

