INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version) VALUES ('AI数据分析', 'ai-data-analysis', null, '1', null, '1', null, null, null, null, null, null);

INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '1', 'AI数据分析', 'ai-data-analysis', NULL, '0', '0', NULL, '1', '100', '100', 'AI数据分析', '1', NULL, NULL, NULL, NULL, NULL, 'ai-data-analysis', NULL, 'ai-data-analysis', '1');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '我的工程', 'ai-self-project', null, '1', '0', 'ai-data-analysis', '1', 10, 10, '我的工程', '1', 'ai-data-analysis', null, null, 'sysadmin', '2021-12-01 10:13:50', 'ai-data-analysis', null, 'ai-data-analysis', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '数据源管理', 'ai-home', null, '1', '0', 'ai-data-analysis', '1', 20, 20, '数据源管理', '1', 'ai-data-analysis', null, null, 'sysadmin', '2021-12-01 10:13:50', 'ai-data-analysis', null, 'ai-data-analysis', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '系统算法', 'ai-algorithm', null, '1', '0', 'ai-data-analysis', '1', 30, 30, '系统算法', '1', 'ai-data-analysis', null, null, 'sysadmin', '2021-12-01 10:13:50', 'ai-data-analysis', null, 'ai-data-analysis', '0');

UPDATE ums_sys_menus SET menu_name = '主机监控' WHERE menu_code = 'master-monitor';
UPDATE ums_sys_menus SET menu_name = '工作组监控' WHERE menu_code = 'worker-monitor';
UPDATE ums_sys_menus SET menu_name = '数据库监控' WHERE menu_code = 'db-monitor';
UPDATE ums_sys_menus SET menu_name = '监控统计' WHERE menu_code = 'statistics-monitor';

INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application`, `level1`) VALUES ('builtIn', '1', '3', '账号权限', 'account-right', NULL, '0', '0', 'system', '1', '1', '1', '账号权限', '1', 'system', NULL, NULL, NULL, NULL, 'application', 'application', '0');
UPDATE ums_sys_menus SET parent_name = 'account-right',default_parent = 'account-right' WHERE menu_code = 'system-account';
UPDATE ums_sys_menus SET parent_name = 'account-right',default_parent = 'account-right' WHERE menu_code = 'role';
UPDATE ums_sys_menus SET parent_name = 'account-right',default_parent = 'account-right' WHERE menu_code = 'menu-manage';
UPDATE ums_sys_menus SET parent_name = 'account-right',default_parent = 'account-right' WHERE menu_code = 'password-strategy';
UPDATE ums_sys_menus SET parent_name = 'config-manage',default_parent = 'config-manage' WHERE menu_code = 'system-config-logo';

INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application`, `level1`) VALUES ('builtIn', '1', '3', '申诉管理', 'appeal', NULL, '0', '0', 'system', '1', '20', '20', '申诉管理', '1', 'system', NULL, NULL, NULL, NULL, 'application', 'application', '0');
UPDATE ums_sys_menus SET parent_name = 'appeal',default_parent = 'appeal' WHERE menu_code = 'appeal-manage';
UPDATE ums_sys_menus SET parent_name = 'appeal',default_parent = 'appeal' WHERE menu_code = 'appeal-self';

UPDATE ums_sys_menus SET menu_order=30,default_order=30 WHERE menu_code = 'config-manage';
UPDATE ums_sys_menus SET menu_order=40,default_order=40 WHERE menu_code = 'base-dictionary';
UPDATE ums_sys_menus SET menu_order=50,default_order=50 WHERE menu_code = 'sys-log';



