INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application`, `level1`) VALUES
('builtIn', '1', '2', '告警规则', 'warn', NULL, '0', '0', 'data-input-config', '1', '80', '80', '告警规则', '1', 'data-input-config', NULL, NULL, NULL, NULL, 'insight', 'insight', '0'),
('builtIn', '1', '3', '平台告警规则管理', 'warn-platform-rule', NULL, '0', '0', 'warn', '1', '20', '20', '平台告警规则管理', '1', 'warn', NULL, NULL, NULL, NULL, 'insight', 'insight', '0'),
('builtIn', '1', '4', '平台告警规则配置', 'warn-platform-rule-create', NULL, '0', '0', 'warn-platform-rule', '1', '1', '1', '平台告警规则配置', '1', 'warn-platform-rule', NULL, NULL, NULL, NULL, 'insight', 'insight', '0'),
('builtIn', '1', '3', '系统告警', 'system-notice', NULL, '0', '0', 'warn', '1', '30', '30', '系统告警', '1', 'warn', NULL, NULL, NULL, NULL, 'insight', 'insight', '0');


INSERT INTO `ums_sys_role_function` (`role_id`, `function_id`,`value`) VALUES ('140d6cf17d244e74855ecdd56c40335c', 'warn','15');
INSERT INTO `ums_sys_role_function` (`role_id`, `function_id`,`value`) VALUES ('140d6cf17d244e74855ecdd56c40335c', 'warn-platform-rule','15');
INSERT INTO `ums_sys_role_function` (`role_id`, `function_id`,`value`) VALUES ('140d6cf17d244e74855ecdd56c40335c', 'warn-platform-rule-create', '15');
INSERT INTO `ums_sys_role_function` (`role_id`, `function_id`,`value`) VALUES ('140d6cf17d244e74855ecdd56c40335c', 'system-notice','15');

INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application`, `level1`) VALUES
('builtIn', '1', '3', '脚本管理', 'shell-manage', NULL, '0', '0', 'warn', '1', '30', '30', '脚本管理', '1', 'warn', NULL, NULL, NULL, NULL, 'insight', 'insight', '0'),
('builtIn', '1', '4', '新建脚本', 'operate-shell-create', NULL, '0', '0', 'shell-manage', '1', '10', '10', '新建脚本', '1', 'shell-manage', NULL, NULL, NULL, NULL, 'insight', 'insight', '0'),
('builtIn', '1', '4', '编辑脚本', 'operate-shell-edit', NULL, '0', '0', 'shell-manage', '1', '20', '20', '编辑脚本', '1', 'shell-manage', NULL, NULL, NULL, NULL, 'insight', 'insight', '0'),
('builtIn', '1', '4', '查看脚本', 'operate-shell-detail', NULL, '0', '0', 'shell-manage', '1', '30', '30', '编辑脚本', '1', 'shell-manage', NULL, NULL, NULL, NULL, 'insight', 'insight', '0');

INSERT INTO `ums_sys_role_function` (`role_id`, `function_id`,`value`) VALUES ('140d6cf17d244e74855ecdd56c40335c', 'shell-manage','15');
INSERT INTO `ums_sys_role_function` (`role_id`, `function_id`,`value`) VALUES ('140d6cf17d244e74855ecdd56c40335c', 'operate-shell-create','15');
INSERT INTO `ums_sys_role_function` (`role_id`, `function_id`,`value`) VALUES ('140d6cf17d244e74855ecdd56c40335c', 'operate-shell-edit', '15');
INSERT INTO `ums_sys_role_function` (`role_id`, `function_id`,`value`) VALUES ('140d6cf17d244e74855ecdd56c40335c', 'operate-shell-detail','15');


INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '3', '密码策略', 'password-strategy', NULL, '0', '0', 'system', '1', '55', '55', '密码策略', '1', 'system', NULL, NULL, NULL, NULL, 'application', NULL, 'application', '0');

INSERT INTO ums_sys_role_function(role_id, function_id, value) VALUES ( '140d6cf17d244e74855ecdd56c40335c', 'password-strategy', '15');

DELETE FROM etl_writer_ch_config WHERE source_id = 0;
INSERT INTO `etl_writer_ch_config` (`source_id`, `reg_value`, `time_format`, `table_name`, `order_fields`, `cluster_display_name`, `shard_num`, `replica_num`, `max_capacity`, `max_keep_days`, `strategy`, `last_backup_index_name`, `failed_index_name`, `cycle_scop`, `status`, `view_id`, `model_view`) VALUES (0, NULL, NULL, 'security_log', 'generic_create_time', 'cluster_nx_copy', NULL, NULL, NULL, 90, NULL, NULL, NULL, NULL, 1, NULL, NULL);

INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '3', '基础字典', 'base-dictionary', NULL, '0', '0', 'system', '1', '60', '60', '基础字典', '1', 'data-dictionary', NULL, NULL, NULL, NULL, 'application', NULL, 'application', '0');
INSERT INTO ums_sys_role_function(role_id, function_id, value) VALUES ( '140d6cf17d244e74855ecdd56c40335c', 'base-dictionary', '15');

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES( 'builtIn', '1', 3, 'API市场', 'market-api', '', '0', '0', 'manage-api', '1', 90, 90, 'API市场', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', NULL, 'data', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES( 'builtIn', '1', 3, '我的API', 'my-api', '', '0', '0', 'manage-api', '1', 100, 100, '我的API', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', NULL, 'data', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES( 'builtIn', '1', 3, 'API审核', 'check-api', '', '0', '0', 'manage-api', '1', 110, 110, 'API审核', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', NULL, 'data', '0');

INSERT INTO ums_sys_role_function ( role_id, function_id, value, create_user, create_date, update_user, update_date) VALUES( '140d6cf17d244e74855ecdd56c40335c', 'market-api', NULL, NULL, NULL, NULL, NULL);
INSERT INTO ums_sys_role_function ( role_id, function_id, value, create_user, create_date, update_user, update_date) VALUES( '140d6cf17d244e74855ecdd56c40335c', 'my-api', NULL, NULL, NULL, NULL, NULL);
INSERT INTO ums_sys_role_function ( role_id, function_id, value, create_user, create_date, update_user, update_date) VALUES( '140d6cf17d244e74855ecdd56c40335c', 'check-api', NULL, NULL, NULL, NULL, NULL);
