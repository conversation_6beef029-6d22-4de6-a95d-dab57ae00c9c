update ums_sys_user set default_router_id = 'network-sdc-portal';
UPDATE ums_sys_application SET code = 'base' where code = 'application';

alter table ums_sys_application modify app_desc varchar(1024) null comment '应用标识';

update ums_sys_application set app_desc = '对采集数据源的配置和数据解析、转换、装载等处理过程，实现对数据的采集存储。' where code = 'insight';
update ums_sys_application set app_desc = '对安全数据从创建到消亡的全过程的监控和治理，实现数据的统一管理。' where code = 'data-lake-governance';
update ums_sys_application set app_desc = '通过数据集市实现对已经纳管的数据的内容的展示和数据对外的订阅。' where code = 'data-mart';
update ums_sys_application set app_desc = '通过数据共享结合数据集市，实现基于租户的使用需求，向租户共享数据。' where code = 'data';
update ums_sys_application set app_desc = '提供集成组件调用、代码编写功能一体化开发服务套件的集成开发环境。' where code = 'data-development-outer';
update ums_sys_application set app_desc = '提供集成组件调用、代码编写功能一体化开发服务套件的集成开发环境。' where code = 'data-development-inner';
update ums_sys_application set app_desc = '提供数据丰富的数据预处理、 数据分析与数据挖掘组件,帮助快速建立数据挖掘工程，提升数据处理的效能。' where code = 'ai-data-analysis';
update ums_sys_application set app_desc = '数澜-数据存储计算。' where code = 'model';
update ums_sys_application set app_desc = '数澜-运维中心。' where code = 'operation-center';
update ums_sys_application set app_desc = '数澜-数据资产。' where code = 'data-assets';
update ums_sys_application set app_desc = '数澜-项目管理。' where code = 'project-manager';

UPDATE ums_sys_application SET app_release = 'etl.png' WHERE code = 'insight';
UPDATE ums_sys_application SET app_release = 'gov.png' WHERE code = 'data-lake-governance';
UPDATE ums_sys_application SET app_release = 'share.png' WHERE code = 'data';
UPDATE ums_sys_application SET app_release = 'market.png' WHERE code = 'data-mart';
UPDATE ums_sys_application SET app_release = 'dev.png' WHERE code = 'data-development-outer';
UPDATE ums_sys_application SET app_release = 'dev.png' WHERE code = 'data-development-inner';
UPDATE ums_sys_application SET app_release = 'analysis.png' WHERE code = 'ai-data-analysis';
UPDATE ums_sys_application SET app_release = 'report.png' WHERE code = 'model';
UPDATE ums_sys_application SET app_release = 'gate.png' WHERE code = 'operation-center';
UPDATE ums_sys_application SET app_release = 'etl.png' WHERE code = 'data-assets';
UPDATE ums_sys_application SET app_release = 'gov.png' WHERE code = 'project-manager';

UPDATE ums_sys_application SET version = '10' WHERE code = 'insight';
UPDATE ums_sys_application SET version = '20' WHERE code = 'data-lake-governance';
UPDATE ums_sys_application SET version = '30' WHERE code = 'data';
UPDATE ums_sys_application SET version = '40' WHERE code = 'data-mart';
UPDATE ums_sys_application SET version = '50' WHERE code = 'data-development-outer';
UPDATE ums_sys_application SET version = '60' WHERE code = 'data-development-inner';
UPDATE ums_sys_application SET version = '70' WHERE code = 'ai-data-analysis';
UPDATE ums_sys_application SET version = '80' WHERE code = 'model';
UPDATE ums_sys_application SET version = '90' WHERE code = 'operation-center';
UPDATE ums_sys_application SET version = '95' WHERE code = 'data-assets';
UPDATE ums_sys_application SET version = '99' WHERE code = 'project-manager';

DELETE FROM ums_sys_menus where menu_code IN ('network-sdc-portal');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '2', '首页', 'network-sdc-portal', NULL, '0', '0', 'base', '1', '10', '10', '首页', '1', 'base', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0');

INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '2', '数据建模', 'province-data-model', NULL, '0', '0', 'data-lake-governance', '1', '70', '70', '数据建模', '1', 'data-lake-governance', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '3', '模型概览', 'province-model-overview', NULL, '0', '0', 'province-data-model', '1', '10', '10', '模型概览', '1', 'province-data-model', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '3', '业务板块', 'business-sector', NULL, '0', '0', 'province-data-model', '1', '20', '20', '业务板块', '1', 'province-data-model', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '3', '维度', 'logic-table', NULL, '0', '0', 'province-data-model', '1', '30', '30', '维度', '1', 'province-data-model', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '3', '业务过程', 'business-process', NULL, '0', '0', 'province-data-model', '1', '40', '40', '业务过程"', '1', 'province-data-model', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '3', '模型目录', 'model-catalog', NULL, '0', '0', 'province-data-model', '1', '50', '50', '模型目录"', '1', 'province-data-model', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '3', '导出逻辑表', 'export-logic-table', NULL, '0', '1', 'province-data-model', '1', '60', '60', '导出逻辑表"', '1', 'province-data-model', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '3', '导入逻辑表', 'import-logic-table', NULL, '0', '1', 'province-data-model', '1', '70', '70', '导入逻辑表"', '1', 'province-data-model', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '3', '逻辑表详情', 'logic-table-detail', NULL, '0', '1', 'province-data-model', '1', '80', '80', '逻辑表详情"', '1', 'province-data-model', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');

UPDATE ums_sys_menus SET parent_name='base',default_parent='base' WHERE parent_name='application';
UPDATE ums_sys_menus SET root_parent='base',application='base' WHERE root_parent='application';
UPDATE ums_sys_menus SET menu_code='base',root_parent='base',application='base' WHERE menu_code='application';
UPDATE ums_sys_role_function SET function_id = 'base' WHERE function_id = 'application';

DELETE FROM ums_sys_menus WHERE menu_code = 'data-lake-home-tenant';














