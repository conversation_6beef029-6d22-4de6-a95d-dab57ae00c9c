CREATE TABLE IF NOT EXISTS `tb_filter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `module_code` varchar(255) NOT NULL COMMENT '模块编码',
  `module_name` varchar(512) DEFAULT NULL COMMENT '模块名称',
  `code` varchar(50) NOT NULL COMMENT '过滤条件编码',
  `name` varchar(512) NOT NULL COMMENT '过滤条件名称',
  `parent_code` varchar(50) DEFAULT NULL COMMENT '父级编码',
  `filter_config` text COMMENT '过滤配置    "valueKey":"过滤条件值key值",\n    "fromTable":""\n}',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识:0-无效；1-有效',
  `page_filter` int(1) DEFAULT NULL COMMENT '是否列表页过滤条件：0-否，1-是',
  `agg_filter` int(1) DEFAULT NULL COMMENT '是否聚合条件：0-否；1-是',
  `is_default` varchar(1) DEFAULT NULL COMMENT '是否默认:0-否，1-是',
  `is_page_default` VARCHAR(100) DEFAULT NULL COMMENT '是否过滤默认:0-否，1-是',
  `sort_no` int(5) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_code` (`module_code`,`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='列表聚合过滤条件配置';

CREATE TABLE IF NOT EXISTS `common_filter_base` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `create_user` varchar(100) NOT NULL COMMENT '过滤条件所属用户',
  `name` varchar(128) NOT NULL COMMENT '过滤条件名称',
  `type` varchar(128) DEFAULT NULL COMMENT '模块类型',
  `filter_desc` varchar(1024) DEFAULT NULL COMMENT '过滤条件描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `last_run_time` datetime DEFAULT NULL COMMENT '上次运行时间',
  `overview` varchar(1) DEFAULT NULL COMMENT '是否为概览页面的查询条件 1-是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `common_filter_condition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `filter_id` bigint(20) NOT NULL COMMENT '所属过滤条件ID',
  `code` varchar(128) NOT NULL COMMENT '过滤类型code',
  `code_column` varchar(128) DEFAULT NULL,
  `filter_mode` int(11) DEFAULT NULL COMMENT '过滤样式( 1-多选方案1 2-多选方案2 3-多选方案3 4-分值方案 5-数值区间 6-日期区间)',
  `start_value` varchar(1024) DEFAULT NULL COMMENT '过滤条件开始值',
  `end_value` varchar(512) DEFAULT NULL COMMENT '过滤结束值',
  `value_list` varchar(2048) DEFAULT NULL COMMENT '值列表',
  `operator` varchar(8) DEFAULT NULL COMMENT '运算符(> < = 等)',
  `logic_operator` varchar(8) DEFAULT NULL COMMENT '逻辑运算符(AND OR)',
  `name` varchar(258) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `common_show_field` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键自增长',
  `fields_list` text NOT NULL COMMENT '自定义列表显示的字段',
  `type` varchar(50) NOT NULL COMMENT '调用的接口的标识',
  `user` varchar(50) DEFAULT NULL COMMENT '创建的用户',
  `create_time` datetime DEFAULT NULL COMMENT '创建的时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改的时间',
  `describes` varchar(50) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='自定义列信息表';