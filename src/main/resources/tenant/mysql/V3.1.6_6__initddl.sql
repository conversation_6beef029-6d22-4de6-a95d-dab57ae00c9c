CREATE TABLE IF NOT EXISTS `model_cube_base_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL COMMENT 'cube名称',
  `cube_desc` varchar(1024) DEFAULT NULL COMMENT '描述',
  `data_source` varchar(128) DEFAULT NULL COMMENT '数据源',
  `table_name` varchar(128) DEFAULT NULL COMMENT 'CUBE表名',
  `store_time` int(11) NOT NULL COMMENT '存储时限',
  `store_time_unit` varchar(8) DEFAULT NULL COMMENT '存储时限单位',
  `enable_advanced` int(11) NOT NULL COMMENT '是否启用高级配置  0-未启用  1-启用',
  `cube_sql` text COMMENT 'CUBE对应的SQL',
  `cube_table` text COMMENT 'CUBE对应的建表SQL',
  `cluster_name` varchar(64) DEFAULT '' COMMENT '集群名称',
  `time_interval_unit` varchar(16) NOT NULL DEFAULT '' COMMENT '时间间隔单位',
  `status` int(11) DEFAULT NULL COMMENT '状态（0 表示禁用，1表示启用）',
  `task_updatetime` datetime DEFAULT NULL COMMENT '任务更新时间',
  `task_result` int(11) DEFAULT NULL COMMENT '执行结果(0-执行失败，1-执行成功)',
  `task_status` int(11) DEFAULT NULL COMMENT '任务状态 1-运行中 2-运行结束 3-上线 4-下线',
  `task_nexttime` datetime DEFAULT NULL COMMENT '任务下次执行时间',
  `create_user` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(64) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `agg_time` int(11) DEFAULT NULL COMMENT '时间聚合',
  `agg_time_unit` varchar(16) DEFAULT NULL COMMENT '时间聚合单位',
  `time_interval` int(11) DEFAULT '1' COMMENT '间隔时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS  `model_cube_cfg` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cube_id` bigint(20) NOT NULL COMMENT 'cube ID',
  `new_field` varchar(128) DEFAULT NULL COMMENT '新增字段',
  `field_category` varchar(8) DEFAULT NULL COMMENT '字段类别(index-指标、dim-维度)',
  `field_type` varchar(32) DEFAULT NULL COMMENT '字段类型',
  `origin_field` varchar(128) DEFAULT NULL COMMENT '源表字段',
  `origin_field_type` varchar(32) DEFAULT NULL COMMENT '源表字段类型',
  `operator` varchar(32) DEFAULT NULL COMMENT '算子',
  `field_desc` varchar(128) DEFAULT NULL COMMENT '字段描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `model_cube_filter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cube_id` bigint(20) NOT NULL COMMENT 'cube ID',
  `field` varchar(128) DEFAULT NULL COMMENT '过滤字段',
  `field_type` varchar(16) DEFAULT NULL COMMENT '字段类型',
  `filter_type` varchar(8) DEFAULT NULL COMMENT '过滤类型',
  `filter_value` varchar(512) DEFAULT NULL COMMENT '过滤值',
  `logic` varchar(8) DEFAULT NULL COMMENT '逻辑运算符',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父节点ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `model_cube_tasklog` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cube_id` bigint(20) NOT NULL COMMENT 'cube ID',
  `start_time` datetime DEFAULT NULL COMMENT '任务开始执行时间',
  `end_time` datetime DEFAULT NULL COMMENT '任务执行结束时间',
  `run_status` int(11) DEFAULT NULL COMMENT '运行状态 1-运行中 2-运行结束',
  `run_result` int(11) DEFAULT NULL COMMENT '运行结果  0-运行失败 1-运行成功',
  `exec_start_time` varchar(32) DEFAULT NULL COMMENT '入参开始时间',
  `exec_end_time` varchar(32) DEFAULT NULL COMMENT '入参结束时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;





