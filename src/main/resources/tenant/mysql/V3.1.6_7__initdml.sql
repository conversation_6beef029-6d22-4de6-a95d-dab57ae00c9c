INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '3', '特征引擎', 'CUBEManager', NULL, '0', '0', 'dataMart', '1', '40', '40', '特征引擎', '1', 'dataMart', NULL, NULL, NULL, NULL, 'data', NULL, 'data', '0');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '3', '新增特征引擎', 'cube-create', NULL, '0', '0', 'CUBEManager', '1', '10', '10', '新增特征引擎', '1', 'CUBEManager', NULL, NULL, NULL, NULL, 'data', NULL, 'data', '0');

INSERT INTO `ums_sys_role_function` (`role_id`, `function_id`) VALUES ('140d6cf17d244e74855ecdd56c40335c', 'CUBEManager');
INSERT INTO `ums_sys_role_function` (`role_id`, `function_id`) VALUES ('140d6cf17d244e74855ecdd56c40335c', 'cube-create');