INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '2', '配置管理', 'config-manage', NULL, '0', '0', 'system', '1', '30', '30', '配置管理', '1', 'system', NULL, NULL, NULL, NULL, 'application', NULL, 'application', NULL);

UPDATE ums_sys_menus SET parent_name = 'config-manage',default_parent = 'config-manage' WHERE menu_code = 'user-auth-config';
UPDATE ums_sys_menus SET parent_name = 'account-right',default_parent = 'account-right' WHERE menu_code = 'organization';
