delete from ums_sys_menus where menu_name = '脱敏规则';
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, '脱敏规则', 'desensitization-rules', null, '0', '0',
        'data-lake-governance-config', '1', 60, 60, '脱敏规则', '1', 'data-lake-governance-config', null, null,
        null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path,
                                           manage_free, hidden, parent_name, status, menu_order, default_order,
                                           default_name, default_status, default_parent, create_user, create_date,
                                           update_user, update_date, root_parent, application_code, application, level1,
                                           menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'Clickhouse', 'data-lake-governance-meta-search-ch', null, '0', '0',
        'data-lake-governance-meta-search', '1', 10, 10, 'Clickhouse', '1', 'data-lake-governance-meta-search', null, null, null,
        null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path,
                                           manage_free, hidden, parent_name, status, menu_order, default_order,
                                           default_name, default_status, default_parent, create_user, create_date,
                                           update_user, update_date, root_parent, application_code, application, level1,
                                           menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'Mysql', 'data-lake-governance-meta-search-mysql', null, '0', '0',
        'data-lake-governance-meta-search', '1', 20, 20, 'Mysql', '1', 'data-lake-governance-meta-search', null, null, null,
        null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path,
                                           manage_free, hidden, parent_name, status, menu_order, default_order,
                                           default_name, default_status, default_parent, create_user, create_date,
                                           update_user, update_date, root_parent, application_code, application, level1,
                                           menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'Elasticsearch', 'data-lake-governance-meta-search-es', null, '0', '0',
        'data-lake-governance-meta-search', '1', 30, 30, 'Elasticsearch', '1', 'data-lake-governance-meta-search', null, null, null,
        null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path,
                                           manage_free, hidden, parent_name, status, menu_order, default_order,
                                           default_name, default_status, default_parent, create_user, create_date,
                                           update_user, update_date, root_parent, application_code, application, level1,
                                           menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'Hive', 'data-lake-governance-meta-search-hive', null, '0', '0',
        'data-lake-governance-meta-search', '1', 40, 40, 'Hive', '1', 'data-lake-governance-meta-search', null, null, null,
        null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);

delete from ums_sys_menus where menu_name = '订阅管理';
INSERT INTO ums_sys_menus ( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free,
                            hidden, parent_name, status, menu_order, default_order, default_name, default_status,
                            default_parent, create_user, create_date, update_user, update_date, root_parent,
                            application_code, application, level1,menu_category, new_open_window)
VALUES ('builtIn', '1', 2, '审批管理', 'approve-layout', null, '0', '0', 'data-mart', '1', 55, 55, '审批管理', '1',
        null, null, null, null, null, 'data-mart', null, 'data-mart', null,'0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1 ,menu_category, new_open_window)
VALUES ( 'builtIn', '1', 3, '订阅审批', 'subscription-manage', null, '0', '0', 'approve-layout', '1', 40, 40,
         '订阅审批', '1', null, null, null, null, null, 'data-mart', null, 'data-mart', null,'0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1,menu_category, new_open_window )
VALUES ( 'builtIn', '1', 3, '发布审批', 'data-mart-approve-manage', null, '0', '0', 'approve-layout', '1', 60, 60,
         '发布审批', '1', null, null, null, null, null, 'data-mart', null, 'data-mart',null, '0', 0);

update ums_sys_menus set menu_name = '数据发布',default_name='数据发布' where menu_code = 'data-mart-data-manage';


