truncate table `ums_sys_menus`;
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '模型调度', 'scheduler-configuration', null, '0', '0', 'model', '1', 50, 50, '模型调度', '1', 'data-storage-calculation', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '任务流管理', 'definition-list', null, '0', '0', 'scheduler-configuration', '1', 1, 1, '任务流管理', '1', 'scheduler-configuration', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '运行实例', 'instance-list', null, '0', '0', 'scheduler-configuration', '1', 20, 20, '运行实例', '1', 'scheduler-configuration', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '模型文件管理', 'fileManagement-list', null, '0', '0', 'scheduler-configuration', '1', 30, 30, '模型文件管理', '1', 'scheduler-configuration', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 1, '数据存储计算', 'model', null, '0', '0', null, '1', 60, 60, '数据存储计算', '1', null, null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '任务流图', 'definition-tree', null, '1', '0', 'definition-list', '1', 1, 1, '任务流图', '1', 'definition-list', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '创建任务流', 'definition-create', null, '1', '0', 'definition-list', '1', 20, 20, '创建任务流', '1', 'definition-list', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '任务流配置', 'definition-flow-detail', null, '1', '0', 'definition-list', '1', 30, 30, '任务流配置', '1', 'definition-list', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '定时管理', 'definition-timing', null, '1', '0', 'definition-list', '1', 40, 40, '定时管理', '1', 'definition-list', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '任务进展', 'instance-gantt', null, '1', '0', 'instance-list', '1', 1, 1, '任务进展', '1', 'instance-list', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '任务流配置', 'instance-flow-detail', null, '1', '0', 'instance-list', '1', 20, 20, '任务流配置', '1', 'instance-list', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '新建文件', 'fileManagement-create-file', null, '1', '0', 'fileManagement-list', '1', 1, 1, '新建文件', '1', 'fileManagement-list', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '编辑文件', 'fileManagement-file-edit', null, '1', '0', 'fileManagement-list', '1', 20, 20, '编辑文件', '1', 'fileManagement-list', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '查看文件', 'fileManagement-file-detail', null, '1', '0', 'fileManagement-list', '1', 30, 30, '查看文件', '1', 'fileManagement-list', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '数据源配置管理', 'db-source-config', null, '0', '0', 'cluster-resource-manage', '1', 40, 40, '数据源配置管理', '1', 'cluster-resource-manage', null, null, null, null, 'model', null, null, null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, 'EasyGo', 'easygo', '', '0', '0', 'system', '1', 80, 80, 'EasyGo', '1', 'system', null, null, null, null, 'system', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '系统管理', 'system', null, '0', '0', 'application', '1', 30, 30, '系统管理', '1', 'application', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 1, '应用', 'application', null, '0', '0', null, '1', 10, 10, '应用', '1', null, null, null, null, null, 'application', null, 'application', '1');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '首页', 'data-lake-home-tenant', null, '0', '0', 'application', '1', 10, 10, '首页', '1', 'application', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '资源管理', 'cluster-resource-manage', null, '0', '0', 'application', '1', 20, 20, '资源管理', '1', 'application', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '集群管理', 'cluster-manage', null, '0', '0', 'cluster-resource-manage', '1', 1, 80, '集群管理', '1', 'cluster-resource-manage', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '系统日志', 'sys-log', null, '0', '0', 'system', '1', 1, 82, '系统日志', '1', 'system', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '其它配置', 'system-config-logo', null, '0', '0', 'system', '1', 1, 83, '其它配置', '1', 'system', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '菜单管理', 'menu-manage', null, '0', '0', 'system', '1', 1, 84, '菜单管理', '1', 'system', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '账号管理', 'system-account', null, '0', '0', 'system', '1', 1, 84, '账号管理', '1', 'system', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '角色管理', 'role', null, '0', '0', 'system', '1', 1, 84, '角色管理', '1', 'system', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '操作审计', 'operation-audit', null, '0', '0', 'sys-log', '1', 1, 1, '操作审计', '1', 'sys-log', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 1, '数据共享', 'data', null, '0', '0', null, '1', 50, 50, '数据共享', '1', null, null, null, null, null, 'data', null, 'data', '1');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '安全日志检索', 'dataExplore', null, '0', '0', 'data', '1', 1, 1, '安全日志检索', '1', 'data', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '安全备份服务', 'resource-manage', null, '0', '0', 'data', '1', 20, 20, '安全备份服务', '1', 'data', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, 'CH备份', 'backup-manage', null, '0', '0', 'resource-manage', '1', 10, 10, '备份管理', '1', 'resource-manage', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, 'CH备份日志', 'backup-date-log', null, '0', '0', 'resource-manage', '1', 20, 20, '备份日志', '1', 'resource-manage', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, 'CH恢复', 'data-resume', null, '0', '0', 'resource-manage', '1', 30, 30, '数仓恢复', '1', 'resource-manage', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, 'Mysql备份', 'config-info', null, '0', '0', 'resource-manage', '1', 40, 40, 'Mysql备份', '1', 'resource-manage', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '文件服务配置', 'file-service', null, '0', '0', 'resource-manage', '1', 50, 50, '文件服务配置', '1', 'resource-manage', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '自定义模型', 'dataMart', null, '0', '0', 'data', '1', 40, 40, '自定义模型', '1', 'data', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '数据集管理', 'dataset', null, '0', '0', 'dataMart', '1', 20, 20, '数据集管理', '1', 'dataMart', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '自定义模型脚本', 'model-explore', null, '0', '0', 'dataMart', '1', 1, 1, '自定义模型脚本', '1', 'dataMart', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '分析报表', 'report-manage', null, '0', '0', 'dataMart', '1', 30, 30, '分析报表', '1', 'dataMart', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '结构化数据', 'struct-data', null, '0', '0', 'data', '1', 30, 30, '结构化数据', '1', 'data', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '数据源管理', 'zeppelin-interpreter', null, '0', '0', 'struct-data', '1', 10, 10, '数据源管理', '1', 'struct-data', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, 'notebook', 'zeppelin-notebook', null, '0', '0', 'struct-data', '1', 20, 20, 'notebook', '1', 'struct-data', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '解释器查询结果', 'zeppelin-paragraph', null, '0', '0', 'struct-data', '1', 30, 30, '解释器查询结果', '1', 'struct-data', null, null, null, null, 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, 'API管理', 'manage-api', '', '0', '0', 'data', '1', 50, 50, 'API管理', '1', 'data', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, 'API配置', 'manage-data-api', '', '0', '0', 'manage-api', '1', 10, 10, 'API配置', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '生成API', 'manage-data-api-create', '', '0', '0', 'manage-api', '1', 20, 20, '生成API', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '生成API-模式选择', 'manage-data-new-api', '', '0', '0', 'manage-api', '1', 30, 30, '生成API-模式选择', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '注册API', 'manage-data-api-register', '', '0', '0', 'manage-api', '1', 40, 40, '注册API', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, 'API授权', 'client-manage', '', '0', '0', 'manage-api', '1', 50, 50, 'API授权', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '数据源管理', 'source-manage', '', '0', '0', 'manage-api', '1', 60, 60, '数据源管理', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '项目管理', 'project-manage', '', '0', '0', 'manage-api', '1', 70, 70, '项目管理', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '安全组', 'security-group', '', '0', '0', 'manage-api', '1', 80, 80, '安全组', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 1, '数据开发', 'data-development-inner', null, '0', '0', null, '1', 50, 50, '数据开发', '1', null, null, null, null, null, 'data-development-inner', null, 'data-development-inner', '1');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '开发概览', 'develop-overview', null, '0', '0', 'data-development-inner', '1', 1, 1, '开发概览', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '项目管理', 'project-list', null, '0', '0', 'data-development-inner', '1', 2, 2, '项目管理', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '资源管理', 'resource-center', null, '0', '0', 'data-development-inner', '1', 3, 3, '资源管理', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '任务监控', 'task-monitor', null, '0', '0', 'data-development-inner', '1', 4, 4, '任务监控', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '数据源管理', 'datasource-manage', null, '0', '0', 'data-development-inner', '1', 5, 5, '数据源管理', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '文件管理', 'file-manage', null, '0', '0', 'resource-center', '1', 3, 3, '文件管理', '1', 'resource-center', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '资源管理', 'resource', null, '0', '0', 'resource-center', '1', 4, 4, '资源管理', '1', 'resource-center', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '函数管理', 'function-manage', null, '0', '0', 'resource-center', '1', 5, 5, '函数管理', '1', 'resource-center', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '任务组配置', 'task-group-option', null, '0', '0', 'resource-center', '1', 6, 6, '任务组配置', '1', 'resource-center', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '任务组队列', 'task-group-queue', null, '0', '0', 'resource-center', '1', 7, 7, '任务组队列', '1', 'resource-center', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, 'Master', 'master-monitor', null, '0', '0', 'task-monitor', '1', 4, 4, 'Master', '1', 'task-monitor', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, 'Worker', 'worker-monitor', null, '0', '0', 'task-monitor', '1', 5, 5, 'Worker', '1', 'task-monitor', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, 'DB', 'db-monitor', null, '0', '0', 'task-monitor', '1', 6, 6, 'DB', '1', 'task-monitor', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, 'Statistics', 'statistics-monitor', null, '0', '0', 'task-monitor', '1', 7, 7, 'Statistics', '1', 'task-monitor', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '审计日志', 'audit-log', null, '0', '0', 'task-monitor', '1', 8, 8, '审计日志', '1', 'task-monitor', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 1, '数据治理', 'data-lake-governance', null, '0', '0', null, '1', 40, 40, '数据治理', '1', null, null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '1');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '元数据管理', 'data-lake-governance-meta', null, '0', '0', 'data-lake-governance', '1', 10, 10, '元数据管理', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '元数据采集', 'data-lake-governance-meta-access', null, '0', '0', 'data-lake-governance-meta', '1', 10, 10, '元数据采集', '1', 'data-lake-governance-meta', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '元数据查询', 'data-lake-governance-meta-search', null, '0', '0', 'data-lake-governance-meta', '1', 20, 20, '元数据查询', '1', 'data-lake-governance-meta', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '数据标准管理', 'data-lake-governance-standard', null, '0', '0', 'data-lake-governance', '1', 20, 20, '数据标准管理', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '数据质量管理', 'data-lake-governance-quality', null, '0', '0', 'data-lake-governance', '1', 30, 30, '数据质量管理', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '稽核数据源管理', 'data-lake-governance-quality-model', null, '0', '0', 'data-lake-governance-quality', '1', 30, 10, '稽核数据源管理', '1', 'data-lake-governance-quality', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '模型管理', 'data-lake-governance-quality-rule', null, '0', '0', 'data-lake-governance-quality', '1', 20, 20, '模型管理', '1', 'data-lake-governance-quality', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '任务管理', 'data-lake-governance-quality-task', null, '0', '0', 'data-lake-governance-quality', '1', 10, 30, '任务管理', '1', 'data-lake-governance-quality', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 5, '质量监测结果', 'data-lake-governance-quality-result', null, '0', '0', 'data-lake-governance-quality-task', '1', 40, 40, '质量监测结果', '1', 'data-lake-governance-quality-task', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '主数据管理', 'data-lake-governance-masterData', null, '0', '0', 'data-lake-governance', '1', 40, 40, '主数据管理', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '主数据模型', 'data-lake-governance-masterData-model', null, '0', '0', 'data-lake-governance-masterData', '1', 10, 10, '主数据模型', '1', 'data-lake-governance-masterData', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '主数据维护', 'data-lake-governance-masterData-maintain', null, '0', '0', 'data-lake-governance-masterData-model', '1', 20, 20, '主数据维护', '1', 'data-lake-governance-masterData-model', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '配置管理', 'data-lake-governance-config', null, '0', '0', 'data-lake-governance', '1', 60, 60, '配置管理', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '业务数据类型分组', 'data-lake-governance-config-grouping', null, '0', '0', 'data-lake-governance-config', '1', 10, 10, '业务数据类型分组', '1', 'data-lake-governance-config', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '主数据类型', 'data-lake-governance-config-data', null, '0', '0', 'data-lake-governance-masterData', '1', 30, 30, '主数据类型', '1', 'data-lake-governance-masterData', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '规则管理', 'data-lake-governance-rule-manage', null, '0', '0', 'data-lake-governance-quality', '1', 40, 40, '规则管理', '1', 'data-lake-governance-quality', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', null);
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '安全数据管理', 'safe-data-manage', null, '0', '0', 'data-lake-governance', '1', 20, 20, '安全数据管理', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '资产目录', 'asset-catalog', null, '0', '0', 'safe-data-manage', '1', 10, 10, '资产目录', '1', 'safe-data-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '资产用途', 'data-lake-governance-asset-use', null, '0', '0', 'data-lake-governance-config', '1', 30, 30, '资产用途', '1', 'data-lake-governance-config', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '敏感分级', 'data-lake-governance-sen-level', null, '0', '0', 'data-lake-governance-config', '1', 30, 30, '敏感分级', '1', 'data-lake-governance-config', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '敏感分类', 'data-lake-governance-sen-classification', null, '0', '0', 'data-lake-governance-config', '1', 30, 30, '敏感分类', '1', 'data-lake-governance-config', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '安全数据资产汇总', 'asset-collect', null, '0', '0', 'safe-data-manage', '1', 10, 10, '安全数据资产汇总', '1', 'safe-data-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '数据业务类型', 'data-lake-governance-config-business', null, '0', '0', 'data-lake-governance-config', '1', 20, 20, '数据业务类型', '1', 'data-lake-governance-config', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 1, '数据采集', 'insight', null, '0', '0', null, '1', 20, 20, '数据采集', '1', null, null, null, null, null, 'insight', null, 'insight', '1');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '数据采集管理', 'dataAccessManagement', null, '0', '0', 'insight', '1', 1, 1, '数据采集管理', '1', 'insight', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '数据转换', 'dataConversion', null, '1', '0', 'dataAccessManagement', '1', 20, 20, '数据转换', '1', 'dataAccessManagement', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '输出配置页面', 'complete', null, '1', '0', 'dataAccessManagement', '1', 30, 30, '输出配置页面', '1', 'dataAccessManagement', null, null, 'sysadmin', '2021-12-01 10:13:50', 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '采集节点管理', 'manage-logmodule', null, '0', '0', 'insight', '1', 30, 30, '采集节点管理', '1', 'insight', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '解析规则管理', 'manage-parsing-rules', null, '0', '0', 'data-input-config', '1', 30, 30, '解析规则管理', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '解析规则详情', 'manage-parsing-rules-detail', null, '1', '0', 'manage-parsing-rules', '1', 1, 1, '解析规则详情', '1', 'manage-parsing-rules', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '新建解析规则', 'manage-parsing-rules-create', null, '1', '0', 'manage-parsing-rules', '1', 20, 20, '新建解析规则', '1', 'manage-parsing-rules', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '解析规则配置', 'manage-parsing-rules-edit', null, '1', '0', 'manage-parsing-rules', '1', 30, 30, '解析规则配置', '1', 'manage-parsing-rules', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '动态补全配置管理', 'manage-dynamic-completion', null, '0', '0', 'data-input-config', '1', 40, 40, '动态补全配置管理', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '采集脚本', 'manage-collection-script', null, '0', '0', 'data-input-config', '1', 50, 50, '采集脚本', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '脚本详情', 'manage-collection-script-detail', null, '1', '0', 'manage-collection-script', '1', 1, 1, '脚本详情', '1', 'manage-collection-script', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '新增脚本', 'manage-collection-script-create', null, '1', '0', 'manage-collection-script', '1', 20, 20, '新增脚本', '1', 'manage-collection-script', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '编辑脚本', 'manage-collection-script-edit', null, '1', '0', 'manage-collection-script', '1', 30, 30, '编辑脚本', '1', 'manage-collection-script', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '宽表字典', 'ueba-dictionary', null, '0', '0', 'data-input-config', '1', 60, 60, '宽表字典', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '场景视图', 'ueba-view', null, '0', '0', 'data-input-config', '1', 70, 70, '场景视图', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '源设备分类管理', 'source-device', null, '0', '0', 'data-input-config', '1', 80, 80, '源设备分类管理', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '视图详情', 'ueba-view-detail', null, '1', '0', 'ueba-view', '1', 1, 1, '视图详情', '1', 'ueba-view', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '采集监控', 'collect-control', null, '0', '0', 'insight', '1', 20, 20, '采集监控', '1', 'insight', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '采集配置', 'data-input-config', null, '0', '0', 'insight', '1', 40, 40, '采集配置', '1', 'insight', null, null, null, null, 'insight', null, 'insight', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '数据采集配置', 'manage-data-input', null, '1', '0', 'dataAccessManagement', '1', 30, 30, '数据采集配置', '1', 'dataAccessManagement', null, null, 'sysadmin', '2021-12-01 10:13:50', 'insight', null, 'insight', '0');

INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, 'Kafka详情', 'kafka-detail', null, '0', '0', 'cluster-manage', '1', 1, 80, 'Kafka详情', '1', 'cluster-manage', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, 'MySQL详情', 'mysql-detail', null, '0', '0', 'cluster-manage', '1', 1, 80, 'MySQL详情', '1', 'cluster-manage', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, 'HDFS详情', 'hdfs-detail', null, '0', '0', 'cluster-manage', '1', 1, 80, 'HDFS详情', '1', 'cluster-manage', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, '数仓详情', 'clickhouse-detail', null, '0', '0', 'cluster-manage', '1', 1, 80, '数仓详情', '1', 'cluster-manage', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 4, 'ES详情', 'elasticsearch-detail', null, '0', '0', 'cluster-manage', '1', 1, 80, 'ES详情', '1', 'cluster-manage', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO `ums_sys_menus` (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '数仓配置', 'database-config', null, '0', '0', 'data-input-config', '1', 90, 90, '数仓配置', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0');

INSERT INTO ums_sys_menus ( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES( 'builtIn', '1', 3, '组织管理', 'organization', NULL, '0', '0', 'system', '1', 1, 85, '组织管理', '1', 'system', NULL, NULL, NULL, NULL, 'application', NULL, 'application', '0');

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date,application_code, application, level1) VALUES( 'builtIn', '1', 1, 'data-mart', '数据集市', 'data-mart', NULL, '0', '0', NULL, '1', 11, 11, '数据集市', '1', NULL, NULL, NULL, NULL, NULL, NULL, 'data-mart',NULL);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date,application_code, application, level1) VALUES( 'builtIn', '1', 2, 'data-mart', '数据集市地图', 'data-mart-map', NULL, '0', '0', 'data-mart', '1', 11, 11, '数据集市地图', '1', NULL, NULL, NULL, NULL, NULL, NULL, 'data-mart',NULL);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date,application_code, application, level1) VALUES( 'builtIn', '1', 3, 'data-mart', '数据集市地图详情', 'data-mart-map-detail', NULL, '0', '0', 'data-mart-map', '1', 11, 11, '数据集市地图详情', '1', NULL, NULL, NULL, NULL, NULL, NULL,'data-mart', NULL);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date,application_code, application, level1) VALUES( 'builtIn', '1', 2, 'data-mart', '集市数据管理', 'data-mart-data-manage', NULL, '0', '0', 'data-mart', '1', 11, 11, '集市数据管理', '1', NULL, NULL, NULL, NULL, NULL, NULL, 'data-mart',NULL);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date,application_code, application, level1) VALUES( 'builtIn', '1', 3, 'data-mart', '数据集市库详情', 'data-mart-data-database', NULL, '0', '0', 'data-mart-data-manage', '1', 11, 11, '数据集市库详情', '1', NULL, NULL, NULL, NULL, NULL, NULL, 'data-mart',NULL);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date,application_code, application, level1) VALUES( 'builtIn', '1', 4, 'data-mart', '数据集市表详情', 'data-mart-database-table', NULL, '0', '0', 'data-mart-data-database', '1', 11, 11, '数据集市表详情', '1', NULL, NULL, NULL, NULL, NULL, NULL, 'data-mart',NULL);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date,application_code, application, level1) VALUES( 'builtIn', '1', 3, 'data-mart', '数据集市表详情', 'data-mart-data-table', NULL, '0', '0', 'data-mart-database-table', '1', 11, 11, '数据集市表详情', '1', NULL, NULL, NULL, NULL, NULL, NULL,'data-mart', NULL);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date,application_code, application, level1) VALUES( 'builtIn', '1', 3, 'data-mart', '集市数据索引详情', 'data-mart-data-index', NULL, '0', '0', 'data-mart-data-manage', '1', 11, 11, '集市数据索引详情', '1', NULL, NULL, NULL, NULL, NULL, NULL, 'data-mart',NULL);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date,application_code, application, level1) VALUES( 'builtIn', '1', 2, 'data-mart', '分类分组管理', 'data-mart-type-group-manage', NULL, '0', '0', 'data-mart', '1', 11, 11, '分类分组管理', '1', NULL, NULL, NULL, NULL, NULL, NULL, 'data-mart',NULL);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date,application_code, application, level1) VALUES( 'builtIn', '1', 3, 'data-mart', '集市分类管理', 'data-mart-type-manage', NULL, '0', '0', 'data-mart-type-group-manage', '1', 11, 11, '集市分类管理', '1', NULL, NULL, NULL, NULL, NULL, NULL, 'data-mart',NULL);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date,application_code, application, level1) VALUES( 'builtIn', '1', 3, 'data-mart', '集市分组管理', 'data-mart-group-manage', NULL, '0', '0', 'data-mart-type-group-manage', '1', 11, 11, '集市分组管理', '1', NULL, NULL, NULL, NULL, NULL, NULL,'data-mart', NULL);




truncate table ums_sys_application;
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version) VALUES ('数据采集', 'insight', null, '1', null, '1', null, null, null, null, null, null);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version) VALUES ('数据治理', 'data-lake-governance', null, '1', null, '1', null, null, null, null, null, null);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version) VALUES ('数据共享', 'data', null, '1', null, '1', null, null, null, null, null, null);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version) VALUES ('数据存储计算', 'model', null, '1', null, '1', null, null, null, null, null, null);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version) VALUES ('数据开发', 'data-development-outer', null, '1', null, '1', null, null, 'http://10.10.25.22/data-dev-outer/data-development', null, null, null);

INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version) VALUES ('运维中心', 'operation-center', null, '1', null, '1', null, null, 'http://10.10.25.22/data-dev-outer/operation-center', null, null, null);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version) VALUES ('数据资产', 'data-assets', null, '1', null, '1', null, null, 'http://10.10.25.22/data-dev-outer/data-assets', null, null, null);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version) VALUES ('项目管理', 'project-manager', null, '1', null, '1', null, null, 'http://10.10.25.22/data-dev-outer/project-manager', null, null, null);

INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version) VALUES ('数据开发(自研)', 'data-development-inner', null, '1', null, '1', null, null, null, null, null, null);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version) VALUES ('应用', 'application', null, '1', null, '1', null, null, null, null, null, null);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version) VALUES ('数据集市', 'data-mart', null, '1', null, '1', null, null, null, null, null, null);


INSERT INTO ums_sys_role_function ( role_id, function_id, value, create_user, create_date, update_user, update_date) VALUES( '140d6cf17d244e74855ecdd56c40335c', 'config-manage', NULL, NULL, NULL, NULL, NULL);
INSERT INTO ums_sys_role_function ( role_id, function_id, value, create_user, create_date, update_user, update_date) VALUES( '140d6cf17d244e74855ecdd56c40335c', 'user-auth-detail', NULL, NULL, NULL, NULL, NULL);
INSERT INTO ums_sys_role_function ( role_id, function_id, value, create_user, create_date, update_user, update_date) VALUES( '140d6cf17d244e74855ecdd56c40335c', 'user-auth-config', NULL, NULL, NULL, NULL, NULL);
INSERT INTO ums_sys_role_function ( role_id, function_id, value, create_user, create_date, update_user, update_date) VALUES( '140d6cf17d244e74855ecdd56c40335c', 'organization', NULL, NULL, NULL, NULL, NULL);