INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('1', '1级', 1, '低敏感级', NULL, 'ASSET_RESOURCE_LEVEL', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('2', '2级', 1, '较敏感级', NULL, 'ASSET_RESOURCE_LEVEL', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('3', '3级', 1, '敏感级', NULL, 'ASSET_RESOURCE_LEVEL', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('4', '4集', 1, '极敏感级', NULL, 'ASSET_RESOURCE_LEVEL', NULL);

INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('000', '机密类型', 1, '', NULL, 'ASSET_RESOURCE_CLASSIFICATION', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('1', 'IP信息', 1, '3级', NULL, 'ASSET_RESOURCE_CLASSIFICATION', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('2', '电话号码', 1, '4级', NULL, 'ASSET_RESOURCE_CLASSIFICATION', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('3', '手机号码', 1, '4级', NULL, 'ASSET_RESOURCE_CLASSIFICATION', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('4', '电子邮箱', 1, '3级', NULL, 'ASSET_RESOURCE_CLASSIFICATION', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('5', 'IMSI', 1, '3级', NULL, 'ASSET_RESOURCE_CLASSIFICATION', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('6', '宽带账号', 1, '4级', NULL, 'ASSET_RESOURCE_CLASSIFICATION', NULL);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('7', '用户名', 1, '3级', NULL, 'ASSET_RESOURCE_CLASSIFICATION', NULL);

alter table ums_sys_role add role_resource_search_limit int default 0 not null comment '资源限制(实时搜索-角色)';

alter table ums_sys_role add user_resource_search_limit int default 0 not null comment '资源限制(实时搜索-用户)';

alter table ums_sys_user add data_limit_extend_role_id varchar(64) null comment '限制继承的角色id';