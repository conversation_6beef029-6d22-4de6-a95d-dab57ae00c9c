delete from ums_sys_application where code = 'model-manager';
INSERT INTO ums_sys_application ( name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ( '模型管理', 'model-manager', null, '1', null, '模型管理', null, 'report.png', null, null, null, '10', 0);

delete from ums_sys_menus where menu_code = 'model-manager';
INSERT INTO ums_sys_menus(menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code,application, level1, menu_category, new_open_window)VALUES ('builtIn', '1', 1, '模型管理', 'model-manager', null, '0', '0', null, '1', 40, 40, '模型管理', '1', null,null, null, null, null, 'model-manager', null, 'model-manager', '1', '0', 0);

delete from ums_sys_menus where menu_code = 'my-model';
INSERT INTO ums_sys_menus(menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,parent_name, status, menu_order, default_order, default_name, default_status, default_parent,create_user, create_date, update_user, update_date, root_parent, application_code,application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '我的模型', 'my-model', null, '0', '0', 'model-manager', '1', 10, 10,'我的模型', '1', 'model-manager', null, null, null, null, 'model-manager', null,'model-manager', '0', '0', 0);

update ums_sys_application set app_desc = '数据存储计算。' where code = 'model';
update ums_sys_application set app_desc = '提供数据开发运维能力，包括运维概览、离线运维、实施运维、算法运维等，并且提供整体监控能力' where code = 'operation-center';
update ums_sys_application set app_desc = '提供全方位的资产全景，包括数据地图、数据标准、数据质量和数据安全能力' where code = 'data-assets';
update ums_sys_application set app_desc = '项目管理。' where code = 'project-manager';