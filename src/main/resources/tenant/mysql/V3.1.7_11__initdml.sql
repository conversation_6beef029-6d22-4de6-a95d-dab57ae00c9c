delete from ums_sys_menus where menu_code in ('ch-backup-manage','backup-manage','backup-date-log','data-resume','mysql-backup-manage','mysql-backup','mysql-backup-log','mysql-resume-log');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1)
VALUES
    ('builtIn', '1', 3, 'CH备份管理', 'ch-backup-manage', null, '0', '0', 'resource-manage', '1', 1, 1, '备份管理', '1',
     'resource-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 4, 'CH备份', 'backup-manage', null, '0', '0', 'ch-backup-manage', '1', 1, 1, 'CH备份', '1',
     'ch-backup-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 4, 'CH备份日志', 'backup-date-log', null, '0', '0', 'ch-backup-manage', '1', 2, 2, 'CH备份日志',
     '1', 'ch-backup-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 4, 'CH恢复', 'data-resume', null, '0', '0', 'ch-backup-manage', '1', 3, 3, 'CH恢复', '1',
     'ch-backup-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 3, 'Mysql备份管理', 'mysql-backup-manage', null, '0', '0', 'resource-manage', '1', 1, 2, 'Mysql备份管理', '1',
     'resource-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 4, 'Mysql备份', 'mysql-backup', null, '0', '0', 'mysql-backup-manage', '1', 1, 1, 'Mysql备份', '1',
     'ch-backup-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 4, 'Mysql备份日志', 'mysql-backup-log', null, '0', '0', 'mysql-backup-manage', '1', 2, 2, 'Mysql备份日志',
     '1', 'ch-backup-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 4, 'Mysql恢复', 'mysql-resume-log', null, '0', '0', 'mysql-backup-manage', '1', 3, 3, 'Mysql恢复', '1',
     'ch-backup-manage', null, null, null, null, 'data', null, 'data', '0');

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1)
VALUES
    ('builtIn', '1', 3, 'ES备份管理', 'es-backup-manage', null, '0', '0', 'resource-manage', '1', 3, 3, 'ES备份管理', '1',
     'resource-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 4, 'ES备份', 'es-backup', null, '0', '0', 'es-backup-manage', '1', 1, 1, 'ES备份', '1',
     'es-backup-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 4, 'ES备份日志', 'es-backup-log', null, '0', '0', 'es-backup-manage', '1', 2, 2, 'ES备份日志',
     '1', 'es-backup-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 4, 'ES恢复', 'es-resume-log', null, '0', '0', 'es-backup-manage', '1', 3, 3, 'ES恢复', '1',
     'es-backup-manage', null, null, null, null, 'data', null, 'data', '0'),

    ('builtIn', '1', 3, 'Hive备份管理', 'hive-backup-manage', null, '0', '0', 'resource-manage', '1', 4, 4, 'Hive备份管理', '1',
     'resource-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 4, 'Hive备份', 'hive-backup', null, '0', '0', 'hive-backup-manage', '1', 1, 1, 'Hive备份', '1',
     'hive-backup-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 4, 'Hive备份日志', 'hive-backup-log', null, '0', '0', 'hive-backup-manage', '1', 2, 2, 'Hive备份日志',
     '1', 'hive-backup-manage', null, null, null, null, 'data', null, 'data', '0'),
    ('builtIn', '1', 4, 'Hive恢复', 'hive-resume-log', null, '0', '0', 'hive-backup-manage', '1', 3, 3, 'Hive恢复', '1',
     'hive-backup-manage', null, null, null, null, 'data', null, 'data', '0');