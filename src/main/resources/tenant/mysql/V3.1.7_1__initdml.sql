DELETE FROM ums_sys_menus WHERE menu_code IN ('subscription-manage','subscription-self','data-mart-home');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 2, '集市统计概览', 'data-mart-home', null, '0', '0', 'data-mart', '1', 1, 1, '集市统计概览', '1', null, null, null, null, null, 'data-mart', null, 'data-mart', null);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '2', '订阅管理', 'subscription-manage', NULL, '0', '0', 'data-mart', '1', '5', '5', '订阅管理', '1', NULL, NULL, NULL, NULL, NULL, 'data-mart', NULL, 'data-mart', NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`, `application_code`, `application`, `level1`) VALUES ('builtIn', '1', '2', '我的订阅', 'subscription-self', NULL, '0', '0', 'data-mart', '1', '3', '3', '我的订阅', '1', NULL, NULL, NULL, NULL, NULL, 'data-mart', NULL, 'data-mart', NULL);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '首页', 'data-api-home', '', '0', '0', 'data', '1', 10, 10, '首页', '1', 'data', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0');

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '逻辑表审批', 'table-audit', NULL, '0', '0', 'province-data-model', '1', 60, 60, '逻辑表审批', '1', 'province-data-model', NULL, NULL, NULL, NULL, 'data-lake-governance', NULL, 'data-lake-governance', '0');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataQualityAlarm', '数据质量告警', '1', NULL, '0', 'INSIGHT_ALARM_PLAT_OBJECT', NULL);
alter table insight_alarm_platform_action modify column webhook_url varchar(1024) null comment 'webhook地址';

alter table insight_alarm_platform_base add alarm_type int default 0 null comment '告警类型(0-自定义 1-内置)' after alarm_desc;
alter table insight_alarm_platform_base add allow_closed int default 1 null comment '是否允许关闭(0-不允许 1-允许)' after enable_status;
alter table insight_alarm_platform_base add alarm_code varchar(32) default 'custom' not null comment '规则code' after alarm_name;

alter table insight_alarm_platform_record add alarm_title varchar(128) default '采集预处理' null comment '告警记录标题' after id;
alter table insight_alarm_platform_record modify alarm_content varchar(512) default '' not null comment '告警内容';
alter table insight_alarm_platform_record add alarm_id int default 0 null comment '告警规则id' after id;
alter table insight_alarm_platform_record add hide_status int default 0 null comment '隐藏状态(0-不隐藏 1-隐藏)' after read_status;

delete from ums_sys_menus where menu_code = 'warn-platform-rule';
delete from ums_sys_menus where menu_code = 'warn-platform-rule-create';
delete from ums_sys_menus where menu_code = 'shell-manage';
delete from ums_sys_menus where menu_code = 'operate-shell-create';
delete from ums_sys_menus where menu_code = 'operate-shell-edit';
delete from ums_sys_menus where menu_code = 'operate-shell-detail';
delete from ums_sys_menus where menu_code = 'system-notice';
delete from ums_sys_menus where menu_code = 'warn';


INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '平台告警', 'notice', NULL, '0', '0', 'base', '1', 20, 20, '平台告警', '1', 'base', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '平台告警', 'system-notice', NULL, '0', '0', 'notice', '1', 10, 10, '平台告警', '1', 'notice', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '告警规则管理', 'warn-platform-rule', NULL, '0', '0', 'notice', '1', 20, 20, '告警规则管理', '1', 'notice', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '告警规则配置', 'warn-platform-rule-create', NULL, '0', '0', 'warn-platform-rule', '1', 10, 10, '告警规则配置', '1', 'warn-platform-rule', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0');

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '告警配置', 'warn-config', NULL, '0', '0', 'notice', '1', 30, 30, '告警配置', '1', 'notice', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '脚本管理', 'shell-manage', NULL, '0', '0', 'warn-config', '1', 10, 10, '脚本管理', '1', 'warn-config', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '新建脚本', 'operate-shell-create', NULL, '0', '0', 'shell-manage', '1', 10, 10, '新建脚本', '1', 'shell-manage', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '编辑脚本', 'operate-shell-edit', NULL, '0', '0', 'shell-manage', '1', 20, 20, '编辑脚本', '1', 'shell-manage', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '查看脚本', 'operate-shell-detail', NULL, '0', '0', 'shell-manage', '1', 30, 30, '查看脚本', '1', 'shell-manage', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '邮箱配置', 'email-manage', NULL, '0', '0', 'warn-config', '1', 20, 20, '邮箱配置', '1', 'warn-config', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0');

