delete from ums_sys_application where code = 'machine_learning';
INSERT INTO ums_sys_application(name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('机器学习', 'machine_learning', null, '1', null, '机器学习', null, 'report.png', null, null, null, '91', 0);
delete from ums_sys_menus where menu_code in ('machine_learning','image_manage','mirror_repository','mirror_manage','online_development','notebook_manage');
INSERT INTO ums_sys_menus(menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '机器学习', 'machine_learning', null, '0', '0', null, '1', 30, 40, '机器学习', '1', null, null, null, null, null, 'machine_learning', null, 'machine_learning', '1', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '镜像管理', 'image_manage', null, '0', '0', 'machine_learning', '1', 1, 1, '镜像管理', '1', 'machine_learning', null, null, null, null, 'machine_learning', null, 'machine_learning', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '镜像仓库', 'mirror_repository', null, '0', '0', 'image_manage', '1', 2, 2, '镜像仓库', '1', 'image_manage', null, null, null, null, 'machine_learning', null, 'machine_learning', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '镜像管理', 'mirror_manage', null, '0', '0', 'image_manage', '1', 1, 1, '镜像管理', '1', 'image_manage', null, null, null, null, 'machine_learning', null, 'machine_learning', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '在线开发', 'online_development', null, '0', '0', 'machine_learning', '1', 2, 2, '在线开发', '1', 'machine_learning', null, null, null, null, 'machine_learning', null, 'machine_learning', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'notebook管理', 'notebook_manage', null, '0', '0', 'online_development', '1', 1, 1, 'notebook管理', '1', 'online_development', null, null, null, null, 'machine_learning', null, 'machine_learning', '0', '0', 0);

delete from ums_sys_menus where menu_code in ('service-manage','model-manage','version-manage','reasoning-service');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 2, '服务管理', 'service-manage', null, '0', '0', 'machine_learning', '1', 4, 4, '服务管理', '1',
        'machine_learning', null, null, null, null, 'machine_learning', null, 'machine_learning', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, '模型管理', 'model-manage', null, '0', '0', 'service-manage', '1', 1, 1, '模型管理', '1',
        'service-manage', null, null, null, null, 'machine_learning', null, 'machine_learning', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, '版本管理', 'version-manage', null, '0', '0', 'service-manage', '1', 2, 2, '版本管理', '1',
        'service-manage', null, null, null, null, 'machine_learning', null, 'machine_learning', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, '推理服务', 'reasoning-service', null, '0', '0', 'service-manage', '1', 3, 3, '推理服务', '1',
        'service-manage', null, null, null, null, 'machine_learning', null, 'machine_learning', '0', '0', 0);