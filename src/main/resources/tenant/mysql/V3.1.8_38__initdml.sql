create table if not exists download_record
(
    id NOT NULL AUTO_INCREMENT,,
    file_name   varchar(1024)                  null comment '文件名',
    file_size   varchar(32)                    null comment '文件大小',
    status      varchar(2)                     null comment '文件生成状态，0-未开始；1-生成中；2-成功；3-失败',
    start_time  datetime                       null comment '文件创建时间',
    end_time    datetime                       null comment '完成时间',
    schedule    varchar(32)                    null comment '完成进度',
    module      varchar(64)                    null comment '下载模块',
    file_path   varchar(512)                   null comment '文件路径',
    log_path    varchar(512)                   null comment '日志文件路径',
    create_user varchar(64) default 'sysadmin' not null comment '创建人',
    PRIMARY KEY (id) USING BTREE
    ) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT='离线下载记录';