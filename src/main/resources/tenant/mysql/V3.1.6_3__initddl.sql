CREATE TABLE IF NOT EXISTS `insight_alarm_platform_action`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `alarm_id` int(11) NOT NULL COMMENT '平台告警标识',
  `email_address` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '目的邮箱地址',
  `carbon_copy_email_address` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '抄送邮箱地址',
  `blind_carbon_copy_email_address` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '密件抄送地址',
  `email_topic` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '邮件主题',
  `email_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '邮件内容',
  `email_csv_field` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '邮件csv字段',
  `email_link_status` int(11) NOT NULL DEFAULT 0 COMMENT '是否链接到url(0-否 1-是)',
  `syslog_url` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'syslog目的地址',
  `syslog_csv_field` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'syslog选择字段',
  `local_file_csv_field` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '本地文件字段',
  `phone_number` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '电话号码',
  `script_id` int(11) NULL DEFAULT NULL COMMENT '脚本id',
  `kafka_url` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'kafka地址',
  `kafka_topic` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'kafka的topic',
  `webhook_url` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'webhook地址',
  `phone_script_id` int(11) NULL DEFAULT NULL COMMENT '短信脚本id',
  `warn_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警名称',
  `classification` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警分类',
  `warn_level` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警级别',
  `warn_content` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警内容',
  `warn_effect` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警后果',
  `warn_solution` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警解决方案',
  `warn_field` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警字段',
  `bug_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '漏洞类型',
  `cve_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'CVE',
  `attack_type` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '攻击类型',
  `sys_alarm_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '系统告警维度',
  `sys_alarm_object` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '系统告警对象',
  `sys_alarm_serious` tinyint(3) NULL DEFAULT NULL COMMENT '系统告警严重性，1高，2中，3低',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '平台告警输出' ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `insight_alarm_platform_base`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '表标识',
  `alarm_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `alarm_desc` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `operator` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '操作人员',
  `enable_status` int(11) NOT NULL DEFAULT 1 COMMENT '启用状态(0-未启用 1-已经启用)',
  `alarm_config` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '告警配置',
  `alarm_object` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '告警对象',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `copy_count` int(11) NULL DEFAULT 0 COMMENT '复制次数',
  `execute_type` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模型运行类型：hour-每小时，day-每天，week-每周，month-每月，cron-表达式',
  `execute_value` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模型运行值',
  `execute_cron` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'cron表达式',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '平台告警配置' ROW_FORMAT = DYNAMIC;


CREATE TABLE IF NOT EXISTS `insight_alarm_platform_record`  (
  `id` int(32) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '表标识',
  `alarm_content` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '告警内容',
  `alarm_serious` tinyint(3) NOT NULL COMMENT '告警严重性，1高，2中，3低',
  `alarm_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '告警类型',
  `alarm_time` datetime(0) NOT NULL COMMENT '创建时间',
  `sys_alarm_user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '系统告警用户',
  `read_status` tinyint(3) NOT NULL COMMENT '已读状态，0未读，1已读',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '已读操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user`(`sys_alarm_user`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '平台告警记录表' ROW_FORMAT = DYNAMIC;


CREATE TABLE IF NOT EXISTS `insight_alarm_email_content_temp`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `topic` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '主题',
  `template_desc` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `email_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '邮件内容',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `early_warn_agg`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `agg_field` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '汇总列名',
  `agg_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '汇总项名称',
  `dict_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字典项',
  `enum_type` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '枚举项',
  `create_user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '预警规则汇总项' ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `early_warn_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `classification` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警分类',
  `warn_level` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警级别',
  `warn_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警名称',
  `warn_time` datetime NULL DEFAULT NULL COMMENT '预警时间',
  `warn_target` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警对象',
  `department` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警部门',
  `warn_content` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警内容',
  `source_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '来源类型',
  `warn_source` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警来源',
  `warn_status` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警状态',
  `cve_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'CVEID',
  `bug_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '漏洞类型',
  `attack_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '攻击类型',
  `warn_effect` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警后果',
  `warn_solution` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警解决方案',
  `warn_file_path` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建预警上传文件',
  `device_ref` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联设备',
  `service_def` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联服务',
  `audit_submit_user` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '提交审核人',
  `audit_submit_time` datetime NULL DEFAULT NULL COMMENT '提交审核时间',
  `audit_submit_result` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '提交审核原因',
  `audit_submit_file_path` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '提交审核附件路径',
  `audit_user` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审核人',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `audit_result` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审核结果',
  `audit_desc` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审核描述',
  `audit_file_path` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审核附件路径',
  `release_user` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发布人',
  `release_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `warn_flag` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警删除状态(0-删除 1-正常)',
  `create_user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '过滤条件所属用户',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `type` varchar(8) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `start_time` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开始时间',
  `sync_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警id',
  `doc_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预警通报报告',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '预警信息表' ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `early_warn_opt_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `early_warn_id` bigint(20) NOT NULL COMMENT '预警ID',
  `opt_user` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人',
  `opt_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `opt_type` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作类型',
  `opt_content` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作说明',
  `create_user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '过滤条件所属用户',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '预警规则操作记录表' ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `ums_sys_script` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `script_name` varchar(32) NOT NULL DEFAULT '' COMMENT '脚本名称',
  `script_desc` varchar(256) NOT NULL DEFAULT '' COMMENT '脚本描述',
  `script_content` text COMMENT '脚本内容',
  `status` varchar(2) NOT NULL DEFAULT '' COMMENT '0-删除 1-正常',
  `create_user` varchar(128) NOT NULL DEFAULT '' COMMENT '创建账号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_alert` varchar(1) DEFAULT '0' COMMENT '是否是工单脚本:1-是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='脚本管理表';

CREATE TABLE IF NOT EXISTS `ums_sys_script_opt_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `script_id` int(11) DEFAULT NULL COMMENT '脚本id',
  `opt_account` varchar(128) NOT NULL DEFAULT '' COMMENT '操作账号',
  `opt_type` varchar(16) NOT NULL DEFAULT '' COMMENT '操作类型',
  `opt_object` varchar(16) NOT NULL DEFAULT '' COMMENT '操作对象',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='脚本操作日志';

ALTER TABLE ums_sys_application ADD in_iframe INT DEFAULT 0;

ALTER TABLE ums_sys_auth_config ADD COLUMN fa_vendor varchar(64) DEFAULT 'asiaInfoSec' COMMENT '4A厂商 默认亚信安全';


