ALTER TABLE ums_sys_password_policy ADD COLUMN `forbid_ip` varchar(1024) DEFAULT NULL COMMENT '限制远程登录地址';
ALTER TABLE ums_sys_password_policy ADD COLUMN `forbid_ip_open` varchar(1) DEFAULT NULL COMMENT '限制远程登录地址是否开启：0-否，1-是';
UPDATE ums_sys_menus SET default_parent = parent_name;

UPDATE ums_sys_application SET is_active = '1' WHERE code = 'application';

CREATE TABLE  IF NOT EXISTS `model_action_session_base_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL DEFAULT '' COMMENT '行为会话名称',
  `action_session_desc` varchar(1024) NOT NULL DEFAULT '' COMMENT '描述',
  `datasource` varchar(128) NOT NULL DEFAULT '' COMMENT '数据源',
  `table_name` varchar(128) NOT NULL DEFAULT '' COMMENT '行为会话表名',
  `cluster_name` varchar(64) DEFAULT '' COMMENT '集群名称',
  `time_interval_unit` varchar(16) NOT NULL DEFAULT '' COMMENT '时间间隔单位',
  `store_time` int(11) NOT NULL COMMENT '存储时限',
  `store_time_unit` varchar(8) NOT NULL DEFAULT '' COMMENT '存储时限单位',
  `enable_advanced` int(11) NOT NULL COMMENT '是否启用高级配置  0-未启用  1-启用',
  `action_session_sql` text COMMENT '行为会话对应的SQL',
  `action_session_table` text COMMENT '行为会话对应的建表SQL',
  `session_object` varchar(32) NOT NULL DEFAULT '' COMMENT '会话对象',
  `max_session_interval_time` int(11) DEFAULT NULL COMMENT '会话事件最大时间间隔',
  `max_session_interval_time_unit` varchar(16) NOT NULL DEFAULT '' COMMENT '会话事件最大时间间隔单位',
  `operation_type` varchar(32) NOT NULL DEFAULT '' COMMENT '操作类型',
  `create_time_field` varchar(32) DEFAULT 'create_time' COMMENT '创建时间字段名',
  `status` int(11) DEFAULT NULL COMMENT '状态（0 表示禁用，1表示启用）',
  `task_updatetime` datetime DEFAULT NULL COMMENT '任务更新时间',
  `task_result` int(11) DEFAULT NULL COMMENT '执行结果(0-执行失败，1-执行成功)',
  `task_status` int(11) DEFAULT NULL COMMENT '任务状态 1-运行中 2-运行结束 3-上线 4-下线',
  `task_nexttime` datetime DEFAULT NULL COMMENT '任务下次执行时间',
  `create_user` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(64) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `agg_time` int(11) DEFAULT NULL COMMENT '时间聚合',
  `agg_time_unit` varchar(16) DEFAULT NULL COMMENT '时间聚合单位',
  `time_interval` int(11) DEFAULT '1' COMMENT '间隔时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8 COMMENT='行为会话基础信息表';

CREATE TABLE  IF NOT EXISTS `model_action_session_filter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action_session_id` bigint(20) NOT NULL COMMENT 'action session ID',
  `field` varchar(128) DEFAULT NULL COMMENT '过滤字段',
  `field_type` varchar(16) DEFAULT NULL COMMENT '字段类型',
  `filter_type` varchar(16) DEFAULT NULL COMMENT '过滤类型',
  `filter_value` varchar(512) DEFAULT NULL COMMENT '过滤值',
  `logic` varchar(8) DEFAULT NULL COMMENT '逻辑运算符',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父节点ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据源过滤表';

CREATE TABLE  IF NOT EXISTS `model_action_session_tasklog` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action_session_id` bigint(20) NOT NULL COMMENT 'action session ID',
  `start_time` datetime DEFAULT NULL COMMENT '任务开始执行时间',
  `end_time` datetime DEFAULT NULL COMMENT '任务执行结束时间',
  `run_status` int(11) DEFAULT NULL COMMENT '运行状态 1-运行中 2-运行结束',
  `run_result` int(11) DEFAULT NULL COMMENT '运行结果  0-运行失败 1-运行成功',
  `exec_start_time` varchar(32) DEFAULT NULL COMMENT '入参开始时间',
  `exec_end_time` varchar(32) DEFAULT NULL COMMENT '入参结束时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='行为会话任务执行记录表';

CREATE TABLE  IF NOT EXISTS `etl_transform_dict` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `dict_name` varchar(128) NOT NULL COMMENT '字典名',
  `cn_dict_name` varchar(128) NOT NULL COMMENT '字典中文名',
  `dict_desc` varchar(128) DEFAULT NULL COMMENT '描述',
  `status` int(11) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `from_source` int(11) DEFAULT '1' COMMENT '来源（1 配置字典，2导入字典）',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(256) DEFAULT NULL COMMENT '创建者',
  `update_user` varchar(256) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='字典基础表';
insert into etl_transform_dict(dict_name,cn_dict_name,dict_desc,create_user,update_user,create_time,update_time)
select distinct table_name,cn_table_name,table_desc,'sysadmin','sysadmin',now(),now() from etl_transform_table;

CREATE TABLE  IF NOT EXISTS `etl_transform_dict_field` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `dict_name` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '字段名称',
  `field_name` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '字段名',
  `field_type` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '字段类型',
  `field_alias_name` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '字段别名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='字典和字段映射表';
insert into etl_transform_dict_field(dict_name,field_name) select  table_name,field_name from etl_transform_table;

CREATE TABLE  IF NOT EXISTS `etl_transform_dict_data` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `dict_name` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '字典名称',
  `k1` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k1',
  `k2` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k2',
  `k3` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k3',
  `k4` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k4',
  `k5` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k5',
  `k6` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k6',
  `k7` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k7',
  `k8` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k8',
  `k9` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k9',
  `k10` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k10',
   `k11` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k11',
  `k12` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k12',
  `k13` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k13',
  `k14` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k14',
  `k15` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k15',
  `k16` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k16',
  `k17` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k17',
  `k18` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k18',
  `k19` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k19',
  `k20` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'k20',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='字段映射表';

INSERT INTO `dataset_authority`(dataset_id,user_name) SELECT `id`,'sysadmin' FROM `dataset_base`;

alter table explore_query_history add extra_condition varchar(2048) charset utf8 null comment '额外的查询条件' after end_time;

alter table etl_transform_function add column parent_function varchar(32) default null comment '父级';
alter table dataset_base add column is_default varchar(2) default '0' comment '是否内置:0-否；1-是';
alter table system_clickhouse_config add column is_secret_free int default 0 comment '是否免密(0-否 1-是)';
alter table system_clickhouse_config add column secret_file_path varchar(128) comment '是否免密(0-否 1-是)';
alter table system_clickhouse_config add column secret_file_password varchar(128) comment '是否免密(0-否 1-是)';
alter table explore_selected_field add agg_fields varchar(2048) default '' not null comment '聚合字段' after selected_fields;
alter table etl_logmoudle add column open_port varchar(32) default '' comment '开放端口';
alter table etl_logmoudle add column resource_pool varchar(128) default '未知' comment '资源池';
alter table etl_logmoudle add column pod varchar(128) default '未知' comment 'pod';
alter table etl_logmoudle add column network_domain varchar(128) default '未知' comment '网络域';
alter table etl_logmoudle add column host_type varchar(128) default '' comment '主机类型';
alter table etl_logmoudle add column manage_ip varchar(128) default '' comment '管理ip';
alter table etl_logmoudle add column carry_network_ip varchar(128) default '' comment '承载网ip';
alter table etl_logmoudle add column vpc_network_segment varchar(128) default '' comment 'VPC网段';
ALTER TABLE etl_transform_condition MODIFY compare_content VARCHAR(512) NULL COMMENT '待比较的表字段或值';
ALTER TABLE explore_selected_field MODIFY selected_fields TEXT NULL COMMENT '已选字段 多个字段用逗号分隔';
ALTER TABLE etl_transform_dict_data MODIFY k1 varchar(512) DEFAULT '' NOT NULL COMMENT 'k1';
ALTER TABLE etl_transform_dict_data MODIFY k2 varchar(512) DEFAULT '' NOT NULL COMMENT 'k2';
ALTER TABLE etl_transform_dict_data MODIFY k3 varchar(512) DEFAULT '' NOT NULL COMMENT 'k3';
ALTER TABLE etl_transform_dict_data MODIFY k4 varchar(512) DEFAULT '' NOT NULL COMMENT 'k4';
ALTER TABLE etl_transform_dict_data MODIFY k5 varchar(512) DEFAULT '' NOT NULL COMMENT 'k5';
ALTER TABLE etl_transform_dict_data MODIFY k6 varchar(512) DEFAULT '' NOT NULL COMMENT 'k6';
ALTER TABLE etl_transform_dict_data MODIFY k7 varchar(512) DEFAULT '' NOT NULL COMMENT 'k7';
ALTER TABLE etl_transform_dict_data MODIFY k8 varchar(512) DEFAULT '' NOT NULL COMMENT 'k8';
ALTER TABLE etl_transform_dict_data MODIFY k9 varchar(512) DEFAULT '' NOT NULL COMMENT 'k9';
ALTER TABLE etl_transform_dict_data MODIFY k10 varchar(512) DEFAULT '' NOT NULL COMMENT 'k10';
ALTER TABLE etl_transform_dict_data MODIFY k11 varchar(512) DEFAULT '' NOT NULL COMMENT 'k11';
ALTER TABLE etl_transform_dict_data MODIFY k12 varchar(512) DEFAULT '' NOT NULL COMMENT 'k12';
ALTER TABLE etl_transform_dict_data MODIFY k13 varchar(512) DEFAULT '' NOT NULL COMMENT 'k13';
ALTER TABLE etl_transform_dict_data MODIFY k14 varchar(512) DEFAULT '' NOT NULL COMMENT 'k14';
ALTER TABLE etl_transform_dict_data MODIFY k15 varchar(512) DEFAULT '' NOT NULL COMMENT 'k15';
ALTER TABLE etl_transform_dict_data MODIFY k16 varchar(512) DEFAULT '' NOT NULL COMMENT 'k16';
ALTER TABLE etl_transform_dict_data MODIFY k17 varchar(512) DEFAULT '' NOT NULL COMMENT 'k17';
ALTER TABLE etl_transform_dict_data MODIFY k18 varchar(512) DEFAULT '' NOT NULL COMMENT 'k18';
ALTER TABLE etl_transform_dict_data MODIFY k19 varchar(512) DEFAULT '' NOT NULL COMMENT 'k19';
ALTER TABLE etl_transform_dict_data MODIFY k20 varchar(512) DEFAULT '' NOT NULL COMMENT 'k20';
ALTER TABLE etl_transform_action MODIFY dest_table varchar(4096);