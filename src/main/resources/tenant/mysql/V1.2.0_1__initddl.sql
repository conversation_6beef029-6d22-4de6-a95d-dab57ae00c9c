CREATE TABLE IF NOT EXISTS `etl_task_day_statistics`(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `day`         varchar(100) NOT NULL COMMENT '日期',
    `task_count`  bigint(20) DEFAULT NULL COMMENT '采集任务数量',
    `create_time` datetime DEFAULT NULL COMMENT '入库时间',
    `flag`        int(11) DEFAULT '1' COMMENT '1有效，0无效',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4 COMMENT='数据采集任务趋势日统计表';