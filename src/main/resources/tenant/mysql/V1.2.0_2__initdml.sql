#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 2, '资源管理', 'cluster-resource-manage', NULL, '0', '0', 'system', '1', 1, 80, '资源管理', '1', 'system', NULL, NULL, NULL, NULL, 'system');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 3, '集群管理', 'cluster-manage', NULL, '0', '0', 'cluster-resource-manage', '1', 1, 10, '集群管理', '1', 'resource-manage', NULL, NULL, NULL, NULL, 'system');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 2, '安全数据管理', 'safe-data-manage', NULL, '0', '0', 'data-lake-governance', '1', 20, 20, '安全数据管理', '1', 'data-lake-governance', NULL, NULL, NULL, NULL, 'data-lake-governance');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 3, '资产目录', 'asset-catalog', NULL, '0', '0', 'safe-data-manage', '1', 10, 10, '资产目录', '1', 'data-lake-governance-meta', NULL, NULL, NULL, NULL, 'data-lake-governance');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 3, 'Kafka详情', 'kafka-detail', NULL, '0', '0', 'cluster-resource-manage', '1', 1, 70, 'Kafka详情', '1', 'resource-manage', NULL, NULL, NULL, NULL, 'system');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 3, '资产用途', 'data-lake-governance-asset-use', NULL, '0', '0', 'data-lake-governance-config', '1', 30, 30, '资产用途', '1', 'data-lake-governance-config', NULL, NULL, NULL, NULL, 'data-lake-governance');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 3, '敏感分级', 'data-lake-governance-sen-level', NULL, '0', '0', 'data-lake-governance-config', '1', 30, 30, '敏感分级', '1', 'data-lake-governance-config', NULL, NULL, NULL, NULL, 'data-lake-governance');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 3, '敏感分类', 'data-lake-governance-sen-classification', NULL, '0', '0', 'data-lake-governance-config', '1', 30, 30, '敏感分类', '1', 'data-lake-governance-config', NULL, NULL, NULL, NULL, 'data-lake-governance');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 2, '模型调度', 'scheduler-configuration', NULL, '0', '0', 'model', '1', 50, 50, '模型调度', '1', 'data-storage-calculation', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 3, '任务流管理', 'definition-list', NULL, '0', '0', 'scheduler-configuration', '1', 1, 1, '任务流管理', '1', 'scheduler-configuration', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 3, '运行实例', 'instance-list', NULL, '0', '0', 'scheduler-configuration', '1', 20, 20, '运行实例', '1', 'scheduler-configuration', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 3, '模型文件管理', 'fileManagement-list', NULL, '0', '0', 'scheduler-configuration', '1', 30, 30, '模型文件管理', '1', 'scheduler-configuration', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 1, '数据存储计算', 'model', NULL, '0', '0', NULL, '1', 60, 60, '数据存储计算', '1', NULL, NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 3, '安全数据资产汇总', 'asset-collect', NULL, '0', '0', 'safe-data-manage', '1', 10, 10, '安全数据资产汇总', '1', 'data-lake-governance-meta', NULL, NULL, NULL, NULL, 'data-lake-governance');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 4, '任务流图', 'definition-tree', NULL, '1', '0', 'definition-list', '1', 1, 1, '任务流图', '1', 'definition-list', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 4, '创建任务流', 'definition-create', NULL, '1', '0', 'definition-list', '1', 20, 20, '创建任务流', '1', 'definition-list', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 4, '任务流配置', 'definition-flow-detail', NULL, '1', '0', 'definition-list', '1', 30, 30, '任务流配置', '1', 'definition-list', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 4, '定时管理', 'definition-timing', NULL, '1', '0', 'definition-list', '1', 40, 40, '定时管理', '1', 'definition-list', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 4, '任务进展', 'instance-gantt', NULL, '1', '0', 'instance-list', '1', 1, 1, '任务进展', '1', 'instance-list', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 4, '任务流配置', 'instance-flow-detail', NULL, '1', '0', 'instance-list', '1', 20, 20, '任务流配置', '1', 'instance-list', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 4, '新建文件', 'fileManagement-create-file', NULL, '1', '0', 'fileManagement-list', '1', 1, 1, '新建文件', '1', 'fileManagement-list', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 4, '编辑文件', 'fileManagement-file-edit', NULL, '1', '0', 'fileManagement-list', '1', 20, 20, '编辑文件', '1', 'fileManagement-list', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 4, '查看文件', 'fileManagement-file-detail', NULL, '1', '0', 'fileManagement-list', '1', 30, 30, '查看文件', '1', 'fileManagement-list', NULL, NULL, NULL, NULL, 'model');
#INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent) VALUES('builtIn', '1', 3, '数据源配置管理', 'db-source-config', NULL, '0', '0', 'cluster-resource-manage', '1', 40, 40, '数据源配置管理', '1', 'cluster-resource-manage', NULL, NULL, NULL, NULL, 'model');
