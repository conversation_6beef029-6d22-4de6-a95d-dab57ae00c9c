INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 2, '融合建模', 'fuse-modeling', NULL, '0', '0', 'data-development-inner', '1', 6, 6, '融合建模', '1', 'data-development-inner', NULL, NULL, NULL, NULL, 'data-development-inner', NULL, 'data-development-inner', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '向导式建模-新建', 'guide-fuse-modeling-add', NULL, '0', '1', 'guide-fuse-modeling', '1', 1, 1, '向导式建模-新建', '1', 'guide-fuse-modeling', NULL, NULL, NULL, NULL, 'data-development-inner', NULL, 'data-development-inner', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '向导式建模-编辑', 'guide-fuse-modeling-edit', NULL, '0', '1', 'guide-fuse-modeling', '1', 2, 2, '向导式建模-编辑', '1', 'guide-fuse-modeling', NULL, NULL, NULL, NULL, 'data-development-inner', NULL, 'data-development-inner', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '向导式建模-查看', 'guide-fuse-modeling-examine', NULL, '0', '1', 'guide-fuse-modeling', '1', 3, 3, '向导式建模-查看', '1', 'guide-fuse-modeling', NULL, NULL, NULL, NULL, 'data-development-inner', NULL, 'data-development-inner', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '编排式建模', 'arrange-fuse-modeling', NULL, '0', '0', 'fuse-modeling', '1', 2, 2, '编排式建模', '1', 'fuse-modeling', NULL, NULL, NULL, NULL, 'data-development-inner', NULL, 'data-development-inner', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '向导式建模', 'guide-fuse-modeling', NULL, '0', '0', 'fuse-modeling', '1', 1, 1, '向导式建模', '1', 'fuse-modeling', NULL, NULL, NULL, NULL, 'data-development-inner', NULL, 'data-development-inner', '0');
