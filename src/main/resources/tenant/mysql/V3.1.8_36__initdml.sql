alter table etl_writer_table_config add column field_comment varchar(500) comment '字段注释';
alter table ums_sys_role_ch_es add column source_tenant_id int comment '源租户ID';
alter table explore_query_history add source_tenant_id int null comment '数据源租户ID' after table_name;

CREATE TABLE `etl_logmodule_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tag_name` varchar(512) DEFAULT NULL COMMENT '标签名称',
  `tag_desc` varchar(1024) DEFAULT NULL COMMENT '标签描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(128) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(128) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `etl_logmodule_tag_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `logmodule_id` bigint(20) DEFAULT NULL COMMENT '采集节点ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(128) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(128) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE etl_logmoudle ADD COLUMN logmodule_tag varchar(512) COMMENT '标签';