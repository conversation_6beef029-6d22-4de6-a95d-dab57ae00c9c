update  ums_sys_menus set menu_code = 'etl-monitor' where menu_code = 'collect-control';
delete from ums_sys_menus where menu_code = 'click-house-monitor';
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,parent_name, status, menu_order, default_order, default_name, default_status, default_parent,create_user, create_date, update_user, update_date, root_parent, application_code,application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '运维监控', 'click-house-monitor', null, '0', '0', 'insight', '1', 20, 20, '运维监控', '1','insight', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
delete from ums_sys_menus where menu_code = 'server-monitor';
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,parent_name, status, menu_order, default_order, default_name, default_status, default_parent,create_user, create_date, update_user, update_date, root_parent, application_code,application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '运维监控', 'server-monitor',NULL,'0','1','click-house-monitor','1',20,2,'运维监控','1','click-house-monitor',NULL,NULL,NULL,NULL, 'insight', null, 'insight', '0', '0', 0);
