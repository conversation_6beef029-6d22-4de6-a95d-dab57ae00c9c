INSERT INTO `ums_sys_role` VALUES
('140d6cf17d244e74855ecdd56c40335c','系统管理员','',0,0,'Admin',REPLACE(UNIX_TIMESTAMP(now(3)),'.',''),'系统管理员',REPLACE(UNIX_TIMESTAMP(now(3)),'.',''),'0');

INSERT INTO `t_ds_tenant` (`tenant_code`, `tenant_name`, `description`, `queue_id`, `create_time`, `update_time`) VALUES ('ueba', 'ueba', 'ueba-job', '14', '2020-04-08 17:55:01', '2020-05-07 16:40:46');

INSERT INTO `ums_sys_work_time` VALUES (1, '08:00', '18:00');

INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '1', 'system', '系统管理', 'system', NULL, '0', '0', NULL, '1', '70', '70', '系统', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '2', 'system', '账号管理', 'system-account', NULL, '0', '0', 'system', '1', '20', '20', '账号管理', '1', 'system', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '2', 'system', '角色管理', 'role', NULL, '0', '0', 'system', '1', '30', '30', '角色管理', '1', 'system', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', 2, 'system', 'API管理', 'manage-api', '', '0', '0', 'system', '1', 10, 10, 'API管理', '1', 'system', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', 3, 'system', 'API配置', 'manage-data-api', '', '0', '0', 'manage-api', '1', 10, 10, 'API配置', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', 3, 'system', '生成API', 'manage-data-api-create', '', '0', '0', 'manage-api', '1', 20, 20, '生成API', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', 3, 'system', '生成API-模式选择', 'manage-data-new-api', '', '0', '0', 'manage-api', '1', 30, 30, '生成API-模式选择', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', 3, 'system', '注册API', 'manage-data-api-register', '', '0', '0', 'manage-api', '1', 40, 40, '注册API', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', 3, 'system', 'API授权', 'client-manage', '', '0', '0', 'manage-api', '1', 50, 50, 'API授权', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', 3, 'system', '数据源管理', 'source-manage', '', '0', '0', 'manage-api', '1', 60, 60, '数据源管理', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', 3, 'system', '项目管理', 'project-manage', '', '0', '0', 'manage-api', '1', 70, 70, '项目管理', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', 3, 'system', '安全组', 'security-group', '', '0', '0', 'manage-api', '1', 80, 80, '安全组', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '1', 'data', '数据共享', 'data', NULL, '0', '0', NULL, '1', '50', '50', '数据共享', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '2', 'data', '安全日志检索', 'dataExplore', NULL, '0', '0', 'data', '1', '1', '1', '安全日志检索', '1', 'data', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '2', 'data', '安全备份服务', 'resource-manage', NULL, '0', '0', 'data', '1', '20', '20', '安全备份服务', '1', 'data', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'data', 'CH备份', 'backup-manage', NULL, '0', '0', 'resource-manage', '1', '10', '10', '备份管理', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'data', 'CH备份日志', 'backup-date-log', NULL, '0', '0', 'resource-manage', '1', '20', '20', '备份日志', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'data', 'CH恢复', 'data-resume', NULL, '0', '0', 'resource-manage', '1', '30', '30', '数仓恢复', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'data', 'Mysql备份', 'config-info', NULL, '0', '0', 'resource-manage', '1', '40', '40', 'Mysql备份', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'data', '文件服务配置', 'file-service', NULL, '0', '0', 'resource-manage', '1', '50', '50', '文件服务配置', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '2', 'data', '自定义模型', 'dataMart', NULL, '0', '0', 'data', '1', '40', '40', '自定义模型', '1', 'data', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'data', '数据集管理', 'dataset', NULL, '0', '0', 'dataMart', '1', '20', '20', '数据集管理', '1', 'dataMart', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'data', '自定义模型脚本', 'model-explore', NULL, '0', '0', 'dataMart', '1', '1', '1', '自定义模型脚本', '1', 'dataMart', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'data', '分析报表', 'report-manage', NULL, '0', '0', 'dataMart', '1', '30', '30', '分析报表', '1', 'dataMart', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '2', 'data', '结构化数据', 'struct-data', NULL, '0', '0', 'data', '1', '30', '30', '结构化数据', '1', 'data', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'data', '数据源管理', 'zeppelin-interpreter', NULL, '0', '0', 'struct-data', '1', '10', '10', '数据源管理', '1', 'struct-data', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'data', 'notebook', 'zeppelin-notebook', NULL, '0', '0', 'struct-data', '1', '20', '20', 'notebook', '1', 'struct-data', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'data', '解释器查询结果', 'zeppelin-paragraph', NULL, '0', '0', 'struct-data', '1', '30', '30', '解释器查询结果', '1', 'struct-data', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '1', 'insight', '数据采集', 'insight', NULL, '0', '0', NULL, '1', '20', '20', '数据采集', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '2', 'insight', '数据采集', 'data-access', NULL, '0', '0', 'insight', '1', '20', '20', '数据采集', '1', 'insight', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'insight', '数据采集管理', 'dataAccessManagement', NULL, '0', '0', 'data-access', '1', '1', '1', '数据采集管理', '1', 'data-access', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '4', 'insight', '数据采集配置', 'manage-data-input', NULL, '1', '0', 'dataAccessManagement', '1', '1', '1', '数据采集配置', '1', 'dataAccessManagement', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '4', 'insight', '数据转换', 'dataConversion', NULL, '1', '0', 'dataAccessManagement', '1', '20', '20', '数据转换', '1', 'dataAccessManagement', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '4', 'insight', '输出配置页面', 'complete', NULL, '1', '0', 'dataAccessManagement', '1', '30', '30', '输出配置页面', '1', 'dataAccessManagement', NULL, NULL, 'sysadmin', '2021-12-01 10:13:50');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'insight', '采集节点管理', 'manage-logmodule', NULL, '0', '0', 'data-access', '1', '20', '20', '采集节点管理', '1', 'data-access', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'insight', '解析规则管理', 'manage-parsing-rules', NULL, '0', '0', 'data-access', '1', '30', '30', '解析规则管理', '1', 'data-access', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '4', 'insight', '解析规则详情', 'manage-parsing-rules-detail', NULL, '1', '0', 'manage-parsing-rules', '1', '1', '1', '解析规则详情', '1', 'manage-parsing-rules', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '4', 'insight', '新建解析规则', 'manage-parsing-rules-create', NULL, '1', '0', 'manage-parsing-rules', '1', '20', '20', '新建解析规则', '1', 'manage-parsing-rules', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '4', 'insight', '解析规则配置', 'manage-parsing-rules-edit', NULL, '1', '0', 'manage-parsing-rules', '1', '30', '30', '解析规则配置', '1', 'manage-parsing-rules', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'insight', '动态补全配置管理', 'manage-dynamic-completion', NULL, '0', '0', 'data-access', '1', '40', '40', '动态补全配置管理', '1', 'data-access', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'insight', '采集脚本', 'manage-collection-script', NULL, '0', '0', 'data-access', '1', '50', '50', '采集脚本', '1', 'data-access', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '4', 'insight', '脚本详情', 'manage-collection-script-detail', NULL, '1', '0', 'manage-collection-script', '1', '1', '1', '脚本详情', '1', 'manage-collection-script', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '4', 'insight', '新增脚本', 'manage-collection-script-create', NULL, '1', '0', 'manage-collection-script', '1', '20', '20', '新增脚本', '1', 'manage-collection-script', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '4', 'insight', '编辑脚本', 'manage-collection-script-edit', NULL, '1', '0', 'manage-collection-script', '1', '30', '30', '编辑脚本', '1', 'manage-collection-script', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'insight', '宽表字典', 'ueba-dictionary', NULL, '0', '0', 'data-access', '1', '60', '60', '宽表字典', '1', 'data-access', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'insight', '场景视图', 'ueba-view', NULL, '0', '0', 'data-access', '1', '70', '70', '场景视图', '1', 'data-access', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'insight', '源设备分类管理', 'source-device', NULL, '0', '0', 'data-access', '1', '80', '80', '源设备分类管理', '1', 'data-access', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '4', 'insight', '视图详情', 'ueba-view-detail', NULL, '1', '0', 'ueba-view', '1', '1', '1', '视图详情', '1', 'ueba-view', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '1', '数据治理', 'data-lake-governance', NULL, '0', '0', NULL, '1', '40', '40', '数据治理', '1', NULL, NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '2', '元数据管理', 'data-lake-governance-meta', NULL, '0', '0', 'data-lake-governance', '1', '10', '10', '元数据管理', '1', 'data-lake-governance', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '3', '元数据采集', 'data-lake-governance-meta-access', NULL, '0', '0', 'data-lake-governance-meta', '1', '10', '10', '元数据采集', '1', 'data-lake-governance-meta', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '3', '元数据查询', 'data-lake-governance-meta-search', NULL, '0', '0', 'data-lake-governance-meta', '1', '20', '20', '元数据查询', '1', 'data-lake-governance-meta', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '2', '数据标准管理', 'data-lake-governance-standard', NULL, '0', '0', 'data-lake-governance', '1', '20', '20', '数据标准管理', '1', 'data-lake-governance', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '2', '数据质量管理', 'data-lake-governance-quality', NULL, '0', '0', 'data-lake-governance', '1', '30', '30', '数据质量管理', '1', 'data-lake-governance', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '3', '稽核数据源管理', 'data-lake-governance-quality-model', NULL, '0', '0', 'data-lake-governance-quality', '1', '30', '10', '稽核数据源管理', '1', 'data-lake-governance-quality', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '3', '模型管理', 'data-lake-governance-quality-rule', NULL, '0', '0', 'data-lake-governance-quality', '1', '20', '20', '模型管理', '1', 'data-lake-governance-quality', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '4', '任务管理', 'data-lake-governance-quality-task', NULL, '0', '0', 'data-lake-governance-quality', '1', '10', '30', '任务管理', '1', 'data-lake-governance-quality', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '5', '质量监测结果', 'data-lake-governance-quality-result', NULL, '0', '0', 'data-lake-governance-quality-task', '1', '40', '40', '质量监测结果', '1', 'data-lake-governance-quality-task', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '2', '主数据管理', 'data-lake-governance-masterData', NULL, '0', '0', 'data-lake-governance', '1', '40', '40', '主数据管理', '1', 'data-lake-governance', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '3', '主数据模型', 'data-lake-governance-masterData-model', NULL, '0', '0', 'data-lake-governance-masterData', '1', '10', '10', '主数据模型', '1', 'data-lake-governance-masterData', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '4', '主数据维护', 'data-lake-governance-masterData-maintain', NULL, '0', '0', 'data-lake-governance-masterData-model', '1', '20', '20', '主数据维护', '1', 'data-lake-governance-masterData-model', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '2', '配置管理', 'data-lake-governance-config', NULL, '0', '0', 'data-lake-governance', '1', '60', '60', '配置管理', '1', 'data-lake-governance', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '3', '数据业务类型', 'data-lake-governance-config-business', NULL, '0', '0', 'data-lake-governance-config', '1', '20', '20', '数据业务类型', '1', 'data-lake-governance-config', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '3', '业务数据类型分组', 'data-lake-governance-config-grouping', NULL, '0', '0', 'data-lake-governance-config', '1', '10', '10', '业务数据类型分组', '1', 'data-lake-governance-config', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '3', '主数据类型', 'data-lake-governance-config-data', NULL, '0', '0', 'data-lake-governance-config', '1', '30', '30', '主数据类型', '1', 'data-lake-governance-config', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '3', '采集监控', 'collect-control', NULL, '0', '0', 'data-access', '1', '5', '5', '采集监控', '1', 'data-access', NULL, NULL, NULL, NULL, 'insight');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '2', '其它配置', 'system-config-logo', NULL, '0', '0', 'system', '1', '40', '40', '其它配置', '1', 'system', NULL, NULL, NULL, NULL, 'system');
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '2', '菜单管理', 'menu-manage', NULL, '0', '0', 'system', '1', '50', '50', '菜单管理', '1', 'system', NULL, NULL, NULL, NULL, 'system');


INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('second', '秒前', '1', NULL, '1', 'RELATIVE_TIME', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('minute', '分钟前', '1', NULL, '2', 'RELATIVE_TIME', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('hour', '小时前', '1', NULL, '3', 'RELATIVE_TIME', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('day', '天前', '1', NULL, '4', 'RELATIVE_TIME', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('week', '周前', '1', NULL, '5', 'RELATIVE_TIME', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('month', '月前', '1', NULL, '6', 'RELATIVE_TIME', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('quarter', '季度前', '1', NULL, '7', 'RELATIVE_TIME', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('year', '年前', '1', NULL, '8', 'RELATIVE_TIME', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('UTF-8', 'UTF-8', '1', NULL, '1', 'CHARSET', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('GBK', 'GBK', '1', NULL, '2', 'CHARSET', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Unicode', 'Unicode', '1', NULL, '3', 'CHARSET', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('1', '修改配置', '1', NULL, '1', 'SOURCE_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('2', '开启配置', '1', NULL, '2', 'SOURCE_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('3', '关闭配置', '1', NULL, '3', 'SOURCE_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('4', '删除配置', '1', NULL, '4', 'SOURCE_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('5', '查看配置', '1', NULL, '4', 'SOURCE_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('CH', '实时数仓', '1', NULL, '1', 'WRITE_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('ES', '索引库', '1', NULL, '2', 'WRITE_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('String', 'String', '1', NULL, '1', '字段类型', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Integer', 'Integer', '1', NULL, '3', '字段类型', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Long', 'Long', '1', NULL, '4', '字段类型', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Boolean', 'Boolean', '1', NULL, '5', '字段类型', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Timestamp', 'Timestamp', '1', NULL, '6', '字段类型', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Float', 'Float', '1', NULL, '7', '字段类型', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Double', 'Double', '1', NULL, '8', '字段类型', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('gt', '>', '1', NULL, '1', 'OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('lt', '<', '1', NULL, '2', 'OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('ge', '>=', '1', NULL, '3', 'OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('le', '<=', '1', NULL, '4', 'OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('eq', '=', '1', NULL, '5', 'OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('ne', '!=', '1', NULL, '6', 'OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('regexp', 'regexp', '1', NULL, '7', 'OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('1', 'UTC Timestamp Millisecond', '1', NULL, '1', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('2', 'UTC Timestamp Second', '1', NULL, '2', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('3', 'yyyy-MM-dd HH:mm:ss', '1', NULL, '3', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('4', 'yyyy-MM-dd HH:mm:ss.SSS', '1', NULL, '4', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('5', 'yyyy-MM-dd\'T\'HH:mm:ss.SSS', '1', NULL, '5', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('6', 'yyyy-MM-dd\'T\'HH:mm:ss.SSSZ', '1', NULL, '6', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('7', 'yyyy-MM-dd\'T\'HH:mm:ss.SSSz', '1', NULL, '7', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('8', 'yyyy年MM月dd日 HH:mm:ss', '1', NULL, '8', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('9', 'yyyy-M-d', '1', NULL, '9', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('10', 'yyyy-M-d H:m:s', '1', NULL, '10', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('11', 'yyyy-MM-dd', '1', NULL, '11', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('12', 'MMM d, yyyy h:m:s aa', '1', NULL, '12', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('13', 'EEE MMM d HH:mm:ss \'CST\' yyyy', '1', NULL, '13', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('14', 'd/MMM/yyyy:h:m:s Z', '1', NULL, '14', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('15', 'yyyy/M/d H:m', '1', NULL, '15', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('16', 'd/MMM/yyyy:H:m:s Z', '1', NULL, '16', 'DATEFORMAT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('shardNum', '5', '1', NULL, '1', 'ES_DEFAULT_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('replicaNum', '1', '1', NULL, '2', 'ES_DEFAULT_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('maxCapacity', '100', '1', NULL, '3', 'ES_DEFAULT_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('maxKeepDays', '90', '1', NULL, '4', 'ES_DEFAULT_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('strategy', 'day', '1', NULL, '5', 'ES_DEFAULT_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('cycleScope', '1', '1', NULL, '6', 'ES_DEFAULT_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('DAYS', 'DAYS', '1', NULL, '1', 'TIMEUNIT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('HOURS', 'HOURS', '1', NULL, '2', 'TIMEUNIT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('MINUTES', 'MINUTES', '1', NULL, '3', 'TIMEUNIT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('maxKeepDays', '90', '1', NULL, '1', 'CH_DEFAULT_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('String', 'String', '1', NULL, '1', 'CH_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Int8', 'Int8', '1', NULL, '2', 'CH_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Int32', 'Int32', '1', NULL, '3', 'CH_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Int64', 'Int64', '1', NULL, '4', 'CH_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('DateTime64', 'DateTime64', '1', NULL, '5', 'CH_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Float32', 'Float32', '1', NULL, '6', 'CH_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Float64', 'Float64', '1', NULL, '7', 'CH_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('keyword', 'keyword', '1', NULL, '1', 'ES_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('text', 'text', '1', NULL, '2', 'ES_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('long', 'long', '1', NULL, '3', 'ES_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('double', 'double', '1', NULL, '4', 'ES_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('date', 'date', '1', NULL, '5', 'ES_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('boolean', 'boolean', '1', NULL, '6', 'ES_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('float', 'float', '1', NULL, '7', 'ES_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('client', '采集器', '1', NULL, '1', 'LOGMOUDLE_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('gate', '网关', '1', NULL, '2', 'LOGMOUDLE_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('parser', '解析器', '1', NULL, '3', 'LOGMOUDLE_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('longitude', '经度', '1', NULL, '5', 'IPREGION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('latitude', '纬度', '1', NULL, '6', 'IPREGION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('country', '国家', '1', NULL, '1', 'IPREGION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('province', '省份', '1', NULL, '2', 'IPREGION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('city', '城市', '1', NULL, '3', 'IPREGION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('isp', '运营商', '1', NULL, '4', 'IPREGION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('MMM d H:m:s', '[a-zA-Z]+\\s\\d+\\s\\d{1,2}:\\d{1,2}:\\d{1,2}', '1', NULL, '3', 'SOURCE_SET_TIME_REG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('yyyy-MM-dd HH:mm:ss', '\\d{4}-\\d{1,2}-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}', '1', NULL, '2', 'SOURCE_SET_TIME_REG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('yyyy-MM-dd HH:mm:ss.SSS', '\\d{4}-\\d{1,2}-\\d{1,2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\.\\d{1,3}', '1', NULL, '1', 'SOURCE_SET_TIME_REG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('name', '姓名', '1', NULL, '1', 'MASK_STRATEGY', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('companyName', '企业单位名称', '1', NULL, '2', 'MASK_STRATEGY', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('IDCardNo', '身份证号码', '1', NULL, '3', 'MASK_STRATEGY', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('orgCode', '组织机构代码', '1', NULL, '4', 'MASK_STRATEGY', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('commercialNo', '工商注册号', '1', NULL, '5', 'MASK_STRATEGY', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('taxpayerID', '纳税人识别号', '1', NULL, '6', 'MASK_STRATEGY', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('phoneNo', '电话号码', '1', NULL, '7', 'MASK_STRATEGY', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('email', '电子邮件', '1', NULL, '8', 'MASK_STRATEGY', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('cardNo', '银行卡号', '1', NULL, '9', 'MASK_STRATEGY', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('selfDefine', '自定义', '1', NULL, '10', 'MASK_STRATEGY', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('1', '已知', '1', NULL, '1', 'INSIGHT_USER_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('0', '未知', '1', NULL, '2', 'INSIGHT_USER_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('在职', '在职', '1', NULL, '1', 'INSIGHT_JOB_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('离职', '离职', '1', NULL, '2', 'INSIGHT_JOB_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('待岗', '待岗', '1', NULL, '3', 'INSIGHT_JOB_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('1', '已知', '1', NULL, '1', 'INSIGHT_DEVICE_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('0', '未知', '1', NULL, '2', 'INSIGHT_DEVICE_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('gt', '>', '1', NULL, '1', 'INSIGHT_FILTER_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('lt', '<', '1', NULL, '2', 'INSIGHT_FILTER_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('eq', '=', '1', NULL, '3', 'INSIGHT_FILTER_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('ge', '>=', '1', NULL, '4', 'INSIGHT_FILTER_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('le', '<=', '1', NULL, '5', 'INSIGHT_FILTER_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('ne', '!=', '1', NULL, '6', 'INSIGHT_FILTER_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('btwe', '介于之间(包含)', '1', NULL, '7', 'INSIGHT_FILTER_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('btw', '介于之间(不包含)', '1', NULL, '8', 'INSIGHT_FILTER_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('oute', '介于之外(包含)', '1', NULL, '9', 'INSIGHT_FILTER_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('out', '介于之外(不包含)', '1', NULL, '10', 'INSIGHT_FILTER_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userName', '用户名', '1', NULL, '1', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('accountName', '账号名', '1', NULL, '2', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceName', '设备名称', '1', NULL, '3', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceIP', 'IP地址', '1', NULL, '4', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('appName', '应用名', '1', NULL, '5', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataName', '数据名', '1', NULL, '6', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomalyName', '异常类型', '1', NULL, '7', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatName', '威胁类型', '1', NULL, '8', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomalyDesc', '异常描述', '1', NULL, '9', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomalySummary', '异常摘要', '1', NULL, '10', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('high', '高(8-10分)', '1', '8,10', '1', 'INSIGHT_ANOMALY_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('mid', '中(5-7分)', '1', '5,7', '2', 'INSIGHT_ANOMALY_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('low', '低(1-4分)', '1', '1,4', '3', 'INSIGHT_ANOMALY_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userName', '用户名', '1', NULL, '1', 'INSIGHT_USER_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('accountName', '账号名', '1', NULL, '2', 'INSIGHT_USER_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userEmail', '邮箱', '1', NULL, '3', 'INSIGHT_USER_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userIDCard', '身份证号码', '1', NULL, '4', 'INSIGHT_USER_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userCellPhone', '手机号码', '1', NULL, '5', 'INSIGHT_USER_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userPhone', '电话号码', '1', NULL, '6', 'INSIGHT_USER_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userUUID', '自然人唯一标识', '1', NULL, '7', 'INSIGHT_USER_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceName', '名称', '1', NULL, '1', 'INSIGHT_DEVICE_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceIP', 'IP地址', '1', NULL, '2', 'INSIGHT_DEVICE_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceUUID', '设备唯一标识', '1', NULL, '3', 'INSIGHT_DEVICE_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('appName', '应用名', '1', NULL, '1', 'INSIGHT_APP_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('appUUID', '应用唯一标识', '1', NULL, '3', 'INSIGHT_APP_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataName', '数据名', '1', NULL, '1', 'INSIGHT_DATA_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataUUID', '数据唯一标识', '1', NULL, '3', 'INSIGHT_DATA_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('high', '高(8-10分)', '1', '8,10', '1', 'INSIGHT_USER_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('mid', '中(5-7分)', '1', '5,7', '2', 'INSIGHT_USER_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('low', '低(1-4分)', '1', '1,4', '3', 'INSIGHT_USER_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('high', '高(8-10分)', '1', '8,10', '1', 'INSIGHT_DEVICE_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('mid', '中(5-7分)', '1', '5,7', '2', 'INSIGHT_DEVICE_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('low', '低(1-4分)', '1', '1,4', '3', 'INSIGHT_DEVICE_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('high', '高(8-10分)', '1', '8,10', '1', 'INSIGHT_APP_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('mid', '中(5-7分)', '1', '5,7', '2', 'INSIGHT_APP_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('low', '低(1-4分)', '1', '1,4', '3', 'INSIGHT_APP_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('high', '高(8-10分)', '1', '8,10', '1', 'INSIGHT_DATA_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('mid', '中(5-7分)', '1', '5,7', '2', 'INSIGHT_DATA_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('low', '低(1-4分)', '1', '1,4', '3', 'INSIGHT_DATA_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('high', '高(8-10分)', '1', '8,10', '1', 'INSIGHT_PROGRAM_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('mid', '中(5-7分)', '1', '5,7', '2', 'INSIGHT_PROGRAM_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('low', '低(1-4分)', '1', '1,4', '3', 'INSIGHT_PROGRAM_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userName', '用户名', '1', NULL, '1', 'INSIGHT_THREAT_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('accountName', '账号名', '1', NULL, '2', 'INSIGHT_THREAT_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceName', '设备名称', '1', NULL, '3', 'INSIGHT_THREAT_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceIP', 'IP地址', '1', NULL, '4', 'INSIGHT_THREAT_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('appName', '应用名', '1', NULL, '5', 'INSIGHT_THREAT_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataName', '数据名', '1', NULL, '6', 'INSIGHT_THREAT_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatName', '威胁类型', '1', NULL, '7', 'INSIGHT_THREAT_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomalyName', '异常类型', '1', NULL, '8', 'INSIGHT_THREAT_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatDesc', '威胁描述', '1', NULL, '9', 'INSIGHT_THREAT_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatSummary', '威胁摘要', '1', NULL, '10', 'INSIGHT_THREAT_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatOption', '威胁处置说明', '1', NULL, '11', 'INSIGHT_THREAT_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('high', '高(8-10分)', '1', '8,10', '1', 'INSIGHT_THREAT_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('mid', '中(5-7分)', '1', '5,7', '2', 'INSIGHT_THREAT_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('low', '低(1-4分)', '1', '1,4', '3', 'INSIGHT_THREAT_SCORE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('未调查', '未调查', '1', NULL, '1', 'INSIGHT_THREAT_SURVEY_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('调查中', '调查中', '1', NULL, '2', 'INSIGHT_THREAT_SURVEY_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('调查完成', '调查完成', '1', NULL, '3', 'INSIGHT_THREAT_SURVEY_RESULT_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('确认威胁', '确认威胁,未处理', '1', NULL, '1', 'INSIGHT_THREAT_SURVEY_RESULT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('误报', '误报', '1', NULL, '2', 'INSIGHT_THREAT_SURVEY_SCORE_RESULT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('positive', '正选', '1', NULL, '1', 'CUBE_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('reverse', '反选', '1', NULL, '2', 'CUBE_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('match', '匹配', '1', NULL, '3', 'CUBE_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('reg', '正则', '1', NULL, '4', 'CUBE_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('gt', '大于', '1', NULL, '5', 'CUBE_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('lt', '小于', '1', NULL, '6', 'CUBE_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('eq', '等于', '1', NULL, '7', 'CUBE_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('ne', '不等于', '1', NULL, '8', 'CUBE_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('index', '度量值', '1', NULL, '1', 'CUBE_FIELD_CATEGORY', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dim', '参考维度', '1', NULL, '2', 'CUBE_FIELD_CATEGORY', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('sum', '求和', '1', NULL, '1', 'CUBE_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('count', '数量', '1', NULL, '3', 'CUBE_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('max', '最大值', '1', NULL, '4', 'CUBE_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('min', '最小值', '1', NULL, '5', 'CUBE_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('avg', '平均值', '1', NULL, '6', 'CUBE_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('mid', '中位数', '1', NULL, '7', 'CUBE_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('upper', '上四分位', '1', NULL, '8', 'CUBE_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('lower', '下四分位', '1', NULL, '9', 'CUBE_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('var', '方差', '1', NULL, '10', 'CUBE_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('stddev', '标准差', '1', NULL, '11', 'CUBE_OPERATOR', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('year', '年', '1', NULL, '1', 'CUBE_TIME_RANGE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('month', '月', '1', NULL, '2', 'CUBE_TIME_RANGE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('week', '周', '1', NULL, '3', 'CUBE_TIME_RANGE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('day', '天', '1', NULL, '4', 'CUBE_TIME_RANGE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('hour', '小时', '1', NULL, '5', 'CUBE_TIME_RANGE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('toYear', 'toYear', '1', NULL, '1', 'CUBE_DATE_FUNCTION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('toMonth ', 'toMonth', '1', NULL, '2', 'CUBE_DATE_FUNCTION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('toDayOfYear ', 'toDayOfYear', '1', NULL, '3', 'CUBE_DATE_FUNCTION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('toDayOfMonth', 'toDayOfMonth', '1', NULL, '4', 'CUBE_DATE_FUNCTION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('toDayOfWeek', 'toDayOfWeek', '1', NULL, '5', 'CUBE_DATE_FUNCTION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('toHour ', 'toHour', '1', NULL, '6', 'CUBE_DATE_FUNCTION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('toMinute', 'toMinute', '1', NULL, '7', 'CUBE_DATE_FUNCTION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('重要', '重要', '1', NULL, '1', 'INSIGHT_APP_WATCHLIST', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('maxValue', '最大值法', '1', NULL, '1', 'INSIGHT_SCORE_RULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('tenPoint', '累加映射10分法', '1', NULL, '2', 'INSIGHT_SCORE_RULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('avgValue', '平均值法', '1', NULL, '3', 'INSIGHT_SCORE_RULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('entropy', '熵权法', '1', NULL, '4', 'INSIGHT_SCORE_RULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('modelExplore', 'http://127.0.0.1:8888/?token=45e1fc35417cbb8e7fc5b4d6cbf9ee7696416075432a715e', '1', NULL, '1', 'URL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('ntpServer', '', '1', NULL, NULL, 'UEBA_SYS_TIME', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('timeInterval', '1', '1', NULL, NULL, 'UEBA_SYS_TIME', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('timeUnit', 'second', '1', NULL, NULL, 'UEBA_SYS_TIME', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('sysTime', '2022-05-20 16:18:55', '1', NULL, NULL, 'UEBA_SYS_TIME', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('path', '/home/<USER>/backup_file/mysql/', '1', NULL, NULL, 'SYSTEM_MYSQL_BACKUP_PATH', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('第三方外包', '第三方外包', '1', NULL, '0', 'INSIGHT_USER_PERSONTYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('企业员工', '企业员工', '1', NULL, '1', 'INSIGHT_USER_PERSONTYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('database', 'ueba', '1', NULL, '0', 'SYSTEM_DW_DATABASE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('MergeTree', 'MergeTree', '1', NULL, '0', 'SYSTEM_DW_TABLE_ENGINE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('UInt8', 'UInt8', '1', NULL, '0', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('UInt16', 'UInt16', '1', NULL, '1', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('UInt32', 'UInt32', '1', NULL, '2', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('UInt64', 'UInt64', '1', NULL, '3', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Int8', 'Int8', '1', NULL, '4', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Int16', 'Int16', '1', NULL, '5', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Int32', 'Int32', '1', NULL, '6', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Int64', 'Int64', '1', NULL, '7', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Float32', 'Float32', '1', NULL, '8', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Float64', 'Float64', '1', NULL, '9', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('String', 'String', '1', NULL, '10', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Date', 'Date', '1', NULL, '11', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('DateTime', 'DateTime', '1', NULL, '12', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('DateTime64', 'DateTime64', '1', NULL, '13', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('title', '', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('businessId', 'f34e2956480d43a8970933b4afabc5e0', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('sysadmin', 'sysadmin', '1', NULL, '0', 'INSIGHT_SURVEY_ACCOUNT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('ReplicatedMergeTree', 'ReplicatedMergeTree', '1', NULL, '1', 'SYSTEM_DW_TABLE_ENGINE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('ReplicatedReplacingMergeTree', 'ReplicatedReplacingMergeTree', '1', NULL, '2', 'SYSTEM_DW_TABLE_ENGINE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('MySQL', 'MySQL', '1', NULL, '3', 'SYSTEM_DW_TABLE_ENGINE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('信息', '信息', '1', NULL, '1', 'INSIGHT_DATA_VALUE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('非常敏感', '非常敏感', '1', NULL, '2', 'INSIGHT_DATA_VALUE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('敏感', '敏感', '1', NULL, '3', 'INSIGHT_DATA_VALUE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('不敏感', '不敏感', '1', NULL, '4', 'INSIGHT_DATA_VALUE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('高', '高', '1', NULL, '1', 'INSIGHT_APP_VALUE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('中', '中', '1', NULL, '2', 'INSIGHT_APP_VALUE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('低', '低', '1', NULL, '3', 'INSIGHT_APP_VALUE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('高', '高', '1', NULL, '1', 'INSIGHT_DEVICE_VALUE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('中', '中', '1', NULL, '2', 'INSIGHT_DEVICE_VALUE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('低', '低', '1', NULL, '3', 'INSIGHT_DEVICE_VALUE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('异常类型1', '异常类型1', '1', NULL, '9', ' INSIGHT_ANOMALY_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('异常类型2', '异常类型2', '1', NULL, '10', ' INSIGHT_ANOMALY_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('defaultTitle', '安全数据中心 | SDC', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('defaultBusinessId', 'f34e2956480d43a8970933b4afabc5e0', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('defaultHeadTitle', 'UEBA', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('defaultHeadBusinessId', 'f9cb054c2d51463db9e4dc87c193f174', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('headTitle', 'UEBA', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('headBusinessId', 'f9cb054c2d51463db9e4dc87c193f174', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('year', '年', '1', NULL, '1', 'ACTION_SESSION_TIME_RANGE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('month', '月', '1', NULL, '2', 'ACTION_SESSION_TIME_RANGE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('week', '周', '1', NULL, '3', 'ACTION_SESSION_TIME_RANGE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('day', '天', '1', NULL, '4', 'ACTION_SESSION_TIME_RANGE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('hour', '小时', '1', NULL, '5', 'ACTION_SESSION_TIME_RANGE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('positive', '正选', '1', NULL, '1', 'ACTION_SESSION_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('reverse', '反选', '1', NULL, '2', 'ACTION_SESSION_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('match', '匹配', '1', NULL, '3', 'ACTION_SESSION_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('reg', '正则', '1', NULL, '4', 'ACTION_SESSION_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('gt', '大于', '1', NULL, '5', 'ACTION_SESSION_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('lt', '小于', '1', NULL, '6', 'ACTION_SESSION_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('eq', '等于', '1', NULL, '7', 'ACTION_SESSION_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('ne', '不等于', '1', NULL, '8', 'ACTION_SESSION_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('minute', '分钟', '1', NULL, '6', 'ACTION_SESSION_TIME_RANGE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('second', '秒', '1', NULL, '7', 'ACTION_SESSION_TIME_RANGE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('未调查', '未调查', '1', NULL, '1', 'INSIGHT_ANOMALY_SURVEY_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('调查中', '调查中', '1', NULL, '2', 'INSIGHT_ANOMALY_SURVEY_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('调查完成', '调查完成', '1', NULL, '3', 'INSIGHT_ANOMALY_SURVEY_RESULT_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('确认异常', '确认异常,未处理', '1', NULL, '1', 'INSIGHT_ANOMALY_SURVEY_RESULT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('误报', '误报', '1', NULL, '2', 'INSIGHT_ANOMALY_SURVEY_SCORE_RESULT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('UUID', 'UUID', '1', NULL, '14', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Array(String)', 'Array(String)', '1', NULL, '15', 'SYSTEM_DW_TABLE_FIELD_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('localPath', '/home/<USER>/insight/alarm/local/', '1', NULL, '1', 'INSIGHT_ALARM_LOCAL_FILE_PATH', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('linkUrl', 'http://127.0.0.1:8081/insight/exception/list/', '1', NULL, '1', 'INSIGHT_ALARM_LINK_URL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('1', '已知', '1', NULL, '1', 'INSIGHT_APP_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('0', '未知', '1', NULL, '2', 'INSIGHT_APP_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('1', '已知', '1', NULL, '1', 'INSIGHT_DATA_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('0', '未知', '1', NULL, '2', 'INSIGHT_DATA_STATE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('login', '登录', '1', NULL, '1', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('logout', '退出', '1', NULL, '2', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('search', '查询', '1', NULL, '3', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('preview', '预览', '1', NULL, '4', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('refresh', '刷新', '1', NULL, '5', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('add', '新增', '1', NULL, '6', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('copy', '复制', '1', NULL, '7', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('save', '保存', '1', NULL, '8', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('import', '导入', '1', NULL, '9', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('edit', '编辑', '1', NULL, '10', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('setting', '配置', '1', NULL, '11', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('move', '移动', '1', NULL, '12', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('start', '启用', '1', NULL, '13', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('stop', '停用', '1', NULL, '14', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('delete', '删除', '1', NULL, '15', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('upload', '上传', '1', NULL, '16', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('download', '下载', '1', NULL, '17', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('saveOrUpdate', '保存更新', '1', NULL, '18', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('detail', '查看', '1', NULL, '19', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('test', '测试', '1', NULL, '20', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('parse', '解析', '1', NULL, '21', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('match', '匹配', '1', NULL, '22', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('startOrStop', '启动和暂停', '1', NULL, '23', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('reset', '重置', '1', NULL, '24', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('index', '首页', '1', NULL, '1', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exception', '异常洞察', '1', NULL, '2', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threat', '威胁洞察', '1', NULL, '3', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('insight-user', '用户分析', '1', NULL, '4', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('insight-device', '设备分析', '1', NULL, '5', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('insight-application', '应用分析', '1', NULL, '6', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('insight-data', '数据分析', '1', NULL, '7', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('insight-program', '程序分析', '1', NULL, '8', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('visual-risk-overview', '风险总览', '1', NULL, '9', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('ai_bi_dashboard', '自定义仪表盘', '1', NULL, '10', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataExplore', '数据探索', '1', NULL, '11', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('data-access', '数据采集', '1', NULL, '12', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataMart', ' 特征挖掘', '1', NULL, '13', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('baseInfo', '基础信息', '1', NULL, '14', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('watchList', '观察列表', '1', NULL, '15', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('model-explore', '模型探索', '1', NULL, '16', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exception-model', '异常模型', '1', NULL, '17', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threat-model', '威胁模型', '1', NULL, '18', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('scheduler-configuration', '模型调度', '1', NULL, '19', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('notebook', 'notebook', '1', NULL, '20', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('account-right', '账号权限', '1', NULL, '21', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('data-dictionary', '数据字典', '1', NULL, '22', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('list-manage', '名单管理', '1', NULL, '23', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('config-manage', '配置管理', '1', NULL, '24', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('resource-manage', '资源管理', '1', NULL, '25', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('warn', '告警管理', '1', NULL, '26', 'SYSTEM_LOG_OPT_MODULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('success', '成功', '1', NULL, '1', 'SYSTEM_LOG_LOG_RESULT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('error', '失败', '1', NULL, '2', 'SYSTEM_LOG_LOG_RESULT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('isDocker', 'false', '1', NULL, '2', 'ETL_SCRIPT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dockerImageName', 'python:slim', '1', NULL, '1', 'ETL_SCRIPT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('timeout', '30000', '1', NULL, '3', 'ETL_SCRIPT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('export', '导出', '1', NULL, '9', 'SYSTEM_LOG_OPT_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('企业员工', '企业员工', '1', NULL, '1', 'personType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('第三方外包', '第三方外包', '1', NULL, '2', 'personType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('离职', '离职', '1', NULL, '1', 'personStatus', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('在职', '在职', '1', NULL, '2', 'personStatus', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('待岗', '待岗', '1', NULL, '3', 'personStatus', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Normal', '普通账号', '1', NULL, '1', 'accountType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Admin', '管理员账号', '1', NULL, '2', 'accountType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Service', '服务账号', '1', NULL, '3', 'accountType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('Unknown', '未知账号', '1', NULL, '4', 'accountType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('锁定', '锁定', '1', NULL, '1', 'accountStatus', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('正常', '正常', '1', NULL, '2', 'accountStatus', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('高', '高', '1', NULL, '1', 'assetValue', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('中', '中', '1', NULL, '2', 'assetValue', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('低', '低', '1', NULL, '3', 'assetValue', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('信息', '信息', '1', NULL, '1', 'dataValue', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('不敏感', '不敏感', '1', NULL, '2', 'dataValue', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('敏感', '敏感', '1', NULL, '3', 'dataValue', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('非常敏感', '非常敏感', '1', NULL, '4', 'dataValue', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('src_user', '源用户', '1', NULL, NULL, 'alarmType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dst_user', '目标用户', '1', NULL, NULL, 'alarmType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('src_device', '源设备', '1', NULL, NULL, 'alarmType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dst_device', '目标设备', '1', NULL, NULL, 'alarmType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('src_account', '源账号', '1', NULL, NULL, 'alarmType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dst_account', '目标账号', '1', NULL, NULL, 'alarmType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('app', '应用', '1', NULL, NULL, 'alarmType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('program', '程序', '1', NULL, NULL, 'alarmType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('data', '数据', '1', NULL, NULL, 'alarmType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('programName', '程序名', '1', NULL, '1', 'INSIGHT_PROGRAM_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('黑客攻击', '黑客攻击', '0', NULL, '1', 'ANOMALY_CLASSIFICATION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('黑客攻击', '黑客攻击', '1', NULL, '1', 'THREAT_CLASSIFICATION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('high', '高', '1', '8,10', '1', 'INSIGHT_ANOMALY_LEVEL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('mid', '中', '1', '5,7', '2', 'INSIGHT_ANOMALY_LEVEL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('low', '低', '1', '1,4', '3', 'INSIGHT_ANOMALY_LEVEL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('high', '高', '1', '8,10', '1', 'INSIGHT_THREAT_LEVEL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('mid', '中', '1', '5,7', '2', 'INSIGHT_THREAT_LEVEL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('low', '低', '1', '1,4', '3', 'INSIGHT_THREAT_LEVEL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('KAFKA', 'Kafka', '1', NULL, '3', 'WRITE_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('srcDeviceTrans', '1', '1', NULL, '1', 'USER_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dstDeviceTrans', '1', '1', NULL, '2', 'USER_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataTransApp', '1', '1', NULL, '3', 'USER_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataTransNoApp', '1', '1', NULL, '4', 'USER_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceLogin', '1', '1', NULL, '5', 'USER_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userOperate', '1', '1', NULL, '6', 'USER_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceRecord', '1', '1', NULL, '7', 'USER_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('applicationDeviceVisit', '1', '1', NULL, '1', 'APPLICATION_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('applicationUserLogin', '1', '1', NULL, '2', 'APPLICATION_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataUserTrans', '1', '1', NULL, '1', 'DATA_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataDeviceTrans', '1', '1', NULL, '2', 'DATA_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataOperateType', '1', '1', NULL, '3', 'DATA_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceUserTrans', '1', '1', NULL, '3', 'DEVICE_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceUserLogin', '1', '1', NULL, '4', 'DEVICE_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceAppTrans', '1', '1', NULL, '5', 'DEVICE_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceOtherTrans', '1', '1', NULL, '6', 'DEVICE_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceUserUse', '1', '1', NULL, '7', 'DEVICE_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceMac', '1', '1', NULL, '8', 'DEVICE_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('graphicSimple', '1', '1', NULL, '1', 'ANOMALY_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomalyRelation', '1', '1', NULL, '2', 'ANOMALY_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatIncludeAnomaly', '1', '1', NULL, '3', 'ANOMALY_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('graphicSimple', '1', '1', NULL, '1', 'THREAT_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatRelation', '1', '1', NULL, '2', 'THREAT_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatTimeTable', '1', '1', NULL, '3', 'THREAT_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomalyIncludeThreat', '1', '1', NULL, '4', 'THREAT_CARD_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('极敏感级', '极敏感级', '1', NULL, '1', 'dataLevel', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('敏感级', '敏感级', '1', NULL, '2', 'dataLevel', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('较敏感级', '较敏感级', '1', NULL, '3', 'dataLevel', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('低敏感级', '低敏感级', '1', NULL, '4', 'dataLevel', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('已备案', '已备案', '1', NULL, '2', 'dataRecordStatus', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('未备案', '未备案', '1', NULL, '3', 'dataRecordStatus', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('待确认', '待确认', '1', NULL, '4', 'dataRecordStatus', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionLatest', '1', '1', '最近异常', '1', 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionClassify', '1', '1', '异常分类', '2', 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionRateLevel', '1', '1', '异常评分等级', '3', 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionToThreat', '1', '1', '异常组成的威胁', '4', 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionWatchList', '1', '1', '异常观察列表', '5', 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionInUser', '1', '1', '用户观察列表中的异常', '6', 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionTime', '1', '1', '异常时间表', '7', 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionTrend', '1', '1', '异常趋势', '8', 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatLatest', '1', '1', '最近威胁', '1', 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatClassify', '1', '1', '威胁分类', '2', 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatSituation', '1', '1', '威胁现状', '3', 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatTop10', '1', '1', '威胁处置排行TOP10', '4', 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatInException', '1', '1', '威胁中的异常', '5', 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatWatchList', '1', '1', '威胁观察列表', '6', 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatTime', '1', '1', '威胁时间表', '7', 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatTrend', '1', '1', '威胁趋势', '8', 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('programScoreRank', '1', '1', '程序评分排行', '1', 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('treatProgram', '1', '1', '威胁相关程序', '2', 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionProgram', '1', '1', '异常相关程序', '3', 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('programScoreLevel', '1', '1', '程序评分等级', '4', 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionProgramTrend', '1', '1', '异常程序趋势', '5', 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('treatProgramTrend', '1', '1', '威胁程序趋势', '6', 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('programWatchList', '1', '1', '程序观察列表', '7', 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('applicationScoreRank', '1', '1', '应用评分排行', '1', 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('treatApplication', '1', '1', '威胁相关应用', '2', 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionApplication', '1', '1', '异常相关应用', '3', 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('applicationScoreLevel', '1', '1', '应用评分等级', '4', 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('applicationWorth', '1', '1', '应用价值', '5', 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('applicationType', '1', '1', '应用类型', '6', 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionApplicationTrend', '1', '1', '异常应用趋势', '7', 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('treatApplicationTrend', '1', '1', '威胁应用趋势', '8', 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('applicationWatchList', '1', '1', '应用观察列表', '9', 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataScoreRank', '1', '1', '数据评分排行', '1', 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('treatInData', '1', '1', '威胁相关数据', '2', 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionInData', '1', '1', '异常相关数据', '3', 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataScoreLevel', '1', '1', '数据评分等级', '4', 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataWorth', '1', '1', '数据价值', '5', 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataType', '1', '1', '数据类型', '6', 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionDataTrend', '1', '1', '异常数据趋势', '7', 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('treatDataTrend', '1', '1', '威胁数据趋势', '8', 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataWatchList', '1', '1', '数据观察列表', '9', 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceScoreRank', '1', '1', '设备评分排行', '1', 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('treatInDevice', '1', '1', '威胁相关设备', '2', 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionInDevice', '1', '1', '异常相关设备', '3', 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceScoreLevel', '1', '1', '设备评分等级', '4', 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceType', '1', '1', '设备类型', '5', 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceWatchList', '1', '1', '设备观察列表', '6', 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionDeviceTrend', '1', '1', '异常设备趋势', '7', 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('treatDeviceTrend', '1', '1', '威胁设备趋势', '8', 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceInDept', '1', '1', '设备所属部门', '9', 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceWorth', '1', '1', '设备价值', '10', 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userScoreRank', '1', '1', '用户评分排行', '1', 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('treatInUser', '1', '1', '威胁相关用户', '2', 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionInUser', '1', '1', '异常相关用户', '3', 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userScoreLevel', '1', '1', '用户评分等级', '4', 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userStatus', '1', '1', '在职状态', '5', 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userWatchList', '1', '1', '用户观察列表', '6', 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('exceptionUserTrend', '1', '1', '异常用户趋势', '7', 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('treatUserTrend', '1', '1', '威胁用户趋势', '8', 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userAddress', '1', '1', '用户所在地址', '9', 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userDept', '1', '1', '用户所在部门', '10', 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userType', '1', '1', '用户类型', '11', 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('loginFailEvent', '1', '1', '登录失败事件', '1', 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('inactiveUserCount', '1', '1', '非活跃用户数', '2', 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataTransferCount', '1', '1', '数据传输量', '3', 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('loginSuccessDeviceType', '1', '1', '登录成功设备类型', '4', 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('loginFailUser', '1', '1', '登录失败用户', '5', 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('loginFailDevice', '1', '1', '登录失败目标设备', '6', 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('visitDataType', '1', '1', '被访问的数据类型', '7', 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('sourceDeviceTransferData', '1', '1', '源设备传输数据', '8', 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('targetDeviceTransferData', '1', '1', '目标设备传输数据', '9', 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('1', '一级分类', '1', '模型分类/一级分类', '1', 'modelType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('2', '二级分类', '1', '模型分类/二级分类', '1', 'modelType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('3', '三级分类', '1', '模型分类/三级分类', '1', 'modelType', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('backgroundBusinessId', '7c0adc1c7c0e7f0d72e69b8bab88e700', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('defaultBackgroundBusinessId', '7c0adc1c7c0e7f0d72e69b8bab88e700', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('copyright', '1', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('defaultCopyright', '1', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('logo_suffix', 'svg、png、jpg、gif', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('favicon_suffix', 'svg、png、jpg、gif、ico', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('background_suffix', 'jpeg、png、jpg', '1', NULL, NULL, 'SYSTEM_LOGO_CONFIG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threat', '1', '1', '威胁', '1', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomaly', '1', '1', '异常', '2', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('user', '1', '1', '用户', '3', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('device', '1', '1', '设备', '4', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('data', '1', '1', '数据', '6', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('program', '1', '1', '程序', '7', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('app', '1', '1', '应用', '5', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('newlyThreatCard', '1', '1', '最新威胁', '8', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threatTrendCard', '1', '1', '威胁趋势', '9', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('newlyAnomalyCard', '1', '1', '最新异常', '10', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomalyTrendCard', '1', '1', '异常趋势', '11', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userScoreRankCard', '1', '1', '用户评分排行', '12', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userStatusCard', '1', '1', '用户状态', '13', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceScoreRankCard', '1', '1', '设备评分排行', '14', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceStatusCard', '1', '1', '设备状态', '15', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataScoreRankCard', '1', '1', '数据评分排行', '16', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataStatusCard', '1', '1', '数据状态', '17', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('programScoreRankCard', '1', '1', '应用评分排行', '18', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('programStatusCard', '1', '1', '应用状态', '19', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('departmentThreatAndAnomalyCard', '1', '1', '部门异常/威胁数据数量分布TOP5', '20', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataThreatAndAnomalyTrendCard', '1', '1', '数据异常/威胁数量趋势', '21', 'HOME_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userTag', '1', '1', '用户标签', '0', 'USER_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('appTag', '1', '1', '应用标签', '0', 'APPLICATION_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataTag', '1', '1', '数据标签', '0', 'DATA_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceTag', '1', '1', '设备标签', '0', 'DEVICE_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('programTag', '1', '1', '程序标签', '0', 'PROGRAM_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomalyMapNum', '50', '1', NULL, '1', 'INSIGHT_ANOMALY_MAP_NUM', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('srcUser', '源用户', '1', NULL, '11', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('srcDevice', '源设备', '1', NULL, '12', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dstUser', '目标用户', '1', NULL, '13', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dstDevice', '目标设备', '1', NULL, '14', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('appName', '目标应用', '1', NULL, '15', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataName', '目标数据', '1', NULL, '16', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('programName', '目标程序', '1', NULL, '17', 'INSIGHT_ANOMALY_LISTFIELD', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('mysql', '/usr/bin/', '1', 'mysql备份的命令路径', '1', 'BACK_UP_ORDER', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userSevenDayPolar', '1', '1', '最近7天热力图', '7', 'USER_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userThreeWeekPolar', '1', '1', '近3周热力图', '9', 'USER_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('programSevenDayPolar', '1', '1', '最近7天热力图', '1', 'PROGRAM_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('programThreeWeekPolar', '1', '1', '近3周热力图', '1', 'PROGRAM_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceThreeWeekPolar', '1', '1', '近3周热力图', '1', 'DEVICE_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deviceSevenDayPolar', '1', '1', '近7天热力图', '2', 'DEVICE_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('applicationThreeWeekPolar', '1', '1', '近3周热力图', '1', 'APPLICATION_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('applicationSevenDayPolar', '1', '1', '近7天热力图', '1', 'APPLICATION_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataThreeWeekPolar', '1', '1', '近3周热力图', '1', 'DATA_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('dataSevenDayPolar', '1', '1', '近7天热力图', '1', 'DATA_CARD_CONFIG', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('认证类场景', '认证类场景', '0', NULL, '2', 'ANOMALY_CLASSIFICATION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('敏感数据泄漏类场景', '敏感数据泄漏类场景', '0', NULL, '3', 'ANOMALY_CLASSIFICATION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('合规审计类场景', '合规审计类场景', '0', NULL, '4', 'ANOMALY_CLASSIFICATION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('确认异常,已处理', '确认异常,已处理', '1', NULL, '2', 'INSIGHT_ANOMALY_SURVEY_RESULT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('确认威胁,已处理', '确认威胁,已处理', '1', NULL, '2', 'INSIGHT_THREAT_SURVEY_RESULT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('safeNotice', '安全通告', '1', '预警类型', '1', 'EARLY_WARN_CLASSIFICATION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('attackWarn', '攻击预警', '1', '预警类型', '2', 'EARLY_WARN_CLASSIFICATION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('bugWarn', '漏洞预警', '1', '预警类型', '3', 'EARLY_WARN_CLASSIFICATION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('virusWarn', '病毒预警', '1', '预警类型', '4', 'EARLY_WARN_CLASSIFICATION', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('first', '一级', '1', '预警级别', '1', 'EARLY_WARN_LEVEL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('second', '二级', '1', '预警级别', '2', 'EARLY_WARN_LEVEL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('third', '三级', '1', '预警级别', '3', 'EARLY_WARN_LEVEL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('fourth', '四级', '1', '预警级别', '4', 'EARLY_WARN_LEVEL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('fifth', '五级', '1', '预警级别', '5', 'EARLY_WARN_LEVEL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('inside', '内部预警', '1', '预警来源分类', '1', 'EARLY_WARN_SOURCE_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('outside', '外部预警', '1', '预警来源分类', '2', 'EARLY_WARN_SOURCE_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('state', '国家主管部门', '1', '预警来源', '1', 'EARLY_WARN_SOURCE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('organization', '安全组织', '1', '预警来源', '2', 'EARLY_WARN_SOURCE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('company', '厂商', '1', '预警来源', '3', 'EARLY_WARN_SOURCE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('highLevelUnit', '上级单位', '1', '预警来源', '4', 'EARLY_WARN_SOURCE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('unit', '本单位', '1', '预警来源', '5', 'EARLY_WARN_SOURCE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('other', '其他', '1', '预警来源', '6', 'EARLY_WARN_SOURCE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('generalSet', '通用设置', '1', '预警-漏洞类型', '1', 'EARLY_WARN_BUG_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('hostInfo', '主机信息', '1', '预警-漏洞类型', '2', 'EARLY_WARN_BUG_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('accountInfo', '账号信息', '1', '预警-漏洞类型', '3', 'EARLY_WARN_BUG_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('portService', '端口服务', '1', '预警-漏洞类型', '4', 'EARLY_WARN_BUG_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('risk', '风险漏洞', '1', '预警-漏洞类型', '5', 'EARLY_WARN_BUG_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('other', '其他', '1', '预警-漏洞类型', '6', 'EARLY_WARN_BUG_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('encryptType', 'MD5', '1', '资源备份数字签名：MD5/SM2', '1', 'BACK_UP_ENCRYPT', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('help', '观安观鉴用户异常行为分析系统-产品使用手册3.1.0.pdf', '1', '是否显示下载帮助', '1', 'SYSTEM_HELP', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('isNull', '不非空', '1', NULL, '9', 'ACTION_SESSION_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('isNotNull', '非空', '1', NULL, '10', 'ACTION_SESSION_FILTER_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('executeCycle', '02', '1', '01-立即执行，02-周期执行', NULL, 'UEBA_SYNC_BASE_INFO', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('executeType', 'day', '1', '时间类型day', NULL, 'UEBA_SYNC_BASE_INFO', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('executeConfig', '15:09', '1', '时间', NULL, 'UEBA_SYNC_BASE_INFO', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('switchContr', '1', '1', '开关：0关1开', NULL, 'UEBA_SYNC_BASE_INFO', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('weightAvg', '加权平均值法', '1', '洞察-评分算法', '5', 'INSIGHT_SCORE_RULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('composite', '综合法', '1', '洞察-评分算法', '6', 'INSIGHT_SCORE_RULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('maxValue', '最大值法', '1', NULL, '0', 'INSIGHT_THREAT_SCORE_RULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('avgValue', '平均值法', '1', NULL, '1', 'INSIGHT_THREAT_SCORE_RULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('weightAvg', '加权平均值法', '1', NULL, '2', 'INSIGHT_THREAT_SCORE_RULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomalyNum', '异常数量统计法', '1', NULL, '3', 'INSIGHT_THREAT_SCORE_RULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomalyCategory', '异常分类统计法', '1', NULL, '4', 'INSIGHT_THREAT_SCORE_RULE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('UTC Timestamp Millisecond', '\\d{13}', '1', NULL, '5', 'SOURCE_SET_TIME_REG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('UTC Timestamp Second', '\\d{10}', '1', NULL, '6', 'SOURCE_SET_TIME_REG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('yyyy年MM月dd日 HH:mm:ss', '\\d{4}年\\d{1,2}月\\d{1,2}日\\s\\d{1,2}:\\d{1,2}:\\d{1,2}', '1', NULL, '7', 'SOURCE_SET_TIME_REG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('MMM d, yyyy h:m:s aa', '\\w+\\s\\d{1,2},\\s\\d{4}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s[a-zA-Z]{2}', '1', NULL, '8', 'SOURCE_SET_TIME_REG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('d/MMM/yyyy:H:m:s Z', '\\d{1,2}\\/\\w+\\/\\d{4}:\\d{1,2}:\\d{1,2}:\\d{1,2}\\s[-+]?[0-9]+', '1', NULL, '9', 'SOURCE_SET_TIME_REG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('/security/fw', '/security/fw', '1', NULL, '0', 'INSIGHT_ANOMALY_DATA', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('青藤云日志', '青藤云日志', '1', NULL, '1', 'INSIGHT_ANOMALY_DATA', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('days', '12', '1', '距离License过期天数提醒', '1', 'LICENSE_EXPIRE_TIP_DAYS', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('gatewayAddr', 'smtp.qq.com', '1', NULL, NULL, 'UEBA_SYS_MAIL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('gatewayPort', '25', '1', NULL, NULL, 'UEBA_SYS_MAIL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('sendMailAddr', '<EMAIL>', '1', NULL, NULL, 'UEBA_SYS_MAIL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('sendMailAccount', '<EMAIL>', '1', NULL, NULL, 'UEBA_SYS_MAIL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('password', 'Ufhw+Wze9yF0ljKTp3l/qLZwvJ7/J5FFcKFyyw+cB+c=', '1', NULL, NULL, 'UEBA_SYS_MAIL', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('limit', '5', '1', NULL, '1', 'DASHBOARD_LIMIT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('nfsEnable', 'off', '1', NULL, NULL, 'SYSTEM_FILE_CFG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('nfsFolder', '', '1', NULL, NULL, 'SYSTEM_FILE_CFG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('hdfsEnable', 'on', '1', NULL, NULL, 'SYSTEM_FILE_CFG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('hdfsServer', '', '1', NULL, NULL, 'SYSTEM_FILE_CFG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('userName', '', '1', NULL, NULL, 'SYSTEM_FILE_CFG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('password', '', '1', NULL, NULL, 'SYSTEM_FILE_CFG', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('17', 'yyyyMMdd HH:mm:ss', '1', NULL, '17', 'DATEFORMAT', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomaly', '0', '1', NULL, '1', 'INSIGHT_CATEGORY_JOIN', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threat', '1', '1', NULL, '1', 'INSIGHT_CATEGORY_JOIN', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('18', 'yyyyMM.dd HH:mm:ss', '1', NULL, '18', 'DATEFORMAT', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('19', 'yyyyMM', '1', NULL, '19', 'DATEFORMAT', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('内部', '黑客攻击-内部', '1', NULL, '0', 'PRIMARY_THREAT_TYPE', '黑客攻击');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('外部', '黑客攻击-外部', '1', NULL, '1', 'PRIMARY_THREAT_TYPE', '黑客攻击');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('杀伤链', '黑客攻击-杀伤链', '1', NULL, '2', 'PRIMARY_THREAT_TYPE', '黑客攻击');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('图风险模型', '黑客攻击-图风险模型', '1', NULL, '3', 'PRIMARY_THREAT_TYPE', '黑客攻击');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('自定义', '黑客攻击-自定义', '1', NULL, '4', 'PRIMARY_THREAT_TYPE', '黑客攻击');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('user', '用户', '1', NULL, '0', 'INSIGHT_ALARM_OBJECT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('device', '设备', '1', NULL, '1', 'INSIGHT_ALARM_OBJECT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('anomaly', '异常', '1', NULL, '2', 'INSIGHT_ALARM_OBJECT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('threat', '威胁', '1', NULL, '3', 'INSIGHT_ALARM_OBJECT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('custom', '自定义', '1', NULL, '4', 'INSIGHT_ALARM_OBJECT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('11', '11', '1', NULL, NULL, 'ANOMALY_CLASSIFICATION', '');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('deface', '网页篡改', '1', '预警-攻击类型', '1', 'EARLY_WARN_ATTACK_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('phishing', '网络仿冒(钓鱼)', '1', '预警-攻击类型', '2', 'EARLY_WARN_ATTACK_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('spam', '垃圾信息', '1', '预警-攻击类型', '3', 'EARLY_WARN_ATTACK_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('leakage', '信息泄露与窃取', '1', '预警-攻击类型', '4', 'EARLY_WARN_ATTACK_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('inaptitude', '不适当邮件内容', '1', '预警-攻击类型', '5', 'EARLY_WARN_ATTACK_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('cheating', '网络诈骗', '1', '预警-攻击类型', '6', 'EARLY_WARN_ATTACK_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('other', '其它', '1', '预警-攻击类型', '7', 'EARLY_WARN_ATTACK_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('etlPreProcessAlarm', '采集预处理告警', '1', NULL, '0', 'INSIGHT_ALARM_PLAT_OBJECT', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('homeDataCron', '0 0/10 * * * ?', '1', NULL, NULL, 'PAGE_DATA_TYPE', NULL);
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('内部', '内部', '1', NULL, '0', 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('外部警报', '外部警报', '1', NULL, '1', 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('外部攻击', '外部攻击', '1', NULL, '2', 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('外部扫描', '外部扫描', '1', NULL, '3', 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('向内', '向内', '1', NULL, '4', 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('向外', '向外', '1', NULL, '5', 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('本地', '本地', '1', NULL, '6', 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('允许的', '允许的', '1', NULL, '0', 'PRIMARY_ANOMALY_TYPE', '异常状态分析');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('受阻', '受阻', '1', NULL, '1', 'PRIMARY_ANOMALY_TYPE', '异常状态分析');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('侦察', '侦察', '1', NULL, '0', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('初始访问', '初始访问', '1', NULL, '1', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('执行', '执行', '1', NULL, '2', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('持久化', '持久化', '1', NULL, '3', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('特权提升', '特权提升', '1', NULL, '4', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('防御规避', '防御规避', '1', NULL, '5', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('凭证访问', '凭证访问', '1', NULL, '6', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('发现', '发现', '1', NULL, '7', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('横向移动', '横向移动', '1', NULL, '8', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('采集', '采集', '1', NULL, '9', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('数据泄露', '数据泄露', '1', NULL, '10', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('命令与控制', '命令与控制', '1', NULL, '11', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('恶意破坏', '恶意破坏', '1', NULL, '12', 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('感染', '感染', '1', NULL, '0', 'PRIMARY_ANOMALY_TYPE', '异常结果分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('可见度降低', '可见度降低', '1', NULL, '1', 'PRIMARY_ANOMALY_TYPE', '异常结果分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('数据销毁', '数据销毁', '1', NULL, '2', 'PRIMARY_ANOMALY_TYPE', '异常结果分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('拒绝服务', '拒绝服务', '1', NULL, '3', 'PRIMARY_ANOMALY_TYPE', '异常结果分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('失控', '失控', '1', NULL, '4', 'PRIMARY_ANOMALY_TYPE', '异常结果分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('对等组', '对等组', '1', NULL, '0', 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('蛮力', '蛮力', '1', NULL, '1', 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('违反政策', '违反政策', '1', NULL, '2', 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('列入黑名单', '列入黑名单', '1', NULL, '3', 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('外部', '外部', '1', NULL, '4', 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('飞行风险', '飞行风险', '1', NULL, '5', 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('移动存储', '移动存储', '1', NULL, '6', 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('用户账号安全类', '用户账号安全类', '1', NULL, '0', 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('人员异常打卡类', '人员异常打卡类', '1', NULL, '1', 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('失陷主机和应用类', '失陷主机和应用类', '1', NULL, '2', 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('数据泄露类', '数据泄露类', '1', NULL, '3', 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('运维操作及业务操作未知异常行为类', '运维操作及业务操作未知异常行为类', '1', NULL, '4', 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('运维操作合规审计类', '运维操作合规审计类', '1', NULL, '5', 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('业务操作合规审计类', '业务操作合规审计类', '1', NULL, '6', 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO `ueba_dictionary` (`key_code`, `value`, `enable`, `remark`, `sortno`, `type`, `parent_id`) VALUES ('send', '发送', '1', NULL, '25', 'SYSTEM_LOG_OPT_TYPE', NULL);

INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('LOCAL_FILE', '本地文件', 'log、csv、text文件', '文件上传', '1', '1', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('STREAM_FILE', '流式数据文件', 'StreamSocketServer', '文件上传', '2', '1', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('KAFKA', 'kafka', '分布式消息系统', '协议加载', '1', '2', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('SYSLOG_UDP', 'syslog_udp', '非连接的协议', '协议加载', '2', '2', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('SYSLOG_TCP', 'syslog_tcp', '传输控制协议', '协议加载', '3', '2', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('DB2', 'DB2', '关系型数据', '数据库', '1', '3', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('MYSQL', 'MySQL', '关系型数据', '数据库', '1', '3', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('ORACLE', 'Oracle', '关系型数据', '数据库', '1', '3', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('SQLSERVER', 'SQLServer', '关系型数据', '数据库', '1', '3', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('HIVE', 'Hive', '数据处理平台', '数据库', '1', '3', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('SCRIPT', 'Script', '采集脚本', '协议加载', '4', '2', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('MONGODB', 'MongoDB', '分布式文件存储数据库', '数据库', '1', '3', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('FTP_SFTP', 'FTP/SFTP', 'FTP、SFTP协议传输', '协议加载', '5', '2', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('HTTP', 'HTTP', 'HTTP协议', '协议加载', '6', '2', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('ELASTICSEARCH', 'ElasticSearch', '非关系数据库', '数据库', '1', '3', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('RABBITMQ', 'RabbitMQ', '分布式消息系统', '协议加载', '7', '2', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('DAMENG', '达梦数据库', '关系型数据库', '数据库', '1', '3', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('CLICKHOUSE', 'ClickHouse', '关系型数据库', '数据库', '1', '3', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_source_type` (`source_type`, `name`, `source_type_desc`, `category`, `sort`, `category_sort`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('PULSAR', 'Pulsar', '分布式消息系统', '协议加载', '8', '2', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('IDSS_RPC', '观安RPC', '远程传输协议', '协议加载', 11, 2, null, 1, null, null, null, null);

INSERT INTO `etl_reader_param_define` (`source_type`, `cn_config_key`, `config_key`, `config_type`, `config_desc`, `sort`, `config_defalut`, `is_required`, `config_level`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('LOCAL_FILE', '上传数据', 'uploadFile', '4', NULL, '1', NULL, '1', '1', '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_reader_param_define` (`source_type`, `cn_config_key`, `config_key`, `config_type`, `config_desc`, `sort`, `config_defalut`, `is_required`, `config_level`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('LOCAL_FILE', '文件路径', 'filePath', '1', NULL, '2', NULL, '0', '1', '1', NULL, NULL, NULL, NULL);


INSERT INTO `etl_parser_function` (`function_name`, `function_code`, `desccription`, `short_name`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('正则解析', 'REG', '正则解析', '正则解析', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_parser_function` (`function_name`, `function_code`, `desccription`, `short_name`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('Json解析', 'JSON', 'Json解析', 'Json解析', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_parser_function` (`function_name`, `function_code`, `desccription`, `short_name`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('KeyValue解析', 'KV', 'KeyValue解析', 'KeyValue解析', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO `etl_parser_function` (`function_name`, `function_code`, `desccription`, `short_name`, `icon`, `status`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('分隔符拆分', 'SPLIT', '分隔符拆分', '分隔符拆分', NULL, '1', NULL, NULL, NULL, NULL);


INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('FILTER', NULL, NULL, '数据过滤', '1', NULL, NULL, NULL, NULL, '9');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('REPLACE', NULL, NULL, '数据替换', '1', NULL, NULL, NULL, NULL, '10');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('DYNAMIC', NULL, NULL, '动态补全', '1', NULL, NULL, NULL, NULL, '30');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('STATIC', NULL, NULL, '静态补全', '1', NULL, NULL, NULL, NULL, '40');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('DATETRANS', NULL, NULL, '时间转换', '1', NULL, NULL, NULL, NULL, '50');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('SUBSTRING', NULL, NULL, '数据截取', '1', NULL, NULL, NULL, NULL, '60');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('MASK', NULL, NULL, '数据脱敏', '1', NULL, NULL, NULL, NULL, '70');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('CONCAT', NULL, NULL, '数据合并', '1', NULL, NULL, NULL, NULL, '80');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('IP2REGION', NULL, NULL, 'IP解析扩展', '1', NULL, NULL, NULL, NULL, '100');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('REMOVE', NULL, NULL, '字段移除', '1', NULL, NULL, NULL, NULL, '110');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('SYSLOGPRI', NULL, NULL, 'SyslogPri', '1', NULL, NULL, NULL, NULL, '120');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('PERSON', NULL, NULL, '自然人信息补全', '1', NULL, NULL, NULL, NULL, '1');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('DEVICE', NULL, NULL, '设备信息补全', '1', NULL, NULL, NULL, NULL, '2');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('APP', NULL, NULL, '应用信息补全', '1', NULL, NULL, NULL, NULL, '3');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('DATA', NULL, NULL, '数据信息补全', '1', NULL, NULL, NULL, NULL, '4');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('JAVATRANSFORM', NULL, NULL, 'Java程序及脚本', '1', NULL, NULL, NULL, NULL, '90');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('KeyValue', NULL, NULL, 'KeyValue', '1', NULL, NULL, NULL, NULL, '51');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('SUBDOMAIN', NULL, NULL, '子域名拆分', '1', NULL, NULL, NULL, NULL, '130');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('JSON', NULL, NULL, 'JSON提取', '1', NULL, NULL, NULL, NULL, '55');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('SQL', NULL, NULL, 'SQL提取', '1', NULL, NULL, NULL, NULL, '58');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('IP2SEGMENT', NULL, NULL, 'IP信息补全', '1', NULL, NULL, NULL, NULL, '7');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('SPLIT', NULL, NULL, '字符串分割', '1', NULL, NULL, NULL, NULL, '102');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('STR_REPLACE', NULL, NULL, '字符替换', '1', NULL, NULL, NULL, NULL, '104');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('URL', NULL, NULL, 'URL解析扩展', '1', NULL, NULL, NULL, NULL, '125');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('BASE64_DECODE', NULL, NULL, '数据解密', '1', NULL, NULL, NULL, NULL, '56');
INSERT INTO `etl_transform_function` (`code`, `trans_func`, `description`, `short_name`, `status`, `create_user`, `create_date`, `update_user`, `update_date`, `show_order`) VALUES ('URL_DECODE_ADD', NULL, NULL, '数据解码', '1', NULL, NULL, NULL, NULL, '57');

INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备', '安全设备', '1', NULL, '安全设备', NULL, '2021-11-25 16:24:09', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/流量检测', '流量检测', '14', '/安全设备', '安全设备/流量检测', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/防火墙', '防火墙', '1', '/安全设备', '安全设备/防火墙', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/网页防篡改', '网页防篡改', '15', '/安全设备', '安全设备/网页防篡改', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/安全防护网关(UTM)', '安全防护网关(UTM)', '2', '/安全设备', '安全设备/安全防护网关(UTM)', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/防病毒网关', '防病毒网关', '16', '/安全设备', '安全设备/防病毒网关', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/Web应用安全网关(WAF)', 'Web应用安全网关(WAF)', '3', '/安全设备', '安全设备/Web应用安全网关(WAF)', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/防病毒系统', '防病毒系统', '17', '/安全设备', '安全设备/防病毒系统', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/VPN设备', 'VPN设备', '4', '/安全设备', '安全设备/VPN设备', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/终端安全', '终端安全', '18', '/安全设备', '安全设备/终端安全', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/安全隔离与信息交换(网闸)', '安全隔离与信息交换(网闸)', '5', '/安全设备', '安全设备/安全隔离与信息交换(网闸)', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/DLP', 'DLP', '19', '/安全设备', '安全设备/DLP', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/APT', 'APT', '6', '/安全设备', '安全设备/APT', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/反垃圾邮件网关', '反垃圾邮件网关', '20', '/安全设备', '安全设备/反垃圾邮件网关', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/数据库审计', '数据库审计', '7', '/安全设备', '安全设备/数据库审计', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/TDA', 'TDA', '21', '/安全设备', '安全设备/TDA', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/堡垒机', '堡垒机', '8', '/安全设备', '安全设备/堡垒机', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/蜜罐', '蜜罐', '22', '/安全设备', '安全设备/蜜罐', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/日志审计', '日志审计', '9', '/安全设备', '安全设备/日志审计', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/身份认证', '身份认证', '23', '/安全设备', '安全设备/身份认证', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/入侵检测IDS', '入侵检测IDS', '10', '/安全设备', '安全设备/入侵检测IDS', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/终端接入控制', '终端接入控制', '24', '/安全设备', '安全设备/终端接入控制', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/入侵防御IPS', '入侵防御IPS', '11', '/安全设备', '安全设备/入侵防御IPS', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/漏洞扫描', '漏洞扫描', '25', '/安全设备', '安全设备/漏洞扫描', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/抗DDOS设备', '抗DDOS设备', '12', '/安全设备', '安全设备/抗DDOS设备', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/其他', '其他', '26', '/安全设备', '安全设备/其他', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/安全设备/流量控制系统', '流量控制系统', '13', '/安全设备', '安全设备/流量控制系统', NULL, '2021-11-25 16:24:15', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/服务器', '服务器', '2', NULL, '服务器', NULL, '2021-11-25 16:24:09', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/网络设备/网管系统', '网管系统', '5', '/网络设备', '网络设备/网管系统', NULL, '2021-11-25 16:24:14', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/服务器/Linux', 'Linux', '1', '/服务器', '服务器/Linux', NULL, '2021-11-25 16:24:13', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/网络设备/打印机', '打印机', '6', '/网络设备', '网络设备/打印机', NULL, '2021-11-25 16:24:14', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/服务器/Windows', 'Windows', '2', '/服务器', '服务器/Windows', NULL, '2021-11-25 16:24:13', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/网络设备/其他', '其他', '7', '/网络设备', '网络设备/其他', NULL, '2021-11-25 16:24:14', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/服务器/AIX ', 'AIX ', '3', '/服务器', '服务器/AIX ', NULL, '2021-11-25 16:24:13', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/存储设备', '存储设备', '4', NULL, '存储设备', NULL, '2021-11-25 16:24:09', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/服务器/HP-UX ', 'HP-UX ', '4', '/服务器', '服务器/HP-UX ', NULL, '2021-11-25 16:24:13', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/存储设备/EMC', 'EMC', '1', '/存储设备', '存储设备/EMC', NULL, '2021-11-25 16:24:12', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/服务器/Solaris', 'Solaris', '5', '/服务器', '服务器/Solaris', NULL, '2021-11-25 16:24:13', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/存储设备/Netapp', 'Netapp', '2', '/存储设备', '存储设备/Netapp', NULL, '2021-11-25 16:24:12', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/服务器/中标麒麟', '中标麒麟', '6', '/服务器', '服务器/中标麒麟', NULL, '2021-11-25 16:24:13', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/存储设备/HP', 'HP', '3', '/存储设备', '存储设备/HP', NULL, '2021-11-25 16:24:12', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/服务器/其他', '其他', '7', '/服务器', '服务器/其他', NULL, '2021-11-25 16:24:13', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/存储设备/IBM', 'IBM', '4', '/存储设备', '存储设备/IBM', NULL, '2021-11-25 16:24:12', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/网络设备', '网络设备', '3', NULL, '网络设备', NULL, '2021-11-25 16:24:09', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/存储设备/VERITA', 'VERITA', '5', '/存储设备', '存储设备/VERITA', NULL, '2021-11-25 16:24:12', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/网络设备/交换机', '交换机', '1', '/网络设备', '网络设备/交换机', NULL, '2021-11-25 16:24:14', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/存储设备/其他', '其他', '6', '/存储设备', '存储设备/其他', NULL, '2021-11-25 16:24:12', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/网络设备/路由器', '路由器', '2', '/网络设备', '网络设备/路由器', NULL, '2021-11-25 16:24:14', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/虚拟化设备', '虚拟化设备', '5', NULL, '虚拟化设备', NULL, '2021-11-25 16:24:09', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/网络设备/负载均衡', '负载均衡', '3', '/网络设备', '网络设备/负载均衡', NULL, '2021-11-25 16:24:14', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/虚拟化设备/KVM', 'KVM', '1', '/虚拟化设备', '虚拟化设备/KVM', NULL, '2021-11-25 16:24:10', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/网络设备/无线AP', '无线AP', '4', '/网络设备', '网络设备/无线AP', NULL, '2021-11-25 16:24:14', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/虚拟化设备/Xen', 'Xen', '2', '/虚拟化设备', '虚拟化设备/Xen', NULL, '2021-11-25 16:24:10', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/虚拟化设备/ESXi', 'ESXi', '3', '/虚拟化设备', '虚拟化设备/ESXi', NULL, '2021-11-25 16:24:10', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/虚拟化设备/Hyper-V', 'Hyper-V', '4', '/虚拟化设备', '虚拟化设备/Hyper-V', NULL, '2021-11-25 16:24:10', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/虚拟化设备/Xenserver', 'Xenserver', '5', '/虚拟化设备', '虚拟化设备/Xenserver', NULL, '2021-11-25 16:24:10', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/虚拟化设备/其他', '其他', '6', '/虚拟化设备', '虚拟化设备/其他', NULL, '2021-11-25 16:24:10', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/机房设备', '机房设备', '6', NULL, '机房设备', NULL, '2021-11-25 16:24:09', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/机房设备/安防', '安防', '1', '/机房设备', '机房设备/安防', NULL, '2021-11-25 16:24:11', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/机房设备/动力', '动力', '2', '/机房设备', '机房设备/动力', NULL, '2021-11-25 16:24:11', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/机房设备/环境', '环境', '3', '/机房设备', '机房设备/环境', NULL, '2021-11-25 16:24:11', NULL, NULL);
INSERT INTO `etl_parser_rule_category` (`asset_type`, `asset_code`, `asset_label`, `show_order`, `parent_code`, `asset_desc`, `create_user`, `create_time`, `update_user`, `update_time`) VALUES ('device', '/机房设备/其他', '其他', '4', '/机房设备', '机房设备/其他', NULL, '2021-11-25 16:24:11', NULL, NULL);

INSERT INTO `etl_wide_table_column` (`table_name`, `column_name`, `data_type`, `data_desc`) VALUES (NULL, 'model_view', 'Nullable(String)', '用于保存跑哪些内置模型');

INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('1', '认证类场景（操作系统）', 'auth_device', '包括了主机、网络设备、安全设备等登录行为的数据');
INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('2', '认证类场景（应用系统）', 'auth_app', '包括了数据库、业务应用等登录行为的数据');
INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('3', 'icmp协议', 'icmp', '');
INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('4', 'WEB', 'web', 'HTTP协议日志/HTTP代理/nginx');
INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('5', 'DNS', 'dns', 'DNS协议日志');
INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('6', 'linux', 'linux', 'Linux系统的命令执行审计日志');
INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('7', 'netflow', 'netflow', '流量探针设备');
INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('8', '蜜罐', 'honeypot', '蜜罐的告警日志');
INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('9', 'dlp', 'dlp', 'DLP的告警日志');
INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('10', '数据库审计', 'database', '数据库审计设备中的SQL请求日志');
INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('11', '堡垒机', 'bastionhost_opt', '堡垒机操作日志');
INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('12', '业务系统', 'business_opt', '业务系统的操作日志');
INSERT INTO `etl_wide_table_view` (`id`, `view_name`, `model_view`, `view_desc`) VALUES ('13', '防火墙', 'firewall', '防火墙中记录的流量数据');


INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('3', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('3', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('3', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('3', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('3', 'dst_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('3', 'dst_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('3', 'dst_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('3', 'dst_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('3', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'src_person_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'src_person_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'src_person_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'dst_account', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'dst_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'dst_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'dst_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'dst_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'program_name', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'process_pid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'process_p_pid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'process_cmdline', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('6', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'src_port', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'dst_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'dst_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'dst_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'dst_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'dst_app_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'dst_app_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'dst_app_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'dst_port', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_ack', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_fin_ack_packets', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_fin_packets', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_psh_ack_packets', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_psh_packets', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_received_app_bytes', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_received_content_packets', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_rst_ack_packets', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_rst_packets', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_sent_app_bytes', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_sent_content_packets', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_syn', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_syn_ack', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_syn_ack_packets', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_syn_packets', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_trans_protocol', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'netflow_dns_ttl', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('7', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'generic_event_id', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'generic_datasource_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'src_port', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'dst_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'dst_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'dst_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'dst_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'dst_port', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'program_name', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'external_alarm_attack', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'external_alarm_attack_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'external_alarm_av_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'external_alarm_risk_level', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('8', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'dst_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'dst_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'dst_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'dst_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'dst_app_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'dst_app_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'dst_app_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'dst_app_name', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'http_url_externalurl_parameter', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'netflow_dns_qr', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'netflow_dns_rrname', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'netflow_dns_rrtype', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'netflow_dns_rcode', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('5', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'dst_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'dst_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'dst_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'dst_app_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'dst_app_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'dst_app_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'dst_port', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'netflow_ack', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'netflow_ack_packets', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'netflow_psh_ack_packets', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'netflow_psh_packets', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'netflow_syn', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'netflow_syn_ack', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'netflow_syn_ack_packets', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'netflow_syn_packets', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'netflow_trans_protocol', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('13', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'eqpt_asset_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'generic_datasource_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'generic_event_type', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'generic_opt_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'generic_opt_content', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'result_action', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'src_person_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'src_person_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'src_person_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'src_person_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'src_person_types', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'src_person_org_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'src_device_mac', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'src_port', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_person_uuid', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_person_uuid_data', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_person_uuid_status', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_person_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_account', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_account_group', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_device_mac', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_app_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_app_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_app_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_app_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_port', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'dst_app_url', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'data_hash', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'netflow_answers_info', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'netflow_flags', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'netflow_rpc_auth_type', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'external_alarm_risk_level', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'data_level', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'login_time', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('2', 'session_id', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'generic_datasource_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'generic_opt_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'result_action', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'src_person_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'src_person_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'src_person_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'src_port', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'dst_account', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'dst_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'dst_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'dst_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'dst_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'dst_port', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'netflow_flags', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('1', 'session_id', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'generic_datasource_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'result_action', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'src_person_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'src_person_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'src_person_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'src_device_ip_province', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'src_device_owner', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'dst_person_cellphone_no', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'dst_account', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'dst_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'dst_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'dst_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'dst_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'dst_device_network_domain', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'dst_app_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'dst_app_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'dst_app_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'dst_app_name', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'dst_port', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'data_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'data_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'data_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'database_table_name', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'file_name', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'file_path', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'file_share_local_path', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'mail_cc_username', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'process_cmdline', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'netflow_trans_protocol', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'netflow_dhcp_hostname', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'data_level', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'session_id', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'resource_name', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'resource_code', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('11', 'oprate_context', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'generic_datasource_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'result_action', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'src_person_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'src_person_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'src_person_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'src_account_group', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'src_device_mac', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'dst_person_org_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'dst_account', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'dst_account_group', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'dst_device_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'dst_app_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'dst_app_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'dst_app_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'data_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'data_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'data_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'data_name', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'data_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'data_owner', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'database_access_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'file_access_time', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'file_create_time', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'file_modify_time', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'file_path', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'file_size', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'file_share_local_path', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'file_share_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'program_name', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'mail_content', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'http_url_externalurl', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'http_httpprotocol', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'netflow_dns_cname', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'external_alarm_risk_level', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('9', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'eqpt_asset_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'generic_datasource_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'generic_opt_type', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'src_person_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'src_person_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'src_person_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'src_account', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'src_account_group', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'src_device_mac', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'src_port', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'dst_account', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'dst_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'dst_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'dst_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'dst_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'dst_device_mac', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'dst_app_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'dst_port', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'database_id', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'database_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'database_table_name', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'database_sql', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'database_type', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'process_cmdline', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'netflow_answers_info', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'netflow_dhcp_hostname', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'external_alarm_risk_level', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'data_level', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'login_time', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'response_code', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'response_data', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'rule_alarm_times', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'rule_id', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'rule_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'rule_policy', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'rule_match_num', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'session_id', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'resource_code', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'module_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'call_verified_result', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'call_in_param', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'entity_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'operate_object', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('10', 'log_txt_context', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'eqpt_ip', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'generic_event_id', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'generic_datasource_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'generic_event_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'generic_opt_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'generic_opt_object', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'generic_opt_content', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'result_action', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'src_person_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'src_person_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'src_person_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'src_person_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'src_person_ctpositionname', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'src_person_org_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'src_person_cellphone_no', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'person_mian_account', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'dst_account', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'dst_account_type', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'dst_app_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'dst_app_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'dst_app_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'dst_app_name', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'data_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'data_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'data_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'data_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'data_value', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'http_url_externalurl', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'http_cookie', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'netflow_flags', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'netflow_received_package_flow', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'netflow_dns_rrtype', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('12', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'generic_datasource_type', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'src_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'src_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'src_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'src_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'dst_device_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'dst_device_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'dst_device_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'dst_device_ip', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'dst_app_uuid', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'dst_app_uuid_data', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'dst_app_uuid_status', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'dst_app_name', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'dst_port', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'http_url_externalurl', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'http_url_externalurl_parameter', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'http_url_httpreferrer', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'http_httpagent', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'http_httpmethod', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'http_httpprotocol', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'http_httpprotocolversion', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'http_httpstatus', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'http_cookie', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'http_response_size', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'http_response_time', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'http_rsp_content_type', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'generic_create_time', '1', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'reserved_a', '0', NULL);
INSERT INTO `etl_wide_table_column_view` (`view_id`, `column_name`, `is_required`, `remark`) VALUES ('4', 'reserved_b', '0', NULL);

INSERT INTO `system_backup_table` VALUES (1, 'etl', 'etl_logmoudle');
INSERT INTO `system_backup_table` VALUES (2, 'etl', 'etl_parser');
INSERT INTO `system_backup_table` VALUES (3, 'etl', 'etl_parser_function');
INSERT INTO `system_backup_table` VALUES (4, 'etl', 'etl_parser_param');
INSERT INTO `system_backup_table` VALUES (5, 'etl', 'etl_parser_param_tmp');
INSERT INTO `system_backup_table` VALUES (6, 'etl', 'etl_parser_reg');
INSERT INTO `system_backup_table` VALUES (7, 'etl', 'etl_parser_reg_tmp');
INSERT INTO `system_backup_table` VALUES (8, 'etl', 'etl_parser_template');
INSERT INTO `system_backup_table` VALUES (9, 'etl', 'etl_reader_param');
INSERT INTO `system_backup_table` VALUES (10, 'etl', 'etl_reader_param_define');
INSERT INTO `system_backup_table` VALUES (11, 'etl', 'etl_source');
INSERT INTO `system_backup_table` VALUES (12, 'etl', 'etl_source_optlog');
INSERT INTO `system_backup_table` VALUES (13, 'etl', 'etl_source_set');
INSERT INTO `system_backup_table` VALUES (14, 'etl', 'etl_source_type');
INSERT INTO `system_backup_table` VALUES (15, 'etl', 'etl_transform_action');
INSERT INTO `system_backup_table` VALUES (16, 'etl', 'etl_transform_condition');
INSERT INTO `system_backup_table` VALUES (17, 'etl', 'etl_transform_function');
INSERT INTO `system_backup_table` VALUES (18, 'etl', 'etl_transform_rule');
INSERT INTO `system_backup_table` VALUES (19, 'etl', 'etl_transform_table');
INSERT INTO `system_backup_table` VALUES (20, 'etl', 'etl_writer_ch_config');
INSERT INTO `system_backup_table` VALUES (21, 'etl', 'etl_writer_config');
INSERT INTO `system_backup_table` VALUES (22, 'etl', 'etl_writer_es_config');
INSERT INTO `system_backup_table` VALUES (23, 'etl', 'etl_writer_table_config');
INSERT INTO `system_backup_table` VALUES (24, 'etl', 'etl_writer_table_define');
INSERT INTO `system_backup_table` VALUES (25, 'base', 'ueba_dictionary');
INSERT INTO `system_backup_table` VALUES (26, 'base', 'ums_sys_holiday');
INSERT INTO `system_backup_table` VALUES (28, 'user', 'ums_sys_org');
INSERT INTO `system_backup_table` VALUES (29, 'user', 'ums_sys_org_dept');
INSERT INTO `system_backup_table` VALUES (30, 'user', 'ums_sys_password_policy');
INSERT INTO `system_backup_table` VALUES (31, 'user', 'ums_sys_role');
INSERT INTO `system_backup_table` VALUES (32, 'user', 'ums_sys_role_ch_es');
INSERT INTO `system_backup_table` VALUES (33, 'user', 'ums_sys_role_data');
INSERT INTO `system_backup_table` VALUES (34, 'user', 'ums_sys_role_extend');
INSERT INTO `system_backup_table` VALUES (35, 'user', 'ums_sys_role_function');
INSERT INTO `system_backup_table` VALUES (36, 'user', 'ums_sys_user');
INSERT INTO `system_backup_table` VALUES (37, 'user', 'ums_sys_user_role');
INSERT INTO `system_backup_table` VALUES (54, 'Dashboard', 'ai_report_config');
INSERT INTO `system_backup_table` VALUES (55, 'Dashboard', 'ai_report_group');
INSERT INTO `system_backup_table` VALUES (56, 'Dashboard', 'ai_report_history');

INSERT INTO `dataset_category`( `id`,`name`, `level`, `operator`, `parent_id`, `create_time`, `update_time`) VALUES ( 1,'自定义', 1, 'sysadmin', NULL, now(), now());
