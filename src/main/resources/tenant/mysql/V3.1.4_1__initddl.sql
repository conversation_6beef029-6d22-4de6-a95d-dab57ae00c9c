/*
Navicat MySQL Data Transfer

Source Server         : ueba_test
Source Server Version : 50724
Source Host           : ************:3306
Source Database       : ueba

Target Server Type    : MYSQL
Target Server Version : 50724
File Encoding         : 65001

Date: 2022-05-20 15:00:06
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for ums_sys_role_function
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_role_function` (
  `role_function_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色操作权限编号',
  `role_id` varchar(32) NOT NULL COMMENT '角色编号',
  `function_id` text NOT NULL COMMENT '操作权限编号',
  `value` varchar(32) DEFAULT NULL COMMENT '值',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` bigint(20) DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` bigint(20) DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`role_function_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for ums_sys_user_role
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_user_role` (
  `user_role_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '人员角色编号',
  `user_id` bigint(32) NOT NULL COMMENT '用户编号',
  `role_id` varchar(32) NOT NULL COMMENT '角色编号',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` bigint(20) DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` bigint(20) DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`user_role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE If Not Exists `ums_sys_role` (
  `role_id` varchar(32) NOT NULL COMMENT '角色编号',
  `role_name` varchar(100) DEFAULT NULL,
  `role_describe` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `builtin` tinyint(1) NOT NULL COMMENT '是否预置\r\n0:预置\r\n1:未预置',
  `del_flag` tinyint(1) NOT NULL COMMENT '删除标识\r\n0:未删除\r\n1:已删除',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` bigint(20) DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` bigint(20) DEFAULT NULL COMMENT '更新日期',
  `is_admin` varchar(1) DEFAULT NULL COMMENT '是否超级管理员',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Table structure for ums_sys_user
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_user` (
  `user_id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '用户编号',
  `dept_id` varchar(100) DEFAULT NULL COMMENT '部门编号',
  `user_name` varchar(300) DEFAULT NULL,
  `password` varchar(32) NOT NULL COMMENT '密码',
  `password_old` varchar(32) DEFAULT NULL COMMENT '旧密码',
  `period_from` bigint(20) NOT NULL COMMENT '账号使用开始日',
  `period_to` bigint(20) NOT NULL COMMENT '账号使用结束日',
  `status` tinyint(1) NOT NULL COMMENT '状态\r\n0:正常\r\n1:锁定',
  `real_name` varchar(300) DEFAULT NULL,
  `code` varchar(100) DEFAULT NULL COMMENT '员工工号',
  `sex` varchar(1) DEFAULT NULL COMMENT '员工性别\r\n0:男\r\n1:女',
  `telephone` varchar(20) DEFAULT NULL,
  `email` varchar(100) NOT NULL COMMENT '电子邮箱',
  `leader` varchar(100) DEFAULT NULL COMMENT '直属领导',
  `login_voucher` varchar(100) DEFAULT NULL COMMENT '单点登录凭证',
  `fail_count` int(11) DEFAULT NULL COMMENT '失败次数',
  `lock_date` datetime DEFAULT NULL COMMENT '锁定日期',
  `final_login_ip` varchar(64) DEFAULT NULL COMMENT '最后登录ip',
  `final_login_date` bigint(20) DEFAULT NULL COMMENT '最后登录时间',
  `builtin` tinyint(1) NOT NULL COMMENT '是否预置\r\n0：预置\r\n1:未预置',
  `security_code` varchar(128) DEFAULT NULL,
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NOT NULL COMMENT '删除标识\r\n0:未删除\r\n1:已删除',
  `tenant_id` int(11) DEFAULT NULL COMMENT '租户ID',
  `queue` varchar(64) DEFAULT NULL COMMENT '队列名称',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` bigint(20) DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` bigint(20) DEFAULT NULL COMMENT '更新日期',
  `social_account` varchar(100) DEFAULT NULL COMMENT '社交账号',
  `first_login_fail_time` datetime DEFAULT NULL COMMENT '连续登陆失败时第一次登录时间',
  `is_first_login` varchar(1) DEFAULT NULL COMMENT '是否第一次登录',
  `is_need_update_password` varchar(1) DEFAULT NULL COMMENT '第一次登录时是否需要修改密码',
  `default_router_id` varchar(100) DEFAULT NULL COMMENT '默认应用id',
  `default_router_name` varchar(100) DEFAULT NULL COMMENT '默认应用路由',
  `default_dashboard` INTEGER(20) DEFAULT NULL COMMENT '默认仪表盘id',
  `mobile` varchar(11) DEFAULT NULL COMMENT '手机号码',
  `dept_name` varchar(100) DEFAULT NULL COMMENT '部门名称',
  `last_update_password_time` datetime DEFAULT NULL,
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- ----------------------------
-- Table structure for backup_record
-- ----------------------------
CREATE TABLE If Not Exists `backup_record` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `config_id` bigint(32) DEFAULT NULL COMMENT '配置id',
  `name` varchar(50) DEFAULT NULL COMMENT '表名',
  `type` varchar(50) DEFAULT NULL COMMENT '操作类型:backup-备份/recover-恢复',
  `backup_desc` varchar(255) NOT NULL COMMENT '备份明细',
  `backup_result` varchar(255) NOT NULL COMMENT '备份结果',
  `backup_result_desc` varchar(255) DEFAULT NULL COMMENT '备份结果描述',
  `file_name` varchar(255) DEFAULT NULL COMMENT '备份文件名称',
  `file_dir` varchar(255) DEFAULT NULL COMMENT '备份文件存储位置',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识',
  `backup_id` bigint(30) DEFAULT NULL,
  `execute_result` text,
  `backup_storage` varchar(255) DEFAULT NULL COMMENT '当前备份记录存放位置',
  `recover_status` varchar(2) DEFAULT '0' COMMENT '0-未恢复；1-恢复中；2-已恢复；3-恢复失败',
  `log_file_path` varchar(255) DEFAULT NULL COMMENT '日志文件路径',
  `log_file_name` varchar(255) DEFAULT NULL COMMENT '日志文件名称',
  `total_rows` bigint(32) DEFAULT NULL COMMENT '文件总数',
  `total_capacity` bigint(32) DEFAULT NULL COMMENT '文件总容量',
  `recover_bak_table` varchar(100) DEFAULT NULL COMMENT '新建表',
  `backup_type` varchar(64) DEFAULT NULL COMMENT '备份类型：备份/归档',
  `backup_policy` varchar(64) DEFAULT NULL COMMENT '策略：all-全量/increment-增量',
  `storage` varchar(64) DEFAULT NULL COMMENT '文件存储位置',
  `recover_rows` bigint(20) DEFAULT '0' COMMENT '恢复个数',
  `recover_same` varchar(2) DEFAULT '0' COMMENT '恢复是否一致',
  `md5_hashcode` varchar(128) DEFAULT NULL COMMENT '文件计算后的hash',
  `sign` varchar(2056) DEFAULT NULL COMMENT 'sm2的签名',
  `recover_time` DATETIME DEFAULT NULL COMMENT '恢复时间',
  `backup_start_time` DATETIME DEFAULT NULL COMMENT '备份分区开始时间',
  `backup_end_time` DATETIME DEFAULT NULL COMMENT '备份分区结束时间',
  `execute_start_time` DATETIME DEFAULT NULL COMMENT '执行开始时间',
  `execute_end_time` DATETIME DEFAULT NULL COMMENT '执行结束时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- ----------------------------
-- Table structure for backup_config
-- ----------------------------
CREATE TABLE If Not Exists `backup_config` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `type` varchar(20) DEFAULT 'ch' COMMENT '备份数据库:ch,mysql',
  `table_name` varchar(50) DEFAULT NULL COMMENT '对象名称',
  `backup_type` varchar(50) DEFAULT NULL COMMENT '备份类型：备份/归档',
  `backup_policy` varchar(50) NOT NULL COMMENT '策略：all-全量/increment-增量',
  `increment_field` varchar(50) DEFAULT NULL COMMENT '增量备份是依赖字段',
  `storage` varchar(50) DEFAULT NULL COMMENT '文件存储位置',
  `storage_volume` int(10) DEFAULT NULL COMMENT '存储容量',
  `volume_unit` varchar(10) DEFAULT NULL COMMENT '容量单位',
  `storage_day` int(5) DEFAULT NULL COMMENT '存储天数',
  `execute_period` varchar(10) DEFAULT NULL COMMENT '执行周期：cycle-周期/single-单次',
  `execute_plan` int(5) DEFAULT NULL COMMENT '执行计划：每隔多少天执行',
  `execute_time` varchar(20) DEFAULT NULL COMMENT '执行时间HH:mm:ss',
  `execute_cron` varchar(512) DEFAULT NULL,
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识',
  `status` varchar(1) DEFAULT NULL,
  `ip` varchar(20) DEFAULT NULL,
  `port` varchar(20) DEFAULT NULL,
  `data_base` varchar(20) DEFAULT NULL,
  `time_type` varchar(20) DEFAULT NULL COMMENT '归档/删除基准线',
  `time_config` varchar(256) DEFAULT NULL COMMENT '定时时间配置',
  `is_recover` varchar(2) DEFAULT NULL COMMENT '是否定时恢复，1-是；0-否',
  `task_name` varchar(256) DEFAULT NULL COMMENT '任务名',
  `backup_method` VARCHAR(1) DEFAULT NULL COMMENT '备份方式:1-节点备份；2-业务备份',
  `recover_config` VARCHAR(1024) DEFAULT NULL COMMENT '恢复配置json',
  `last_finish_time` DATETIME DEFAULT NULL COMMENT '最后一次执行时间',
  `backup_result` VARCHAR(1) DEFAULT NULL COMMENT '最后一次执行结果',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ums_sys_datasource_config
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_datasource_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `moudle_name` varchar(32) DEFAULT NULL COMMENT '配置模块 dashboard-仪表盘  schedule-模型调度',
  `datasource_type` varchar(16) DEFAULT NULL COMMENT '数据源类型',
  `ip` varchar(64) DEFAULT NULL COMMENT 'IP地址',
  `db_name` varchar(64) DEFAULT NULL COMMENT '数据库名',
  `port` varchar(16) DEFAULT NULL COMMENT '端口',
  `user_name` varchar(128) DEFAULT NULL COMMENT '用户名',
  `password` varchar(128) DEFAULT NULL COMMENT '密码',
  `show_name` varchar(128) DEFAULT NULL COMMENT '展示名',
  `time_out` int(11) DEFAULT NULL COMMENT '超时时间',
  `encrypt` varchar(2) DEFAULT NULL COMMENT '是否加密 1-加密 0-不加密',
  `status` varchar(2) DEFAULT NULL COMMENT '1-正常 0-删除',
  `copy_cnt` int(11) DEFAULT NULL COMMENT '复制次数',
  `db_url` varchar(512) DEFAULT NULL COMMENT '数据库连接字符串',
  `create_user` varchar(16) DEFAULT NULL COMMENT '创建账号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(16) DEFAULT NULL COMMENT '更新账号',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据源配置';


-- ----------------------------
-- Table structure for system_tables
-- ----------------------------
CREATE TABLE If Not Exists `system_tables` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '指定类型数据库',
  `database` varchar(256) DEFAULT NULL COMMENT '数据库名',
  `table_name` varchar(256) DEFAULT NULL COMMENT '表名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='Ueba的系统表，非脚本等新建的表。该表需要手动维护。';


-- ----------------------------
-- Table structure for backup_recover
-- ----------------------------
CREATE TABLE If Not Exists `backup_recover` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `config_id` int(32) NOT NULL COMMENT 'config表id',
  `execute_type` varchar(100) DEFAULT NULL COMMENT '运行类型：hour-每小时，day-每天，week-每周，month-每月，cron-表达式',
  `execute_value` varchar(255) DEFAULT NULL COMMENT '时间值',
  `execute_cron` varchar(512) DEFAULT NULL,
  `create_user` varchar(256) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '新增时间',
  `update_user` varchar(256) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- ----------------------------
-- Table structure for ueba_dictionary
-- ----------------------------
CREATE TABLE If Not Exists `ueba_dictionary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `key_code` varchar(255) NOT NULL COMMENT '字典编码',
  `value` varchar(255) DEFAULT NULL COMMENT '字典值',
  `enable` int(11) DEFAULT NULL COMMENT '是否可用 (0-不可用 1-可用)',
  `remark` varchar(4000) DEFAULT NULL COMMENT '备注',
  `sortno` int(10) DEFAULT NULL COMMENT '排序',
  `type` varchar(255) DEFAULT NULL COMMENT '字典类型',
  `parent_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for ums_sys_role_data
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_role_data` (
  `role_data_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色数据权限编号',
  `role_id` varchar(32) NOT NULL COMMENT '角色编号',
  `function_id` text NOT NULL COMMENT '操作权限编号',
  `data_id` text NOT NULL COMMENT '数据权限编号',
  `value` varchar(32) DEFAULT NULL,
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` bigint(20) DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` bigint(20) DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`role_data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Table structure for ums_create_table_record
-- ----------------------------
CREATE TABLE If Not Exists `ums_create_table_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `table_name` varchar(32) NOT NULL DEFAULT '' COMMENT '表名',
  `flag` int(11) NOT NULL DEFAULT '1' COMMENT '建表状态,1-正常 0-删除',
  `create_user` varchar(32) NOT NULL DEFAULT '' COMMENT '创建者',
  `update_user` varchar(32) NOT NULL DEFAULT '' COMMENT '修改者',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数仓创建记录表';


-- ----------------------------
-- Table structure for ums_sys_role_extend
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_role_extend` (
  `extend_id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '继承id',
  `role_id` varchar(255) NOT NULL COMMENT '角色id',
  `extend_role_id` varchar(255) DEFAULT NULL COMMENT '继承的角色id',
  `flag` varchar(1) DEFAULT '1' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`extend_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE If Not Exists `ums_sys_role_ch_es` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `type` varchar(100) DEFAULT NULL COMMENT '类型：es,ch',
  `role_id` varchar(100) DEFAULT NULL COMMENT '角色id',
  `table_name` varchar(100) NOT NULL COMMENT '表名',
  `query_condition` text COMMENT '查询条件参数',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- ----------------------------
-- Table structure for ums_sys_org
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_org` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_code` varchar(255) DEFAULT NULL COMMENT '组织编码',
  `org_name` varchar(255) DEFAULT NULL COMMENT '组织名称',
  `org_full_name` varchar(512) DEFAULT NULL COMMENT '组织全称',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标识：0-已删除；1-未删除',
  `parent_org_code` varchar(100) DEFAULT 'sdc' NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Table structure for system_backup_config
-- ----------------------------
CREATE TABLE If Not Exists `system_backup_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_type` varchar(64) NOT NULL DEFAULT '' COMMENT '配置类型',
  `backup_time` date DEFAULT NULL COMMENT '备份时间',
  `backup_file` varchar(128) NOT NULL DEFAULT '' COMMENT '备份文件地址',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0-删除 1-有效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `config_name` varchar(64) NOT NULL DEFAULT '' COMMENT '配置名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='备份配置管理';


-- ----------------------------
-- Table structure for system_backup_table
-- ----------------------------
CREATE TABLE If Not Exists `system_backup_table` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `config_type` varchar(32) NOT NULL DEFAULT '',
  `table_name` varchar(32) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Table structure for ums_sys_org_dept
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_org_dept` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` bigint(20) NOT NULL COMMENT '组织唯一标识',
  `dept_id` varchar(100) DEFAULT NULL COMMENT '部门唯一标识',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for ums_sys_password_policy
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_password_policy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `min_length` int(11) DEFAULT NULL COMMENT '最小字符数',
  `max_length` int(11) DEFAULT NULL COMMENT '最大长度',
  `is_lower_case` varchar(1) DEFAULT NULL COMMENT '是否包含小写:0-否，1-是',
  `is_big_case` varchar(1) DEFAULT NULL COMMENT '是否包含大写:0-否，1-是',
  `is_special_char` varchar(1) DEFAULT NULL COMMENT '是否包含特殊字符:0-否，1-是',
  `rule_open` varchar(1) DEFAULT NULL COMMENT '规则是否开启：0-否，1-是',
  `expiration` int(11) DEFAULT NULL COMMENT '密码有效期天数',
  `alter_day` int(11) DEFAULT NULL COMMENT '密码到期前提醒天数',
  `expiration_open` varchar(1) DEFAULT NULL COMMENT '过期时间策略是否开启：0-否，1-是',
  `login_num` int(11) DEFAULT NULL COMMENT '登录失败次数',
  `lock_threshold` int(11) DEFAULT NULL COMMENT '锁定阈值分钟',
  `lock_time` int(11) DEFAULT NULL COMMENT '锁定持续分钟',
  `lock_open` varchar(1) DEFAULT NULL COMMENT '锁定策略是否开启：0-否，1-是',
  `logout_time` int(11) DEFAULT NULL COMMENT '登出时间',
  `logout_open` varchar(1) DEFAULT NULL COMMENT '登出时间策略是否开启：0-否，1-是',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  `flag` varchar(1) DEFAULT NULL COMMENT '有效标识：0-无效，1-有效',
  `is_number` varchar(1) DEFAULT NULL COMMENT '是否包含数字：0-否，1-是',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for ums_sys_menus
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_menus` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `menu_type` varchar(32) NOT NULL COMMENT '菜单类型(builtIn-内置菜单 custom-自定义菜单)',
  `menu_property` varchar(2) NOT NULL COMMENT '菜单属性 0-不允许有子菜单 1-允许有子菜单',
  `menu_level` int(11) NOT NULL COMMENT '菜单层级',
  `menu_name` varchar(64) NOT NULL COMMENT '菜单名称',
  `menu_code` varchar(64) NOT NULL COMMENT '菜单唯一标识',
  `menu_path` varchar(512) DEFAULT NULL COMMENT '菜单路径',
  `manage_free` varchar(2) DEFAULT '0',
  `hidden` varchar(2) DEFAULT '0' COMMENT '是否隐藏',
  `parent_name` varchar(64) DEFAULT NULL COMMENT '父级菜单',
  `status` varchar(2) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `menu_order` int(11) DEFAULT NULL COMMENT '菜单顺序',
  `default_order` int(11) NOT NULL COMMENT '默认顺序',
  `default_name` varchar(64) NOT NULL COMMENT '默认名称',
  `default_status` varchar(2) NOT NULL COMMENT '默认状态',
  `default_parent` varchar(64) DEFAULT NULL COMMENT '默认父级',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  `root_parent` varchar(64) DEFAULT NULL COMMENT '菜单对于的一级菜单',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统菜单表';


-- ----------------------------
-- Table structure for ums_sys_calendar
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_calendar` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '日期',
  `day_of_year` int(11) NOT NULL COMMENT '年中的第几天',
  `day_of_month` int(11) NOT NULL COMMENT '月中的第几天',
  `day_of_week` int(11) NOT NULL COMMENT '星期中的第几天',
  `is_work` tinyint(4) NOT NULL COMMENT '1-工作  2-休息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Table structure for ums_sys_holiday
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_holiday` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(2) DEFAULT NULL COMMENT '类型 1-假期  2-补班',
  `name` varchar(128) DEFAULT NULL COMMENT '名称',
  `start_date` varchar(32) DEFAULT NULL COMMENT '开始日期',
  `end_date` varchar(32) DEFAULT NULL COMMENT '结束日期',
  `holiday_desc` varchar(1024) DEFAULT NULL COMMENT '描述',
  `holiday_year` varchar(8) DEFAULT NULL COMMENT '假期所属年份',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建新者',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新日期',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标识：0-已删除；1-未删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Table structure for ums_sys_license
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_license` (
  `license` varbinary(2048) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Table structure for ums_sys_log
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_name` varchar(16) NOT NULL DEFAULT '' COMMENT '用户名',
  `real_name` varchar(16) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `login_ip` varchar(32) NOT NULL DEFAULT '' COMMENT '登录ip',
  `user_agent` varchar(256) NOT NULL DEFAULT '' COMMENT 'UA',
  `request_path` varchar(64) NOT NULL DEFAULT '' COMMENT '请求路径',
  `log_name` varchar(2048) NOT NULL DEFAULT '' COMMENT '操作名称',
  `log_result` varchar(32) NOT NULL DEFAULT '' COMMENT '操作结果',
  `opt_type` varchar(32) NOT NULL DEFAULT '' COMMENT '操作类型',
  `opt_module` varchar(64) NOT NULL DEFAULT '' COMMENT '操作模块',
  `http_method` varchar(16) NOT NULL DEFAULT '' COMMENT '请求方式',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='日志审计表';



-- ----------------------------
-- Table structure for ums_sys_work_time
-- ----------------------------
CREATE TABLE If Not Exists `ums_sys_work_time` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `on_time` varchar(20) DEFAULT NULL,
  `out_time` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作时间配置';


CREATE TABLE If Not Exists `ums_sys_auth_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '登录配置标识',
  `auth_type` tinyint(3) NOT NULL COMMENT '认证类型, 0-原生体系，1-oauth2，2-4A',
  `enable_status` tinyint(3) NOT NULL COMMENT '启用状态，0-未启用，1-启用',
  `default_status` tinyint(3) NOT NULL COMMENT '默认状态，0-非默认，1-默认',
  `display_name` varchar(200) NOT NULL COMMENT '显示名称',
  `sync_status` tinyint(3) DEFAULT NULL COMMENT '同步状态',
  `enable_create_user` tinyint(3) NOT NULL COMMENT '是否创建新用户，0-否，1-是',
  `role_id` varchar(50) DEFAULT NULL COMMENT '用户角色',
  `grant_type` tinyint(3) DEFAULT NULL COMMENT '授权类型，0-授权式，1-隐藏式',
  `oauth_base_field_info` text COMMENT '基础字段信息',
  `oauth_code_request_url` varchar(1000) DEFAULT NULL COMMENT '请求code地址',
  `oauth_code_request_way` varchar(20) DEFAULT NULL COMMENT '请求code方式，get/post',
  `oauth_code_resp_field` varchar(50) DEFAULT NULL COMMENT '响应code字段',
  `oauth_code_field_info` text COMMENT '请求code参数信息',
  `oauth_token_request_url` varchar(1000) DEFAULT NULL COMMENT '请求token地址',
  `oauth_token_request_way` varchar(20) DEFAULT NULL COMMENT '请求token方式，get/post',
  `oauth_token_resp_field` varchar(50) DEFAULT NULL COMMENT '响应token字段',
  `oauth_token_resp_format` varchar(20) DEFAULT NULL COMMENT '响应token的格式，json/xml',
  `oauth_token_field_info` text COMMENT '请求token参数信息',
  `oauth_user_request_url` varchar(1000) DEFAULT NULL COMMENT '请求用户地址',
  `oauth_user_request_way` varchar(20) DEFAULT NULL COMMENT '请求user方式，get/post',
  `oauth_user_resp_field` varchar(50) DEFAULT NULL COMMENT '响应user字段',
  `oauth_user_resp_format` varchar(20) DEFAULT NULL COMMENT '响应user的格式，json/xml',
  `oauth_user_field_info` text COMMENT '请求user参数信息',
  `fa_app_field` varchar(50) DEFAULT NULL COMMENT '应用标识',
  `fa_login_url` varchar(1000) DEFAULT NULL COMMENT '登录地址',
  `fa_auth_url` varchar(1000) DEFAULT NULL COMMENT '认证地址',
  `fa_user_resp_field` varchar(50) DEFAULT NULL COMMENT '响应用户字段',
  `fa_request_protocol` varchar(20) DEFAULT NULL COMMENT '请求协议',
  `fa_method_name` varchar(50) DEFAULT NULL COMMENT 'webservice方法名',
  `fa_request_way` varchar(20) DEFAULT NULL COMMENT 'http请求方式，get/post',
  `fa_login_field_info` text COMMENT '登录参数信息',
  `fa_check_field_info` text COMMENT '认证参数信息',
  `fa_request_xml_template` varchar(1024) NULL comment '请求xml报文',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户认证配置';


CREATE TABLE If Not Exists `t_ds_tenant` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'key',
  `tenant_code` varchar(64) DEFAULT NULL COMMENT 'tenant code',
  `tenant_name` varchar(64) DEFAULT NULL COMMENT 'tenant name',
  `description` varchar(256) DEFAULT NULL,
  `queue_id` int(11) DEFAULT NULL COMMENT 'queue id',
  `create_time` datetime DEFAULT NULL COMMENT 'create time',
  `update_time` datetime DEFAULT NULL COMMENT 'update time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE If Not Exists `recover_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `task_name` varchar(255) DEFAULT NULL COMMENT '任务名称',
  `backup_record_id` bigint(20) DEFAULT NULL COMMENT '备份日志ID',
  `backup_time` datetime DEFAULT NULL COMMENT '备份时间',
  `recover_table_name` varchar(255) DEFAULT NULL COMMENT '恢复的表名',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_path` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `log_path` varchar(255) DEFAULT NULL COMMENT '错误日志路径',
  `recover_status` varchar(1) DEFAULT NULL COMMENT '恢复状态：0-待恢复,1-恢复中；2-恢复结束',
  `recover_same` varchar(1) DEFAULT NULL COMMENT '恢复是否一致：1-是；0-否',
  `backup_rows` bigint(20) DEFAULT NULL COMMENT '备份行数',
  `recover_rows` bigint(20) DEFAULT NULL COMMENT '恢复行数',
  `recover_result` varchar(1) DEFAULT NULL COMMENT '恢复结果：1-成功；0-失败',
  `recover_result_desc` varchar(512) DEFAULT NULL COMMENT '恢复接口描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识：1-有效；0-无效',
  `recover_id` bigint(20) DEFAULT NULL COMMENT '恢复父级id',
  `recover_start_time` datetime DEFAULT NULL COMMENT '恢复分区开始时间',
  `recover_end_time` datetime DEFAULT NULL COMMENT '恢复分区结束时间',
  `recover_type` varchar(1) DEFAULT NULL COMMENT '1-直接追加，2-清空表后追加；3-新表',
  `execute_start_time` DATETIME DEFAULT NULL COMMENT '执行开始时间',
  `execute_end_time` DATETIME DEFAULT NULL COMMENT '执行结束时间',
  PRIMARY KEY (`id`),
  KEY `backup_id_index` (`backup_record_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8 COMMENT='恢复记录表';


CREATE TABLE If Not Exists `dataset_accelerate_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `dataset_id` int(11) NOT NULL,
  `exec_type` varchar(16) NOT NULL DEFAULT '' COMMENT '执行类型(minute、hour、week、month)',
  `exec_value` varchar(256) NOT NULL DEFAULT '' COMMENT '执行具体的值',
  `exec_hour` int(11) DEFAULT NULL COMMENT '小时',
  `exec_minute` int(11) DEFAULT NULL COMMENT '分钟',
  `table_name` varchar(32) NOT NULL DEFAULT '' COMMENT '表名',
  `order_field` varchar(32) NOT NULL DEFAULT '' COMMENT '排序字段',
  `cluster_name` varchar(64) NOT NULL DEFAULT '' COMMENT '集群名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据集加速配置表';


CREATE TABLE If Not Exists `dataset_accelerate_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `dataset_id` int(11) NOT NULL,
  `exec_status` int(11) NOT NULL COMMENT '执行状态（0-失败 1-成功）',
  `exec_detail` varchar(512) NOT NULL DEFAULT '' COMMENT '详细信息',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `count` int(11) DEFAULT NULL COMMENT '加速生成的数量',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据集操作日志';


CREATE TABLE If Not Exists `dataset_authority` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `dataset_id` int(11) NOT NULL,
  `user_name` varchar(16) NOT NULL DEFAULT '' COMMENT '用户名集合',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据集权限表';

CREATE TABLE If Not Exists `dataset_base` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `dataset_name` varchar(32) NOT NULL DEFAULT '' COMMENT '数据集名称',
  `dataset_desc` varchar(256) NOT NULL DEFAULT '' COMMENT '数据集描述',
  `is_accelerate` int(11) NOT NULL DEFAULT '0' COMMENT '是否加速',
  `from_source` varchar(16) NOT NULL DEFAULT '' COMMENT '来源(MODEL_EXPLORE-模型探索 MANUAL-手动)',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '是否有效(0-失效 1-启用)',
  `gpl_content` text COMMENT 'gpl主语句',
  `model` text COMMENT '维度指标',
  `create_user` varchar(32) NOT NULL DEFAULT '' COMMENT '创建者',
  `update_user` varchar(32) NOT NULL DEFAULT '' COMMENT '最后一次更新',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `category_id` int(11) DEFAULT NULL COMMENT '分类id',
  `category_name` varchar(64) DEFAULT NULL COMMENT '分类名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据集基础表';

CREATE TABLE If Not Exists `dataset_category` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) DEFAULT NULL COMMENT '分类名称',
  `level` int(11) DEFAULT NULL COMMENT '分类级别',
  `operator` varchar(32) DEFAULT NULL COMMENT '操作人员',
  `parent_id` int(11) DEFAULT NULL COMMENT '父级id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据集分类';

CREATE TABLE If Not Exists `dataset_field` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `dataset_id` int(11) NOT NULL COMMENT '数据集id',
  `field_name` varchar(64) NOT NULL DEFAULT '' COMMENT '字段名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE If Not Exists `dataset_gpl_filter_condition` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `dataset_id` int(11) NOT NULL,
  `dbquery_content` text COMMENT 'dbquery内容',
  `dbquery_fields` text COMMENT '模型探索跳转过来，保存所有字段',
  `gpl_condition` text COMMENT 'gpl对应的过滤条件',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据集GPL过滤条件表';

CREATE TABLE If Not Exists `dataset_operation_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `dataset_id` int(11) NOT NULL,
  `operation_type` varchar(16) NOT NULL DEFAULT '' COMMENT '操作类型',
  `operation_detail` varchar(128) NOT NULL DEFAULT '' COMMENT '操作详情',
  `operator` varchar(32) NOT NULL DEFAULT '' COMMENT '操作人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据集操作日志';

CREATE TABLE If Not Exists `dataset_table_filter_condition` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `dataset_id` int(11) NOT NULL COMMENT '数据集id',
  `table_name` varchar(32) NOT NULL DEFAULT '' COMMENT ' 表名',
  `connector` varchar(16) NOT NULL DEFAULT '' COMMENT '连接符(and or)',
  `where_sql` varchar(1024) NOT NULL DEFAULT '' COMMENT '高级模式下的sql',
  `selected_field` text COMMENT '选中的字段',
  `filter_condition` text,
  `selected_field_type` text COMMENT '选中的字段类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据集数据库过滤条件表';


CREATE TABLE If Not Exists `ai_report` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT,
  `report_name` varchar(255) DEFAULT NULL COMMENT '报表名称',
  `report_time` datetime DEFAULT NULL COMMENT '报表时间',
  `execute_content` text COMMENT '执行内容',
  `execute_content_type` varchar(10) DEFAULT NULL COMMENT '执行内容类型:gpl,sql',
  `execute_type` varchar(10) DEFAULT NULL COMMENT '执行类型:day-每天；week-每周；month-每月；year-每年；cron-cron表达式',
  `execute_config` varchar(50) DEFAULT NULL COMMENT '执行配置',
  `execute_cron` varchar(512) DEFAULT NULL,
  `current_execute_time` datetime DEFAULT NULL COMMENT '当前执行时间',
  `next_execute_time` datetime DEFAULT NULL COMMENT '下次执行时间',
  `send_type` varchar(20) DEFAULT NULL COMMENT '发送类型：local,mail,sftp',
  `send_config` text COMMENT '发送配置:json格式',
  `send_format` varchar(255) DEFAULT NULL COMMENT '发送格式:csv,pdf',
  `report_result` varchar(20) DEFAULT NULL COMMENT '报表执行结果:0-生成中；1-生成成功；2-生成失败；3-发送成功；4-发送失败',
  `status` varchar(255) DEFAULT NULL COMMENT '报表当前状态1-开启；0-关闭',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(255) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(255) DEFAULT NULL COMMENT '更新人',
  `flag` int(1) DEFAULT NULL COMMENT '有效标识:1-有效；0-无效',
  `planTime` varchar(255) DEFAULT NULL COMMENT '任务计划时间',
  `second` int(10) DEFAULT NULL COMMENT '秒',
  `minute` int(10) DEFAULT NULL COMMENT '分',
  `hour` int(10) DEFAULT NULL COMMENT '时',
  `dayOfweek` int(10) DEFAULT NULL COMMENT '一周第几天',
  `dayOfMonth` int(10) DEFAULT NULL COMMENT '一个月第几天',
  `monthOfYear` int(10) DEFAULT NULL COMMENT '一年第几个月',
  `split_char` varchar(20) DEFAULT NULL COMMENT '分割符',
  `dashboard_drag_node` text COMMENT 'dashboard节点',
  `drag_selected` text COMMENT 'dashboard选中节点',
  `widget_list` text COMMENT 'dashboard详细配置',
  `dashboard_url` varchar(4096) DEFAULT NULL COMMENT 'dashboard拼接的url',
  `description` varchar(1024) DEFAULT NULL COMMENT '描述',
  `has_header` varchar(1) DEFAULT NULL COMMENT '是否需要表头:1-是，0-否，默认1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_report_name` (`report_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE If Not Exists `ai_report_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `report_id` int(11) DEFAULT NULL COMMENT '报表id',
  `config` text COMMENT '配置项',
  `is_delete` int(2) DEFAULT NULL COMMENT '是否删除，1：正常，2：删除',
  `is_cycle` int(2) DEFAULT NULL COMMENT '是否时定时任务，1：是，2：否',
  `cycle` varchar(50) DEFAULT NULL COMMENT '定时周期表达式',
  `export_type` varchar(50) DEFAULT NULL COMMENT '导出类型,多个逗号分隔，word、pdf、html',
  `create_id` int(4) DEFAULT NULL COMMENT '创建人',
  `modify_id` int(4) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报表配置';

CREATE TABLE If Not Exists `ai_report_execute` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `report_id` bigint(30) DEFAULT NULL COMMENT '报表id',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_local_path` varchar(255) DEFAULT NULL COMMENT '文件本地保存地址',
  `execute_time` datetime DEFAULT NULL COMMENT '执行时间',
  `execute_result` varchar(10) DEFAULT NULL COMMENT '执行结果',
  `execute_result_desc` varchar(2000) DEFAULT NULL COMMENT '执行结果描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(255) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(255) DEFAULT NULL COMMENT '更新人',
  `flag` varchar(1) DEFAULT NULL COMMENT '有效标识',
  `source_file_name` varchar(200) DEFAULT NULL COMMENT '原始文件名称',
  `exception_file` varchar(256) DEFAULT NULL COMMENT '异常文件路径',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE If Not Exists `ai_report_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(50) DEFAULT NULL COMMENT '名称',
  `parent_id` int(2) DEFAULT NULL COMMENT '父节点id',
  `project_id` int(11) DEFAULT NULL COMMENT '项目',
  `report_type` int(2) DEFAULT NULL COMMENT '报表类型，1：目录，2：报表',
  `is_delete` int(2) DEFAULT NULL COMMENT '是否删除,删除的为回收站;1:正常,2:删除',
  `create_id` int(4) DEFAULT NULL COMMENT '创建人',
  `modify_id` int(4) DEFAULT NULL COMMENT '更新人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_time` datetime DEFAULT NULL COMMENT '更新时间',
  `sortno` int(4) DEFAULT NULL COMMENT '序号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报表报告组';

CREATE TABLE If Not Exists `ai_report_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `report_id` int(11) DEFAULT NULL COMMENT '报表id',
  `is_auto` int(11) DEFAULT NULL COMMENT '标识自动/手动生成，1：自动，2：手动',
  `status` int(2) DEFAULT NULL COMMENT '状态，1：进行中，2：已完成，3：异常',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `path` varchar(255) DEFAULT NULL COMMENT '服务器文件路径',
  `export_type` varchar(20) DEFAULT NULL COMMENT '导出类型，word、pdf、html',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报表配置';

CREATE TABLE If Not Exists `ai_report_send` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `report_id` bigint(30) DEFAULT NULL COMMENT '报表id',
  `execute_id` bigint(30) DEFAULT NULL COMMENT '执行记录id',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_size` bigint(30) DEFAULT NULL COMMENT '文件大小',
  `send_type` varchar(255) DEFAULT NULL COMMENT '输出类型：local,mail,sftp,ftp',
  `send_time` datetime DEFAULT NULL COMMENT '发送时间',
  `send_result` varchar(255) DEFAULT NULL COMMENT '发送结果：1-成功；2-失败',
  `send_result_desc` varchar(500) DEFAULT NULL COMMENT '发送结果描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(255) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(255) DEFAULT NULL COMMENT '更新人',
  `flag` varchar(1) DEFAULT NULL COMMENT '有效标识：1-有效，0-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE If Not Exists `ai_user_report` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `report_id` bigint(30) DEFAULT NULL COMMENT '报表id',
  `user_id` bigint(30) DEFAULT NULL COMMENT '用户id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE If Not Exists `etl_logmoudle` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据源配置ID',
  `work_id` varchar(64) NOT NULL COMMENT 'work的唯一标识',
  `work_ip` varchar(64) NOT NULL COMMENT 'work部署的地址',
  `work_moudle` varchar(16) NOT NULL COMMENT '类型single, client, relay, gate, parser',
  `work_desc` varchar(512) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_parser
-- ----------------------------
CREATE TABLE If Not Exists `etl_parser` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '解析规则ID',
  `template_id` bigint(20) NOT NULL COMMENT '规则模板ID',
  `source_id` bigint(20) NOT NULL COMMENT '数据源ID',
  `new_create` int(11) NOT NULL COMMENT '0-使用已有规则  1-新建规则',
  `keep_raw_data` int(11) NOT NULL DEFAULT '0' COMMENT '是否记录原始日志（0表示不记录，1表示记录）',
  `disable_record_errdata` int(11) NOT NULL DEFAULT '0' COMMENT '是否记录解析失败日志标识(0表示未解析，1表示已解析)',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_parser_function
-- ----------------------------
CREATE TABLE If Not Exists `etl_parser_function` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '解析规则ID',
  `function_name` varchar(128) NOT NULL COMMENT '解析器名称',
  `function_code` varchar(64) NOT NULL COMMENT '解析器编码',
  `desccription` varchar(256) DEFAULT NULL COMMENT '解析器描述',
  `short_name` varchar(64) DEFAULT NULL COMMENT '缩写名称',
  `icon` varchar(64) DEFAULT NULL COMMENT '图标路径',
  `status` int(11) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`),
  UNIQUE KEY `function_code` (`function_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_parser_param
-- ----------------------------
CREATE TABLE If Not Exists `etl_parser_param` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `paraser_reg_id` bigint(20) NOT NULL COMMENT '解析规则ID',
  `source_id` bigint(20) DEFAULT NULL COMMENT '数据源ID',
  `col_index` varchar(512) DEFAULT NULL COMMENT '列',
  `name` varchar(512) DEFAULT NULL COMMENT '变量标识',
  `sample_value` longtext COMMENT '示例值',
  `format` varchar(32) DEFAULT NULL COMMENT '数据格式',
  `type` varchar(16) DEFAULT NULL COMMENT '数据类型',
  `status` int(11) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_parser_param_tmp
-- ----------------------------
CREATE TABLE If Not Exists `etl_parser_param_tmp` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `paraser_reg_id` varchar(128) DEFAULT NULL COMMENT '解析规则ID',
  `source_id` bigint(20) DEFAULT NULL COMMENT '数据源ID',
  `col_index` varchar(512) DEFAULT NULL COMMENT '列',
  `name` varchar(512) DEFAULT NULL COMMENT '变量标识',
  `sample_value` longtext COMMENT '示例值',
  `format` varchar(32) DEFAULT NULL COMMENT '数据格式',
  `type` varchar(16) DEFAULT NULL COMMENT '数据类型',
  `status` int(11) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_parser_reg
-- ----------------------------
CREATE TABLE If Not Exists `etl_parser_reg` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '解析规则ID',
  `name` varchar(128) DEFAULT NULL COMMENT '解析规则名称',
  `template_id` bigint(20) DEFAULT NULL COMMENT '规则所属模板ID',
  `uuid` varchar(128) DEFAULT NULL,
  `parser_type` varchar(64) NOT NULL COMMENT '解析器编码',
  `function` varchar(64) DEFAULT NULL COMMENT '解析器名称',
  `sort_id` int(11) DEFAULT NULL COMMENT '正则匹配优先级',
  `split1` varchar(10) DEFAULT NULL COMMENT '分隔符1',
  `split2` varchar(10) DEFAULT NULL COMMENT '分隔符2',
  `reg_type` varchar(10) DEFAULT NULL COMMENT '长正则，短正则',
  `parent_uuid` varchar(128) DEFAULT NULL COMMENT '父节点正则',
  `parent_column` varchar(64) DEFAULT NULL COMMENT '父节点列名',
  `action_type` varchar(8) DEFAULT NULL COMMENT '匹配类型',
  `reg_value` text COMMENT '正则表达式',
  `sample_log` longtext COMMENT '样例数据',
  `status` int(11) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `underline_word` text COMMENT '划词信息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_parser_reg_tmp
-- ----------------------------
CREATE TABLE If Not Exists `etl_parser_reg_tmp` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '解析规则ID',
  `tmp_id` varchar(128) DEFAULT NULL COMMENT '解析规则临时ID',
  `name` varchar(128) DEFAULT NULL COMMENT '解析规则名称',
  `template_id` bigint(20) DEFAULT NULL COMMENT '规则所属模板ID',
  `uuid` varchar(128) DEFAULT NULL,
  `parser_type` varchar(64) NOT NULL COMMENT '解析器编码',
  `function` varchar(64) DEFAULT NULL COMMENT '解析器名称',
  `sort_id` int(11) DEFAULT NULL COMMENT '正则匹配优先级',
  `split1` varchar(10) DEFAULT NULL COMMENT '分隔符1',
  `split2` varchar(10) DEFAULT NULL COMMENT '分隔符2',
  `reg_type` varchar(10) DEFAULT NULL COMMENT '长正则，短正则',
  `parent_uuid` varchar(128) DEFAULT NULL COMMENT '父节点正则',
  `parent_column` varchar(64) DEFAULT NULL COMMENT '父节点列名',
  `action_type` varchar(8) DEFAULT NULL COMMENT '匹配类型',
  `reg_value` text COMMENT '正则表达式',
  `sample_log` longtext COMMENT '样例数据',
  `underline_word` text COMMENT '划词信息',
  `status` int(11) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_parser_result
-- ----------------------------
CREATE TABLE If Not Exists `etl_parser_result` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `template_id` bigint(20) NOT NULL COMMENT '解析模板ID',
  `reg_id` bigint(20) NOT NULL COMMENT '解析规则ID',
  `column_name` varchar(255) DEFAULT NULL COMMENT '解析出的列名',
  `column_value` varchar(2048) DEFAULT NULL,
  `show_order` int(11) DEFAULT NULL COMMENT '显示顺序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_parser_rule_category
-- ----------------------------
CREATE TABLE If Not Exists `etl_parser_rule_category` (
  `id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `asset_type` varchar(10) DEFAULT NULL COMMENT '资产类型',
  `asset_code` varchar(255) DEFAULT NULL COMMENT '资产编码',
  `asset_label` varchar(255) DEFAULT NULL COMMENT '资产标签',
  `show_order` int(11) DEFAULT NULL COMMENT '显示顺序',
  `parent_code` varchar(255) DEFAULT NULL COMMENT '父级编码',
  `asset_desc` varchar(255) DEFAULT NULL COMMENT '父级编码',
  `create_user` varchar(256) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` varchar(256) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_parser_template
-- ----------------------------
CREATE TABLE If Not Exists `etl_parser_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '规则模板ID',
  `name` varchar(128) DEFAULT NULL COMMENT '规则模板名称',
  `status` int(11) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `category` varchar(128) DEFAULT NULL COMMENT '模板类别',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父ID',
  `template_desc` varchar(512) DEFAULT NULL COMMENT '规则模板描述',
  `manage` int(11) DEFAULT '1' COMMENT '规则模板是否被管理（0 表示不可以管理，1表示可以）',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  `url` varchar(255) DEFAULT NULL COMMENT '规则文件保存类路径',
  `rule_from` varchar(255) DEFAULT NULL COMMENT '规则来源  system-系统自定义 selfDefine-自定义',
  `copy_cnt` int(11) DEFAULT NULL COMMENT '复制次数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_reader_param
-- ----------------------------
CREATE TABLE If Not Exists `etl_reader_param` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据源配置ID',
  `source_id` bigint(20) NOT NULL COMMENT '数据源ID',
  `config_key` varchar(128) NOT NULL COMMENT '数据源配置项key',
  `config_value` varchar(8192) NOT NULL COMMENT '数据源配置项值',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_reader_param_define
-- ----------------------------
CREATE TABLE If Not Exists `etl_reader_param_define` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据源配置项ID',
  `source_type` varchar(32) NOT NULL COMMENT '数据源类型',
  `cn_config_key` varchar(128) NOT NULL COMMENT '配置中文名',
  `config_key` varchar(128) NOT NULL COMMENT '配置',
  `config_type` varchar(64) NOT NULL COMMENT '配置类型(1-字符 2-数字 3-枚举 4-附件)',
  `config_desc` varchar(128) DEFAULT NULL COMMENT '配置说明',
  `sort` int(11) NOT NULL COMMENT '显示顺序',
  `config_defalut` varchar(64) DEFAULT NULL COMMENT '默认值',
  `is_required` varchar(3) NOT NULL DEFAULT '1' COMMENT '是否必填(0-非必填 1-必填)',
  `config_level` varchar(3) NOT NULL DEFAULT '1' COMMENT '参数等级(1-基础配置 2-高级配置)',
  `status` int(11) NOT NULL COMMENT '状态（0 表示禁用，1表示启用）',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_shell
-- ----------------------------
CREATE TABLE If Not Exists `etl_shell` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `shell_name` varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
  `shell_desc` varchar(64) NOT NULL DEFAULT '' COMMENT '描述',
  `shell_content` text NOT NULL COMMENT '脚本内容',
  `create_user` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_shell_opt_log
-- ----------------------------
CREATE TABLE If Not Exists `etl_shell_opt_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `shell_id` int(11) NOT NULL COMMENT '脚本id',
  `opt_account` varchar(32) NOT NULL DEFAULT '' COMMENT '操作人员',
  `opt_type` varchar(32) NOT NULL DEFAULT '' COMMENT '操作类型',
  `opt_object` varchar(32) NOT NULL DEFAULT '' COMMENT '操作对象',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_source
-- ----------------------------
CREATE TABLE If Not Exists `etl_source` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据源ID',
  `source_type` varchar(32) NOT NULL COMMENT '数据源类型',
  `source_name` varchar(128) NOT NULL COMMENT '数据源名称',
  `logmodule_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'logmoduleID(多个逗号分隔)',
  `source_desc` varchar(512) DEFAULT NULL COMMENT '数据源描述',
  `status` int(11) NOT NULL COMMENT '状态（0 表示禁用，1表示启用）',
  `source_switch` int(11) DEFAULT NULL COMMENT '开关（0-关 1-开）',
  `access_status` int(11) DEFAULT NULL COMMENT '接入状态 0-未生效  1-已生效',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  `keep_raw_data` int(11) NOT NULL DEFAULT '1' COMMENT '是否记录原始日志（0表示不记录，1表示记录）',
  `keep_parser_error_data` int(11) DEFAULT NULL COMMENT '是否保存解析失败日志（0表示不记录，1表示记录）',
  `copy_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '复制次数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_source_optlog
-- ----------------------------
CREATE TABLE If Not Exists `etl_source_optlog` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `source_id` bigint(20) NOT NULL COMMENT '数据源id',
  `opt_time` datetime DEFAULT NULL COMMENT '操作时间',
  `opt_user` varchar(32) DEFAULT NULL COMMENT '操作人',
  `opt_type` int(11) DEFAULT NULL COMMENT '1-修改配置 2-开启配置 3-关闭配置 4-删除配置 5-查看配置',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_source_set
-- ----------------------------
CREATE TABLE If Not Exists `etl_source_set` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据源配置ID',
  `source_id` bigint(20) NOT NULL COMMENT '数据源ID',
  `line_tag_type` varchar(32) DEFAULT NULL COMMENT '换行方式',
  `line_tag` varchar(512) DEFAULT NULL COMMENT '内容',
  `time_tag_type` varchar(32) DEFAULT NULL COMMENT '时间戳类型',
  `time_tag` varchar(512) DEFAULT NULL COMMENT '内容',
  `charset` varchar(64) DEFAULT NULL COMMENT '字符编码',
  `time_format` varchar(64) DEFAULT NULL COMMENT '时间格式',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_source_type
-- ----------------------------
CREATE TABLE If Not Exists `etl_source_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据源类型编号',
  `source_type` varchar(32) NOT NULL COMMENT '类型编码',
  `name` varchar(32) NOT NULL COMMENT '类型名称',
  `source_type_desc` varchar(128) DEFAULT NULL COMMENT '描述',
  `category` varchar(32) NOT NULL COMMENT '类型分类',
  `sort` int(11) NOT NULL COMMENT '类型排序 前台根据该顺序展示',
  `category_sort` int(11) NOT NULL COMMENT '分类排序 前台根据该顺序展示',
  `icon` varchar(64) DEFAULT NULL COMMENT '对应的icon图片路径',
  `status` int(11) NOT NULL COMMENT '状态（0 表示禁用，1表示启用）',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_transform_action
-- ----------------------------
CREATE TABLE If Not Exists `etl_transform_action` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `rule_id` bigint(20) NOT NULL COMMENT '转换规则ID',
  `dest_field` text,
  `dest_field_type` varchar(16) DEFAULT NULL COMMENT '目标列类型',
  `dest_value` varchar(1024) DEFAULT NULL COMMENT '目标列的值',
  `dest_table` varchar(4096) DEFAULT NULL COMMENT '目标表',
  `dest_column` text COMMENT '目标表的列',
  `format_from` varchar(4096) DEFAULT NULL COMMENT '时间转换-源格式',
  `format_to` varchar(4096) DEFAULT NULL COMMENT '时间转换-目标格式',
  `start_index` int(11) DEFAULT NULL COMMENT '字段截取或字段模糊的起始位置',
  `end_index` int(11) DEFAULT NULL COMMENT '字段截取或字段模糊的结束位置',
  `combineChar` varchar(1024) DEFAULT NULL COMMENT '字段合并连接符',
  `longitude` varchar(64) DEFAULT NULL COMMENT '经度',
  `latitude` varchar(64) DEFAULT NULL COMMENT '纬度',
  `country` varchar(64) DEFAULT NULL COMMENT '国家',
  `province` varchar(64) DEFAULT NULL COMMENT '省份',
  `city` varchar(64) DEFAULT NULL COMMENT '城市',
  `isp` varchar(64) DEFAULT NULL COMMENT '运营商',
  `datamask_strategy` varchar(16) DEFAULT NULL COMMENT '数据脱敏策略',
  `datamask_reg` varchar(128) DEFAULT NULL COMMENT '数据脱敏策略正则',
  `script_type` varchar(32) DEFAULT '' COMMENT '脚本类型',
  `script_content` text COMMENT '脚本内容',
  `split` varchar(64) DEFAULT NULL COMMENT '字段分隔符',
  `kv_split` varchar(64) DEFAULT NULL COMMENT 'KV分隔符',
  `sample_data` text COMMENT '样例数据',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_transform_condition
-- ----------------------------
CREATE TABLE If Not Exists `etl_transform_condition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `rule_id` bigint(20) NOT NULL COMMENT '转换规则ID',
  `node_id` bigint(20) DEFAULT NULL COMMENT '当前节点ID(备用字段)',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父ID，如果为第一个条件则父ID为-1(备用字段)',
  `node_type` varchar(16) DEFAULT NULL COMMENT '此条记录下子记录之间操作逻辑，值为and、or、not(备用字段)',
  `condition_name` varchar(128) DEFAULT NULL COMMENT '条件类型，比如事件名称、事件严重级别等(备用字段)',
  `src_field` varchar(128) DEFAULT NULL COMMENT '源列',
  `logic` varchar(16) DEFAULT NULL COMMENT '条件逻辑，值为>、<、>=、<=、!=、=、like',
  `compare_table` varchar(128) DEFAULT NULL COMMENT '待比较的表',
  `compare_content` varchar(256) DEFAULT NULL COMMENT '待比较的表字段或值',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_transform_function
-- ----------------------------
CREATE TABLE If Not Exists `etl_transform_function` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `code` varchar(64) NOT NULL COMMENT '转换器编码',
  `trans_func` bigint(20) DEFAULT NULL COMMENT '转换器类别',
  `description` varchar(128) DEFAULT NULL COMMENT '转换器描述',
  `short_name` varchar(64) NOT NULL COMMENT '转换器简称',
  `status` int(11) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  `show_order` int(11) DEFAULT NULL COMMENT '规则排列顺序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_transform_rule
-- ----------------------------
CREATE TABLE If Not Exists `etl_transform_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `source_id` bigint(20) NOT NULL COMMENT '数据源ID',
  `rule_name` varchar(64) DEFAULT NULL COMMENT '转换规则名称',
  `rule_type` varchar(64) NOT NULL COMMENT '转换类型',
  `rule_func` varchar(64) DEFAULT NULL COMMENT '转换处理函数',
  `expression` varchar(512) DEFAULT NULL,
  `rule_desc` varchar(128) DEFAULT NULL COMMENT '规则描述',
  `rule_order` int(11) NOT NULL COMMENT '规则顺序',
  `status` int(11) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  `enable_advanced` char(1) DEFAULT NULL COMMENT '是否启用高级配置  0-未启用  1-启用',
  `parent_id` varchar(64) DEFAULT NULL COMMENT '转换规则父节点',
  `parent_type` varchar(64) DEFAULT NULL COMMENT '转换规则父节点类型',
  `preview` int(11) DEFAULT '0' COMMENT '是否预览数据(0-否 1-是)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_transform_table
-- ----------------------------
CREATE TABLE If Not Exists `etl_transform_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `table_name` varchar(128) NOT NULL COMMENT '表名',
  `cn_table_name` varchar(128) NOT NULL COMMENT '表中文名',
  `table_desc` varchar(128) DEFAULT NULL COMMENT '表描述',
  `field_name` varchar(64) NOT NULL COMMENT '列名',
  `field_type` varchar(16) DEFAULT NULL COMMENT '列类型',
  `status` int(11) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(256) DEFAULT NULL COMMENT '创建者',
  `update_user` varchar(256) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_wide_table_column
-- ----------------------------
CREATE TABLE If Not Exists `etl_wide_table_column` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `table_name` varchar(128) DEFAULT NULL COMMENT '表名',
  `column_name` varchar(128) DEFAULT NULL COMMENT '列名',
  `data_type` varchar(32) DEFAULT NULL COMMENT '数据类型',
  `data_desc` varchar(512) DEFAULT NULL COMMENT '字段描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_wide_table_column_view
-- ----------------------------
CREATE TABLE If Not Exists `etl_wide_table_column_view` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `view_id` bigint(20) DEFAULT NULL COMMENT '视图id',
  `column_name` varchar(64) DEFAULT NULL COMMENT '字段名称',
  `is_required` int(1) DEFAULT NULL COMMENT '是否必填：1-必填；0-非必填',
  `remark` varchar(1024) DEFAULT NULL COMMENT '视图下备注字段',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_wide_table_view
-- ----------------------------
CREATE TABLE If Not Exists `etl_wide_table_view` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `view_name` varchar(128) DEFAULT NULL COMMENT '视图名称',
  `model_view` varchar(128) DEFAULT NULL COMMENT 'model_view',
  `view_desc` varchar(512) DEFAULT NULL COMMENT '视图描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_writer_ch_config
-- ----------------------------
CREATE TABLE If Not Exists `etl_writer_ch_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `source_id` bigint(20) NOT NULL COMMENT '数据源ID',
  `reg_value` text COMMENT '正则表达式',
  `time_format` varchar(16) DEFAULT NULL COMMENT '时间格式',
  `table_name` varchar(128) DEFAULT NULL COMMENT '表名',
  `order_fields` varchar(512) DEFAULT NULL COMMENT '排序字段',
  `cluster_display_name` varchar(32) DEFAULT NULL COMMENT '集群名称',
  `shard_num` int(11) DEFAULT NULL COMMENT '分片数',
  `replica_num` int(11) DEFAULT NULL COMMENT '副本数',
  `max_capacity` int(11) DEFAULT NULL COMMENT '最大容量',
  `max_keep_days` int(11) DEFAULT NULL COMMENT '最大保留天数',
  `strategy` varchar(16) DEFAULT NULL COMMENT '映射对应策略',
  `last_backup_index_name` varchar(128) DEFAULT NULL COMMENT '最后一次备份到的索引名称',
  `failed_index_name` varchar(128) DEFAULT NULL COMMENT '归档失败的索引名称',
  `cycle_scop` int(11) DEFAULT NULL COMMENT '缩容周期',
  `status` int(11) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `view_id` varchar(256) DEFAULT NULL COMMENT '视图id',
  `model_view` varchar(256) DEFAULT NULL COMMENT 'model_view',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_writer_config
-- ----------------------------
CREATE TABLE If Not Exists `etl_writer_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `source_id` bigint(20) NOT NULL COMMENT '数据源ID',
  `write_type` varchar(8) DEFAULT NULL COMMENT '输出类型ES CH等',
  `config_type` varchar(16) DEFAULT NULL COMMENT '配置类型（0表示default，1表示自定义）',
  `enable_flag` int(11) DEFAULT NULL COMMENT '是否启用配置 0-未启用 1-已启用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_writer_es_config
-- ----------------------------
CREATE TABLE If Not Exists `etl_writer_es_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `source_id` bigint(20) NOT NULL COMMENT '数据源ID',
  `reg_value` text COMMENT '正则表达式',
  `time_format` varchar(16) DEFAULT NULL COMMENT '时间格式',
  `index_name` varchar(128) DEFAULT NULL COMMENT '索引名',
  `cluster_display_name` varchar(32) DEFAULT NULL COMMENT '集群名称',
  `shard_num` int(11) DEFAULT NULL COMMENT '分片数',
  `replica_num` int(11) DEFAULT NULL COMMENT '副本数',
  `max_capacity` int(11) DEFAULT NULL COMMENT '最大容量',
  `max_keep_days` int(11) DEFAULT NULL COMMENT '最大保留天数',
  `strategy` varchar(16) DEFAULT NULL COMMENT '映射对应策略',
  `last_backup_index_name` varchar(128) DEFAULT NULL COMMENT '最后一次备份到的索引名称',
  `failed_index_name` varchar(128) DEFAULT NULL COMMENT '归档失败的索引名称',
  `cycle_scop` int(11) DEFAULT NULL COMMENT '缩容周期',
  `status` int(11) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_writer_kafka_config
-- ----------------------------
CREATE TABLE If Not Exists `etl_writer_kafka_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `source_id` int(11) NOT NULL COMMENT '数据源id',
  `topic` varchar(512) NOT NULL DEFAULT '' COMMENT 'kafka的topic',
  `origin_to_topic` varchar(512) DEFAULT NULL COMMENT '输出原始文本消息到 kafka',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_writer_table_config
-- ----------------------------
CREATE TABLE If Not Exists `etl_writer_table_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `source_id` bigint(20) NOT NULL COMMENT '数据源ID',
  `write_type` varchar(16) DEFAULT NULL COMMENT '入库类型',
  `column_name` varchar(128) DEFAULT NULL COMMENT '列名',
  `data_type` varchar(32) DEFAULT NULL COMMENT '数据类型',
  `data_name` varchar(128) DEFAULT NULL COMMENT '来源列明',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for etl_writer_table_define
-- ----------------------------
CREATE TABLE If Not Exists `etl_writer_table_define` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `writer_type` varchar(8) NOT NULL COMMENT '输出类型 CH-实时数仓 ES-索引库',
  `table_name` varchar(128) DEFAULT NULL COMMENT '表名',
  `column_name` varchar(48) DEFAULT NULL COMMENT '列名',
  `data_type` varchar(32) DEFAULT NULL COMMENT '数据类型',
  `data_name` varchar(48) DEFAULT NULL COMMENT '来源数据名',
  `data_desc` varchar(512) DEFAULT NULL COMMENT '字段描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for explore_query_condition
-- ----------------------------
CREATE TABLE If Not Exists `explore_query_condition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `create_user` varchar(100) DEFAULT NULL COMMENT '用户',
  `condition_name` varchar(32) NOT NULL COMMENT '查询模板名称',
  `condition_desc` varchar(128) DEFAULT NULL COMMENT '查询模板描述',
  `write_type` varchar(16) NOT NULL COMMENT '查询类别  CH-实时数仓  ES-索引库',
  `table_name` varchar(128) NOT NULL COMMENT '表名或索引名',
  `query_condition` text COMMENT '查询条件',
  `time_type` varchar(2) DEFAULT NULL COMMENT '1- 快捷时间选择 2-相对时间 3-时间范围',
  `quick_time` varchar(16) DEFAULT NULL COMMENT '快捷时间编码',
  `relative_days` int(11) DEFAULT NULL COMMENT '相对时间值',
  `relative_type` varchar(16) DEFAULT NULL COMMENT '相对时间类型',
  `relative_ontime` varchar(2) DEFAULT NULL COMMENT '相对时间整点类型 0-当前 1-整点',
  `now_ontime` varchar(2) DEFAULT NULL COMMENT '当前时间整点类型 0-当前 1-整点',
  `start_time` varchar(32) DEFAULT NULL COMMENT '开始时间',
  `end_time` varchar(32) DEFAULT NULL COMMENT '结束时间',
  `range_type` varchar(2) DEFAULT NULL COMMENT '1-介于 2-之前 3-之后',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for explore_query_history
-- ----------------------------
CREATE TABLE If Not Exists `explore_query_history` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `table_name` varchar(32) NOT NULL DEFAULT '' COMMENT '表名称',
  `quick_time` varchar(32) NOT NULL DEFAULT '' COMMENT '时间范围',
  `query_condition` text COMMENT '查询条件',
  `write_type` varchar(32) CHARACTER SET latin1 NOT NULL COMMENT '查询类型(CH,ES)',
  `start_time` varchar(32) NOT NULL DEFAULT '' COMMENT '开始时间',
  `end_time` varchar(32) NOT NULL DEFAULT '' COMMENT '结束时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `query_hash` varchar(128) DEFAULT NULL COMMENT '查询条件hash值',
  `history_type` varchar(32) DEFAULT NULL COMMENT 'explore,gpl  分别表示模型探索，gpl',
  `create_user` varchar(32) DEFAULT 'sysadmin' COMMENT '查询人员',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='搜索历史表';

-- ----------------------------
-- Table structure for explore_selected_field
-- ----------------------------
CREATE TABLE If Not Exists `explore_selected_field` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `create_user` varchar(100) DEFAULT NULL COMMENT '用户',
  `write_type` varchar(16) NOT NULL COMMENT '查询类别  CH-实时数仓  ES-索引库',
  `selected_fields` text NOT NULL COMMENT '已选字段 多个字段用逗号分隔',
  `table_name` varchar(128) NOT NULL COMMENT '表名或索引名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE If Not Exists `system_clickhouse_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `cluster_name` varchar(32) NOT NULL DEFAULT '' COMMENT 'CH的集群名称',
  `cluster_username` varchar(32) NOT NULL DEFAULT '' COMMENT 'ch账号',
  `cluster_password` varchar(64) NOT NULL DEFAULT '' COMMENT 'ch密码',
  `ip` varchar(32) NOT NULL DEFAULT '' COMMENT 'ip地址',
  `username` varchar(32) NOT NULL DEFAULT '' COMMENT '用户名',
  `password` varchar(64) NOT NULL DEFAULT '' COMMENT '密码',
  `su_username` varchar(32) NOT NULL DEFAULT '' COMMENT '管理员用户名',
  `su_password` varchar(64) NOT NULL DEFAULT '' COMMENT '管理员密码',
  `network_card` varchar(32) NOT NULL DEFAULT '' COMMENT '网卡名称',
  `recover_tmp_dir` varchar(100) DEFAULT NULL COMMENT 'CH备份恢复临时目录',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;








