delete  from ums_sys_menus where parent_name = 'manage-api';
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'API首页', 'data-api-home', '', '0', '0', 'manage-api', '1', 0, 0, '数据共享概览', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'test5', '2024-05-10 20:32:23', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'API市场', 'market-api', '', '0', '0', 'manage-api', '1', 1, 1, 'API市场', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);

INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '我的API', 'my-api', '', '0', '0', 'manage-api', '1', 3, 3, '我的申请', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'API配置', 'manage-data-api', '', '0', '0', 'manage-api', '1', 4, 4, '我的发布', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '共享脱敏结果', 'desensitization-api-detail', '', '0', '1', 'manage-api', '1', 5, 5, '共享脱敏结果', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);

INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'API审核', 'api-examine', '', '0', '0', 'manage-api', '1', 7, 7, 'API审核', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '申请审核', 'check-api', '', '0', '0', 'api-examine', '1', 8, 8, '申请审核', '1', 'api-examine', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '发布审核', 'api-publish-audit', '', '0', '0', 'api-examine', '1', 9, 9, '发布审核', '1', 'api-examine', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'API授权', 'client-manage', '', '0', '0', 'manage-api', '1', 10, 10, 'API授权', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);

INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据源管理', 'source-manage', '', '0', '0', 'manage-api', '1', 12, 12, '数据源管理', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '水印溯源', 'data-share-traceability', '', '0', '0', 'manage-api', '1', 13, 13, '水印溯源', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '项目管理', 'project-manage', '', '0', '0', 'manage-api', '1', 14, 14, '项目管理', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '安全组', 'security-group', '', '0', '0', 'manage-api', '1', 15, 15, '安全组', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '水印规则', 'data-share-watermark', '', '0', '0', 'manage-api', '1', 16, 16, '水印规则', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);

INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '生成API', 'manage-data-api-create', '', '0', '0', 'manage-api', '1', 17, 17, '生成API', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '生成API-模式选择', 'manage-data-new-api', '', '0', '0', 'manage-api', '1', 18, 18, '生成API-模式选择', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '注册API', 'manage-data-api-register', '', '0', '0', 'manage-api', '1', 19, 19, '注册API', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', null, 'data', '0', '0', 0);
