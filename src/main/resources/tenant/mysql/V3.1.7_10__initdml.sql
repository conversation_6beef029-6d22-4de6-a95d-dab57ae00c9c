INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '基础信息', 'baseInfo', '', '0', '0', 'data-input-config', '1', 90, 90, '基础信息', '1', 'data-input-config', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'insight', NULL, 'insight', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '自然人信息管理', 'person', NULL, '0', '0', 'baseInfo', '1', 10, 10, '自然人信息管理', '1', 'baseInfo', NULL, NULL, NULL, NULL, 'insight', NULL, 'insight', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '账号信息管理', 'account', NULL, '0', '0', 'baseInfo', '1', 20, 20, '账号信息管理', '1', 'baseInfo', NULL, NULL, NULL, NULL, 'insight', NULL, 'insight', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '设备信息管理', 'base-device', NULL, '0', '0', 'baseInfo', '1', 30, 30, '设备信息管理', '1', 'baseInfo', NULL, NULL, NULL, NULL, 'insight', NULL, 'insight', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '网络域信息管理', 'network-device', NULL, '0', '0', 'baseInfo', '1', 40, 40, '网络域信息管理', '1', 'baseInfo', NULL, NULL, NULL, NULL, 'insight', NULL, 'insight', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '应用信息管理', 'application', NULL, '0', '0', 'baseInfo', '1', 50, 50, '应用信息管理', '1', 'baseInfo', NULL, NULL, NULL, NULL, 'insight', NULL, 'insight', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '数据信息管理', 'base-dataInfo', NULL, '0', '0', 'baseInfo', '1', 60, 60, '数据信息管理', '1', 'baseInfo', NULL, NULL, NULL, NULL, 'insight', NULL, 'insight', '0');


CREATE TABLE IF NOT EXISTS asset_standby_field_config (
  id int(11) unsigned NOT NULL AUTO_INCREMENT,
  config_table_name varchar(64) NOT NULL DEFAULT '' COMMENT '配置表名',
  standby_field_name varchar(32) NOT NULL DEFAULT '' COMMENT '备用字段名称',
  standby_field_type varchar(32) CHARACTER SET latin1 NOT NULL COMMENT '备用字段类型',
  standby_field_show_name varchar(32) NOT NULL DEFAULT '' COMMENT '备用字段展示名称',
  enable_status int(11) DEFAULT NULL COMMENT '启用状态',
  status int(11) DEFAULT NULL COMMENT '删除状态',
  create_user varchar(16) DEFAULT '' COMMENT '创建者',
  update_user varchar(16) DEFAULT '' COMMENT '更新者',
  create_time datetime DEFAULT NULL COMMENT '创建时间',
  update_time datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;