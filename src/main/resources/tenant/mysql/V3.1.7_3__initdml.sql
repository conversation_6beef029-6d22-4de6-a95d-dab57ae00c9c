INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '水印规则', 'data-share-watermark', '', '0', '0', 'manage-api', '1', 120, 120, '水印规则', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', NULL, 'data', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '水印溯源', 'data-share-traceability', '', '0', '0', 'manage-api', '1', 130, 130, '水印溯源', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', NULL, 'data', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '脱敏规则', 'desensitization-rules', '', '0', '0', 'manage-api', '1', 140, 140, '脱敏规则', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', NULL, 'data', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES('builtIn', '1', 3, '共享脱敏结果', 'desensitization-api-detail', '', '0', '1', 'manage-api', '1', 150, 150, '共享脱敏结果', '1', 'manage-api', 'sysadmin', '2022-03-21 16:01:15', 'sysadmin', '2022-03-21 16:01:15', 'data', NULL, 'data', '0');

INSERT INTO ums_sys_role_function (role_id, function_id, value, create_user, create_date, update_user, update_date) VALUES( '140d6cf17d244e74855ecdd56c40335c', 'data-share-traceability', NULL, NULL, NULL, NULL, NULL);
INSERT INTO ums_sys_role_function (role_id, function_id, value, create_user, create_date, update_user, update_date) VALUES( '140d6cf17d244e74855ecdd56c40335c', 'data-share-watermark', NULL, NULL, NULL, NULL, NULL);
INSERT INTO ums_sys_role_function (role_id, function_id, value, create_user, create_date, update_user, update_date) VALUES( '140d6cf17d244e74855ecdd56c40335c', 'desensitization-rules', NULL, NULL, NULL, NULL, NULL);
INSERT INTO ums_sys_role_function (role_id, function_id, value, create_user, create_date, update_user, update_date) VALUES( '140d6cf17d244e74855ecdd56c40335c', 'desensitization-api-detail', NULL, NULL, NULL, NULL, NULL);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1)
VALUES ('builtIn', '1', 2, '数据加密管理', 'data-lake-governance-encryption-manage', null, '0', '0',
        'data-lake-governance', '1', 70, 70, '数据加密管理', '1', 'data-lake-governance', null, null, null, null,
        'data-lake-governance', null, 'data-lake-governance', '0'),
       ('builtIn', '1', 3, '加密任务', 'data-lake-governance-encryption-task', null, '0', '0',
        'data-lake-governance-encryption-manage', '1', 10, 10, '加密任务', '1',
        'data-lake-governance-encryption-manage', null, null, null, null, 'data-lake-governance', null,
        'data-lake-governance', '0'),
       ('builtIn', '1', 3, '加密详情', 'encryption-task-detail', null, '0', '1',
        'data-lake-governance-encryption-manage', '1', 20, 20, '加密详情', '1',
        'data-lake-governance-encryption-manage', null, null, null, null, 'data-lake-governance', null,
        'data-lake-governance', '0'),
       ('builtIn', '1', 3, '秘钥管理', 'data-lake-governance-private-key-manage', null, '0', '0',
        'data-lake-governance-encryption-manage', '1', 30, 30, '秘钥管理', '1',
        'data-lake-governance-encryption-manage', null, null, null, null, 'data-lake-governance', null,
        'data-lake-governance', '0'),
       ('builtIn', '1', 3, '秘钥备份管理', 'data-lake-governance-private-key-backup-manage', null, '0', '0',
        'data-lake-governance-encryption-manage', '1', 40, 40, '秘钥备份管理', '1',
        'data-lake-governance-encryption-manage', null, null, null, null, 'data-lake-governance', null,
        'data-lake-governance', '0');