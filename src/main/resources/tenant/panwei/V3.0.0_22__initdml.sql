delete from ums_sys_menus where menu_code in ('seatunnel','seatunnel-task-manage','seatunnel-datasource-manage','seatunnel-virtual-tables','seatunnel-user-manage');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,parent_name, status, menu_order, default_order, default_name, default_status, default_parent,create_user, create_date, update_user, update_date, root_parent, application_code,application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '数据集成', 'seatunnel', null, '0', '0', 'data-development-inner', '1', 90, 90,'数据集成', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null,'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,parent_name, status, menu_order, default_order, default_name, default_status, default_parent,create_user, create_date, update_user, update_date, root_parent, application_code,application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '任务管理', 'seatunnel-task-manage', null, '0', '0', 'seatunnel', '1', 1, 1,'任务管理', '1', 'seatunnel', null, null, null, null, 'data-development-inner', null,'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,parent_name, status, menu_order, default_order, default_name, default_status, default_parent,create_user, create_date, update_user, update_date, root_parent, application_code,application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据源', 'seatunnel-datasource-manage', null, '0', '0', 'seatunnel', '1', 2, 2,'数据源', '1', 'seatunnel', null, null, null, null, 'data-development-inner', null,'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,parent_name, status, menu_order, default_order, default_name, default_status, default_parent,create_user, create_date, update_user, update_date, root_parent, application_code,application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '虚拟列表', 'seatunnel-virtual-tables', null, '0', '0', 'seatunnel', '1', 3, 3,'虚拟列表', '1', 'seatunnel', null, null, null, null, 'data-development-inner', null,'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,parent_name, status, menu_order, default_order, default_name, default_status, default_parent,create_user, create_date, update_user, update_date, root_parent, application_code,application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '用户管理', 'seatunnel-user-manage', null, '0', '0', 'seatunnel', '1', 4, 4,'用户管理', '1', 'seatunnel', null, null, null, null, 'data-development-inner', null,'data-development-inner', '0', '0', 0);