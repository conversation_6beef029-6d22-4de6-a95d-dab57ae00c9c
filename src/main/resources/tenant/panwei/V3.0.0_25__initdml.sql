CREATE TABLE if not exists etl_single_task
(
    id BIGSERIAL PRIMARY KEY,
    single_id   VARCHAR(40) NULL DEFAULT NULL ,
    task_id     BIGINT      NULL DEFAULT NULL ,
    create_time TIMESTAMP   NULL DEFAULT NULL
);

CREATE TABLE if not exists etl_single
(
    id BIGSERIAL PRIMARY KEY,
    single_id     VARCHAR(40) NOT NULL ,
    single_ip     VARCHAR(40) NOT NULL ,
    single_port   VARCHAR(5)  NULL DEFAULT NULL ,
    single_status SMALLINT    NULL DEFAULT NULL ,
    create_time   TIMESTAMP   NULL DEFAULT NULL,
    update_time   TIMESTAMP   NULL DEFAULT NULL
);

COMMENT ON COLUMN etl_single_task.single_id IS '节点id';
COMMENT ON COLUMN etl_single_task.task_id IS '任务id';
COMMENT ON COLUMN etl_single.single_id IS '任务id';
COMMENT ON COLUMN etl_single.single_id IS '节点id';
COMMENT ON COLUMN etl_single.single_ip IS '节点ip';
COMMENT ON COLUMN etl_single.single_port IS '节点端口';
COMMENT ON COLUMN etl_single.single_status IS '节点状态';