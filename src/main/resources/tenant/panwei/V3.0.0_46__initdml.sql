delete from ums_sys_menus where menu_code in( 'unified-data-source-management','certification-management','data-source-management','data-source-select','data-lake-governance-meta-search-panwei','data-lake-governance-meta-search-kafka');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 2, '统一数据源管理', 'unified-data-source-management', null, '0', '0', 'base', '1', 50, 50, '数据资产管理',
        '1', 'base', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, '认证管理', 'certification-management', null, '0', '0', 'unified-data-source-management', '1', 10, 10,
        '数据资产管理', '1', 'unified-data-source-management', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, '数据源管理', 'data-source-management', null, '0', '0', 'unified-data-source-management', '1', 10, 10,
        '数据资产管理', '1', 'unified-data-source-management', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,
                           parent_name, status, menu_order, default_order, default_name, default_status, default_parent,
                           create_user, create_date, update_user, update_date, root_parent, application_code,
                           application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 4, '数据源选择', 'data-source-select', null, '0', '0', 'data-source-management', '1', 10, 10,
        '数据资产管理', '1', 'data-source-management', null, null, null, null, 'base', null, 'base', '0', '0', 0);

INSERT INTO ums_sys_menus ( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path,
                            manage_free, hidden, parent_name, status, menu_order, default_order, default_name,
                            default_status, default_parent, create_user, create_date, update_user, update_date,
                            root_parent, application_code, application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'PANWEI', 'data-lake-governance-meta-search-panwei', null, '0', '0', 'data-lake-governance-meta-search', '1', 60, 60,
        'PANWEI', '1', 'data-lake-governance-meta-search', null, null, null, null, 'data-lake-governance', null,
        'data-lake-governance', '0', '0', 0);

INSERT INTO ums_sys_menus ( menu_type, menu_property, menu_level, menu_name, menu_code, menu_path,
                            manage_free, hidden, parent_name, status, menu_order, default_order, default_name,
                            default_status, default_parent, create_user, create_date, update_user, update_date,
                            root_parent, application_code, application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'Kafka', 'data-lake-governance-meta-search-kafka', null, '0', '0', 'data-lake-governance-meta-search', '1', 70, 70,
        'Kafka', '1', 'data-lake-governance-meta-search', null, null, null, null, 'data-lake-governance', null,
        'data-lake-governance', '0', '0', 0);
