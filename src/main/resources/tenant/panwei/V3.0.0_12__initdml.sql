CREATE TABLE IF NOT EXISTS etl_config
(
	id SERIAL  PRIMARY KEY,
	read_conf TEXT NOT NULL ,
	structured_conf TEXT,
	expansion_conf TEXT,
	load_conf TEXT,
	example_json TEXT,
	enabled SMALLINT DEFAULT 1,
	create_user VARCHAR(32) DEFAULT '',
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	name VARCHAR(100),
	data_source VARCHAR(100),
	source_type VARCHAR(100),
	status INTEGER NOT NULL ,
	update_user VARCHAR(100),
	copy_cnt INTEGER DEFAULT 0
);

COMMENT ON COLUMN etl_config.read_conf IS '读源配置';
COMMENT ON COLUMN etl_config.structured_conf IS '结构化配置，sql采集忽略';
COMMENT ON COLUMN etl_config.expansion_conf IS '扩展化配置';
COMMENT ON COLUMN etl_config.load_conf IS '写入配置';
COMMENT ON COLUMN etl_config.example_json IS '原始数据';
COMMENT ON COLUMN etl_config.enabled IS '是否启用';
COMMENT ON COLUMN etl_config.create_user IS '创建人';
COMMENT ON COLUMN etl_config.create_time IS '创建时间';
COMMENT ON COLUMN etl_config.update_time IS '更新时间';
COMMENT ON COLUMN etl_config.data_source IS '数据来源';
COMMENT ON COLUMN etl_config.source_type IS '接入方式';
COMMENT ON COLUMN etl_config.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_config.update_user IS '更新者';
COMMENT ON COLUMN etl_config.copy_cnt IS '复制次数';