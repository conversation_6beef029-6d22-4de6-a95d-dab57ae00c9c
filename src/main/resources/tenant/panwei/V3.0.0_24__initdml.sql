INSERT INTO etl_dictionary_set (id, dict_name, dict_desc, is_default, dict_category, copy_cnt, delete_flag, create_time, create_user, update_time, update_user) VALUES (1, '态势字典集', '态势字典集', '1', '1', 0, '0', now(), 'sysadmin', now(), 'sysadmin');

INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'database_id', 'String', '数据库ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_name', 'String', '目的设备名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'eqpt_business_system', 'String', '数据源IP地址业务系统', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_serial', 'String', 'TLS证书的序列号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'alarm_times', 'String', '告警次数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'user_group_id', 'String', '用户组ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'data_category', 'String', '数据分类', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'ioc_type', 'String', 'IOC类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_version', 'String', '版本', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_f', 'String', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_request_size', 'Int64', '请求包的大小', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'rule_alarm_times', 'String', '规则告警次数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'logout_time', 'DateTime64', '登出时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'total_match_num', 'String', '总匹配数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'database_table_field', 'String', '数据库字段名', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_datasource_type', 'String', '数据源类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'process_p_name', 'String', '父进程名', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_value', 'String', '目的设备价值', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_aa', 'String', '权威解析服务器标志', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_rrname', 'String', '请求对象名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'response_code', 'String', '返回码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_accept_language', 'String', '接受的语言', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'is_logout', 'String', '是否登出', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'resource_name', 'String', '资源名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_g', 'String', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_account_uuid', 'String', '源账号唯一标识（hash值）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_area_name', 'String', '源自然人所在地理位置', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_cookie', 'String', '浏览器缓存', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_option55', 'String', '选项55字符串序列', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_account', 'String', '源账号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'data_uuid_status', 'String', '数据唯一标识补全状态（找不到则为“未知”，找到则为“已知”）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_jacs_hash', 'String', 'JA3S的HASH值', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'total', 'Int64', '总数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_rcode', 'String', '返回码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'message_id', 'String', '消息ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'time_cost', 'String', '耗时', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_opt_type', 'String', '操作类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_ttl', 'String', '存活时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'model_view', 'String', '模型标签', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'sensitive_file_type_id', 'String', '敏感文件类型ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_notbefore', 'String', 'TLS证书中的NotBefore字段', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_certificate', 'String', 'TLS证书base64编码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_sent_content_bytes', 'Int64', '上行有效负载字节数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_create_time', 'DateTime64', '文件创建时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'authorization', 'String', '授权书,HTTP授权的授权证书', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_dept', 'String', '目的设备所属部门名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'authority_name', 'String', '响应域名,一般为要查询的域名，有时也会是 IP 地址，用于反向查询。dns流量中的值是name，日志为domain', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_request_native_lm', 'String', 'SMB1 native Lan Manager string', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_ip_city', 'String', '源设备IP所属城市', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_business_code', 'String', '被调用端业务系统编码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_accept', 'String', '接受的类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_received_package_flow', 'Int64', '已接收包数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_account_type', 'String', '源账号账号类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'identification', 'String', '内外网标识', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'result_action_id', 'String', '操作行为结果ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'rule_policy', 'String', '策略规则', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_ftp_reply', 'String', '命令应答，可能包含多行，采用数组格式', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_sent_content_packets', 'Int64', '上行有效负载包个数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_app_dept', 'String', '目的所属部门名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'parser_total', 'Int16', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'authority_address', 'String', '响应地址,表示按查询段要求返回的相关资源记录的数据。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_modified', 'String', '自unix启动以来的时间戳（秒）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_rpc_auth_type', 'String', '验证类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_response_native_os', 'String', 'SMB1 native OS string', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_collect_time', 'DateTime64', '日志采集时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_api_uuid_data', 'String', 'API明文唯一标识', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_status_code', 'String', '响应状态 (200,404 等)', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_d', 'String', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'login_type', 'String', '登录类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_out_param', 'String', '输出参数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_httpagent', 'String', 'User-Agent', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_event_id', 'String', '原始日志ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_hash', 'String', '文件HASH值', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_rsp_end_time', 'DateTime64', '响应结束时间 相对1970年的时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_accessed', 'String', '自unix启动以来的时间戳（秒）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'answer_name', 'String', '请求域名,一般为要查询的域名，有时也会是 IP 地址，用于反向查询。dns流量中的值是name，日志为domain', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_routers', 'String', '路由', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_url_externalurl_domain', 'String', '访问URL的域名', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_rsp_header_length', 'String', '响应头长度', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_app_uuid_status', 'String', '目的应用唯一标识补全状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_flags', 'String', '标记', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'status', 'String', '状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'token', 'String', 'token', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_uuid_status', 'String', '源自然人唯一标识补全状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_url_httpreferrer', 'String', '来源URL', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'log_txt_type', 'String', '记录的文本类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_qr', 'String', '包标志(请求和回复)', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'victim_mac', 'String', '受害者mac', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'idss_stage_chain', 'String', '攻击链', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_client_ip', 'String', '客户端IP地址', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'email_message_id', 'String', '信体messageid', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_trans_protocol', 'String', '传输层协议', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'attack_mac', 'String', '攻击者Mac', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_expired_date', 'String', '过期时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'app_protocol', 'String', '应用协议', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_rd', 'String', '报文截断标志', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_rpc_creds_machine_name', 'String', '主机名', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_nfs_procedure', 'String', '远程调用', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_received_content_bytes', 'Int64', '下行有效负载字节数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_raw_log', 'String', '原始日志正文', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_nat_src_ip', 'String', 'NAT后源IP', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_created', 'String', '自unix启动以来的时间戳（秒）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'eqpt_owner', 'String', '数据源IP地址资产责任人', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'process_cmdline', 'String', '执行命令', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_from_info', 'String', '调用发起方信息', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'interface_call_time', 'DateTime64', '接口调用日期时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'is_download_attachment', 'String', '是否下载文件', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_collect_ip', 'String', '采集器IP', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_dns_servers', 'String', 'DNS服务器列表', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_response_native_lm', 'String', 'SMB1 native Lan Manager string', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'login_time', 'DateTime64', '登陆时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_business_name', 'String', '被调用端业务系统名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'apply_end_time', 'DateTime64', '申请结束时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_rsp_content_position', 'String', '响应体开始位置', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_sent_app_bytes', 'Int64', '上行有效负载应用层字节数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_ssh_client_proto_version', 'String', '客户端协议版本号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_command_data', 'String', '命令数据', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_accept_encoding', 'String', '接受的字符编码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_parent_id', 'String', '父流编号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'program_name', 'String', '程序名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_nat_src_port', 'Int32', 'NAT后源端口', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_assigned_ip', 'String', '分配得IP地址', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_from_business_code', 'String', '接口调用端业务系统编码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'alarm_compression_times', 'String', '告警压缩后次数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_e', 'String', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'su_username', 'String', 'su的账户名', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'coordinate_operator', 'String', '协同操作人', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'end_time', 'DateTime64', '结束时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_nfs_status', 'String', '状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_client_dialects', 'String', '客户端所说的SMB方言列表。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_additional_info', 'String', '额外资源信息', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_types', 'String', '目的自然人类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_uuid', 'String', '源设备唯一标识(hash值)', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'peak_flow', 'String', '峰值流量', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_ip_city', 'String', '目的设备IP所属城市', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_received_app_bytes', 'Int64', '下行有效负载应用层字节数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_nfs_file_tx', 'String', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_rsp_file_path', 'String', '响应文件路径(相对路径)', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_app_protocol_type', 'String', '应用协议类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_requested_ip', 'String', '请求者IP地址', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_account_uuid_data', 'String', '源账号唯一标识（明文）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'user_id', 'String', '用户ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_ssh_client_software_version', 'String', '客户端软件版本号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'verify_model', 'String', '授权模式', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_req_content_length', 'String', '请求体长度', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'mark_file_results', 'String', '标记文件结果', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_type', 'String', '文件类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_app_owner', 'String', '目的资产责任人', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_req_content_type', 'String', '请求内容类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_uuid_status', 'String', '源设备唯一标识补全状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_url_httpreferrer_type', 'String', '来源URL的类型（外部、内部）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_time_a', 'DateTime64', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'log_type', 'String', '日志类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_risk_level', 'String', '风险级别', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_owner', 'String', '目的设备资产责任人', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tx_id', 'String', '通讯协议组(class)', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'query_elapsed_time', 'DateTime64', '查询消耗的时间(单位为微秒)', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_c', 'String', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_server_guid', 'String', 'server GUID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'event_name', 'String', '事件告警名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'apply_time', 'DateTime64', '申请时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'eqpt_asset_name', 'String', '数据源名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_app_name', 'String', '目的应用名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smtp_helo', 'String', 'SMTP helo命令', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_session_end_reason', 'String', '会话终止原因', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'object_type', 'String', '对象类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_api_uuid', 'String', 'API密文唯一标识', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_access', 'String', '指示如何打开文件。“正常”或“关闭时删除”（字段可能会更改）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_uuid_data', 'String', '目的自然人唯一标识（明文）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_org_name', 'String', '源自然人组织机构', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_confidence', 'String', '置信度', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_id', 'String', '流编号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'eqpt_asset_value', 'String', '数据源IP地址资产价值', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_b', 'String', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_ssh_server_proto_version', 'String', '服务器协议版本号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_status', 'String', '目的自然人状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_vendor', 'String', '源设备厂商名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_system_type', 'String', '文件系统类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_mail', 'String', '源自然人邮箱', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'treasury_verified_code', 'String', '金库验证过程标识', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_syn_ack', 'Int8', '是否有tcp第二次应答', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_received_byte_flow', 'Int64', '已接收字节数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_account', 'String', '目的账号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_int_b', 'Int64', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'data_value', 'String', '数据价值', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_types', 'String', '源自然人类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_status_code', 'String', '十六进制字符串状态代码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_issuerdn', 'String', 'TLS证书中的issuer字段', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_dialect', 'String', '协商的协议方言，或“未知”如果丢失', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_a', 'String', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_share_name', 'String', '共享名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'apply_context', 'String', '申请信息', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_httpprotocol', 'String', 'http协议', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_identity_card', 'String', '源自然人身份证号码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'process_name', 'String', '进程名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_fingerprint', 'String', 'TLS证书的（SHA1）指纹', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_identity_card', 'String', '目的自然人身份证号码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_fin_packets', 'Int64', 'fin标志的数据包个数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_priority', 'String', '日志严重级别', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'entity_name', 'String', '管理实体名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'data_record_status', 'String', '数据备案状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_uuid', 'String', '目的自然人唯一标识（hash值）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_rsp_content_type', 'String', '回应类型 -1未知,1html,2xml,3txt,4json,5js,6css,7image,11xls,12xlsx,13doc,14docx,21pdf,31rar,101其它', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smtp_to', 'String', '收件人地址', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'eqpt_asset_dept', 'String', '数据源P地址资产所属部门名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_flow_age', 'String', '流持续时间，单位：秒', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'summary', 'String', '日志概要', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'feature_rule_times', 'String', '特征加规则次数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_app_type', 'String', '目的应用类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_response_time', 'DateTime64', '响应时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'rule_name', 'String', '命中的规则名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'answer_type', 'String', '请求资源类型,DNS 查询请求的资源类型。通常查询类型为 A 类型，表示由域名获取对应的 IP 地址。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_ip', 'String', '源设备IP', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'mail_dst_username', 'String', '收件人', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_jacs_string', 'String', 'JA3S组成字符串', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_uuid', 'String', '日志入库编号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'mail_content', 'String', '邮件内容', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_type', 'String', '目的设备类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_mail', 'String', '目的自然人邮箱', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'mail_type', 'String', '邮件格式', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_leave_office_time', 'DateTime64', '目的自然人离职时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_ctpositionname', 'String', '目的自然人所属岗位', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'verify_context', 'String', '授权信息', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'resource_code', 'String', '资源编码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'answer_time_to_live', 'String', '请求生命周期,以秒为单位，表示资源记录的生命周期，一般用于当地址解析程序取出资源记录后决定保存及使用缓存数据的时间。它同时也可以表明该资源记录的稳定程度，稳定的信息会被分配一个很大的值。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_access_privilege', 'String', '文件访问权限', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_from_business_name', 'String', '接口调用端业务系统名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'alarm_occurrence', 'String', '攻击发生次数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'rule_match_num', 'String', '命中规则数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_cnnvd_id', 'String', 'cve 编号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_response_head', 'String', '响应头', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_password', 'String', '父流编号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_ldap_protocol_auth', 'String', '验证方式', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_cname', 'String', 'CNAME记录(别名记录)', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'data_level', 'String', '数据分级', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_modbus_vendor', 'String', '生成厂商', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'mail_src_username', 'String', '发件人', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_connection_status', 'String', '过期时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_verified_result', 'String', '调用过程认证结果', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_nat_dst_ip', 'String', 'NAT后目的IP', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_tree_id', 'String', 'Tree ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_req_content_position', 'String', '请求体开始位置', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_duration', 'String', '持续时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'resource_info', 'String', '归属资源信息', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'query_affected_rows', 'String', '查询影响的行数，统计返回多少行', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_app_uuid', 'String', '目的应用唯一标识（hash值）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_opt_content', 'String', '操作内容', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_av_type', 'String', '病毒类别', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_modbus_version', 'String', '固件版本号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_from_ip', 'String', '调用发起方的IP', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'data_dept', 'String', '数据所属部门名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_status', 'String', '源自然人状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'ueba_flow_id', 'Int32', '数据采集任务的ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'object_server', 'String', '对象服务', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_changed', 'String', '自unix启动以来的时间戳（秒）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'database_table_name', 'String', '数据库表名', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'serivice_id', 'String', '服务ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'disk_size', 'String', '磁盘大小', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'parser_count', 'Int16', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'events_num', 'String', '事件数量', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_network_domain', 'String', '源设备网络域', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'database_name', 'String', '数据库名', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'answer_address', 'String', '请求地址,表示按查询段要求返回的相关资源记录的数据。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_rrtype', 'String', '请求对象类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_ack', 'Int8', '是否有tcp第三次确认', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'flag', 'String', '标志', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_ip_country', 'String', '源设备IP所属国家', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_j', 'String', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'sensitive_level', 'String', '敏感等级', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_response_size', 'Int64', '返回包的大小', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_app_uuid_data', 'String', '目的应用唯一标识（明文）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_mian_account', 'String', '目的主账号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'sensitive_type', 'String', '敏感类别', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'system_alarm_times', 'String', '系统告警次数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_method', 'String', '请求类型 1Get,2Post,3Put,4Delete', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_urg_packets', 'Int64', '里面有紧急指针标识的数据包个数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'rule_id', 'String', '命中的规则ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'module_code', 'String', '模块编号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_accept_charset', 'String', '接受的字符集', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'additional_name', 'String', '附加域名,一般为要查询的域名，有时也会是 IP 地址，用于反向查询。dns流量中的值是name，日志为domain', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'attck_chain', 'String', 'ATT&CK阶段', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_app_value', 'String', '目的应用价值', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_leave_office_time', 'DateTime64', '源自然人离职时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_time_b', 'DateTime64', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'authority_time_to_live', 'String', '响应生命周期,以秒为单位，表示资源记录的生命周期，一般用于当地址解析程序取出资源记录后决定保存及使用缓存数据的时间。它同时也可以表明该资源记录的稳定程度，稳定的信息会被分配一个很大的值。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_ip_region', 'String', '目的设备IP内外网标识', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'authority_data_length', 'String', '响应数据长度,资源数据的长度。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'mail_size', 'String', '邮件大小', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'uuid', 'UUID', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_answers_info', 'String', '应答资源信息', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_interface_name', 'String', '被调用接口名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_a', 'String', 'Address记录', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_upload_path', 'String', '文件上传路径', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_type', 'String', '源设备类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'apply_start_time', 'DateTime64', '申请起始时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_server_os', 'String', '服务端系统信息', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_id', 'String', '文件ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_port', 'Int32', '源设备端口', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_uuid_data', 'String', '源设备唯一标识（明文）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'alarm_direction', 'String', '攻击方向', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_jac_string', 'String', 'JA3组成字符串', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_response_set_cookie', 'String', '响应set_cookie', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_notafter', 'String', 'TLS证书中的NotAfter字段', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'client_app_id', 'String', '客户端进程号，agent可取到，否则为空', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_syn_ack_packets', 'Int64', 'syn-ack标志的数据包个数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_opt_object', 'String', '操作对象', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'additional_data_length', 'String', '附加数据长度,资源数据的长度。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_vuln_info', 'String', '漏洞描述', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'response_data', 'String', '查询返回的结果集，只返回前n行', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_owner', 'String', '源设备资产责任人', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'oprate_code', 'String', '操作类型编码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_attack', 'String', '攻击名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'eqpt_ip_uuid', 'String', '数据源设备的唯一标识', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'eqpt_vendor', 'String', '数据源IP地址厂商', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'user_home_path', 'String', '用户主目录', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'database_type', 'String', '数据库类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_in_param', 'String', '输入参数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_end_time', 'DateTime64', '攻击结束时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'data_owner', 'String', '数据所属负责人', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smtp_from', 'String', '发件人地址', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_ip', 'String', '目的设备IP', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_psh_packets', 'String', 'psh标志的数据包个数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'log_txt_context', 'String', '记录的文本信息', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_icmp_payload', 'String', '负载', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'count', 'Int64', '计数次数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_vuln_type', 'String', '漏洞类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'data_name', 'String', '数据名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_relay_ip', 'String', '回应的IP地址', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'process_pid', 'String', '进程ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_account_status', 'String', '目的状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'session_id', 'String', '会话ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_rpc_creds_gid', 'String', '内部传输号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_h', 'String', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_iface', 'String', '网口名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_ip_region', 'String', '源设备IP内外网标识', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_syn_packets', 'Int64', 'syn标志的数据包个数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'password', 'String', '密码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_vendor', 'String', '目的设备厂商名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_cve_id', 'String', 'cnnvd 编号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_received_content_packets', 'Int64', '下行有效负载包个数统计', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_flags', 'String', '标记', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_status', 'String', '状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'victim_ip', 'String', '受害者IP', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_httpprotocolversion', 'String', 'http协议版本', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_uuid_status', 'String', '目的自然人唯一标识补全状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_subject', 'String', 'TLS证书的subject字段', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_ftp_mode', 'String', 'FTP连接的类型。大多数连接是“被动的”，但也可能是“主动的”', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'answer_class', 'String', '请求地址类型,地址类型，通常为互联网地址，值为 1。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_modify_time', 'DateTime64', '文件修改时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'total_flow', 'String', '总流量', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_share', 'String', '共享名', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_i', 'String', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'process_p_pid', 'String', '父进程ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'eqpt_network_domain', 'String', '数据源IP地址网络域', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'sensitive_sample', 'String', '敏感元素样例', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_account_uuid', 'String', '目标账号唯一标识（hash值）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_ip_province', 'String', '源设备IP所属省份', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_queries_info', 'String', '查询资源信息', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'additional_time_to_live', 'String', '附加生命周期,以秒为单位，表示资源记录的生命周期，一般用于当地址解析程序取出资源记录后决定保存及使用缓存数据的时间。它同时也可以表明该资源记录的稳定程度，稳定的信息会被分配一个很大的值。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_cellphone_no', 'String', '源自然人手机号码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_ftp_completion_code', 'String', '三位数的完成代码。第一个数字表示响应是好的、坏的还是不完整的。这也是数组格式，可能包含多个匹配多个应答行的完成代码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_device_ip', 'String', '数据源ip', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_x_forwarded_for', 'String', '返回内容类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'oprate_context', 'String', '操作内容', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_client_guid', 'String', 'client GUID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_ftp_dynamic_port', 'String', '当适用时，用“port”或“EPRT”命令为后续数据传输建立的动态端口', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'log_video_frame', 'String', '记录时刻对应视频帧数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_status', 'String', '调用成功与否', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'is_batch_operate', 'String', '是否批量操作', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_ra', 'String', '支持递归查询标志', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_pcap_size', 'Int64', '原始数据包大小', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_app_network_domain', 'String', '目的应用网络域', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'oprate_name', 'String', '操作类型名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_rsp_header_position', 'String', '响应头开始位置', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'database_access_time', 'DateTime64', '数据库访问时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_ip_province', 'String', '目的设备IP所属省份', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_nat_dst_port', 'Int32', 'NAT后目的端口', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_attack_type', 'String', '攻击类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_httpmethod', 'String', 'http请求方法', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_opcode', 'String', '操作码,操作码。其中，0 表示标准查询；1 表示反向查询；2 表示服务器状态请求。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_type', 'String', 'dhcp类型：request、reply', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_ack_packets', 'Int64', 'ack标志的数据包个数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'result_action', 'String', '操作行为结果', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_tc', 'String', '报文截断标志', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'treasury_scene_id', 'String', '金库场景ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_sent_package_flow', 'Int64', '已发送包数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_req_header_file', 'String', '请求头开始文件', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_rpc_status', 'String', '状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_sni', 'String', '客户端发送的服务器名称指示（SNI）域名', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'sensitive_file_type_name', 'String', '敏感文件类型名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_killchain_stage', 'String', '杀伤链阶段', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_authorization_info', 'String', '授权资源信息', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'eqpt_ip', 'String', '数据源IP地址', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'verify_condition', 'String', '授权条件', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_account_status', 'String', '源账号状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_area_name', 'String', '目的自然人所在地理位置', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_account_group', 'String', '源账号组', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_rsp_end_timestamp', 'DateTime64', '响应结束时间毫秒', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_ip_country', 'String', '目的设备IP所属国家', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_icmp_checksum', 'String', '校验值', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_share_type', 'String', 'FILE, PIPE, PRINT or unknown', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'verify_time', 'DateTime64', '授权时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_name', 'String', '目的自然人姓名', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_request_head', 'String', '请求头', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'database_sql', 'String', '数据库SQL语句', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_smb_request_native_os', 'String', 'SMB1 native OS string', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_response_datetime', 'DateTime64', '响应时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'client_app_name', 'String', '客户端应用程序名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_protocol_id', 'String', 'DNS业务编号、DHCP请求标识、内部传输号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'response_action', 'String', '响应动作', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_httpstatus', 'String', 'http返回状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_account_uuid_data', 'String', '目标账号唯一标识（明文）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'eqpt_asset_type', 'String', '数据源IP地址资产类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'mail_cc_username', 'String', '抄送人', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'ioc_value', 'String', 'IOC值', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'treasury_apply_id', 'String', '金库申请ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_name', 'String', '源设备名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_app_vendor', 'String', '目的厂商名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_interface_desc', 'String', '被调用接口名称中文描述', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_req_start_timestamp', 'DateTime64', '请求开始时间的毫秒', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'database_system_type', 'String', '数据库系统类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_cellphone_no', 'String', '目的自然人手机号码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_size', 'Int64', '文件大小', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_rpc_creds_uid', 'String', '内部传输号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_parent_path', 'String', '文件父目录路径', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_create_time', 'DateTime64(3)', '日志产生时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_rpc_xid', 'String', 'RPC内部传输号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'os_type', 'String', '操作系统类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'answer_data_length', 'String', '请求数据长度,资源数据的长度。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_dhcp_type', 'String', 'dhcp类型 request offer', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_mac', 'String', '目的设备mac地址', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_account_group', 'String', '目的账号组', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_sent_byte_flow', 'Int64', '已发送字节数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'database_access_privilege', 'String', '数据库访问权限', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_share_local_path', 'String', '共享路径', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_event_type', 'String', '事件类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_uuid', 'String', '源自然人唯一标识（hash值）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_rst_ack_packets', 'Int64', 'rst-ack数据包个数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_uuid', 'String', '目的设备唯一标识（hash值）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_session_id', 'String', '会话ID', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'data_hash', 'String', '数据哈希值', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'mail_title', 'String', '邮件标题', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_modbus_model', 'String', '固件型号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_true_client_ip', 'String', '真实客户地址', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_jac_hash', 'String', 'JA3的HASH值', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_value', 'String', '源设备价值', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'attachment_num', 'String', '邮件附件数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_port', 'Int32', '目的应用所属端口', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dns_type', 'String', '消息类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_start_time', 'DateTime64', '攻击开始时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_vuln_name', 'String', '漏洞名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_ipv6', 'String', '目的设备IPv6', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_rsp_content_length', 'String', '响应体长度楼上还有个 http_httpmethod', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_uuid_data', 'String', '目的设备唯一标识（明文）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_url_externalurl_type', 'String', '访问URL的类型（外部、内部）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_icmp_payload_len', 'String', '负载长度', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_network_domain', 'String', '目的设备网络域', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_command', 'String', '命令号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'module_name', 'String', '模块名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_ipv6', 'String', '源设备IPv6', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_ctpositionname', 'String', '源自然人所属岗位', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_state', 'String', '文件状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'payload', 'String', 'payload', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_mac', 'String', '源设备mac地址', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_name', 'String', '文件名', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_request_body', 'String', '请求内容', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_req_start_time', 'DateTime64', '请求开始时间 相对1970年的时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'start_time', 'DateTime64', '开始时间，发生时间记录了日志信息产生的时间，方便用户查看和定位系统事件。', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_device_uuid_status', 'String', '目的设备唯一标识补全状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'mail_head', 'String', '邮件头', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'operate_object', 'String', '操作对象', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'process_p_cmdline', 'String', '父进程执行命令', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'mail_bcc_username', 'String', '密送人', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'attack_ip', 'String', '攻击者IP', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'operate_command', 'String', '操作命令', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'data_uuid', 'String', '数据唯一标识（hash值）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_request_authorization', 'String', '请求验证信息', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_client_mac', 'String', '客户端MAC地址', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'generic_into_time', 'DateTime64(3)', '日志入库时间', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_app_url', 'String', '目的应用URL', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_fin_ack_packets', 'Int64', 'fin-ack数据包个数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_uuid_data', 'String', '源自然人唯一标识（明文）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_url_externalurl_parameter', 'String', '访问URL的参数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_ssh_server_software_version', 'String', '服务器软件版本号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'data_uuid_data', 'String', '数据唯一标识（明文）', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_person_name', 'String', '源自然人姓名', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_person_org_name', 'String', '目的自然人组织机构', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_client_os', 'String', '客户端系统信息', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_rst_packets', 'Int64', 'rst标志的数据包个数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_response_body', 'String', '响应内容', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'sub_status', 'String', '子状态', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_type', 'String', '流量类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'dst_account_type', 'String', '目的账号类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_psh_ack_packets', 'Int64', 'psh-ack数据包个数', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'verify_result', 'String', '授权结果', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'data_type', 'String', '数据类型', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'protocol', 'String', '网络层协议', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_ftp_reply_received', 'String', '指示响应是否与命令匹配。在一些不典型的情况下，命令可能缺少响应', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_hostname', 'String', '客户端名，request才有', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'call_interface_ip', 'String', '被调用接口的IP', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_url_externalurl', 'String', '访问URL', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_syn', 'Int8', '是否有tcp第一次握手', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'proxy_authorization', 'String', '代理授权,连接到代理的授权证书', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_tls_chain', 'String', '整个TLS证书链base64编码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'reserved_int_a', 'Int64', '', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'person_mian_account', 'String', '源主账号', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'src_device_dept', 'String', '源所属部门名称', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_subnet_mask', 'String', '子网掩码', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'http_req_header_length', 'String', '请求头长度', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'external_alarm_risk_operation', 'String', '检测到风险后执行的操作,如:已清除', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_path', 'String', '文件路径', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'sensitive_percent', 'String', '敏感元素占比', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'netflow_dhcp_params', 'String', '请求的参数列表', now(), 'sysadmin', now(), 'sysadmin');
INSERT INTO etl_dictionary_set_field(dict_id, field_name, field_type, field_desc, create_time, create_user, update_time, update_user) VALUES (1, 'file_access_time', 'DateTime64', '文件访问时间', now(), 'sysadmin', now(), 'sysadmin');

DELETE FROM ums_sys_menus WHERE menu_code = 'manage-logmodule-tag';
DELETE FROM etl_source_type WHERE source_type = 'PANWEI';
DELETE FROM ums_sys_menus WHERE menu_code ='manage-logmodule-tag';
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '采集节点标签管理', 'manage-logmodule-tag', null, '0', '0', 'data-input-config', '1', 55, 55, '采集节点标签管理', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
DELETE FROM etl_source_type WHERE source_type = 'PANWEI';
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('PANWEI', '磐维数据库', '关系型数据库', '数据库', 1, 3, null, 1, null, null, null, null);
