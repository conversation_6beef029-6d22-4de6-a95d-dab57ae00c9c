create table ums_file_upload
(
    id SERIAL  PRIMARY KEY,
    file_name   VA<PERSON>HAR(255),
    file_size   VARCHAR(20),
    status      VARCHAR(10),
    start_time  VARCHAR(256),
    end_time    VARCHAR(256),
    "desc"      VARCHAR(1024),
    create_user VARCHAR(256),
    create_time VARCHAR(256),
    update_user VARCHAR(256),
    update_time VARCHAR(256),
    module      VARCHAR(20),
    duration    VARCHAR,
    schedule    VARCHAR(10)
);
COMMENT ON COLUMN ums_file_upload.file_name IS '文件名';
COMMENT ON COLUMN ums_file_upload.file_size IS '文件大小';
COMMENT ON COLUMN ums_file_upload.status IS '状态：1-未开始 2-进行中 3-成功 4-失败';
COMMENT ON COLUMN ums_file_upload.start_time IS '开始时间';
COMMENT ON COLUMN ums_file_upload.end_time IS '结束时间';
COMMENT ON COLUMN ums_file_upload.desc IS '描述';
COMMENT ON COLUMN ums_file_upload.module IS '基础信息类型';
COMMENT ON COLUMN ums_file_upload.duration IS '持续时间';
COMMENT ON COLUMN ums_file_upload.schedule IS '进度';

ALTER TABLE ums_sys_log ALTER COLUMN user_name TYPE VARCHAR(128);
ALTER TABLE ums_sys_log ALTER COLUMN real_name TYPE VARCHAR(128);
ALTER TABLE ums_sys_log ALTER COLUMN request_path TYPE VARCHAR(128);
ALTER TABLE ums_sys_log ALTER COLUMN opt_module_code TYPE VARCHAR(64);
