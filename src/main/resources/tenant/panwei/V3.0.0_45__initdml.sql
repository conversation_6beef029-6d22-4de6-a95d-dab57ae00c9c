delete from ums_sys_application where code = 'super-admin-permission';

INSERT INTO ums_sys_application(name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('超管权限', 'super-admin-permission', null, '1', null, '超管权限', null, 'report.png', null, null, null, '100', 0);

delete from ums_sys_menus where menu_code in ( 'cluster-resource-manage', 'cluster-manage', 'clickhouse-manage', 'clickhouse-detail', 'elasticsearch-manage', 'elasticsearch-detail', 'kafka-manage', 'kafka-detail', 'mysql-manage', 'mysql-detail', 'hdfs-manage', 'hdfs-detail');

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 1, '超管权限', 'super-admin-permission', NULL, '0', '0', NULL, '1', 50, 50, '超管权限', '1', NULL, NULL, NULL, NULL, NULL, 'super-admin-permission', NULL, 'super-admin-permission', '1', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 2, '租户管理', 'tenant-manage', NULL, '0', '0', 'super-admin-permission', '1', 1, 1, '租户管理', '1', 'super-admin-permission', NULL, NULL, NULL, NULL, 'super-admin-permission', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 2, '资源管理', 'cluster-resource-manage', NULL, '0', '0', 'super-admin-permission', '1', 20, 20, '资源管理', '1', 'super-admin-permission', NULL, NULL, NULL, NULL, 'super-admin-permission', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 2, '授权管理', 'license-manage', NULL, '0', '0', 'super-admin-permission', '1', 30, 30, '授权管理', '1', 'super-admin-permission', NULL, NULL, NULL, NULL, 'super-admin-permission', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 2, '4A用户管理', '4a-user-management', NULL, '0', '0', 'super-admin-permission', '1', 40, 40, '4A用户管理', '1', 'super-admin-permission', NULL, NULL, NULL, NULL, 'super-admin-permission', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '集群管理', 'cluster-manage', NULL, '0', '0', 'cluster-resource-manage', '1', 10, 10, '集群管理', '1', 'cluster-resource-manage', NULL, NULL, NULL, NULL, 'super-admin-permission', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, '数仓管理', 'clickhouse-manage', NULL, '0', '0', 'cluster-resource-manage', '1', 10, 10, '数仓管理', '1', 'resource-manage', NULL, NULL, NULL, NULL, 'system', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, '数仓详情', 'clickhouse-detail', NULL, '0', '0', 'cluster-resource-manage', '1', 20, 20, '数仓详情', '1', 'resource-manage', NULL, NULL, NULL, NULL, 'system', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'ES管理', 'elasticsearch-manage', NULL, '0', '0', 'cluster-resource-manage', '1', 30, 30, 'ES管理', '1', 'resource-manage', NULL, NULL, NULL, NULL, 'system', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'ES详情', 'elasticsearch-detail', NULL, '0', '0', 'cluster-resource-manage', '1', 40, 40, 'ES详情', '1', 'resource-manage', NULL, NULL, NULL, NULL, 'system', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'Kafka管理', 'kafka-manage', NULL, '0', '0', 'cluster-resource-manage', '1', 50, 50, 'Kafka管理', '1', 'resource-manage', NULL, NULL, NULL, NULL, 'system', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'Kafka详情', 'kafka-detail', NULL, '0', '0', 'cluster-resource-manage', '1', 60, 60, 'Kafka详情', '1', 'resource-manage', NULL, NULL, NULL, NULL, 'system', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'MySQL管理', 'mysql-manage', NULL, '0', '0', 'cluster-resource-manage', '1', 70, 70, 'MySQL管理', '1', 'resource-manage', NULL, NULL, NULL, NULL, 'system', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'MySQL详情', 'mysql-detail', NULL, '0', '0', 'cluster-resource-manage', '1', 80, 80, 'MySQL详情', '1', 'resource-manage', NULL, NULL, NULL, NULL, 'system', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'HDFS管理', 'hdfs-manage', NULL, '0', '0', 'cluster-resource-manage', '1', 90, 90, 'HDFS管理', '1', 'resource-manage', NULL, NULL, NULL, NULL, 'system', NULL, 'super-admin-permission', '0', '0', 0);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'HDFS详情', 'hdfs-detail', NULL, '0', '0', 'cluster-resource-manage', '1', 100, 100, 'HDFS详情', '1', 'resource-manage', NULL, NULL, NULL, NULL, 'system', NULL, 'super-admin-permission', '0', '0', 0);
