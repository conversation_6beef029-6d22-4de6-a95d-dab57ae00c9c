delete from ums_sys_menus where menu_code in ('data-asset-manage','data-asset','data-asset-manage-list','online-asset','offline-asset','offline-log','archive-data','archive-log','destroy-data','destroy-log','restore-log');

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 2, '数据资产管理', 'data-asset-manage', NULL, '0', '0', 'base', '1', 50, 50, '数据资产管理', '1', 'base', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '数据资产管理', 'data-asset', NULL, '0', '0', 'data-asset-manage', '1', 10, 10, '数据资产管理', '1', 'data-asset-manage', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '数据资产管理', 'data-asset-manage-list', NULL, '0', '0', 'data-asset', '1', 10, 10, '数据资产管理', '1', 'data-asset', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '在线资产', 'online-asset', NULL, '0', '0', 'data-asset', '1', 20, 20, '在线资产', '1', 'data-asset', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '离线资产', 'offline-asset', NULL, '0', '0', 'data-asset', '1', 30, 30, '离线资产', '1', 'data-asset', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '离线日志', 'offline-log', NULL, '0', '0', 'data-asset', '1', 40, 40, '离线日志', '1', 'data-asset', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '归档数据', 'archive-data', NULL, '0', '0', 'data-asset', '1', 50, 50, '归档数据', '1', 'data-asset', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '归档日志', 'archive-log', NULL, '0', '0', 'data-asset', '1', 60, 60, '归档日志', '1', 'data-asset', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '销毁数据', 'destroy-data', NULL, '0', '0', 'data-asset', '1', 70, 70, '销毁数据', '1', 'data-asset', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '销毁日志', 'destroy-log', NULL, '0', '0', 'data-asset', '1', 80, 10, '销毁日志', '1', 'data-asset', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES('builtIn', '1', 3, '恢复日志', 'restore-log', NULL, '0', '0', 'data-asset', '1', 90, 90, '恢复日志', '1', 'data-asset', NULL, NULL, NULL, NULL, 'base', NULL, 'base', '0', '0', 0);