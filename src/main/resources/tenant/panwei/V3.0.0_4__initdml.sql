CREATE TABLE if not exists xty_eqpt_log_report_ip (
    id SERIAL PRIMARY KEY,
    branch_code VARCHAR(10) NOT NULL,
    vendor_name VARCHAR(20) NOT NULL,
    eqpt_type VARCHAR(20) NOT NULL,
    eqpt_ip VARCHAR(64) NOT NULL,
    collect_type VARCHAR(32),
    business_dept VARCHAR(50),
    UNIQUE (branch_code, vendor_name, eqpt_type, eqpt_ip)
);
COMMENT ON TABLE xty_eqpt_log_report_ip IS '原始日志漏报分析（设备信息记录表）';
COMMENT ON COLUMN xty_eqpt_log_report_ip.branch_code IS '资源池';
COMMENT ON COLUMN xty_eqpt_log_report_ip.vendor_name IS '厂商';
COMMENT ON COLUMN xty_eqpt_log_report_ip.eqpt_type IS '设备类型';
COMMENT ON COLUMN xty_eqpt_log_report_ip.eqpt_ip IS '设备IP';
COMMENT ON COLUMN xty_eqpt_log_report_ip.collect_type IS '接入方式';
COMMENT ON COLUMN xty_eqpt_log_report_ip.business_dept IS '业务部门';

CREATE TABLE if not exists xty_etl_eqpt_list_aqglzx (
    id SERIAL PRIMARY KEY,
    branch_code VARCHAR(50) DEFAULT '',
    pod VARCHAR(50) DEFAULT '',
    vendor VARCHAR(50) DEFAULT '',
    eqpt_device_type VARCHAR(50) DEFAULT '',
    ip VARCHAR(100) DEFAULT '',
    is_manage_ip VARCHAR(20) DEFAULT '',
    eqpt_belong VARCHAR(50) DEFAULT ''
);
COMMENT ON COLUMN xty_etl_eqpt_list_aqglzx.branch_code IS '资源池';
COMMENT ON COLUMN xty_etl_eqpt_list_aqglzx.pod IS 'Pod';
COMMENT ON COLUMN xty_etl_eqpt_list_aqglzx.vendor IS '厂商';
COMMENT ON COLUMN xty_etl_eqpt_list_aqglzx.eqpt_device_type IS '设备类型';
COMMENT ON COLUMN xty_etl_eqpt_list_aqglzx.ip IS 'IP地址';
COMMENT ON COLUMN xty_etl_eqpt_list_aqglzx.is_manage_ip IS '管理IP标识';
COMMENT ON COLUMN xty_etl_eqpt_list_aqglzx.eqpt_belong IS '所属设备';

CREATE TABLE if not exists xty_log_report (
    id VARCHAR(255) NOT NULL PRIMARY KEY,
    time_id VARCHAR(2) DEFAULT NULL,
    vendor_name VARCHAR(100) DEFAULT NULL,
    eqpt_type VARCHAR(100) DEFAULT NULL,
    total_long BIGINT DEFAULT NULL,
    warn_toal_long BIGINT DEFAULT NULL,
    eqpt_ip VARCHAR(100) DEFAULT NULL,
    branch_code VARCHAR(100) DEFAULT NULL,
    send_time BIGINT DEFAULT NULL,
    create_time BIGINT DEFAULT NULL,
    raw_log TEXT,
    insert_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    send_count BIGINT DEFAULT NULL,
    send_success_count BIGINT DEFAULT NULL,
    receive_count BIGINT DEFAULT NULL,
    stock_count BIGINT DEFAULT NULL,
    nokafka_stock_count BIGINT DEFAULT NULL,
    miss_rate FLOAT DEFAULT NULL,
    stock_rate FLOAT DEFAULT NULL
);
CREATE INDEX xty_log_report_index ON xty_log_report (create_time, branch_code, vendor_name);
COMMENT ON TABLE xty_log_report IS '设备上报的数据清单';
COMMENT ON COLUMN xty_log_report.time_id IS '日志时间小时';
COMMENT ON COLUMN xty_log_report.vendor_name IS '厂商';
COMMENT ON COLUMN xty_log_report.eqpt_type IS '设备类型';
COMMENT ON COLUMN xty_log_report.total_long IS '总时长';
COMMENT ON COLUMN xty_log_report.warn_toal_long IS '警告总时长';
COMMENT ON COLUMN xty_log_report.eqpt_ip IS '设备IP';
COMMENT ON COLUMN xty_log_report.branch_code IS '资源池';
COMMENT ON COLUMN xty_log_report.send_time IS '发送时间';
COMMENT ON COLUMN xty_log_report.create_time IS '实际日志时间';
COMMENT ON COLUMN xty_log_report.raw_log IS '原始日志';
COMMENT ON COLUMN xty_log_report.insert_time IS '插入时间';
COMMENT ON COLUMN xty_log_report.send_count IS '警告总数';
COMMENT ON COLUMN xty_log_report.send_success_count IS '总数';
COMMENT ON COLUMN xty_log_report.receive_count IS '接入方式为Kafka的日志总量';
COMMENT ON COLUMN xty_log_report.stock_count IS '接入总量';
COMMENT ON COLUMN xty_log_report.nokafka_stock_count IS '接入方式非Kafka的日志总量';
COMMENT ON COLUMN xty_log_report.miss_rate IS '发送率';
COMMENT ON COLUMN xty_log_report.stock_rate IS '入库率';

