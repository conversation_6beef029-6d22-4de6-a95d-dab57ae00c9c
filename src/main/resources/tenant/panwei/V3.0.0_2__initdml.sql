delete from dataset_category;
INSERT INTO dataset_category (name, level, operator, parent_id, create_time, update_time) VALUES ('自定义', 1, 'sysadmin', null, '2024-06-28 00:50:05.0', '2024-06-28 00:50:05.0');

delete from etl_parser_function;
INSERT INTO etl_parser_function (function_name, function_code, desccription, short_name, icon, status, create_user, create_date, update_user, update_date) VALUES ('正则解析', 'REG', '正则解析', '正则解析', null, 1, null, null, null, null);
INSERT INTO etl_parser_function (function_name, function_code, desccription, short_name, icon, status, create_user, create_date, update_user, update_date) VALUES ('Json解析', 'JSON', 'Json解析', 'Json解析', null, 1, null, null, null, null);
INSERT INTO etl_parser_function (function_name, function_code, desccription, short_name, icon, status, create_user, create_date, update_user, update_date) VALUES ('KeyValue解析', 'KV', 'KeyValue解析', 'KeyValue解析', null, 1, null, null, null, null);
INSERT INTO etl_parser_function (function_name, function_code, desccription, short_name, icon, status, create_user, create_date, update_user, update_date) VALUES ('分隔符拆分', 'SPLIT', '分隔符拆分', '分隔符拆分', null, 1, null, null, null, null);

delete from etl_parser_rule_category;
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备', '安全设备', 1, null, '安全设备', null, '2021-11-26 00:24:09.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/流量检测', '流量检测', 14, '/安全设备', '安全设备/流量检测', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/防火墙', '防火墙', 1, '/安全设备', '安全设备/防火墙', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/网页防篡改', '网页防篡改', 15, '/安全设备', '安全设备/网页防篡改', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/安全防护网关(UTM)', '安全防护网关(UTM)', 2, '/安全设备', '安全设备/安全防护网关(UTM)', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/防病毒网关', '防病毒网关', 16, '/安全设备', '安全设备/防病毒网关', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/Web应用安全网关(WAF)', 'Web应用安全网关(WAF)', 3, '/安全设备', '安全设备/Web应用安全网关(WAF)', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/防病毒系统', '防病毒系统', 17, '/安全设备', '安全设备/防病毒系统', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/VPN设备', 'VPN设备', 4, '/安全设备', '安全设备/VPN设备', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/终端安全', '终端安全', 18, '/安全设备', '安全设备/终端安全', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/安全隔离与信息交换(网闸)', '安全隔离与信息交换(网闸)', 5, '/安全设备', '安全设备/安全隔离与信息交换(网闸)', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/DLP', 'DLP', 19, '/安全设备', '安全设备/DLP', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/APT', 'APT', 6, '/安全设备', '安全设备/APT', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/反垃圾邮件网关', '反垃圾邮件网关', 20, '/安全设备', '安全设备/反垃圾邮件网关', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/数据库审计', '数据库审计', 7, '/安全设备', '安全设备/数据库审计', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/TDA', 'TDA', 21, '/安全设备', '安全设备/TDA', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/堡垒机', '堡垒机', 8, '/安全设备', '安全设备/堡垒机', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/蜜罐', '蜜罐', 22, '/安全设备', '安全设备/蜜罐', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/日志审计', '日志审计', 9, '/安全设备', '安全设备/日志审计', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/身份认证', '身份认证', 23, '/安全设备', '安全设备/身份认证', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/入侵检测IDS', '入侵检测IDS', 10, '/安全设备', '安全设备/入侵检测IDS', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/终端接入控制', '终端接入控制', 24, '/安全设备', '安全设备/终端接入控制', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/入侵防御IPS', '入侵防御IPS', 11, '/安全设备', '安全设备/入侵防御IPS', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/漏洞扫描', '漏洞扫描', 25, '/安全设备', '安全设备/漏洞扫描', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/抗DDOS设备', '抗DDOS设备', 12, '/安全设备', '安全设备/抗DDOS设备', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/其他', '其他', 26, '/安全设备', '安全设备/其他', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/安全设备/流量控制系统', '流量控制系统', 13, '/安全设备', '安全设备/流量控制系统', null, '2021-11-26 00:24:15.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/服务器', '服务器', 2, null, '服务器', null, '2021-11-26 00:24:09.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/网络设备/网管系统', '网管系统', 5, '/网络设备', '网络设备/网管系统', null, '2021-11-26 00:24:14.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/服务器/Linux', 'Linux', 1, '/服务器', '服务器/Linux', null, '2021-11-26 00:24:13.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/网络设备/打印机', '打印机', 6, '/网络设备', '网络设备/打印机', null, '2021-11-26 00:24:14.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/服务器/Windows', 'Windows', 2, '/服务器', '服务器/Windows', null, '2021-11-26 00:24:13.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/网络设备/其他', '其他', 7, '/网络设备', '网络设备/其他', null, '2021-11-26 00:24:14.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/服务器/AIX ', 'AIX ', 3, '/服务器', '服务器/AIX ', null, '2021-11-26 00:24:13.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/存储设备', '存储设备', 4, null, '存储设备', null, '2021-11-26 00:24:09.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/服务器/HP-UX ', 'HP-UX ', 4, '/服务器', '服务器/HP-UX ', null, '2021-11-26 00:24:13.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/存储设备/EMC', 'EMC', 1, '/存储设备', '存储设备/EMC', null, '2021-11-26 00:24:12.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/服务器/Solaris', 'Solaris', 5, '/服务器', '服务器/Solaris', null, '2021-11-26 00:24:13.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/存储设备/Netapp', 'Netapp', 2, '/存储设备', '存储设备/Netapp', null, '2021-11-26 00:24:12.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/服务器/中标麒麟', '中标麒麟', 6, '/服务器', '服务器/中标麒麟', null, '2021-11-26 00:24:13.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/存储设备/HP', 'HP', 3, '/存储设备', '存储设备/HP', null, '2021-11-26 00:24:12.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/服务器/其他', '其他', 7, '/服务器', '服务器/其他', null, '2021-11-26 00:24:13.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/存储设备/IBM', 'IBM', 4, '/存储设备', '存储设备/IBM', null, '2021-11-26 00:24:12.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/网络设备', '网络设备', 3, null, '网络设备', null, '2021-11-26 00:24:09.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/存储设备/VERITA', 'VERITA', 5, '/存储设备', '存储设备/VERITA', null, '2021-11-26 00:24:12.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/网络设备/交换机', '交换机', 1, '/网络设备', '网络设备/交换机', null, '2021-11-26 00:24:14.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/存储设备/其他', '其他', 6, '/存储设备', '存储设备/其他', null, '2021-11-26 00:24:12.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/网络设备/路由器', '路由器', 2, '/网络设备', '网络设备/路由器', null, '2021-11-26 00:24:14.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/虚拟化设备', '虚拟化设备', 5, null, '虚拟化设备', null, '2021-11-26 00:24:09.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/网络设备/负载均衡', '负载均衡', 3, '/网络设备', '网络设备/负载均衡', null, '2021-11-26 00:24:14.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/虚拟化设备/KVM', 'KVM', 1, '/虚拟化设备', '虚拟化设备/KVM', null, '2021-11-26 00:24:10.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/网络设备/无线AP', '无线AP', 4, '/网络设备', '网络设备/无线AP', null, '2021-11-26 00:24:14.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/虚拟化设备/Xen', 'Xen', 2, '/虚拟化设备', '虚拟化设备/Xen', null, '2021-11-26 00:24:10.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/虚拟化设备/ESXi', 'ESXi', 3, '/虚拟化设备', '虚拟化设备/ESXi', null, '2021-11-26 00:24:10.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/虚拟化设备/Hyper-V', 'Hyper-V', 4, '/虚拟化设备', '虚拟化设备/Hyper-V', null, '2021-11-26 00:24:10.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/虚拟化设备/Xenserver', 'Xenserver', 5, '/虚拟化设备', '虚拟化设备/Xenserver', null, '2021-11-26 00:24:10.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/虚拟化设备/其他', '其他', 6, '/虚拟化设备', '虚拟化设备/其他', null, '2021-11-26 00:24:10.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/机房设备', '机房设备', 6, null, '机房设备', null, '2021-11-26 00:24:09.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/机房设备/安防', '安防', 1, '/机房设备', '机房设备/安防', null, '2021-11-26 00:24:11.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/机房设备/动力', '动力', 2, '/机房设备', '机房设备/动力', null, '2021-11-26 00:24:11.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/机房设备/环境', '环境', 3, '/机房设备', '机房设备/环境', null, '2021-11-26 00:24:11.0', null, null);
INSERT INTO etl_parser_rule_category (asset_type, asset_code, asset_label, show_order, parent_code, asset_desc, create_user, create_time, update_user, update_time) VALUES ('device', '/机房设备/其他', '其他', 4, '/机房设备', '机房设备/其他', null, '2021-11-26 00:24:11.0', null, null);

delete from etl_reader_param_define;
INSERT INTO etl_reader_param_define (source_type, cn_config_key, config_key, config_type, config_desc, sort, config_defalut, is_required, config_level, status, create_user, create_date, update_user, update_date) VALUES ('LOCAL_FILE', '上传数据', 'uploadFile', '4', null, 1, null, '1', '1', 1, null, null, null, null);
INSERT INTO etl_reader_param_define (source_type, cn_config_key, config_key, config_type, config_desc, sort, config_defalut, is_required, config_level, status, create_user, create_date, update_user, update_date) VALUES ('LOCAL_FILE', '文件路径', 'filePath', '1', null, 2, null, '0', '1', 1, null, null, null, null);

delete from etl_source_type;
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('LOCAL_FILE', '本地文件', 'log、csv、text文件', '文件上传', 1, 1, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('STREAM_FILE', '流式数据文件', 'StreamSocketServer', '文件上传', 2, 1, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('KAFKA', 'kafka', '分布式消息系统', '协议加载', 1, 2, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('SYSLOG_UDP', 'syslog_udp', '非连接的协议', '协议加载', 2, 2, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('SYSLOG_TCP', 'syslog_tcp', '传输控制协议', '协议加载', 3, 2, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('DB2', 'DB2', '关系型数据', '数据库', 1, 3, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('MYSQL', 'MySQL', '关系型数据', '数据库', 1, 3, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('ORACLE', 'Oracle', '关系型数据', '数据库', 1, 3, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('SQLSERVER', 'SQLServer', '关系型数据', '数据库', 1, 3, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('HIVE', 'Hive', '数据处理平台', '数据库', 1, 3, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('SCRIPT', 'Script', '采集脚本', '协议加载', 4, 2, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('MONGODB', 'MongoDB', '分布式文件存储数据库', '数据库', 1, 3, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('FTP_SFTP', 'FTP/SFTP', 'FTP、SFTP协议传输', '协议加载', 5, 2, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('HTTP', 'HTTP', 'HTTP协议', '协议加载', 6, 2, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('ELASTICSEARCH', 'ElasticSearch', '非关系数据库', '数据库', 1, 3, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('RABBITMQ', 'RabbitMQ', '分布式消息系统', '协议加载', 7, 2, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('DAMENG', '达梦数据库', '关系型数据库', '数据库', 1, 3, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('CLICKHOUSE', 'ClickHouse', '关系型数据库', '数据库', 1, 3, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('PULSAR', 'Pulsar', '分布式消息系统', '协议加载', 8, 2, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('IDSS_RPC', '观安RPC', '远程传输协议', '协议加载', 11, 2, null, 1, null, null, null, null);
INSERT INTO etl_source_type (source_type, name, source_type_desc, category, sort, category_sort, icon, status, create_user, create_date, update_user, update_date) VALUES ('ROCKETMQ', 'RocketMQ', '分布式消息系统', '协议加载', 11, 2, null, 1, null, null, null, null);

delete from etl_transform_function;
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('EXTRACT_PARSER', null, null, '提取/解析', 1, null, null, null, null, 100, null);
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('KeyValue', null, null, 'KeyValue解析', 1, null, null, null, null, 100, 'EXTRACT_PARSER');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('JSON', null, null, 'JSON解析', 1, null, null, null, null, 200, 'EXTRACT_PARSER');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('SHORT_REG', null, null, '正则提取', 1, null, null, null, null, 300, 'EXTRACT_PARSER');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('SPLIT', null, null, '字符串分割', 1, null, null, null, null, 400, 'EXTRACT_PARSER');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('DATA_PROCESS', null, null, '数据处理', 1, null, null, null, null, 200, null);
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('REPLACE', null, null, '数据替换', 1, null, null, null, null, 100, 'DATA_PROCESS');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('STR_REPLACE', null, null, '字符替换', 1, null, null, null, null, 200, 'DATA_PROCESS');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('CONCAT', null, null, '数据合并', 1, null, null, null, null, 300, 'DATA_PROCESS');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('SUBSTRING', null, null, '数据截取', 1, null, null, null, null, 400, 'DATA_PROCESS');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('FILTER', null, null, '数据移除', 1, null, null, null, null, 500, 'DATA_PROCESS');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('REMOVE', null, null, '字段删除', 1, null, null, null, null, 600, 'DATA_PROCESS');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('MASK', null, null, '数据脱敏', 1, null, null, null, null, 700, 'DATA_PROCESS');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('DATA_CONVERT', null, null, '数据转换', 1, null, null, null, null, 300, null);
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('DATETRANS', null, null, '时间转换', 1, null, null, null, null, 100, 'DATA_CONVERT');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('NUMBER_TO_IP', null, null, '数字转IP', 1, null, null, null, null, 200, 'DATA_CONVERT');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('BASE64_DECODE', null, null, '数据解密', 1, null, null, null, null, 300, 'DATA_CONVERT');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('URL_DECODE_ADD', null, null, '数据解码', 1, null, null, null, null, 400, 'DATA_CONVERT');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('FULL_DATA', null, null, '丰富数据', 1, null, null, null, null, 400, null);
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('STATIC', null, null, '静态补全', 1, null, null, null, null, 100, 'FULL_DATA');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('DYNAMIC', null, null, '动态补全', 1, null, null, null, null, 200, 'FULL_DATA');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('IP2REGION', null, null, 'IP解析扩展', 1, null, null, null, null, 300, 'FULL_DATA');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('SYSLOGPRI', null, null, 'SyslogPri', 1, null, null, null, null, 400, 'FULL_DATA');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('URL', null, null, 'URL解析扩展', 1, null, null, null, null, 500, 'FULL_DATA');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('SUBDOMAIN', null, null, '子域名拆分', 1, null, null, null, null, 600, 'FULL_DATA');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('SQL', null, null, 'SQL提取', 1, null, null, null, null, 700, 'FULL_DATA');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('JAVATRANSFORM', null, null, 'Java程序及脚本', 1, null, null, null, null, 800, 'FULL_DATA');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('UNIQUE', null, null, '唯一标识', 1, null, null, null, null, 500, null);
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('SRC_DEVICE_UNIQUE', null, null, '源设备唯一标识', 1, null, null, null, null, 100, 'UNIQUE');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('DST_DEVICE_UNIQUE', null, null, '目标设备唯一标识', 1, null, null, null, null, 200, 'UNIQUE');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('DST_APP_UNIQUE', null, null, '目标应用唯一标识', 1, null, null, null, null, 300, 'UNIQUE');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('API_UNIQUE', null, null, 'API唯一标识', 1, null, null, null, null, 400, 'UNIQUE');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('UEBA_ADDITIONAL', null, null, 'UEBA补全', 1, null, null, null, null, 600, null);
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('PERSON', null, null, '自然人信息补全', 1, null, null, null, null, 100, 'UEBA_ADDITIONAL');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('DEVICE', null, null, '设备信息补全', 1, null, null, null, null, 200, 'UEBA_ADDITIONAL');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('APP', null, null, '应用信息补全', 1, null, null, null, null, 300, 'UEBA_ADDITIONAL');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('DATA', null, null, '数据信息补全', 1, null, null, null, null, 400, 'UEBA_ADDITIONAL');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('IP2SEGMENT', null, null, '网络域信息补全', 1, null, null, null, null, 500, 'UEBA_ADDITIONAL');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('IPV6_STANDARD', null, null, 'IP标准化', 1, null, null, null, null, 350, 'FULL_DATA');
INSERT INTO etl_transform_function (code, trans_func, description, short_name, status, create_user, create_date, update_user, update_date, show_order, parent_function) VALUES ('IP_LABEL', null, null, 'IP标签', 1, null, null, null, null, 375, 'FULL_DATA');

delete from etl_wide_table_column;
INSERT INTO etl_wide_table_column (table_name, column_name, data_type, data_desc) VALUES (null, 'model_view', 'Nullable(String)', '用于保存跑哪些内置模型');

delete from etl_wide_table_column_view;
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (3, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (3, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (3, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (3, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (3, 'dst_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (3, 'dst_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (3, 'dst_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (3, 'dst_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (3, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'src_person_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'src_person_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'src_person_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'dst_account', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'dst_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'dst_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'dst_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'dst_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'program_name', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'process_pid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'process_p_pid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'process_cmdline', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (6, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'src_port', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'dst_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'dst_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'dst_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'dst_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'dst_app_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'dst_app_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'dst_app_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'dst_port', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_ack', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_fin_ack_packets', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_fin_packets', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_psh_ack_packets', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_psh_packets', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_received_app_bytes', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_received_content_packets', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_rst_ack_packets', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_rst_packets', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_sent_app_bytes', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_sent_content_packets', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_syn', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_syn_ack', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_syn_ack_packets', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_syn_packets', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_trans_protocol', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'netflow_dns_ttl', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (7, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'generic_event_id', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'generic_datasource_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'src_port', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'dst_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'dst_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'dst_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'dst_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'dst_port', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'program_name', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'external_alarm_attack', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'external_alarm_attack_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'external_alarm_av_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'external_alarm_risk_level', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (8, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'dst_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'dst_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'dst_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'dst_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'dst_app_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'dst_app_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'dst_app_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'dst_app_name', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'http_url_externalurl_parameter', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'netflow_dns_qr', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'netflow_dns_rrname', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'netflow_dns_rrtype', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'netflow_dns_rcode', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (5, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'dst_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'dst_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'dst_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'dst_app_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'dst_app_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'dst_app_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'dst_port', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'netflow_ack', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'netflow_ack_packets', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'netflow_psh_ack_packets', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'netflow_psh_packets', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'netflow_syn', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'netflow_syn_ack', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'netflow_syn_ack_packets', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'netflow_syn_packets', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'netflow_trans_protocol', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (13, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'eqpt_asset_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'generic_datasource_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'generic_event_type', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'generic_opt_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'generic_opt_content', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'result_action', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'src_person_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'src_person_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'src_person_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'src_person_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'src_person_types', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'src_person_org_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'src_device_mac', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'src_port', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_person_uuid', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_person_uuid_data', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_person_uuid_status', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_person_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_account', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_account_group', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_device_mac', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_app_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_app_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_app_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_app_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_port', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'dst_app_url', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'data_hash', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'netflow_answers_info', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'netflow_flags', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'netflow_rpc_auth_type', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'external_alarm_risk_level', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'data_level', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'login_time', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (2, 'session_id', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'generic_datasource_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'generic_opt_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'result_action', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'src_person_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'src_person_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'src_person_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'src_port', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'dst_account', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'dst_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'dst_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'dst_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'dst_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'dst_port', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'netflow_flags', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (1, 'session_id', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'generic_datasource_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'result_action', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'src_person_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'src_person_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'src_person_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'src_device_ip_province', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'src_device_owner', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'dst_person_cellphone_no', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'dst_account', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'dst_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'dst_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'dst_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'dst_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'dst_device_network_domain', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'dst_app_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'dst_app_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'dst_app_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'dst_app_name', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'dst_port', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'data_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'data_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'data_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'database_table_name', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'file_name', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'file_path', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'file_share_local_path', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'mail_cc_username', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'process_cmdline', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'netflow_trans_protocol', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'netflow_dhcp_hostname', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'data_level', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'session_id', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'resource_name', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'resource_code', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (11, 'oprate_context', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'generic_datasource_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'result_action', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'src_person_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'src_person_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'src_person_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'src_account_group', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'src_device_mac', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'dst_person_org_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'dst_account', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'dst_account_group', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'dst_device_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'dst_app_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'dst_app_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'dst_app_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'data_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'data_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'data_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'data_name', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'data_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'data_owner', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'database_access_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'file_access_time', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'file_create_time', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'file_modify_time', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'file_path', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'file_size', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'file_share_local_path', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'file_share_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'program_name', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'mail_content', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'http_url_externalurl', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'http_httpprotocol', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'netflow_dns_cname', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'external_alarm_risk_level', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (9, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'eqpt_asset_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'generic_datasource_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'generic_opt_type', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'src_person_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'src_person_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'src_person_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'src_account', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'src_account_group', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'src_device_mac', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'src_port', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'dst_account', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'dst_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'dst_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'dst_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'dst_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'dst_device_mac', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'dst_app_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'dst_port', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'database_id', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'database_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'database_table_name', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'database_sql', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'database_type', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'process_cmdline', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'netflow_answers_info', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'netflow_dhcp_hostname', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'external_alarm_risk_level', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'data_level', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'login_time', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'response_code', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'response_data', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'rule_alarm_times', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'rule_id', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'rule_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'rule_policy', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'rule_match_num', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'session_id', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'resource_code', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'module_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'call_verified_result', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'call_in_param', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'entity_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'operate_object', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (10, 'log_txt_context', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'eqpt_ip', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'generic_event_id', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'generic_datasource_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'generic_event_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'generic_opt_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'generic_opt_object', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'generic_opt_content', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'result_action', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'src_person_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'src_person_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'src_person_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'src_person_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'src_person_ctpositionname', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'src_person_org_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'src_person_cellphone_no', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'person_mian_account', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'dst_account', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'dst_account_type', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'dst_app_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'dst_app_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'dst_app_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'dst_app_name', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'data_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'data_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'data_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'data_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'data_value', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'http_url_externalurl', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'http_cookie', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'netflow_flags', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'netflow_received_package_flow', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'netflow_dns_rrtype', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (12, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'generic_datasource_type', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'src_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'src_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'src_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'src_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'dst_device_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'dst_device_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'dst_device_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'dst_device_ip', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'dst_app_uuid', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'dst_app_uuid_data', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'dst_app_uuid_status', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'dst_app_name', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'dst_port', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'http_url_externalurl', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'http_url_externalurl_parameter', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'http_url_httpreferrer', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'http_httpagent', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'http_httpmethod', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'http_httpprotocol', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'http_httpprotocolversion', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'http_httpstatus', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'http_cookie', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'http_response_size', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'http_response_time', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'http_rsp_content_type', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'generic_create_time', 1, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'reserved_a', 0, null);
INSERT INTO etl_wide_table_column_view (view_id, column_name, is_required, remark) VALUES (4, 'reserved_b', 0, null);

delete from etl_wide_table_view;
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('认证类场景（操作系统）', 'auth_device', '包括了主机、网络设备、安全设备等登录行为的数据');
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('认证类场景（应用系统）', 'auth_app', '包括了数据库、业务应用等登录行为的数据');
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('icmp协议', 'icmp', '');
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('WEB', 'web', 'HTTP协议日志/HTTP代理/nginx');
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('DNS', 'dns', 'DNS协议日志');
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('linux', 'linux', 'Linux系统的命令执行审计日志');
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('netflow', 'netflow', '流量探针设备');
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('蜜罐', 'honeypot', '蜜罐的告警日志');
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('dlp', 'dlp', 'DLP的告警日志');
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('数据库审计', 'database', '数据库审计设备中的SQL请求日志');
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('堡垒机', 'bastionhost_opt', '堡垒机操作日志');
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('业务系统', 'business_opt', '业务系统的操作日志');
INSERT INTO etl_wide_table_view (view_name, model_view, view_desc) VALUES ('防火墙', 'firewall', '防火墙中记录的流量数据');

delete from etl_writer_ch_config;
INSERT INTO etl_writer_ch_config (source_id, reg_value, time_format, table_name, order_fields, cluster_display_name, shard_num, replica_num, max_capacity, max_keep_days, strategy, last_backup_index_name, failed_index_name, cycle_scop, status, view_id, model_view) VALUES (0, null, null, 'security_log', 'generic_create_time', 'cluster_nx_copy', null, null, null, 90, null, null, null, null, 1, null, null);
INSERT INTO etl_writer_ch_config (source_id, reg_value, time_format, table_name, order_fields, cluster_display_name, shard_num, replica_num, max_capacity, max_keep_days, strategy, last_backup_index_name, failed_index_name, cycle_scop, status, view_id, model_view) VALUES (3, null, null, 'pgsqlsave', 'generic_create_time', 'cluster_1x_copy', null, null, null, 90, null, null, null, null, 1, null, null);

delete from system_backup_table;
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_logmoudle');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_parser');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_parser_function');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_parser_param');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_parser_param_tmp');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_parser_reg');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_parser_reg_tmp');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_parser_template');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_reader_param');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_reader_param_define');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_source');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_source_optlog');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_source_set');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_source_type');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_transform_action');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_transform_condition');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_transform_function');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_transform_rule');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_transform_table');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_writer_ch_config');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_writer_config');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_writer_es_config');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_writer_table_config');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('etl', 'etl_writer_table_define');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('base', 'ueba_dictionary');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('base', 'ums_sys_holiday');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('user', 'ums_sys_org');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('user', 'ums_sys_org_dept');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('user', 'ums_sys_password_policy');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('user', 'ums_sys_role');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('user', 'ums_sys_role_ch_es');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('user', 'ums_sys_role_data');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('user', 'ums_sys_role_extend');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('user', 'ums_sys_role_function');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('user', 'ums_sys_user');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('user', 'ums_sys_user_role');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('Dashboard', 'ai_report_config');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('Dashboard', 'ai_report_group');
INSERT INTO system_backup_table (config_type, table_name) VALUES ('Dashboard', 'ai_report_history');

delete from ueba_dictionary;
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('second', '秒前', 1, null, 1, 'RELATIVE_TIME', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('minute', '分钟前', 1, null, 2, 'RELATIVE_TIME', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('hour', '小时前', 1, null, 3, 'RELATIVE_TIME', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('day', '天前', 1, null, 4, 'RELATIVE_TIME', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('week', '周前', 1, null, 5, 'RELATIVE_TIME', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('month', '月前', 1, null, 6, 'RELATIVE_TIME', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('quarter', '季度前', 1, null, 7, 'RELATIVE_TIME', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('year', '年前', 1, null, 8, 'RELATIVE_TIME', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('UTF-8', 'UTF-8', 1, null, 1, 'CHARSET', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('GBK', 'GBK', 1, null, 2, 'CHARSET', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Unicode', 'Unicode', 1, null, 3, 'CHARSET', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('1', '修改配置', 1, null, 1, 'SOURCE_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('2', '开启配置', 1, null, 2, 'SOURCE_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('3', '关闭配置', 1, null, 3, 'SOURCE_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('4', '删除配置', 1, null, 4, 'SOURCE_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('5', '查看配置', 1, null, 4, 'SOURCE_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('CH', '实时数仓', 1, null, 1, 'WRITE_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('ES', '索引库', 1, null, 2, 'WRITE_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('String', 'String', 1, null, 1, '字段类型', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Integer', 'Integer', 1, null, 3, '字段类型', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Long', 'Long', 1, null, 4, '字段类型', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Boolean', 'Boolean', 1, null, 5, '字段类型', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Timestamp', 'Timestamp', 1, null, 6, '字段类型', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Float', 'Float', 1, null, 7, '字段类型', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Double', 'Double', 1, null, 8, '字段类型', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('gt', '>', 1, null, 1, 'OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('lt', '<', 1, null, 2, 'OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('ge', '>=', 1, null, 3, 'OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('le', '<=', 1, null, 4, 'OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('eq', '=', 1, null, 5, 'OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('ne', '!=', 1, null, 6, 'OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('regexp', 'regexp', 1, null, 7, 'OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('1', 'UTC Timestamp Millisecond', 1, null, 1, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('2', 'UTC Timestamp Second', 1, null, 2, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('3', 'yyyy-MM-dd HH:mm:ss', 1, null, 3, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('4', 'yyyy-MM-dd HH:mm:ss.SSS', 1, null, 4, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('5', 'yyyy-MM-dd''T''HH:mm:ss.SSS', 1, null, 5, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('6', 'yyyy-MM-dd''T''HH:mm:ss.SSSZ', 1, null, 6, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('7', 'yyyy-MM-dd''T''HH:mm:ss.SSSz', 1, null, 7, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('8', 'yyyy年MM月dd日 HH:mm:ss', 1, null, 8, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('9', 'yyyy-M-d', 1, null, 9, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('10', 'yyyy-M-d H:m:s', 1, null, 10, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('11', 'yyyy-MM-dd', 1, null, 11, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('12', 'MMM d, yyyy h:m:s aa', 1, null, 12, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('13', 'EEE MMM d HH:mm:ss ''CST'' yyyy', 1, null, 13, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('14', 'd/MMM/yyyy:h:m:s Z', 1, null, 14, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('15', 'yyyy/M/d H:m', 1, null, 15, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('16', 'd/MMM/yyyy:H:m:s Z', 1, null, 16, 'DATEFORMAT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('shardNum', '5', 1, null, 1, 'ES_DEFAULT_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('replicaNum', '1', 1, null, 2, 'ES_DEFAULT_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('maxCapacity', '100', 1, null, 3, 'ES_DEFAULT_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('maxKeepDays', '90', 1, null, 4, 'ES_DEFAULT_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('strategy', 'day', 1, null, 5, 'ES_DEFAULT_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('cycleScope', '1', 1, null, 6, 'ES_DEFAULT_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('DAYS', 'DAYS', 1, null, 1, 'TIMEUNIT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('HOURS', 'HOURS', 1, null, 2, 'TIMEUNIT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('MINUTES', 'MINUTES', 1, null, 3, 'TIMEUNIT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('maxKeepDays', '90', 1, null, 1, 'CH_DEFAULT_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('String', 'String', 1, null, 1, 'CH_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Int8', 'Int8', 1, null, 2, 'CH_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Int32', 'Int32', 1, null, 3, 'CH_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Int64', 'Int64', 1, null, 4, 'CH_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('DateTime64', 'DateTime64', 1, null, 5, 'CH_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Float32', 'Float32', 1, null, 6, 'CH_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Float64', 'Float64', 1, null, 7, 'CH_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('keyword', 'keyword', 1, null, 1, 'ES_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('text', 'text', 1, null, 2, 'ES_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('long', 'long', 1, null, 3, 'ES_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('double', 'double', 1, null, 4, 'ES_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('date', 'date', 1, null, 5, 'ES_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('boolean', 'boolean', 1, null, 6, 'ES_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('float', 'float', 1, null, 7, 'ES_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('client', '采集器', 1, null, 1, 'LOGMOUDLE_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('gate', '网关', 1, null, 2, 'LOGMOUDLE_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('parser', '解析器', 1, null, 3, 'LOGMOUDLE_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('longitude', '经度', 1, null, 5, 'IPREGION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('latitude', '纬度', 1, null, 6, 'IPREGION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('country', '国家', 1, null, 1, 'IPREGION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('province', '省份', 1, null, 2, 'IPREGION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('city', '城市', 1, null, 3, 'IPREGION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('isp', '运营商', 1, null, 4, 'IPREGION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('MMM d H:m:s', '[a-zA-Z]+\s\d+\s\d{1,2}:\d{1,2}:\d{1,2}', 1, null, 3, 'SOURCE_SET_TIME_REG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('yyyy-MM-dd HH:mm:ss', '\d{4}-\d{1,2}-\d{1,2}\s\d{1,2}:\d{1,2}:\d{1,2}', 1, null, 2, 'SOURCE_SET_TIME_REG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('yyyy-MM-dd HH:mm:ss.SSS', '\d{4}-\d{1,2}-\d{1,2}\s\d{1,2}:\d{1,2}:\d{1,2}\.\d{1,3}', 1, null, 1, 'SOURCE_SET_TIME_REG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('name', '姓名', 1, null, 1, 'MASK_STRATEGY', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('companyName', '企业单位名称', 1, null, 2, 'MASK_STRATEGY', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('IDCardNo', '身份证号码', 1, null, 3, 'MASK_STRATEGY', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('orgCode', '组织机构代码', 1, null, 4, 'MASK_STRATEGY', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('commercialNo', '工商注册号', 1, null, 5, 'MASK_STRATEGY', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('taxpayerID', '纳税人识别号', 1, null, 6, 'MASK_STRATEGY', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('phoneNo', '电话号码', 1, null, 7, 'MASK_STRATEGY', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('email', '电子邮件', 1, null, 8, 'MASK_STRATEGY', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('cardNo', '银行卡号', 1, null, 9, 'MASK_STRATEGY', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('selfDefine', '自定义', 1, null, 10, 'MASK_STRATEGY', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('1', '已知', 1, null, 1, 'INSIGHT_USER_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('0', '未知', 1, null, 2, 'INSIGHT_USER_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('在职', '在职', 1, null, 1, 'INSIGHT_JOB_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('离职', '离职', 1, null, 2, 'INSIGHT_JOB_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('待岗', '待岗', 1, null, 3, 'INSIGHT_JOB_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('1', '已知', 1, null, 1, 'INSIGHT_DEVICE_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('0', '未知', 1, null, 2, 'INSIGHT_DEVICE_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('gt', '>', 1, null, 1, 'INSIGHT_FILTER_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('lt', '<', 1, null, 2, 'INSIGHT_FILTER_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('eq', '=', 1, null, 3, 'INSIGHT_FILTER_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('ge', '>=', 1, null, 4, 'INSIGHT_FILTER_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('le', '<=', 1, null, 5, 'INSIGHT_FILTER_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('ne', '!=', 1, null, 6, 'INSIGHT_FILTER_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('btwe', '介于之间(包含)', 1, null, 7, 'INSIGHT_FILTER_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('btw', '介于之间(不包含)', 1, null, 8, 'INSIGHT_FILTER_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('oute', '介于之外(包含)', 1, null, 9, 'INSIGHT_FILTER_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('out', '介于之外(不包含)', 1, null, 10, 'INSIGHT_FILTER_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userName', '用户名', 1, null, 1, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('accountName', '账号名', 1, null, 2, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceName', '设备名称', 1, null, 3, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceIP', 'IP地址', 1, null, 4, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('appName', '应用名', 1, null, 5, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataName', '数据名', 1, null, 6, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomalyName', '异常类型', 1, null, 7, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatName', '威胁类型', 1, null, 8, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomalyDesc', '异常描述', 1, null, 9, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomalySummary', '异常摘要', 1, null, 10, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('high', '高(8-10分)', 1, '8,10', 1, 'INSIGHT_ANOMALY_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('mid', '中(5-7分)', 1, '5,7', 2, 'INSIGHT_ANOMALY_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('low', '低(1-4分)', 1, '1,4', 3, 'INSIGHT_ANOMALY_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userName', '用户名', 1, null, 1, 'INSIGHT_USER_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('accountName', '账号名', 1, null, 2, 'INSIGHT_USER_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userEmail', '邮箱', 1, null, 3, 'INSIGHT_USER_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userIDCard', '身份证号码', 1, null, 4, 'INSIGHT_USER_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userCellPhone', '手机号码', 1, null, 5, 'INSIGHT_USER_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userPhone', '电话号码', 1, null, 6, 'INSIGHT_USER_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userUUID', '自然人唯一标识', 1, null, 7, 'INSIGHT_USER_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceName', '名称', 1, null, 1, 'INSIGHT_DEVICE_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceIP', 'IP地址', 1, null, 2, 'INSIGHT_DEVICE_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceUUID', '设备唯一标识', 1, null, 3, 'INSIGHT_DEVICE_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('appName', '应用名', 1, null, 1, 'INSIGHT_APP_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('appUUID', '应用唯一标识', 1, null, 3, 'INSIGHT_APP_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataName', '数据名', 1, null, 1, 'INSIGHT_DATA_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataUUID', '数据唯一标识', 1, null, 3, 'INSIGHT_DATA_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('high', '高(8-10分)', 1, '8,10', 1, 'INSIGHT_USER_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('mid', '中(5-7分)', 1, '5,7', 2, 'INSIGHT_USER_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('low', '低(1-4分)', 1, '1,4', 3, 'INSIGHT_USER_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('high', '高(8-10分)', 1, '8,10', 1, 'INSIGHT_DEVICE_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('mid', '中(5-7分)', 1, '5,7', 2, 'INSIGHT_DEVICE_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('low', '低(1-4分)', 1, '1,4', 3, 'INSIGHT_DEVICE_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('high', '高(8-10分)', 1, '8,10', 1, 'INSIGHT_APP_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('mid', '中(5-7分)', 1, '5,7', 2, 'INSIGHT_APP_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('low', '低(1-4分)', 1, '1,4', 3, 'INSIGHT_APP_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('high', '高(8-10分)', 1, '8,10', 1, 'INSIGHT_DATA_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('mid', '中(5-7分)', 1, '5,7', 2, 'INSIGHT_DATA_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('low', '低(1-4分)', 1, '1,4', 3, 'INSIGHT_DATA_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('high', '高(8-10分)', 1, '8,10', 1, 'INSIGHT_PROGRAM_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('mid', '中(5-7分)', 1, '5,7', 2, 'INSIGHT_PROGRAM_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('low', '低(1-4分)', 1, '1,4', 3, 'INSIGHT_PROGRAM_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userName', '用户名', 1, null, 1, 'INSIGHT_THREAT_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('accountName', '账号名', 1, null, 2, 'INSIGHT_THREAT_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceName', '设备名称', 1, null, 3, 'INSIGHT_THREAT_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceIP', 'IP地址', 1, null, 4, 'INSIGHT_THREAT_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('appName', '应用名', 1, null, 5, 'INSIGHT_THREAT_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataName', '数据名', 1, null, 6, 'INSIGHT_THREAT_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatName', '威胁类型', 1, null, 7, 'INSIGHT_THREAT_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomalyName', '异常类型', 1, null, 8, 'INSIGHT_THREAT_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatDesc', '威胁描述', 1, null, 9, 'INSIGHT_THREAT_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatSummary', '威胁摘要', 1, null, 10, 'INSIGHT_THREAT_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatOption', '威胁处置说明', 1, null, 11, 'INSIGHT_THREAT_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('high', '高(8-10分)', 1, '8,10', 1, 'INSIGHT_THREAT_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('mid', '中(5-7分)', 1, '5,7', 2, 'INSIGHT_THREAT_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('low', '低(1-4分)', 1, '1,4', 3, 'INSIGHT_THREAT_SCORE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('未调查', '未调查', 1, null, 1, 'INSIGHT_THREAT_SURVEY_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('调查中', '调查中', 1, null, 2, 'INSIGHT_THREAT_SURVEY_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('调查完成', '调查完成', 1, null, 3, 'INSIGHT_THREAT_SURVEY_RESULT_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('确认威胁', '确认威胁,未处理', 1, null, 1, 'INSIGHT_THREAT_SURVEY_RESULT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('误报', '误报', 1, null, 2, 'INSIGHT_THREAT_SURVEY_SCORE_RESULT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('positive', '正选', 1, null, 1, 'CUBE_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('reverse', '反选', 1, null, 2, 'CUBE_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('match', '匹配', 1, null, 3, 'CUBE_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('reg', '正则', 1, null, 4, 'CUBE_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('gt', '大于', 1, null, 5, 'CUBE_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('lt', '小于', 1, null, 6, 'CUBE_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('eq', '等于', 1, null, 7, 'CUBE_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('ne', '不等于', 1, null, 8, 'CUBE_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('index', '度量值', 1, null, 1, 'CUBE_FIELD_CATEGORY', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dim', '参考维度', 1, null, 2, 'CUBE_FIELD_CATEGORY', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('sum', '求和', 1, null, 1, 'CUBE_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('count', '数量', 1, null, 3, 'CUBE_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('max', '最大值', 1, null, 4, 'CUBE_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('min', '最小值', 1, null, 5, 'CUBE_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('avg', '平均值', 1, null, 6, 'CUBE_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('mid', '中位数', 1, null, 7, 'CUBE_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('upper', '上四分位', 1, null, 8, 'CUBE_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('lower', '下四分位', 1, null, 9, 'CUBE_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('var', '方差', 1, null, 10, 'CUBE_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('stddev', '标准差', 1, null, 11, 'CUBE_OPERATOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('year', '年', 1, null, 1, 'CUBE_TIME_RANGE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('month', '月', 1, null, 2, 'CUBE_TIME_RANGE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('week', '周', 1, null, 3, 'CUBE_TIME_RANGE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('day', '天', 1, null, 4, 'CUBE_TIME_RANGE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('hour', '小时', 1, null, 5, 'CUBE_TIME_RANGE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('toYear', 'toYear', 1, null, 1, 'CUBE_DATE_FUNCTION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('toMonth ', 'toMonth', 1, null, 2, 'CUBE_DATE_FUNCTION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('toDayOfYear ', 'toDayOfYear', 1, null, 3, 'CUBE_DATE_FUNCTION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('toDayOfMonth', 'toDayOfMonth', 1, null, 4, 'CUBE_DATE_FUNCTION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('toDayOfWeek', 'toDayOfWeek', 1, null, 5, 'CUBE_DATE_FUNCTION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('toHour ', 'toHour', 1, null, 6, 'CUBE_DATE_FUNCTION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('toMinute', 'toMinute', 1, null, 7, 'CUBE_DATE_FUNCTION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('重要', '重要', 1, null, 1, 'INSIGHT_APP_WATCHLIST', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('maxValue', '最大值法', 1, null, 1, 'INSIGHT_SCORE_RULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('tenPoint', '累加映射10分法', 1, null, 2, 'INSIGHT_SCORE_RULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('avgValue', '平均值法', 1, null, 3, 'INSIGHT_SCORE_RULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('entropy', '熵权法', 1, null, 4, 'INSIGHT_SCORE_RULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('modelExplore', 'http://127.0.0.1:8888/?token=45e1fc35417cbb8e7fc5b4d6cbf9ee7696416075432a715e', 1, null, 1, 'URL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('ntpServer', '', 1, null, null, 'UEBA_SYS_TIME', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('timeInterval', '1', 1, null, null, 'UEBA_SYS_TIME', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('timeUnit', 'second', 1, null, null, 'UEBA_SYS_TIME', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('sysTime', '2022-05-20 16:18:55', 1, null, null, 'UEBA_SYS_TIME', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('path', '/home/<USER>/backup_file/mysql/', 1, null, null, 'SYSTEM_MYSQL_BACKUP_PATH', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('第三方外包', '第三方外包', 1, null, 0, 'INSIGHT_USER_PERSONTYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('企业员工', '企业员工', 1, null, 1, 'INSIGHT_USER_PERSONTYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('database', 'ueba', 1, null, 0, 'SYSTEM_DW_DATABASE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('MergeTree', 'MergeTree', 1, null, 0, 'SYSTEM_DW_TABLE_ENGINE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('UInt8', 'UInt8', 1, null, 0, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('UInt16', 'UInt16', 1, null, 1, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('UInt32', 'UInt32', 1, null, 2, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('UInt64', 'UInt64', 1, null, 3, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Int8', 'Int8', 1, null, 4, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Int16', 'Int16', 1, null, 5, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Int32', 'Int32', 1, null, 6, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Int64', 'Int64', 1, null, 7, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Float32', 'Float32', 1, null, 8, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Float64', 'Float64', 1, null, 9, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('String', 'String', 1, null, 10, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Date', 'Date', 1, null, 11, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('DateTime', 'DateTime', 1, null, 12, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('DateTime64', 'DateTime64', 1, null, 13, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('title', '', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('businessId', 'f34e2956480d43a8970933b4afabc5e0', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('sysadmin', 'sysadmin', 1, null, 0, 'INSIGHT_SURVEY_ACCOUNT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('ReplicatedMergeTree', 'ReplicatedMergeTree', 1, null, 1, 'SYSTEM_DW_TABLE_ENGINE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('ReplicatedReplacingMergeTree', 'ReplicatedReplacingMergeTree', 1, null, 2, 'SYSTEM_DW_TABLE_ENGINE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('MySQL', 'MySQL', 1, null, 3, 'SYSTEM_DW_TABLE_ENGINE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('信息', '信息', 1, null, 1, 'INSIGHT_DATA_VALUE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('非常敏感', '非常敏感', 1, null, 2, 'INSIGHT_DATA_VALUE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('敏感', '敏感', 1, null, 3, 'INSIGHT_DATA_VALUE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('不敏感', '不敏感', 1, null, 4, 'INSIGHT_DATA_VALUE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('高', '高', 1, null, 1, 'INSIGHT_APP_VALUE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('中', '中', 1, null, 2, 'INSIGHT_APP_VALUE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('低', '低', 1, null, 3, 'INSIGHT_APP_VALUE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('高', '高', 1, null, 1, 'INSIGHT_DEVICE_VALUE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('中', '中', 1, null, 2, 'INSIGHT_DEVICE_VALUE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('低', '低', 1, null, 3, 'INSIGHT_DEVICE_VALUE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('异常类型1', '异常类型1', 1, null, 9, ' INSIGHT_ANOMALY_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('异常类型2', '异常类型2', 1, null, 10, ' INSIGHT_ANOMALY_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('defaultTitle', '安全数据中心 | SDC', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('defaultBusinessId', 'f34e2956480d43a8970933b4afabc5e0', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('defaultHeadTitle', 'UEBA', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('defaultHeadBusinessId', 'f9cb054c2d51463db9e4dc87c193f174', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('headTitle', 'UEBA', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('headBusinessId', 'f9cb054c2d51463db9e4dc87c193f174', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('year', '年', 1, null, 1, 'ACTION_SESSION_TIME_RANGE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('month', '月', 1, null, 2, 'ACTION_SESSION_TIME_RANGE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('week', '周', 1, null, 3, 'ACTION_SESSION_TIME_RANGE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('day', '天', 1, null, 4, 'ACTION_SESSION_TIME_RANGE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('hour', '小时', 1, null, 5, 'ACTION_SESSION_TIME_RANGE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('positive', '正选', 1, null, 1, 'ACTION_SESSION_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('reverse', '反选', 1, null, 2, 'ACTION_SESSION_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('match', '匹配', 1, null, 3, 'ACTION_SESSION_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('reg', '正则', 1, null, 4, 'ACTION_SESSION_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('gt', '大于', 1, null, 5, 'ACTION_SESSION_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('lt', '小于', 1, null, 6, 'ACTION_SESSION_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('eq', '等于', 1, null, 7, 'ACTION_SESSION_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('ne', '不等于', 1, null, 8, 'ACTION_SESSION_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('minute', '分钟', 1, null, 6, 'ACTION_SESSION_TIME_RANGE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('second', '秒', 1, null, 7, 'ACTION_SESSION_TIME_RANGE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('未调查', '未调查', 1, null, 1, 'INSIGHT_ANOMALY_SURVEY_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('调查中', '调查中', 1, null, 2, 'INSIGHT_ANOMALY_SURVEY_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('调查完成', '调查完成', 1, null, 3, 'INSIGHT_ANOMALY_SURVEY_RESULT_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('确认异常', '确认异常,未处理', 1, null, 1, 'INSIGHT_ANOMALY_SURVEY_RESULT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('误报', '误报', 1, null, 2, 'INSIGHT_ANOMALY_SURVEY_SCORE_RESULT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('UUID', 'UUID', 1, null, 14, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Array(String)', 'Array(String)', 1, null, 15, 'SYSTEM_DW_TABLE_FIELD_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('localPath', '/home/<USER>/insight/alarm/local/', 1, null, 1, 'INSIGHT_ALARM_LOCAL_FILE_PATH', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('linkUrl', 'http://127.0.0.1:8081/insight/exception/list/', 1, null, 1, 'INSIGHT_ALARM_LINK_URL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('1', '已知', 1, null, 1, 'INSIGHT_APP_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('0', '未知', 1, null, 2, 'INSIGHT_APP_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('1', '已知', 1, null, 1, 'INSIGHT_DATA_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('0', '未知', 1, null, 2, 'INSIGHT_DATA_STATE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('login', '登录', 1, null, 1, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('logout', '退出', 1, null, 2, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('search', '查询', 1, null, 3, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('preview', '预览', 1, null, 4, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('refresh', '刷新', 1, null, 5, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('add', '新增', 1, null, 6, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('copy', '复制', 1, null, 7, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('save', '保存', 1, null, 8, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('import', '导入', 1, null, 9, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('edit', '编辑', 1, null, 10, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('setting', '配置', 1, null, 11, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('move', '移动', 1, null, 12, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('start', '启用', 1, null, 13, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('stop', '停用', 1, null, 14, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('delete', '删除', 1, null, 15, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('upload', '上传', 1, null, 16, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('download', '下载', 1, null, 17, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('saveOrUpdate', '保存更新', 1, null, 18, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('detail', '查看', 1, null, 19, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('test', '测试', 1, null, 20, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('parse', '解析', 1, null, 21, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('match', '匹配', 1, null, 22, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('startOrStop', '启动和暂停', 1, null, 23, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('reset', '重置', 1, null, 24, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('index', '首页', 1, null, 1, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exception', '异常洞察', 1, null, 2, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threat', '威胁洞察', 1, null, 3, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('insight-user', '用户分析', 1, null, 4, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('insight-device', '设备分析', 1, null, 5, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('insight-application', '应用分析', 1, null, 6, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('insight-data', '数据分析', 1, null, 7, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('insight-program', '程序分析', 1, null, 8, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('visual-risk-overview', '风险总览', 1, null, 9, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('ai_bi_dashboard', '自定义仪表盘', 1, null, 10, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataExplore', '数据探索', 1, null, 11, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('data-access', '数据采集', 1, null, 12, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataMart', ' 特征挖掘', 1, null, 13, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('baseInfo', '基础信息', 1, null, 14, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('watchList', '观察列表', 1, null, 15, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('model-explore', '模型探索', 1, null, 16, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exception-model', '异常模型', 1, null, 17, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threat-model', '威胁模型', 1, null, 18, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('scheduler-configuration', '模型调度', 1, null, 19, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('notebook', 'notebook', 1, null, 20, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('account-right', '账号权限', 1, null, 21, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('data-dictionary', '数据字典', 1, null, 22, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('list-manage', '名单管理', 1, null, 23, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('config-manage', '配置管理', 1, null, 24, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('resource-manage', '资源管理', 1, null, 25, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('warn', '告警管理', 1, null, 26, 'SYSTEM_LOG_OPT_MODULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('success', '成功', 1, null, 1, 'SYSTEM_LOG_LOG_RESULT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('error', '失败', 1, null, 2, 'SYSTEM_LOG_LOG_RESULT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('isDocker', 'false', 1, null, 2, 'ETL_SCRIPT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dockerImageName', 'python:slim', 1, null, 1, 'ETL_SCRIPT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('timeout', '30000', 1, null, 3, 'ETL_SCRIPT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('export', '导出', 1, null, 9, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('企业员工', '企业员工', 1, null, 1, 'personType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('第三方外包', '第三方外包', 1, null, 2, 'personType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('离职', '离职', 1, null, 1, 'personStatus', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('在职', '在职', 1, null, 2, 'personStatus', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('待岗', '待岗', 1, null, 3, 'personStatus', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Normal', '普通账号', 1, null, 1, 'accountType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Admin', '管理员账号', 1, null, 2, 'accountType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Service', '服务账号', 1, null, 3, 'accountType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Unknown', '未知账号', 1, null, 4, 'accountType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('锁定', '锁定', 1, null, 1, 'accountStatus', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('正常', '正常', 1, null, 2, 'accountStatus', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('高', '高', 1, null, 1, 'assetValue', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('中', '中', 1, null, 2, 'assetValue', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('低', '低', 1, null, 3, 'assetValue', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('信息', '信息', 1, null, 1, 'dataValue', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('不敏感', '不敏感', 1, null, 2, 'dataValue', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('敏感', '敏感', 1, null, 3, 'dataValue', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('非常敏感', '非常敏感', 1, null, 4, 'dataValue', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('src_user', '源用户', 1, null, null, 'alarmType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dst_user', '目标用户', 1, null, null, 'alarmType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('src_device', '源设备', 1, null, null, 'alarmType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dst_device', '目标设备', 1, null, null, 'alarmType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('src_account', '源账号', 1, null, null, 'alarmType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dst_account', '目标账号', 1, null, null, 'alarmType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('app', '应用', 1, null, null, 'alarmType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('program', '程序', 1, null, null, 'alarmType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('data', '数据', 1, null, null, 'alarmType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('programName', '程序名', 1, null, 1, 'INSIGHT_PROGRAM_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('黑客攻击', '黑客攻击', 0, null, 1, 'ANOMALY_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('黑客攻击', '黑客攻击', 1, null, 1, 'THREAT_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('high', '高', 1, '8,10', 1, 'INSIGHT_ANOMALY_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('mid', '中', 1, '5,7', 2, 'INSIGHT_ANOMALY_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('low', '低', 1, '1,4', 3, 'INSIGHT_ANOMALY_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('high', '高', 1, '8,10', 1, 'INSIGHT_THREAT_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('mid', '中', 1, '5,7', 2, 'INSIGHT_THREAT_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('low', '低', 1, '1,4', 3, 'INSIGHT_THREAT_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('KAFKA', 'Kafka', 1, null, 3, 'WRITE_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('srcDeviceTrans', '1', 1, null, 1, 'USER_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dstDeviceTrans', '1', 1, null, 2, 'USER_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataTransApp', '1', 1, null, 3, 'USER_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataTransNoApp', '1', 1, null, 4, 'USER_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceLogin', '1', 1, null, 5, 'USER_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userOperate', '1', 1, null, 6, 'USER_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceRecord', '1', 1, null, 7, 'USER_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('applicationDeviceVisit', '1', 1, null, 1, 'APPLICATION_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('applicationUserLogin', '1', 1, null, 2, 'APPLICATION_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataUserTrans', '1', 1, null, 1, 'DATA_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataDeviceTrans', '1', 1, null, 2, 'DATA_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataOperateType', '1', 1, null, 3, 'DATA_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceUserTrans', '1', 1, null, 3, 'DEVICE_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceUserLogin', '1', 1, null, 4, 'DEVICE_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceAppTrans', '1', 1, null, 5, 'DEVICE_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceOtherTrans', '1', 1, null, 6, 'DEVICE_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceUserUse', '1', 1, null, 7, 'DEVICE_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceMac', '1', 1, null, 8, 'DEVICE_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('graphicSimple', '1', 1, null, 1, 'ANOMALY_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomalyRelation', '1', 1, null, 2, 'ANOMALY_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatIncludeAnomaly', '1', 1, null, 3, 'ANOMALY_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('graphicSimple', '1', 1, null, 1, 'THREAT_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatRelation', '1', 1, null, 2, 'THREAT_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatTimeTable', '1', 1, null, 3, 'THREAT_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomalyIncludeThreat', '1', 1, null, 4, 'THREAT_CARD_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('极敏感级', '极敏感级', 1, null, 1, 'dataLevel', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('敏感级', '敏感级', 1, null, 2, 'dataLevel', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('较敏感级', '较敏感级', 1, null, 3, 'dataLevel', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('低敏感级', '低敏感级', 1, null, 4, 'dataLevel', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('已备案', '已备案', 1, null, 2, 'dataRecordStatus', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('未备案', '未备案', 1, null, 3, 'dataRecordStatus', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('待确认', '待确认', 1, null, 4, 'dataRecordStatus', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionLatest', '1', 1, '最近异常', 1, 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionClassify', '1', 1, '异常分类', 2, 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionRateLevel', '1', 1, '异常评分等级', 3, 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionToThreat', '1', 1, '异常组成的威胁', 4, 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionWatchList', '1', 1, '异常观察列表', 5, 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionInUser', '1', 1, '用户观察列表中的异常', 6, 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionTime', '1', 1, '异常时间表', 7, 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionTrend', '1', 1, '异常趋势', 8, 'EXCEPTION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatLatest', '1', 1, '最近威胁', 1, 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatClassify', '1', 1, '威胁分类', 2, 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatSituation', '1', 1, '威胁现状', 3, 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatTop10', '1', 1, '威胁处置排行TOP10', 4, 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatInException', '1', 1, '威胁中的异常', 5, 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatWatchList', '1', 1, '威胁观察列表', 6, 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatTime', '1', 1, '威胁时间表', 7, 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatTrend', '1', 1, '威胁趋势', 8, 'THREAT_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('programScoreRank', '1', 1, '程序评分排行', 1, 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('treatProgram', '1', 1, '威胁相关程序', 2, 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionProgram', '1', 1, '异常相关程序', 3, 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('programScoreLevel', '1', 1, '程序评分等级', 4, 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionProgramTrend', '1', 1, '异常程序趋势', 5, 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('treatProgramTrend', '1', 1, '威胁程序趋势', 6, 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('programWatchList', '1', 1, '程序观察列表', 7, 'PROGRAM_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('applicationScoreRank', '1', 1, '应用评分排行', 1, 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('treatApplication', '1', 1, '威胁相关应用', 2, 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionApplication', '1', 1, '异常相关应用', 3, 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('applicationScoreLevel', '1', 1, '应用评分等级', 4, 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('applicationWorth', '1', 1, '应用价值', 5, 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('applicationType', '1', 1, '应用类型', 6, 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionApplicationTrend', '1', 1, '异常应用趋势', 7, 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('treatApplicationTrend', '1', 1, '威胁应用趋势', 8, 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('applicationWatchList', '1', 1, '应用观察列表', 9, 'APPLICATION_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataScoreRank', '1', 1, '数据评分排行', 1, 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('treatInData', '1', 1, '威胁相关数据', 2, 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionInData', '1', 1, '异常相关数据', 3, 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataScoreLevel', '1', 1, '数据评分等级', 4, 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataWorth', '1', 1, '数据价值', 5, 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataType', '1', 1, '数据类型', 6, 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionDataTrend', '1', 1, '异常数据趋势', 7, 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('treatDataTrend', '1', 1, '威胁数据趋势', 8, 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataWatchList', '1', 1, '数据观察列表', 9, 'DATA_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceScoreRank', '1', 1, '设备评分排行', 1, 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('treatInDevice', '1', 1, '威胁相关设备', 2, 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionInDevice', '1', 1, '异常相关设备', 3, 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceScoreLevel', '1', 1, '设备评分等级', 4, 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceType', '1', 1, '设备类型', 5, 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceWatchList', '1', 1, '设备观察列表', 6, 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionDeviceTrend', '1', 1, '异常设备趋势', 7, 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('treatDeviceTrend', '1', 1, '威胁设备趋势', 8, 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceInDept', '1', 1, '设备所属部门', 9, 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceWorth', '1', 1, '设备价值', 10, 'DEVICE_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userScoreRank', '1', 1, '用户评分排行', 1, 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('treatInUser', '1', 1, '威胁相关用户', 2, 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionInUser', '1', 1, '异常相关用户', 3, 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userScoreLevel', '1', 1, '用户评分等级', 4, 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userStatus', '1', 1, '在职状态', 5, 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userWatchList', '1', 1, '用户观察列表', 6, 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('exceptionUserTrend', '1', 1, '异常用户趋势', 7, 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('treatUserTrend', '1', 1, '威胁用户趋势', 8, 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userAddress', '1', 1, '用户所在地址', 9, 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userDept', '1', 1, '用户所在部门', 10, 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userType', '1', 1, '用户类型', 11, 'USER_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('loginFailEvent', '1', 1, '登录失败事件', 1, 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('inactiveUserCount', '1', 1, '非活跃用户数', 2, 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataTransferCount', '1', 1, '数据传输量', 3, 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('loginSuccessDeviceType', '1', 1, '登录成功设备类型', 4, 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('loginFailUser', '1', 1, '登录失败用户', 5, 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('loginFailDevice', '1', 1, '登录失败目标设备', 6, 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('visitDataType', '1', 1, '被访问的数据类型', 7, 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('sourceDeviceTransferData', '1', 1, '源设备传输数据', 8, 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('targetDeviceTransferData', '1', 1, '目标设备传输数据', 9, 'ANALYSIS_OVERVIEW_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('1', '一级分类', 1, '模型分类/一级分类', 1, 'modelType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('2', '二级分类', 1, '模型分类/二级分类', 1, 'modelType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('3', '三级分类', 1, '模型分类/三级分类', 1, 'modelType', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('backgroundBusinessId', '7c0adc1c7c0e7f0d72e69b8bab88e700', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('defaultBackgroundBusinessId', '7c0adc1c7c0e7f0d72e69b8bab88e700', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('copyright', '1', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('defaultCopyright', '1', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('logo_suffix', 'svg、png、jpg、gif', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('favicon_suffix', 'svg、png、jpg、gif、ico', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('background_suffix', 'jpeg、png、jpg', 1, null, null, 'SYSTEM_LOGO_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threat', '1', 1, '威胁', 1, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomaly', '1', 1, '异常', 2, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('user', '1', 1, '用户', 3, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('device', '1', 1, '设备', 4, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('data', '1', 1, '数据', 6, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('program', '1', 1, '程序', 7, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('app', '1', 1, '应用', 5, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('newlyThreatCard', '1', 1, '最新威胁', 8, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threatTrendCard', '1', 1, '威胁趋势', 9, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('newlyAnomalyCard', '1', 1, '最新异常', 10, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomalyTrendCard', '1', 1, '异常趋势', 11, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userScoreRankCard', '1', 1, '用户评分排行', 12, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userStatusCard', '1', 1, '用户状态', 13, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceScoreRankCard', '1', 1, '设备评分排行', 14, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceStatusCard', '1', 1, '设备状态', 15, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataScoreRankCard', '1', 1, '数据评分排行', 16, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataStatusCard', '1', 1, '数据状态', 17, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('programScoreRankCard', '1', 1, '应用评分排行', 18, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('programStatusCard', '1', 1, '应用状态', 19, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('departmentThreatAndAnomalyCard', '1', 1, '部门异常/威胁数据数量分布TOP5', 20, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataThreatAndAnomalyTrendCard', '1', 1, '数据异常/威胁数量趋势', 21, 'HOME_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userTag', '1', 1, '用户标签', 0, 'USER_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('appTag', '1', 1, '应用标签', 0, 'APPLICATION_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataTag', '1', 1, '数据标签', 0, 'DATA_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceTag', '1', 1, '设备标签', 0, 'DEVICE_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('programTag', '1', 1, '程序标签', 0, 'PROGRAM_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomalyMapNum', '50', 1, null, 1, 'INSIGHT_ANOMALY_MAP_NUM', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('srcUser', '源用户', 1, null, 11, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('srcDevice', '源设备', 1, null, 12, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dstUser', '目标用户', 1, null, 13, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dstDevice', '目标设备', 1, null, 14, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('appName', '目标应用', 1, null, 15, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataName', '目标数据', 1, null, 16, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('programName', '目标程序', 1, null, 17, 'INSIGHT_ANOMALY_LISTFIELD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('mysql', '/usr/bin/', 1, 'mysql备份的命令路径', 1, 'BACK_UP_ORDER', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userSevenDayPolar', '1', 1, '最近7天热力图', 7, 'USER_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userThreeWeekPolar', '1', 1, '近3周热力图', 9, 'USER_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('programSevenDayPolar', '1', 1, '最近7天热力图', 1, 'PROGRAM_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('programThreeWeekPolar', '1', 1, '近3周热力图', 1, 'PROGRAM_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceThreeWeekPolar', '1', 1, '近3周热力图', 1, 'DEVICE_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deviceSevenDayPolar', '1', 1, '近7天热力图', 2, 'DEVICE_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('applicationThreeWeekPolar', '1', 1, '近3周热力图', 1, 'APPLICATION_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('applicationSevenDayPolar', '1', 1, '近7天热力图', 1, 'APPLICATION_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataThreeWeekPolar', '1', 1, '近3周热力图', 1, 'DATA_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataSevenDayPolar', '1', 1, '近7天热力图', 1, 'DATA_CARD_CONFIG', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('认证类场景', '认证类场景', 0, null, 2, 'ANOMALY_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('敏感数据泄漏类场景', '敏感数据泄漏类场景', 0, null, 3, 'ANOMALY_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('合规审计类场景', '合规审计类场景', 0, null, 4, 'ANOMALY_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('确认异常,已处理', '确认异常,已处理', 1, null, 2, 'INSIGHT_ANOMALY_SURVEY_RESULT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('确认威胁,已处理', '确认威胁,已处理', 1, null, 2, 'INSIGHT_THREAT_SURVEY_RESULT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('safeNotice', '安全通告', 1, '预警类型', 1, 'EARLY_WARN_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('attackWarn', '攻击预警', 1, '预警类型', 2, 'EARLY_WARN_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('bugWarn', '漏洞预警', 1, '预警类型', 3, 'EARLY_WARN_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('virusWarn', '病毒预警', 1, '预警类型', 4, 'EARLY_WARN_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('first', '一级', 1, '预警级别', 1, 'EARLY_WARN_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('second', '二级', 1, '预警级别', 2, 'EARLY_WARN_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('third', '三级', 1, '预警级别', 3, 'EARLY_WARN_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('fourth', '四级', 1, '预警级别', 4, 'EARLY_WARN_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('fifth', '五级', 1, '预警级别', 5, 'EARLY_WARN_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('inside', '内部预警', 1, '预警来源分类', 1, 'EARLY_WARN_SOURCE_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('outside', '外部预警', 1, '预警来源分类', 2, 'EARLY_WARN_SOURCE_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('state', '国家主管部门', 1, '预警来源', 1, 'EARLY_WARN_SOURCE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('organization', '安全组织', 1, '预警来源', 2, 'EARLY_WARN_SOURCE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('company', '厂商', 1, '预警来源', 3, 'EARLY_WARN_SOURCE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('highLevelUnit', '上级单位', 1, '预警来源', 4, 'EARLY_WARN_SOURCE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('unit', '本单位', 1, '预警来源', 5, 'EARLY_WARN_SOURCE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('other', '其他', 1, '预警来源', 6, 'EARLY_WARN_SOURCE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('generalSet', '通用设置', 1, '预警-漏洞类型', 1, 'EARLY_WARN_BUG_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('hostInfo', '主机信息', 1, '预警-漏洞类型', 2, 'EARLY_WARN_BUG_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('accountInfo', '账号信息', 1, '预警-漏洞类型', 3, 'EARLY_WARN_BUG_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('portService', '端口服务', 1, '预警-漏洞类型', 4, 'EARLY_WARN_BUG_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('risk', '风险漏洞', 1, '预警-漏洞类型', 5, 'EARLY_WARN_BUG_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('other', '其他', 1, '预警-漏洞类型', 6, 'EARLY_WARN_BUG_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('encryptType', 'MD5', 1, '资源备份数字签名：MD5/SM2', 1, 'BACK_UP_ENCRYPT', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('help', '观安观鉴用户异常行为分析系统-产品使用手册3.1.0.pdf', 1, '是否显示下载帮助', 1, 'SYSTEM_HELP', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('isNull', '不非空', 1, null, 9, 'ACTION_SESSION_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('isNotNull', '非空', 1, null, 10, 'ACTION_SESSION_FILTER_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('executeCycle', '02', 1, '01-立即执行，02-周期执行', null, 'UEBA_SYNC_BASE_INFO', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('executeType', 'day', 1, '时间类型day', null, 'UEBA_SYNC_BASE_INFO', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('executeConfig', '15:09', 1, '时间', null, 'UEBA_SYNC_BASE_INFO', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('switchContr', '1', 1, '开关：0关1开', null, 'UEBA_SYNC_BASE_INFO', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('weightAvg', '加权平均值法', 1, '洞察-评分算法', 5, 'INSIGHT_SCORE_RULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('composite', '综合法', 1, '洞察-评分算法', 6, 'INSIGHT_SCORE_RULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('maxValue', '最大值法', 1, null, 0, 'INSIGHT_THREAT_SCORE_RULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('avgValue', '平均值法', 1, null, 1, 'INSIGHT_THREAT_SCORE_RULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('weightAvg', '加权平均值法', 1, null, 2, 'INSIGHT_THREAT_SCORE_RULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomalyNum', '异常数量统计法', 1, null, 3, 'INSIGHT_THREAT_SCORE_RULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomalyCategory', '异常分类统计法', 1, null, 4, 'INSIGHT_THREAT_SCORE_RULE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('UTC Timestamp Millisecond', '\d{13}', 1, null, 5, 'SOURCE_SET_TIME_REG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('UTC Timestamp Second', '\d{10}', 1, null, 6, 'SOURCE_SET_TIME_REG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('yyyy年MM月dd日 HH:mm:ss', '\d{4}年\d{1,2}月\d{1,2}日\s\d{1,2}:\d{1,2}:\d{1,2}', 1, null, 7, 'SOURCE_SET_TIME_REG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('MMM d, yyyy h:m:s aa', '\w+\s\d{1,2},\s\d{4}\s\d{1,2}:\d{1,2}:\d{1,2}\s[a-zA-Z]{2}', 1, null, 8, 'SOURCE_SET_TIME_REG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('d/MMM/yyyy:H:m:s Z', '\d{1,2}\/\w+\/\d{4}:\d{1,2}:\d{1,2}:\d{1,2}\s[-+]?[0-9]+', 1, null, 9, 'SOURCE_SET_TIME_REG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('/security/fw', '/security/fw', 1, null, 0, 'INSIGHT_ANOMALY_DATA', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('青藤云日志', '青藤云日志', 1, null, 1, 'INSIGHT_ANOMALY_DATA', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('days', '12', 1, '距离License过期天数提醒', 1, 'LICENSE_EXPIRE_TIP_DAYS', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('gatewayAddr', 'smtp.qq.com', 1, null, null, 'UEBA_SYS_MAIL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('gatewayPort', '25', 1, null, null, 'UEBA_SYS_MAIL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('sendMailAddr', '<EMAIL>', 1, null, null, 'UEBA_SYS_MAIL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('sendMailAccount', '<EMAIL>', 1, null, null, 'UEBA_SYS_MAIL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('password', 'Ufhw+Wze9yF0ljKTp3l/qLZwvJ7/J5FFcKFyyw+cB+c=', 1, null, null, 'UEBA_SYS_MAIL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('limit', '5', 1, null, 1, 'DASHBOARD_LIMIT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('nfsEnable', 'off', 1, null, null, 'SYSTEM_FILE_CFG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('nfsFolder', '', 1, null, null, 'SYSTEM_FILE_CFG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('hdfsEnable', 'on', 1, null, null, 'SYSTEM_FILE_CFG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('hdfsServer', '', 1, null, null, 'SYSTEM_FILE_CFG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('userName', '', 1, null, null, 'SYSTEM_FILE_CFG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('password', '', 1, null, null, 'SYSTEM_FILE_CFG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('17', 'yyyyMMdd HH:mm:ss', 1, null, 17, 'DATEFORMAT', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomaly', '0', 1, null, 1, 'INSIGHT_CATEGORY_JOIN', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threat', '1', 1, null, 1, 'INSIGHT_CATEGORY_JOIN', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('18', 'yyyyMM.dd HH:mm:ss', 1, null, 18, 'DATEFORMAT', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('19', 'yyyyMM', 1, null, 19, 'DATEFORMAT', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('内部', '黑客攻击-内部', 1, null, 0, 'PRIMARY_THREAT_TYPE', '黑客攻击');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('外部', '黑客攻击-外部', 1, null, 1, 'PRIMARY_THREAT_TYPE', '黑客攻击');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('杀伤链', '黑客攻击-杀伤链', 1, null, 2, 'PRIMARY_THREAT_TYPE', '黑客攻击');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('图风险模型', '黑客攻击-图风险模型', 1, null, 3, 'PRIMARY_THREAT_TYPE', '黑客攻击');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('自定义', '黑客攻击-自定义', 1, null, 4, 'PRIMARY_THREAT_TYPE', '黑客攻击');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('user', '用户', 1, null, 0, 'INSIGHT_ALARM_OBJECT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('device', '设备', 1, null, 1, 'INSIGHT_ALARM_OBJECT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('anomaly', '异常', 1, null, 2, 'INSIGHT_ALARM_OBJECT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('threat', '威胁', 1, null, 3, 'INSIGHT_ALARM_OBJECT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('custom', '自定义', 1, null, 4, 'INSIGHT_ALARM_OBJECT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('11', '11', 1, null, null, 'ANOMALY_CLASSIFICATION', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('deface', '网页篡改', 1, '预警-攻击类型', 1, 'EARLY_WARN_ATTACK_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('phishing', '网络仿冒(钓鱼)', 1, '预警-攻击类型', 2, 'EARLY_WARN_ATTACK_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('spam', '垃圾信息', 1, '预警-攻击类型', 3, 'EARLY_WARN_ATTACK_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('leakage', '信息泄露与窃取', 1, '预警-攻击类型', 4, 'EARLY_WARN_ATTACK_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('inaptitude', '不适当邮件内容', 1, '预警-攻击类型', 5, 'EARLY_WARN_ATTACK_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('cheating', '网络诈骗', 1, '预警-攻击类型', 6, 'EARLY_WARN_ATTACK_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('other', '其它', 1, '预警-攻击类型', 7, 'EARLY_WARN_ATTACK_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('etlPreProcessAlarm', '采集预处理告警', 1, null, 0, 'INSIGHT_ALARM_PLAT_OBJECT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('homeDataCron', '0 0/10 * * * ?', 1, null, null, 'PAGE_DATA_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('内部', '内部', 1, null, 0, 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('外部警报', '外部警报', 1, null, 1, 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('外部攻击', '外部攻击', 1, null, 2, 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('外部扫描', '外部扫描', 1, null, 3, 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('向内', '向内', 1, null, 4, 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('向外', '向外', 1, null, 5, 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('本地', '本地', 1, null, 6, 'PRIMARY_ANOMALY_TYPE', '威胁范围分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('允许的', '允许的', 1, null, 0, 'PRIMARY_ANOMALY_TYPE', '异常状态分析');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('受阻', '受阻', 1, null, 1, 'PRIMARY_ANOMALY_TYPE', '异常状态分析');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('侦察', '侦察', 1, null, 0, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('初始访问', '初始访问', 1, null, 1, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('执行', '执行', 1, null, 2, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('持久化', '持久化', 1, null, 3, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('特权提升', '特权提升', 1, null, 4, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('防御规避', '防御规避', 1, null, 5, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('凭证访问', '凭证访问', 1, null, 6, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('发现', '发现', 1, null, 7, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('横向移动', '横向移动', 1, null, 8, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('采集', '采集', 1, null, 9, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('数据泄露', '数据泄露', 1, null, 10, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('命令与控制', '命令与控制', 1, null, 11, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('恶意破坏', '恶意破坏', 1, null, 12, 'PRIMARY_ANOMALY_TYPE', 'ATT&CK分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('感染', '感染', 1, null, 0, 'PRIMARY_ANOMALY_TYPE', '异常结果分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('可见度降低', '可见度降低', 1, null, 1, 'PRIMARY_ANOMALY_TYPE', '异常结果分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('数据销毁', '数据销毁', 1, null, 2, 'PRIMARY_ANOMALY_TYPE', '异常结果分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('拒绝服务', '拒绝服务', 1, null, 3, 'PRIMARY_ANOMALY_TYPE', '异常结果分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('失控', '失控', 1, null, 4, 'PRIMARY_ANOMALY_TYPE', '异常结果分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('对等组', '对等组', 1, null, 0, 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('蛮力', '蛮力', 1, null, 1, 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('违反政策', '违反政策', 1, null, 2, 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('列入黑名单', '列入黑名单', 1, null, 3, 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('外部', '外部', 1, null, 4, 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('飞行风险', '飞行风险', 1, null, 5, 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('移动存储', '移动存储', 1, null, 6, 'PRIMARY_ANOMALY_TYPE', '附加维表数据分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('用户账号安全类', '用户账号安全类', 1, null, 0, 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('人员异常打卡类', '人员异常打卡类', 1, null, 1, 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('失陷主机和应用类', '失陷主机和应用类', 1, null, 2, 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('数据泄露类', '数据泄露类', 1, null, 3, 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('运维操作及业务操作未知异常行为类', '运维操作及业务操作未知异常行为类', 1, null, 4, 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('运维操作合规审计类', '运维操作合规审计类', 1, null, 5, 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('业务操作合规审计类', '业务操作合规审计类', 1, null, 6, 'PRIMARY_ANOMALY_TYPE', '异常模型分类');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('send', '发送', 1, null, 25, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('query', '查询', 1, null, 26, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('execute', '执行', 1, null, 27, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('freeze', '冻结和解冻', 1, null, 28, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('restore', '恢复', 1, null, 29, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('未知', '未知', 1, null, 0, 'ETL_NODE_RESOURCE_POOL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('未知', '未知', 1, null, 0, 'ETL_NODE_POD', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('未知', '未知', 1, null, 0, 'ETL_NODE_NETWORK_DOMAIN', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Linux', 'Linux', 1, null, 0, 'ETL_NODE_HOST_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Windows', 'Windows', 1, null, 1, 'ETL_NODE_HOST_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Vmware', 'Vmware', 1, null, 2, 'ETL_NODE_HOST_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('Unix', 'Unix', 1, null, 3, 'ETL_NODE_HOST_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('relay', '中继', 1, null, 4, 'LOGMOUDLE_TYPE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('dataQualityAlarm', '数据质量告警', 1, null, 0, 'INSIGHT_ALARM_PLAT_OBJECT', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('1', '1级', 1, '低敏感级', null, 'ASSET_RESOURCE_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('2', '2级', 1, '较敏感级', null, 'ASSET_RESOURCE_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('3', '3级', 1, '敏感级', null, 'ASSET_RESOURCE_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('000', '机密类型', 1, '', null, 'ASSET_RESOURCE_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('111', '测试分类111', 1, '', null, 'ASSET_RESOURCE_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('1', 'IP信息', 1, '3级', null, 'ASSET_RESOURCE_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('2', '电话号码', 1, '4级', null, 'ASSET_RESOURCE_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('3', '手机号码', 1, '4级', null, 'ASSET_RESOURCE_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('4', '电子邮箱', 1, '3级', null, 'ASSET_RESOURCE_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('5', 'IMSI', 1, '3级', null, 'ASSET_RESOURCE_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('6', '宽带账号', 1, '4级', null, 'ASSET_RESOURCE_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('7', '用户名', 1, '3级', null, 'ASSET_RESOURCE_CLASSIFICATION', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('4', '4级', 1, '极敏感级', null, 'ASSET_RESOURCE_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('criminal_gang', '犯罪团伙', 1, null, 1, 'TAGS_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('virus_family', '病毒家族', 1, null, 2, 'TAGS_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('security_incident', '安全事件
', 1, null, 3, 'TAGS_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('related_field', '相关行业', 1, null, 4, 'TAGS_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('associative_zone', '相关区域', 1, null, 5, 'TAGS_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('security_hole', '安全漏洞', 1, null, 6, 'TAGS_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('tag_base', '基础标签', 1, null, 7, 'TAGS_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('public_information', '公共信息', 1, null, 8, 'TAGS_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('严重', '严重', 1, null, 0, 'INTELLIGENCE_THREAT_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('高', '高', 1, null, 1, 'INTELLIGENCE_THREAT_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('中', '中', 1, null, 2, 'INTELLIGENCE_THREAT_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('低', '低', 1, null, 3, 'INTELLIGENCE_THREAT_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('无威胁', '无威胁', 1, null, 4, 'INTELLIGENCE_THREAT_LEVEL', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('IDSS', '本平台', 1, null, 1, 'INFORMATION_SOURCE_VENDOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('THREAT_V43', '微步', 1, null, 1, 'INFORMATION_SOURCE_VENDOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('TIC', '启明', 1, null, 1, 'INFORMATION_SOURCE_VENDOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('IDSS_INSTITUTE', '观安', 1, null, 1, 'INFORMATION_SOURCE_VENDOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('批量导入', '批量导入', 1, '', 1, 'INTELLIGENCE_SOURCE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('单个新增', '单个新增', 1, '', 2, 'INTELLIGENCE_SOURCE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('情报碰撞', '情报碰撞', 1, '', 3, 'INTELLIGENCE_SOURCE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('情报查询', '情报查询', 1, '', 4, 'INTELLIGENCE_SOURCE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('TOP_SEC', '天融信', 1, null, 1, 'INFORMATION_SOURCE_VENDOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('QI_AN_XIN', '奇安信', 1, null, 1, 'INFORMATION_SOURCE_VENDOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('request_config', '{"expire":10,"expireUnit":"y","requestInterval":7,"requestIntervalUnit":"d"}', 1, null, 1, 'INTELLIGENCE_INTERFACE_CONFIG', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('0', '非恶意', 1, '', 1, 'INTELLIGENCE_ISSPITE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('1', '恶意', 1, '', 1, 'INTELLIGENCE_ISSPITE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('2', '未知', 1, '', 1, 'INTELLIGENCE_ISSPITE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('CERT_INSTITUTE', 'CERT', 1, null, 1, 'INFORMATION_SOURCE_VENDOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('IDSS_SDC', 'SDC', 1, null, 1, 'INFORMATION_SOURCE_VENDOR', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('credible_conf', '[{"attrCode":"banSuggest","attrName":"封禁建议","credibleOrder":["THREAT_V43","TOP_SEC","QI_AN_XIN","CERT_INSTITUTE"]},{"attrCode":"ipLocation","attrName":"IP地理位置","credibleOrder":["IDSS"]},{"attrCode":"ipCarrier","attrName":"IP运营商","credibleOrder":["IDSS"]}]', 1, null, 1, 'INTELLIGENCE_CREDIBLE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('黑灰产 ', '["色情网站","赌博网站","勒索软件","黑客工具","黑灰产","钓鱼","广告程序","劫持"]', 1, '', 1, 'THREAT_TYPE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('木马病毒 ', '["僵尸网络","木马","恶意软件","病毒","蠕虫病毒","C2"]', 1, '', 2, 'THREAT_TYPE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('网络攻击 ', '["漏洞利用","DDos","网络攻击","暴力破解"]', 1, '', 3, 'THREAT_TYPE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('有害内容 ', '["垃圾邮件","挖矿","恶意网站","恶意软件下载链接","重保"]', 1, '', 4, 'THREAT_TYPE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('可疑行为 ', '["扫描器","爬虫","代理","DGA域名"]', 1, '', 5, 'THREAT_TYPE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('APT ', '["APT"]', 1, '', 6, 'THREAT_TYPE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('失陷设备 ', '["失陷主机","傀儡机"]', 1, '', 7, 'THREAT_TYPE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('白名单 ', '["白名单"]', 1, '', 8, 'THREAT_TYPE', '');
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('其他 ', '["其他","待定","基础信息"]', 1, '', 9, 'THREAT_TYPE', '');

delete from ums_sys_application;
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('数据采集', 'insight', null, '1', null, '对采集数据源的配置和数据解析、转换、装载等处理过程，实现对数据的采集存储。', null, 'etl.png', null, null, null, '10', 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('数据治理', 'data-lake-governance', null, '1', null, '对安全数据从创建到消亡的全过程的监控和治理，实现数据的统一管理。', null, 'gov.png', null, null, null, '20', 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('数据共享', 'data', null, '1', null, '通过数据共享结合数据集市，实现基于租户的使用需求，向租户共享数据。', null, 'share.png', null, null, null, '30', 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('数据存储计算', 'model', null, '0', null, '数澜-数据存储计算。', null, 'report.png', null, null, null, '80', 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('数据开发', 'data-development-outer', null, '0', null, '提供集成组件调用、代码编写功能一体化开发服务套件的集成开发环境。', null, 'dev.png', 'http://10.250.108.181:8000/data-development-outer/data-development', null, null, '50', 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('运维中心', 'operation-center', null, '0', null, '数澜-运维中心。', null, 'gate.png', 'http://10.250.108.181:8000/data-development-outer/operation-center', null, null, '90', 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('数据资产', 'data-assets', null, '0', null, '数澜-数据资产。', null, 'etl.png', 'http://10.250.108.181:8000/data-development-outer/data-assets', null, null, '95', 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('项目管理', 'project-manager', null, '0', null, '数澜-项目管理。', null, 'gov.png', 'http://10.250.108.181:8000/data-development-outer/project-manager', null, null, '99', 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('数据开发(自研)', 'data-development-inner', null, '1', null, '提供集成组件调用、代码编写功能一体化开发服务套件的集成开发环境。', null, 'dev.png', null, null, null, '60', 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('应用', 'base', null, '0', null, '1', null, null, null, null, null, null, 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('数据集市', 'data-mart', null, '1', null, '通过数据集市实现对已经纳管的数据的内容的展示和数据对外的订阅。', null, 'market.png', null, null, null, '40', 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('AI数据分析', 'ai-data-analysis', null, '1', null, '提供数据丰富的数据预处理、 数据分析与数据挖掘组件,帮助快速建立数据挖掘工程，提升数据处理的效能。', null, 'analysis.png', null, null, null, '70', 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('威胁情报', 'high-value-analysis', null, '1', null, '威胁情报', null, 'report.png', null, null, null, '80', 0);
INSERT INTO ums_sys_application (name, code, type, is_active, sso, app_desc, is_edit, app_release, app_path, app_owner, create_time, version, in_iframe) VALUES ('高价值分析', 'high-value-analysis-plus', null, '0', null, '高价值分析', null, 'report.png', null, null, null, '80', 0);

delete from ums_sys_menus;
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '模型调度', 'scheduler-configuration', null, '0', '0', 'model', '1', 50, 50, '模型调度', '1', 'model', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '任务流管理', 'definition-list', null, '0', '0', 'scheduler-configuration', '1', 1, 1, '任务流管理', '1', 'scheduler-configuration', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '运行实例', 'instance-list', null, '0', '0', 'scheduler-configuration', '1', 20, 20, '运行实例', '1', 'scheduler-configuration', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '模型文件管理', 'fileManagement-list', null, '0', '0', 'scheduler-configuration', '1', 30, 30, '模型文件管理', '1', 'scheduler-configuration', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '数据存储计算', 'model', null, '0', '0', null, '1', 60, 60, '数据存储计算', '1', null, null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '任务流图', 'definition-tree', null, '1', '0', 'definition-list', '1', 1, 1, '任务流图', '1', 'definition-list', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '创建任务流', 'definition-create', null, '1', '0', 'definition-list', '1', 20, 20, '创建任务流', '1', 'definition-list', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '任务流配置', 'definition-flow-detail', null, '1', '0', 'definition-list', '1', 30, 30, '任务流配置', '1', 'definition-list', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '定时管理', 'definition-timing', null, '1', '0', 'definition-list', '1', 40, 40, '定时管理', '1', 'definition-list', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '任务进展', 'instance-gantt', null, '1', '0', 'instance-list', '1', 1, 1, '任务进展', '1', 'instance-list', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '任务流配置', 'instance-flow-detail', null, '1', '0', 'instance-list', '1', 20, 20, '任务流配置', '1', 'instance-list', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '新建文件', 'fileManagement-create-file', null, '1', '0', 'fileManagement-list', '1', 1, 1, '新建文件', '1', 'fileManagement-list', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '编辑文件', 'fileManagement-file-edit', null, '1', '0', 'fileManagement-list', '1', 20, 20, '编辑文件', '1', 'fileManagement-list', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '查看文件', 'fileManagement-file-detail', null, '1', '0', 'fileManagement-list', '1', 30, 30, '查看文件', '1', 'fileManagement-list', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据源配置管理', 'db-source-config', null, '0', '0', 'cluster-resource-manage', '1', 40, 40, '数据源配置管理', '1', 'cluster-resource-manage', null, null, null, null, 'model', null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, 'EasyGo', 'easygo', '', '0', '0', 'system', '1', 80, 80, 'EasyGo', '1', 'system', null, null, null, null, 'system', null, 'application', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '系统管理', 'system', null, '0', '0', 'base', '1', 30, 30, '系统管理', '1', 'base', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '应用', 'base', null, '0', '0', null, '1', 10, 10, '应用', '1', null, null, null, null, null, 'base', null, 'base', '1', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '资源管理', 'cluster-resource-manage', null, '0', '0', 'base', '1', 20, 20, '资源管理', '1', 'base', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '集群管理', 'cluster-manage', null, '0', '0', 'cluster-resource-manage', '1', 1, 80, '集群管理', '1', 'cluster-resource-manage', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '系统日志', 'sys-log', null, '0', '0', 'system', '1', 50, 50, '系统日志', '1', 'system', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '其它配置', 'system-config-logo', null, '0', '0', 'config-manage', '1', 1, 83, '其它配置', '1', 'config-manage', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '菜单管理', 'menu-manage', null, '0', '0', 'account-right', '1', 1, 84, '菜单管理', '1', 'account-right', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '账号管理', 'system-account', null, '0', '0', 'account-right', '1', 1, 84, '账号管理', '1', 'account-right', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '角色管理', 'role', null, '0', '0', 'account-right', '1', 1, 84, '角色管理', '1', 'account-right', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '操作审计', 'operation-audit', null, '0', '0', 'sys-log', '1', 1, 1, '操作审计', '1', 'sys-log', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '数据共享', 'data', null, '0', '0', null, '1', 50, 50, '数据共享', '1', null, null, null, null, null, 'data', null, 'data', '1', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '安全日志检索', 'dataExplore', null, '0', '0', 'data', '1', 1, 1, '安全日志检索', '1', 'data', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '安全备份服务', 'resource-manage', null, '0', '0', 'data', '1', 20, 20, '安全备份服务', '1', 'data', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'Mysql备份', 'config-info', null, '0', '0', 'resource-manage', '1', 40, 40, 'Mysql备份', '1', 'resource-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '文件服务配置', 'file-service', null, '0', '0', 'resource-manage', '1', 50, 50, '文件服务配置', '1', 'resource-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '自定义模型', 'dataMart', null, '0', '0', 'data', '1', 40, 40, '自定义模型', '1', 'data', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据集管理', 'dataset', null, '0', '0', 'dataMart', '1', 20, 20, '数据集管理', '1', 'dataMart', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '自定义模型脚本', 'model-explore', null, '0', '0', 'dataMart', '1', 1, 1, '自定义模型脚本', '1', 'dataMart', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '分析报表', 'report-manage', null, '0', '0', 'dataMart', '1', 30, 30, '分析报表', '1', 'dataMart', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '结构化数据', 'struct-data', null, '0', '0', 'data', '1', 30, 30, '结构化数据', '1', 'data', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据源管理', 'zeppelin-interpreter', null, '0', '0', 'struct-data', '1', 10, 10, '数据源管理', '1', 'struct-data', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'notebook', 'zeppelin-notebook', null, '0', '0', 'struct-data', '1', 20, 20, 'notebook', '1', 'struct-data', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '解释器查询结果', 'zeppelin-paragraph', null, '0', '0', 'struct-data', '1', 30, 30, '解释器查询结果', '1', 'struct-data', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, 'API管理', 'manage-api', '', '0', '0', 'data', '1', 50, 50, 'API管理', '1', 'data', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'API配置', 'manage-data-api', '', '0', '0', 'manage-api', '1', 10, 10, 'API配置', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '生成API', 'manage-data-api-create', '', '0', '0', 'manage-api', '1', 20, 20, '生成API', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '生成API-模式选择', 'manage-data-new-api', '', '0', '0', 'manage-api', '1', 30, 30, '生成API-模式选择', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '注册API', 'manage-data-api-register', '', '0', '0', 'manage-api', '1', 40, 40, '注册API', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'API授权', 'client-manage', '', '0', '0', 'manage-api', '1', 50, 50, 'API授权', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据源管理', 'source-manage', '', '0', '0', 'manage-api', '1', 60, 60, '数据源管理', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '项目管理', 'project-manage', '', '0', '0', 'manage-api', '1', 70, 70, '项目管理', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '安全组', 'security-group', '', '0', '0', 'manage-api', '1', 80, 80, '安全组', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '数据开发', 'data-development-inner', null, '0', '0', null, '1', 50, 50, '数据开发', '1', null, null, null, null, null, 'data-development-inner', null, 'data-development-inner', '1', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '开发概览', 'develop-overview', null, '0', '0', 'data-development-inner', '1', 1, 1, '开发概览', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '项目管理', 'project-list', null, '0', '0', 'data-development-inner', '1', 2, 2, '项目管理', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '资源管理', 'resource-center', null, '0', '0', 'data-development-inner', '1', 3, 3, '资源管理', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '任务监控', 'task-monitor', null, '0', '0', 'data-development-inner', '1', 4, 4, '任务监控', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '数据源管理', 'datasource-manage', null, '0', '0', 'data-development-inner', '1', 5, 5, '数据源管理', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '文件管理', 'file-manage', null, '0', '0', 'resource-center', '1', 3, 3, '文件管理', '1', 'resource-center', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '资源管理', 'resource', null, '0', '0', 'resource-center', '1', 4, 4, '资源管理', '1', 'resource-center', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '函数管理', 'function-manage', null, '0', '0', 'resource-center', '1', 5, 5, '函数管理', '1', 'resource-center', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '任务组配置', 'task-group-option', null, '0', '0', 'resource-center', '1', 6, 6, '任务组配置', '1', 'resource-center', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '任务组队列', 'task-group-queue', null, '0', '0', 'resource-center', '1', 7, 7, '任务组队列', '1', 'resource-center', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '主机监控', 'master-monitor', null, '0', '0', 'task-monitor', '1', 4, 4, 'Master', '1', 'task-monitor', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '工作组监控', 'worker-monitor', null, '0', '0', 'task-monitor', '1', 5, 5, 'Worker', '1', 'task-monitor', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据库监控', 'db-monitor', null, '0', '0', 'task-monitor', '1', 6, 6, 'DB', '1', 'task-monitor', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '监控统计', 'statistics-monitor', null, '0', '0', 'task-monitor', '1', 7, 7, 'Statistics', '1', 'task-monitor', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '审计日志', 'audit-log', null, '0', '0', 'task-monitor', '1', 8, 8, '审计日志', '1', 'task-monitor', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '数据治理', 'data-lake-governance', null, '0', '0', null, '1', 40, 40, '数据治理', '1', null, null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '1', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '元数据', 'data-lake-governance-meta', null, '0', '0', 'data-lake-governance', '1', 10, 10, '元数据管理', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '元数据采集', 'data-lake-governance-meta-access', null, '0', '0', 'data-lake-governance-meta', '1', 10, 10, '元数据采集', '1', 'data-lake-governance-meta', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '元数据维护', 'data-lake-governance-meta-search', null, '0', '0', 'data-lake-governance-meta', '1', 20, 20, '元数据查询', '1', 'data-lake-governance-meta', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '数据标准', 'data-lake-governance-standard-manage', null, '0', '0', 'data-lake-governance', '1', 20, 20, '数据标准', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '数据质量管理', 'data-lake-governance-quality', null, '0', '0', 'data-lake-governance', '1', 30, 30, '数据质量管理', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据源管理', 'data-lake-governance-quality-model', null, '0', '0', 'data-lake-governance-quality', '1', 50, 50, '稽核数据源管理', '1', 'data-lake-governance-quality', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '模型管理', 'data-lake-governance-quality-rule', null, '0', '0', 'data-lake-governance-quality', '1', 30, 30, '模型管理', '1', 'data-lake-governance-quality', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '质量任务', 'data-lake-governance-quality-task', null, '0', '0', 'data-lake-governance-quality', '1', 20, 20, '任务管理', '1', 'data-lake-governance-quality', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 5, '质量监测结果', 'data-lake-governance-quality-result', null, '0', '0', 'data-lake-governance-quality-task', '1', 40, 40, '质量监测结果', '1', 'data-lake-governance-quality-task', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '主数据管理', 'data-lake-governance-masterData', null, '0', '0', 'data-lake-governance', '1', 40, 40, '主数据管理', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '主数据模型', 'data-lake-governance-masterData-model', null, '0', '0', 'data-lake-governance-masterData', '1', 10, 10, '主数据模型', '1', 'data-lake-governance-masterData', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '主数据维护', 'data-lake-governance-masterData-maintain', null, '0', '0', 'data-lake-governance-masterData-model', '1', 20, 20, '主数据维护', '1', 'data-lake-governance-masterData-model', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '配置管理', 'data-lake-governance-config', null, '0', '0', 'data-lake-governance', '1', 60, 60, '配置管理', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '业务数据类型分组', 'data-lake-governance-config-grouping', null, '0', '0', 'data-lake-governance-config', '1', 10, 10, '业务数据类型分组', '1', 'data-lake-governance-config', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '主数据类型', 'data-lake-governance-config-data', null, '0', '0', 'data-lake-governance-masterData', '1', 30, 30, '主数据类型', '1', 'data-lake-governance-masterData', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '安全数据管理', 'safe-data-manage', null, '0', '0', 'data-lake-governance', '1', 20, 20, '安全数据管理', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '资产目录', 'asset-catalog', null, '0', '0', 'safe-data-manage', '1', 10, 10, '资产目录', '1', 'safe-data-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '资产用途', 'data-lake-governance-asset-use', null, '0', '0', 'data-lake-governance-config', '1', 30, 30, '资产用途', '1', 'data-lake-governance-config', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '敏感分级', 'data-lake-governance-sen-level', null, '0', '0', 'data-lake-governance-config', '1', 30, 30, '敏感分级', '1', 'data-lake-governance-config', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '敏感分类', 'data-lake-governance-sen-classification', null, '0', '0', 'data-lake-governance-config', '1', 30, 30, '敏感分类', '1', 'data-lake-governance-config', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '安全数据资产汇总', 'asset-collect', null, '0', '0', 'safe-data-manage', '1', 10, 10, '安全数据资产汇总', '1', 'safe-data-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据业务类型', 'data-lake-governance-config-business', null, '0', '0', 'data-lake-governance-config', '1', 20, 20, '数据业务类型', '1', 'data-lake-governance-config', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '数据采集', 'insight', null, '0', '0', null, '1', 20, 20, '数据采集', '1', null, null, null, null, null, 'insight', null, 'insight', '1', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '数据采集管理', 'dataAccessManagement', null, '0', '0', 'insight', '1', 1, 1, '数据采集管理', '1', 'insight', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '数据转换', 'dataConversion', null, '1', '0', 'dataAccessManagement', '1', 20, 20, '数据转换', '1', 'dataAccessManagement', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '输出配置页面', 'complete', null, '1', '0', 'dataAccessManagement', '1', 30, 30, '输出配置页面', '1', 'dataAccessManagement', null, null, 'sysadmin', '2021-12-01 18:13:50.0', 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '采集节点管理', 'manage-logmodule', null, '0', '0', 'insight', '1', 30, 30, '采集节点管理', '1', 'insight', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '解析规则管理', 'manage-parsing-rules', null, '0', '0', 'data-input-config', '1', 30, 30, '解析规则管理', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '解析规则详情', 'manage-parsing-rules-detail', null, '1', '0', 'manage-parsing-rules', '1', 1, 1, '解析规则详情', '1', 'manage-parsing-rules', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '新建解析规则', 'manage-parsing-rules-create', null, '1', '0', 'manage-parsing-rules', '1', 20, 20, '新建解析规则', '1', 'manage-parsing-rules', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '解析规则配置', 'manage-parsing-rules-edit', null, '1', '0', 'manage-parsing-rules', '1', 30, 30, '解析规则配置', '1', 'manage-parsing-rules', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '动态补全配置管理', 'manage-dynamic-completion', null, '0', '0', 'data-input-config', '1', 40, 40, '动态补全配置管理', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '采集脚本', 'manage-collection-script', null, '0', '0', 'data-input-config', '1', 50, 50, '采集脚本', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '脚本详情', 'manage-collection-script-detail', null, '1', '0', 'manage-collection-script', '1', 1, 1, '脚本详情', '1', 'manage-collection-script', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '新增脚本', 'manage-collection-script-create', null, '1', '0', 'manage-collection-script', '1', 20, 20, '新增脚本', '1', 'manage-collection-script', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '编辑脚本', 'manage-collection-script-edit', null, '1', '0', 'manage-collection-script', '1', 30, 30, '编辑脚本', '1', 'manage-collection-script', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '宽表字典', 'ueba-dictionary', null, '0', '0', 'data-input-config', '1', 60, 60, '宽表字典', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '场景视图', 'ueba-view', null, '0', '0', 'data-input-config', '1', 70, 70, '场景视图', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '源设备分类管理', 'source-device', null, '0', '0', 'data-input-config', '1', 80, 80, '源设备分类管理', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '视图详情', 'ueba-view-detail', null, '1', '0', 'ueba-view', '1', 1, 1, '视图详情', '1', 'ueba-view', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '采集监控', 'collect-control', null, '0', '0', 'insight', '1', 20, 20, '采集监控', '1', 'insight', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '采集配置', 'data-input-config', null, '0', '0', 'insight', '1', 40, 40, '采集配置', '1', 'insight', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '数据采集配置', 'manage-data-input', null, '1', '0', 'dataAccessManagement', '1', 30, 30, '数据采集配置', '1', 'dataAccessManagement', null, null, 'sysadmin', '2021-12-01 18:13:50.0', 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'Kafka详情', 'kafka-detail', null, '0', '0', 'cluster-manage', '1', 1, 80, 'Kafka详情', '1', 'cluster-manage', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'MySQL详情', 'mysql-detail', null, '0', '0', 'cluster-manage', '1', 1, 80, 'MySQL详情', '1', 'cluster-manage', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'HDFS详情', 'hdfs-detail', null, '0', '0', 'cluster-manage', '1', 1, 80, 'HDFS详情', '1', 'cluster-manage', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '数仓详情', 'clickhouse-detail', null, '0', '0', 'cluster-manage', '1', 1, 80, '数仓详情', '1', 'cluster-manage', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'ES详情', 'elasticsearch-detail', null, '0', '0', 'cluster-manage', '1', 1, 80, 'ES详情', '1', 'cluster-manage', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数仓配置', 'database-config', null, '0', '0', 'data-input-config', '1', 90, 90, '数仓配置', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '组织管理', 'organization', null, '0', '0', 'account-right', '1', 1, 85, '组织管理', '1', 'account-right', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '数据集市', 'data-mart', null, '0', '0', null, '1', 11, 11, '数据集市', '1', null, null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '数据集市地图', 'data-mart-map', null, '0', '0', 'data-mart', '1', 11, 11, '数据集市地图', '1', 'data-mart', null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据集市地图详情', 'data-mart-map-detail', null, '0', '0', 'data-mart-map', '1', 11, 11, '数据集市地图详情', '1', 'data-mart-map', null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '数据发布', 'data-mart-data-manage', null, '0', '0', 'data-mart', '1', 11, 11, '数据发布', '1', 'data-mart', null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据集市库详情', 'data-mart-data-database', null, '0', '0', 'data-mart-data-manage', '1', 11, 11, '数据集市库详情', '1', 'data-mart-data-manage', null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '数据集市表详情', 'data-mart-database-table', null, '0', '0', 'data-mart-data-database', '1', 11, 11, '数据集市表详情', '1', 'data-mart-data-database', null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据集市表详情', 'data-mart-data-table', null, '0', '0', 'data-mart-data-manage', '1', 11, 11, '数据集市表详情', '1', 'data-mart-database-table', null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '集市数据索引详情', 'data-mart-data-index', null, '0', '0', 'data-mart-data-manage', '1', 11, 11, '集市数据索引详情', '1', 'data-mart-data-manage', null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '分类分组管理', 'data-mart-type-group-manage', null, '0', '0', 'data-mart', '1', 11, 11, '分类分组管理', '1', 'data-mart', null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '集市分类管理', 'data-mart-type-manage', null, '0', '0', 'data-mart-type-group-manage', '1', 11, 11, '集市分类管理', '1', 'data-mart-type-group-manage', null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '集市分组管理', 'data-mart-group-manage', null, '0', '0', 'data-mart-type-group-manage', '1', 11, 11, '集市分组管理', '1', 'data-mart-type-group-manage', null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '密码策略', 'password-strategy', null, '0', '0', 'account-right', '1', 55, 55, '密码策略', '1', 'account-right', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '基础字典', 'base-dictionary', null, '0', '0', 'system', '1', 40, 40, '基础字典', '1', 'system', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'API市场', 'market-api', '', '0', '0', 'manage-api', '1', 90, 90, 'API市场', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '我的API', 'my-api', '', '0', '0', 'manage-api', '1', 100, 100, '我的API', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'API审核', 'check-api', '', '0', '0', 'manage-api', '1', 110, 110, 'API审核', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '融合建模', 'fuse-modeling', null, '0', '0', 'data-development-inner', '1', 6, 6, '融合建模', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '向导式建模-新建', 'guide-fuse-modeling-add', null, '0', '1', 'guide-fuse-modeling', '1', 1, 1, '向导式建模-新建', '1', 'guide-fuse-modeling', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '向导式建模-编辑', 'guide-fuse-modeling-edit', null, '0', '1', 'guide-fuse-modeling', '1', 2, 2, '向导式建模-编辑', '1', 'guide-fuse-modeling', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '向导式建模-查看', 'guide-fuse-modeling-examine', null, '0', '1', 'guide-fuse-modeling', '1', 3, 3, '向导式建模-查看', '1', 'guide-fuse-modeling', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '编排式建模', 'arrange-fuse-modeling', null, '0', '0', 'fuse-modeling', '1', 2, 2, '编排式建模', '1', 'fuse-modeling', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '向导式建模', 'guide-fuse-modeling', null, '0', '0', 'fuse-modeling', '1', 1, 1, '向导式建模', '1', 'fuse-modeling', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '特征引擎', 'CUBEManager', null, '0', '0', 'dataMart', '1', 40, 40, '特征引擎', '1', 'dataMart', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '新增特征引擎', 'cube-create', null, '0', '0', 'CUBEManager', '1', 10, 10, '新增特征引擎', '1', 'CUBEManager', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '申诉管理', 'appeal-manage', null, '0', '0', 'appeal', '1', 56, 56, '申诉管理', '1', 'appeal', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '我的申诉', 'appeal-self', null, '0', '0', 'appeal', '1', 57, 57, '我的申诉', '1', 'appeal', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '漏报监控', 'underreporting-monitor', null, '0', '0', 'insight', '1', 41, 41, '漏报监控', '1', 'insight', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '漏报详情', 'underreporting-monitor-detail', null, '0', '0', 'underreporting-monitor', '1', 41, 41, '漏报详情', '1', 'underreporting-monitor', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, 'AI数据分析', 'ai-data-analysis', null, '0', '0', null, '1', 100, 100, 'AI数据分析', '1', null, null, null, null, null, 'ai-data-analysis', null, 'ai-data-analysis', '1', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '我的工程', 'ai-self-project', null, '1', '0', 'ai-data-analysis', '1', 10, 10, '我的工程', '1', 'ai-data-analysis', null, null, 'sysadmin', '2021-12-01 18:13:50.0', 'ai-data-analysis', null, 'ai-data-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '数据源管理', 'ai-home', null, '1', '0', 'ai-data-analysis', '1', 20, 20, '数据源管理', '1', 'ai-data-analysis', null, null, 'sysadmin', '2021-12-01 18:13:50.0', 'ai-data-analysis', null, 'ai-data-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '系统算法', 'ai-algorithm', null, '1', '0', 'ai-data-analysis', '1', 30, 30, '系统算法', '1', 'ai-data-analysis', null, null, 'sysadmin', '2021-12-01 18:13:50.0', 'ai-data-analysis', null, 'ai-data-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '账号权限', 'account-right', null, '0', '0', 'system', '1', 1, 1, '账号权限', '1', 'system', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '申诉管理', 'appeal', null, '0', '0', 'system', '1', 20, 20, '申诉管理', '1', 'system', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '配置管理', 'config-manage', null, '0', '0', 'system', '1', 30, 30, '配置管理', '1', 'system', null, null, null, null, 'base', null, 'base', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '用户认证', 'user-auth-config', null, '0', '0', 'config-manage', '1', 110, 110, '用户认证', '1', 'config-manage', null, null, null, null, 'base', null, 'base', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '用户认证配置', 'user-auth-detail', null, '0', '0', 'user-auth-config', '1', 1, 1, '用户认证配置', '1', 'user-auth-config', null, null, null, null, 'base', null, 'base', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '首页', 'network-sdc-portal', null, '0', '0', 'base', '1', 10, 10, '首页', '1', 'base', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '数据建模', 'province-data-model', null, '0', '0', 'data-lake-governance', '1', 70, 70, '数据建模', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '模型概览', 'province-model-overview', null, '0', '0', 'province-data-model', '1', 10, 10, '模型概览', '1', 'province-data-model', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '业务板块', 'business-sector', null, '0', '0', 'province-data-model', '1', 20, 20, '业务板块', '1', 'province-data-model', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '维度', 'logic-table', null, '0', '0', 'province-data-model', '1', 30, 30, '维度', '1', 'province-data-model', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '业务过程', 'business-process', null, '0', '0', 'province-data-model', '1', 40, 40, '业务过程"', '1', 'province-data-model', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '模型目录', 'model-catalog', null, '0', '0', 'province-data-model', '1', 50, 50, '模型目录"', '1', 'province-data-model', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '导出逻辑表', 'export-logic-table', null, '0', '1', 'province-data-model', '1', 60, 60, '导出逻辑表"', '1', 'province-data-model', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '导入逻辑表', 'import-logic-table', null, '0', '1', 'province-data-model', '1', 70, 70, '导入逻辑表"', '1', 'province-data-model', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '逻辑表详情', 'logic-table-detail', null, '0', '1', 'province-data-model', '1', 80, 80, '逻辑表详情"', '1', 'province-data-model', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据标准管理', 'layout-standard-manage', null, '0', '0', 'data-lake-governance-standard-manage', '1', 1, 10, '数据标准', '1', 'data-lake-governance-standard-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '发布申请', 'data-lake-governance-release-application', null, '0', '0', 'data-lake-governance-standard-manage', '1', 1, 20, '发布申请', '1', 'data-lake-governance-standard-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '发布审批', 'data-lake-governance-release-approval', null, '0', '0', 'data-lake-governance-standard-manage', '1', 1, 30, '发布审批', '1', 'data-lake-governance-standard-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '版本恢复', 'data-lake-governance-release-recovery', null, '0', '0', 'data-lake-governance-standard-manage', '1', 1, 40, '版本恢复', '1', 'data-lake-governance-standard-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '集市统计概览', 'data-mart-home', null, '0', '0', 'data-mart', '1', 1, 1, '集市统计概览', '1', null, null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '我的订阅', 'subscription-self', null, '0', '0', 'data-mart', '1', 3, 3, '我的订阅', '1', null, null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'API首页', 'data-api-home', '', '0', '0', 'manage-api', '1', 0, 0, 'API首页', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '逻辑表审批', 'table-audit', null, '0', '0', 'province-data-model', '1', 60, 60, '逻辑表审批', '1', 'province-data-model', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '平台告警', 'notice', null, '0', '0', 'base', '1', 20, 20, '平台告警', '1', 'base', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '平台告警', 'system-notice', null, '0', '0', 'notice', '1', 10, 10, '平台告警', '1', 'notice', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '告警规则管理', 'warn-platform-rule', null, '0', '0', 'notice', '1', 20, 20, '告警规则管理', '1', 'notice', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '告警规则配置', 'warn-platform-rule-create', null, '0', '0', 'warn-platform-rule', '1', 10, 10, '告警规则配置', '1', 'warn-platform-rule', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '告警配置', 'warn-config', null, '0', '0', 'notice', '1', 30, 30, '告警配置', '1', 'notice', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '脚本管理', 'shell-manage', null, '0', '0', 'warn-config', '1', 10, 10, '脚本管理', '1', 'warn-config', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '新建脚本', 'operate-shell-create', null, '0', '0', 'shell-manage', '1', 10, 10, '新建脚本', '1', 'shell-manage', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '编辑脚本', 'operate-shell-edit', null, '0', '0', 'shell-manage', '1', 20, 20, '编辑脚本', '1', 'shell-manage', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '查看脚本', 'operate-shell-detail', null, '0', '0', 'shell-manage', '1', 30, 30, '查看脚本', '1', 'shell-manage', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '邮箱配置', 'email-manage', null, '0', '0', 'warn-config', '1', 20, 20, '邮箱配置', '1', 'warn-config', null, null, null, null, 'base', null, 'base', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '水印规则', 'data-share-watermark', '', '0', '0', 'manage-api', '1', 120, 120, '水印规则', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '水印溯源', 'data-share-traceability', '', '0', '0', 'manage-api', '1', 130, 130, '水印溯源', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '共享脱敏结果', 'desensitization-api-detail', '', '0', '1', 'manage-api', '1', 150, 150, '共享脱敏结果', '1', 'manage-api', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '数据加密管理', 'data-lake-governance-encryption-manage', null, '0', '0', 'data-lake-governance', '1', 70, 70, '数据加密管理', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '加密任务', 'data-lake-governance-encryption-task', null, '0', '0', 'data-lake-governance-encryption-manage', '1', 10, 10, '加密任务', '1', 'data-lake-governance-encryption-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '加密详情', 'encryption-task-detail', null, '0', '1', 'data-lake-governance-encryption-manage', '1', 20, 20, '加密详情', '1', 'data-lake-governance-encryption-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '秘钥管理', 'data-lake-governance-private-key-manage', null, '0', '0', 'data-lake-governance-encryption-manage', '1', 30, 30, '秘钥管理', '1', 'data-lake-governance-encryption-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '秘钥备份管理', 'data-lake-governance-private-key-backup-manage', null, '0', '0', 'data-lake-governance-encryption-manage', '1', 40, 40, '秘钥备份管理', '1', 'data-lake-governance-encryption-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '数据源质量详情', 'datasouce-quality-detail', '', '0', '0', 'data-lake-governance-quality-model', '1', 10, 10, '数据源质量详情', '1', 'data-lake-governance-quality-model', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '质量概览', 'quality-overview', '', '0', '0', 'data-lake-governance-quality', '1', 60, 60, '质量告警概览', '1', 'data-lake-governance-quality', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '安全中心', 'calc-service-manage', null, '0', '0', 'data-development-inner', '1', 60, 6, '安全中心', '1', 'data-development-inner', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '用户管理', 'security-user-manage', null, '0', '0', 'calc-service-manage', '1', 10, 6, '用户管理', '1', 'calc-service-manage', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '告警组管理', 'security-alarm-group-manage', null, '0', '0', 'calc-service-manage', '1', 20, 6, '告警组管理', '1', 'calc-service-manage', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '告警实例管理', 'security-alarm-instance-manage', null, '0', '0', 'calc-service-manage', '1', 30, 6, '告警实例管理', '1', 'calc-service-manage', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'Worker分组管理', 'security-worker-group-manage', null, '0', '0', 'calc-service-manage', '1', 40, 6, 'Worker分组管理', '1', 'calc-service-manage', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'Yarn队列管理', 'security-yarn-queue-manage', null, '0', '0', 'calc-service-manage', '1', 50, 6, 'Yarn队列管理', '1', 'calc-service-manage', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '环境管理', 'security-environment-manage', null, '0', '0', 'calc-service-manage', '1', 60, 6, '环境管理', '1', 'calc-service-manage', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '集群管理', 'security-cluster-manage', null, '0', '0', 'calc-service-manage', '1', 70, 6, '集群管理', '1', 'calc-service-manage', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'K8S命名空间管理', 'security-k8s-namespace-manage', null, '0', '0', 'calc-service-manage', '1', 80, 6, 'K8S命名空间管理', '1', 'calc-service-manage', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '令牌管理', 'security-token-manage', null, '0', '0', 'calc-service-manage', '1', 90, 6, '令牌管理', '1', 'calc-service-manage', null, null, null, null, 'data-development-inner', null, 'data-development-inner', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '数据字典', 'dict', null, '0', '0', 'data-lake-governance', '1', 80, 80, '数据字典', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '字典概览', 'dict-overview', null, '0', '0', 'dict', '1', 1, 10, '字典概览', '1', 'dict', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '全部数据', 'dict-search', null, '0', '0', 'dict', '1', 1, 20, '全部数据', '1', 'dict', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '字典目录', 'dict-dir', null, '0', '0', 'dict', '1', 1, 30, '字典目录', '1', 'dict', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '字典目录详情', 'dict-dir-detail', null, '0', '1', 'dict-dir', '1', 1, 40, '字典目录详情', '1', 'dict-dir', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '基础信息', 'baseInfo', '', '0', '0', 'data-input-config', '1', 90, 90, '基础信息', '1', 'data-input-config', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '自然人信息管理', 'person', null, '0', '0', 'baseInfo', '1', 10, 10, '自然人信息管理', '1', 'baseInfo', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '账号信息管理', 'account', null, '0', '0', 'baseInfo', '1', 20, 20, '账号信息管理', '1', 'baseInfo', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '设备信息管理', 'base-device', null, '0', '0', 'baseInfo', '1', 30, 30, '设备信息管理', '1', 'baseInfo', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '网络域信息管理', 'network-device', null, '0', '0', 'baseInfo', '1', 40, 40, '网络域信息管理', '1', 'baseInfo', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '应用信息管理', 'application', null, '0', '0', 'baseInfo', '1', 50, 50, '应用信息管理', '1', 'baseInfo', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据信息管理', 'base-dataInfo', null, '0', '0', 'baseInfo', '1', 60, 60, '数据信息管理', '1', 'baseInfo', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'CH备份管理', 'ch-backup-manage', null, '0', '0', 'resource-manage', '1', 1, 1, '备份管理', '1', 'resource-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'CH备份', 'backup-manage', null, '0', '0', 'ch-backup-manage', '1', 1, 1, 'CH备份', '1', 'ch-backup-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'CH备份日志', 'backup-date-log', null, '0', '0', 'ch-backup-manage', '1', 2, 2, 'CH备份日志', '1', 'ch-backup-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'CH恢复', 'data-resume', null, '0', '0', 'ch-backup-manage', '1', 3, 3, 'CH恢复', '1', 'ch-backup-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'Mysql备份管理', 'mysql-backup-manage', null, '0', '0', 'resource-manage', '1', 1, 2, 'Mysql备份管理', '1', 'resource-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'Mysql备份', 'mysql-backup', null, '0', '0', 'mysql-backup-manage', '1', 1, 1, 'Mysql备份', '1', 'ch-backup-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'Mysql备份日志', 'mysql-backup-log', null, '0', '0', 'mysql-backup-manage', '1', 2, 2, 'Mysql备份日志', '1', 'ch-backup-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'Mysql恢复', 'mysql-resume-log', null, '0', '0', 'mysql-backup-manage', '1', 3, 3, 'Mysql恢复', '1', 'ch-backup-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'ES备份管理', 'es-backup-manage', null, '0', '0', 'resource-manage', '1', 3, 3, 'ES备份管理', '1', 'resource-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'ES备份', 'es-backup', null, '0', '0', 'es-backup-manage', '1', 1, 1, 'ES备份', '1', 'es-backup-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'ES备份日志', 'es-backup-log', null, '0', '0', 'es-backup-manage', '1', 2, 2, 'ES备份日志', '1', 'es-backup-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'ES恢复', 'es-resume-log', null, '0', '0', 'es-backup-manage', '1', 3, 3, 'ES恢复', '1', 'es-backup-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'Hive备份管理', 'hive-backup-manage', null, '0', '0', 'resource-manage', '1', 4, 4, 'Hive备份管理', '1', 'resource-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'Hive备份', 'hive-backup', null, '0', '0', 'hive-backup-manage', '1', 1, 1, 'Hive备份', '1', 'hive-backup-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'Hive备份日志', 'hive-backup-log', null, '0', '0', 'hive-backup-manage', '1', 2, 2, 'Hive备份日志', '1', 'hive-backup-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'Hive恢复', 'hive-resume-log', null, '0', '0', 'hive-backup-manage', '1', 3, 3, 'Hive恢复', '1', 'hive-backup-manage', null, null, null, null, 'data', null, 'data', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '威胁情报', 'high-value-analysis', null, '0', '0', null, '1', 80, 80, '高价值分析', '1', null, null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '情报查询', 'analysis-intelligence-search', null, '0', '0', 'high-value-analysis', '1', 10, 10, '情报查询', '1', 'high-value-analysis', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '情报管理', 'operation-intelligence', null, '0', '0', 'high-value-analysis', '1', 30, 30, '情报管理', '1', 'high-value-analysis', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'IP情报列表', 'operation-intelligence-ip', null, '0', '0', 'operation-intelligence', '1', 10, 10, 'IP情报列表', '1', 'operation-intelligence', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'IP情报新增', 'operation-intelligence-ip-add', null, '0', '0', 'operation-intelligence-ip', '1', 10, 10, 'IP情报新增', '1', 'operation-intelligence-ip', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'IP情报编辑', 'operation-intelligence-ip-edit', null, '0', '0', 'operation-intelligence-ip', '1', 20, 20, 'IP情报编辑', '1', 'operation-intelligence-ip', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'IP情报详情', 'operation-intelligence-ip-detail', null, '0', '0', 'operation-intelligence-ip', '1', 30, 30, 'IP情报详情', '1', 'operation-intelligence-ip', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '导入历史', 'operation-intelligence-ip-inHistory', null, '0', '0', 'operation-intelligence-ip', '1', 40, 40, '导入历史', '1', 'operation-intelligence-ip', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '域名情报列表', 'operation-intelligence-domain', null, '0', '0', 'operation-intelligence', '1', 20, 20, '域名情报列表', '1', 'operation-intelligence', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '域名情报新增', 'operation-intelligence-domain-add', null, '0', '0', 'operation-intelligence-domain', '1', 10, 10, '域名情报新增', '1', 'operation-intelligence-domain', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '域名情报编辑', 'operation-intelligence-domain-edit', null, '0', '0', 'operation-intelligence-domain', '1', 20, 20, '域名情报编辑', '1', 'operation-intelligence-domain', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '导入历史', 'operation-intelligence-domain-inHistory', null, '0', '0', 'operation-intelligence-domain', '1', 30, 30, '导入历史', '1', 'operation-intelligence-domain', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '域名情报详情', 'operation-intelligence-domain-detail', null, '0', '0', 'operation-intelligence-domain', '1', 40, 40, '域名情报详情', '1', 'operation-intelligence-domain', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'URL情报列表', 'operation-intelligence-url', null, '0', '0', 'operation-intelligence', '1', 30, 30, 'URL情报列表', '1', 'operation-intelligence', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'URL情报新增', 'operation-intelligence-url-add', null, '0', '0', 'operation-intelligence-url', '1', 10, 10, 'URL情报新增', '1', 'operation-intelligence-url', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'URL情报编辑', 'operation-intelligence-url-edit', null, '0', '0', 'operation-intelligence-url', '1', 20, 20, 'URL情报编辑', '1', 'operation-intelligence-url', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '导入历史', 'operation-intelligence-url-inHistory', null, '0', '0', 'operation-intelligence-url', '1', 30, 30, '导入历史', '1', 'operation-intelligence-url', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, 'URL情报详情', 'operation-intelligence-url-detail', null, '0', '0', 'operation-intelligence-url', '1', 40, 40, 'URL情报详情', '1', 'operation-intelligence-url', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '文件情报列表', 'operation-intelligence-file', null, '0', '0', 'operation-intelligence', '1', 40, 40, '文件情报列表', '1', 'operation-intelligence', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '文件情报新增', 'operation-intelligence-file-add', null, '0', '0', 'operation-intelligence-file', '1', 10, 10, '文件情报新增', '1', 'operation-intelligence-file', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '文件情报编辑', 'operation-intelligence-file-edit', null, '0', '0', 'operation-intelligence-file', '1', 20, 20, '文件情报编辑', '1', 'operation-intelligence-file', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '导入历史', 'operation-intelligence-file-inHistory', null, '0', '0', 'operation-intelligence-file', '1', 30, 30, '导入历史', '1', 'operation-intelligence-file', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '文件情报详情', 'operation-intelligence-file-detail', null, '0', '0', 'operation-intelligence-file', '1', 40, 40, '文件情报详情', '1', 'operation-intelligence-file', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '情报接口配置', 'operation-intelligence-interface', null, '0', '0', 'operation-intelligence', '1', 50, 50, '情报接口配置', '1', 'operation-intelligence', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '接口编辑', 'operation-intelligence-interface-edit', null, '0', '0', 'operation-intelligence-interface', '1', 10, 10, '接口编辑', '1', 'operation-intelligence-interface', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '特征管理', 'library-layout', null, '0', '0', 'high-value-analysis-plus', '1', 60, 60, '特征管理', '1', 'high-value-analysis-plus', null, null, null, null, 'high-value-analysis-plus', null, 'high-value-analysis-plus', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '特征信息', 'library-info', null, '0', '0', 'library-layout', '1', 60, 60, '特征信息', '1', 'library-layout', null, null, null, null, 'high-value-analysis-plus', null, 'high-value-analysis-plus', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '高价值分析', 'high-value-analysis-plus', null, '0', '0', null, '1', 80, 80, '高价值分析', '1', null, null, null, null, null, 'high-value-analysis-plus', null, 'high-value-analysis-plus', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '脱敏规则', 'desensitization-rules', null, '0', '0', 'data-lake-governance-config', '1', 60, 60, '脱敏规则', '1', 'data-lake-governance-config', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'Clickhouse', 'data-lake-governance-meta-search-ch', null, '0', '0', 'data-lake-governance-meta-search', '1', 10, 10, 'Clickhouse', '1', 'data-lake-governance-meta-search', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'Mysql', 'data-lake-governance-meta-search-mysql', null, '0', '0', 'data-lake-governance-meta-search', '1', 20, 20, 'Mysql', '1', 'data-lake-governance-meta-search', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'Elasticsearch', 'data-lake-governance-meta-search-es', null, '0', '0', 'data-lake-governance-meta-search', '1', 30, 30, 'Elasticsearch', '1', 'data-lake-governance-meta-search', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'Hive', 'data-lake-governance-meta-search-hive', null, '0', '0', 'data-lake-governance-meta-search', '1', 40, 40, 'Hive', '1', 'data-lake-governance-meta-search', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '审批管理', 'approve-layout', null, '0', '0', 'data-mart', '1', 55, 55, '审批管理', '1', null, null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '订阅审批', 'subscription-manage', null, '0', '0', 'approve-layout', '1', 40, 40, '订阅审批', '1', null, null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '发布审批', 'data-mart-approve-manage', null, '0', '0', 'approve-layout', '1', 60, 60, '发布审批', '1', null, null, null, null, null, 'data-mart', null, 'data-mart', null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '数据开发', 'data-development-outer', null, '0', '0', null, '1', 50, 50, '数据开发', '1', null, null, null, null, null, 'data-development-outer', null, 'data-development-outer', '1', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '运维中心', 'operation-center', null, '0', '0', null, '1', 60, 60, '运维中心', '1', null, null, null, null, null, 'operation-center', null, 'operation-center', '1', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '数据资产', 'data-assets', null, '0', '0', null, '1', 70, 70, '数据资产', '1', null, null, null, null, null, 'data-assets', null, 'data-assets', '1', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '项目管理', 'project-manager', null, '0', '0', null, '1', 80, 80, '项目管理', '1', null, null, null, null, null, 'project-manager', null, 'project-manager', '1', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '数据标签', 'tag-management', null, '0', '0', 'data-lake-governance', '1', 80, 80, '数据标签', '1', 'data-lake-governance', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '标签类型管理', 'tag-type-management', null, '0', '0', 'tag-management', '1', 1, 40, '标签类型管理', '1', 'tag-management', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '标签值管理', 'tag-value-management', null, '0', '1', 'tag-management', '1', 1, 40, '标签值管理', '1', 'tag-management', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '表指标项查询概览', 'dict-indicator-items', null, '0', '0', 'dict', '1', 30, 30, '表指标项查询概览', '1', 'dict', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '指标项明细', 'dict-indicator-items-detail', null, '0', '1', 'dict-indicator-items', '1', 10, 10, '指标项明细', '1', 'dict-indicator-items', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '情报概览', 'operation-intelligence-overview', null, '0', '0', 'operation-intelligence', '1', 1, 1, '情报概览', '1', 'operation-intelligence', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '情报API授权管理', 'operation-intelligence-app', null, '0', '0', 'operation-intelligence', '1', 55, 55, '情报API授权管理', '1', 'operation-intelligence', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '情报API授权新增', 'operation-intelligence-app-add', null, '0', '0', 'operation-intelligence-app', '1', 1, 1, '情报API授权新增', '1', 'operation-intelligence-app', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '情报API授权编辑', 'operation-intelligence-app-edit', null, '0', '0', 'operation-intelligence-app', '1', 5, 5, '情报API授权修改', '1', 'operation-intelligence-app', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '情报API授权删除', 'operation-intelligence-app-delete', null, '0', '0', 'operation-intelligence-app', '1', 10, 10, '情报API授权删除', '1', 'operation-intelligence-app', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '邮件情报列表', 'operation-intelligence-email', null, '0', '0', 'operation-intelligence', '1', 45, 45, '邮件情报列表', '1', 'operation-intelligence', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '邮件情报新增', 'operation-intelligence-email-add', null, '0', '0', 'operation-intelligence-email', '1', 1, 1, '邮件情报新增', '1', 'operation-intelligence-email', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '邮件情报编辑', 'operation-intelligence-email-edit', null, '0', '0', 'operation-intelligence-email', '1', 20, 20, '邮件情报编辑', '1', 'operation-intelligence-email', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '邮件情报详情', 'operation-intelligence-email-detail', null, '0', '0', 'operation-intelligence-email', '1', 30, 30, '邮件情报详情', '1', 'operation-intelligence-email', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '导入历史', 'operation-intelligence-email-inHistory', null, '0', '0', 'operation-intelligence-email', '1', 40, 40, '导入历史', '1', 'operation-intelligence-email', null, null, null, null, null, null, 'argus', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '威胁情报搜索', 'analysis-intelligence-primary', null, '0', '0', 'analysis-intelligence-search', '1', 10, 10, '威胁情报查询', '1', 'analysis-intelligence-search', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '情报详情', 'analysis-intelligence-search-detail', null, '0', '1', 'analysis-intelligence-primary', '1', 10, 10, '情报详情', '1', 'analysis-intelligence-primary', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '情报聚合', 'analysis-intelligence-primary-detail-agg', null, '0', '0', 'analysis-intelligence-primary', '1', 20, 20, '情报聚合', '1', 'analysis-intelligence-primary', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '1', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '高级情报查询', 'analysis-intelligence-senior', null, '0', '0', 'analysis-intelligence-search', '1', 20, 20, '高级情报查询', '1', 'analysis-intelligence-search', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '上传文件', 'analysis-intelligence-senior-upload', null, '0', '0', 'analysis-intelligence-senior', '1', 10, 10, '上传文件', '1', 'analysis-intelligence-senior', null, null, null, null, null, null, 'argus', '0', '1', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '明细报表', 'analysis-intelligence-senior-report', null, '0', '0', 'analysis-intelligence-senior', '1', 20, 20, '明细报表', '1', 'analysis-intelligence-senior', null, null, null, null, null, null, 'argus', '0', '1', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '情报详情', 'analysis-intelligence-senior-detail', null, '0', '1', 'analysis-intelligence-senior', '1', 30, 30, '情报详情', '1', 'analysis-intelligence-senior', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '情报聚合', 'analysis-intelligence-senior-detail-agg', null, '0', '0', 'analysis-intelligence-senior', '1', 40, 40, '情报聚合', '1', 'analysis-intelligence-senior', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '1', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'IP地址查询', 'ip-search', null, '0', '0', 'analysis-intelligence-search', '1', 30, 30, ' IP地址查询', '1', 'analysis-intelligence-search', null, null, null, null, 'high-value-analysis', null, 'high-value-analysis', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '质量分析', 'layout-quality-report', null, '0', '0', 'data-lake-governance-quality', '1', 10, 10, '质量报告', '1', 'data-lake-governance-quality', null, null, 'test', '2024-08-27 22:31:27.0', 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据元标准', 'data-lake-governance-meta-standard', null, '0', '0', 'layout-standard-manage', '1', 20, 20, '数据元标准', '1', 'layout-standard-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '告警信息', 'quality-system-notice', null, '0', '0', 'layout-quality-report', '1', 20, 20, '告警信息', '1', 'layout-quality-report', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '告警规则管理', 'quality-warn-platform-rule', null, '0', '0', 'layout-quality-report', '1', 30, 30, '告警规则管理', '1', 'layout-quality-report', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '告警规则创建', 'quality-warn-platform-rule-create', null, '0', '0', 'quality-warn-platform-rule', '1', 10, 10, '告警规则创建', '1', 'quality-warn-platform-rule', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '质量报表', 'quality-report', null, '0', '0', 'layout-quality-report', '1', 10, 10, '质量报告', '1', 'layout-quality-report', null, null, 'test', '2024-08-27 22:31:39.0', 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '数据字典', 'standard-dict', null, '0', '0', 'layout-standard-manage', '1', 30, 30, '数据字典', '1', 'layout-standard-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '基础数据标准', 'data-lake-governance-standard', null, '0', '0', 'layout-standard-manage', '1', 10, 10, '基础数据标准', '1', 'layout-standard-manage', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '质量模版', 'quality-template', null, '0', '0', 'data-lake-governance-quality', '1', 20, 20, '质量模版', '1', 'data-lake-governance-quality', null, null, null, null, 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '模板管理', 'built-in-custom-template', '', '0', '0', 'quality-template', '1', 1, 10, '模板管理', '1', 'quality-template', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 4, '动态阈值', 'dynamic-threshold', '', '0', '0', 'quality-template', '1', 1, 10, '动态阈值', '1', 'quality-template', 'sysadmin', '2022-03-22 00:01:15.0', 'sysadmin', '2022-03-22 00:01:15.0', 'data-lake-governance', null, 'data-lake-governance', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '宽表字典集', 'etl-large-data-manage', null, '0', '0', 'data-input-config', '1', 45, 45, '宽表字典集', '1', 'data-input-config', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '应用列表管理', 'insight-application-list', null, '0', '0', 'insight', '1', 50, 50, '应用列表管理', '1', 'insight', null, null, null, null, 'insight', null, 'insight', '0', '0', 0);

delete from ums_sys_role;
INSERT INTO ums_sys_role (role_id, role_name, role_describe, builtin, del_flag, create_user, create_date, update_user, update_date, is_admin, role_resource_search_limit, user_resource_search_limit) VALUES ('140d6cf17d244e74855ecdd56c40335c', '系统管理员', '', false, false, 'Admin', 1719478187661, '系统管理员', 1719478187661, '0', 0, 0);

delete from ums_sys_work_time;
INSERT INTO ums_sys_work_time (on_time, out_time) VALUES ('08:00', '18:00');
