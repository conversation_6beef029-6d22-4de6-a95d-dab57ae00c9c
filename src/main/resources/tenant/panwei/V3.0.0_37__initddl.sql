delete from tb_filter where module_code = 'soarAlarmETL' and code = 'createName';
delete from tb_filter where module_code = 'soarAlarmETL' and code = 'dataSource';
delete from tb_filter where module_code = 'soarAlarmETL' and code = 'name';
delete from tb_filter where module_code = 'soarAlarmEtL' and code = 'soarAlarmEtL';
delete from tb_filter where module_code = 'soarAlarmETL' and code = 'sourceType';
INSERT INTO tb_filter ( module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, is_page_default, sort_no) VALUES ('soarAlarmETL', '告警管理告警接入配置', 'createName', '创建人', 'soarAlarmETL', '{"filterType":"select","filterColumn":"create_user","filterCondition":"like","affectTable":"soar_etl_config","valueType":"column","createUser":""}', '1', 1, 1, '0', NULL, NULL);
INSERT INTO tb_filter ( module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, is_page_default, sort_no) VALUES ('soarAlarmETL', '告警管理告警接入配置', 'dataSource', '数据源', 'soarAlarmETL', '{"filterType":"input","filterColumn":"data_source","filterCondition":"like","affectTable":"soar_etl_config","valueType":"column","createUser":""}', '1', 1, 1, '0', NULL, NULL);
INSERT INTO tb_filter ( module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, is_page_default, sort_no) VALUES ( 'soarAlarmETL', '告警管理告警接入配置', 'name', '名称', 'soarAlarmETL', '{"filterType":"input","filterColumn":"name","filterCondition":"like","affectTable":"soar_etl_config","valueType":"column","createUser":""}', '1', 1, 1, '0', NULL, NULL);
INSERT INTO tb_filter (module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, is_page_default, sort_no) VALUES ('soarAlarmEtL', '告警管理告警接入配置', 'soarAlarmEtL', '告警管理告警接入配置', NULL, NULL, '1', 1, 1, '0', NULL, NULL);
INSERT INTO tb_filter (module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, is_page_default, sort_no) VALUES ('soarAlarmETL', '告警管理告警接入配置', 'sourceType', '接入方式', 'soarAlarmETL', '{"filterType":"select","filterColumn":"source_type","filterCondition":"like","affectTable":"soar_etl_config","valueType":"column","createUser":""}', '1', 1, 1, '0', NULL, NULL);