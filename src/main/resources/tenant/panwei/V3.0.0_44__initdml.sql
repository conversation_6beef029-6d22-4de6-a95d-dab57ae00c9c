CREATE TABLE download_record
(
    id          bigserial PRIMARY KEY,
    file_name   varchar(1024)                  ,
    file_size   varchar(32)                    ,
    status      varchar(2)                     ,
    start_time  timestamp                      ,
    end_time    timestamp                      ,
    schedule    varchar(32)                    ,
    module      varchar(64)                    ,
    file_path   varchar(512)                   ,
    log_path    varchar(512)                   ,
    create_user varchar(64) DEFAULT 'sysadmin' NOT NULL
);

COMMENT ON TABLE download_record IS '下载记录表';
COMMENT ON COLUMN download_record.file_name IS '文件名';
COMMENT ON COLUMN download_record.file_size IS '文件大小';
COMMENT ON COLUMN download_record.status IS '文件生成状态，0-未开始；1-生成中；2-成功；3-失败';
COMMENT ON COLUMN download_record.start_time IS '文件创建时间';
COMMENT ON COLUMN download_record.end_time IS '完成时间';
COMMENT ON COLUMN download_record.schedule IS '完成进度';
COMMENT ON COLUMN download_record.module IS '下载模块';
COMMENT ON COLUMN download_record.file_path IS '文件路径';
COMMENT ON COLUMN download_record.log_path IS '日志文件路径';
COMMENT ON COLUMN download_record.create_user IS '创建人';