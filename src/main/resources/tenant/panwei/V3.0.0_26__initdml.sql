DELETE FROM tb_filter WHERE module_code='intelligenceIp';
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'intelligenceIp', 'IP情报库', NULL, '', '1', 1, NULL, '0', NULL);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'ipAddr', 'IP', 'intelligenceIp', '{"filterType":"input","filterColumn":"ip_addr","filterCondition":"like","affectTable":"ti_ip","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 1);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'threatType', '威胁类型', 'intelligenceIp', '{"filterType":"select","filterColumn":"threat_type","filterCondition":"in","affectTable":"ti_ip","valueType":"column","valueSource":"select key_code as code,value as name from ueba_dictionary where type = ''THREAT_TYPE''"}', '1', 1, NULL, '1', 2);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'tagsType', '标签类型', 'intelligenceIp', '{"filterType":"input","filterColumn":"tags_type","filterCondition":"like","affectTable":"ti_ip","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 3);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'carrier', '运营商', 'intelligenceIp', '{"filterType":"input","filterColumn":"carrier","filterCondition":"like","affectTable":"ti_ip","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 4);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'updateTime', '更新时间', 'intelligenceIp', '{"filterType":"date","filterColumn":"update_time","filterCondition":"between","affectTable":"ti_ip","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 5);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'source', '情报来源', 'intelligenceIp', '{"filterType":"select","filterColumn":"source","filterCondition":"in","affectTable":"ti_ip","valueType":"column","valueSource":"select key_code as code,value as name from ueba_dictionary where type = ''INTELLIGENCE_SOURCE''"}', '1', 1, NULL, '1', 6);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'country', '国家', 'intelligenceIp', '{"filterType":"input","filterColumn":"country","filterCondition":"like","affectTable":"ti_ip","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 7);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'province', '省', 'intelligenceIp', '{"filterType":"input","filterColumn":"province","filterCondition":"like","affectTable":"ti_ip","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 8);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'city', '市', 'intelligenceIp', '{"filterType":"input","filterColumn":"city","filterCondition":"like","affectTable":"ti_ip","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 9);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'tag', '标签', 'intelligenceIp', '{"filterType":"input","filterColumn":"tag","filterCondition":"like","affectTable":"ti_ip","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 10);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'informationSourceVendor', '情报来源厂商', 'intelligenceIp', '{"filterType":"input","filterColumn":"information_source_vendor","filterCondition":"like","affectTable":"ti_relevant_information","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 11);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'confidence', '可信度评分', 'intelligenceIp', '{"filterType":"interval","filterColumn":"confidence","filterCondition":"between","affectTable":"ti_relevant_information","valueType":"column","valueSource":"select key_code as code,value as name from ueba_dictionary where type = ''CONFIDENCE_INTERVAL''"}', '1', 1, NULL, '0', 12);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'asnNumber', 'ASN号码', 'intelligenceIp', '{"filterType":"input","filterColumn":"asn_number","filterCondition":"like","affectTable":"ti_ip_asn","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 13);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceIp', 'IP情报库', 'fileHash', '文件hash', 'intelligenceIp', '{"filterType":"input","filterColumn":"file_hash","filterCondition":"like","affectTable":"ti_relevant_simple","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 14);



DELETE FROM tb_filter WHERE module_code='intelligenceDomain';
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'intelligenceDomain', '域名情报库', NULL, '', '1', 1, NULL, '0', NULL);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'domain', '域名', 'intelligenceDomain', '{"filterType":"input","filterColumn":"domain","filterCondition":"like","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 1);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'threatType', '威胁类型', 'intelligenceDomain', '{"filterType":"select","filterColumn":"threat_type","filterCondition":"in","affectTable":"ti_domain","valueType":"column","valueSource":"select key_code as code,value as name from ueba_dictionary where type = ''THREAT_TYPE''"}', '1', 1, NULL, '1', 2);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'tagsType', '标签类型', 'intelligenceDomain', '{"filterType":"input","filterColumn":"tags_type","filterCondition":"like","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 3);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'registrarName', '域名服务商', 'intelligenceDomain', '{"filterType":"input","filterColumn":"registrar_name","filterCondition":"like","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 4);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'updateTime', '更新时间', 'intelligenceDomain', '{"filterType":"date","filterColumn":"update_time","filterCondition":"between","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 5);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'source', '情报来源', 'intelligenceDomain', '{"filterType":"select","filterColumn":"source","filterCondition":"in","affectTable":"ti_domain","valueType":"column","valueSource":"select key_code as code,value as name from ueba_dictionary where type = ''INTELLIGENCE_SOURCE''"}', '1', 1, NULL, '1', 6);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'tag', '标签', 'intelligenceDomain', '{"filterType":"input","filterColumn":"tag","filterCondition":"like","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 7);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'informationSourceVendor', '情报来源厂商', 'intelligenceDomain', '{"filterType":"input","filterColumn":"information_source_vendor","filterCondition":"like","affectTable":"ti_relevant_information","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 8);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'confidence', '可信度评分', 'intelligenceDomain', '{"filterType":"interval","filterColumn":"confidence","filterCondition":"between","affectTable":"ti_relevant_information","valueType":"column","valueSource":"select key_code as code,value as name from ueba_dictionary where type = ''CONFIDENCE_INTERVAL''"}', '1', 1, NULL, '0', 9);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'fileHash', '文件hash', 'intelligenceDomain', '{"filterType":"input","filterColumn":"file_hash","filterCondition":"like","affectTable":"ti_relevant_simple","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 10);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'ipAddr', 'IP', 'intelligenceDomain', '{"filterType":"input","filterColumn":"ip_addr","filterCondition":"like","affectTable":"ti_relevant_ip","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 11);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'nameServer', '域名服务器', 'intelligenceDomain', '{"filterType":"input","filterColumn":"name_server","filterCondition":"like","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 12);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'registrantEmail', '注册邮箱', 'intelligenceDomain', '{"filterType":"input","filterColumn":"registrant_email","filterCondition":"like","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 13);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'registrantName', '注册人', 'intelligenceDomain', '{"filterType":"input","filterColumn":"registrant_name","filterCondition":"like","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 14);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'registrantCompany', '注册机构', 'intelligenceDomain', '{"filterType":"input","filterColumn":"registrant_company","filterCondition":"like","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 15);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'registrantCdate', '注册时间', 'intelligenceDomain', '{"filterType":"date","filterColumn":"registrant_cdate","filterCondition":"between","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 16);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'registrantEdate', '过期时间', 'intelligenceDomain', '{"filterType":"date","filterColumn":"registrant_edate","filterCondition":"between","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 17);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceDomain', '域名情报库', 'alexaRank', 'Alexa排名', 'intelligenceDomain', '{"filterType":"interval","filterColumn":"alexa_rank","filterCondition":"between","affectTable":"ti_domain","valueType":"column","valueSource":"select key_code as code,value as name from ueba_dictionary where type = ''ALEXARANK_INTERVAL''"}', '1', 1, NULL, '0', 18);

DELETE FROM  tb_filter WHERE module_code='intelligenceUrl';
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceUrl', 'URL情报库', 'intelligenceUrl', 'URL情报库', NULL, '', '1', 1, NULL, '0', NULL);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceUrl', 'URL情报库', 'url', 'URL', 'intelligenceUrl', '{"filterType":"input","filterColumn":"url","filterCondition":"like","affectTable":"ti_url","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 1);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceUrl', 'URL情报库', 'threatType', '威胁类型', 'intelligenceUrl', '{"filterType":"select","filterColumn":"threat_type","filterCondition":"in","affectTable":"ti_url","valueType":"column","valueSource":"select key_code as code,value as name from ueba_dictionary where type = ''THREAT_TYPE''"}', '1', 1, NULL, '1', 2);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceUrl', 'URL情报库', 'tagsType', '标签类型', 'intelligenceUrl', '{"filterType":"input","filterColumn":"tags_type","filterCondition":"like","affectTable":"ti_url","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 3);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceUrl', 'URL情报库', 'updateTime', '更新时间', 'intelligenceUrl', '{"filterType":"date","filterColumn":"update_time","filterCondition":"between","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 4);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceUrl', 'URL情报库', 'source', '情报来源', 'intelligenceUrl', '{"filterType":"select","filterColumn":"source","filterCondition":"in","affectTable":"ti_url","valueType":"column","valueSource":"select key_code as code,value as name from ueba_dictionary where type = ''INTELLIGENCE_SOURCE''"}', '1', 1, NULL, '1', 5);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceUrl', 'URL情报库', 'createTime', '创建时间', 'intelligenceUrl', '{"filterType":"date","filterColumn":"create_time","filterCondition":"between","affectTable":"ti_domain","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 6);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceUrl', 'URL情报库', 'tag', '标签', 'intelligenceUrl', '{"filterType":"input","filterColumn":"tag","filterCondition":"like","affectTable":"ti_url","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 7);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceUrl', 'URL情报库', 'informationSourceVendor', '情报来源厂商', 'intelligenceUrl', '{"filterType":"input","filterColumn":"information_source_vendor","filterCondition":"like","affectTable":"ti_relevant_information","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 8);


DELETE FROM  tb_filter WHERE module_code='intelligenceFile';
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'intelligenceFile', '文件情报库', NULL, '', '1', 1, NULL, '0', NULL);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'fileName', '文件名称', 'intelligenceFile', '{"filterType":"input","filterColumn":"file_name","filterCondition":"like","affectTable":"ti_file","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 1);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'sampleSha256', '文件Hash', 'intelligenceFile', '{"filterType":"input","filterColumn":"sample_sha256","filterCondition":"like","affectTable":"ti_file","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 2);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'threatType', '威胁类型', 'intelligenceFile', '{"filterType":"select","filterColumn":"threat_type","filterCondition":"in","affectTable":"ti_file","valueType":"column","valueSource":"select key_code as code,value as name from ueba_dictionary where type = ''THREAT_TYPE''"}', '1', 1, NULL, '1', 3);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'tag', '标签', 'intelligenceFile', '{"filterType":"input","filterColumn":"tag","filterCondition":"like","affectTable":"ti_file","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 4);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'updateTime', '更新时间', 'intelligenceFile', '{"filterType":"date","filterColumn":"update_time","filterCondition":"between","affectTable":"ti_file","valueType":"column","valueSource":""}', '1', 1, NULL, '1', 5);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'source', '情报来源', 'intelligenceFile', '{"filterType":"select","filterColumn":"source","filterCondition":"in","affectTable":"ti_file","valueType":"column","valueSource":"select key_code as code,value as name from ueba_dictionary where type = ''INTELLIGENCE_SOURCE''"}', '1', 1, NULL, '1', 6);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'informationSourceVendor', '情报来源厂商', 'intelligenceFile', '{"filterType":"input","filterColumn":"information_source_vendor","filterCondition":"like","affectTable":"ti_relevant_information","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 7);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'sha1', '文件sha1', 'intelligenceFile', '{"filterType":"input","filterColumn":"sha1","filterCondition":"like","affectTable":"ti_file","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 8);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'md5', '文件md5', 'intelligenceFile', '{"filterType":"input","filterColumn":"md5","filterCondition":"like","affectTable":"ti_file","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 9);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'threatLevel', '威胁等级', 'intelligenceFile', '{"filterType":"select","filterColumn":"threat_level","filterCondition":"in","affectTable":"ti_file","valueType":"column","valueSource":"select key_code as code,value as name from ueba_dictionary where type = ''THREAT_LEVEL''"}', '1', 1, NULL, '0', 10);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'fileType', '文件类型', 'intelligenceFile', '{"filterType":"input","filterColumn":"file_type","filterCondition":"like","affectTable":"ti_file","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 11);
INSERT INTO tb_filter(module_code, module_name, code, name, parent_code, filter_config, flag, page_filter, agg_filter, is_default, sort_no) VALUES ('intelligenceFile', '文件情报库', 'fileExtType', '文件扩展类型', 'intelligenceFile', '{"filterType":"input","filterColumn":"file_ext_type","filterCondition":"like","affectTable":"ti_file","valueType":"column","valueSource":""}', '1', 1, NULL, '0', 12);
