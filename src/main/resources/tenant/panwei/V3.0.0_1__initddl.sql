DROP TABLE IF EXISTS ai_report;
DROP TABLE IF EXISTS ai_report_config;
DROP TABLE IF EXISTS ai_report_execute;
DROP TABLE IF EXISTS ai_report_group;
DROP TABLE IF EXISTS ai_report_history;
DROP TABLE IF EXISTS ai_report_send;
DROP TABLE IF EXISTS ai_user_report;
DROP TABLE IF EXISTS asset_net_district;
DROP TABLE IF EXISTS asset_safe_domain;
DROP TABLE IF EXISTS asset_standby_field_config;
DROP TABLE IF EXISTS backup_config;
DROP TABLE IF EXISTS backup_record;
DROP TABLE IF EXISTS backup_recover;
DROP TABLE IF EXISTS common_filter_base;
DROP TABLE IF EXISTS common_filter_condition;
DROP TABLE IF EXISTS common_show_field;
DROP TABLE IF EXISTS dataset_accelerate_config;
DROP TABLE IF EXISTS dataset_accelerate_log;
DROP TABLE IF EXISTS dataset_authority;
DROP TABLE IF EXISTS dataset_base;
DROP TABLE IF EXISTS dataset_category;
DROP TABLE IF EXISTS dataset_field;
DROP TABLE IF EXISTS dataset_gpl_filter_condition;
DROP TABLE IF EXISTS dataset_operation_log;
DROP TABLE IF EXISTS dataset_table_filter_condition;
DROP TABLE IF EXISTS early_warn_agg;
DROP TABLE IF EXISTS early_warn_info;
DROP TABLE IF EXISTS early_warn_opt_log;
DROP TABLE IF EXISTS etl_asset_resource;
DROP TABLE IF EXISTS etl_asset_summary;
DROP TABLE IF EXISTS etl_dictionary_set;
DROP TABLE IF EXISTS etl_dictionary_set_field;
DROP TABLE IF EXISTS etl_dictionary_set_source;
DROP TABLE IF EXISTS etl_logmoudle;
DROP TABLE IF EXISTS etl_parser;
DROP TABLE IF EXISTS etl_parser_function;
DROP TABLE IF EXISTS etl_parser_param;
DROP TABLE IF EXISTS etl_parser_param_tmp;
DROP TABLE IF EXISTS etl_parser_reg;
DROP TABLE IF EXISTS etl_parser_reg_tmp;
DROP TABLE IF EXISTS etl_parser_result;
DROP TABLE IF EXISTS etl_parser_rule_category;
DROP TABLE IF EXISTS etl_parser_template;
DROP TABLE IF EXISTS etl_reader_param;
DROP TABLE IF EXISTS etl_reader_param_define;
DROP TABLE IF EXISTS etl_shell;
DROP TABLE IF EXISTS etl_shell_opt_log;
DROP TABLE IF EXISTS etl_source;
DROP TABLE IF EXISTS etl_source_optlog;
DROP TABLE IF EXISTS etl_source_set;
DROP TABLE IF EXISTS etl_source_type;
DROP TABLE IF EXISTS etl_task_day_statistics;
DROP TABLE IF EXISTS etl_transform_action;
DROP TABLE IF EXISTS etl_transform_condition;
DROP TABLE IF EXISTS etl_transform_dict;
DROP TABLE IF EXISTS etl_transform_dict_data;
DROP TABLE IF EXISTS etl_transform_dict_field;
DROP TABLE IF EXISTS etl_transform_function;
DROP TABLE IF EXISTS etl_transform_rule;
DROP TABLE IF EXISTS etl_transform_table;
DROP TABLE IF EXISTS etl_wide_table_column;
DROP TABLE IF EXISTS etl_wide_table_column_view;
DROP TABLE IF EXISTS etl_wide_table_view;
DROP TABLE IF EXISTS etl_writer_ch_config;
DROP TABLE IF EXISTS etl_writer_config;
DROP TABLE IF EXISTS etl_writer_es_config;
DROP TABLE IF EXISTS etl_writer_kafka_config;
DROP TABLE IF EXISTS etl_writer_table_config;
DROP TABLE IF EXISTS etl_writer_table_define;
DROP TABLE IF EXISTS explore_query_condition;
DROP TABLE IF EXISTS explore_query_history;
DROP TABLE IF EXISTS explore_selected_field;
DROP TABLE IF EXISTS insight_alarm_email_content_temp;
DROP TABLE IF EXISTS insight_alarm_platform_action;
DROP TABLE IF EXISTS insight_alarm_platform_base;
DROP TABLE IF EXISTS insight_alarm_platform_record;
DROP TABLE IF EXISTS intranet_configuration;
DROP TABLE IF EXISTS model_action_session_base_info;
DROP TABLE IF EXISTS model_action_session_filter;
DROP TABLE IF EXISTS model_action_session_tasklog;
DROP TABLE IF EXISTS model_cube_base_info;
DROP TABLE IF EXISTS model_cube_cfg;
DROP TABLE IF EXISTS model_cube_filter;
DROP TABLE IF EXISTS model_cube_tasklog;
DROP TABLE IF EXISTS recover_record;
DROP TABLE IF EXISTS sys_db_version_upgrade;
DROP TABLE IF EXISTS sys_db_version_upgrade_detail;
DROP TABLE IF EXISTS sys_import_his;
DROP TABLE IF EXISTS system_backup_config;
DROP TABLE IF EXISTS system_backup_table;
DROP TABLE IF EXISTS system_clickhouse_config;
DROP TABLE IF EXISTS system_tables;
DROP TABLE IF EXISTS tb_filter;
DROP TABLE IF EXISTS ti_bad_score_config;
DROP TABLE IF EXISTS ti_batch_query_detail;
DROP TABLE IF EXISTS ti_batch_query_task;
DROP TABLE IF EXISTS ti_interface_conf;
DROP TABLE IF EXISTS ti_open_app_auth;
DROP TABLE IF EXISTS ti_search_his;
DROP TABLE IF EXISTS ueba_dictionary;
DROP TABLE IF EXISTS ums_create_table_record;
DROP TABLE IF EXISTS ums_sys_application;
DROP TABLE IF EXISTS ums_sys_auth_config;
DROP TABLE IF EXISTS ums_sys_calendar;
DROP TABLE IF EXISTS ums_sys_datasource_config;
DROP TABLE IF EXISTS ums_sys_holiday;
DROP TABLE IF EXISTS ums_sys_license;
DROP TABLE IF EXISTS ums_sys_log;
DROP TABLE IF EXISTS ums_sys_menus;
DROP TABLE IF EXISTS ums_sys_org;
DROP TABLE IF EXISTS ums_sys_org_dept;
DROP TABLE IF EXISTS ums_sys_password_policy;
DROP TABLE IF EXISTS ums_sys_role;
DROP TABLE IF EXISTS ums_sys_role_ch_es;
DROP TABLE IF EXISTS ums_sys_role_data;
DROP TABLE IF EXISTS ums_sys_role_extend;
DROP TABLE IF EXISTS ums_sys_role_function;
DROP TABLE IF EXISTS ums_sys_route_interface;
DROP TABLE IF EXISTS ums_sys_script;
DROP TABLE IF EXISTS ums_sys_script_opt_log;
DROP TABLE IF EXISTS ums_sys_user;
DROP TABLE IF EXISTS ums_sys_user_role;
DROP TABLE IF EXISTS ums_sys_work_time;


CREATE TABLE ai_report
(
	id SERIAL  PRIMARY KEY,
	report_name VARCHAR(255),
	report_time TIMESTAMP,
	execute_content TEXT,
	execute_content_type VARCHAR(10),
	execute_type VARCHAR(10),
	execute_config VARCHAR(50),
	execute_cron VARCHAR(512),
	current_execute_time TIMESTAMP,
	next_execute_time TIMESTAMP,
	send_type VARCHAR(20),
	send_config TEXT,
	send_format VARCHAR(255),
	report_result VARCHAR(20),
	status VARCHAR(255),
	create_time TIMESTAMP,
	create_user VARCHAR(255),
	update_time TIMESTAMP,
	update_user VARCHAR(255),
	flag INTEGER,
	planTime VARCHAR(255),
	second INTEGER,
	minute INTEGER,
	hour INTEGER,
	dayOfweek INTEGER,
	dayOfMonth INTEGER,
	monthOfYear INTEGER,
	split_char VARCHAR(20),
	dashboard_drag_node TEXT,
	drag_selected TEXT,
	widget_list TEXT,
	dashboard_url VARCHAR(4096),
	description VARCHAR(1024),
	has_header VARCHAR(1)
);

CREATE TABLE ai_report_config
(
	id SERIAL  PRIMARY KEY,
	report_id INTEGER,
	config TEXT,
	is_delete INTEGER,
	is_cycle INTEGER,
	cycle VARCHAR(50),
	export_type VARCHAR(50),
	create_id INTEGER,
	modify_id INTEGER,
	create_time TIMESTAMP,
	modify_time TIMESTAMP
);

CREATE TABLE ai_report_execute
(
	id SERIAL  PRIMARY KEY,
	report_id BIGINT,
	file_name VARCHAR(255),
	file_local_path VARCHAR(255),
	execute_time TIMESTAMP,
	execute_result VARCHAR(10),
	execute_result_desc VARCHAR(2000),
	create_time TIMESTAMP,
	create_user VARCHAR(255),
	update_time TIMESTAMP,
	update_user VARCHAR(255),
	flag VARCHAR(1),
	source_file_name VARCHAR(200),
	exception_file VARCHAR(256)
);

CREATE TABLE ai_report_group
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(50),
	parent_id INTEGER,
	project_id INTEGER,
	report_type INTEGER,
	is_delete INTEGER,
	create_id INTEGER,
	modify_id INTEGER,
	create_time TIMESTAMP,
	modify_time TIMESTAMP,
	sortno INTEGER
);

CREATE TABLE ai_report_history
(
	id SERIAL  PRIMARY KEY,
	report_id INTEGER,
	is_auto INTEGER,
	status INTEGER,
	start_time TIMESTAMP,
	end_time TIMESTAMP,
	path VARCHAR(255),
	export_type VARCHAR(20)
);

CREATE TABLE ai_report_send
(
	id SERIAL  PRIMARY KEY,
	report_id BIGINT,
	execute_id BIGINT,
	file_name VARCHAR(255),
	file_size BIGINT,
	send_type VARCHAR(255),
	send_time TIMESTAMP,
	send_result VARCHAR(255),
	send_result_desc VARCHAR(500),
	create_time TIMESTAMP,
	create_user VARCHAR(255),
	update_time TIMESTAMP,
	update_user VARCHAR(255),
	flag VARCHAR(1)
);

CREATE TABLE ai_user_report
(
	id SERIAL  PRIMARY KEY,
	report_id BIGINT,
	user_id BIGINT
);

CREATE TABLE asset_net_district
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(256),
	ip_location VARCHAR(256),
	ip_section VARCHAR(256),
	net_describe TEXT,
	create_time TIMESTAMP NOT NULL ,
	create_user VARCHAR(256),
	update_time TIMESTAMP,
	update_user VARCHAR(256),
	flag INTEGER DEFAULT 1
);

CREATE TABLE asset_safe_domain
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(255),
	physics_location VARCHAR(255),
	ip_type INTEGER,
	domain_type INTEGER,
	remark TEXT,
	member TEXT,
	safe_domain_type TEXT,
	origin_param TEXT,
	is_del SMALLINT,
	create_time TIMESTAMP,
	create_user VARCHAR(128),
	update_time TIMESTAMP,
	update_user VARCHAR(128)
);

CREATE TABLE asset_standby_field_config
(
	id SERIAL  PRIMARY KEY,
	config_table_name VARCHAR(64) DEFAULT '',
	standby_field_name VARCHAR(32) DEFAULT '',
	standby_field_type VARCHAR(32) NOT NULL ,
	standby_field_show_name VARCHAR(32) DEFAULT '',
	enable_status INTEGER,
	status INTEGER,
	create_user VARCHAR(16) DEFAULT '',
	update_user VARCHAR(16) DEFAULT '',
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE backup_config
(
	id SERIAL  PRIMARY KEY,
	type VARCHAR(20) DEFAULT 'ch',
	table_name VARCHAR(50),
	backup_type VARCHAR(50),
	backup_policy VARCHAR(50) NOT NULL ,
	increment_field VARCHAR(50),
	storage VARCHAR(50),
	storage_volume INTEGER,
	volume_unit VARCHAR(10),
	storage_day INTEGER,
	execute_period VARCHAR(10),
	execute_plan INTEGER,
	execute_time VARCHAR(20),
	execute_cron VARCHAR(512),
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP,
	flag VARCHAR(1) DEFAULT '1',
	status VARCHAR(1),
	ip VARCHAR(20),
	port VARCHAR(20),
	data_base VARCHAR(20),
	time_type VARCHAR(20),
	time_config VARCHAR(256),
	is_recover VARCHAR(2),
	task_name VARCHAR(256),
	backup_method VARCHAR(1),
	recover_config VARCHAR(1024),
	last_finish_time TIMESTAMP,
	backup_result VARCHAR(1)
);

CREATE TABLE backup_record
(
	id SERIAL  PRIMARY KEY,
	config_id BIGINT,
	name VARCHAR(50),
	type VARCHAR(50),
	backup_desc VARCHAR(255) NOT NULL ,
	backup_result VARCHAR(255) NOT NULL ,
	backup_result_desc VARCHAR(255),
	file_name VARCHAR(255),
	file_dir VARCHAR(255),
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	flag VARCHAR(1) DEFAULT '1',
	backup_id BIGINT,
	execute_result TEXT,
	backup_storage VARCHAR(255),
	recover_status VARCHAR(2) DEFAULT '0',
	log_file_path VARCHAR(255),
	log_file_name VARCHAR(255),
	total_rows BIGINT,
	total_capacity BIGINT,
	recover_bak_table VARCHAR(100),
	backup_type VARCHAR(64),
	backup_policy VARCHAR(64),
	storage VARCHAR(64),
	recover_rows BIGINT DEFAULT 0,
	recover_same VARCHAR(2) DEFAULT '0',
	md5_hashcode VARCHAR(128),
	sign VARCHAR(2056),
	recover_time TIMESTAMP,
	backup_start_time TIMESTAMP,
	backup_end_time TIMESTAMP,
	execute_start_time TIMESTAMP,
	execute_end_time TIMESTAMP
);

CREATE TABLE backup_recover
(
	id SERIAL  PRIMARY KEY,
	config_id INTEGER NOT NULL ,
	execute_type VARCHAR(100),
	execute_value VARCHAR(255),
	execute_cron VARCHAR(512),
	create_user VARCHAR(256),
	create_time TIMESTAMP NOT NULL ,
	update_user VARCHAR(256),
	update_time TIMESTAMP
);

CREATE TABLE common_filter_base
(
	id SERIAL  PRIMARY KEY,
	create_user VARCHAR(100) NOT NULL ,
	name VARCHAR(128) NOT NULL ,
	type VARCHAR(128),
	filter_desc VARCHAR(1024),
	create_time TIMESTAMP,
	update_user VARCHAR(100),
	update_time TIMESTAMP,
	last_run_time TIMESTAMP,
	overview VARCHAR(1)
);

CREATE TABLE common_filter_condition
(
	id SERIAL  PRIMARY KEY,
	filter_id BIGINT NOT NULL ,
	code VARCHAR(128) NOT NULL ,
	code_column VARCHAR(128),
	filter_mode INTEGER,
	start_value VARCHAR(1024),
	end_value VARCHAR(512),
	value_list VARCHAR(2048),
	operator VARCHAR(8),
	logic_operator VARCHAR(8),
	name VARCHAR(258)
);

CREATE TABLE common_show_field
(
	id SERIAL  PRIMARY KEY,
	fields_list TEXT NOT NULL ,
	type VARCHAR(50) NOT NULL ,
	"user" VARCHAR(50),
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	describes VARCHAR(50)
);

CREATE TABLE dataset_accelerate_config
(
	id SERIAL  PRIMARY KEY,
	dataset_id INTEGER NOT NULL ,
	exec_type VARCHAR(16) DEFAULT '',
	exec_value VARCHAR(256) DEFAULT '',
	exec_hour INTEGER,
	exec_minute INTEGER,
	table_name VARCHAR(32) DEFAULT '',
	order_field VARCHAR(32) DEFAULT '',
	cluster_name VARCHAR(64) DEFAULT '',
	out_datasource_type VARCHAR(20) DEFAULT 'clickhouse',
	is_accelerate INTEGER DEFAULT 0
);

CREATE TABLE dataset_accelerate_log
(
	id SERIAL  PRIMARY KEY,
	dataset_id INTEGER NOT NULL ,
	exec_status INTEGER NOT NULL ,
	exec_detail VARCHAR(512) DEFAULT '',
	start_time TIMESTAMP NOT NULL ,
	end_time TIMESTAMP NOT NULL ,
	count INTEGER
);

CREATE TABLE dataset_authority
(
	id SERIAL  PRIMARY KEY,
	dataset_id INTEGER NOT NULL ,
	user_name VARCHAR(16) DEFAULT ''
);

CREATE TABLE dataset_base
(
	id SERIAL  PRIMARY KEY,
	dataset_name VARCHAR(32) DEFAULT '',
	dataset_desc VARCHAR(256) DEFAULT '',
	is_accelerate INTEGER DEFAULT 0,
	from_source VARCHAR(16) DEFAULT '',
	status INTEGER DEFAULT 1,
	gpl_content TEXT,
	model TEXT,
	create_user VARCHAR(32) DEFAULT '',
	update_user VARCHAR(32) DEFAULT '',
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	category_id INTEGER,
	category_name VARCHAR(64),
	is_default VARCHAR(2) DEFAULT '0'
);

CREATE TABLE dataset_category
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(64),
	level INTEGER,
	operator VARCHAR(32),
	parent_id INTEGER,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE dataset_field
(
	id SERIAL  PRIMARY KEY,
	dataset_id INTEGER NOT NULL ,
	field_name VARCHAR(64) DEFAULT ''
);

CREATE TABLE dataset_gpl_filter_condition
(
	id SERIAL  PRIMARY KEY,
	dataset_id INTEGER NOT NULL ,
	dbquery_content TEXT,
	dbquery_fields TEXT,
	gpl_condition TEXT
);

CREATE TABLE dataset_operation_log
(
	id SERIAL  PRIMARY KEY,
	dataset_id INTEGER NOT NULL ,
	operation_type VARCHAR(16) DEFAULT '',
	operation_detail VARCHAR(128) DEFAULT '',
	operator VARCHAR(32) DEFAULT '',
	create_time TIMESTAMP
);

CREATE TABLE dataset_table_filter_condition
(
	id SERIAL  PRIMARY KEY,
	dataset_id INTEGER NOT NULL ,
	table_name VARCHAR(32) DEFAULT '',
	connector VARCHAR(16) DEFAULT '',
	where_sql VARCHAR(1024) DEFAULT '',
	selected_field TEXT,
	filter_condition TEXT,
	selected_field_type TEXT
);

CREATE TABLE early_warn_agg
(
	id SERIAL  PRIMARY KEY,
	agg_field VARCHAR(128),
	agg_name VARCHAR(128),
	dict_type VARCHAR(64),
	enum_type VARCHAR(512),
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP
);

CREATE TABLE early_warn_info
(
	id SERIAL  PRIMARY KEY,
	classification VARCHAR(64),
	warn_level VARCHAR(64),
	warn_name VARCHAR(128),
	warn_time TIMESTAMP,
	warn_target VARCHAR(64),
	department VARCHAR(64),
	warn_content VARCHAR(512),
	source_type VARCHAR(32),
	warn_source VARCHAR(32),
	warn_status VARCHAR(32),
	cve_id VARCHAR(64),
	bug_type VARCHAR(32),
	attack_type VARCHAR(32),
	warn_effect VARCHAR(512),
	warn_solution VARCHAR(512),
	warn_file_path VARCHAR(1024),
	device_ref VARCHAR(512),
	service_def VARCHAR(512),
	audit_submit_user VARCHAR(128),
	audit_submit_time TIMESTAMP,
	audit_submit_result VARCHAR(512),
	audit_submit_file_path VARCHAR(1024),
	audit_user VARCHAR(128),
	audit_time TIMESTAMP,
	audit_result VARCHAR(128),
	audit_desc VARCHAR(512),
	audit_file_path VARCHAR(1024),
	release_user VARCHAR(128),
	release_time TIMESTAMP,
	warn_flag VARCHAR(2),
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP,
	type VARCHAR(8),
	start_time VARCHAR(32),
	sync_id VARCHAR(64),
	doc_url VARCHAR(256)
);

CREATE TABLE early_warn_opt_log
(
	id SERIAL  PRIMARY KEY,
	early_warn_id BIGINT NOT NULL ,
	opt_user VARCHAR(128),
	opt_time TIMESTAMP,
	opt_type VARCHAR(128),
	opt_content VARCHAR(512),
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP
);

CREATE TABLE etl_asset_resource
(
	id SERIAL  PRIMARY KEY,
	etl_source_id BIGINT NOT NULL ,
	en_name VARCHAR(100),
	asset_purpose_id BIGINT,
	classification_id BIGINT,
	asset_level_id BIGINT,
	description VARCHAR(1000),
	create_time TIMESTAMP,
	create_user VARCHAR(255),
	update_time TIMESTAMP,
	update_user VARCHAR(255),
	del_flag INTEGER DEFAULT 0
);

CREATE TABLE etl_asset_summary
(
	id SERIAL  PRIMARY KEY,
	asset_total INTEGER,
	asset_purpose_json TEXT,
	device_type_json TEXT,
	data_total_all BIGINT,
	disk_all BIGINT,
	last_7days_data_total BIGINT,
	last_7days_disk BIGINT,
	asset_list_json TEXT,
	last_7days_asset_json TEXT,
	disk_total_list_json TEXT,
	del_flag INTEGER DEFAULT 0,
	create_time TIMESTAMP
);

CREATE TABLE etl_dictionary_set
(
	id SERIAL  PRIMARY KEY,
	dict_name VARCHAR(128),
	dict_desc VARCHAR(512),
	is_default VARCHAR(2) DEFAULT '0',
	dict_category VARCHAR(2) DEFAULT '0',
	copy_cnt INTEGER DEFAULT 0,
	delete_flag VARCHAR(2) DEFAULT '0',
	create_time TIMESTAMP,
	create_user VARCHAR(128),
	update_time TIMESTAMP,
	update_user VARCHAR(128)
);

CREATE TABLE etl_dictionary_set_field
(
	id SERIAL  PRIMARY KEY,
	dict_id BIGINT,
	field_name VARCHAR(128),
	field_type VARCHAR(64),
	field_desc VARCHAR(512),
	create_time TIMESTAMP,
	create_user VARCHAR(128),
	update_time TIMESTAMP,
	update_user VARCHAR(128)
);

CREATE TABLE etl_dictionary_set_source
(
	id SERIAL  PRIMARY KEY,
	dict_id BIGINT,
	source_id BIGINT NOT NULL ,
	create_time TIMESTAMP,
	create_user VARCHAR(128),
	update_time TIMESTAMP,
	update_user VARCHAR(128)
);

CREATE TABLE etl_logmoudle
(
	id SERIAL  PRIMARY KEY,
	work_id VARCHAR(64) NOT NULL ,
	work_ip VARCHAR(64) NOT NULL ,
	work_moudle VARCHAR(16) NOT NULL ,
	work_desc VARCHAR(512),
	status INTEGER DEFAULT 1,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP,
	open_port VARCHAR(32) DEFAULT '',
	resource_pool VARCHAR(128) DEFAULT '未知',
	pod VARCHAR(128) DEFAULT '未知',
	network_domain VARCHAR(128) DEFAULT '未知',
	host_type VARCHAR(128) DEFAULT '',
	manage_ip VARCHAR(128) DEFAULT '',
	carry_network_ip VARCHAR(128) DEFAULT '',
	vpc_network_segment VARCHAR(128) DEFAULT '',
    logmodule_tag VARCHAR(512)
);

CREATE TABLE etl_logmodule_tag
(
    id SERIAL  PRIMARY KEY,
    tag_name VARCHAR(512) NULL ,
    tag_desc VARCHAR(1024) NOT NULL ,
    create_time TIMESTAMP  ,
    create_user VARCHAR(128),
    update_time TIMESTAMP,
    update_user VARCHAR(128)
);
CREATE TABLE etl_logmodule_tag_relation
(
    id SERIAL  PRIMARY KEY,
    logmodule_id VARCHAR(64)  NULL ,
    tag_id VARCHAR(64) NOT NULL ,
    create_time TIMESTAMP  ,
    create_user VARCHAR(128),
    update_time TIMESTAMP,
    update_user VARCHAR(128)
);


CREATE TABLE etl_parser
(
	id SERIAL  PRIMARY KEY,
	template_id BIGINT NOT NULL ,
	source_id BIGINT NOT NULL ,
	new_create INTEGER NOT NULL ,
	keep_raw_data INTEGER DEFAULT 0,
	disable_record_errdata INTEGER DEFAULT 0,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP
);

CREATE TABLE etl_parser_function
(
	id SERIAL  PRIMARY KEY,
	function_name VARCHAR(128) NOT NULL ,
	function_code VARCHAR(64) NOT NULL ,
	desccription VARCHAR(256),
	short_name VARCHAR(64),
	icon VARCHAR(64),
	status INTEGER DEFAULT 1,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP
);

CREATE TABLE etl_parser_param
(
	id SERIAL  PRIMARY KEY,
	paraser_reg_id BIGINT NOT NULL ,
	source_id BIGINT,
	col_index VARCHAR(512),
	name VARCHAR(512),
	sample_value TEXT,
	format VARCHAR(32),
	type VARCHAR(16),
	status INTEGER DEFAULT 1,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP
);

CREATE TABLE etl_parser_param_tmp
(
	id SERIAL  PRIMARY KEY,
	paraser_reg_id VARCHAR(128),
	source_id BIGINT,
	col_index VARCHAR(512),
	name VARCHAR(512),
	sample_value TEXT,
	format VARCHAR(32),
	type VARCHAR(16),
	status INTEGER DEFAULT 1,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP
);

CREATE TABLE etl_parser_reg
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(128),
	template_id BIGINT,
	uuid VARCHAR(128),
	parser_type VARCHAR(64) NOT NULL ,
	function VARCHAR(64),
	sort_id INTEGER,
	split1 VARCHAR(10),
	split2 VARCHAR(10),
	reg_type VARCHAR(10),
	parent_uuid VARCHAR(128),
	parent_column VARCHAR(64),
	action_type VARCHAR(8),
	reg_value TEXT,
	sample_log TEXT,
	status INTEGER DEFAULT 1,
	underline_word TEXT
);

CREATE TABLE etl_parser_reg_tmp
(
	id SERIAL  PRIMARY KEY,
	tmp_id VARCHAR(128),
	name VARCHAR(128),
	template_id BIGINT,
	uuid VARCHAR(128),
	parser_type VARCHAR(64) NOT NULL ,
	function VARCHAR(64),
	sort_id INTEGER,
	split1 VARCHAR(10),
	split2 VARCHAR(10),
	reg_type VARCHAR(10),
	parent_uuid VARCHAR(128),
	parent_column VARCHAR(64),
	action_type VARCHAR(8),
	reg_value TEXT,
	sample_log TEXT,
	underline_word TEXT,
	status INTEGER DEFAULT 1
);

CREATE TABLE etl_parser_result
(
	id SERIAL  PRIMARY KEY,
	template_id BIGINT NOT NULL ,
	reg_id BIGINT NOT NULL ,
	column_name VARCHAR(255),
	column_value VARCHAR(2048),
	show_order INTEGER
);

CREATE TABLE etl_parser_rule_category
(
	id SERIAL  PRIMARY KEY,
	asset_type VARCHAR(10),
	asset_code VARCHAR(255),
	asset_label VARCHAR(255),
	show_order INTEGER,
	parent_code VARCHAR(255),
	asset_desc VARCHAR(255),
	create_user VARCHAR(256),
	create_time TIMESTAMP,
	update_user VARCHAR(256),
	update_time TIMESTAMP
);

CREATE TABLE etl_parser_template
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(128),
	status INTEGER DEFAULT 1,
	category VARCHAR(128),
	parent_id BIGINT,
	template_desc VARCHAR(512),
	manage INTEGER DEFAULT 1,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP,
	url VARCHAR(255),
	rule_from VARCHAR(255),
	copy_cnt INTEGER
);

CREATE TABLE etl_reader_param
(
	id SERIAL  PRIMARY KEY,
	source_id BIGINT NOT NULL ,
	config_key VARCHAR(128) NOT NULL ,
	config_value VARCHAR(8192)  NULL ,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP
);

CREATE TABLE etl_reader_param_define
(
	id SERIAL  PRIMARY KEY,
	source_type VARCHAR(32) NOT NULL ,
	cn_config_key VARCHAR(128) NOT NULL ,
	config_key VARCHAR(128) NOT NULL ,
	config_type VARCHAR(64) NOT NULL ,
	config_desc VARCHAR(128),
	sort INTEGER NOT NULL ,
	config_defalut VARCHAR(64),
	is_required VARCHAR(3) DEFAULT '1',
	config_level VARCHAR(3) DEFAULT '1',
	status INTEGER NOT NULL ,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP
);

CREATE TABLE etl_shell
(
	id SERIAL  PRIMARY KEY,
	shell_name VARCHAR(32) DEFAULT '',
	shell_desc VARCHAR(64) DEFAULT '',
	shell_content TEXT NOT NULL ,
	create_user VARCHAR(32) DEFAULT '',
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE etl_shell_opt_log
(
	id SERIAL  PRIMARY KEY,
	shell_id INTEGER NOT NULL ,
	opt_account VARCHAR(32) DEFAULT '',
	opt_type VARCHAR(32) DEFAULT '',
	opt_object VARCHAR(32) DEFAULT '',
	create_time TIMESTAMP
);

CREATE TABLE etl_source
(
	id SERIAL  PRIMARY KEY,
	source_type VARCHAR(32) NOT NULL ,
	source_name VARCHAR(128) NOT NULL ,
	logmodule_id VARCHAR(64) DEFAULT '',
	source_desc VARCHAR(512),
	status INTEGER NOT NULL ,
	source_switch INTEGER,
	access_status INTEGER,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP,
	keep_raw_data INTEGER DEFAULT 1,
	keep_parser_error_data INTEGER,
	copy_cnt INTEGER DEFAULT 0
);

CREATE TABLE etl_source_optlog
(
	id SERIAL  PRIMARY KEY,
	source_id BIGINT NOT NULL ,
	opt_time TIMESTAMP,
	opt_user VARCHAR(32),
	opt_type INTEGER
);

CREATE TABLE etl_source_set
(
	id SERIAL  PRIMARY KEY,
	source_id BIGINT NOT NULL ,
	line_tag_type VARCHAR(32),
	line_tag VARCHAR(512),
	time_tag_type VARCHAR(32),
	time_tag VARCHAR(512),
	charset VARCHAR(64),
	time_format VARCHAR(64),
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP
);

CREATE TABLE etl_source_type
(
	id SERIAL  PRIMARY KEY,
	source_type VARCHAR(32) NOT NULL ,
	name VARCHAR(32) NOT NULL ,
	source_type_desc VARCHAR(128),
	category VARCHAR(32) NOT NULL ,
	sort INTEGER NOT NULL ,
	category_sort INTEGER NOT NULL ,
	icon VARCHAR(64),
	status INTEGER NOT NULL ,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP
);

CREATE TABLE etl_task_day_statistics
(
	id SERIAL  PRIMARY KEY,
	day VARCHAR(100) NOT NULL ,
	task_count BIGINT,
	create_time TIMESTAMP,
	flag INTEGER DEFAULT 1
);

CREATE TABLE etl_transform_action
(
	id SERIAL  PRIMARY KEY,
	rule_id BIGINT NOT NULL ,
	dest_field TEXT,
	dest_field_type VARCHAR(16),
	dest_value VARCHAR(1024),
	dest_table VARCHAR(4096),
	dest_column TEXT,
	format_from VARCHAR(4096),
	format_to VARCHAR(4096),
	start_index INTEGER,
	end_index INTEGER,
	combineChar VARCHAR(1024),
	longitude VARCHAR(64),
	latitude VARCHAR(64),
	country VARCHAR(64),
	province VARCHAR(64),
	city VARCHAR(64),
	isp VARCHAR(64),
	datamask_strategy VARCHAR(16),
	datamask_reg VARCHAR(128),
	script_type VARCHAR(32) DEFAULT '',
	script_content TEXT,
	split VARCHAR(64),
	kv_split VARCHAR(64),
	sample_data TEXT
);

CREATE TABLE etl_transform_condition
(
	id SERIAL  PRIMARY KEY,
	rule_id BIGINT NOT NULL ,
	node_id BIGINT,
	parent_id BIGINT,
	node_type VARCHAR(16),
	condition_name VARCHAR(128),
	src_field VARCHAR(128),
	logic VARCHAR(16),
	compare_table VARCHAR(128),
	compare_content VARCHAR(512)
);

CREATE TABLE etl_transform_dict
(
	id SERIAL  PRIMARY KEY,
	dict_name VARCHAR(128) NOT NULL ,
	cn_dict_name VARCHAR(128) NOT NULL ,
	dict_desc VARCHAR(128),
	status INTEGER DEFAULT 1,
	from_source INTEGER DEFAULT 1,
	update_time TIMESTAMP,
	create_time TIMESTAMP,
	create_user VARCHAR(256),
	update_user VARCHAR(256)
);

CREATE TABLE etl_transform_dict_data
(
	id SERIAL  PRIMARY KEY,
	dict_name VARCHAR(64) DEFAULT '',
	k1 VARCHAR(512) DEFAULT '',
	k2 VARCHAR(512) DEFAULT '',
	k3 VARCHAR(512) DEFAULT '',
	k4 VARCHAR(512) DEFAULT '',
	k5 VARCHAR(512) DEFAULT '',
	k6 VARCHAR(512) DEFAULT '',
	k7 VARCHAR(512) DEFAULT '',
	k8 VARCHAR(512) DEFAULT '',
	k9 VARCHAR(512) DEFAULT '',
	k10 VARCHAR(512) DEFAULT '',
	k11 VARCHAR(512) DEFAULT '',
	k12 VARCHAR(512) DEFAULT '',
	k13 VARCHAR(512) DEFAULT '',
	k14 VARCHAR(512) DEFAULT '',
	k15 VARCHAR(512) DEFAULT '',
	k16 VARCHAR(512) DEFAULT '',
	k17 VARCHAR(512) DEFAULT '',
	k18 VARCHAR(512) DEFAULT '',
	k19 VARCHAR(512) DEFAULT '',
	k20 VARCHAR(512) DEFAULT ''
);

CREATE TABLE etl_transform_dict_field
(
	id SERIAL  PRIMARY KEY,
	dict_name VARCHAR(64) DEFAULT '',
	field_name VARCHAR(64),
	field_type VARCHAR(64) DEFAULT '',
	field_alias_name VARCHAR(64) DEFAULT ''
);

CREATE TABLE etl_transform_function
(
	id SERIAL  PRIMARY KEY,
	code VARCHAR(64) NOT NULL ,
	trans_func BIGINT,
	description VARCHAR(128),
	short_name VARCHAR(64) NOT NULL ,
	status INTEGER DEFAULT 1,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP,
	show_order INTEGER,
	parent_function VARCHAR(32)
);

CREATE TABLE etl_transform_rule
(
	id SERIAL  PRIMARY KEY,
	source_id BIGINT NOT NULL ,
	rule_name VARCHAR(64),
	rule_type VARCHAR(64) NOT NULL ,
	rule_func VARCHAR(64),
	expression VARCHAR(512),
	rule_desc VARCHAR(128),
	rule_order INTEGER NOT NULL ,
	status INTEGER DEFAULT 1,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP,
	enable_advanced CHARACTER(1),
	parent_id VARCHAR(64),
	parent_type VARCHAR(64),
	preview INTEGER DEFAULT 0
);

CREATE TABLE etl_transform_table
(
	id SERIAL  PRIMARY KEY,
	table_name VARCHAR(128) NOT NULL ,
	cn_table_name VARCHAR(128) NOT NULL ,
	table_desc VARCHAR(128),
	field_name VARCHAR(64) NOT NULL ,
	field_type VARCHAR(16),
	status INTEGER DEFAULT 1,
	update_time TIMESTAMP,
	create_time TIMESTAMP,
	create_user VARCHAR(256),
	update_user VARCHAR(256)
);

CREATE TABLE etl_wide_table_column
(
	id SERIAL  PRIMARY KEY,
	table_name VARCHAR(128),
	column_name VARCHAR(128),
	data_type VARCHAR(32),
	data_desc VARCHAR(512)
);

CREATE TABLE etl_wide_table_column_view
(
	id SERIAL  PRIMARY KEY,
	view_id BIGINT,
	column_name VARCHAR(64),
	is_required INTEGER,
	remark VARCHAR(1024)
);

CREATE TABLE etl_wide_table_view
(
	id SERIAL  PRIMARY KEY,
	view_name VARCHAR(128),
	model_view VARCHAR(128),
	view_desc VARCHAR(512)
);

CREATE TABLE etl_writer_ch_config
(
	id SERIAL  PRIMARY KEY,
	source_id BIGINT NOT NULL ,
	reg_value TEXT,
	time_format VARCHAR(16),
	table_name VARCHAR(128),
	order_fields VARCHAR(512),
	cluster_display_name VARCHAR(32),
	shard_num INTEGER,
	replica_num INTEGER,
	max_capacity INTEGER,
	max_keep_days INTEGER,
	strategy VARCHAR(16),
	last_backup_index_name VARCHAR(128),
	failed_index_name VARCHAR(128),
	cycle_scop INTEGER,
	status INTEGER DEFAULT 1,
	view_id VARCHAR(256),
	model_view VARCHAR(256)
);

CREATE TABLE etl_writer_config
(
	id SERIAL  PRIMARY KEY,
	source_id BIGINT NOT NULL ,
	write_type VARCHAR(8),
	config_type VARCHAR(16),
	enable_flag INTEGER
);

CREATE TABLE etl_writer_es_config
(
	id SERIAL  PRIMARY KEY,
	source_id BIGINT NOT NULL ,
	reg_value TEXT,
	time_format VARCHAR(16),
	index_name VARCHAR(128),
	cluster_display_name VARCHAR(32),
	shard_num INTEGER,
	replica_num INTEGER,
	max_capacity INTEGER,
	max_keep_days INTEGER,
	strategy VARCHAR(16),
	last_backup_index_name VARCHAR(128),
	failed_index_name VARCHAR(128),
	cycle_scop INTEGER,
	status INTEGER DEFAULT 1
);

CREATE TABLE etl_writer_kafka_config
(
	id SERIAL  PRIMARY KEY,
	source_id INTEGER NOT NULL ,
	topic VARCHAR(64) DEFAULT '',
	origin_to_topic VARCHAR(64)
);

CREATE TABLE etl_writer_table_config
(
	id SERIAL  PRIMARY KEY,
	source_id BIGINT NOT NULL ,
	write_type VARCHAR(16),
	column_name VARCHAR(128),
	data_type VARCHAR(32),
	data_name VARCHAR(128),
    field_comment VARCHAR(128) NULL
);

CREATE TABLE etl_writer_table_define
(
	id SERIAL  PRIMARY KEY,
	writer_type VARCHAR(8) NOT NULL ,
	table_name VARCHAR(128),
	column_name VARCHAR(48),
	data_type VARCHAR(32),
	data_name VARCHAR(48),
	data_desc VARCHAR(512)
);

CREATE TABLE explore_query_condition
(
	id SERIAL  PRIMARY KEY,
	create_user VARCHAR(100),
	condition_name VARCHAR(32) NOT NULL ,
	condition_desc VARCHAR(128),
	write_type VARCHAR(16) NOT NULL ,
	table_name VARCHAR(128) NOT NULL ,
	query_condition TEXT,
	time_type VARCHAR(2),
	quick_time VARCHAR(16),
	relative_days INTEGER,
	relative_type VARCHAR(16),
	relative_ontime VARCHAR(2),
	now_ontime VARCHAR(2),
	start_time VARCHAR(32),
	end_time VARCHAR(32),
	range_type VARCHAR(2)
);

CREATE TABLE explore_query_history
(
	id SERIAL  PRIMARY KEY,
	table_name VARCHAR(32) DEFAULT '',
	quick_time VARCHAR(32) DEFAULT '',
	query_condition TEXT,
	write_type VARCHAR(32) NOT NULL ,
	start_time VARCHAR(32) DEFAULT '',
	end_time VARCHAR(32) DEFAULT '',
	extra_condition VARCHAR(2048),
	create_time TIMESTAMP NOT NULL ,
	query_hash VARCHAR(128),
	history_type VARCHAR(32),
	create_user VARCHAR(32) DEFAULT 'sysadmin'
);

CREATE TABLE explore_selected_field
(
	id SERIAL  PRIMARY KEY,
	create_user VARCHAR(100),
	write_type VARCHAR(16) NOT NULL ,
	selected_fields TEXT,
	agg_fields VARCHAR(2048) DEFAULT '',
	table_name VARCHAR(128) NOT NULL
);

CREATE TABLE insight_alarm_email_content_temp
(
	id SERIAL  PRIMARY KEY,
	topic VARCHAR(64) DEFAULT '',
	template_desc VARCHAR(64) DEFAULT '',
	email_content TEXT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE insight_alarm_platform_action
(
	id SERIAL  PRIMARY KEY,
	alarm_id INTEGER NOT NULL ,
	email_address VARCHAR(512) DEFAULT '',
	carbon_copy_email_address VARCHAR(512) DEFAULT '',
	blind_carbon_copy_email_address VARCHAR(512) DEFAULT '',
	email_topic VARCHAR(32) DEFAULT '',
	email_content TEXT,
	email_csv_field TEXT,
	email_link_status INTEGER DEFAULT 0,
	syslog_url VARCHAR(64) DEFAULT '',
	syslog_csv_field TEXT,
	local_file_csv_field TEXT,
	phone_number VARCHAR(32) DEFAULT '',
	script_id INTEGER,
	kafka_url VARCHAR(128),
	kafka_topic VARCHAR(64),
	webhook_url VARCHAR(1024),
	phone_script_id INTEGER,
	warn_name VARCHAR(32),
	classification VARCHAR(32),
	warn_level VARCHAR(32),
	warn_content VARCHAR(512),
	warn_effect VARCHAR(512),
	warn_solution VARCHAR(512),
	warn_field VARCHAR(2048),
	bug_type VARCHAR(32),
	cve_id VARCHAR(128),
	attack_type VARCHAR(128),
	sys_alarm_type VARCHAR(32),
	sys_alarm_object TEXT,
	sys_alarm_serious SMALLINT
);

CREATE TABLE insight_alarm_platform_base
(
	id SERIAL  PRIMARY KEY,
	alarm_name VARCHAR(32) DEFAULT '',
	alarm_code VARCHAR(32) DEFAULT 'custom',
	alarm_desc VARCHAR(64) DEFAULT '',
	alarm_type INTEGER DEFAULT 0,
	operator VARCHAR(64) DEFAULT '',
	enable_status INTEGER DEFAULT 1,
	allow_closed INTEGER DEFAULT 1,
	alarm_config TEXT NOT NULL ,
	alarm_object VARCHAR(64) DEFAULT '',
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	copy_count INTEGER DEFAULT 0,
	execute_type VARCHAR(10),
	execute_value VARCHAR(50),
	execute_cron VARCHAR(64),
	flow_id BIGINT
);

CREATE TABLE insight_alarm_platform_record
(
	id SERIAL  PRIMARY KEY,
	alarm_id INTEGER DEFAULT 0,
	alarm_title VARCHAR(128) DEFAULT '采集预处理',
	alarm_content VARCHAR(512) DEFAULT '',
	alarm_serious SMALLINT NOT NULL ,
	alarm_type VARCHAR(64) DEFAULT '',
	alarm_time TIMESTAMP NOT NULL ,
	sys_alarm_user VARCHAR(64) NOT NULL ,
	read_status SMALLINT NOT NULL ,
	hide_status INTEGER DEFAULT 0,
	update_time TIMESTAMP
);

CREATE TABLE intranet_configuration
(
	id SERIAL  PRIMARY KEY,
	safe_domain_type TEXT,
	origin_param TEXT,
	is_del SMALLINT,
	create_time TIMESTAMP,
	create_user VARCHAR(128),
	update_time TIMESTAMP,
	update_user VARCHAR(128),
	member TEXT
);

CREATE TABLE model_action_session_base_info
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(128) DEFAULT '',
	action_session_desc VARCHAR(1024) DEFAULT '',
	datasource VARCHAR(128) DEFAULT '',
	table_name VARCHAR(128) DEFAULT '',
	cluster_name VARCHAR(64) DEFAULT '',
	time_interval_unit VARCHAR(16) DEFAULT '',
	store_time INTEGER NOT NULL ,
	store_time_unit VARCHAR(8) DEFAULT '',
	enable_advanced INTEGER NOT NULL ,
	action_session_sql TEXT,
	action_session_table TEXT,
	session_object VARCHAR(32) DEFAULT '',
	max_session_interval_time INTEGER,
	max_session_interval_time_unit VARCHAR(16) DEFAULT '',
	operation_type VARCHAR(32) DEFAULT '',
	create_time_field VARCHAR(32) DEFAULT 'create_time',
	status INTEGER,
	task_updatetime TIMESTAMP,
	task_result INTEGER,
	task_status INTEGER,
	task_nexttime TIMESTAMP,
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP,
	agg_time INTEGER,
	agg_time_unit VARCHAR(16),
	time_interval INTEGER DEFAULT 1
);

CREATE TABLE model_action_session_filter
(
	id SERIAL  PRIMARY KEY,
	action_session_id BIGINT NOT NULL ,
	field VARCHAR(128),
	field_type VARCHAR(16),
	filter_type VARCHAR(16),
	filter_value VARCHAR(512),
	logic VARCHAR(8),
	parent_id BIGINT
);

CREATE TABLE model_action_session_tasklog
(
	id SERIAL  PRIMARY KEY,
	action_session_id BIGINT NOT NULL ,
	start_time TIMESTAMP,
	end_time TIMESTAMP,
	run_status INTEGER,
	run_result INTEGER,
	exec_start_time VARCHAR(32),
	exec_end_time VARCHAR(32)
);

CREATE TABLE model_cube_base_info
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(128) NOT NULL ,
	cube_desc VARCHAR(1024),
	data_source VARCHAR(128),
	table_name VARCHAR(128),
	store_time INTEGER NOT NULL ,
	store_time_unit VARCHAR(8),
	enable_advanced INTEGER NOT NULL ,
	cube_sql TEXT,
	cube_table TEXT,
	cluster_name VARCHAR(64) DEFAULT '',
	time_interval_unit VARCHAR(16) DEFAULT '',
	status INTEGER,
	task_updatetime TIMESTAMP,
	task_result INTEGER,
	task_status INTEGER,
	task_nexttime TIMESTAMP,
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP,
	agg_time INTEGER,
	agg_time_unit VARCHAR(16),
	time_interval INTEGER DEFAULT 1
);

CREATE TABLE model_cube_cfg
(
	id SERIAL  PRIMARY KEY,
	cube_id BIGINT NOT NULL ,
	new_field VARCHAR(128),
	field_category VARCHAR(8),
	field_type VARCHAR(32),
	origin_field VARCHAR(128),
	origin_field_type VARCHAR(32),
	operator VARCHAR(32),
	field_desc VARCHAR(128)
);

CREATE TABLE model_cube_filter
(
	id SERIAL  PRIMARY KEY,
	cube_id BIGINT NOT NULL ,
	field VARCHAR(128),
	field_type VARCHAR(16),
	filter_type VARCHAR(8),
	filter_value VARCHAR(512),
	logic VARCHAR(8),
	parent_id BIGINT
);

CREATE TABLE model_cube_tasklog
(
	id SERIAL  PRIMARY KEY,
	cube_id BIGINT NOT NULL ,
	start_time TIMESTAMP,
	end_time TIMESTAMP,
	run_status INTEGER,
	run_result INTEGER,
	exec_start_time VARCHAR(32),
	exec_end_time VARCHAR(32)
);

CREATE TABLE recover_record
(
	id SERIAL  PRIMARY KEY,
	task_name VARCHAR(255),
	backup_record_id BIGINT,
	backup_time TIMESTAMP,
	recover_table_name VARCHAR(255),
	file_name VARCHAR(255),
	file_path VARCHAR(255),
	log_path VARCHAR(255),
	recover_status VARCHAR(1),
	recover_same VARCHAR(1),
	backup_rows BIGINT,
	recover_rows BIGINT,
	recover_result VARCHAR(1),
	recover_result_desc VARCHAR(512),
	create_time TIMESTAMP,
	create_user VARCHAR(50),
	update_time TIMESTAMP,
	update_user VARCHAR(50),
	flag VARCHAR(1) DEFAULT '1',
	recover_id BIGINT,
	recover_start_time TIMESTAMP,
	recover_end_time TIMESTAMP,
	recover_type VARCHAR(1),
	execute_start_time TIMESTAMP,
	execute_end_time TIMESTAMP
);

CREATE TABLE sys_db_version_upgrade
(
	id VARCHAR PRIMARY KEY,
	version VARCHAR(50),
	module_name VARCHAR(255),
	description VARCHAR(255),
	content TEXT,
	error_content TEXT,
	version_code VARCHAR(255),
	status SMALLINT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time VARCHAR(30),
	success_content TEXT,
	error_msg TEXT,
	is_atomic SMALLINT,
	exclude_content TEXT
);

CREATE TABLE sys_db_version_upgrade_detail
(
	id VARCHAR PRIMARY KEY,
	version VARCHAR(50),
	module_name VARCHAR(255),
	content TEXT,
	error_content TEXT,
	success_content TEXT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	exclude_content TEXT
);

CREATE TABLE sys_import_his
(
	id SERIAL  PRIMARY KEY,
	model_name VARCHAR(128) NOT NULL ,
	file_name VARCHAR(128) NOT NULL ,
	operator VARCHAR(64) NOT NULL ,
	file_size VARCHAR(64),
	count INTEGER,
	success_count INTEGER,
	err_count INTEGER,
	status VARCHAR(50),
	speed VARCHAR(32),
	basic_facts TEXT,
	import_src VARCHAR(32),
	start_time VARCHAR(30),
	end_time VARCHAR(30)
);

CREATE TABLE system_backup_config
(
	id SERIAL  PRIMARY KEY,
	config_type VARCHAR(64) DEFAULT '',
	backup_time DATE,
	backup_file VARCHAR(128) DEFAULT '',
	status SMALLINT DEFAULT 1,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	config_name VARCHAR(64) DEFAULT ''
);

CREATE TABLE system_backup_table
(
	id SERIAL  PRIMARY KEY,
	config_type VARCHAR(32) DEFAULT '',
	table_name VARCHAR(32) DEFAULT ''
);

CREATE TABLE system_clickhouse_config
(
	id SERIAL  PRIMARY KEY,
	cluster_name VARCHAR(32) DEFAULT '',
	cluster_username VARCHAR(32) DEFAULT '',
	cluster_password VARCHAR(64) DEFAULT '',
	ip VARCHAR(32) DEFAULT '',
	username VARCHAR(32) DEFAULT '',
	password VARCHAR(64) DEFAULT '',
	su_username VARCHAR(32) DEFAULT '',
	su_password VARCHAR(64) DEFAULT '',
	network_card VARCHAR(32) DEFAULT '',
	recover_tmp_dir VARCHAR(100),
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	is_secret_free INTEGER DEFAULT 0,
	secret_file_path VARCHAR(128),
	secret_file_password VARCHAR(128)
);

CREATE TABLE system_tables
(
	id SERIAL  PRIMARY KEY,
	type VARCHAR(2) NOT NULL ,
	database VARCHAR(256),
	table_name VARCHAR(256)
);

CREATE TABLE tb_filter
(
	id SERIAL  PRIMARY KEY,
	module_code VARCHAR(255) NOT NULL ,
	module_name VARCHAR(512),
	code VARCHAR(50) NOT NULL ,
	name VARCHAR(512) NOT NULL ,
	parent_code VARCHAR(50),
	filter_config TEXT,
	flag VARCHAR(1) DEFAULT '1',
	page_filter INTEGER,
	agg_filter INTEGER,
	is_default VARCHAR(1),
	is_page_default VARCHAR(100),
	sort_no INTEGER
);

CREATE TABLE ti_bad_score_config
(
	score_id VARCHAR PRIMARY KEY,
	information_source_vendor VARCHAR(255) NOT NULL ,
	source VARCHAR(255) NOT NULL ,
	score INTEGER NOT NULL ,
	weight INTEGER NOT NULL
);

CREATE TABLE ti_batch_query_detail
(
	id SERIAL  PRIMARY KEY,
	query_task_id BIGINT NOT NULL ,
	value VARCHAR(1000),
	type VARCHAR(10),
	success_vendors VARCHAR(100),
	threat_types VARCHAR(200),
	tags VARCHAR(500),
	is_spite INTEGER DEFAULT 0,
	score DOUBLE PRECISION DEFAULT 0.00,
	relevant_information TEXT,
	extend_info TEXT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE ti_batch_query_task
(
	id SERIAL  PRIMARY KEY,
	type VARCHAR(10),
	value_arr TEXT,
	total_num INTEGER DEFAULT 0,
	success_num INTEGER DEFAULT 0,
	finish_num INTEGER DEFAULT 0,
	hit_vendors VARCHAR(100),
	vendor_success_info VARCHAR(200),
	exec_start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	exec_end_time TIMESTAMP,
	create_user VARCHAR(50),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	status INTEGER DEFAULT 1
);

CREATE TABLE ti_interface_conf
(
	id SERIAL  PRIMARY KEY,
	alias_code VARCHAR(255) NOT NULL ,
	information_source_vendor VARCHAR(255),
	information_source_code VARCHAR(255),
	alias_name VARCHAR(255) NOT NULL ,
	protocol VARCHAR(20) NOT NULL ,
	ip_port VARCHAR(255) NOT NULL ,
	api_addr VARCHAR(255) NOT NULL ,
	url_param VARCHAR(5000),
	req_method VARCHAR(10) NOT NULL ,
	req_header VARCHAR(5000),
	req_body VARCHAR(5000),
	status VARCHAR(5) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE ti_open_app_auth
(
	id VARCHAR(64) NOT NULL ,
	app_name VARCHAR(100),
	token VARCHAR(128),
	secret_key VARCHAR(255),
	auth_req_uri VARCHAR(512),
	rate_limit INTEGER,
	rate_time_num INTEGER,
	rate_time_unit VARCHAR(50),
	enable_status SMALLINT,
	create_user VARCHAR(100),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	app_user VARCHAR(50),
	description VARCHAR(255),
	rate_limit_enable SMALLINT DEFAULT 1,
	day_limit_num BIGINT,
	day_limit_enable SMALLINT DEFAULT 1,
	month_limit_num BIGINT,
	month_limit_enable SMALLINT DEFAULT 1,
	effective_start_time TIMESTAMP,
	effective_end_time TIMESTAMP,
	effective_time_enable SMALLINT DEFAULT 1,
	temp_enable SMALLINT DEFAULT 0,
	temp_day_limit_num BIGINT,
	temp_day_limit_enable SMALLINT DEFAULT 1,
	temp_effective_start_time TIMESTAMP,
	temp_effective_end_time TIMESTAMP,
	idss_enable SMALLINT DEFAULT 0
);

CREATE TABLE ti_search_his
(
	id SERIAL  PRIMARY KEY,
	search_content VARCHAR(2048),
	search_user VARCHAR(255),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	type VARCHAR(10)
);

CREATE TABLE ueba_dictionary
(
	id SERIAL  PRIMARY KEY,
	key_code VARCHAR(255) NOT NULL ,
	value VARCHAR(512),
	enable INTEGER,
	remark VARCHAR(4000),
	sortno INTEGER,
	type VARCHAR(255),
	parent_id VARCHAR(255)
);

CREATE TABLE ums_create_table_record
(
	id SERIAL  PRIMARY KEY,
	table_name VARCHAR(32) DEFAULT '',
	flag INTEGER DEFAULT 1,
	create_user VARCHAR(32) DEFAULT '',
	update_user VARCHAR(32) DEFAULT '',
	create_date TIMESTAMP,
	update_date TIMESTAMP
);

CREATE TABLE ums_sys_application
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(100),
	code VARCHAR(100),
	type VARCHAR(100),
	is_active VARCHAR(1) DEFAULT '0',
	sso VARCHAR(100),
	app_desc VARCHAR(1024),
	is_edit VARCHAR(1),
	app_release VARCHAR(100),
	app_path VARCHAR(255),
	app_owner VARCHAR(64),
	create_time TIMESTAMP,
	version VARCHAR(100),
	in_iframe INTEGER DEFAULT 0
);

CREATE TABLE ums_sys_auth_config
(
	id SERIAL  PRIMARY KEY,
	auth_type SMALLINT NOT NULL ,
	enable_status SMALLINT NOT NULL ,
	default_status SMALLINT NOT NULL ,
	display_name VARCHAR(200) NOT NULL ,
	sync_status SMALLINT,
	enable_create_user SMALLINT NOT NULL ,
	role_id VARCHAR(50),
	grant_type SMALLINT,
	oauth_base_field_info TEXT,
	oauth_code_request_url VARCHAR(1000),
	oauth_code_request_way VARCHAR(20),
	oauth_code_resp_field VARCHAR(50),
	oauth_code_field_info TEXT,
	oauth_token_request_url VARCHAR(1000),
	oauth_token_request_way VARCHAR(20),
	oauth_token_resp_field VARCHAR(50),
	oauth_token_resp_format VARCHAR(20),
	oauth_token_field_info TEXT,
	oauth_user_request_url VARCHAR(1000),
	oauth_user_request_way VARCHAR(20),
	oauth_user_resp_field VARCHAR(50),
	oauth_user_resp_format VARCHAR(20),
	oauth_user_field_info TEXT,
	fa_app_field VARCHAR(50),
	fa_login_url VARCHAR(1000),
	fa_auth_url VARCHAR(1000),
	fa_user_resp_field VARCHAR(50),
	fa_request_protocol VARCHAR(20),
	fa_method_name VARCHAR(50),
	fa_request_way VARCHAR(20),
	fa_login_field_info TEXT,
	fa_check_field_info TEXT,
	fa_request_xml_template VARCHAR(1024),
	create_user VARCHAR(100),
	create_time TIMESTAMP NOT NULL ,
	update_user VARCHAR(100),
	update_time TIMESTAMP NOT NULL ,
	fa_vendor VARCHAR(64) DEFAULT 'asiaInfoSec'
);

CREATE TABLE ums_sys_calendar
(
	id SERIAL  PRIMARY KEY,
	date DATE NOT NULL ,
	day_of_year INTEGER NOT NULL ,
	day_of_month INTEGER NOT NULL ,
	day_of_week INTEGER NOT NULL ,
	is_work SMALLINT NOT NULL
);

CREATE TABLE ums_sys_datasource_config
(
	id SERIAL  PRIMARY KEY,
	moudle_name VARCHAR(32),
	datasource_type VARCHAR(16),
	ip VARCHAR(64),
	db_name VARCHAR(64),
	port VARCHAR(16),
	user_name VARCHAR(128),
	password VARCHAR(128),
	show_name VARCHAR(128),
	time_out INTEGER,
	encrypt VARCHAR(2),
	status VARCHAR(2),
	copy_cnt INTEGER,
	db_url VARCHAR(512),
	create_user VARCHAR(16),
	create_time TIMESTAMP,
	update_user VARCHAR(16),
	update_time TIMESTAMP
);

CREATE TABLE ums_sys_holiday
(
	id SERIAL  PRIMARY KEY,
	type VARCHAR(2),
	name VARCHAR(128),
	start_date VARCHAR(32),
	end_date VARCHAR(32),
	holiday_desc VARCHAR(1024),
	holiday_year VARCHAR(8),
	create_user VARCHAR(100),
	create_time TIMESTAMP,
	update_user VARCHAR(100),
	update_time TIMESTAMP,
	del_flag VARCHAR(1)
);

CREATE TABLE ums_sys_license
(
	license BYTEA,
	sn_data VARCHAR(1024),
	sn_ip VARCHAR(512)
);

CREATE TABLE ums_sys_log
(
	id SERIAL  PRIMARY KEY,
	user_name VARCHAR(16) DEFAULT '',
	real_name VARCHAR(16) DEFAULT '',
	login_ip VARCHAR(32) DEFAULT '',
	user_agent VARCHAR(256) DEFAULT '',
	request_path VARCHAR(64) DEFAULT '',
	log_name VARCHAR(2048) DEFAULT '',
	log_result VARCHAR(32) DEFAULT '',
	opt_type VARCHAR(32) DEFAULT '',
	opt_type_code VARCHAR(20),
	opt_module VARCHAR(64) DEFAULT '',
	opt_module_code VARCHAR(20),
	http_method VARCHAR(16) DEFAULT '',
	create_time TIMESTAMP NOT NULL
);

CREATE TABLE ums_sys_menus
(
	id SERIAL  PRIMARY KEY,
	menu_type VARCHAR(32) NOT NULL ,
	menu_property VARCHAR(2) NOT NULL ,
	menu_level INTEGER NOT NULL ,
	menu_name VARCHAR(64) NOT NULL ,
	menu_code VARCHAR(64) NOT NULL ,
	menu_path VARCHAR(512),
	manage_free VARCHAR(2) DEFAULT '0',
	hidden VARCHAR(2) DEFAULT '0',
	parent_name VARCHAR(64),
	status VARCHAR(2) DEFAULT '1',
	menu_order INTEGER,
	default_order INTEGER NOT NULL ,
	default_name VARCHAR(64) NOT NULL ,
	default_status VARCHAR(2) NOT NULL ,
	default_parent VARCHAR(64),
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP,
	root_parent VARCHAR(64),
	application_code VARCHAR(100),
	application VARCHAR(100),
	level1 VARCHAR(100),
	menu_category VARCHAR(2) DEFAULT '0',
	new_open_window INTEGER DEFAULT 0
);

CREATE TABLE ums_sys_org
(
	id SERIAL  PRIMARY KEY,
	org_code VARCHAR(255),
	org_name VARCHAR(255),
	org_full_name VARCHAR(512),
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP,
	del_flag VARCHAR(1),
	parent_org_code VARCHAR(100) DEFAULT 'sdc'
);

CREATE TABLE ums_sys_org_dept
(
	id SERIAL  PRIMARY KEY,
	org_id BIGINT NOT NULL ,
	dept_id VARCHAR(100),
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP
);

CREATE TABLE ums_sys_password_policy
(
	id SERIAL  PRIMARY KEY,
	min_length INTEGER,
	max_length INTEGER,
	is_lower_case VARCHAR(1),
	is_big_case VARCHAR(1),
	is_special_char VARCHAR(1),
	rule_open VARCHAR(1),
	expiration INTEGER,
	alter_day INTEGER,
	expiration_open VARCHAR(1),
	login_num INTEGER,
	lock_threshold INTEGER,
	lock_time INTEGER,
	lock_open VARCHAR(1),
	logout_time INTEGER,
	logout_open VARCHAR(1),
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP,
	flag VARCHAR(1),
	is_number VARCHAR(1),
	forbid_ip VARCHAR(1024),
	forbid_ip_open VARCHAR(1)
);

CREATE TABLE ums_sys_role
(
	role_id VARCHAR PRIMARY KEY,
	role_name VARCHAR(100),
	role_describe VARCHAR(255),
	builtin SMALLINT NOT NULL ,
	del_flag SMALLINT NOT NULL ,
	create_user VARCHAR(100),
	create_date BIGINT,
	update_user VARCHAR(100),
	update_date BIGINT,
	is_admin VARCHAR(1),
	role_resource_search_limit INTEGER DEFAULT 0,
	user_resource_search_limit INTEGER DEFAULT 0
);

CREATE TABLE ums_sys_role_ch_es
(
	id SERIAL  PRIMARY KEY,
	type VARCHAR(100),
	role_id VARCHAR(100),
	table_name VARCHAR(100) NOT NULL ,
	query_condition TEXT,
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP,
	flag VARCHAR(1) DEFAULT '1',
    source_tenant_id BIGINT NULL
);

CREATE TABLE ums_sys_role_data
(
	role_data_id SERIAL  PRIMARY KEY,
	role_id VARCHAR(32) NOT NULL ,
	function_id TEXT NOT NULL ,
	data_id TEXT NOT NULL ,
	value VARCHAR(32),
	create_user VARCHAR(100),
	create_date BIGINT,
	update_user VARCHAR(100),
	update_date BIGINT
);

CREATE TABLE ums_sys_role_extend
(
	extend_id SERIAL  PRIMARY KEY,
	role_id VARCHAR(255) NOT NULL ,
	extend_role_id VARCHAR(255),
	flag VARCHAR(1) DEFAULT '1'
);

CREATE TABLE ums_sys_role_function
(
	role_function_id SERIAL  PRIMARY KEY,
	role_id VARCHAR(32) NOT NULL ,
	function_id TEXT NOT NULL ,
	value VARCHAR(32),
	create_user VARCHAR(100),
	create_date BIGINT,
	update_user VARCHAR(100),
	update_date BIGINT
);

CREATE TABLE ums_sys_route_interface
(
	id SERIAL  PRIMARY KEY,
	route_code VARCHAR(255),
	url VARCHAR(255)
);

CREATE TABLE ums_sys_script
(
	id SERIAL  PRIMARY KEY,
	script_name VARCHAR(32) DEFAULT '',
	script_desc VARCHAR(256) DEFAULT '',
	script_content TEXT,
	status VARCHAR(2) DEFAULT '',
	create_user VARCHAR(128) DEFAULT '',
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	is_alert VARCHAR(1) DEFAULT '0'
);

CREATE TABLE ums_sys_script_opt_log
(
	id SERIAL  PRIMARY KEY,
	script_id INTEGER,
	opt_account VARCHAR(128) DEFAULT '',
	opt_type VARCHAR(16) DEFAULT '',
	opt_object VARCHAR(16) DEFAULT '',
	create_time TIMESTAMP
);

CREATE TABLE ums_sys_user
(
	user_id SERIAL  PRIMARY KEY,
	dept_id VARCHAR(100),
	user_name VARCHAR(300),
	password VARCHAR(32) NOT NULL ,
	password_old VARCHAR(32),
	period_from BIGINT NOT NULL ,
	period_to BIGINT NOT NULL ,
	status SMALLINT NOT NULL ,
	real_name VARCHAR(300),
	code VARCHAR(100),
	sex VARCHAR(1),
	telephone VARCHAR(20),
	email VARCHAR(100) NOT NULL ,
	leader VARCHAR(100),
	login_voucher VARCHAR(100),
	fail_count INTEGER,
	lock_date TIMESTAMP,
	final_login_ip VARCHAR(64),
	final_login_date BIGINT,
	builtin SMALLINT NOT NULL ,
	security_code VARCHAR(128),
	remark VARCHAR(300),
	del_flag SMALLINT NOT NULL ,
	tenant_id INTEGER,
	queue VARCHAR(64),
	create_user VARCHAR(100),
	create_date BIGINT,
	update_user VARCHAR(100),
	update_date BIGINT,
	social_account VARCHAR(100),
	first_login_fail_time TIMESTAMP,
	is_first_login VARCHAR(1),
	is_need_update_password VARCHAR(1),
	default_router_id VARCHAR(100),
	default_router_name VARCHAR(100),
	default_dashboard INTEGER,
	mobile VARCHAR(11),
	dept_name VARCHAR(100),
	last_update_password_time TIMESTAMP,
	data_limit_extend_role_id VARCHAR(64)
);

CREATE TABLE ums_sys_user_role
(
	user_role_id SERIAL  PRIMARY KEY,
	user_id BIGINT NOT NULL ,
	role_id VARCHAR(32) NOT NULL ,
	create_user VARCHAR(100),
	create_date BIGINT,
	update_user VARCHAR(100),
	update_date BIGINT
);

CREATE TABLE ums_sys_work_time
(
	id SERIAL  PRIMARY KEY,
	on_time VARCHAR(20),
	out_time VARCHAR(20)
);



COMMENT ON COLUMN ai_report.report_name IS '报表名称';
COMMENT ON COLUMN ai_report.report_time IS '报表时间';
COMMENT ON COLUMN ai_report.execute_content IS '执行内容';
COMMENT ON COLUMN ai_report.execute_content_type IS '执行内容类型:gpl,sql';
COMMENT ON COLUMN ai_report.execute_type IS '执行类型:day-每天；week-每周；month-每月；year-每年；cron-cron表达式';
COMMENT ON COLUMN ai_report.execute_config IS '执行配置';
COMMENT ON COLUMN ai_report.current_execute_time IS '当前执行时间';
COMMENT ON COLUMN ai_report.next_execute_time IS '下次执行时间';
COMMENT ON COLUMN ai_report.send_type IS '发送类型：local,mail,sftp';
COMMENT ON COLUMN ai_report.send_config IS '发送配置:json格式';
COMMENT ON COLUMN ai_report.send_format IS '发送格式:csv,pdf';
COMMENT ON COLUMN ai_report.report_result IS '报表执行结果:0-生成中；1-生成成功；2-生成失败；3-发送成功；4-发送失败';
COMMENT ON COLUMN ai_report.status IS '报表当前状态1-开启；0-关闭';
COMMENT ON COLUMN ai_report.create_time IS '创建时间';
COMMENT ON COLUMN ai_report.create_user IS '创建人';
COMMENT ON COLUMN ai_report.update_time IS '更新时间';
COMMENT ON COLUMN ai_report.update_user IS '更新人';
COMMENT ON COLUMN ai_report.flag IS '有效标识:1-有效；0-无效';
COMMENT ON COLUMN ai_report.planTime IS '任务计划时间';
COMMENT ON COLUMN ai_report.second IS '秒';
COMMENT ON COLUMN ai_report.minute IS '分';
COMMENT ON COLUMN ai_report.hour IS '时';
COMMENT ON COLUMN ai_report.dayOfweek IS '一周第几天';
COMMENT ON COLUMN ai_report.dayOfMonth IS '一个月第几天';
COMMENT ON COLUMN ai_report.monthOfYear IS '一年第几个月';
COMMENT ON COLUMN ai_report.split_char IS '分割符';
COMMENT ON COLUMN ai_report.dashboard_drag_node IS 'dashboard节点';
COMMENT ON COLUMN ai_report.drag_selected IS 'dashboard选中节点';
COMMENT ON COLUMN ai_report.widget_list IS 'dashboard详细配置';
COMMENT ON COLUMN ai_report.dashboard_url IS 'dashboard拼接的url';
COMMENT ON COLUMN ai_report.description IS '描述';
COMMENT ON COLUMN ai_report.has_header IS '是否需要表头:1-是，0-否，默认1';
COMMENT ON COLUMN ai_report_config.id IS '主键';
COMMENT ON COLUMN ai_report_config.report_id IS '报表id';
COMMENT ON COLUMN ai_report_config.config IS '配置项';
COMMENT ON COLUMN ai_report_config.is_delete IS '是否删除，1：正常，2：删除';
COMMENT ON COLUMN ai_report_config.is_cycle IS '是否时定时任务，1：是，2：否';
COMMENT ON COLUMN ai_report_config.cycle IS '定时周期表达式';
COMMENT ON COLUMN ai_report_config.export_type IS '导出类型,多个逗号分隔，word、pdf、html';
COMMENT ON COLUMN ai_report_config.create_id IS '创建人';
COMMENT ON COLUMN ai_report_config.modify_id IS '更新人';
COMMENT ON COLUMN ai_report_config.create_time IS '创建时间';
COMMENT ON COLUMN ai_report_config.modify_time IS '更新时间';
COMMENT ON COLUMN ai_report_execute.id IS 'id';
COMMENT ON COLUMN ai_report_execute.report_id IS '报表id';
COMMENT ON COLUMN ai_report_execute.file_name IS '文件名称';
COMMENT ON COLUMN ai_report_execute.file_local_path IS '文件本地保存地址';
COMMENT ON COLUMN ai_report_execute.execute_time IS '执行时间';
COMMENT ON COLUMN ai_report_execute.execute_result IS '执行结果';
COMMENT ON COLUMN ai_report_execute.execute_result_desc IS '执行结果描述';
COMMENT ON COLUMN ai_report_execute.create_time IS '创建时间';
COMMENT ON COLUMN ai_report_execute.create_user IS '创建人';
COMMENT ON COLUMN ai_report_execute.update_time IS '更新时间';
COMMENT ON COLUMN ai_report_execute.update_user IS '更新人';
COMMENT ON COLUMN ai_report_execute.flag IS '有效标识';
COMMENT ON COLUMN ai_report_execute.source_file_name IS '原始文件名称';
COMMENT ON COLUMN ai_report_execute.exception_file IS '异常文件路径';
COMMENT ON COLUMN ai_report_group.id IS '主键';
COMMENT ON COLUMN ai_report_group.name IS '名称';
COMMENT ON COLUMN ai_report_group.parent_id IS '父节点id';
COMMENT ON COLUMN ai_report_group.project_id IS '项目';
COMMENT ON COLUMN ai_report_group.report_type IS '报表类型，1：目录，2：报表';
COMMENT ON COLUMN ai_report_group.is_delete IS '是否删除,删除的为回收站;1:正常,2:删除';
COMMENT ON COLUMN ai_report_group.create_id IS '创建人';
COMMENT ON COLUMN ai_report_group.modify_id IS '更新人';
COMMENT ON COLUMN ai_report_group.create_time IS '创建时间';
COMMENT ON COLUMN ai_report_group.modify_time IS '更新时间';
COMMENT ON COLUMN ai_report_group.sortno IS '序号';
COMMENT ON COLUMN ai_report_history.id IS '主键';
COMMENT ON COLUMN ai_report_history.report_id IS '报表id';
COMMENT ON COLUMN ai_report_history.is_auto IS '标识自动/手动生成，1：自动，2：手动';
COMMENT ON COLUMN ai_report_history.status IS '状态，1：进行中，2：已完成，3：异常';
COMMENT ON COLUMN ai_report_history.start_time IS '开始时间';
COMMENT ON COLUMN ai_report_history.end_time IS '结束时间';
COMMENT ON COLUMN ai_report_history.path IS '服务器文件路径';
COMMENT ON COLUMN ai_report_history.export_type IS '导出类型，word、pdf、html';
COMMENT ON COLUMN ai_report_send.id IS 'id';
COMMENT ON COLUMN ai_report_send.report_id IS '报表id';
COMMENT ON COLUMN ai_report_send.execute_id IS '执行记录id';
COMMENT ON COLUMN ai_report_send.file_name IS '文件名称';
COMMENT ON COLUMN ai_report_send.file_size IS '文件大小';
COMMENT ON COLUMN ai_report_send.send_type IS '输出类型：local,mail,sftp,ftp';
COMMENT ON COLUMN ai_report_send.send_time IS '发送时间';
COMMENT ON COLUMN ai_report_send.send_result IS '发送结果：1-成功；2-失败';
COMMENT ON COLUMN ai_report_send.send_result_desc IS '发送结果描述';
COMMENT ON COLUMN ai_report_send.create_time IS '创建时间';
COMMENT ON COLUMN ai_report_send.create_user IS '创建人';
COMMENT ON COLUMN ai_report_send.update_time IS '更新时间';
COMMENT ON COLUMN ai_report_send.update_user IS '更新人';
COMMENT ON COLUMN ai_report_send.flag IS '有效标识：1-有效，0-无效';
COMMENT ON COLUMN ai_user_report.id IS 'id';
COMMENT ON COLUMN ai_user_report.report_id IS '报表id';
COMMENT ON COLUMN ai_user_report.user_id IS '用户id';
COMMENT ON COLUMN asset_net_district.id IS '主键';
COMMENT ON COLUMN asset_net_district.name IS '网络域名称';
COMMENT ON COLUMN asset_net_district.ip_location IS '物理位置';
COMMENT ON COLUMN asset_net_district.ip_section IS 'ip范围';
COMMENT ON COLUMN asset_net_district.net_describe IS '描述';
COMMENT ON COLUMN asset_net_district.create_time IS '创建时间';
COMMENT ON COLUMN asset_net_district.create_user IS '创建者';
COMMENT ON COLUMN asset_net_district.update_time IS '更新时间';
COMMENT ON COLUMN asset_net_district.update_user IS '更新者';
COMMENT ON COLUMN asset_net_district.flag IS '有效标识（1-有效；0-无效）';
COMMENT ON COLUMN asset_safe_domain.id IS '主键';
COMMENT ON COLUMN asset_safe_domain.name IS '安全域名称';
COMMENT ON COLUMN asset_safe_domain.physics_location IS '物理位置';
COMMENT ON COLUMN asset_safe_domain.ip_type IS 'ip类型,1ipv4,2ipv6';
COMMENT ON COLUMN asset_safe_domain.domain_type IS '域类型,1安全域,2非安全域';
COMMENT ON COLUMN asset_safe_domain.remark IS '描述';
COMMENT ON COLUMN asset_safe_domain.member IS 'ip集合';
COMMENT ON COLUMN asset_safe_domain.safe_domain_type IS '类型,json格式';
COMMENT ON COLUMN asset_safe_domain.origin_param IS '原始数据类型';
COMMENT ON COLUMN asset_safe_domain.is_del IS '是否删除,1是,0否';
COMMENT ON COLUMN asset_safe_domain.create_time IS '创建时间';
COMMENT ON COLUMN asset_safe_domain.create_user IS '创建人';
COMMENT ON COLUMN asset_safe_domain.update_time IS '更新时间';
COMMENT ON COLUMN asset_safe_domain.update_user IS '更新人';
COMMENT ON COLUMN asset_standby_field_config.config_table_name IS '配置表名';
COMMENT ON COLUMN asset_standby_field_config.standby_field_name IS '备用字段名称';
COMMENT ON COLUMN asset_standby_field_config.standby_field_type IS '备用字段类型';
COMMENT ON COLUMN asset_standby_field_config.standby_field_show_name IS '备用字段展示名称';
COMMENT ON COLUMN asset_standby_field_config.enable_status IS '启用状态';
COMMENT ON COLUMN asset_standby_field_config.status IS '删除状态';
COMMENT ON COLUMN asset_standby_field_config.create_user IS '创建者';
COMMENT ON COLUMN asset_standby_field_config.update_user IS '更新者';
COMMENT ON COLUMN asset_standby_field_config.create_time IS '创建时间';
COMMENT ON COLUMN asset_standby_field_config.update_time IS '更新时间';
COMMENT ON COLUMN backup_config.id IS 'id';
COMMENT ON COLUMN backup_config.type IS '备份数据库:ch,mysql';
COMMENT ON COLUMN backup_config.table_name IS '对象名称';
COMMENT ON COLUMN backup_config.backup_type IS '备份类型：备份/归档';
COMMENT ON COLUMN backup_config.backup_policy IS '策略：all-全量/increment-增量';
COMMENT ON COLUMN backup_config.increment_field IS '增量备份是依赖字段';
COMMENT ON COLUMN backup_config.storage IS '文件存储位置';
COMMENT ON COLUMN backup_config.storage_volume IS '存储容量';
COMMENT ON COLUMN backup_config.volume_unit IS '容量单位';
COMMENT ON COLUMN backup_config.storage_day IS '存储天数';
COMMENT ON COLUMN backup_config.execute_period IS '执行周期：cycle-周期/single-单次';
COMMENT ON COLUMN backup_config.execute_plan IS '执行计划：每隔多少天执行';
COMMENT ON COLUMN backup_config.execute_time IS '执行时间HH:mm:ss';
COMMENT ON COLUMN backup_config.create_user IS '创建者';
COMMENT ON COLUMN backup_config.create_date IS '创建日期';
COMMENT ON COLUMN backup_config.update_user IS '更新者';
COMMENT ON COLUMN backup_config.update_date IS '更新日期';
COMMENT ON COLUMN backup_config.flag IS '有效标识';
COMMENT ON COLUMN backup_config.time_type IS '归档/删除基准线';
COMMENT ON COLUMN backup_config.time_config IS '定时时间配置';
COMMENT ON COLUMN backup_config.is_recover IS '是否定时恢复，1-是；0-否';
COMMENT ON COLUMN backup_config.task_name IS '任务名';
COMMENT ON COLUMN backup_config.backup_method IS '备份方式:1-节点备份；2-业务备份';
COMMENT ON COLUMN backup_config.recover_config IS '恢复配置json';
COMMENT ON COLUMN backup_config.last_finish_time IS '最后一次执行时间';
COMMENT ON COLUMN backup_config.backup_result IS '最后一次执行结果';
COMMENT ON COLUMN backup_record.id IS 'id';
COMMENT ON COLUMN backup_record.config_id IS '配置id';
COMMENT ON COLUMN backup_record.name IS '表名';
COMMENT ON COLUMN backup_record.type IS '操作类型:backup-备份/recover-恢复';
COMMENT ON COLUMN backup_record.backup_desc IS '备份明细';
COMMENT ON COLUMN backup_record.backup_result IS '备份结果';
COMMENT ON COLUMN backup_record.backup_result_desc IS '备份结果描述';
COMMENT ON COLUMN backup_record.file_name IS '备份文件名称';
COMMENT ON COLUMN backup_record.file_dir IS '备份文件存储位置';
COMMENT ON COLUMN backup_record.create_user IS '创建者';
COMMENT ON COLUMN backup_record.create_date IS '创建日期';
COMMENT ON COLUMN backup_record.flag IS '有效标识';
COMMENT ON COLUMN backup_record.backup_storage IS '当前备份记录存放位置';
COMMENT ON COLUMN backup_record.recover_status IS '0-未恢复；1-恢复中；2-已恢复；3-恢复失败';
COMMENT ON COLUMN backup_record.log_file_path IS '日志文件路径';
COMMENT ON COLUMN backup_record.log_file_name IS '日志文件名称';
COMMENT ON COLUMN backup_record.total_rows IS '文件总数';
COMMENT ON COLUMN backup_record.total_capacity IS '文件总容量';
COMMENT ON COLUMN backup_record.recover_bak_table IS '新建表';
COMMENT ON COLUMN backup_record.backup_type IS '备份类型：备份/归档';
COMMENT ON COLUMN backup_record.backup_policy IS '策略：all-全量/increment-增量';
COMMENT ON COLUMN backup_record.storage IS '文件存储位置';
COMMENT ON COLUMN backup_record.recover_rows IS '恢复个数';
COMMENT ON COLUMN backup_record.recover_same IS '恢复是否一致';
COMMENT ON COLUMN backup_record.md5_hashcode IS '文件计算后的hash';
COMMENT ON COLUMN backup_record.sign IS 'sm2的签名';
COMMENT ON COLUMN backup_record.recover_time IS '恢复时间';
COMMENT ON COLUMN backup_record.backup_start_time IS '备份分区开始时间';
COMMENT ON COLUMN backup_record.backup_end_time IS '备份分区结束时间';
COMMENT ON COLUMN backup_record.execute_start_time IS '执行开始时间';
COMMENT ON COLUMN backup_record.execute_end_time IS '执行结束时间';
COMMENT ON COLUMN backup_recover.id IS 'id';
COMMENT ON COLUMN backup_recover.config_id IS 'config表id';
COMMENT ON COLUMN backup_recover.execute_type IS '运行类型：hour-每小时，day-每天，week-每周，month-每月，cron-表达式';
COMMENT ON COLUMN backup_recover.execute_value IS '时间值';
COMMENT ON COLUMN backup_recover.create_user IS '创建者';
COMMENT ON COLUMN backup_recover.create_time IS '新增时间';
COMMENT ON COLUMN backup_recover.update_user IS '更新者';
COMMENT ON COLUMN backup_recover.update_time IS '更新时间';
COMMENT ON COLUMN common_filter_base.create_user IS '过滤条件所属用户';
COMMENT ON COLUMN common_filter_base.name IS '过滤条件名称';
COMMENT ON COLUMN common_filter_base.type IS '模块类型';
COMMENT ON COLUMN common_filter_base.filter_desc IS '过滤条件描述';
COMMENT ON COLUMN common_filter_base.create_time IS '创建时间';
COMMENT ON COLUMN common_filter_base.update_user IS '修改人';
COMMENT ON COLUMN common_filter_base.update_time IS '修改时间';
COMMENT ON COLUMN common_filter_base.last_run_time IS '上次运行时间';
COMMENT ON COLUMN common_filter_base.overview IS '是否为概览页面的查询条件 1-是';
COMMENT ON COLUMN common_filter_condition.filter_id IS '所属过滤条件ID';
COMMENT ON COLUMN common_filter_condition.code IS '过滤类型code';
COMMENT ON COLUMN common_filter_condition.filter_mode IS '过滤样式( 1-多选方案1 2-多选方案2 3-多选方案3 4-分值方案 5-数值区间 6-日期区间)';
COMMENT ON COLUMN common_filter_condition.start_value IS '过滤条件开始值';
COMMENT ON COLUMN common_filter_condition.end_value IS '过滤结束值';
COMMENT ON COLUMN common_filter_condition.value_list IS '值列表';
COMMENT ON COLUMN common_filter_condition.operator IS '运算符(> < = 等)';
COMMENT ON COLUMN common_filter_condition.logic_operator IS '逻辑运算符(AND OR)';
COMMENT ON COLUMN common_show_field.id IS '主键自增长';
COMMENT ON COLUMN common_show_field.fields_list IS '自定义列表显示的字段';
COMMENT ON COLUMN common_show_field.type IS '调用的接口的标识';
COMMENT ON COLUMN common_show_field.user IS '创建的用户';
COMMENT ON COLUMN common_show_field.create_time IS '创建的时间';
COMMENT ON COLUMN common_show_field.update_time IS '修改的时间';
COMMENT ON COLUMN common_show_field.describes IS '描述';
COMMENT ON COLUMN dataset_accelerate_config.exec_type IS '执行类型(minute、hour、week、month)';
COMMENT ON COLUMN dataset_accelerate_config.exec_value IS '执行具体的值';
COMMENT ON COLUMN dataset_accelerate_config.exec_hour IS '小时';
COMMENT ON COLUMN dataset_accelerate_config.exec_minute IS '分钟';
COMMENT ON COLUMN dataset_accelerate_config.table_name IS '表名';
COMMENT ON COLUMN dataset_accelerate_config.order_field IS '排序字段';
COMMENT ON COLUMN dataset_accelerate_config.cluster_name IS '集群名称';
COMMENT ON COLUMN dataset_accelerate_config.out_datasource_type IS '输出数据源名称:clickhouse,elasticsearch,kafka';
COMMENT ON COLUMN dataset_accelerate_config.is_accelerate IS '是否加速';
COMMENT ON COLUMN dataset_accelerate_log.exec_status IS '执行状态（0-失败 1-成功）';
COMMENT ON COLUMN dataset_accelerate_log.exec_detail IS '详细信息';
COMMENT ON COLUMN dataset_accelerate_log.start_time IS '开始时间';
COMMENT ON COLUMN dataset_accelerate_log.end_time IS '结束时间';
COMMENT ON COLUMN dataset_accelerate_log.count IS '加速生成的数量';
COMMENT ON COLUMN dataset_authority.user_name IS '用户名集合';
COMMENT ON COLUMN dataset_base.dataset_name IS '数据集名称';
COMMENT ON COLUMN dataset_base.dataset_desc IS '数据集描述';
COMMENT ON COLUMN dataset_base.is_accelerate IS '是否加速';
COMMENT ON COLUMN dataset_base.from_source IS '来源(MODEL_EXPLORE-模型探索 MANUAL-手动)';
COMMENT ON COLUMN dataset_base.status IS '是否有效(0-失效 1-启用)';
COMMENT ON COLUMN dataset_base.gpl_content IS 'gpl主语句';
COMMENT ON COLUMN dataset_base.model IS '维度指标';
COMMENT ON COLUMN dataset_base.create_user IS '创建者';
COMMENT ON COLUMN dataset_base.update_user IS '最后一次更新';
COMMENT ON COLUMN dataset_base.create_time IS '创建时间';
COMMENT ON COLUMN dataset_base.update_time IS '更新时间';
COMMENT ON COLUMN dataset_base.category_id IS '分类id';
COMMENT ON COLUMN dataset_base.category_name IS '分类名称';
COMMENT ON COLUMN dataset_base.is_default IS '是否内置:0-否；1-是';
COMMENT ON COLUMN dataset_category.id IS '主键';
COMMENT ON COLUMN dataset_category.name IS '分类名称';
COMMENT ON COLUMN dataset_category.level IS '分类级别';
COMMENT ON COLUMN dataset_category.operator IS '操作人员';
COMMENT ON COLUMN dataset_category.parent_id IS '父级id';
COMMENT ON COLUMN dataset_category.create_time IS '创建时间';
COMMENT ON COLUMN dataset_category.update_time IS '更新时间';
COMMENT ON COLUMN dataset_field.dataset_id IS '数据集id';
COMMENT ON COLUMN dataset_field.field_name IS '字段名称';
COMMENT ON COLUMN dataset_gpl_filter_condition.dbquery_content IS 'dbquery内容';
COMMENT ON COLUMN dataset_gpl_filter_condition.dbquery_fields IS '模型探索跳转过来，保存所有字段';
COMMENT ON COLUMN dataset_gpl_filter_condition.gpl_condition IS 'gpl对应的过滤条件';
COMMENT ON COLUMN dataset_operation_log.operation_type IS '操作类型';
COMMENT ON COLUMN dataset_operation_log.operation_detail IS '操作详情';
COMMENT ON COLUMN dataset_operation_log.operator IS '操作人';
COMMENT ON COLUMN dataset_operation_log.create_time IS '创建时间';
COMMENT ON COLUMN dataset_table_filter_condition.dataset_id IS '数据集id';
COMMENT ON COLUMN dataset_table_filter_condition.table_name IS ' 表名';
COMMENT ON COLUMN dataset_table_filter_condition.connector IS '连接符(and or)';
COMMENT ON COLUMN dataset_table_filter_condition.where_sql IS '高级模式下的sql';
COMMENT ON COLUMN dataset_table_filter_condition.selected_field IS '选中的字段';
COMMENT ON COLUMN dataset_table_filter_condition.selected_field_type IS '选中的字段类型';
COMMENT ON COLUMN early_warn_agg.agg_field IS '汇总列名';
COMMENT ON COLUMN early_warn_agg.agg_name IS '汇总项名称';
COMMENT ON COLUMN early_warn_agg.dict_type IS '字典项';
COMMENT ON COLUMN early_warn_agg.enum_type IS '枚举项';
COMMENT ON COLUMN early_warn_agg.create_user IS '创建人';
COMMENT ON COLUMN early_warn_agg.create_time IS '创建时间';
COMMENT ON COLUMN early_warn_agg.update_user IS '修改人';
COMMENT ON COLUMN early_warn_agg.update_time IS '修改时间';
COMMENT ON COLUMN early_warn_info.classification IS '预警分类';
COMMENT ON COLUMN early_warn_info.warn_level IS '预警级别';
COMMENT ON COLUMN early_warn_info.warn_name IS '预警名称';
COMMENT ON COLUMN early_warn_info.warn_time IS '预警时间';
COMMENT ON COLUMN early_warn_info.warn_target IS '预警对象';
COMMENT ON COLUMN early_warn_info.department IS '预警部门';
COMMENT ON COLUMN early_warn_info.warn_content IS '预警内容';
COMMENT ON COLUMN early_warn_info.source_type IS '来源类型';
COMMENT ON COLUMN early_warn_info.warn_source IS '预警来源';
COMMENT ON COLUMN early_warn_info.warn_status IS '预警状态';
COMMENT ON COLUMN early_warn_info.cve_id IS 'CVEID';
COMMENT ON COLUMN early_warn_info.bug_type IS '漏洞类型';
COMMENT ON COLUMN early_warn_info.attack_type IS '攻击类型';
COMMENT ON COLUMN early_warn_info.warn_effect IS '预警后果';
COMMENT ON COLUMN early_warn_info.warn_solution IS '预警解决方案';
COMMENT ON COLUMN early_warn_info.warn_file_path IS '创建预警上传文件';
COMMENT ON COLUMN early_warn_info.device_ref IS '关联设备';
COMMENT ON COLUMN early_warn_info.service_def IS '关联服务';
COMMENT ON COLUMN early_warn_info.audit_submit_user IS '提交审核人';
COMMENT ON COLUMN early_warn_info.audit_submit_time IS '提交审核时间';
COMMENT ON COLUMN early_warn_info.audit_submit_result IS '提交审核原因';
COMMENT ON COLUMN early_warn_info.audit_submit_file_path IS '提交审核附件路径';
COMMENT ON COLUMN early_warn_info.audit_user IS '审核人';
COMMENT ON COLUMN early_warn_info.audit_time IS '审核时间';
COMMENT ON COLUMN early_warn_info.audit_result IS '审核结果';
COMMENT ON COLUMN early_warn_info.audit_desc IS '审核描述';
COMMENT ON COLUMN early_warn_info.audit_file_path IS '审核附件路径';
COMMENT ON COLUMN early_warn_info.release_user IS '发布人';
COMMENT ON COLUMN early_warn_info.release_time IS '发布时间';
COMMENT ON COLUMN early_warn_info.warn_flag IS '预警删除状态(0-删除 1-正常)';
COMMENT ON COLUMN early_warn_info.create_user IS '过滤条件所属用户';
COMMENT ON COLUMN early_warn_info.create_time IS '创建时间';
COMMENT ON COLUMN early_warn_info.update_user IS '修改人';
COMMENT ON COLUMN early_warn_info.update_time IS '修改时间';
COMMENT ON COLUMN early_warn_info.type IS '类型';
COMMENT ON COLUMN early_warn_info.start_time IS '开始时间';
COMMENT ON COLUMN early_warn_info.sync_id IS '预警id';
COMMENT ON COLUMN early_warn_info.doc_url IS '预警通报报告';
COMMENT ON COLUMN early_warn_opt_log.early_warn_id IS '预警ID';
COMMENT ON COLUMN early_warn_opt_log.opt_user IS '操作人';
COMMENT ON COLUMN early_warn_opt_log.opt_time IS '操作时间';
COMMENT ON COLUMN early_warn_opt_log.opt_type IS '操作类型';
COMMENT ON COLUMN early_warn_opt_log.opt_content IS '操作说明';
COMMENT ON COLUMN early_warn_opt_log.create_user IS '过滤条件所属用户';
COMMENT ON COLUMN early_warn_opt_log.create_time IS '创建时间';
COMMENT ON COLUMN early_warn_opt_log.update_user IS '修改人';
COMMENT ON COLUMN early_warn_opt_log.update_time IS '修改时间';
COMMENT ON COLUMN etl_asset_resource.etl_source_id IS '数据源ID';
COMMENT ON COLUMN etl_asset_resource.en_name IS '资源英文名';
COMMENT ON COLUMN etl_asset_resource.asset_purpose_id IS '资产用途，字典表id';
COMMENT ON COLUMN etl_asset_resource.classification_id IS '敏感分类，字典表id';
COMMENT ON COLUMN etl_asset_resource.asset_level_id IS '敏感分级，字典表id';
COMMENT ON COLUMN etl_asset_resource.description IS '资源描述';
COMMENT ON COLUMN etl_asset_resource.create_time IS '创建时间';
COMMENT ON COLUMN etl_asset_resource.create_user IS '创建人';
COMMENT ON COLUMN etl_asset_resource.update_time IS '更新时间';
COMMENT ON COLUMN etl_asset_resource.update_user IS '更新人';
COMMENT ON COLUMN etl_asset_resource.del_flag IS '是否已删除，0否，1是';
COMMENT ON COLUMN etl_asset_summary.asset_purpose_json IS '资产用途';
COMMENT ON COLUMN etl_asset_summary.device_type_json IS '资产类型';
COMMENT ON COLUMN etl_asset_summary.data_total_all IS '数据累计总数量';
COMMENT ON COLUMN etl_asset_summary.disk_all IS '占用磁盘总空间';
COMMENT ON COLUMN etl_asset_summary.last_7days_data_total IS '近7日新增数据量';
COMMENT ON COLUMN etl_asset_summary.last_7days_disk IS '近7日新增使用磁盘空间';
COMMENT ON COLUMN etl_asset_summary.asset_list_json IS '资产数据量';
COMMENT ON COLUMN etl_asset_summary.last_7days_asset_json IS '近7日新增资产数量排行';
COMMENT ON COLUMN etl_asset_summary.disk_total_list_json IS '资产磁盘空间占比';
COMMENT ON COLUMN etl_asset_summary.del_flag IS '是否已删除，0否1是';
COMMENT ON COLUMN etl_asset_summary.create_time IS '入库时间';
COMMENT ON COLUMN etl_dictionary_set.id IS '主键';
COMMENT ON COLUMN etl_dictionary_set.dict_name IS '字典集名称';
COMMENT ON COLUMN etl_dictionary_set.dict_desc IS '字典集描述';
COMMENT ON COLUMN etl_dictionary_set.is_default IS '是否默认 0-否 1-是';
COMMENT ON COLUMN etl_dictionary_set.dict_category IS '字典集类别 0-自定义 1-内置';
COMMENT ON COLUMN etl_dictionary_set.copy_cnt IS '复制次数';
COMMENT ON COLUMN etl_dictionary_set.delete_flag IS '删除标识 0-未删除 1-已删除';
COMMENT ON COLUMN etl_dictionary_set.create_time IS '创建时间';
COMMENT ON COLUMN etl_dictionary_set.create_user IS '创建人';
COMMENT ON COLUMN etl_dictionary_set.update_time IS '更新时间';
COMMENT ON COLUMN etl_dictionary_set.update_user IS '更新人';
COMMENT ON COLUMN etl_dictionary_set_field.id IS '主键';
COMMENT ON COLUMN etl_dictionary_set_field.dict_id IS '字典集ID';
COMMENT ON COLUMN etl_dictionary_set_field.field_name IS '字段名称';
COMMENT ON COLUMN etl_dictionary_set_field.field_type IS '字段类型';
COMMENT ON COLUMN etl_dictionary_set_field.field_desc IS '字段描述';
COMMENT ON COLUMN etl_dictionary_set_field.create_time IS '创建时间';
COMMENT ON COLUMN etl_dictionary_set_field.create_user IS '创建人';
COMMENT ON COLUMN etl_dictionary_set_field.update_time IS '更新时间';
COMMENT ON COLUMN etl_dictionary_set_field.update_user IS '更新人';
COMMENT ON COLUMN etl_dictionary_set_source.id IS '主键';
COMMENT ON COLUMN etl_dictionary_set_source.dict_id IS '字典集ID';
COMMENT ON COLUMN etl_dictionary_set_source.source_id IS '数据源ID';
COMMENT ON COLUMN etl_dictionary_set_source.create_time IS '创建时间';
COMMENT ON COLUMN etl_dictionary_set_source.create_user IS '创建人';
COMMENT ON COLUMN etl_dictionary_set_source.update_time IS '更新时间';
COMMENT ON COLUMN etl_dictionary_set_source.update_user IS '更新人';
COMMENT ON COLUMN etl_logmoudle.id IS '数据源配置ID';
COMMENT ON COLUMN etl_logmoudle.work_id IS 'work的唯一标识';
COMMENT ON COLUMN etl_logmoudle.work_ip IS 'work部署的地址';
COMMENT ON COLUMN etl_logmoudle.work_moudle IS '类型single, client, relay, gate, parser';
COMMENT ON COLUMN etl_logmoudle.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_logmoudle.create_user IS '创建者';
COMMENT ON COLUMN etl_logmoudle.create_date IS '创建日期';
COMMENT ON COLUMN etl_logmoudle.update_user IS '更新者';
COMMENT ON COLUMN etl_logmoudle.update_date IS '更新日期';
COMMENT ON COLUMN etl_logmoudle.open_port IS '开放端口';
COMMENT ON COLUMN etl_logmoudle.resource_pool IS '资源池';
COMMENT ON COLUMN etl_logmoudle.pod IS 'pod';
COMMENT ON COLUMN etl_logmoudle.network_domain IS '网络域';
COMMENT ON COLUMN etl_logmoudle.host_type IS '主机类型';
COMMENT ON COLUMN etl_logmoudle.manage_ip IS '管理ip';
COMMENT ON COLUMN etl_logmoudle.carry_network_ip IS '承载网ip';
COMMENT ON COLUMN etl_logmoudle.vpc_network_segment IS 'VPC网段';

COMMENT ON COLUMN etl_logmoudle.logmodule_tag IS '标签';
COMMENT ON COLUMN etl_logmodule_tag.tag_name IS '标签名称';
COMMENT ON COLUMN etl_logmodule_tag.tag_desc IS '标签描述';
COMMENT ON COLUMN etl_logmodule_tag_relation.logmodule_id IS '采集节点ID';
COMMENT ON COLUMN etl_logmodule_tag_relation.tag_id IS '标签';

COMMENT ON COLUMN etl_parser.id IS '解析规则ID';
COMMENT ON COLUMN etl_parser.template_id IS '规则模板ID';
COMMENT ON COLUMN etl_parser.source_id IS '数据源ID';
COMMENT ON COLUMN etl_parser.new_create IS '0-使用已有规则  1-新建规则';
COMMENT ON COLUMN etl_parser.keep_raw_data IS '是否记录原始日志（0表示不记录，1表示记录）';
COMMENT ON COLUMN etl_parser.disable_record_errdata IS '是否记录解析失败日志标识(0表示未解析，1表示已解析)';
COMMENT ON COLUMN etl_parser.create_user IS '创建者';
COMMENT ON COLUMN etl_parser.create_date IS '创建日期';
COMMENT ON COLUMN etl_parser.update_user IS '更新者';
COMMENT ON COLUMN etl_parser.update_date IS '更新日期';
COMMENT ON COLUMN etl_parser_function.id IS '解析规则ID';
COMMENT ON COLUMN etl_parser_function.function_name IS '解析器名称';
COMMENT ON COLUMN etl_parser_function.function_code IS '解析器编码';
COMMENT ON COLUMN etl_parser_function.desccription IS '解析器描述';
COMMENT ON COLUMN etl_parser_function.short_name IS '缩写名称';
COMMENT ON COLUMN etl_parser_function.icon IS '图标路径';
COMMENT ON COLUMN etl_parser_function.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_parser_function.create_user IS '创建者';
COMMENT ON COLUMN etl_parser_function.create_date IS '创建日期';
COMMENT ON COLUMN etl_parser_function.update_user IS '更新者';
COMMENT ON COLUMN etl_parser_function.update_date IS '更新日期';
COMMENT ON COLUMN etl_parser_param.id IS 'ID';
COMMENT ON COLUMN etl_parser_param.paraser_reg_id IS '解析规则ID';
COMMENT ON COLUMN etl_parser_param.source_id IS '数据源ID';
COMMENT ON COLUMN etl_parser_param.col_index IS '列';
COMMENT ON COLUMN etl_parser_param.name IS '变量标识';
COMMENT ON COLUMN etl_parser_param.sample_value IS '示例值';
COMMENT ON COLUMN etl_parser_param.format IS '数据格式';
COMMENT ON COLUMN etl_parser_param.type IS '数据类型';
COMMENT ON COLUMN etl_parser_param.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_parser_param.create_user IS '创建者';
COMMENT ON COLUMN etl_parser_param.create_date IS '创建日期';
COMMENT ON COLUMN etl_parser_param.update_user IS '更新者';
COMMENT ON COLUMN etl_parser_param.update_date IS '更新日期';
COMMENT ON COLUMN etl_parser_param_tmp.id IS 'ID';
COMMENT ON COLUMN etl_parser_param_tmp.paraser_reg_id IS '解析规则ID';
COMMENT ON COLUMN etl_parser_param_tmp.source_id IS '数据源ID';
COMMENT ON COLUMN etl_parser_param_tmp.col_index IS '列';
COMMENT ON COLUMN etl_parser_param_tmp.name IS '变量标识';
COMMENT ON COLUMN etl_parser_param_tmp.sample_value IS '示例值';
COMMENT ON COLUMN etl_parser_param_tmp.format IS '数据格式';
COMMENT ON COLUMN etl_parser_param_tmp.type IS '数据类型';
COMMENT ON COLUMN etl_parser_param_tmp.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_parser_param_tmp.create_user IS '创建者';
COMMENT ON COLUMN etl_parser_param_tmp.create_date IS '创建日期';
COMMENT ON COLUMN etl_parser_param_tmp.update_user IS '更新者';
COMMENT ON COLUMN etl_parser_param_tmp.update_date IS '更新日期';
COMMENT ON COLUMN etl_parser_reg.id IS '解析规则ID';
COMMENT ON COLUMN etl_parser_reg.name IS '解析规则名称';
COMMENT ON COLUMN etl_parser_reg.template_id IS '规则所属模板ID';
COMMENT ON COLUMN etl_parser_reg.parser_type IS '解析器编码';
COMMENT ON COLUMN etl_parser_reg.function IS '解析器名称';
COMMENT ON COLUMN etl_parser_reg.sort_id IS '正则匹配优先级';
COMMENT ON COLUMN etl_parser_reg.split1 IS '分隔符1';
COMMENT ON COLUMN etl_parser_reg.split2 IS '分隔符2';
COMMENT ON COLUMN etl_parser_reg.reg_type IS '长正则，短正则';
COMMENT ON COLUMN etl_parser_reg.parent_uuid IS '父节点正则';
COMMENT ON COLUMN etl_parser_reg.parent_column IS '父节点列名';
COMMENT ON COLUMN etl_parser_reg.action_type IS '匹配类型';
COMMENT ON COLUMN etl_parser_reg.reg_value IS '正则表达式';
COMMENT ON COLUMN etl_parser_reg.sample_log IS '样例数据';
COMMENT ON COLUMN etl_parser_reg.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_parser_reg.underline_word IS '划词信息';
COMMENT ON COLUMN etl_parser_reg_tmp.id IS '解析规则ID';
COMMENT ON COLUMN etl_parser_reg_tmp.tmp_id IS '解析规则临时ID';
COMMENT ON COLUMN etl_parser_reg_tmp.name IS '解析规则名称';
COMMENT ON COLUMN etl_parser_reg_tmp.template_id IS '规则所属模板ID';
COMMENT ON COLUMN etl_parser_reg_tmp.parser_type IS '解析器编码';
COMMENT ON COLUMN etl_parser_reg_tmp.function IS '解析器名称';
COMMENT ON COLUMN etl_parser_reg_tmp.sort_id IS '正则匹配优先级';
COMMENT ON COLUMN etl_parser_reg_tmp.split1 IS '分隔符1';
COMMENT ON COLUMN etl_parser_reg_tmp.split2 IS '分隔符2';
COMMENT ON COLUMN etl_parser_reg_tmp.reg_type IS '长正则，短正则';
COMMENT ON COLUMN etl_parser_reg_tmp.parent_uuid IS '父节点正则';
COMMENT ON COLUMN etl_parser_reg_tmp.parent_column IS '父节点列名';
COMMENT ON COLUMN etl_parser_reg_tmp.action_type IS '匹配类型';
COMMENT ON COLUMN etl_parser_reg_tmp.reg_value IS '正则表达式';
COMMENT ON COLUMN etl_parser_reg_tmp.sample_log IS '样例数据';
COMMENT ON COLUMN etl_parser_reg_tmp.underline_word IS '划词信息';
COMMENT ON COLUMN etl_parser_reg_tmp.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_parser_result.id IS '主键';
COMMENT ON COLUMN etl_parser_result.template_id IS '解析模板ID';
COMMENT ON COLUMN etl_parser_result.reg_id IS '解析规则ID';
COMMENT ON COLUMN etl_parser_result.column_name IS '解析出的列名';
COMMENT ON COLUMN etl_parser_result.show_order IS '显示顺序';
COMMENT ON COLUMN etl_parser_rule_category.id IS '主键';
COMMENT ON COLUMN etl_parser_rule_category.asset_type IS '资产类型';
COMMENT ON COLUMN etl_parser_rule_category.asset_code IS '资产编码';
COMMENT ON COLUMN etl_parser_rule_category.asset_label IS '资产标签';
COMMENT ON COLUMN etl_parser_rule_category.show_order IS '显示顺序';
COMMENT ON COLUMN etl_parser_rule_category.parent_code IS '父级编码';
COMMENT ON COLUMN etl_parser_rule_category.asset_desc IS '父级编码';
COMMENT ON COLUMN etl_parser_rule_category.create_user IS '创建人';
COMMENT ON COLUMN etl_parser_rule_category.create_time IS '创建时间';
COMMENT ON COLUMN etl_parser_rule_category.update_user IS '更新人';
COMMENT ON COLUMN etl_parser_rule_category.update_time IS '更新时间';
COMMENT ON COLUMN etl_parser_template.id IS '规则模板ID';
COMMENT ON COLUMN etl_parser_template.name IS '规则模板名称';
COMMENT ON COLUMN etl_parser_template.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_parser_template.category IS '模板类别';
COMMENT ON COLUMN etl_parser_template.parent_id IS '父ID';
COMMENT ON COLUMN etl_parser_template.template_desc IS '规则模板描述';
COMMENT ON COLUMN etl_parser_template.manage IS '规则模板是否被管理（0 表示不可以管理，1表示可以）';
COMMENT ON COLUMN etl_parser_template.create_user IS '创建者';
COMMENT ON COLUMN etl_parser_template.create_date IS '创建日期';
COMMENT ON COLUMN etl_parser_template.update_user IS '更新者';
COMMENT ON COLUMN etl_parser_template.update_date IS '更新日期';
COMMENT ON COLUMN etl_parser_template.url IS '规则文件保存类路径';
COMMENT ON COLUMN etl_parser_template.rule_from IS '规则来源  system-系统自定义 selfDefine-自定义';
COMMENT ON COLUMN etl_parser_template.copy_cnt IS '复制次数';
COMMENT ON COLUMN etl_reader_param.id IS '数据源配置ID';
COMMENT ON COLUMN etl_reader_param.source_id IS '数据源ID';
COMMENT ON COLUMN etl_reader_param.config_key IS '数据源配置项key';
COMMENT ON COLUMN etl_reader_param.config_value IS '数据源配置项值';
COMMENT ON COLUMN etl_reader_param.create_user IS '创建者';
COMMENT ON COLUMN etl_reader_param.create_date IS '创建日期';
COMMENT ON COLUMN etl_reader_param.update_user IS '更新者';
COMMENT ON COLUMN etl_reader_param.update_date IS '更新日期';
COMMENT ON COLUMN etl_reader_param_define.id IS '数据源配置项ID';
COMMENT ON COLUMN etl_reader_param_define.source_type IS '数据源类型';
COMMENT ON COLUMN etl_reader_param_define.cn_config_key IS '配置中文名';
COMMENT ON COLUMN etl_reader_param_define.config_key IS '配置';
COMMENT ON COLUMN etl_reader_param_define.config_type IS '配置类型(1-字符 2-数字 3-枚举 4-附件)';
COMMENT ON COLUMN etl_reader_param_define.config_desc IS '配置说明';
COMMENT ON COLUMN etl_reader_param_define.sort IS '显示顺序';
COMMENT ON COLUMN etl_reader_param_define.config_defalut IS '默认值';
COMMENT ON COLUMN etl_reader_param_define.is_required IS '是否必填(0-非必填 1-必填)';
COMMENT ON COLUMN etl_reader_param_define.config_level IS '参数等级(1-基础配置 2-高级配置)';
COMMENT ON COLUMN etl_reader_param_define.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_reader_param_define.create_user IS '创建者';
COMMENT ON COLUMN etl_reader_param_define.create_date IS '创建日期';
COMMENT ON COLUMN etl_reader_param_define.update_user IS '更新者';
COMMENT ON COLUMN etl_reader_param_define.update_date IS '更新日期';
COMMENT ON COLUMN etl_shell.shell_name IS '名称';
COMMENT ON COLUMN etl_shell.shell_desc IS '描述';
COMMENT ON COLUMN etl_shell.shell_content IS '脚本内容';
COMMENT ON COLUMN etl_shell.create_user IS '创建人';
COMMENT ON COLUMN etl_shell.create_time IS '创建时间';
COMMENT ON COLUMN etl_shell.update_time IS '更新时间';
COMMENT ON COLUMN etl_shell_opt_log.shell_id IS '脚本id';
COMMENT ON COLUMN etl_shell_opt_log.opt_account IS '操作人员';
COMMENT ON COLUMN etl_shell_opt_log.opt_type IS '操作类型';
COMMENT ON COLUMN etl_shell_opt_log.opt_object IS '操作对象';
COMMENT ON COLUMN etl_shell_opt_log.create_time IS '创建时间';
COMMENT ON COLUMN etl_source.id IS '数据源ID';
COMMENT ON COLUMN etl_source.source_type IS '数据源类型';
COMMENT ON COLUMN etl_source.source_name IS '数据源名称';
COMMENT ON COLUMN etl_source.logmodule_id IS 'logmoduleID(多个逗号分隔)';
COMMENT ON COLUMN etl_source.source_desc IS '数据源描述';
COMMENT ON COLUMN etl_source.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_source.source_switch IS '开关（0-关 1-开）';
COMMENT ON COLUMN etl_source.access_status IS '接入状态 0-未生效  1-已生效';
COMMENT ON COLUMN etl_source.create_user IS '创建者';
COMMENT ON COLUMN etl_source.create_date IS '创建日期';
COMMENT ON COLUMN etl_source.update_user IS '更新者';
COMMENT ON COLUMN etl_source.update_date IS '更新日期';
COMMENT ON COLUMN etl_source.keep_raw_data IS '是否记录原始日志（0表示不记录，1表示记录）';
COMMENT ON COLUMN etl_source.keep_parser_error_data IS '是否保存解析失败日志（0表示不记录，1表示记录）';
COMMENT ON COLUMN etl_source.copy_cnt IS '复制次数';
COMMENT ON COLUMN etl_source_optlog.id IS '主键';
COMMENT ON COLUMN etl_source_optlog.source_id IS '数据源id';
COMMENT ON COLUMN etl_source_optlog.opt_time IS '操作时间';
COMMENT ON COLUMN etl_source_optlog.opt_user IS '操作人';
COMMENT ON COLUMN etl_source_optlog.opt_type IS '1-修改配置 2-开启配置 3-关闭配置 4-删除配置 5-查看配置';
COMMENT ON COLUMN etl_source_set.id IS '数据源配置ID';
COMMENT ON COLUMN etl_source_set.source_id IS '数据源ID';
COMMENT ON COLUMN etl_source_set.line_tag_type IS '换行方式';
COMMENT ON COLUMN etl_source_set.line_tag IS '内容';
COMMENT ON COLUMN etl_source_set.time_tag_type IS '时间戳类型';
COMMENT ON COLUMN etl_source_set.time_tag IS '内容';
COMMENT ON COLUMN etl_source_set.charset IS '字符编码';
COMMENT ON COLUMN etl_source_set.time_format IS '时间格式';
COMMENT ON COLUMN etl_source_set.create_user IS '创建者';
COMMENT ON COLUMN etl_source_set.create_date IS '创建日期';
COMMENT ON COLUMN etl_source_set.update_user IS '更新者';
COMMENT ON COLUMN etl_source_set.update_date IS '更新日期';
COMMENT ON COLUMN etl_source_type.id IS '数据源类型编号';
COMMENT ON COLUMN etl_source_type.source_type IS '类型编码';
COMMENT ON COLUMN etl_source_type.name IS '类型名称';
COMMENT ON COLUMN etl_source_type.source_type_desc IS '描述';
COMMENT ON COLUMN etl_source_type.category IS '类型分类';
COMMENT ON COLUMN etl_source_type.sort IS '类型排序 前台根据该顺序展示';
COMMENT ON COLUMN etl_source_type.category_sort IS '分类排序 前台根据该顺序展示';
COMMENT ON COLUMN etl_source_type.icon IS '对应的icon图片路径';
COMMENT ON COLUMN etl_source_type.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_source_type.create_user IS '创建者';
COMMENT ON COLUMN etl_source_type.create_date IS '创建日期';
COMMENT ON COLUMN etl_source_type.update_user IS '更新者';
COMMENT ON COLUMN etl_source_type.update_date IS '更新日期';
COMMENT ON COLUMN etl_task_day_statistics.day IS '日期';
COMMENT ON COLUMN etl_task_day_statistics.task_count IS '采集任务数量';
COMMENT ON COLUMN etl_task_day_statistics.create_time IS '入库时间';
COMMENT ON COLUMN etl_task_day_statistics.flag IS '1有效，0无效';
COMMENT ON COLUMN etl_transform_action.id IS '主键';
COMMENT ON COLUMN etl_transform_action.rule_id IS '转换规则ID';
COMMENT ON COLUMN etl_transform_action.dest_field_type IS '目标列类型';
COMMENT ON COLUMN etl_transform_action.dest_value IS '目标列的值';
COMMENT ON COLUMN etl_transform_action.dest_column IS '目标表的列';
COMMENT ON COLUMN etl_transform_action.format_from IS '时间转换-源格式';
COMMENT ON COLUMN etl_transform_action.format_to IS '时间转换-目标格式';
COMMENT ON COLUMN etl_transform_action.start_index IS '字段截取或字段模糊的起始位置';
COMMENT ON COLUMN etl_transform_action.end_index IS '字段截取或字段模糊的结束位置';
COMMENT ON COLUMN etl_transform_action.combineChar IS '字段合并连接符';
COMMENT ON COLUMN etl_transform_action.longitude IS '经度';
COMMENT ON COLUMN etl_transform_action.latitude IS '纬度';
COMMENT ON COLUMN etl_transform_action.country IS '国家';
COMMENT ON COLUMN etl_transform_action.province IS '省份';
COMMENT ON COLUMN etl_transform_action.city IS '城市';
COMMENT ON COLUMN etl_transform_action.isp IS '运营商';
COMMENT ON COLUMN etl_transform_action.datamask_strategy IS '数据脱敏策略';
COMMENT ON COLUMN etl_transform_action.datamask_reg IS '数据脱敏策略正则';
COMMENT ON COLUMN etl_transform_action.script_type IS '脚本类型';
COMMENT ON COLUMN etl_transform_action.script_content IS '脚本内容';
COMMENT ON COLUMN etl_transform_action.split IS '字段分隔符';
COMMENT ON COLUMN etl_transform_action.kv_split IS 'KV分隔符';
COMMENT ON COLUMN etl_transform_action.sample_data IS '样例数据';
COMMENT ON COLUMN etl_transform_condition.id IS '主键';
COMMENT ON COLUMN etl_transform_condition.rule_id IS '转换规则ID';
COMMENT ON COLUMN etl_transform_condition.node_id IS '当前节点ID(备用字段)';
COMMENT ON COLUMN etl_transform_condition.parent_id IS '父ID，如果为第一个条件则父ID为-1(备用字段)';
COMMENT ON COLUMN etl_transform_condition.node_type IS '此条记录下子记录之间操作逻辑，值为and、or、not(备用字段)';
COMMENT ON COLUMN etl_transform_condition.condition_name IS '条件类型，比如事件名称、事件严重级别等(备用字段)';
COMMENT ON COLUMN etl_transform_condition.src_field IS '源列';
COMMENT ON COLUMN etl_transform_condition.logic IS '条件逻辑，值为>、<、>=、<=、!=、=、like';
COMMENT ON COLUMN etl_transform_condition.compare_table IS '待比较的表';
COMMENT ON COLUMN etl_transform_condition.compare_content IS '待比较的表字段或值';
COMMENT ON COLUMN etl_transform_dict.id IS '主键';
COMMENT ON COLUMN etl_transform_dict.dict_name IS '字典名';
COMMENT ON COLUMN etl_transform_dict.cn_dict_name IS '字典中文名';
COMMENT ON COLUMN etl_transform_dict.dict_desc IS '描述';
COMMENT ON COLUMN etl_transform_dict.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_transform_dict.from_source IS '来源（1 配置字典，2导入字典）';
COMMENT ON COLUMN etl_transform_dict.update_time IS '更新时间';
COMMENT ON COLUMN etl_transform_dict.create_time IS '创建时间';
COMMENT ON COLUMN etl_transform_dict.create_user IS '创建者';
COMMENT ON COLUMN etl_transform_dict.update_user IS '更新者';
COMMENT ON COLUMN etl_transform_dict_data.id IS '主键';
COMMENT ON COLUMN etl_transform_dict_data.dict_name IS '字典名称';
COMMENT ON COLUMN etl_transform_dict_data.k1 IS 'k1';
COMMENT ON COLUMN etl_transform_dict_data.k2 IS 'k2';
COMMENT ON COLUMN etl_transform_dict_data.k3 IS 'k3';
COMMENT ON COLUMN etl_transform_dict_data.k4 IS 'k4';
COMMENT ON COLUMN etl_transform_dict_data.k5 IS 'k5';
COMMENT ON COLUMN etl_transform_dict_data.k6 IS 'k6';
COMMENT ON COLUMN etl_transform_dict_data.k7 IS 'k7';
COMMENT ON COLUMN etl_transform_dict_data.k8 IS 'k8';
COMMENT ON COLUMN etl_transform_dict_data.k9 IS 'k9';
COMMENT ON COLUMN etl_transform_dict_data.k10 IS 'k10';
COMMENT ON COLUMN etl_transform_dict_data.k11 IS 'k11';
COMMENT ON COLUMN etl_transform_dict_data.k12 IS 'k12';
COMMENT ON COLUMN etl_transform_dict_data.k13 IS 'k13';
COMMENT ON COLUMN etl_transform_dict_data.k14 IS 'k14';
COMMENT ON COLUMN etl_transform_dict_data.k15 IS 'k15';
COMMENT ON COLUMN etl_transform_dict_data.k16 IS 'k16';
COMMENT ON COLUMN etl_transform_dict_data.k17 IS 'k17';
COMMENT ON COLUMN etl_transform_dict_data.k18 IS 'k18';
COMMENT ON COLUMN etl_transform_dict_data.k19 IS 'k19';
COMMENT ON COLUMN etl_transform_dict_data.k20 IS 'k20';
COMMENT ON COLUMN etl_transform_dict_field.id IS '主键';
COMMENT ON COLUMN etl_transform_dict_field.dict_name IS '字段名称';
COMMENT ON COLUMN etl_transform_dict_field.field_name IS '字段名';
COMMENT ON COLUMN etl_transform_dict_field.field_type IS '字段类型';
COMMENT ON COLUMN etl_transform_dict_field.field_alias_name IS '字段别名';
COMMENT ON COLUMN etl_transform_function.id IS '主键';
COMMENT ON COLUMN etl_transform_function.code IS '转换器编码';
COMMENT ON COLUMN etl_transform_function.trans_func IS '转换器类别';
COMMENT ON COLUMN etl_transform_function.description IS '转换器描述';
COMMENT ON COLUMN etl_transform_function.short_name IS '转换器简称';
COMMENT ON COLUMN etl_transform_function.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_transform_function.create_user IS '创建者';
COMMENT ON COLUMN etl_transform_function.create_date IS '创建日期';
COMMENT ON COLUMN etl_transform_function.update_user IS '更新者';
COMMENT ON COLUMN etl_transform_function.update_date IS '更新日期';
COMMENT ON COLUMN etl_transform_function.show_order IS '规则排列顺序';
COMMENT ON COLUMN etl_transform_function.parent_function IS '父级';
COMMENT ON COLUMN etl_transform_rule.id IS '主键';
COMMENT ON COLUMN etl_transform_rule.source_id IS '数据源ID';
COMMENT ON COLUMN etl_transform_rule.rule_name IS '转换规则名称';
COMMENT ON COLUMN etl_transform_rule.rule_type IS '转换类型';
COMMENT ON COLUMN etl_transform_rule.rule_func IS '转换处理函数';
COMMENT ON COLUMN etl_transform_rule.rule_desc IS '规则描述';
COMMENT ON COLUMN etl_transform_rule.rule_order IS '规则顺序';
COMMENT ON COLUMN etl_transform_rule.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_transform_rule.create_user IS '创建者';
COMMENT ON COLUMN etl_transform_rule.create_date IS '创建日期';
COMMENT ON COLUMN etl_transform_rule.update_user IS '更新者';
COMMENT ON COLUMN etl_transform_rule.update_date IS '更新日期';
COMMENT ON COLUMN etl_transform_rule.enable_advanced IS '是否启用高级配置  0-未启用  1-启用';
COMMENT ON COLUMN etl_transform_rule.parent_id IS '转换规则父节点';
COMMENT ON COLUMN etl_transform_rule.parent_type IS '转换规则父节点类型';
COMMENT ON COLUMN etl_transform_rule.preview IS '是否预览数据(0-否 1-是)';
COMMENT ON COLUMN etl_transform_table.id IS '主键';
COMMENT ON COLUMN etl_transform_table.table_name IS '表名';
COMMENT ON COLUMN etl_transform_table.cn_table_name IS '表中文名';
COMMENT ON COLUMN etl_transform_table.table_desc IS '表描述';
COMMENT ON COLUMN etl_transform_table.field_name IS '列名';
COMMENT ON COLUMN etl_transform_table.field_type IS '列类型';
COMMENT ON COLUMN etl_transform_table.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_transform_table.update_time IS '更新时间';
COMMENT ON COLUMN etl_transform_table.create_time IS '创建时间';
COMMENT ON COLUMN etl_transform_table.create_user IS '创建者';
COMMENT ON COLUMN etl_transform_table.update_user IS '更新者';
COMMENT ON COLUMN etl_wide_table_column.id IS '主键';
COMMENT ON COLUMN etl_wide_table_column.table_name IS '表名';
COMMENT ON COLUMN etl_wide_table_column.column_name IS '列名';
COMMENT ON COLUMN etl_wide_table_column.data_type IS '数据类型';
COMMENT ON COLUMN etl_wide_table_column.data_desc IS '字段描述';
COMMENT ON COLUMN etl_wide_table_column_view.id IS '主键';
COMMENT ON COLUMN etl_wide_table_column_view.view_id IS '视图id';
COMMENT ON COLUMN etl_wide_table_column_view.column_name IS '字段名称';
COMMENT ON COLUMN etl_wide_table_column_view.is_required IS '是否必填：1-必填；0-非必填';
COMMENT ON COLUMN etl_wide_table_column_view.remark IS '视图下备注字段';
COMMENT ON COLUMN etl_wide_table_view.id IS '主键';
COMMENT ON COLUMN etl_wide_table_view.view_name IS '视图名称';
COMMENT ON COLUMN etl_wide_table_view.model_view IS 'model_view';
COMMENT ON COLUMN etl_wide_table_view.view_desc IS '视图描述';
COMMENT ON COLUMN etl_writer_ch_config.id IS '主键';
COMMENT ON COLUMN etl_writer_ch_config.source_id IS '数据源ID';
COMMENT ON COLUMN etl_writer_ch_config.reg_value IS '正则表达式';
COMMENT ON COLUMN etl_writer_ch_config.time_format IS '时间格式';
COMMENT ON COLUMN etl_writer_ch_config.table_name IS '表名';
COMMENT ON COLUMN etl_writer_ch_config.order_fields IS '排序字段';
COMMENT ON COLUMN etl_writer_ch_config.cluster_display_name IS '集群名称';
COMMENT ON COLUMN etl_writer_ch_config.shard_num IS '分片数';
COMMENT ON COLUMN etl_writer_ch_config.replica_num IS '副本数';
COMMENT ON COLUMN etl_writer_ch_config.max_capacity IS '最大容量';
COMMENT ON COLUMN etl_writer_ch_config.max_keep_days IS '最大保留天数';
COMMENT ON COLUMN etl_writer_ch_config.strategy IS '映射对应策略';
COMMENT ON COLUMN etl_writer_ch_config.last_backup_index_name IS '最后一次备份到的索引名称';
COMMENT ON COLUMN etl_writer_ch_config.failed_index_name IS '归档失败的索引名称';
COMMENT ON COLUMN etl_writer_ch_config.cycle_scop IS '缩容周期';
COMMENT ON COLUMN etl_writer_ch_config.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_writer_ch_config.view_id IS '视图id';
COMMENT ON COLUMN etl_writer_ch_config.model_view IS 'model_view';
COMMENT ON COLUMN etl_writer_config.id IS '主键';
COMMENT ON COLUMN etl_writer_config.source_id IS '数据源ID';
COMMENT ON COLUMN etl_writer_config.write_type IS '输出类型ES CH等';
COMMENT ON COLUMN etl_writer_config.config_type IS '配置类型（0表示default，1表示自定义）';
COMMENT ON COLUMN etl_writer_config.enable_flag IS '是否启用配置 0-未启用 1-已启用';
COMMENT ON COLUMN etl_writer_es_config.id IS '主键';
COMMENT ON COLUMN etl_writer_es_config.source_id IS '数据源ID';
COMMENT ON COLUMN etl_writer_es_config.reg_value IS '正则表达式';
COMMENT ON COLUMN etl_writer_es_config.time_format IS '时间格式';
COMMENT ON COLUMN etl_writer_es_config.index_name IS '索引名';
COMMENT ON COLUMN etl_writer_es_config.cluster_display_name IS '集群名称';
COMMENT ON COLUMN etl_writer_es_config.shard_num IS '分片数';
COMMENT ON COLUMN etl_writer_es_config.replica_num IS '副本数';
COMMENT ON COLUMN etl_writer_es_config.max_capacity IS '最大容量';
COMMENT ON COLUMN etl_writer_es_config.max_keep_days IS '最大保留天数';
COMMENT ON COLUMN etl_writer_es_config.strategy IS '映射对应策略';
COMMENT ON COLUMN etl_writer_es_config.last_backup_index_name IS '最后一次备份到的索引名称';
COMMENT ON COLUMN etl_writer_es_config.failed_index_name IS '归档失败的索引名称';
COMMENT ON COLUMN etl_writer_es_config.cycle_scop IS '缩容周期';
COMMENT ON COLUMN etl_writer_es_config.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN etl_writer_kafka_config.source_id IS '数据源id';
COMMENT ON COLUMN etl_writer_kafka_config.topic IS 'kafka的topic';
COMMENT ON COLUMN etl_writer_kafka_config.origin_to_topic IS '输出原始文本消息到 kafka';
COMMENT ON COLUMN etl_writer_table_config.id IS '主键';
COMMENT ON COLUMN etl_writer_table_config.source_id IS '数据源ID';
COMMENT ON COLUMN etl_writer_table_config.write_type IS '入库类型';
COMMENT ON COLUMN etl_writer_table_config.column_name IS '列名';
COMMENT ON COLUMN etl_writer_table_config.data_type IS '数据类型';
COMMENT ON COLUMN etl_writer_table_config.data_name IS '来源列明';
COMMENT ON COLUMN etl_writer_table_define.id IS '主键';
COMMENT ON COLUMN etl_writer_table_define.writer_type IS '输出类型 CH-实时数仓 ES-索引库';
COMMENT ON COLUMN etl_writer_table_define.table_name IS '表名';
COMMENT ON COLUMN etl_writer_table_define.column_name IS '列名';
COMMENT ON COLUMN etl_writer_table_define.data_type IS '数据类型';
COMMENT ON COLUMN etl_writer_table_define.data_name IS '来源数据名';
COMMENT ON COLUMN etl_writer_table_define.data_desc IS '字段描述';
COMMENT ON COLUMN explore_query_condition.create_user IS '用户';
COMMENT ON COLUMN explore_query_condition.condition_name IS '查询模板名称';
COMMENT ON COLUMN explore_query_condition.condition_desc IS '查询模板描述';
COMMENT ON COLUMN explore_query_condition.write_type IS '查询类别  CH-实时数仓  ES-索引库';
COMMENT ON COLUMN explore_query_condition.table_name IS '表名或索引名';
COMMENT ON COLUMN explore_query_condition.query_condition IS '查询条件';
COMMENT ON COLUMN explore_query_condition.time_type IS '1- 快捷时间选择 2-相对时间 3-时间范围';
COMMENT ON COLUMN explore_query_condition.quick_time IS '快捷时间编码';
COMMENT ON COLUMN explore_query_condition.relative_days IS '相对时间值';
COMMENT ON COLUMN explore_query_condition.relative_type IS '相对时间类型';
COMMENT ON COLUMN explore_query_condition.relative_ontime IS '相对时间整点类型 0-当前 1-整点';
COMMENT ON COLUMN explore_query_condition.now_ontime IS '当前时间整点类型 0-当前 1-整点';
COMMENT ON COLUMN explore_query_condition.start_time IS '开始时间';
COMMENT ON COLUMN explore_query_condition.end_time IS '结束时间';
COMMENT ON COLUMN explore_query_condition.range_type IS '1-介于 2-之前 3-之后';
COMMENT ON COLUMN explore_query_history.table_name IS '表名称';
COMMENT ON COLUMN explore_query_history.quick_time IS '时间范围';
COMMENT ON COLUMN explore_query_history.query_condition IS '查询条件';
COMMENT ON COLUMN explore_query_history.write_type IS '查询类型(CH,ES)';
COMMENT ON COLUMN explore_query_history.start_time IS '开始时间';
COMMENT ON COLUMN explore_query_history.end_time IS '结束时间';
COMMENT ON COLUMN explore_query_history.extra_condition IS '额外的查询条件';
COMMENT ON COLUMN explore_query_history.create_time IS '创建时间';
COMMENT ON COLUMN explore_query_history.query_hash IS '查询条件hash值';
COMMENT ON COLUMN explore_query_history.history_type IS 'explore,gpl  分别表示模型探索，gpl';
COMMENT ON COLUMN explore_query_history.create_user IS '查询人员';
COMMENT ON COLUMN explore_selected_field.create_user IS '用户';
COMMENT ON COLUMN explore_selected_field.write_type IS '查询类别  CH-实时数仓  ES-索引库';
COMMENT ON COLUMN explore_selected_field.selected_fields IS '已选字段 多个字段用逗号分隔';
COMMENT ON COLUMN explore_selected_field.agg_fields IS '聚合字段';
COMMENT ON COLUMN explore_selected_field.table_name IS '表名或索引名';
COMMENT ON COLUMN insight_alarm_email_content_temp.topic IS '主题';
COMMENT ON COLUMN insight_alarm_email_content_temp.template_desc IS '描述';
COMMENT ON COLUMN insight_alarm_email_content_temp.email_content IS '邮件内容';
COMMENT ON COLUMN insight_alarm_email_content_temp.create_time IS '创建时间';
COMMENT ON COLUMN insight_alarm_platform_action.alarm_id IS '平台告警标识';
COMMENT ON COLUMN insight_alarm_platform_action.email_address IS '目的邮箱地址';
COMMENT ON COLUMN insight_alarm_platform_action.carbon_copy_email_address IS '抄送邮箱地址';
COMMENT ON COLUMN insight_alarm_platform_action.blind_carbon_copy_email_address IS '密件抄送地址';
COMMENT ON COLUMN insight_alarm_platform_action.email_topic IS '邮件主题';
COMMENT ON COLUMN insight_alarm_platform_action.email_content IS '邮件内容';
COMMENT ON COLUMN insight_alarm_platform_action.email_csv_field IS '邮件csv字段';
COMMENT ON COLUMN insight_alarm_platform_action.email_link_status IS '是否链接到url(0-否 1-是)';
COMMENT ON COLUMN insight_alarm_platform_action.syslog_url IS 'syslog目的地址';
COMMENT ON COLUMN insight_alarm_platform_action.syslog_csv_field IS 'syslog选择字段';
COMMENT ON COLUMN insight_alarm_platform_action.local_file_csv_field IS '本地文件字段';
COMMENT ON COLUMN insight_alarm_platform_action.phone_number IS '电话号码';
COMMENT ON COLUMN insight_alarm_platform_action.script_id IS '脚本id';
COMMENT ON COLUMN insight_alarm_platform_action.kafka_url IS 'kafka地址';
COMMENT ON COLUMN insight_alarm_platform_action.kafka_topic IS 'kafka的topic';
COMMENT ON COLUMN insight_alarm_platform_action.webhook_url IS 'webhook地址';
COMMENT ON COLUMN insight_alarm_platform_action.phone_script_id IS '短信脚本id';
COMMENT ON COLUMN insight_alarm_platform_action.warn_name IS '预警名称';
COMMENT ON COLUMN insight_alarm_platform_action.classification IS '预警分类';
COMMENT ON COLUMN insight_alarm_platform_action.warn_level IS '预警级别';
COMMENT ON COLUMN insight_alarm_platform_action.warn_content IS '预警内容';
COMMENT ON COLUMN insight_alarm_platform_action.warn_effect IS '预警后果';
COMMENT ON COLUMN insight_alarm_platform_action.warn_solution IS '预警解决方案';
COMMENT ON COLUMN insight_alarm_platform_action.warn_field IS '预警字段';
COMMENT ON COLUMN insight_alarm_platform_action.bug_type IS '漏洞类型';
COMMENT ON COLUMN insight_alarm_platform_action.cve_id IS 'CVE';
COMMENT ON COLUMN insight_alarm_platform_action.attack_type IS '攻击类型';
COMMENT ON COLUMN insight_alarm_platform_action.sys_alarm_type IS '系统告警维度';
COMMENT ON COLUMN insight_alarm_platform_action.sys_alarm_object IS '系统告警对象';
COMMENT ON COLUMN insight_alarm_platform_action.sys_alarm_serious IS '系统告警严重性，1高，2中，3低';
COMMENT ON COLUMN insight_alarm_platform_base.id IS '表标识';
COMMENT ON COLUMN insight_alarm_platform_base.alarm_name IS '名称';
COMMENT ON COLUMN insight_alarm_platform_base.alarm_code IS '规则code';
COMMENT ON COLUMN insight_alarm_platform_base.alarm_desc IS '描述';
COMMENT ON COLUMN insight_alarm_platform_base.alarm_type IS '告警类型(0-自定义 1-内置)';
COMMENT ON COLUMN insight_alarm_platform_base.operator IS '操作人员';
COMMENT ON COLUMN insight_alarm_platform_base.enable_status IS '启用状态(0-未启用 1-已经启用)';
COMMENT ON COLUMN insight_alarm_platform_base.allow_closed IS '是否允许关闭(0-不允许 1-允许)';
COMMENT ON COLUMN insight_alarm_platform_base.alarm_config IS '告警配置';
COMMENT ON COLUMN insight_alarm_platform_base.alarm_object IS '告警对象';
COMMENT ON COLUMN insight_alarm_platform_base.create_time IS '创建时间';
COMMENT ON COLUMN insight_alarm_platform_base.update_time IS '更新时间';
COMMENT ON COLUMN insight_alarm_platform_base.copy_count IS '复制次数';
COMMENT ON COLUMN insight_alarm_platform_base.execute_type IS '模型运行类型：hour-每小时，day-每天，week-每周，month-每月，cron-表达式';
COMMENT ON COLUMN insight_alarm_platform_base.execute_value IS '模型运行值';
COMMENT ON COLUMN insight_alarm_platform_base.execute_cron IS 'cron表达式';
COMMENT ON COLUMN insight_alarm_platform_base.flow_id IS '数据源ID';
COMMENT ON COLUMN insight_alarm_platform_record.id IS '表标识';
COMMENT ON COLUMN insight_alarm_platform_record.alarm_id IS '告警规则id';
COMMENT ON COLUMN insight_alarm_platform_record.alarm_title IS '告警记录标题';
COMMENT ON COLUMN insight_alarm_platform_record.alarm_content IS '告警内容';
COMMENT ON COLUMN insight_alarm_platform_record.alarm_serious IS '告警严重性，1高，2中，3低';
COMMENT ON COLUMN insight_alarm_platform_record.alarm_type IS '告警类型';
COMMENT ON COLUMN insight_alarm_platform_record.alarm_time IS '创建时间';
COMMENT ON COLUMN insight_alarm_platform_record.sys_alarm_user IS '系统告警用户';
COMMENT ON COLUMN insight_alarm_platform_record.read_status IS '已读状态，0未读，1已读';
COMMENT ON COLUMN insight_alarm_platform_record.hide_status IS '隐藏状态(0-不隐藏 1-隐藏)';
COMMENT ON COLUMN insight_alarm_platform_record.update_time IS '已读操作时间';
COMMENT ON COLUMN intranet_configuration.safe_domain_type IS '类型,json格式';
COMMENT ON COLUMN intranet_configuration.origin_param IS '原始数据类型';
COMMENT ON COLUMN intranet_configuration.is_del IS '是否删除,1是,0否';
COMMENT ON COLUMN intranet_configuration.create_time IS '创建时间';
COMMENT ON COLUMN intranet_configuration.create_user IS '创建人';
COMMENT ON COLUMN intranet_configuration.update_time IS '更新时间';
COMMENT ON COLUMN intranet_configuration.update_user IS '更新人';
COMMENT ON COLUMN intranet_configuration.member IS 'IP集合';
COMMENT ON COLUMN model_action_session_base_info.name IS '行为会话名称';
COMMENT ON COLUMN model_action_session_base_info.action_session_desc IS '描述';
COMMENT ON COLUMN model_action_session_base_info.datasource IS '数据源';
COMMENT ON COLUMN model_action_session_base_info.table_name IS '行为会话表名';
COMMENT ON COLUMN model_action_session_base_info.cluster_name IS '集群名称';
COMMENT ON COLUMN model_action_session_base_info.time_interval_unit IS '时间间隔单位';
COMMENT ON COLUMN model_action_session_base_info.store_time IS '存储时限';
COMMENT ON COLUMN model_action_session_base_info.store_time_unit IS '存储时限单位';
COMMENT ON COLUMN model_action_session_base_info.enable_advanced IS '是否启用高级配置  0-未启用  1-启用';
COMMENT ON COLUMN model_action_session_base_info.action_session_sql IS '行为会话对应的SQL';
COMMENT ON COLUMN model_action_session_base_info.action_session_table IS '行为会话对应的建表SQL';
COMMENT ON COLUMN model_action_session_base_info.session_object IS '会话对象';
COMMENT ON COLUMN model_action_session_base_info.max_session_interval_time IS '会话事件最大时间间隔';
COMMENT ON COLUMN model_action_session_base_info.max_session_interval_time_unit IS '会话事件最大时间间隔单位';
COMMENT ON COLUMN model_action_session_base_info.operation_type IS '操作类型';
COMMENT ON COLUMN model_action_session_base_info.create_time_field IS '创建时间字段名';
COMMENT ON COLUMN model_action_session_base_info.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN model_action_session_base_info.task_updatetime IS '任务更新时间';
COMMENT ON COLUMN model_action_session_base_info.task_result IS '执行结果(0-执行失败，1-执行成功)';
COMMENT ON COLUMN model_action_session_base_info.task_status IS '任务状态 1-运行中 2-运行结束 3-上线 4-下线';
COMMENT ON COLUMN model_action_session_base_info.task_nexttime IS '任务下次执行时间';
COMMENT ON COLUMN model_action_session_base_info.create_user IS '创建人';
COMMENT ON COLUMN model_action_session_base_info.create_time IS '创建时间';
COMMENT ON COLUMN model_action_session_base_info.update_user IS '修改人';
COMMENT ON COLUMN model_action_session_base_info.update_time IS '修改时间';
COMMENT ON COLUMN model_action_session_base_info.agg_time IS '时间聚合';
COMMENT ON COLUMN model_action_session_base_info.agg_time_unit IS '时间聚合单位';
COMMENT ON COLUMN model_action_session_base_info.time_interval IS '间隔时间';
COMMENT ON COLUMN model_action_session_filter.action_session_id IS 'action session ID';
COMMENT ON COLUMN model_action_session_filter.field IS '过滤字段';
COMMENT ON COLUMN model_action_session_filter.field_type IS '字段类型';
COMMENT ON COLUMN model_action_session_filter.filter_type IS '过滤类型';
COMMENT ON COLUMN model_action_session_filter.filter_value IS '过滤值';
COMMENT ON COLUMN model_action_session_filter.logic IS '逻辑运算符';
COMMENT ON COLUMN model_action_session_filter.parent_id IS '父节点ID';
COMMENT ON COLUMN model_action_session_tasklog.action_session_id IS 'action session ID';
COMMENT ON COLUMN model_action_session_tasklog.start_time IS '任务开始执行时间';
COMMENT ON COLUMN model_action_session_tasklog.end_time IS '任务执行结束时间';
COMMENT ON COLUMN model_action_session_tasklog.run_status IS '运行状态 1-运行中 2-运行结束';
COMMENT ON COLUMN model_action_session_tasklog.run_result IS '运行结果  0-运行失败 1-运行成功';
COMMENT ON COLUMN model_action_session_tasklog.exec_start_time IS '入参开始时间';
COMMENT ON COLUMN model_action_session_tasklog.exec_end_time IS '入参结束时间';
COMMENT ON COLUMN model_cube_base_info.name IS 'cube名称';
COMMENT ON COLUMN model_cube_base_info.cube_desc IS '描述';
COMMENT ON COLUMN model_cube_base_info.data_source IS '数据源';
COMMENT ON COLUMN model_cube_base_info.table_name IS 'CUBE表名';
COMMENT ON COLUMN model_cube_base_info.store_time IS '存储时限';
COMMENT ON COLUMN model_cube_base_info.store_time_unit IS '存储时限单位';
COMMENT ON COLUMN model_cube_base_info.enable_advanced IS '是否启用高级配置  0-未启用  1-启用';
COMMENT ON COLUMN model_cube_base_info.cube_sql IS 'CUBE对应的SQL';
COMMENT ON COLUMN model_cube_base_info.cube_table IS 'CUBE对应的建表SQL';
COMMENT ON COLUMN model_cube_base_info.cluster_name IS '集群名称';
COMMENT ON COLUMN model_cube_base_info.time_interval_unit IS '时间间隔单位';
COMMENT ON COLUMN model_cube_base_info.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN model_cube_base_info.task_updatetime IS '任务更新时间';
COMMENT ON COLUMN model_cube_base_info.task_result IS '执行结果(0-执行失败，1-执行成功)';
COMMENT ON COLUMN model_cube_base_info.task_status IS '任务状态 1-运行中 2-运行结束 3-上线 4-下线';
COMMENT ON COLUMN model_cube_base_info.task_nexttime IS '任务下次执行时间';
COMMENT ON COLUMN model_cube_base_info.create_user IS '创建人';
COMMENT ON COLUMN model_cube_base_info.create_time IS '创建时间';
COMMENT ON COLUMN model_cube_base_info.update_user IS '修改人';
COMMENT ON COLUMN model_cube_base_info.update_time IS '修改时间';
COMMENT ON COLUMN model_cube_base_info.agg_time IS '时间聚合';
COMMENT ON COLUMN model_cube_base_info.agg_time_unit IS '时间聚合单位';
COMMENT ON COLUMN model_cube_base_info.time_interval IS '间隔时间';
COMMENT ON COLUMN model_cube_cfg.cube_id IS 'cube ID';
COMMENT ON COLUMN model_cube_cfg.new_field IS '新增字段';
COMMENT ON COLUMN model_cube_cfg.field_category IS '字段类别(index-指标、dim-维度)';
COMMENT ON COLUMN model_cube_cfg.field_type IS '字段类型';
COMMENT ON COLUMN model_cube_cfg.origin_field IS '源表字段';
COMMENT ON COLUMN model_cube_cfg.origin_field_type IS '源表字段类型';
COMMENT ON COLUMN model_cube_cfg.operator IS '算子';
COMMENT ON COLUMN model_cube_cfg.field_desc IS '字段描述';
COMMENT ON COLUMN model_cube_filter.cube_id IS 'cube ID';
COMMENT ON COLUMN model_cube_filter.field IS '过滤字段';
COMMENT ON COLUMN model_cube_filter.field_type IS '字段类型';
COMMENT ON COLUMN model_cube_filter.filter_type IS '过滤类型';
COMMENT ON COLUMN model_cube_filter.filter_value IS '过滤值';
COMMENT ON COLUMN model_cube_filter.logic IS '逻辑运算符';
COMMENT ON COLUMN model_cube_filter.parent_id IS '父节点ID';
COMMENT ON COLUMN model_cube_tasklog.cube_id IS 'cube ID';
COMMENT ON COLUMN model_cube_tasklog.start_time IS '任务开始执行时间';
COMMENT ON COLUMN model_cube_tasklog.end_time IS '任务执行结束时间';
COMMENT ON COLUMN model_cube_tasklog.run_status IS '运行状态 1-运行中 2-运行结束';
COMMENT ON COLUMN model_cube_tasklog.run_result IS '运行结果  0-运行失败 1-运行成功';
COMMENT ON COLUMN model_cube_tasklog.exec_start_time IS '入参开始时间';
COMMENT ON COLUMN model_cube_tasklog.exec_end_time IS '入参结束时间';
COMMENT ON COLUMN recover_record.id IS 'id';
COMMENT ON COLUMN recover_record.task_name IS '任务名称';
COMMENT ON COLUMN recover_record.backup_record_id IS '备份日志ID';
COMMENT ON COLUMN recover_record.backup_time IS '备份时间';
COMMENT ON COLUMN recover_record.recover_table_name IS '恢复的表名';
COMMENT ON COLUMN recover_record.file_name IS '文件名称';
COMMENT ON COLUMN recover_record.file_path IS '文件路径';
COMMENT ON COLUMN recover_record.log_path IS '错误日志路径';
COMMENT ON COLUMN recover_record.recover_status IS '恢复状态：0-待恢复,1-恢复中；2-恢复结束';
COMMENT ON COLUMN recover_record.recover_same IS '恢复是否一致：1-是；0-否';
COMMENT ON COLUMN recover_record.backup_rows IS '备份行数';
COMMENT ON COLUMN recover_record.recover_rows IS '恢复行数';
COMMENT ON COLUMN recover_record.recover_result IS '恢复结果：1-成功；0-失败';
COMMENT ON COLUMN recover_record.recover_result_desc IS '恢复接口描述';
COMMENT ON COLUMN recover_record.create_time IS '创建时间';
COMMENT ON COLUMN recover_record.create_user IS '创建人';
COMMENT ON COLUMN recover_record.update_time IS '更新时间';
COMMENT ON COLUMN recover_record.update_user IS '更新人';
COMMENT ON COLUMN recover_record.flag IS '有效标识：1-有效；0-无效';
COMMENT ON COLUMN recover_record.recover_id IS '恢复父级id';
COMMENT ON COLUMN recover_record.recover_start_time IS '恢复分区开始时间';
COMMENT ON COLUMN recover_record.recover_end_time IS '恢复分区结束时间';
COMMENT ON COLUMN recover_record.recover_type IS '1-直接追加，2-清空表后追加；3-新表';
COMMENT ON COLUMN recover_record.execute_start_time IS '执行开始时间';
COMMENT ON COLUMN recover_record.execute_end_time IS '执行结束时间';
COMMENT ON COLUMN sys_db_version_upgrade.id IS ';';
COMMENT ON COLUMN sys_db_version_upgrade.version IS '版本描述';
COMMENT ON COLUMN sys_db_version_upgrade.module_name IS '模块名称';
COMMENT ON COLUMN sys_db_version_upgrade.description IS '版本描述';
COMMENT ON COLUMN sys_db_version_upgrade.content IS '执行脚本内容';
COMMENT ON COLUMN sys_db_version_upgrade.error_content IS '执行失败的脚本内容';
COMMENT ON COLUMN sys_db_version_upgrade.version_code IS '版本号+模块名的md5值作为防冲突依据';
COMMENT ON COLUMN sys_db_version_upgrade.status IS '成功=1; 0=未执行  2=部分成功、9=失败';
COMMENT ON COLUMN sys_db_version_upgrade.create_time IS '执行时间';
COMMENT ON COLUMN sys_db_version_upgrade.success_content IS '执行成功的脚本';
COMMENT ON COLUMN sys_db_version_upgrade.error_msg IS '错误信息';
COMMENT ON COLUMN sys_db_version_upgrade.is_atomic IS '是否原子执行';
COMMENT ON COLUMN sys_db_version_upgrade.exclude_content IS '被排除的脚本内容';
COMMENT ON COLUMN sys_db_version_upgrade_detail.id IS '版本记录ID';
COMMENT ON COLUMN sys_db_version_upgrade_detail.version IS '版本描述';
COMMENT ON COLUMN sys_db_version_upgrade_detail.module_name IS '模块名称';
COMMENT ON COLUMN sys_db_version_upgrade_detail.content IS '执行脚本内容';
COMMENT ON COLUMN sys_db_version_upgrade_detail.error_content IS '执行失败的脚本内容';
COMMENT ON COLUMN sys_db_version_upgrade_detail.success_content IS '执行成功的脚本';
COMMENT ON COLUMN sys_db_version_upgrade_detail.create_time IS '执行时间';
COMMENT ON COLUMN sys_db_version_upgrade_detail.exclude_content IS '被排除的脚本内容';
COMMENT ON COLUMN sys_import_his.model_name IS '模块名称';
COMMENT ON COLUMN sys_import_his.file_name IS '文件名称';
COMMENT ON COLUMN sys_import_his.operator IS '导入人';
COMMENT ON COLUMN sys_import_his.file_size IS '文件大小';
COMMENT ON COLUMN sys_import_his.count IS '数据总条数';
COMMENT ON COLUMN sys_import_his.success_count IS '导入成功的条数';
COMMENT ON COLUMN sys_import_his.err_count IS '导入失败的条数';
COMMENT ON COLUMN sys_import_his.status IS '导入状态';
COMMENT ON COLUMN sys_import_his.speed IS '进度(%)';
COMMENT ON COLUMN sys_import_his.basic_facts IS '概况';
COMMENT ON COLUMN sys_import_his.import_src IS '导入来源';
COMMENT ON COLUMN sys_import_his.start_time IS '开始时间';
COMMENT ON COLUMN sys_import_his.end_time IS '结束时间';
COMMENT ON COLUMN system_backup_config.id IS '主键';
COMMENT ON COLUMN system_backup_config.config_type IS '配置类型';
COMMENT ON COLUMN system_backup_config.backup_time IS '备份时间';
COMMENT ON COLUMN system_backup_config.backup_file IS '备份文件地址';
COMMENT ON COLUMN system_backup_config.status IS '0-删除 1-有效';
COMMENT ON COLUMN system_backup_config.create_time IS '创建时间';
COMMENT ON COLUMN system_backup_config.update_time IS '更新时间';
COMMENT ON COLUMN system_backup_config.config_name IS '配置名称';
COMMENT ON COLUMN system_clickhouse_config.cluster_name IS 'CH的集群名称';
COMMENT ON COLUMN system_clickhouse_config.cluster_username IS 'ch账号';
COMMENT ON COLUMN system_clickhouse_config.cluster_password IS 'ch密码';
COMMENT ON COLUMN system_clickhouse_config.ip IS 'ip地址';
COMMENT ON COLUMN system_clickhouse_config.username IS '用户名';
COMMENT ON COLUMN system_clickhouse_config.password IS '密码';
COMMENT ON COLUMN system_clickhouse_config.su_username IS '管理员用户名';
COMMENT ON COLUMN system_clickhouse_config.su_password IS '管理员密码';
COMMENT ON COLUMN system_clickhouse_config.network_card IS '网卡名称';
COMMENT ON COLUMN system_clickhouse_config.recover_tmp_dir IS 'CH备份恢复临时目录';
COMMENT ON COLUMN system_clickhouse_config.create_time IS '创建时间';
COMMENT ON COLUMN system_clickhouse_config.update_time IS '更新时间';
COMMENT ON COLUMN system_clickhouse_config.is_secret_free IS '是否免密(0-否 1-是)';
COMMENT ON COLUMN system_clickhouse_config.secret_file_path IS '是否免密(0-否 1-是)';
COMMENT ON COLUMN system_clickhouse_config.secret_file_password IS '是否免密(0-否 1-是)';
COMMENT ON COLUMN system_tables.type IS '指定类型数据库';
COMMENT ON COLUMN system_tables.database IS '数据库名';
COMMENT ON COLUMN system_tables.table_name IS '表名';
COMMENT ON COLUMN tb_filter.id IS '主键';
COMMENT ON COLUMN tb_filter.module_code IS '模块编码';
COMMENT ON COLUMN tb_filter.module_name IS '模块名称';
COMMENT ON COLUMN tb_filter.code IS '过滤条件编码';
COMMENT ON COLUMN tb_filter.name IS '过滤条件名称';
COMMENT ON COLUMN tb_filter.parent_code IS '父级编码';
COMMENT ON COLUMN tb_filter.filter_config IS '过滤配置    "valueKey":"过滤条件值key值",
    "fromTable":""
}';
COMMENT ON COLUMN tb_filter.flag IS '有效标识:0-无效；1-有效';
COMMENT ON COLUMN tb_filter.page_filter IS '是否列表页过滤条件：0-否，1-是';
COMMENT ON COLUMN tb_filter.agg_filter IS '是否聚合条件：0-否；1-是';
COMMENT ON COLUMN tb_filter.is_default IS '是否默认:0-否，1-是';
COMMENT ON COLUMN tb_filter.is_page_default IS '是否过滤默认:0-否，1-是';
COMMENT ON COLUMN tb_filter.sort_no IS '排序';
COMMENT ON COLUMN ti_bad_score_config.information_source_vendor IS '情报来源厂商';
COMMENT ON COLUMN ti_bad_score_config.source IS '情报来源';
COMMENT ON COLUMN ti_bad_score_config.score IS '基础分';
COMMENT ON COLUMN ti_bad_score_config.weight IS '权重';
COMMENT ON COLUMN ti_batch_query_detail.id IS '主键';
COMMENT ON COLUMN ti_batch_query_detail.query_task_id IS '关联的任务id';
COMMENT ON COLUMN ti_batch_query_detail.value IS '情报值';
COMMENT ON COLUMN ti_batch_query_detail.type IS '情报类型，IP,URL,FILE,DOMAIN,EMAIL';
COMMENT ON COLUMN ti_batch_query_detail.success_vendors IS '碰撞成功的情报厂商,多个度号分割';
COMMENT ON COLUMN ti_batch_query_detail.threat_types IS '威胁类型json数组';
COMMENT ON COLUMN ti_batch_query_detail.tags IS '标签json数组';
COMMENT ON COLUMN ti_batch_query_detail.is_spite IS '是否恶意，1是，0否';
COMMENT ON COLUMN ti_batch_query_detail.score IS '可信度评分';
COMMENT ON COLUMN ti_batch_query_detail.relevant_information IS '各厂商情报详情';
COMMENT ON COLUMN ti_batch_query_detail.extend_info IS '扩展信息';
COMMENT ON COLUMN ti_batch_query_detail.create_time IS '创建时间';
COMMENT ON COLUMN ti_batch_query_task.id IS '主键';
COMMENT ON COLUMN ti_batch_query_task.type IS '情报类型 IP,DOMAIN,FILE,URL,EMAIL';
COMMENT ON COLUMN ti_batch_query_task.value_arr IS '情报碰撞值json数组';
COMMENT ON COLUMN ti_batch_query_task.total_num IS '总情报数';
COMMENT ON COLUMN ti_batch_query_task.success_num IS '碰撞成功数';
COMMENT ON COLUMN ti_batch_query_task.finish_num IS '执行完成数量';
COMMENT ON COLUMN ti_batch_query_task.hit_vendors IS '碰撞的所有厂商';
COMMENT ON COLUMN ti_batch_query_task.vendor_success_info IS '各厂商情报碰撞成功数 json';
COMMENT ON COLUMN ti_batch_query_task.exec_start_time IS '执行开始时间';
COMMENT ON COLUMN ti_batch_query_task.exec_end_time IS '执行结束时间';
COMMENT ON COLUMN ti_batch_query_task.create_user IS '创建用户';
COMMENT ON COLUMN ti_batch_query_task.create_time IS '创建时间';
COMMENT ON COLUMN ti_batch_query_task.status IS '运行状态 0待执行,1运行中，2运行成功，3运行失败,4手动停止';
COMMENT ON COLUMN ti_interface_conf.id IS '主键id';
COMMENT ON COLUMN ti_interface_conf.alias_code IS '情报接口编码';
COMMENT ON COLUMN ti_interface_conf.information_source_vendor IS '情报来源厂商';
COMMENT ON COLUMN ti_interface_conf.information_source_code IS '情报来源厂商code';
COMMENT ON COLUMN ti_interface_conf.alias_name IS '名称';
COMMENT ON COLUMN ti_interface_conf.protocol IS '接口协议类型';
COMMENT ON COLUMN ti_interface_conf.ip_port IS '接口';
COMMENT ON COLUMN ti_interface_conf.api_addr IS '服务地址';
COMMENT ON COLUMN ti_interface_conf.url_param IS 'url中的参数';
COMMENT ON COLUMN ti_interface_conf.req_method IS '请求方式';
COMMENT ON COLUMN ti_interface_conf.req_header IS '请求头';
COMMENT ON COLUMN ti_interface_conf.req_body IS '请求body';
COMMENT ON COLUMN ti_interface_conf.status IS '状态 0停用 1启用';
COMMENT ON COLUMN ti_interface_conf.create_time IS '创建时间';
COMMENT ON COLUMN ti_interface_conf.update_time IS '更新时间';
COMMENT ON COLUMN ti_open_app_auth.app_name IS '应用名称';
COMMENT ON COLUMN ti_open_app_auth.token IS '认证token';
COMMENT ON COLUMN ti_open_app_auth.secret_key IS '加密秘钥';
COMMENT ON COLUMN ti_open_app_auth.auth_req_uri IS '已授权的接口URI,多个使用半角逗号隔开';
COMMENT ON COLUMN ti_open_app_auth.rate_limit IS '限流值';
COMMENT ON COLUMN ti_open_app_auth.rate_time_num IS '限流时间值';
COMMENT ON COLUMN ti_open_app_auth.rate_time_unit IS '限流时间单位：s、m、h、d';
COMMENT ON COLUMN ti_open_app_auth.enable_status IS '启用状态';
COMMENT ON COLUMN ti_open_app_auth.create_time IS '创建时间';
COMMENT ON COLUMN ti_open_app_auth.update_time IS '更新时间';
COMMENT ON COLUMN ti_open_app_auth.app_user IS '用户名称';
COMMENT ON COLUMN ti_open_app_auth.description IS '描述';
COMMENT ON COLUMN ti_open_app_auth.rate_limit_enable IS '限制并发调用次数 0限制，1不限制';
COMMENT ON COLUMN ti_open_app_auth.day_limit_num IS '天调用次数';
COMMENT ON COLUMN ti_open_app_auth.day_limit_enable IS '限制天调用次数 0限制，1不限制';
COMMENT ON COLUMN ti_open_app_auth.month_limit_num IS '月调用次数';
COMMENT ON COLUMN ti_open_app_auth.month_limit_enable IS '限制月调用次数 0限制，1不限制';
COMMENT ON COLUMN ti_open_app_auth.effective_start_time IS '调用有效开始时间';
COMMENT ON COLUMN ti_open_app_auth.effective_end_time IS '调用有效结束时间';
COMMENT ON COLUMN ti_open_app_auth.effective_time_enable IS '限制调用有效时间 0限制，1不限制';
COMMENT ON COLUMN ti_open_app_auth.temp_enable IS '临时调用状态 0不开启，1开启';
COMMENT ON COLUMN ti_open_app_auth.temp_day_limit_num IS '天调用次数';
COMMENT ON COLUMN ti_open_app_auth.temp_day_limit_enable IS '临时调用限制 0限制，1不限制';
COMMENT ON COLUMN ti_open_app_auth.temp_effective_start_time IS '临时调用有效开始时间';
COMMENT ON COLUMN ti_open_app_auth.temp_effective_end_time IS '临时调用有效结束时间';
COMMENT ON COLUMN ti_open_app_auth.idss_enable IS '是否碰撞本平台录入的情报 0否，1是';
COMMENT ON COLUMN ti_search_his.search_content IS '搜索内容';
COMMENT ON COLUMN ti_search_his.search_user IS '搜索人';
COMMENT ON COLUMN ti_search_his.type IS '情报类型，IP,DOMAIN,FILE,URL,EMAIL';
COMMENT ON COLUMN ueba_dictionary.id IS '主键';
COMMENT ON COLUMN ueba_dictionary.key_code IS '字典编码';
COMMENT ON COLUMN ueba_dictionary.value IS '字典值';
COMMENT ON COLUMN ueba_dictionary.enable IS '是否可用 (0-不可用 1-可用)';
COMMENT ON COLUMN ueba_dictionary.remark IS '备注';
COMMENT ON COLUMN ueba_dictionary.sortno IS '排序';
COMMENT ON COLUMN ueba_dictionary.type IS '字典类型';
COMMENT ON COLUMN ums_create_table_record.table_name IS '表名';
COMMENT ON COLUMN ums_create_table_record.flag IS '建表状态,1-正常 0-删除';
COMMENT ON COLUMN ums_create_table_record.create_user IS '创建者';
COMMENT ON COLUMN ums_create_table_record.update_user IS '修改者';
COMMENT ON COLUMN ums_create_table_record.create_date IS '创建时间';
COMMENT ON COLUMN ums_create_table_record.update_date IS '修改时间';
COMMENT ON COLUMN ums_sys_application.name IS '应用名称(中文)';
COMMENT ON COLUMN ums_sys_application.code IS '应用编码(英文)';
COMMENT ON COLUMN ums_sys_application.type IS '应用类型';
COMMENT ON COLUMN ums_sys_application.is_active IS '是否启用（0-不启用 1-启用）';
COMMENT ON COLUMN ums_sys_application.sso IS '单点登录';
COMMENT ON COLUMN ums_sys_application.app_desc IS '应用标识';
COMMENT ON COLUMN ums_sys_application.is_edit IS '是否可编辑（0-不可编辑 1-可编辑）';
COMMENT ON COLUMN ums_sys_application.app_release IS '版本号';
COMMENT ON COLUMN ums_sys_application.version IS 'build号';
COMMENT ON COLUMN ums_sys_auth_config.id IS '登录配置标识';
COMMENT ON COLUMN ums_sys_auth_config.auth_type IS '认证类型, 0-原生体系，1-oauth2，2-4A';
COMMENT ON COLUMN ums_sys_auth_config.enable_status IS '启用状态，0-未启用，1-启用';
COMMENT ON COLUMN ums_sys_auth_config.default_status IS '默认状态，0-非默认，1-默认';
COMMENT ON COLUMN ums_sys_auth_config.display_name IS '显示名称';
COMMENT ON COLUMN ums_sys_auth_config.sync_status IS '同步状态';
COMMENT ON COLUMN ums_sys_auth_config.enable_create_user IS '是否创建新用户，0-否，1-是';
COMMENT ON COLUMN ums_sys_auth_config.role_id IS '用户角色';
COMMENT ON COLUMN ums_sys_auth_config.grant_type IS '授权类型，0-授权式，1-隐藏式';
COMMENT ON COLUMN ums_sys_auth_config.oauth_base_field_info IS '基础字段信息';
COMMENT ON COLUMN ums_sys_auth_config.oauth_code_request_url IS '请求code地址';
COMMENT ON COLUMN ums_sys_auth_config.oauth_code_request_way IS '请求code方式，get/post';
COMMENT ON COLUMN ums_sys_auth_config.oauth_code_resp_field IS '响应code字段';
COMMENT ON COLUMN ums_sys_auth_config.oauth_code_field_info IS '请求code参数信息';
COMMENT ON COLUMN ums_sys_auth_config.oauth_token_request_url IS '请求token地址';
COMMENT ON COLUMN ums_sys_auth_config.oauth_token_request_way IS '请求token方式，get/post';
COMMENT ON COLUMN ums_sys_auth_config.oauth_token_resp_field IS '响应token字段';
COMMENT ON COLUMN ums_sys_auth_config.oauth_token_resp_format IS '响应token的格式，json/xml';
COMMENT ON COLUMN ums_sys_auth_config.oauth_token_field_info IS '请求token参数信息';
COMMENT ON COLUMN ums_sys_auth_config.oauth_user_request_url IS '请求用户地址';
COMMENT ON COLUMN ums_sys_auth_config.oauth_user_request_way IS '请求user方式，get/post';
COMMENT ON COLUMN ums_sys_auth_config.oauth_user_resp_field IS '响应user字段';
COMMENT ON COLUMN ums_sys_auth_config.oauth_user_resp_format IS '响应user的格式，json/xml';
COMMENT ON COLUMN ums_sys_auth_config.oauth_user_field_info IS '请求user参数信息';
COMMENT ON COLUMN ums_sys_auth_config.fa_app_field IS '应用标识';
COMMENT ON COLUMN ums_sys_auth_config.fa_login_url IS '登录地址';
COMMENT ON COLUMN ums_sys_auth_config.fa_auth_url IS '认证地址';
COMMENT ON COLUMN ums_sys_auth_config.fa_user_resp_field IS '响应用户字段';
COMMENT ON COLUMN ums_sys_auth_config.fa_request_protocol IS '请求协议';
COMMENT ON COLUMN ums_sys_auth_config.fa_method_name IS 'webservice方法名';
COMMENT ON COLUMN ums_sys_auth_config.fa_request_way IS 'http请求方式，get/post';
COMMENT ON COLUMN ums_sys_auth_config.fa_login_field_info IS '登录参数信息';
COMMENT ON COLUMN ums_sys_auth_config.fa_check_field_info IS '认证参数信息';
COMMENT ON COLUMN ums_sys_auth_config.fa_request_xml_template IS '请求xml报文';
COMMENT ON COLUMN ums_sys_auth_config.create_user IS '创建人';
COMMENT ON COLUMN ums_sys_auth_config.create_time IS '创建时间';
COMMENT ON COLUMN ums_sys_auth_config.update_user IS '更新人';
COMMENT ON COLUMN ums_sys_auth_config.update_time IS '更新时间';
COMMENT ON COLUMN ums_sys_auth_config.fa_vendor IS '4A厂商 默认亚信安全';
COMMENT ON COLUMN ums_sys_calendar.date IS '日期';
COMMENT ON COLUMN ums_sys_calendar.day_of_year IS '年中的第几天';
COMMENT ON COLUMN ums_sys_calendar.day_of_month IS '月中的第几天';
COMMENT ON COLUMN ums_sys_calendar.day_of_week IS '星期中的第几天';
COMMENT ON COLUMN ums_sys_calendar.is_work IS '1-工作  2-休息';
COMMENT ON COLUMN ums_sys_datasource_config.moudle_name IS '配置模块 dashboard-仪表盘  schedule-模型调度';
COMMENT ON COLUMN ums_sys_datasource_config.datasource_type IS '数据源类型';
COMMENT ON COLUMN ums_sys_datasource_config.ip IS 'IP地址';
COMMENT ON COLUMN ums_sys_datasource_config.db_name IS '数据库名';
COMMENT ON COLUMN ums_sys_datasource_config.port IS '端口';
COMMENT ON COLUMN ums_sys_datasource_config.user_name IS '用户名';
COMMENT ON COLUMN ums_sys_datasource_config.password IS '密码';
COMMENT ON COLUMN ums_sys_datasource_config.show_name IS '展示名';
COMMENT ON COLUMN ums_sys_datasource_config.time_out IS '超时时间';
COMMENT ON COLUMN ums_sys_datasource_config.encrypt IS '是否加密 1-加密 0-不加密';
COMMENT ON COLUMN ums_sys_datasource_config.status IS '1-正常 0-删除';
COMMENT ON COLUMN ums_sys_datasource_config.copy_cnt IS '复制次数';
COMMENT ON COLUMN ums_sys_datasource_config.db_url IS '数据库连接字符串';
COMMENT ON COLUMN ums_sys_datasource_config.create_user IS '创建账号';
COMMENT ON COLUMN ums_sys_datasource_config.create_time IS '创建时间';
COMMENT ON COLUMN ums_sys_datasource_config.update_user IS '更新账号';
COMMENT ON COLUMN ums_sys_datasource_config.update_time IS '更新时间';
COMMENT ON COLUMN ums_sys_holiday.type IS '类型 1-假期  2-补班';
COMMENT ON COLUMN ums_sys_holiday.name IS '名称';
COMMENT ON COLUMN ums_sys_holiday.start_date IS '开始日期';
COMMENT ON COLUMN ums_sys_holiday.end_date IS '结束日期';
COMMENT ON COLUMN ums_sys_holiday.holiday_desc IS '描述';
COMMENT ON COLUMN ums_sys_holiday.holiday_year IS '假期所属年份';
COMMENT ON COLUMN ums_sys_holiday.create_user IS '创建新者';
COMMENT ON COLUMN ums_sys_holiday.create_time IS '创建日期';
COMMENT ON COLUMN ums_sys_holiday.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_holiday.update_time IS '更新日期';
COMMENT ON COLUMN ums_sys_holiday.del_flag IS '删除标识：0-已删除；1-未删除';
COMMENT ON COLUMN ums_sys_log.user_name IS '用户名';
COMMENT ON COLUMN ums_sys_log.real_name IS '真实姓名';
COMMENT ON COLUMN ums_sys_log.login_ip IS '登录ip';
COMMENT ON COLUMN ums_sys_log.user_agent IS 'UA';
COMMENT ON COLUMN ums_sys_log.request_path IS '请求路径';
COMMENT ON COLUMN ums_sys_log.log_name IS '操作名称';
COMMENT ON COLUMN ums_sys_log.log_result IS '操作结果';
COMMENT ON COLUMN ums_sys_log.opt_type IS '操作类型';
COMMENT ON COLUMN ums_sys_log.opt_type_code IS '操作类型编码';
COMMENT ON COLUMN ums_sys_log.opt_module IS '操作模块';
COMMENT ON COLUMN ums_sys_log.opt_module_code IS '操作模块编码';
COMMENT ON COLUMN ums_sys_log.http_method IS '请求方式';
COMMENT ON COLUMN ums_sys_log.create_time IS '创建时间';
COMMENT ON COLUMN ums_sys_menus.id IS 'ID';
COMMENT ON COLUMN ums_sys_menus.menu_type IS '菜单类型(builtIn-内置菜单 custom-自定义菜单)';
COMMENT ON COLUMN ums_sys_menus.menu_property IS '菜单属性 0-不允许有子菜单 1-允许有子菜单';
COMMENT ON COLUMN ums_sys_menus.menu_level IS '菜单层级';
COMMENT ON COLUMN ums_sys_menus.menu_name IS '菜单名称';
COMMENT ON COLUMN ums_sys_menus.menu_code IS '菜单唯一标识';
COMMENT ON COLUMN ums_sys_menus.menu_path IS '菜单路径';
COMMENT ON COLUMN ums_sys_menus.hidden IS '是否隐藏';
COMMENT ON COLUMN ums_sys_menus.parent_name IS '父级菜单';
COMMENT ON COLUMN ums_sys_menus.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN ums_sys_menus.menu_order IS '菜单顺序';
COMMENT ON COLUMN ums_sys_menus.default_order IS '默认顺序';
COMMENT ON COLUMN ums_sys_menus.default_name IS '默认名称';
COMMENT ON COLUMN ums_sys_menus.default_status IS '默认状态';
COMMENT ON COLUMN ums_sys_menus.default_parent IS '默认父级';
COMMENT ON COLUMN ums_sys_menus.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_menus.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_menus.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_menus.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_menus.root_parent IS '菜单对于的一级菜单';
COMMENT ON COLUMN ums_sys_menus.menu_category IS '菜单类别 0-菜单 1-按钮';
COMMENT ON COLUMN ums_sys_menus.new_open_window IS '是否新开窗口(0-否 1-是)';
COMMENT ON COLUMN ums_sys_org.org_code IS '组织编码';
COMMENT ON COLUMN ums_sys_org.org_name IS '组织名称';
COMMENT ON COLUMN ums_sys_org.org_full_name IS '组织全称';
COMMENT ON COLUMN ums_sys_org.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_org.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_org.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_org.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_org.del_flag IS '删除标识：0-已删除；1-未删除';
COMMENT ON COLUMN ums_sys_org_dept.org_id IS '组织唯一标识';
COMMENT ON COLUMN ums_sys_org_dept.dept_id IS '部门唯一标识';
COMMENT ON COLUMN ums_sys_org_dept.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_org_dept.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_org_dept.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_org_dept.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_password_policy.min_length IS '最小字符数';
COMMENT ON COLUMN ums_sys_password_policy.max_length IS '最大长度';
COMMENT ON COLUMN ums_sys_password_policy.is_lower_case IS '是否包含小写:0-否，1-是';
COMMENT ON COLUMN ums_sys_password_policy.is_big_case IS '是否包含大写:0-否，1-是';
COMMENT ON COLUMN ums_sys_password_policy.is_special_char IS '是否包含特殊字符:0-否，1-是';
COMMENT ON COLUMN ums_sys_password_policy.rule_open IS '规则是否开启：0-否，1-是';
COMMENT ON COLUMN ums_sys_password_policy.expiration IS '密码有效期天数';
COMMENT ON COLUMN ums_sys_password_policy.alter_day IS '密码到期前提醒天数';
COMMENT ON COLUMN ums_sys_password_policy.expiration_open IS '过期时间策略是否开启：0-否，1-是';
COMMENT ON COLUMN ums_sys_password_policy.login_num IS '登录失败次数';
COMMENT ON COLUMN ums_sys_password_policy.lock_threshold IS '锁定阈值分钟';
COMMENT ON COLUMN ums_sys_password_policy.lock_time IS '锁定持续分钟';
COMMENT ON COLUMN ums_sys_password_policy.lock_open IS '锁定策略是否开启：0-否，1-是';
COMMENT ON COLUMN ums_sys_password_policy.logout_time IS '登出时间';
COMMENT ON COLUMN ums_sys_password_policy.logout_open IS '登出时间策略是否开启：0-否，1-是';
COMMENT ON COLUMN ums_sys_password_policy.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_password_policy.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_password_policy.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_password_policy.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_password_policy.flag IS '有效标识：0-无效，1-有效';
COMMENT ON COLUMN ums_sys_password_policy.is_number IS '是否包含数字：0-否，1-是';
COMMENT ON COLUMN ums_sys_password_policy.forbid_ip IS '限制远程登录地址';
COMMENT ON COLUMN ums_sys_password_policy.forbid_ip_open IS '限制远程登录地址是否开启：0-否，1-是';
COMMENT ON COLUMN ums_sys_role.role_id IS '角色编号';
COMMENT ON COLUMN ums_sys_role.role_describe IS '角色描述';
COMMENT ON COLUMN ums_sys_role.builtin IS '是否预置 0:预置 1:未预置';
COMMENT ON COLUMN ums_sys_role.del_flag IS '删除标识 0:未删除 1:已删除';
COMMENT ON COLUMN ums_sys_role.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_role.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_role.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_role.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_role.is_admin IS '是否超级管理员';
COMMENT ON COLUMN ums_sys_role.role_resource_search_limit IS '资源限制(实时搜索-角色)';
COMMENT ON COLUMN ums_sys_role.user_resource_search_limit IS '资源限制(实时搜索-用户)';
COMMENT ON COLUMN ums_sys_role_ch_es.id IS 'id';
COMMENT ON COLUMN ums_sys_role_ch_es.type IS '类型：es,ch';
COMMENT ON COLUMN ums_sys_role_ch_es.role_id IS '角色id';
COMMENT ON COLUMN ums_sys_role_ch_es.table_name IS '表名';
COMMENT ON COLUMN ums_sys_role_ch_es.query_condition IS '查询条件参数';
COMMENT ON COLUMN ums_sys_role_ch_es.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_role_ch_es.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_role_ch_es.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_role_ch_es.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_role_ch_es.flag IS '有效标识';
COMMENT ON COLUMN ums_sys_role_data.role_data_id IS '角色数据权限编号';
COMMENT ON COLUMN ums_sys_role_data.role_id IS '角色编号';
COMMENT ON COLUMN ums_sys_role_data.function_id IS '操作权限编号';
COMMENT ON COLUMN ums_sys_role_data.data_id IS '数据权限编号';
COMMENT ON COLUMN ums_sys_role_data.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_role_data.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_role_data.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_role_data.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_role_extend.extend_id IS '继承id';
COMMENT ON COLUMN ums_sys_role_extend.role_id IS '角色id';
COMMENT ON COLUMN ums_sys_role_extend.extend_role_id IS '继承的角色id';
COMMENT ON COLUMN ums_sys_role_extend.flag IS '删除标识：0-未删除，1-已删除';
COMMENT ON COLUMN ums_sys_role_function.role_function_id IS '角色操作权限编号';
COMMENT ON COLUMN ums_sys_role_function.role_id IS '角色编号';
COMMENT ON COLUMN ums_sys_role_function.function_id IS '操作权限编号';
COMMENT ON COLUMN ums_sys_role_function.value IS '值';
COMMENT ON COLUMN ums_sys_role_function.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_role_function.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_role_function.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_role_function.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_route_interface.route_code IS '路由编码';
COMMENT ON COLUMN ums_sys_route_interface.url IS '接口路径';
COMMENT ON COLUMN ums_sys_script.script_name IS '脚本名称';
COMMENT ON COLUMN ums_sys_script.script_desc IS '脚本描述';
COMMENT ON COLUMN ums_sys_script.script_content IS '脚本内容';
COMMENT ON COLUMN ums_sys_script.status IS '0-删除 1-正常';
COMMENT ON COLUMN ums_sys_script.create_user IS '创建账号';
COMMENT ON COLUMN ums_sys_script.create_time IS '创建时间';
COMMENT ON COLUMN ums_sys_script.update_time IS '更新时间';
COMMENT ON COLUMN ums_sys_script.is_alert IS '是否是工单脚本:1-是';
COMMENT ON COLUMN ums_sys_script_opt_log.script_id IS '脚本id';
COMMENT ON COLUMN ums_sys_script_opt_log.opt_account IS '操作账号';
COMMENT ON COLUMN ums_sys_script_opt_log.opt_type IS '操作类型';
COMMENT ON COLUMN ums_sys_script_opt_log.opt_object IS '操作对象';
COMMENT ON COLUMN ums_sys_script_opt_log.create_time IS '创建时间';
COMMENT ON COLUMN ums_sys_user.user_id IS '用户编号';
COMMENT ON COLUMN ums_sys_user.dept_id IS '部门编号';
COMMENT ON COLUMN ums_sys_user.password IS '密码';
COMMENT ON COLUMN ums_sys_user.password_old IS '旧密码';
COMMENT ON COLUMN ums_sys_user.period_from IS '账号使用开始日';
COMMENT ON COLUMN ums_sys_user.period_to IS '账号使用结束日';
COMMENT ON COLUMN ums_sys_user.status IS '状态 0:正常 1:锁定';
COMMENT ON COLUMN ums_sys_user.code IS '员工工号';
COMMENT ON COLUMN ums_sys_user.sex IS '员工性别 0:男 1:女';
COMMENT ON COLUMN ums_sys_user.email IS '电子邮箱';
COMMENT ON COLUMN ums_sys_user.leader IS '直属领导';
COMMENT ON COLUMN ums_sys_user.login_voucher IS '单点登录凭证';
COMMENT ON COLUMN ums_sys_user.fail_count IS '失败次数';
COMMENT ON COLUMN ums_sys_user.lock_date IS '锁定日期';
COMMENT ON COLUMN ums_sys_user.final_login_ip IS '最后登录ip';
COMMENT ON COLUMN ums_sys_user.final_login_date IS '最后登录时间';
COMMENT ON COLUMN ums_sys_user.builtin IS '是否预置 0：预置 1:未预置';
COMMENT ON COLUMN ums_sys_user.remark IS '备注';
COMMENT ON COLUMN ums_sys_user.del_flag IS '删除标识 0:未删除 1:已删除';
COMMENT ON COLUMN ums_sys_user.tenant_id IS '租户ID';
COMMENT ON COLUMN ums_sys_user.queue IS '队列名称';
COMMENT ON COLUMN ums_sys_user.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_user.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_user.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_user.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_user.social_account IS '社交账号';
COMMENT ON COLUMN ums_sys_user.first_login_fail_time IS '连续登陆失败时第一次登录时间';
COMMENT ON COLUMN ums_sys_user.is_first_login IS '是否第一次登录';
COMMENT ON COLUMN ums_sys_user.is_need_update_password IS '第一次登录时是否需要修改密码';
COMMENT ON COLUMN ums_sys_user.default_router_id IS '默认应用id';
COMMENT ON COLUMN ums_sys_user.default_router_name IS '默认应用路由';
COMMENT ON COLUMN ums_sys_user.default_dashboard IS '默认仪表盘id';
COMMENT ON COLUMN ums_sys_user.mobile IS '手机号码';
COMMENT ON COLUMN ums_sys_user.dept_name IS '部门名称';
COMMENT ON COLUMN ums_sys_user.data_limit_extend_role_id IS '限制继承的角色id';
COMMENT ON COLUMN ums_sys_user_role.user_role_id IS '人员角色编号';
COMMENT ON COLUMN ums_sys_user_role.user_id IS '用户编号';
COMMENT ON COLUMN ums_sys_user_role.role_id IS '角色编号';
COMMENT ON COLUMN ums_sys_user_role.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_user_role.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_user_role.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_user_role.update_date IS '更新日期';


CREATE UNIQUE INDEX INDEX_REPORT_NAME_IDX ON ai_report(report_name);
CREATE UNIQUE INDEX FUNCTION_CODE_IDX ON etl_parser_function(function_code);
CREATE INDEX IDX_USER_IDX ON insight_alarm_platform_record(sys_alarm_user);
CREATE INDEX BACKUP_ID_INDEX_IDX ON recover_record(backup_record_id);
CREATE UNIQUE INDEX UK_CODE_IDX ON tb_filter(module_code,code);
CREATE UNIQUE INDEX ALIAS_CODE_IDX ON ti_interface_conf(alias_code);