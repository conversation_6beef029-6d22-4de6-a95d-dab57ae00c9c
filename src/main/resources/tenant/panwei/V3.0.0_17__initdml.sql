delete from ums_sys_menus where menu_code in ('indicator-development','indicator-development-detail','user-model');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,parent_name, status, menu_order, default_order, default_name, default_status, default_parent,create_user, create_date, update_user, update_date, root_parent, application_code,application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '指标模型开发', 'indicator-development', null, '0', '0', 'model-manager', '1', 20, 20, '指标模型开发', '1','model-manager', null, null, null, null, 'model-manager', null, 'model-manager', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,parent_name, status, menu_order, default_order, default_name, default_status, default_parent,create_user, create_date, update_user, update_date, root_parent, application_code,application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, '同步开发任务', 'indicator-development-detail', null, '0', '1', 'indicator-development', '1', 10, 10, '同步开发任务', '1','indicator-development', null, null, null, null, 'model-manager', null, 'model-manager', '0', '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden,parent_name, status, menu_order, default_order, default_name, default_status, default_parent,create_user, create_date, update_user, update_date, root_parent, application_code,application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, '用户模型', 'user-model', null, '0', '0', 'model-manager', '1', 30, 30,'用户模型', '1', 'model-manager', null, null, null, null, 'model-manager', null, 'model-manager', '0', '0',0);
