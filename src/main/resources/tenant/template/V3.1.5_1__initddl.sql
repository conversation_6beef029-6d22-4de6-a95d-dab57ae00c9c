CREATE TABLE IF NOT EXISTS mt_base_account_local ON CLUSTER cluster_2x_copy
(
    `id`                  String COMMENT '唯一标识',
    `account`             String COMMENT '账号',
    `password`            Nullable(String) COMMENT '账号密码',
    `account_group`       Nullable(String) COMMENT '账号组',
    `account_type`        Nullable(String) COMMENT '账号类型',
    `dept_id`             Nullable(String) COMMENT '所属部门ID',
    `dept_name`           Nullable(String) COMMENT '所属部门名称',
    `device_id`           Nullable(String) COMMENT '所属设备ID',
    `device_ip`           Nullable(String) COMMENT '所属设备IP',
    `app_id`              Nullable(String) COMMENT '所属应用ID',
    `app_name`            Nullable(String) COMMENT '所属应用名称',
    `status`              Nullable(String) COMMENT '状态',
    `last_login_time`     Nullable(DateTime64(3)) COMMENT '最近登录时间',
    `valid_time`          Nullable(DateTime64(3)) COMMENT '账号生效时间',
    `invalid_time`        Nullable(DateTime64(3)) COMMENT '账号失效时间',
    `valid_period`        Nullable(DateTime64(3)) COMMENT '账号密码有效期',
    `person_id`           Nullable(String) COMMENT '所属自然人ID',
    `person_uuid`         Nullable(String) COMMENT '所属自然人uuid',
    `account_create_time` Nullable(DateTime64(3)) COMMENT '账号创建时间',
    `account_uuid`        Nullable(String) COMMENT '账号uuid',
    `resource`            Nullable(String) COMMENT '账号标志(0-未知 1-已知)',
    `standby1`            Nullable(String) COMMENT '备用字段1',
    `standby2`            Nullable(String) COMMENT '备用字段2',
    `standby3`            Nullable(String) COMMENT '备用字段3',
    `standby4`            Nullable(String) COMMENT '备用字段4',
    `standby5`            Nullable(String) COMMENT '备用字段5',
    `create_time`         DateTime64(3)    DEFAULT now64() COMMENT '创建时间',
    `create_user`         Nullable(String) COMMENT '创建人',
    `update_time`         DateTime64(3)    DEFAULT now64() COMMENT '更新日期',
    `update_user`         Nullable(String) COMMENT '更新人',
    `flag`                Nullable(String) DEFAULT CAST('1', 'Nullable(String)') COMMENT '删除标志(0-删除 1-正常)',
    `standby6`            Nullable(String) COMMENT 'standby6',
    `standby7`            Nullable(String) COMMENT 'standby7',
    `standby8`            Nullable(String) COMMENT 'standby8',
    `standby9`            Nullable(String) COMMENT 'standby9',
    `standby10`           Nullable(String) COMMENT 'standby10'
) ENGINE = ReplicatedMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_nx_copy}/mt_base_account_local', '{replica}')
      PARTITION BY toDate(create_time) ORDER BY create_time SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS mt_base_account ON CLUSTER cluster_2x_copy
(
    `id`                  String,
    `account`             String,
    `password`            Nullable(String),
    `account_group`       Nullable(String),
    `account_type`        Nullable(String),
    `dept_id`             Nullable(String),
    `dept_name`           Nullable(String),
    `device_id`           Nullable(String),
    `device_ip`           Nullable(String),
    `app_id`              Nullable(String),
    `app_name`            Nullable(String),
    `status`              Nullable(String),
    `last_login_time`     Nullable(DateTime64(3)),
    `valid_time`          Nullable(DateTime64(3)),
    `invalid_time`        Nullable(DateTime64(3)),
    `valid_period`        Nullable(DateTime64(3)),
    `person_id`           Nullable(String),
    `person_uuid`         Nullable(String),
    `account_create_time` Nullable(DateTime64(3)),
    `account_uuid`        Nullable(String),
    `resource`            Nullable(String),
    `standby1`            Nullable(String),
    `standby2`            Nullable(String),
    `standby3`            Nullable(String),
    `standby4`            Nullable(String),
    `standby5`            Nullable(String),
    `create_time`         DateTime64(3)    DEFAULT now64(),
    `create_user`         Nullable(String),
    `update_time`         DateTime64(3)    DEFAULT now64(),
    `update_user`         Nullable(String),
    `flag`                Nullable(String) DEFAULT CAST('1', 'Nullable(String)'),
    `standby6`            Nullable(String) COMMENT 'standby6',
    `standby7`            Nullable(String) COMMENT 'standby7',
    `standby8`            Nullable(String) COMMENT 'standby8',
    `standby9`            Nullable(String) COMMENT 'standby9',
    `standby10`           Nullable(String) COMMENT 'standby10'
) ENGINE = Distributed('cluster_nx_copy', '%s', 'mt_base_account_local', rand());

CREATE TABLE IF NOT EXISTS mt_base_application_local ON CLUSTER cluster_2x_copy
(
    `id`                 String COMMENT '唯一标志',
    `app_name`           String COMMENT '应用名称',
    `app_ip`             Nullable(String) COMMENT '应用ID',
    `app_port`           Nullable(String) COMMENT '应用端口',
    `app_url`            Nullable(String) COMMENT '应用URL',
    `dept_id`            Nullable(String) COMMENT '所属部门ID',
    `dept_name`          Nullable(String) COMMENT '所属部门名称',
    `app_type`           Nullable(String) COMMENT '应用类型',
    `app_value`          Nullable(String) COMMENT '应用价值',
    `app_network_domain` Nullable(String) COMMENT '应用网络域',
    `app_owner`          Nullable(String) COMMENT '应用责任人',
    `app_vendor`         Nullable(String) COMMENT '应用厂商',
    `resource`           Nullable(String) COMMENT '应用标志(0-未知 1-已知)',
    `app_uuid`           Nullable(String) COMMENT '应用UUID',
    `standby1`           Nullable(String) COMMENT '备用字段1',
    `standby2`           Nullable(String) COMMENT '备用字段2',
    `standby3`           Nullable(String) COMMENT '备用字段3',
    `standby4`           Nullable(String) COMMENT '备用字段4',
    `standby5`           Nullable(String) COMMENT '备用字段5',
    `create_time`        DateTime64(3)    DEFAULT now64() COMMENT '创建时间',
    `create_user`        Nullable(String) COMMENT '创建人',
    `update_time`        DateTime64(3)    DEFAULT now64() COMMENT '更新时间',
    `update_user`        Nullable(String) COMMENT '更新人',
    `flag`               Nullable(String) DEFAULT CAST('1', 'Nullable(String)') COMMENT '删除标志(0-删除 1-正常)',
    `standby6`           Nullable(String) COMMENT 'standby6',
    `standby7`           Nullable(String) COMMENT 'standby7',
    `standby8`           Nullable(String) COMMENT 'standby8',
    `standby9`           Nullable(String) COMMENT 'standby9',
    `standby10`          Nullable(String) COMMENT 'standby10'
) ENGINE = ReplicatedMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_nx_copy}/mt_base_application_local', '{replica}')
      PARTITION BY toDate(create_time) ORDER BY create_time SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS mt_base_application ON CLUSTER cluster_2x_copy
(
    `id`                 String COMMENT '唯一标志',
    `app_name`           String COMMENT '应用名称',
    `app_ip`             Nullable(String) COMMENT '应用ID',
    `app_port`           Nullable(String) COMMENT '应用端口',
    `app_url`            Nullable(String) COMMENT '应用URL',
    `dept_id`            Nullable(String) COMMENT '所属部门ID',
    `dept_name`          Nullable(String) COMMENT '所属部门名称',
    `app_type`           Nullable(String) COMMENT '应用类型',
    `app_value`          Nullable(String) COMMENT '应用价值',
    `app_network_domain` Nullable(String) COMMENT '应用网络域',
    `app_owner`          Nullable(String) COMMENT '应用责任人',
    `app_vendor`         Nullable(String) COMMENT '应用厂商',
    `resource`           Nullable(String) COMMENT '应用标志(0-未知 1-已知)',
    `app_uuid`           Nullable(String) COMMENT '应用UUID',
    `standby1`           Nullable(String) COMMENT '备用字段1',
    `standby2`           Nullable(String) COMMENT '备用字段2',
    `standby3`           Nullable(String) COMMENT '备用字段3',
    `standby4`           Nullable(String) COMMENT '备用字段4',
    `standby5`           Nullable(String) COMMENT '备用字段5',
    `create_time`        DateTime64(3)    DEFAULT now64() COMMENT '创建时间',
    `create_user`        Nullable(String) COMMENT '创建人',
    `update_time`        DateTime64(3)    DEFAULT now64() COMMENT '更新时间',
    `update_user`        Nullable(String) COMMENT '更新人',
    `flag`               Nullable(String) DEFAULT CAST('1', 'Nullable(String)') COMMENT '删除标志(0-删除 1-正常)',
    `standby6`           Nullable(String) COMMENT 'standby6',
    `standby7`           Nullable(String) COMMENT 'standby7',
    `standby8`           Nullable(String) COMMENT 'standby8',
    `standby9`           Nullable(String) COMMENT 'standby9',
    `standby10`          Nullable(String) COMMENT 'standby10'
) ENGINE = Distributed('cluster_nx_copy', '%s', 'mt_base_application_local', rand());

CREATE TABLE IF NOT EXISTS mt_base_data_local ON CLUSTER cluster_2x_copy
(
    `id`                 String COMMENT '唯一标志',
    `data_name`          String COMMENT '数据名称',
    `data_ip`            Nullable(String) COMMENT '数据所属IP',
    `app_id`             Nullable(String) COMMENT '数据所属应用ID',
    `app_name`           Nullable(String) COMMENT '数据所属应用名称',
    `dept_id`            Nullable(String) COMMENT '数据所属部门ID',
    `dept_name`          Nullable(String) COMMENT '数据所属部门名称',
    `data_db`            Nullable(String) COMMENT '数据所属数据库',
    `data_table`         Nullable(String) COMMENT '数据所属表名',
    `data_type`          Nullable(String) COMMENT '数据类型',
    `data_value`         Nullable(String) COMMENT '数据价值',
    `data_hash`          Nullable(String) COMMENT '数据Hash',
    `data_owner`         Nullable(String) COMMENT '数据所属责任人',
    `data_vendor`        Nullable(String) COMMENT '数据所属厂商',
    `resource`           Nullable(String) COMMENT '数据状态(0-未知 1-已知)',
    `data_uuid`          Nullable(String) COMMENT '数据UUID',
    `standby1`           Nullable(String) COMMENT '备用字段1',
    `standby2`           Nullable(String) COMMENT '备用字段2',
    `standby3`           Nullable(String) COMMENT '备用字段3',
    `standby4`           Nullable(String) COMMENT '备用字段4',
    `standby5`           Nullable(String) COMMENT '备用字段5',
    `create_time`        DateTime64(3)    DEFAULT now64() COMMENT '创建时间',
    `create_user`        Nullable(String) COMMENT '创建人',
    `update_time`        DateTime64(3)    DEFAULT now64() COMMENT '更新时间',
    `update_user`        Nullable(String) COMMENT '更新人',
    `flag`               Nullable(String) DEFAULT CAST('1', 'Nullable(String)') COMMENT '上传标志(0-删除 1-正常)',
    `data_sensitive`     Nullable(String) COMMENT '敏感内容',
    `data_category`      Nullable(String) COMMENT '数据分类',
    `data_level`         Nullable(String) COMMENT '数据分级:04-极敏感级、03-敏感级、02-较敏感级、01-低敏感级',
    `data_record_status` Nullable(String) COMMENT '数据备案状态:1-已备案、2-未备案、0-待确认',
    `standby6`           Nullable(String) COMMENT 'standby6',
    `standby7`           Nullable(String) COMMENT 'standby7',
    `standby8`           Nullable(String) COMMENT 'standby8',
    `standby9`           Nullable(String) COMMENT 'standby9',
    `standby10`          Nullable(String) COMMENT 'standby10'
) ENGINE = ReplicatedMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_nx_copy}/mt_base_data_local', '{replica}')
      PARTITION BY toDate(create_time) ORDER BY create_time SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS mt_base_data ON CLUSTER cluster_2x_copy
(
    `id`                 String COMMENT '唯一标志',
    `data_name`          String COMMENT '数据名称',
    `data_ip`            Nullable(String) COMMENT '数据所属IP',
    `app_id`             Nullable(String) COMMENT '数据所属应用ID',
    `app_name`           Nullable(String) COMMENT '数据所属应用名称',
    `dept_id`            Nullable(String) COMMENT '数据所属部门ID',
    `dept_name`          Nullable(String) COMMENT '数据所属部门名称',
    `data_db`            Nullable(String) COMMENT '数据所属数据库',
    `data_table`         Nullable(String) COMMENT '数据所属表名',
    `data_type`          Nullable(String) COMMENT '数据类型',
    `data_value`         Nullable(String) COMMENT '数据价值',
    `data_hash`          Nullable(String) COMMENT '数据Hash',
    `data_owner`         Nullable(String) COMMENT '数据所属责任人',
    `data_vendor`        Nullable(String) COMMENT '数据所属厂商',
    `resource`           Nullable(String) COMMENT '数据状态(0-未知 1-已知)',
    `data_uuid`          Nullable(String) COMMENT '数据UUID',
    `standby1`           Nullable(String) COMMENT '备用字段1',
    `standby2`           Nullable(String) COMMENT '备用字段2',
    `standby3`           Nullable(String) COMMENT '备用字段3',
    `standby4`           Nullable(String) COMMENT '备用字段4',
    `standby5`           Nullable(String) COMMENT '备用字段5',
    `create_time`        DateTime64(3)    DEFAULT now64() COMMENT '创建时间',
    `create_user`        Nullable(String) COMMENT '创建人',
    `update_time`        DateTime64(3)    DEFAULT now64() COMMENT '更新时间',
    `update_user`        Nullable(String) COMMENT '更新人',
    `flag`               Nullable(String) DEFAULT CAST('1', 'Nullable(String)') COMMENT '上传标志(0-删除 1-正常)',
    `data_sensitive`     Nullable(String) COMMENT '敏感内容',
    `data_category`      Nullable(String) COMMENT '数据分类',
    `data_level`         Nullable(String) COMMENT '数据分级:04-极敏感级、03-敏感级、02-较敏感级、01-低敏感级',
    `data_record_status` Nullable(String) COMMENT '数据备案状态:1-已备案、2-未备案、0-待确认',
    `standby6`           Nullable(String) COMMENT 'standby6',
    `standby7`           Nullable(String) COMMENT 'standby7',
    `standby8`           Nullable(String) COMMENT 'standby8',
    `standby9`           Nullable(String) COMMENT 'standby9',
    `standby10`          Nullable(String) COMMENT 'standby10'
) ENGINE = Distributed('cluster_nx_copy', '%s', 'mt_base_data_local', rand());

CREATE TABLE IF NOT EXISTS mt_base_device_local ON CLUSTER cluster_2x_copy
(
    `id`                    String COMMENT '唯一标志',
    `device_sn`             Nullable(String) COMMENT '设备SN号',
    `device_name`           String COMMENT '设备名称',
    `device_ip`             Nullable(String) COMMENT '设备IP',
    `device_mac`            Nullable(String) COMMENT '设备MAC',
    `dept_id`               Nullable(String) COMMENT '设备所属部门ID',
    `dept_name`             Nullable(String) COMMENT '设备所属部门名称',
    `device_type`           Nullable(String) COMMENT '设备类型',
    `device_value`          Nullable(String) COMMENT '设备价值',
    `device_buy_time`       Nullable(DateTime64(3)) COMMENT '设备购买时间',
    `device_network_domain` Nullable(String) COMMENT '设备网络域',
    `device_owner`          Nullable(String) COMMENT '设备责任人',
    `device_vendor`         Nullable(String) COMMENT '设备厂商',
    `device_status`         Nullable(String) COMMENT '设备状态',
    `device_resource`       Nullable(String) COMMENT '设备标志(0-未知 1-已知)',
    `device_uuid`           Nullable(String) COMMENT '设备uuid',
    `standby1`              Nullable(String) COMMENT '备用字段1',
    `standby2`              Nullable(String) COMMENT '备用字段2',
    `standby3`              Nullable(String) COMMENT '备用字段3',
    `standby4`              Nullable(String) COMMENT '备用字段4',
    `standby5`              Nullable(String) COMMENT '备用字段5',
    `create_time`           DateTime64(3)    DEFAULT now64() COMMENT '创建时间',
    `create_user`           Nullable(String) COMMENT '创建人',
    `update_time`           DateTime64(3)    DEFAULT now64() COMMENT '更新日期',
    `update_user`           Nullable(String) COMMENT '更新人',
    `flag`                  Nullable(String) DEFAULT CAST('1', 'Nullable(String)') COMMENT '删除标志(0-删除 1-正常)',
    `standby6`              Nullable(String) COMMENT 'standby6',
    `standby7`              Nullable(String) COMMENT 'standby7',
    `standby8`              Nullable(String) COMMENT 'standby8',
    `standby9`              Nullable(String) COMMENT 'standby9',
    `standby10`             Nullable(String) COMMENT 'standby10'
) ENGINE = ReplicatedMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_nx_copy}/mt_base_device_local', '{replica}')
      PARTITION BY toDate(create_time) ORDER BY create_time SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS mt_base_device ON CLUSTER cluster_2x_copy
(
    `id`                    String COMMENT '唯一标志',
    `device_sn`             Nullable(String) COMMENT '设备SN号',
    `device_name`           String COMMENT '设备名称',
    `device_ip`             Nullable(String) COMMENT '设备IP',
    `device_mac`            Nullable(String) COMMENT '设备MAC',
    `dept_id`               Nullable(String) COMMENT '设备所属部门ID',
    `dept_name`             Nullable(String) COMMENT '设备所属部门名称',
    `device_type`           Nullable(String) COMMENT '设备类型',
    `device_value`          Nullable(String) COMMENT '设备价值',
    `device_buy_time`       Nullable(DateTime64(3)) COMMENT '设备购买时间',
    `device_network_domain` Nullable(String) COMMENT '设备网络域',
    `device_owner`          Nullable(String) COMMENT '设备责任人',
    `device_vendor`         Nullable(String) COMMENT '设备厂商',
    `device_status`         Nullable(String) COMMENT '设备状态',
    `device_resource`       Nullable(String) COMMENT '设备标志(0-未知 1-已知)',
    `device_uuid`           Nullable(String) COMMENT '设备uuid',
    `standby1`              Nullable(String) COMMENT '备用字段1',
    `standby2`              Nullable(String) COMMENT '备用字段2',
    `standby3`              Nullable(String) COMMENT '备用字段3',
    `standby4`              Nullable(String) COMMENT '备用字段4',
    `standby5`              Nullable(String) COMMENT '备用字段5',
    `create_time`           DateTime64(3)    DEFAULT now64() COMMENT '创建时间',
    `create_user`           Nullable(String) COMMENT '创建人',
    `update_time`           DateTime64(3)    DEFAULT now64() COMMENT '更新日期',
    `update_user`           Nullable(String) COMMENT '更新人',
    `flag`                  Nullable(String) DEFAULT CAST('1', 'Nullable(String)') COMMENT '删除标志(0-删除 1-正常)',
    `standby6`              Nullable(String) COMMENT 'standby6',
    `standby7`              Nullable(String) COMMENT 'standby7',
    `standby8`              Nullable(String) COMMENT 'standby8',
    `standby9`              Nullable(String) COMMENT 'standby9',
    `standby10`             Nullable(String) COMMENT 'standby10'
) ENGINE = Distributed('cluster_nx_copy', '%s', 'mt_base_device_local', rand());

CREATE TABLE IF NOT EXISTS mt_base_person_local ON CLUSTER cluster_2x_copy
(
    `id`                   String COMMENT '唯一标志',
    `person_uuid`          String COMMENT '自然人UUID',
    `person_name`          String COMMENT '自然人名称',
    `person_sex`           Nullable(String) COMMENT '性别',
    `person_mobile`        Nullable(String) COMMENT '手机号码',
    `person_identity_card` Nullable(String) COMMENT '身份证号码',
    `person_office_phone`  Nullable(String) COMMENT '电话号码',
    `person_entry_time`    Nullable(DateTime64(3)) COMMENT '入职时间',
    `person_leave_time`    Nullable(DateTime64(3)) COMMENT '离职时间',
    `person_status`        Nullable(String) COMMENT '用户状态',
    `person_station`       Nullable(String) COMMENT '所属岗位',
    `person_sso_account`   Nullable(String) COMMENT '登录账号',
    `person_type`          Nullable(String) COMMENT '自然人类型',
    `dept_id`              Nullable(String) COMMENT '所属部门ID',
    `dept_name`            Nullable(String) COMMENT '所属部门名称',
    `person_location`      Nullable(String) COMMENT '自然人地址',
    `person_email`         Nullable(String) COMMENT '邮箱',
    `person_resource`      Nullable(String) COMMENT '自然人标志(0-未知 1-已知)',
    `person_other_info`    Nullable(String) COMMENT '自然人额外信息',
    `standby1`             Nullable(String) COMMENT '备用字段1',
    `standby2`             Nullable(String) COMMENT '备用字段2',
    `standby3`             Nullable(String) COMMENT '备用字段3',
    `standby4`             Nullable(String) COMMENT '备用字段4',
    `standby5`             Nullable(String) COMMENT '备用字段5',
    `create_time`          DateTime64(3)    DEFAULT now64() COMMENT '创建日期',
    `create_user`          Nullable(String) COMMENT '创建人',
    `update_time`          DateTime64(3)    DEFAULT now64() COMMENT '更新日期',
    `update_user`          Nullable(String) COMMENT '更新人',
    `flag`                 Nullable(String) DEFAULT CAST('1', 'Nullable(String)') COMMENT '删除标志(0-删除 1-正常)',
    `standby6`             Nullable(String) COMMENT 'standby6',
    `standby7`             Nullable(String) COMMENT 'standby7',
    `standby8`             Nullable(String) COMMENT 'standby8',
    `standby9`             Nullable(String) COMMENT 'standby9',
    `standby10`            Nullable(String) COMMENT 'standby10'
) ENGINE = ReplicatedMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_nx_copy}/mt_base_person_local', '{replica}')
      PARTITION BY toDate(create_time) ORDER BY create_time SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS mt_base_person ON CLUSTER cluster_2x_copy
(
    `id`                   String COMMENT '唯一标志',
    `person_uuid`          String COMMENT '自然人UUID',
    `person_name`          String COMMENT '自然人名称',
    `person_sex`           Nullable(String) COMMENT '性别',
    `person_mobile`        Nullable(String) COMMENT '手机号码',
    `person_identity_card` Nullable(String) COMMENT '身份证号码',
    `person_office_phone`  Nullable(String) COMMENT '电话号码',
    `person_entry_time`    Nullable(DateTime64(3)) COMMENT '入职时间',
    `person_leave_time`    Nullable(DateTime64(3)) COMMENT '离职时间',
    `person_status`        Nullable(String) COMMENT '用户状态',
    `person_station`       Nullable(String) COMMENT '所属岗位',
    `person_sso_account`   Nullable(String) COMMENT '登录账号',
    `person_type`          Nullable(String) COMMENT '自然人类型',
    `dept_id`              Nullable(String) COMMENT '所属部门ID',
    `dept_name`            Nullable(String) COMMENT '所属部门名称',
    `person_location`      Nullable(String) COMMENT '自然人地址',
    `person_email`         Nullable(String) COMMENT '邮箱',
    `person_resource`      Nullable(String) COMMENT '自然人标志(0-未知 1-已知)',
    `person_other_info`    Nullable(String) COMMENT '自然人额外信息',
    `standby1`             Nullable(String) COMMENT '备用字段1',
    `standby2`             Nullable(String) COMMENT '备用字段2',
    `standby3`             Nullable(String) COMMENT '备用字段3',
    `standby4`             Nullable(String) COMMENT '备用字段4',
    `standby5`             Nullable(String) COMMENT '备用字段5',
    `create_time`          DateTime64(3)    DEFAULT now64() COMMENT '创建日期',
    `create_user`          Nullable(String) COMMENT '创建人',
    `update_time`          DateTime64(3)    DEFAULT now64() COMMENT '更新日期',
    `update_user`          Nullable(String) COMMENT '更新人',
    `flag`                 Nullable(String) DEFAULT CAST('1', 'Nullable(String)') COMMENT '删除标志(0-删除 1-正常)',
    `standby6`             Nullable(String) COMMENT 'standby6',
    `standby7`             Nullable(String) COMMENT 'standby7',
    `standby8`             Nullable(String) COMMENT 'standby8',
    `standby9`             Nullable(String) COMMENT 'standby9',
    `standby10`            Nullable(String) COMMENT 'standby10'
) ENGINE = Distributed('cluster_nx_copy', '%s', 'mt_base_person_local', rand());