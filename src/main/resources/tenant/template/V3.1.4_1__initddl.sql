CREATE TABLE IF NOT EXISTS etl_wide_table_column_view ON CLUSTER cluster_nx_copy (
    `id` Int64 COMMENT '唯一标志',
    `view_id` Int64 COMMENT '视图id',
    `column_name` String COMMENT '字段名称',
    `is_required` Int64 COMMENT '是否必填：1-必填；0-非必填'
) ENGINE = PostgreSQL('%s','%s','etl_wide_table_column_view','%s','%s','%s');

 CREATE TABLE IF NOT EXISTS etl_wide_table_view ON CLUSTER cluster_nx_copy (
    `id` Int64 COMMENT '唯一标志',
    `view_name` String COMMENT '视图名称',
    `model_view` String COMMENT 'model_view',
    `view_desc` String COMMENT '视图描述'
) ENGINE = PostgreSQL('%s','%s','etl_wide_table_view','%s','%s','%s');

 CREATE TABLE IF NOT EXISTS etl_logmoudle ON CLUSTER cluster_nx_copy (
    `id` Int32 COMMENT '数据源配置ID',
    `work_id` Nullable(String) COMMENT 'work的唯一标识',
    `work_ip` Nullable(String) COMMENT 'work部署的地址',
    `work_moudle` Nullable(String) COMMENT '类型single,client,relay,gate,parser',
    `work_desc` Nullable(String),
    `status` Nullable(String) COMMENT '状态（0 表示禁用，1表示启用）',
    `create_user` Nullable(String) COMMENT '创建者',
    `create_date` DateTime COMMENT '创建日期',
    `update_user` Nullable(String) COMMENT '更新者',
    `update_date` DateTime COMMENT '更新日期'
) ENGINE = PostgreSQL('%s','%s','etl_logmoudle','%s','%s','%s');

CREATE TABLE IF NOT EXISTS etl_source ON CLUSTER cluster_nx_copy (
    `id` Int64 COMMENT '唯一标志',
    `source_type` String COMMENT '数据源类型',
    `source_name` String COMMENT '数据源名称',
    `logmodule_id` String COMMENT '对应logmoudleId',
    `source_desc` String COMMENT '数据源描述',
    `status` Int64 COMMENT '状态(0-禁用 1-启用)',
    `source_switch` Int64 COMMENT '开关(0-关 1-开)',
    `access_status` Int64 COMMENT '接入状态(0-未生效 1-已生效)',
    `create_user` String COMMENT '创建人',
    `create_date` DateTime COMMENT '创建时间',
    `update_user` String COMMENT '更新人',
    `update_date` DateTime COMMENT '更新时间',
    `keep_raw_data` Int64 COMMENT '是否保存原始日志(0-否 1-是)',
    `keep_parser_error_data` Int64 COMMENT '是否保存解析失败日志(0-否 1-是)',
    `copy_cnt` Int64 COMMENT '复制次数'
) ENGINE = PostgreSQL('%s','%s','etl_source','%s','%s','%s');


CREATE TABLE IF NOT EXISTS etl_reader_param ON CLUSTER cluster_nx_copy (
     `id` Int64 COMMENT '数据源配置ID',
     `source_id` Int64 COMMENT '数据源ID',
     `config_key` String COMMENT '数据源配置项key',
     `config_value` String COMMENT '数据源配置项值',
     `create_user` String COMMENT '创建者',
     `create_date` DateTime COMMENT '创建日期',
     `update_user` String COMMENT '更新者',
     `update_date` DateTime COMMENT '更新日期'
) ENGINE = PostgreSQL('%s','%s','etl_reader_param','%s','%s','%s');

 CREATE TABLE IF NOT EXISTS etl_transform_action ON CLUSTER cluster_nx_copy (
    `id` Int64 COMMENT '唯一标识',
    `rule_id` Int64 COMMENT '转换规则ID',
    `dest_field` String COMMENT '目标列',
    `dest_field_type` String COMMENT '目标列类型',
    `dest_value` String COMMENT '目标列的值',
    `dest_table` String COMMENT '目标表',
    `dest_column` String COMMENT '目标表的列',
    `format_from` String COMMENT '时间转换-源格式',
    `format_to` String COMMENT '时间转换-目标格式',
    `start_index` Int32 COMMENT '字段截取或字段模糊的起始位置',
    `end_index` Int32 COMMENT '字段截取或字段模糊的结束位置',
    `combinechar` String COMMENT '字段合并连接符',
    `longitude` String COMMENT '经度',
    `latitude` String COMMENT '纬度',
    `country` String COMMENT '国家',
    `province` String COMMENT '省份',
    `city` String COMMENT '城市',
    `isp` String COMMENT '运营商',
    `datamask_strategy` String COMMENT '数据脱敏策略',
    `datamask_reg` String COMMENT '数据脱敏正则',
    `script_type` String COMMENT '脚本类型',
    `script_content` String COMMENT '脚本内容',
    `split` String COMMENT '分隔符',
    `kv_split` String COMMENT 'KV分隔符',
    `sample_data` String COMMENT '示例数据'
) ENGINE = PostgreSQL('%s','%s', 'etl_transform_action','%s','%s','%s');

CREATE TABLE IF NOT EXISTS etl_transform_condition ON CLUSTER cluster_nx_copy (
    `id` Int64 COMMENT '唯一标志',
    `rule_id` Int64 COMMENT '转换规则ID',
    `node_id` Int64 COMMENT '当前节点ID(备用字段)',
    `parent_id` Int64 COMMENT '父节点ID(备用字段)',
    `node_type` String COMMENT '逻辑运算符and or(备用字段)',
    `condition_name` String COMMENT '条件类型(备用字段)',
    `src_field` String COMMENT '源列',
    `logic` String COMMENT '比较运算符',
    `compare_table` String COMMENT '待比较的表',
    `compare_content` String COMMENT '待比较表的字段或值'
) ENGINE = PostgreSQL('%s','%s','etl_transform_condition','%s','%s','%s');

 CREATE TABLE IF NOT EXISTS etl_transform_rule ON CLUSTER cluster_nx_copy (
    `id` Int64 COMMENT '唯一标志',
    `source_id` Int64 COMMENT '数据源ID',
    `rule_name` String COMMENT '转换规则名称',
    `rule_type` String COMMENT '转换类型',
    `rule_func` String COMMENT '转换处理函数',
    `expression` String COMMENT '转换处理正则',
    `rule_desc` String COMMENT '转换规则描述',
    `rule_order` Int32 COMMENT '转换规则顺序',
    `status` Int32 COMMENT '状态(0-禁用 1-启用)',
    `parent_id` String COMMENT '转换规则父节点',
    `parent_type` String COMMENT '转换规则父节点类型',
    `preview` Int32 COMMENT '是否预览数据(0-否 1-是)',
    `create_user` String COMMENT '创建人',
    `create_date` DateTime COMMENT '创建日期',
    `update_user` String COMMENT '更新人',
    `update_date` DateTime COMMENT '更新日期'
) ENGINE = PostgreSQL('%s','%s','etl_transform_rule','%s','%s','%s');

CREATE TABLE IF NOT EXISTS etl_transform_table ON CLUSTER cluster_nx_copy (
    `id` Int64 COMMENT '唯一标志',
    `table_name` String COMMENT '表名',
    `cn_table_name` String COMMENT '表中文名',
    `table_desc` String COMMENT '表描述',
    `field_name` String COMMENT '列名',
    `field_type` String COMMENT '列类型',
    `status` Int32 COMMENT '状态(0-禁用 1-启用)',
    `update_time` DateTime COMMENT '更新时间',
    `create_time` DateTime COMMENT '创建时间',
    `create_user` Nullable(String) COMMENT '创建人',
    `update_user` Nullable(String) COMMENT '更新人'
) ENGINE = PostgreSQL('%s','%s','etl_transform_table','%s','%s','%s');

create table intranet_configuration ON CLUSTER cluster_nx_copy (
    id               Int64 comment '主键',
    safe_domain_type String comment '类型,json格式',
    origin_param     String comment '原始数据类型',
    is_del           Int8 comment '是否删除,1是,0否',
    create_time      DateTime comment '创建时间',
    create_user      String comment '创建人',
    update_time      DateTime comment '更新时间',
    update_user      String comment '更新人'
) ENGINE = PostgreSQL('%s','%s', 'intranet_configuration', '%s','%s','%s');

CREATE TABLE IF NOT EXISTS flow_metric_local ON CLUSTER cluster_1x_copy (
    `start_time` DateTime64(3),
    `end_time` DateTime64(3),
    `node_ip` String,
    `node_type` String,
    `node_id` String,
    `flow_type` String,
    `flow_id` String,
    `source_ip` String,
    `writer_type` Nullable(String),
    `in_count` Nullable(UInt32),
    `out_count` Nullable(UInt32),
    `eps` Int32,
    `length` UInt32,
    `create_time` DateTime64(3) DEFAULT now64(),
    `uuid` UUID DEFAULT generateUUIDv4()
) ENGINE = ReplicatedMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_1x_copy}/flow_metric_local', '{replica}')
PARTITION BY toYYYYMMDD(create_time) ORDER BY create_time TTL toDateTime(create_time) + toIntervalMonth(1) SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS flow_metric ON CLUSTER cluster_1x_copy (
    `start_time` DateTime64(3),
    `end_time` DateTime64(3),
    `node_ip` String,
    `node_type` String,
    `node_id` String,
    `flow_type` String,
    `flow_id` String,
    `source_ip` String,
    `writer_type` Nullable(String),
    `in_count` Nullable(UInt32),
    `out_count` Nullable(UInt32),
    `eps` Int32,
    `length` UInt32,
    `create_time` DateTime64(3) DEFAULT now64(),
    `uuid` UUID DEFAULT generateUUIDv4()
) ENGINE = Distributed('cluster_1x_copy', '%s', 'flow_metric_local', rand());

CREATE MATERIALIZED VIEW IF NOT EXISTS mv_flow_metric_interval_1min_local ON CLUSTER cluster_1x_copy (
    `flow_id` String,
    `flow_type` String,
    `node_type` String,
    `source_ip` String,
    `interval_end_time` DateTime,
    `out_count` UInt64,
    `in_count` UInt64,
    `length` UInt64
) ENGINE = ReplicatedSummingMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_1x_copy}/mv_flow_metric_interval_1min_local','{replica}')
PARTITION BY toYYYYMM(interval_end_time)
ORDER BY (flow_id,flow_type,node_type,source_ip,interval_end_time)
SETTINGS index_granularity = 8192
POPULATE
AS
SELECT
    flow_id,
    flow_type,
    node_type,
    source_ip,
    formatDateTime(toStartOfInterval(end_time, INTERVAL 1 minute), '%Y-%m-%d %R:%S') as interval_end_time,
    sum(in_count) as in_count,
    sum(out_count) as out_count,
    sum(length) as length
FROM %s.flow_metric_local WHERE eps !=-1
GROUP BY flow_id,flow_type,node_type,source_ip,interval_end_time;

CREATE TABLE IF NOT EXISTS mv_flow_metric_interval_1min ON CLUSTER cluster_1x_copy (
    `flow_id` String,
    `flow_type` String,
    `node_type` String,
    `source_ip` String,
    `interval_end_time` DateTime,
    `out_count` UInt64,
    `in_count` UInt64,
    `length` UInt64
) ENGINE = Distributed('cluster_1x_copy', '%s', 'mv_flow_metric_interval_1min_local', rand());

CREATE MATERIALIZED VIEW IF NOT EXISTS mv_flow_metric_interval_10min_local ON CLUSTER cluster_1x_copy (
    `flow_id` String,
    `flow_type` String,
    `node_type` String,
    `source_ip` String,
    `interval_end_time` DateTime,
    `out_count` UInt64,
    `in_count` UInt64,
    `length` UInt64
) ENGINE = ReplicatedSummingMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_1x_copy}/mv_flow_metric_interval_10min_local','{replica}')
PARTITION BY toYYYYMM(interval_end_time)
ORDER BY (flow_id,flow_type,node_type,source_ip,interval_end_time)
SETTINGS index_granularity = 8192
POPULATE
AS
SELECT
    flow_id,
    flow_type,
    node_type,
    source_ip,
    formatDateTime(toStartOfInterval(end_time, INTERVAL 10 minute), '%Y-%m-%d %R:%S') as interval_end_time,
    sum(in_count) as in_count,
    sum(out_count) as out_count,
    sum(length) as length
FROM %s.flow_metric_local WHERE eps !=-1
GROUP BY flow_id,flow_type,node_type,source_ip,interval_end_time;

CREATE TABLE IF NOT EXISTS mv_flow_metric_interval_10min ON CLUSTER cluster_1x_copy (
    `flow_id` String,
    `flow_type` String,
    `node_type` String,
    `source_ip` String,
    `interval_end_time` DateTime,
    `out_count` UInt64,
    `in_count` UInt64,
    `length` UInt64
) ENGINE = Distributed('cluster_1x_copy', '%s', 'mv_flow_metric_interval_10min_local', rand());

CREATE MATERIALIZED VIEW IF NOT EXISTS mv_flow_metric_interval_1hour_local ON CLUSTER cluster_1x_copy (
    `flow_id` String,
    `flow_type` String,
    `node_type` String,
    `source_ip` String,
    `interval_end_time` DateTime,
    `out_count` UInt64,
    `in_count` UInt64,
    `length` UInt64
) ENGINE = ReplicatedSummingMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_1x_copy}/mv_flow_metric_interval_1hour_local','{replica}')
PARTITION BY toYYYYMM(interval_end_time)
ORDER BY (flow_id,flow_type,node_type,source_ip,interval_end_time)
SETTINGS index_granularity = 8192
POPULATE
AS
SELECT
    flow_id,
    flow_type,
    node_type,
    source_ip,
    formatDateTime(toStartOfInterval(end_time, INTERVAL 1 hour), '%Y-%m-%d %R:%S') as interval_end_time,
    sum(in_count) as in_count,
    sum(out_count) as out_count,
    sum(length) as length
FROM %s.flow_metric_local WHERE eps !=-1
GROUP BY flow_id,flow_type,node_type,source_ip,interval_end_time;

CREATE TABLE IF NOT EXISTS mv_flow_metric_interval_1hour ON CLUSTER cluster_1x_copy (
    `flow_id` String,
    `flow_type` String,
    `node_type` String,
    `source_ip` String,
    `interval_end_time` DateTime,
    `out_count` UInt64,
    `in_count` UInt64,
    `length` UInt64
) ENGINE = Distributed('cluster_1x_copy', '%s', 'mv_flow_metric_interval_1hour_local', rand());

CREATE MATERIALIZED VIEW IF NOT EXISTS mv_flow_metric_interval_1day_local ON CLUSTER cluster_1x_copy (
    `flow_id` String,
    `flow_type` String,
    `node_id` String,
    `node_type` String,
    `source_ip` String,
    `writer_type` String,
    `interval_end_time` DateTime,
    `out_count` UInt64,
    `length` UInt64,
    `in_count` UInt64
) ENGINE = ReplicatedSummingMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_1x_copy}/mv_flow_metric_interval_1day_local', '{replica}')
PARTITION BY toYYYYMM(interval_end_time)
ORDER BY (flow_id, flow_type, node_id, node_type, source_ip, writer_type, interval_end_time)
SETTINGS index_granularity = 8192
POPULATE
AS
SELECT
    flow_id,
    flow_type,
    node_id,
    node_type,
    source_ip,
    writer_type,
    formatDateTime(toStartOfInterval(end_time, INTERVAL 1 day), '%Y-%m-%d %R:%S') AS interval_end_time,
    sum(out_count) AS out_count,
    sum(length) AS length,
    sum(in_count) AS in_count
FROM %s.flow_metric_local WHERE eps !=-1
GROUP BY flow_id,flow_type,node_id,node_type,source_ip,writer_type,interval_end_time;

CREATE TABLE IF NOT EXISTS mv_flow_metric_interval_1day ON CLUSTER cluster_1x_copy (
    `flow_id` String,
    `flow_type` String,
    `node_id` String,
    `node_type` String,
    `source_ip` String,
    `writer_type` String,
    `interval_end_time` DateTime,
    `out_count` UInt64,
    `length` UInt64,
    `in_count` UInt64
) ENGINE = Distributed('cluster_1x_copy', '%s', 'mv_flow_metric_interval_1day_local', rand());

CREATE TABLE IF NOT EXISTS mt_system_monitor_metric_local ON CLUSTER cluster_nx_copy (
    `index` Nullable(String) DEFAULT CAST('','Nullable(String)') COMMENT '索引',
    `key` Nullable(String) DEFAULT CAST('','Nullable(String)') COMMENT '监控指标属性',
    `value` Nullable(String) DEFAULT CAST('','Nullable(String)') COMMENT '监控指标具体值',
    `time` DateTime DEFAULT now() COMMENT '执行时间',
    `ip` Nullable(String) DEFAULT CAST('','Nullable(String)') COMMENT '集群ip'
) ENGINE = ReplicatedMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_nx_copy}/mt_system_monitor_metric_local','{replica}')
PARTITION BY toYYYYMM(time) ORDER BY time TTL time + INTERVAL 1 MONTH SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS mt_system_monitor_metric ON CLUSTER cluster_nx_copy (
    `index` Nullable(String) DEFAULT CAST('','Nullable(String)') COMMENT '索引',
    `key` Nullable(String) DEFAULT CAST('', 'Nullable(String)') COMMENT '监控指标属性',
    `value` Nullable(String) DEFAULT CAST('','Nullable(String)') COMMENT '监控指标具体值',
    `time` DateTime DEFAULT now() COMMENT '执行时间',
    `ip` Nullable(String) DEFAULT CAST('','Nullable(String)') COMMENT '集群ip'
) ENGINE = Distributed('cluster_nx_copy', '%s', 'mt_system_monitor_metric_local', rand());

CREATE TABLE IF NOT EXISTS security_log_local ON CLUSTER cluster_2x_copy (
    `eqpt_ip_uuid` Nullable(String) COMMENT '数据源设备的唯一标识',
    `eqpt_ip` Nullable(String) COMMENT '设备IP地址',
    `eqpt_asset_dept` Nullable(String) COMMENT '设备IP地址资产所属部门名称',
    `eqpt_asset_name` Nullable(String) COMMENT '设备名称',
    `eqpt_asset_type` Nullable(String) COMMENT '设备IP地址资产类型',
    `eqpt_asset_value` Nullable(String) COMMENT '设备IP地址资产价值',
    `eqpt_business_system` Nullable(String) COMMENT '设备IP地址业务系统',
    `eqpt_network_domain` Nullable(String) COMMENT '设备IP地址网络域',
    `eqpt_owner` Nullable(String) COMMENT '设备IP地址资产责任人',
    `eqpt_vendor` Nullable(String) COMMENT '设备IP地址厂商',
    `ueba_flow_id` Nullable(Int32) COMMENT '数据采集任务的ID（logmodule存放采集任务ID用，以便通过ID获取对应采集任务解析出来的所有字段名）',
    `generic_uuid` Nullable(String) COMMENT '日志入库编号',
    `generic_event_id` Nullable(String) COMMENT '原始日志ID',
    `generic_collect_time` Nullable(DateTime64(3)) COMMENT '日志采集时间',
    `generic_collect_ip` Nullable(String) COMMENT '采集器IP',
    `generic_datasource_type` Nullable(String) COMMENT '数据源类型',
    `generic_event_type` Nullable(String) COMMENT '事件类型',
    `generic_opt_type` Nullable(String) COMMENT '操作类型',
    `generic_opt_object` Nullable(String) COMMENT '操作对象',
    `generic_opt_content` Nullable(String) COMMENT '操作内容',
    `generic_priority` Nullable(String) COMMENT '日志严重级别',
    `generic_raw_log` Nullable(String) COMMENT '原始日志正文',
    `result_action_id` Nullable(String) COMMENT '操作行为结果ID',
    `result_action` Nullable(String) COMMENT '操作行为结果',
    `src_person_uuid` Nullable(String) COMMENT '自然人唯一标识（hash值）',
    `src_person_uuid_data` Nullable(String) COMMENT '自然人唯一标识（明文）',
    `src_person_uuid_status` Nullable(String) COMMENT '自然人唯一标识补全状态（找不到则为“未知”，找到则为“已知”）',
    `src_person_name` Nullable(String) COMMENT '姓名',
    `src_person_status` Nullable(String) COMMENT '状态（是否在职）',
    `src_person_ctpositionname` Nullable(String) COMMENT '所属岗位',
    `src_person_types` Nullable(String) COMMENT '类型（公司员工、或第三方外包）',
    `src_person_org_name` Nullable(String) COMMENT '组织机构',
    `src_person_cellphone_no` Nullable(String) COMMENT '手机号码',
    `src_person_identity_card` Nullable(String) COMMENT '身份证号码',
    `src_person_mail` Nullable(String) COMMENT '邮箱',
    `src_person_leave_office_time` Nullable(DateTime64(3)) COMMENT '离职时间',
    `src_person_area_name` Nullable(String) COMMENT '所在地理位置',
    `src_account` Nullable(String) COMMENT '账号',
    `src_account_group` Nullable(String) COMMENT '账号组',
    `src_account_status` Nullable(String) COMMENT '状态',
    `person_mian_account` Nullable(String) COMMENT '主账号',
    `src_account_type` Nullable(String) COMMENT '账号类型',
    `src_device_uuid` Nullable(String) COMMENT '设备唯一标识(hash值)',
    `src_device_uuid_data` Nullable(String) COMMENT '设备唯一标识（明文）',
    `src_device_uuid_status` Nullable(String) COMMENT '设备唯一标识补全状态（找不到则为“未知”，找到则为“已知”）',
    `src_device_name` Nullable(String) COMMENT '设备名称',
    `src_device_ip` Nullable(String) COMMENT '设备IP',
    `src_device_ip_region` Nullable(String) COMMENT '设备IP内外网标识',
    `src_device_ip_country` Nullable(String) COMMENT '设备IP所属国家',
    `src_device_ip_province` Nullable(String) COMMENT '设备IP所属省份',
    `src_device_ip_city` Nullable(String) COMMENT '设备IP所属城市',
    `src_device_mac` Nullable(String) COMMENT '设备mac地址',
    `src_device_dept` Nullable(String) COMMENT '所属部门名称',
    `src_device_type` Nullable(String) COMMENT '设备类型',
    `src_device_owner` Nullable(String) COMMENT '资产责任人',
    `src_device_vendor` Nullable(String) COMMENT '厂商名称',
    `src_port` Nullable(Int32) COMMENT '设备端口',
    `src_device_value` Nullable(String) COMMENT '设备价值',
    `src_device_network_domain` Nullable(String) COMMENT '设备网络域',
    `dst_person_uuid` Nullable(String) COMMENT '自然人唯一标识（hash值）',
    `dst_person_uuid_data` Nullable(String) COMMENT '自然人唯一标识（明文）',
    `dst_person_uuid_status` Nullable(String) COMMENT '自然人唯一标识补全状态（找不到则为“未知”，找到则为“已知”）',
    `dst_person_name` Nullable(String) COMMENT '姓名',
    `dst_person_status` Nullable(String) COMMENT '状态（是否在职）',
    `dst_person_ctpositionname` Nullable(String) COMMENT '所属岗位',
    `dst_person_types` Nullable(String) COMMENT '类型（公司员工、或第三方外包）',
    `dst_person_org_name` Nullable(String) COMMENT '组织机构',
    `dst_person_cellphone_no` Nullable(String) COMMENT '手机号码',
    `dst_person_identity_card` Nullable(String) COMMENT '身份证号码',
    `dst_person_mail` Nullable(String) COMMENT '邮箱',
    `dst_person_mian_account` Nullable(String) COMMENT '主账号',
    `dst_person_leave_office_time` Nullable(DateTime64(3)) COMMENT '离职时间',
    `dst_person_area_name` Nullable(String) COMMENT '所在地理位置',
    `dst_account` Nullable(String) COMMENT '账号',
    `dst_account_group` Nullable(String) COMMENT '账号组',
    `dst_account_status` Nullable(String) COMMENT '状态',
    `dst_account_type` Nullable(String) COMMENT '账号类型',
    `dst_device_uuid` Nullable(String) COMMENT '设备唯一标识（hash值）',
    `dst_device_uuid_data` Nullable(String) COMMENT '设备唯一标识（明文）',
    `dst_device_uuid_status` Nullable(String) COMMENT '设备唯一标识补全状态（找不到则为“未知”，找到则为“已知”）',
    `dst_device_name` Nullable(String) COMMENT '设备名称',
    `dst_device_ip` Nullable(String) COMMENT '设备IP',
    `dst_device_ip_region` Nullable(String) COMMENT '设备IP内外网标识',
    `dst_device_ip_country` Nullable(String) COMMENT '设备IP所属国家',
    `dst_device_ip_province` Nullable(String) COMMENT '设备IP所属省份',
    `dst_device_ip_city` Nullable(String) COMMENT '设备IP所属城市',
    `dst_device_mac` Nullable(String) COMMENT '设备mac地址',
    `dst_device_dept` Nullable(String) COMMENT '所属部门名称',
    `dst_device_type` Nullable(String) COMMENT '设备类型',
    `dst_device_owner` Nullable(String) COMMENT '资产责任人',
    `dst_device_vendor` Nullable(String) COMMENT '厂商名称',
    `dst_device_value` Nullable(String) COMMENT '设备价值',
    `dst_device_network_domain` Nullable(String) COMMENT '设备网络域',
    `dst_app_uuid` Nullable(String) COMMENT '应用唯一标识（hash值）',
    `dst_app_uuid_data` Nullable(String) COMMENT '应用唯一标识（明文）',
    `dst_app_uuid_status` Nullable(String) COMMENT '应用唯一标识补全状态（找不到则为“未知”，找到则为“已知”）',
    `dst_app_name` Nullable(String) COMMENT '应用名称',
    `dst_port` Nullable(Int32) COMMENT '应用所属端口',
    `dst_app_url` Nullable(String) COMMENT '应用URL',
    `dst_app_dept` Nullable(String) COMMENT '所属部门名称',
    `dst_app_type` Nullable(String) COMMENT '应用类型',
    `dst_app_value` Nullable(String) COMMENT '应用价值',
    `dst_app_network_domain` Nullable(String) COMMENT '应用网络域',
    `dst_app_owner` Nullable(String) COMMENT '资产责任人',
    `dst_app_vendor` Nullable(String) COMMENT '厂商名称',
    `data_uuid` Nullable(String) COMMENT '数据唯一标识（hash值）',
    `data_uuid_data` Nullable(String) COMMENT '数据唯一标识（明文）',
    `data_uuid_status` Nullable(String) COMMENT '数据唯一标识补全状态（找不到则为“未知”，找到则为“已知”）',
    `data_name` Nullable(String) COMMENT '数据名称',
    `data_hash` Nullable(String) COMMENT '数据哈希值',
    `data_dept` Nullable(String) COMMENT '所属部门名称',
    `data_type` Nullable(String) COMMENT '数据类型',
    `data_value` Nullable(String) COMMENT '数据价值',
    `data_owner` Nullable(String) COMMENT '数据所属负责人',
    `database_access_privilege` Nullable(String) COMMENT '数据库访问权限',
    `database_access_time` Nullable(DateTime64(3)) COMMENT '数据库访问时间',
    `database_id` Nullable(String) COMMENT '数据库ID',
    `database_name` Nullable(String) COMMENT '数据库名',
    `database_table_name` Nullable(String) COMMENT '数据库表名',
    `database_sql` Nullable(String) COMMENT '数据库SQL语句',
    `database_system_type` Nullable(String) COMMENT '数据库系统类型',
    `database_type` Nullable(String) COMMENT '数据库类型',
    `file_access_privilege` Nullable(String) COMMENT '文件访问权限',
    `file_access_time` Nullable(DateTime64(3)) COMMENT '文件访问时间',
    `file_create_time` Nullable(DateTime64(3)) COMMENT '文件创建时间',
    `file_hash` Nullable(String) COMMENT '文件HASH值',
    `file_id` Nullable(String) COMMENT '文件ID',
    `file_modify_time` Nullable(DateTime64(3)) COMMENT '文件修改时间',
    `file_name` Nullable(String) COMMENT '文件名',
    `file_path` Nullable(String) COMMENT '文件路径',
    `file_size` Nullable(Int64) COMMENT '文件大小',
    `file_system_type` Nullable(String) COMMENT '文件系统类型',
    `file_type` Nullable(String) COMMENT '文件类型',
    `file_share_local_path` Nullable(String) COMMENT '共享路径',
    `file_share_name` Nullable(String) COMMENT '共享名称',
    `file_state` Nullable(String) COMMENT '文件状态',
    `program_name` Nullable(String) COMMENT '程序名称',
    `mail_src_username` Nullable(String) COMMENT '发件人',
    `mail_dst_username` Nullable(String) COMMENT '收件人',
    `mail_bcc_username` Nullable(String) COMMENT '密送人',
    `mail_cc_username` Nullable(String) COMMENT '抄送人',
    `mail_head` Nullable(String) COMMENT '邮件头',
    `mail_title` Nullable(String) COMMENT '邮件标题',
    `mail_content` Nullable(String) COMMENT '邮件内容',
    `mail_type` Nullable(String) COMMENT '邮件格式',
    `http_url_externalurl` Nullable(String) COMMENT '访问URL',
    `http_url_externalurl_type` Nullable(String) COMMENT '访问URL的类型（外部、内部）',
    `http_url_externalurl_domain` Nullable(String) COMMENT '访问URL的域名',
    `http_url_externalurl_parameter` Nullable(String) COMMENT '访问URL的参数',
    `http_url_httpreferrer` Nullable(String) COMMENT '来源URL',
    `http_url_httpreferrer_type` Nullable(String) COMMENT '来源URL的类型（外部、内部）',
    `http_httpagent` Nullable(String) COMMENT 'User-Agent',
    `http_httpmethod` Nullable(String) COMMENT 'http请求方法',
    `http_httpprotocol` Nullable(String) COMMENT 'http协议',
    `http_httpprotocolversion` Nullable(String) COMMENT 'http协议班恩',
    `http_httpstatus` Nullable(String) COMMENT 'http返回状态',
    `http_cookie` Nullable(String) COMMENT '浏览器缓存',
    `http_req_content_type` Nullable(String) COMMENT '请求内容类型',
    `http_request_body` Nullable(String) COMMENT '请求内容',
    `http_request_size` Nullable(Int64) COMMENT '请求包的大小',
    `http_response_body` Nullable(String) COMMENT '响应内容',
    `http_response_datetime` Nullable(DateTime64(3)) COMMENT '响应时间',
    `http_response_size` Nullable(Int64) COMMENT '返回包的大小',
    `http_response_time` Nullable(DateTime64(3)) COMMENT '响应时间',
    `http_rsp_content_type` Nullable(String) COMMENT '返回内容类型',
    `http_x_forwarded_for` Nullable(String) COMMENT '代理IP地址',
    `http_accept` Nullable(String) COMMENT '接受的类型',
    `http_accept_charset` Nullable(String) COMMENT '接受的字符集',
    `http_accept_encoding` Nullable(String) COMMENT '接受的字符编码',
    `http_accept_language` Nullable(String) COMMENT '接受的语言',
    `http_expired_date` Nullable(String) COMMENT '过期时间',
    `http_connection_status` Nullable(String) COMMENT '连接状态',
    `process_pid` Nullable(String) COMMENT '进程ID',
    `process_p_pid` Nullable(String) COMMENT '父进程ID',
    `process_p_cmdline` Nullable(String) COMMENT '父进程执行命令',
    `process_p_name` Nullable(String) COMMENT '父进程名',
    `process_cmdline` Nullable(String) COMMENT '执行命令',
    `process_name` Nullable(String) COMMENT '进程名称',
    `netflow_ack` Nullable(Int8) COMMENT '是否有tcp第三次确认',
    `netflow_ack_packets` Nullable(Int64) COMMENT 'ack标志的数据包个数',
    `netflow_additional_info` Nullable(String) COMMENT '额外资源信息',
    `netflow_answers_info` Nullable(String) COMMENT '应答资源信息',
    `netflow_authorization_info` Nullable(String) COMMENT '授权资源信息',
    `netflow_confidence` Nullable(String) COMMENT '置信度',
    `netflow_fin_ack_packets` Nullable(Int64) COMMENT 'fin-ack数据包个数',
    `netflow_fin_packets` Nullable(Int64) COMMENT 'fin标志的数据包个数',
    `netflow_flags` Nullable(String) COMMENT '标记',
    `netflow_nat_dst_ip` Nullable(String) COMMENT 'NAT后目的IP',
    `netflow_nat_dst_port` Nullable(Int32) COMMENT 'NAT后目的端口',
    `netflow_nat_src_ip` Nullable(String) COMMENT 'NAT后源IP',
    `netflow_nat_src_port` Nullable(Int32) COMMENT 'NAT后源端口',
    `netflow_pcap_size` Nullable(Int64) COMMENT '原始数据包大小',
    `netflow_psh_ack_packets` Nullable(Int64) COMMENT 'psh-ack数据包个数',
    `netflow_psh_packets` Nullable(String) COMMENT 'psh标志的数据包个数',
    `netflow_queries_info` Nullable(String) COMMENT '查询资源信息',
    `netflow_received_app_bytes` Nullable(Int64) COMMENT '下行有效负载应用层字节数',
    `netflow_received_content_bytes` Nullable(Int64) COMMENT '下行有效负载字节数',
    `netflow_received_content_packets` Nullable(Int64) COMMENT '下行有效负载包个数统计',
    `netflow_received_package_flow` Nullable(Int64) COMMENT '已接收包数',
    `netflow_request_authorization` Nullable(String) COMMENT '请求验证信息',
    `netflow_request_head` Nullable(String) COMMENT '请求头',
    `netflow_response_head` Nullable(String) COMMENT '响应头',
    `netflow_response_set_cookie` Nullable(String) COMMENT '响应set_cookie',
    `netflow_rst_ack_packets` Nullable(Int64) COMMENT 'rst-ack数据包个数',
    `netflow_rst_packets` Nullable(Int64) COMMENT 'rst标志的数据包个数',
    `netflow_sent_app_bytes` Nullable(Int64) COMMENT '上行有效负载应用层字节数',
    `netflow_sent_byte_flow` Nullable(Int64) DEFAULT 0 COMMENT '已发送字节数',
    `netflow_sent_content_bytes` Nullable(Int64) COMMENT '上行有效负载字节数',
    `netflow_sent_content_packets` Nullable(Int64) COMMENT '上行有效负载包个数',
    `netflow_session_end_reason` Nullable(String) COMMENT '会话终止原因',
    `netflow_syn` Nullable(Int8) COMMENT '是否有tcp第一次握手',
    `netflow_syn_ack` Nullable(Int8) COMMENT '是否有tcp第二次应答',
    `netflow_syn_ack_packets` Nullable(Int64) COMMENT 'syn-ack标志的数据包个数',
    `netflow_syn_packets` Nullable(Int64) COMMENT 'syn标志的数据包个数',
    `netflow_urg_packets` Nullable(Int64) COMMENT '里面有紧急指针标识的数据包个数',
    `netflow_trans_protocol` Nullable(String) COMMENT '传输层协议',
    `netflow_app_protocol_type` Nullable(String) COMMENT '应用协议类型',
    `netflow_id` Nullable(String) COMMENT '流编号',
    `netflow_iface` Nullable(String) COMMENT '网口名称',
    `netflow_parent_id` Nullable(String) COMMENT '父流编号',
    `netflow_password` Nullable(String) COMMENT '密码',
    `netflow_status` Nullable(String) COMMENT '状态',
    `netflow_version` Nullable(String) COMMENT '版本',
    `netflow_session_id` Nullable(String) COMMENT '会话ID',
    `netflow_tls_subject` Nullable(String) COMMENT 'TLS证书的subject字段',
    `netflow_tls_issuerdn` Nullable(String) COMMENT 'TLS证书中的issuer字段',
    `netflow_tls_serial` Nullable(String) COMMENT 'TLS证书的序列号',
    `netflow_tls_fingerprint` Nullable(String) COMMENT 'TLS证书的（SHA1）指纹',
    `netflow_tls_sni` Nullable(String) COMMENT '客户端发送的服务器名称指示（SNI）域名',
    `netflow_tls_notbefore` Nullable(String) COMMENT 'TLS证书中的NotBefore字段',
    `netflow_tls_notafter` Nullable(String) COMMENT 'TLS证书中的NotAfter字段',
    `netflow_tls_certificate` Nullable(String) COMMENT 'TLS证书base64编码',
    `netflow_tls_chain` Nullable(String) COMMENT '整个TLS证书链base64编码',
    `netflow_tls_jac_hash` Nullable(String) COMMENT 'JA3的HASH值',
    `netflow_tls_jac_string` Nullable(String) COMMENT 'JA3组成字符串',
    `netflow_tls_jacs_hash` Nullable(String) COMMENT 'JA3S的HASH值',
    `netflow_tls_jacs_string` Nullable(String) COMMENT 'JA3S组成字符串',
    `netflow_ssh_client_proto_version` Nullable(String) COMMENT '客户端协议版本号',
    `netflow_ssh_client_software_version` Nullable(String) COMMENT '客户端软件版本号',
    `netflow_ssh_server_proto_version` Nullable(String) COMMENT '服务器协议版本号',
    `netflow_ssh_server_software_version` Nullable(String) COMMENT '服务器软件版本号',
    `netflow_dns_type` Nullable(String) COMMENT '消息类型',
    `netflow_protocol_id` Nullable(String) COMMENT 'DNS业务编号、DHCP请求标识、内部传输号',
    `netflow_dns_flags` Nullable(String) COMMENT '标记',
    `netflow_dns_qr` Nullable(String) COMMENT '包标志(请求和回复)',
    `netflow_dns_aa` Nullable(String) COMMENT '权威解析服务器标志',
    `netflow_dns_tc` Nullable(String) COMMENT '报文截断标志',
    `netflow_dns_rd` Nullable(String) COMMENT '递归查询标志',
    `netflow_dns_ra` Nullable(String) COMMENT '支持递归查询标志',
    `netflow_dns_rrname` Nullable(String) COMMENT '请求对象名称',
    `netflow_dns_rrtype` Nullable(String) COMMENT '请求对象类型',
    `netflow_dns_rcode` Nullable(String) COMMENT '返回码',
    `netflow_tx_id` Nullable(String) COMMENT '通讯协议组(class)',
    `netflow_dns_ttl` Nullable(String) COMMENT '存活时间',
    `netflow_dns_cname` Nullable(String) COMMENT 'CNAME记录(别名记录)',
    `netflow_dns_a` Nullable(String) COMMENT 'Address记录',
    `netflow_dhcp_type` Nullable(String) COMMENT 'dhcp类型：request、reply',
    `netflow_dhcp_client_mac` Nullable(String) COMMENT '客户端MAC地址',
    `netflow_dhcp_assigned_ip` Nullable(String) COMMENT '分配得IP地址',
    `netflow_dhcp_client_ip` Nullable(String) COMMENT '客户端IP地址',
    `netflow_dhcp_relay_ip` Nullable(String) COMMENT '回应的IP地址',
    `netflow_dhcp_requested_ip` Nullable(String) COMMENT '请求者IP地址',
    `netflow_dhcp_hostname` Nullable(String) COMMENT '客户端名，request才有',
    `netflow_dhcp_subnet_mask` Nullable(String) COMMENT '子网掩码',
    `netflow_dhcp_dhcp_type` Nullable(String) COMMENT 'dhcp类型 request offer',
    `netflow_dhcp_routers` Nullable(String) COMMENT '路由',
    `netflow_dhcp_dns_servers` Nullable(String) COMMENT 'DNS服务器列表',
    `netflow_dhcp_params` Nullable(String) COMMENT '请求的参数列表',
    `netflow_dhcp_option55` Nullable(String) COMMENT '选项55字符串序列',
    `netflow_ftp_reply` Nullable(String) COMMENT '命令应答，可能包含多行，采用数组格式',
    `netflow_ftp_completion_code` Nullable(String) COMMENT '三位数的完成代码。第一个数字表示响应是好的、坏的还是不完整的。这也是数组格式，可能包含多个匹配多个应答行的完成代码',
    `netflow_ftp_dynamic_port` Nullable(String) COMMENT '当适用时，用“port”或“EPRT”命令为后续数据传输建立的动态端口',
    `netflow_ftp_mode` Nullable(String) COMMENT 'FTP连接的类型。大多数连接是“被动的”，但也可能是“主动的”',
    `netflow_ftp_reply_received` Nullable(String) COMMENT '指示响应是否与命令匹配。在一些不典型的情况下，命令可能缺少响应',
    `netflow_smtp_helo` Nullable(String) COMMENT 'SMTP helo命令',
    `netflow_smtp_from` Nullable(String) COMMENT '发件人地址',
    `netflow_smtp_to` Nullable(String) COMMENT '收件人地址',
    `netflow_smb_dialect` Nullable(String) COMMENT '协商的协议方言，或“未知”如果丢失',
    `netflow_smb_status_code` Nullable(String) COMMENT '十六进制字符串状态代码',
    `netflow_smb_tree_id` Nullable(String) COMMENT 'Tree ID',
    `netflow_smb_access` Nullable(String) COMMENT '指示如何打开文件。“正常”或“关闭时删除”（字段可能会更改）',
    `netflow_smb_share` Nullable(String) COMMENT '共享名',
    `netflow_smb_share_type` Nullable(String) COMMENT 'FILE,PIPE,PRINT or unknown',
    `netflow_smb_client_dialects` Nullable(String) COMMENT '客户端所说的SMB方言列表。',
    `netflow_smb_client_guid` Nullable(String) COMMENT 'client GUID',
    `netflow_smb_server_guid` Nullable(String) COMMENT 'server GUID',
    `netflow_smb_request_native_os` Nullable(String) COMMENT 'SMB1 native OS string',
    `netflow_smb_request_native_lm` Nullable(String) COMMENT 'SMB1 native Lan Manager string',
    `netflow_smb_response_native_os` Nullable(String) COMMENT 'SMB1 native OS string',
    `netflow_smb_response_native_lm` Nullable(String) COMMENT 'SMB1 native Lan Manager string',
    `netflow_smb_created` Nullable(String) COMMENT '自unix启动以来的时间戳（秒）',
    `netflow_smb_accessed` Nullable(String) COMMENT '自unix启动以来的时间戳（秒）',
    `netflow_smb_modified` Nullable(String) COMMENT '自unix启动以来的时间戳（秒）',
    `netflow_smb_changed` Nullable(String) COMMENT '自unix启动以来的时间戳（秒）',
    `netflow_rpc_xid` Nullable(String) COMMENT 'RPC内部传输号',
    `netflow_rpc_status` Nullable(String) COMMENT '状态',
    `netflow_rpc_auth_type` Nullable(String) COMMENT '验证类型',
    `netflow_rpc_creds_machine_name` Nullable(String) COMMENT '主机名',
    `netflow_rpc_creds_uid` Nullable(String) COMMENT '内部传输号',
    `netflow_rpc_creds_gid` Nullable(String) COMMENT '内部传输号',
    `netflow_nfs_procedure` Nullable(String) COMMENT '远程调用',
    `netflow_nfs_file_tx` Nullable(String),
    `netflow_nfs_status` Nullable(String) COMMENT '状态',
    `netflow_modbus_vendor` Nullable(String) COMMENT '生成厂商',
    `netflow_modbus_model` Nullable(String) COMMENT '固件型号',
    `netflow_modbus_version` Nullable(String) COMMENT '固件版本号',
    `netflow_received_byte_flow` Nullable(Int64) DEFAULT 0 COMMENT '已接收字节数',
    `netflow_sent_package_flow` Nullable(Int64) COMMENT '已发送包数',
    `external_alarm_attack` Nullable(String) COMMENT '攻击名称',
    `external_alarm_attack_type` Nullable(String) COMMENT '攻击类型',
    `external_alarm_av_type` Nullable(String) COMMENT '病毒类别',
    `external_alarm_start_time` Nullable(DateTime64(3)) COMMENT '攻击开始时间',
    `external_alarm_duration` Nullable(String) COMMENT '持续时间',
    `external_alarm_end_time` Nullable(DateTime64(3)) COMMENT '攻击结束时间',
    `external_alarm_risk_level` Nullable(String) COMMENT '风险级别',
    `external_alarm_risk_operation` Nullable(String) COMMENT '检测到风险后执行的操作,如:已清除',
    `model_view` Nullable(String) COMMENT '用于保存跑哪些内置模型',
    `generic_create_time` DateTime64(3),
    `generic_into_time` DateTime64(3) DEFAULT now64(),
    `generic_device_ip` Nullable(String) COMMENT '数据源ip',
    `uuid` UUID DEFAULT generateUUIDv4(),
    `parser_total` Nullable(Int16),
    `parser_count` Nullable(Int16),
    `data_category` Nullable(String) COMMENT '数据分类',
    `data_level` Nullable(String) COMMENT '数据分级',
    `data_record_status` Nullable(String) COMMENT '数据备案状态',
    `start_time` Nullable(DateTime64(3)) COMMENT '开始时间，发生时间记录了日志信息产生的时间，方便用户查看和定位系统事件。',
    `end_time` Nullable(DateTime64(3)) COMMENT '结束时间',
    `login_time` Nullable(DateTime64(3)) COMMENT '登陆时间',
    `logout_time` Nullable(DateTime64(3)) COMMENT '登出时间',
    `client_app_id` Nullable(String) COMMENT '客户端进程号，agent可取到，否则为空',
    `client_app_name` Nullable(String) COMMENT '客户端应用程序名称',
    `query_affected_rows` Nullable(String) COMMENT '查询影响的行数，统计返回多少行',
    `query_elapsed_time` Nullable(DateTime64(3)) COMMENT '查询消耗的时间(单位为微秒)',
    `response_code` Nullable(String) COMMENT '返回码',
    `response_data` Nullable(String) COMMENT '查询返回的结果集，只返回前n行',
    `is_logout` Nullable(String) COMMENT '是否登出',
    `events_num` Nullable(String) COMMENT '事件数量',
    `alarm_times` Nullable(String) COMMENT '告警次数',
    `alarm_compression_times` Nullable(String) COMMENT '告警压缩后次数',
    `rule_alarm_times` Nullable(String) COMMENT '规则告警次数',
    `feature_rule_times` Nullable(String) COMMENT '特征加规则次数',
    `system_alarm_times` Nullable(String) COMMENT '系统告警次数',
    `file_parent_path` Nullable(String) COMMENT '文件父目录路径',
    `file_upload_path` Nullable(String) COMMENT '文件上传路径',
    `sensitive_file_type_id` Nullable(String) COMMENT '敏感文件类型ID',
    `sensitive_file_type_name` Nullable(String) COMMENT '敏感文件类型名称',
    `attachment_num` Nullable(String) COMMENT '邮件附件数',
    `is_download_attachment` Nullable(String) COMMENT '是否下载文件',
    `response_action` Nullable(String) COMMENT '响应动作',
    `mark_file_results` Nullable(String) COMMENT '标记文件结果',
    `rule_id` Nullable(String) COMMENT '命中的规则ID',
    `rule_name` Nullable(String) COMMENT '策略名称',
    `rule_policy` Nullable(String) COMMENT '策略规则',
    `total_match_num` Nullable(String) COMMENT '总匹配数',
    `rule_match_num` Nullable(String) COMMENT '命中规则数',
    `session_id` Nullable(String) COMMENT '会话ID',
    `resource_name` Nullable(String) COMMENT '资源名称',
    `resource_code` Nullable(String) COMMENT '资源编码',
    `module_code` Nullable(String) COMMENT '模块编号',
    `module_name` Nullable(String) COMMENT '模块名称',
    `oprate_code` Nullable(String) COMMENT '操作类型编码',
    `oprate_name` Nullable(String) COMMENT '操作类型名称',
    `oprate_context` Nullable(String) COMMENT '操作内容',
    `treasury_apply_id` Nullable(String) COMMENT '金库申请ID',
    `treasury_verified_code` Nullable(String) COMMENT '金库验证过程标识',
    `interface_call_time` Nullable(DateTime64(3)) COMMENT '接口调用日期时间',
    `call_from_business_code` Nullable(String) COMMENT '接口调用端业务系统编码',
    `call_from_business_name` Nullable(String) COMMENT '接口调用端业务系统名称',
    `call_from_info` Nullable(String) COMMENT '调用发起方信息',
    `call_from_ip` Nullable(String) COMMENT '调用发起方的IP',
    `call_business_code` Nullable(String) COMMENT '被调用端业务系统编码',
    `call_business_name` Nullable(String) COMMENT '被调用端业务系统名称',
    `call_interface_name` Nullable(String) COMMENT '被调用接口名称',
    `call_interface_desc` Nullable(String) COMMENT '被调用接口名称中文描述',
    `call_interface_ip` Nullable(String) COMMENT '被调用接口的IP',
    `call_verified_result` Nullable(String) COMMENT '调用过程认证结果',
    `call_status` Nullable(String) COMMENT '调用成功与否',
    `call_in_param` Nullable(String) COMMENT '输入参数',
    `call_out_param` Nullable(String) COMMENT '输出参数',
    `entity_name` Nullable(String) COMMENT '管理实体名称',
    `operate_object` Nullable(String) COMMENT '操作对象',
    `operate_command` Nullable(String) COMMENT '操作命令',
    `is_batch_operate` Nullable(String) COMMENT '是否批量操作',
    `apply_time` Nullable(DateTime64(3)) COMMENT '申请时间',
    `apply_context` Nullable(String) COMMENT '申请信息',
    `resource_info` Nullable(String) COMMENT '归属资源信息',
    `treasury_scene_id` Nullable(String) COMMENT '金库场景ID',
    `verify_condition` Nullable(String) COMMENT '授权条件',
    `apply_start_time` Nullable(DateTime64(3)) COMMENT '申请起始时间',
    `apply_end_time` Nullable(DateTime64(3)) COMMENT '申请结束时间',
    `coordinate_operator` Nullable(String) COMMENT '协同操作人',
    `verify_time` Nullable(DateTime64(3)) COMMENT '授权时间',
    `verify_model` Nullable(String) COMMENT '授权模式',
    `verify_result` Nullable(String) COMMENT '授权结果',
    `verify_context` Nullable(String) COMMENT ' 授权信息',
    `log_txt_type` Nullable(String) COMMENT '记录的文本类型',
    `log_txt_context` Nullable(String) COMMENT '记录的文本信息',
    `log_video_frame` Nullable(String) COMMENT '记录时刻对应视频帧数',
    `http_req_header_file` Nullable(String) COMMENT '请求头开始文件',
    `http_req_header_length` Nullable(String) COMMENT '请求头长度',
    `http_req_content_position` Nullable(String) COMMENT '请求体开始位置',
    `http_req_content_length` Nullable(String) COMMENT '请求体长度',
    `http_rsp_file_path` Nullable(String) COMMENT '响应文件路径(相对路径)',
    `http_rsp_header_position` Nullable(String) COMMENT '响应头开始位置',
    `http_rsp_header_length` Nullable(String) COMMENT '响应头长度',
    `http_rsp_content_position` Nullable(String) COMMENT '响应体开始位置',
    `http_rsp_content_length` Nullable(String) COMMENT '响应体长度楼上还有个 http_httpmethod',
    `http_method` Nullable(String) COMMENT '请求类型 1Get,2Post,3Put,4Delete',
    `http_req_start_time` Nullable(DateTime64(3)) COMMENT '请求开始时间 相对1970年的时间',
    `http_req_start_timestamp` Nullable(DateTime64(3)) COMMENT '请求开始时间的毫秒',
    `http_rsp_end_time` Nullable(DateTime64(3)) COMMENT '响应结束时间 相对1970年的时间',
    `http_rsp_end_timestamp` Nullable(DateTime64(3)) COMMENT '响应结束时间毫秒',
    `http_status_code` Nullable(String) COMMENT '响应状态 (200,404 等)',
    `reserved_a` Nullable(String),
    `reserved_b` Nullable(String),
    `reserved_c` Nullable(String),
    `reserved_d` Nullable(String),
    `reserved_e` Nullable(String),
    `reserved_f` Nullable(String),
    `reserved_g` Nullable(String),
    `reserved_h` Nullable(String),
    `reserved_i` Nullable(String),
    `reserved_j` Nullable(String),
    `reserved_time_a` Nullable(DateTime64(3)),
    `reserved_time_b` Nullable(DateTime64(3)),
    `reserved_int_a` Nullable(Int64),
    `reserved_int_b` Nullable(Int64)
) ENGINE = ReplicatedMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_2x_copy}/security_log_local','{replica}')
PARTITION BY toYYYYMMDD(generic_create_time) ORDER BY generic_create_time TTL toDateTime(generic_into_time) + toIntervalDay(180) SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS security_log ON CLUSTER cluster_2x_copy (
    `eqpt_ip_uuid` Nullable(String) COMMENT '数据源设备的唯一标识',
    `eqpt_ip` Nullable(String) COMMENT '设备IP地址',
    `eqpt_asset_dept` Nullable(String) COMMENT '设备IP地址资产所属部门名称',
    `eqpt_asset_name` Nullable(String) COMMENT '设备名称',
    `eqpt_asset_type` Nullable(String) COMMENT '设备IP地址资产类型',
    `eqpt_asset_value` Nullable(String) COMMENT '设备IP地址资产价值',
    `eqpt_business_system` Nullable(String) COMMENT '设备IP地址业务系统',
    `eqpt_network_domain` Nullable(String) COMMENT '设备IP地址网络域',
    `eqpt_owner` Nullable(String) COMMENT '设备IP地址资产责任人',
    `eqpt_vendor` Nullable(String) COMMENT '设备IP地址厂商',
    `ueba_flow_id` Nullable(Int32) COMMENT '数据采集任务的ID（logmodule存放采集任务ID用，以便通过ID获取对应采集任务解析出来的所有字段名）',
    `generic_uuid` Nullable(String) COMMENT '日志入库编号',
    `generic_event_id` Nullable(String) COMMENT '原始日志ID',
    `generic_collect_time` Nullable(DateTime64(3)) COMMENT '日志采集时间',
    `generic_collect_ip` Nullable(String) COMMENT '采集器IP',
    `generic_datasource_type` Nullable(String) COMMENT '数据源类型',
    `generic_event_type` Nullable(String) COMMENT '事件类型',
    `generic_opt_type` Nullable(String) COMMENT '操作类型',
    `generic_opt_object` Nullable(String) COMMENT '操作对象',
    `generic_opt_content` Nullable(String) COMMENT '操作内容',
    `generic_priority` Nullable(String) COMMENT '日志严重级别',
    `generic_raw_log` Nullable(String) COMMENT '原始日志正文',
    `result_action_id` Nullable(String) COMMENT '操作行为结果ID',
    `result_action` Nullable(String) COMMENT '操作行为结果',
    `src_person_uuid` Nullable(String) COMMENT '自然人唯一标识（hash值）',
    `src_person_uuid_data` Nullable(String) COMMENT '自然人唯一标识（明文）',
    `src_person_uuid_status` Nullable(String) COMMENT '自然人唯一标识补全状态（找不到则为“未知”，找到则为“已知”）',
    `src_person_name` Nullable(String) COMMENT '姓名',
    `src_person_status` Nullable(String) COMMENT '状态（是否在职）',
    `src_person_ctpositionname` Nullable(String) COMMENT '所属岗位',
    `src_person_types` Nullable(String) COMMENT '类型（公司员工、或第三方外包）',
    `src_person_org_name` Nullable(String) COMMENT '组织机构',
    `src_person_cellphone_no` Nullable(String) COMMENT '手机号码',
    `src_person_identity_card` Nullable(String) COMMENT '身份证号码',
    `src_person_mail` Nullable(String) COMMENT '邮箱',
    `src_person_leave_office_time` Nullable(DateTime64(3)) COMMENT '离职时间',
    `src_person_area_name` Nullable(String) COMMENT '所在地理位置',
    `src_account` Nullable(String) COMMENT '账号',
    `src_account_group` Nullable(String) COMMENT '账号组',
    `src_account_status` Nullable(String) COMMENT '状态',
    `person_mian_account` Nullable(String) COMMENT '主账号',
    `src_account_type` Nullable(String) COMMENT '账号类型',
    `src_device_uuid` Nullable(String) COMMENT '设备唯一标识(hash值)',
    `src_device_uuid_data` Nullable(String) COMMENT '设备唯一标识（明文）',
    `src_device_uuid_status` Nullable(String) COMMENT '设备唯一标识补全状态（找不到则为“未知”，找到则为“已知”）',
    `src_device_name` Nullable(String) COMMENT '设备名称',
    `src_device_ip` Nullable(String) COMMENT '设备IP',
    `src_device_ip_region` Nullable(String) COMMENT '设备IP内外网标识',
    `src_device_ip_country` Nullable(String) COMMENT '设备IP所属国家',
    `src_device_ip_province` Nullable(String) COMMENT '设备IP所属省份',
    `src_device_ip_city` Nullable(String) COMMENT '设备IP所属城市',
    `src_device_mac` Nullable(String) COMMENT '设备mac地址',
    `src_device_dept` Nullable(String) COMMENT '所属部门名称',
    `src_device_type` Nullable(String) COMMENT '设备类型',
    `src_device_owner` Nullable(String) COMMENT '资产责任人',
    `src_device_vendor` Nullable(String) COMMENT '厂商名称',
    `src_port` Nullable(Int32) COMMENT '设备端口',
    `src_device_value` Nullable(String) COMMENT '设备价值',
    `src_device_network_domain` Nullable(String) COMMENT '设备网络域',
    `dst_person_uuid` Nullable(String) COMMENT '自然人唯一标识（hash值）',
    `dst_person_uuid_data` Nullable(String) COMMENT '自然人唯一标识（明文）',
    `dst_person_uuid_status` Nullable(String) COMMENT '自然人唯一标识补全状态（找不到则为“未知”，找到则为“已知”）',
    `dst_person_name` Nullable(String) COMMENT '姓名',
    `dst_person_status` Nullable(String) COMMENT '状态（是否在职）',
    `dst_person_ctpositionname` Nullable(String) COMMENT '所属岗位',
    `dst_person_types` Nullable(String) COMMENT '类型（公司员工、或第三方外包）',
    `dst_person_org_name` Nullable(String) COMMENT '组织机构',
    `dst_person_cellphone_no` Nullable(String) COMMENT '手机号码',
    `dst_person_identity_card` Nullable(String) COMMENT '身份证号码',
    `dst_person_mail` Nullable(String) COMMENT '邮箱',
    `dst_person_mian_account` Nullable(String) COMMENT '主账号',
    `dst_person_leave_office_time` Nullable(DateTime64(3)) COMMENT '离职时间',
    `dst_person_area_name` Nullable(String) COMMENT '所在地理位置',
    `dst_account` Nullable(String) COMMENT '账号',
    `dst_account_group` Nullable(String) COMMENT '账号组',
    `dst_account_status` Nullable(String) COMMENT '状态',
    `dst_account_type` Nullable(String) COMMENT '账号类型',
    `dst_device_uuid` Nullable(String) COMMENT '设备唯一标识（hash值）',
    `dst_device_uuid_data` Nullable(String) COMMENT '设备唯一标识（明文）',
    `dst_device_uuid_status` Nullable(String) COMMENT '设备唯一标识补全状态（找不到则为“未知”，找到则为“已知”）',
    `dst_device_name` Nullable(String) COMMENT '设备名称',
    `dst_device_ip` Nullable(String) COMMENT '设备IP',
    `dst_device_ip_region` Nullable(String) COMMENT '设备IP内外网标识',
    `dst_device_ip_country` Nullable(String) COMMENT '设备IP所属国家',
    `dst_device_ip_province` Nullable(String) COMMENT '设备IP所属省份',
    `dst_device_ip_city` Nullable(String) COMMENT '设备IP所属城市',
    `dst_device_mac` Nullable(String) COMMENT '设备mac地址',
    `dst_device_dept` Nullable(String) COMMENT '所属部门名称',
    `dst_device_type` Nullable(String) COMMENT '设备类型',
    `dst_device_owner` Nullable(String) COMMENT '资产责任人',
    `dst_device_vendor` Nullable(String) COMMENT '厂商名称',
    `dst_device_value` Nullable(String) COMMENT '设备价值',
    `dst_device_network_domain` Nullable(String) COMMENT '设备网络域',
    `dst_app_uuid` Nullable(String) COMMENT '应用唯一标识（hash值）',
    `dst_app_uuid_data` Nullable(String) COMMENT '应用唯一标识（明文）',
    `dst_app_uuid_status` Nullable(String) COMMENT '应用唯一标识补全状态（找不到则为“未知”，找到则为“已知”）',
    `dst_app_name` Nullable(String) COMMENT '应用名称',
    `dst_port` Nullable(Int32) COMMENT '应用所属端口',
    `dst_app_url` Nullable(String) COMMENT '应用URL',
    `dst_app_dept` Nullable(String) COMMENT '所属部门名称',
    `dst_app_type` Nullable(String) COMMENT '应用类型',
    `dst_app_value` Nullable(String) COMMENT '应用价值',
    `dst_app_network_domain` Nullable(String) COMMENT '应用网络域',
    `dst_app_owner` Nullable(String) COMMENT '资产责任人',
    `dst_app_vendor` Nullable(String) COMMENT '厂商名称',
    `data_uuid` Nullable(String) COMMENT '数据唯一标识（hash值）',
    `data_uuid_data` Nullable(String) COMMENT '数据唯一标识（明文）',
    `data_uuid_status` Nullable(String) COMMENT '数据唯一标识补全状态（找不到则为“未知”，找到则为“已知”）',
    `data_name` Nullable(String) COMMENT '数据名称',
    `data_hash` Nullable(String) COMMENT '数据哈希值',
    `data_dept` Nullable(String) COMMENT '所属部门名称',
    `data_type` Nullable(String) COMMENT '数据类型',
    `data_value` Nullable(String) COMMENT '数据价值',
    `data_owner` Nullable(String) COMMENT '数据所属负责人',
    `database_access_privilege` Nullable(String) COMMENT '数据库访问权限',
    `database_access_time` Nullable(DateTime64(3)) COMMENT '数据库访问时间',
    `database_id` Nullable(String) COMMENT '数据库ID',
    `database_name` Nullable(String) COMMENT '数据库名',
    `database_table_name` Nullable(String) COMMENT '数据库表名',
    `database_sql` Nullable(String) COMMENT '数据库SQL语句',
    `database_system_type` Nullable(String) COMMENT '数据库系统类型',
    `database_type` Nullable(String) COMMENT '数据库类型',
    `file_access_privilege` Nullable(String) COMMENT '文件访问权限',
    `file_access_time` Nullable(DateTime64(3)) COMMENT '文件访问时间',
    `file_create_time` Nullable(DateTime64(3)) COMMENT '文件创建时间',
    `file_hash` Nullable(String) COMMENT '文件HASH值',
    `file_id` Nullable(String) COMMENT '文件ID',
    `file_modify_time` Nullable(DateTime64(3)) COMMENT '文件修改时间',
    `file_name` Nullable(String) COMMENT '文件名',
    `file_path` Nullable(String) COMMENT '文件路径',
    `file_size` Nullable(Int64) COMMENT '文件大小',
    `file_system_type` Nullable(String) COMMENT '文件系统类型',
    `file_type` Nullable(String) COMMENT '文件类型',
    `file_share_local_path` Nullable(String) COMMENT '共享路径',
    `file_share_name` Nullable(String) COMMENT '共享名称',
    `file_state` Nullable(String) COMMENT '文件状态',
    `program_name` Nullable(String) COMMENT '程序名称',
    `mail_src_username` Nullable(String) COMMENT '发件人',
    `mail_dst_username` Nullable(String) COMMENT '收件人',
    `mail_bcc_username` Nullable(String) COMMENT '密送人',
    `mail_cc_username` Nullable(String) COMMENT '抄送人',
    `mail_head` Nullable(String) COMMENT '邮件头',
    `mail_title` Nullable(String) COMMENT '邮件标题',
    `mail_content` Nullable(String) COMMENT '邮件内容',
    `mail_type` Nullable(String) COMMENT '邮件格式',
    `http_url_externalurl` Nullable(String) COMMENT '访问URL',
    `http_url_externalurl_type` Nullable(String) COMMENT '访问URL的类型（外部、内部）',
    `http_url_externalurl_domain` Nullable(String) COMMENT '访问URL的域名',
    `http_url_externalurl_parameter` Nullable(String) COMMENT '访问URL的参数',
    `http_url_httpreferrer` Nullable(String) COMMENT '来源URL',
    `http_url_httpreferrer_type` Nullable(String) COMMENT '来源URL的类型（外部、内部）',
    `http_httpagent` Nullable(String) COMMENT 'User-Agent',
    `http_httpmethod` Nullable(String) COMMENT 'http请求方法',
    `http_httpprotocol` Nullable(String) COMMENT 'http协议',
    `http_httpprotocolversion` Nullable(String) COMMENT 'http协议班恩',
    `http_httpstatus` Nullable(String) COMMENT 'http返回状态',
    `http_cookie` Nullable(String) COMMENT '浏览器缓存',
    `http_req_content_type` Nullable(String) COMMENT '请求内容类型',
    `http_request_body` Nullable(String) COMMENT '请求内容',
    `http_request_size` Nullable(Int64) COMMENT '请求包的大小',
    `http_response_body` Nullable(String) COMMENT '响应内容',
    `http_response_datetime` Nullable(DateTime64(3)) COMMENT '响应时间',
    `http_response_size` Nullable(Int64) COMMENT '返回包的大小',
    `http_response_time` Nullable(DateTime64(3)) COMMENT '响应时间',
    `http_rsp_content_type` Nullable(String) COMMENT '返回内容类型',
    `http_x_forwarded_for` Nullable(String) COMMENT '代理IP地址',
    `http_accept` Nullable(String) COMMENT '接受的类型',
    `http_accept_charset` Nullable(String) COMMENT '接受的字符集',
    `http_accept_encoding` Nullable(String) COMMENT '接受的字符编码',
    `http_accept_language` Nullable(String) COMMENT '接受的语言',
    `http_expired_date` Nullable(String) COMMENT '过期时间',
    `http_connection_status` Nullable(String) COMMENT '连接状态',
    `process_pid` Nullable(String) COMMENT '进程ID',
    `process_p_pid` Nullable(String) COMMENT '父进程ID',
    `process_p_cmdline` Nullable(String) COMMENT '父进程执行命令',
    `process_p_name` Nullable(String) COMMENT '父进程名',
    `process_cmdline` Nullable(String) COMMENT '执行命令',
    `process_name` Nullable(String) COMMENT '进程名称',
    `netflow_ack` Nullable(Int8) COMMENT '是否有tcp第三次确认',
    `netflow_ack_packets` Nullable(Int64) COMMENT 'ack标志的数据包个数',
    `netflow_additional_info` Nullable(String) COMMENT '额外资源信息',
    `netflow_answers_info` Nullable(String) COMMENT '应答资源信息',
    `netflow_authorization_info` Nullable(String) COMMENT '授权资源信息',
    `netflow_confidence` Nullable(String) COMMENT '置信度',
    `netflow_fin_ack_packets` Nullable(Int64) COMMENT 'fin-ack数据包个数',
    `netflow_fin_packets` Nullable(Int64) COMMENT 'fin标志的数据包个数',
    `netflow_flags` Nullable(String) COMMENT '标记',
    `netflow_nat_dst_ip` Nullable(String) COMMENT 'NAT后目的IP',
    `netflow_nat_dst_port` Nullable(Int32) COMMENT 'NAT后目的端口',
    `netflow_nat_src_ip` Nullable(String) COMMENT 'NAT后源IP',
    `netflow_nat_src_port` Nullable(Int32) COMMENT 'NAT后源端口',
    `netflow_pcap_size` Nullable(Int64) COMMENT '原始数据包大小',
    `netflow_psh_ack_packets` Nullable(Int64) COMMENT 'psh-ack数据包个数',
    `netflow_psh_packets` Nullable(String) COMMENT 'psh标志的数据包个数',
    `netflow_queries_info` Nullable(String) COMMENT '查询资源信息',
    `netflow_received_app_bytes` Nullable(Int64) COMMENT '下行有效负载应用层字节数',
    `netflow_received_content_bytes` Nullable(Int64) COMMENT '下行有效负载字节数',
    `netflow_received_content_packets` Nullable(Int64) COMMENT '下行有效负载包个数统计',
    `netflow_received_package_flow` Nullable(Int64) COMMENT '已接收包数',
    `netflow_request_authorization` Nullable(String) COMMENT '请求验证信息',
    `netflow_request_head` Nullable(String) COMMENT '请求头',
    `netflow_response_head` Nullable(String) COMMENT '响应头',
    `netflow_response_set_cookie` Nullable(String) COMMENT '响应set_cookie',
    `netflow_rst_ack_packets` Nullable(Int64) COMMENT 'rst-ack数据包个数',
    `netflow_rst_packets` Nullable(Int64) COMMENT 'rst标志的数据包个数',
    `netflow_sent_app_bytes` Nullable(Int64) COMMENT '上行有效负载应用层字节数',
    `netflow_sent_byte_flow` Nullable(Int64) DEFAULT 0 COMMENT '已发送字节数',
    `netflow_sent_content_bytes` Nullable(Int64) COMMENT '上行有效负载字节数',
    `netflow_sent_content_packets` Nullable(Int64) COMMENT '上行有效负载包个数',
    `netflow_session_end_reason` Nullable(String) COMMENT '会话终止原因',
    `netflow_syn` Nullable(Int8) COMMENT '是否有tcp第一次握手',
    `netflow_syn_ack` Nullable(Int8) COMMENT '是否有tcp第二次应答',
    `netflow_syn_ack_packets` Nullable(Int64) COMMENT 'syn-ack标志的数据包个数',
    `netflow_syn_packets` Nullable(Int64) COMMENT 'syn标志的数据包个数',
    `netflow_urg_packets` Nullable(Int64) COMMENT '里面有紧急指针标识的数据包个数',
    `netflow_trans_protocol` Nullable(String) COMMENT '传输层协议',
    `netflow_app_protocol_type` Nullable(String) COMMENT '应用协议类型',
    `netflow_id` Nullable(String) COMMENT '流编号',
    `netflow_iface` Nullable(String) COMMENT '网口名称',
    `netflow_parent_id` Nullable(String) COMMENT '父流编号',
    `netflow_password` Nullable(String) COMMENT '密码',
    `netflow_status` Nullable(String) COMMENT '状态',
    `netflow_version` Nullable(String) COMMENT '版本',
    `netflow_session_id` Nullable(String) COMMENT '会话ID',
    `netflow_tls_subject` Nullable(String) COMMENT 'TLS证书的subject字段',
    `netflow_tls_issuerdn` Nullable(String) COMMENT 'TLS证书中的issuer字段',
    `netflow_tls_serial` Nullable(String) COMMENT 'TLS证书的序列号',
    `netflow_tls_fingerprint` Nullable(String) COMMENT 'TLS证书的（SHA1）指纹',
    `netflow_tls_sni` Nullable(String) COMMENT '客户端发送的服务器名称指示（SNI）域名',
    `netflow_tls_notbefore` Nullable(String) COMMENT 'TLS证书中的NotBefore字段',
    `netflow_tls_notafter` Nullable(String) COMMENT 'TLS证书中的NotAfter字段',
    `netflow_tls_certificate` Nullable(String) COMMENT 'TLS证书base64编码',
    `netflow_tls_chain` Nullable(String) COMMENT '整个TLS证书链base64编码',
    `netflow_tls_jac_hash` Nullable(String) COMMENT 'JA3的HASH值',
    `netflow_tls_jac_string` Nullable(String) COMMENT 'JA3组成字符串',
    `netflow_tls_jacs_hash` Nullable(String) COMMENT 'JA3S的HASH值',
    `netflow_tls_jacs_string` Nullable(String) COMMENT 'JA3S组成字符串',
    `netflow_ssh_client_proto_version` Nullable(String) COMMENT '客户端协议版本号',
    `netflow_ssh_client_software_version` Nullable(String) COMMENT '客户端软件版本号',
    `netflow_ssh_server_proto_version` Nullable(String) COMMENT '服务器协议版本号',
    `netflow_ssh_server_software_version` Nullable(String) COMMENT '服务器软件版本号',
    `netflow_dns_type` Nullable(String) COMMENT '消息类型',
    `netflow_protocol_id` Nullable(String) COMMENT 'DNS业务编号、DHCP请求标识、内部传输号',
    `netflow_dns_flags` Nullable(String) COMMENT '标记',
    `netflow_dns_qr` Nullable(String) COMMENT '包标志(请求和回复)',
    `netflow_dns_aa` Nullable(String) COMMENT '权威解析服务器标志',
    `netflow_dns_tc` Nullable(String) COMMENT '报文截断标志',
    `netflow_dns_rd` Nullable(String) COMMENT '递归查询标志',
    `netflow_dns_ra` Nullable(String) COMMENT '支持递归查询标志',
    `netflow_dns_rrname` Nullable(String) COMMENT '请求对象名称',
    `netflow_dns_rrtype` Nullable(String) COMMENT '请求对象类型',
    `netflow_dns_rcode` Nullable(String) COMMENT '返回码',
    `netflow_tx_id` Nullable(String) COMMENT '通讯协议组(class)',
    `netflow_dns_ttl` Nullable(String) COMMENT '存活时间',
    `netflow_dns_cname` Nullable(String) COMMENT 'CNAME记录(别名记录)',
    `netflow_dns_a` Nullable(String) COMMENT 'Address记录',
    `netflow_dhcp_type` Nullable(String) COMMENT 'dhcp类型：request、reply',
    `netflow_dhcp_client_mac` Nullable(String) COMMENT '客户端MAC地址',
    `netflow_dhcp_assigned_ip` Nullable(String) COMMENT '分配得IP地址',
    `netflow_dhcp_client_ip` Nullable(String) COMMENT '客户端IP地址',
    `netflow_dhcp_relay_ip` Nullable(String) COMMENT '回应的IP地址',
    `netflow_dhcp_requested_ip` Nullable(String) COMMENT '请求者IP地址',
    `netflow_dhcp_hostname` Nullable(String) COMMENT '客户端名，request才有',
    `netflow_dhcp_subnet_mask` Nullable(String) COMMENT '子网掩码',
    `netflow_dhcp_dhcp_type` Nullable(String) COMMENT 'dhcp类型 request offer',
    `netflow_dhcp_routers` Nullable(String) COMMENT '路由',
    `netflow_dhcp_dns_servers` Nullable(String) COMMENT 'DNS服务器列表',
    `netflow_dhcp_params` Nullable(String) COMMENT '请求的参数列表',
    `netflow_dhcp_option55` Nullable(String) COMMENT '选项55字符串序列',
    `netflow_ftp_reply` Nullable(String) COMMENT '命令应答，可能包含多行，采用数组格式',
    `netflow_ftp_completion_code` Nullable(String) COMMENT '三位数的完成代码。第一个数字表示响应是好的、坏的还是不完整的。这也是数组格式，可能包含多个匹配多个应答行的完成代码',
    `netflow_ftp_dynamic_port` Nullable(String) COMMENT '当适用时，用“port”或“EPRT”命令为后续数据传输建立的动态端口',
    `netflow_ftp_mode` Nullable(String) COMMENT 'FTP连接的类型。大多数连接是“被动的”，但也可能是“主动的”',
    `netflow_ftp_reply_received` Nullable(String) COMMENT '指示响应是否与命令匹配。在一些不典型的情况下，命令可能缺少响应',
    `netflow_smtp_helo` Nullable(String) COMMENT 'SMTP helo命令',
    `netflow_smtp_from` Nullable(String) COMMENT '发件人地址',
    `netflow_smtp_to` Nullable(String) COMMENT '收件人地址',
    `netflow_smb_dialect` Nullable(String) COMMENT '协商的协议方言，或“未知”如果丢失',
    `netflow_smb_status_code` Nullable(String) COMMENT '十六进制字符串状态代码',
    `netflow_smb_tree_id` Nullable(String) COMMENT 'Tree ID',
    `netflow_smb_access` Nullable(String) COMMENT '指示如何打开文件。“正常”或“关闭时删除”（字段可能会更改）',
    `netflow_smb_share` Nullable(String) COMMENT '共享名',
    `netflow_smb_share_type` Nullable(String) COMMENT 'FILE,PIPE,PRINT or unknown',
    `netflow_smb_client_dialects` Nullable(String) COMMENT '客户端所说的SMB方言列表。',
    `netflow_smb_client_guid` Nullable(String) COMMENT 'client GUID',
    `netflow_smb_server_guid` Nullable(String) COMMENT 'server GUID',
    `netflow_smb_request_native_os` Nullable(String) COMMENT 'SMB1 native OS string',
    `netflow_smb_request_native_lm` Nullable(String) COMMENT 'SMB1 native Lan Manager string',
    `netflow_smb_response_native_os` Nullable(String) COMMENT 'SMB1 native OS string',
    `netflow_smb_response_native_lm` Nullable(String) COMMENT 'SMB1 native Lan Manager string',
    `netflow_smb_created` Nullable(String) COMMENT '自unix启动以来的时间戳（秒）',
    `netflow_smb_accessed` Nullable(String) COMMENT '自unix启动以来的时间戳（秒）',
    `netflow_smb_modified` Nullable(String) COMMENT '自unix启动以来的时间戳（秒）',
    `netflow_smb_changed` Nullable(String) COMMENT '自unix启动以来的时间戳（秒）',
    `netflow_rpc_xid` Nullable(String) COMMENT 'RPC内部传输号',
    `netflow_rpc_status` Nullable(String) COMMENT '状态',
    `netflow_rpc_auth_type` Nullable(String) COMMENT '验证类型',
    `netflow_rpc_creds_machine_name` Nullable(String) COMMENT '主机名',
    `netflow_rpc_creds_uid` Nullable(String) COMMENT '内部传输号',
    `netflow_rpc_creds_gid` Nullable(String) COMMENT '内部传输号',
    `netflow_nfs_procedure` Nullable(String) COMMENT '远程调用',
    `netflow_nfs_file_tx` Nullable(String),
    `netflow_nfs_status` Nullable(String) COMMENT '状态',
    `netflow_modbus_vendor` Nullable(String) COMMENT '生成厂商',
    `netflow_modbus_model` Nullable(String) COMMENT '固件型号',
    `netflow_modbus_version` Nullable(String) COMMENT '固件版本号',
    `netflow_received_byte_flow` Nullable(Int64) DEFAULT 0 COMMENT '已接收字节数',
    `netflow_sent_package_flow` Nullable(Int64) COMMENT '已发送包数',
    `external_alarm_attack` Nullable(String) COMMENT '攻击名称',
    `external_alarm_attack_type` Nullable(String) COMMENT '攻击类型',
    `external_alarm_av_type` Nullable(String) COMMENT '病毒类别',
    `external_alarm_start_time` Nullable(DateTime64(3)) COMMENT '攻击开始时间',
    `external_alarm_duration` Nullable(String) COMMENT '持续时间',
    `external_alarm_end_time` Nullable(DateTime64(3)) COMMENT '攻击结束时间',
    `external_alarm_risk_level` Nullable(String) COMMENT '风险级别',
    `external_alarm_risk_operation` Nullable(String) COMMENT '检测到风险后执行的操作,如:已清除',
    `model_view` Nullable(String) COMMENT '用于保存跑哪些内置模型',
    `generic_create_time` DateTime64(3),
    `generic_into_time` DateTime64(3) DEFAULT now64(),
    `generic_device_ip` Nullable(String) COMMENT '数据源ip',
    `uuid` UUID DEFAULT generateUUIDv4(),
    `parser_total` Nullable(Int16),
    `parser_count` Nullable(Int16),
    `data_category` Nullable(String) COMMENT '数据分类',
    `data_level` Nullable(String) COMMENT '数据分级',
    `data_record_status` Nullable(String) COMMENT '数据备案状态',
    `start_time` Nullable(DateTime64(3)) COMMENT '开始时间，发生时间记录了日志信息产生的时间，方便用户查看和定位系统事件。',
    `end_time` Nullable(DateTime64(3)) COMMENT '结束时间',
    `login_time` Nullable(DateTime64(3)) COMMENT '登陆时间',
    `logout_time` Nullable(DateTime64(3)) COMMENT '登出时间',
    `client_app_id` Nullable(String) COMMENT '客户端进程号，agent可取到，否则为空',
    `client_app_name` Nullable(String) COMMENT '客户端应用程序名称',
    `query_affected_rows` Nullable(String) COMMENT '查询影响的行数，统计返回多少行',
    `query_elapsed_time` Nullable(DateTime64(3)) COMMENT '查询消耗的时间(单位为微秒)',
    `response_code` Nullable(String) COMMENT '返回码',
    `response_data` Nullable(String) COMMENT '查询返回的结果集，只返回前n行',
    `is_logout` Nullable(String) COMMENT '是否登出',
    `events_num` Nullable(String) COMMENT '事件数量',
    `alarm_times` Nullable(String) COMMENT '告警次数',
    `alarm_compression_times` Nullable(String) COMMENT '告警压缩后次数',
    `rule_alarm_times` Nullable(String) COMMENT '规则告警次数',
    `feature_rule_times` Nullable(String) COMMENT '特征加规则次数',
    `system_alarm_times` Nullable(String) COMMENT '系统告警次数',
    `file_parent_path` Nullable(String) COMMENT '文件父目录路径',
    `file_upload_path` Nullable(String) COMMENT '文件上传路径',
    `sensitive_file_type_id` Nullable(String) COMMENT '敏感文件类型ID',
    `sensitive_file_type_name` Nullable(String) COMMENT '敏感文件类型名称',
    `attachment_num` Nullable(String) COMMENT '邮件附件数',
    `is_download_attachment` Nullable(String) COMMENT '是否下载文件',
    `response_action` Nullable(String) COMMENT '响应动作',
    `mark_file_results` Nullable(String) COMMENT '标记文件结果',
    `rule_id` Nullable(String) COMMENT '命中的规则ID',
    `rule_name` Nullable(String) COMMENT '策略名称',
    `rule_policy` Nullable(String) COMMENT '策略规则',
    `total_match_num` Nullable(String) COMMENT '总匹配数',
    `rule_match_num` Nullable(String) COMMENT '命中规则数',
    `session_id` Nullable(String) COMMENT '会话ID',
    `resource_name` Nullable(String) COMMENT '资源名称',
    `resource_code` Nullable(String) COMMENT '资源编码',
    `module_code` Nullable(String) COMMENT '模块编号',
    `module_name` Nullable(String) COMMENT '模块名称',
    `oprate_code` Nullable(String) COMMENT '操作类型编码',
    `oprate_name` Nullable(String) COMMENT '操作类型名称',
    `oprate_context` Nullable(String) COMMENT '操作内容',
    `treasury_apply_id` Nullable(String) COMMENT '金库申请ID',
    `treasury_verified_code` Nullable(String) COMMENT '金库验证过程标识',
    `interface_call_time` Nullable(DateTime64(3)) COMMENT '接口调用日期时间',
    `call_from_business_code` Nullable(String) COMMENT '接口调用端业务系统编码',
    `call_from_business_name` Nullable(String) COMMENT '接口调用端业务系统名称',
    `call_from_info` Nullable(String) COMMENT '调用发起方信息',
    `call_from_ip` Nullable(String) COMMENT '调用发起方的IP',
    `call_business_code` Nullable(String) COMMENT '被调用端业务系统编码',
    `call_business_name` Nullable(String) COMMENT '被调用端业务系统名称',
    `call_interface_name` Nullable(String) COMMENT '被调用接口名称',
    `call_interface_desc` Nullable(String) COMMENT '被调用接口名称中文描述',
    `call_interface_ip` Nullable(String) COMMENT '被调用接口的IP',
    `call_verified_result` Nullable(String) COMMENT '调用过程认证结果',
    `call_status` Nullable(String) COMMENT '调用成功与否',
    `call_in_param` Nullable(String) COMMENT '输入参数',
    `call_out_param` Nullable(String) COMMENT '输出参数',
    `entity_name` Nullable(String) COMMENT '管理实体名称',
    `operate_object` Nullable(String) COMMENT '操作对象',
    `operate_command` Nullable(String) COMMENT '操作命令',
    `is_batch_operate` Nullable(String) COMMENT '是否批量操作',
    `apply_time` Nullable(DateTime64(3)) COMMENT '申请时间',
    `apply_context` Nullable(String) COMMENT '申请信息',
    `resource_info` Nullable(String) COMMENT '归属资源信息',
    `treasury_scene_id` Nullable(String) COMMENT '金库场景ID',
    `verify_condition` Nullable(String) COMMENT '授权条件',
    `apply_start_time` Nullable(DateTime64(3)) COMMENT '申请起始时间',
    `apply_end_time` Nullable(DateTime64(3)) COMMENT '申请结束时间',
    `coordinate_operator` Nullable(String) COMMENT '协同操作人',
    `verify_time` Nullable(DateTime64(3)) COMMENT '授权时间',
    `verify_model` Nullable(String) COMMENT '授权模式',
    `verify_result` Nullable(String) COMMENT '授权结果',
    `verify_context` Nullable(String) COMMENT ' 授权信息',
    `log_txt_type` Nullable(String) COMMENT '记录的文本类型',
    `log_txt_context` Nullable(String) COMMENT '记录的文本信息',
    `log_video_frame` Nullable(String) COMMENT '记录时刻对应视频帧数',
    `http_req_header_file` Nullable(String) COMMENT '请求头开始文件',
    `http_req_header_length` Nullable(String) COMMENT '请求头长度',
    `http_req_content_position` Nullable(String) COMMENT '请求体开始位置',
    `http_req_content_length` Nullable(String) COMMENT '请求体长度',
    `http_rsp_file_path` Nullable(String) COMMENT '响应文件路径(相对路径)',
    `http_rsp_header_position` Nullable(String) COMMENT '响应头开始位置',
    `http_rsp_header_length` Nullable(String) COMMENT '响应头长度',
    `http_rsp_content_position` Nullable(String) COMMENT '响应体开始位置',
    `http_rsp_content_length` Nullable(String) COMMENT '响应体长度楼上还有个 http_httpmethod',
    `http_method` Nullable(String) COMMENT '请求类型 1Get,2Post,3Put,4Delete',
    `http_req_start_time` Nullable(DateTime64(3)) COMMENT '请求开始时间 相对1970年的时间',
    `http_req_start_timestamp` Nullable(DateTime64(3)) COMMENT '请求开始时间的毫秒',
    `http_rsp_end_time` Nullable(DateTime64(3)) COMMENT '响应结束时间 相对1970年的时间',
    `http_rsp_end_timestamp` Nullable(DateTime64(3)) COMMENT '响应结束时间毫秒',
    `http_status_code` Nullable(String) COMMENT '响应状态 (200,404 等)',
    `reserved_a` Nullable(String),
    `reserved_b` Nullable(String),
    `reserved_c` Nullable(String),
    `reserved_d` Nullable(String),
    `reserved_e` Nullable(String),
    `reserved_f` Nullable(String),
    `reserved_g` Nullable(String),
    `reserved_h` Nullable(String),
    `reserved_i` Nullable(String),
    `reserved_j` Nullable(String),
    `reserved_time_a` Nullable(DateTime64(3)),
    `reserved_time_b` Nullable(DateTime64(3)),
    `reserved_int_a` Nullable(Int64),
    `reserved_int_b` Nullable(Int64)
) ENGINE = Distributed('cluster_2x_copy','%s','security_log_local',rand());




