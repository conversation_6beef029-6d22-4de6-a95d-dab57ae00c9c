CREATE MATERIALIZED VIEW IF NOT EXISTS mv_flow_metric_interval_1day_monitor_local ON CLUSTER cluster_1x_copy (
    `flow_id` String,
    `flow_type` String,
    `node_id` String,
    `node_type` String,
    `source_ip` String,
    `writer_type` String,
    `interval_end_time` DateTime,
    `out_count` UInt64,
    `length` UInt64,
    `in_count` UInt64,
    `total` UInt64
) ENGINE = ReplicatedSummingMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_1x_copy}/mv_flow_metric_interval_1day_monitor_local', '{replica}')
PARTITION BY toYYYYMM(interval_end_time)
ORDER BY (flow_id, flow_type, node_id, node_type, source_ip, writer_type, interval_end_time)
SETTINGS index_granularity = 8192
POPULATE
AS
SELECT
    flow_id,
    flow_type,
    node_id,
    node_type,
    source_ip,
    writer_type,
    formatDateTime(toStartOfInterval(end_time, INTERVAL 1 day), '%Y-%m-%d %R:%S') AS interval_end_time,
    sum(out_count) AS out_count,
    sum(length) AS length,
    sum(in_count) AS in_count,
    count(flow_id) AS total
FROM %s.flow_metric_local WHERE eps !=-1
GROUP BY flow_id,flow_type,node_id,node_type,source_ip,writer_type,interval_end_time;

CREATE TABLE IF NOT EXISTS mv_flow_metric_interval_1day_monitor ON CLUSTER cluster_1x_copy (
    `flow_id` String,
    `flow_type` String,
    `node_id` String,
    `node_type` String,
    `source_ip` String,
    `writer_type` String,
    `interval_end_time` DateTime,
    `out_count` UInt64,
    `length` UInt64,
    `in_count` UInt64,
    `total` UInt64
) ENGINE = Distributed('cluster_1x_copy', '%s', 'mv_flow_metric_interval_1day_monitor_local', rand());


CREATE MATERIALIZED VIEW IF NOT EXISTS mv_flow_metric_latest_monitor_local ON CLUSTER cluster_1x_copy (
    `flow_id` String,
    `node_type` String ,
    `source_ip` String,
    `writer_type` String,
    `flow_type` String,
    `in_count` UInt64,
    `out_count` UInt64,
    `length` UInt64,
    `read_status` UInt64,
    `read_size` UInt64,
    `write_size` UInt64,
    `queue_size` UInt64,
    `write_status` String,
    `latest_time` DateTime
) ENGINE = ReplicatedAggregatingMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_1x_copy}/mv_flow_metric_latest_monitor_local', '{replica}')
PARTITION BY toYYYYMM(latest_time)
ORDER BY (flow_id, node_type, source_ip, writer_type)
SETTINGS index_granularity = 8192
AS
SELECT
    flow_id ,
    node_type ,
    source_ip ,
    writer_type ,
    anyLast(flow_type) as flow_type,
    argMax(in_count, create_time) as in_count,
    argMax(out_count, create_time) as out_count,
    argMax(length, create_time) as length,
    argMax(read_status, create_time) as read_status,
    argMax(read_size, create_time) as read_size,
    argMax(write_size, create_time) as write_size,
    argMax(queue_size, create_time) as queue_size,
    argMax(write_status, create_time) as write_status,
    max(create_time) as latest_time
FROM %s.flow_metric_local
WHERE eps != -1
GROUP BY flow_id, node_type, source_ip, writer_type;

CREATE TABLE IF NOT EXISTS mv_flow_metric_latest_monitor ON CLUSTER cluster_1x_copy (
    `flow_id` String,
    `node_type` String ,
    `source_ip` String,
    `writer_type` String,
    `flow_type` String,
    `in_count` UInt64,
    `out_count` UInt64,
    `length` UInt64,
    `read_status` UInt64,
    `read_size` UInt64,
    `write_size` UInt64,
    `queue_size` UInt64,
    `write_status` String,
    `latest_time` DateTime
) ENGINE = Distributed('cluster_1x_copy', '%s', 'mv_flow_metric_latest_monitor_local', rand());
