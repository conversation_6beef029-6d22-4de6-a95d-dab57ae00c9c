CREATE TABLE IF NOT EXISTS eqpt_resource_local ON CLUSTER cluster_2x_copy
(
    `id`               UUID          default generateUUIDv4(),
    `create_time`      DateTime64(3) default now64(),
    `branch_code`      String comment '资源池代码',
    `branch_code_name` String comment '资源池名称',
    `vendor`           String comment '厂商代码',
    `vendor_name`      String comment '厂商名称',
    `eqpt_type`        String comment '设备类型代码',
    `eqpt_type_Name`   String comment '设备类型名称'
) ENGINE = ReplicatedMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_nx_copy}/eqpt_resource_local', '{replica}')
      PARTITION BY toDate(create_time) ORDER BY create_time SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS eqpt_resource ON CLUSTER cluster_2x_copy
(
    `id`               UUID          default generateUUIDv4(),
    `create_time`      DateTime64(3) default now64(),
    `branch_code`      String comment '资源池代码',
    `branch_code_name` String comment '资源池名称',
    `vendor`           String comment '厂商代码',
    `vendor_name`      String comment '厂商名称',
    `eqpt_type`        String comment '设备类型代码',
    `eqpt_type_Name`   String comment '设备类型名称'
) ENGINE = Distributed('cluster_nx_copy', '%s', 'eqpt_resource_local', rand());

CREATE TABLE IF NOT EXISTS eqpt_monitor_local ON CLUSTER cluster_2x_copy
(
    `id`             UUID          default generateUUIDv4(),
    `create_time`      DateTime64(3) default now64(),
    `branch_code`      String comment '资源池代码',
    `branch_code_name` String comment '资源池名称',
    `vendor`           String comment '厂商代码',
    `vendor_name`      String comment '厂商名称',
    `eqpt_type`        String comment '设备类型代码',
    `eqpt_type_Name`   String comment '设备类型名称',
    `eqpt_ip`          String comment '设备IP',
    `send_count`       UInt64 comment '发送量',
    `recevie_count`    UInt64 comment '接送量'
    ) ENGINE = ReplicatedMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_nx_copy}/eqpt_monitor_local', '{replica}')
    PARTITION BY toDate(create_time) ORDER BY create_time SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS eqpt_monitor ON CLUSTER cluster_2x_copy
(
    `id`             UUID          default generateUUIDv4(),
    `create_time`      DateTime64(3) default now64(),
    `branch_code`      String comment '资源池代码',
    `branch_code_name` String comment '资源池名称',
    `vendor`           String comment '厂商代码',
    `vendor_name`      String comment '厂商名称',
    `eqpt_type`        String comment '设备类型代码',
    `eqpt_type_Name`   String comment '设备类型名称',
    `eqpt_ip`          String comment '设备IP',
    `send_count`       UInt64 comment '发送量',
    `recevie_count`    UInt64 comment '接送量'
    ) ENGINE = Distributed('cluster_nx_copy', '%s', 'eqpt_monitor_local', rand());
