CREATE TABLE IF NOT EXISTS mt_base_asset_type_local ON CLUSTER cluster_nx_copy (
    `id` UUID DEFAULT generateUUIDv4() COMMENT '唯一标志',
    `asset_type` String COMMENT '资产类型',
    `asset_code` Nullable(String) COMMENT '资产编码',
    `asset_label` Nullable(String) COMMENT '资产名称',
    `show_order` Nullable(String) COMMENT '排序',
    `parent_code` Nullable(String) COMMENT '父节点',
    `asset_desc` Nullable(String) COMMENT '资产描述',
    `create_time` DateTime64(3) DEFAULT now64() COMMENT '创建时间',
    `create_user` Nullable(String) COMMENT '创建人',
    `update_time` DateTime64(3) DEFAULT now64() COMMENT '更新时间',
    `update_user` Nullable(String) COMMENT '更新人',
    `flag` Nullable(String) DEFAULT CAST('1', 'Nullable(String)') COMMENT '删除标志(0-删除 1-正常)'
) ENGINE = ReplicatedMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_nx_copy}/mt_base_asset_type_local', '{replica}')
PARTITION BY toYYYYMM(create_time) ORDER BY create_time;

CREATE TABLE IF NOT EXISTS mt_base_asset_type ON CLUSTER cluster_nx_copy (
    `id` UUID DEFAULT generateUUIDv4() COMMENT '唯一标志',
    `asset_type` String COMMENT '资产类型',
    `asset_code` Nullable(String) COMMENT '资产编码',
    `asset_label` Nullable(String) COMMENT '资产名称',
    `show_order` Nullable(String) COMMENT '排序',
    `parent_code` Nullable(String) COMMENT '父节点',
    `asset_desc` Nullable(String) COMMENT '资产描述',
    `create_time` DateTime64(3) DEFAULT now64() COMMENT '创建时间',
    `create_user` Nullable(String) COMMENT '创建人',
    `update_time` DateTime64(3) DEFAULT now64() COMMENT '更新时间',
    `update_user` Nullable(String) COMMENT '更新人',
    `flag` Nullable(String) DEFAULT CAST('1', 'Nullable(String)') COMMENT '删除标志(0-删除 1-正常)'
) ENGINE = Distributed('cluster_nx_copy', '%s', 'mt_base_asset_type_local', rand());