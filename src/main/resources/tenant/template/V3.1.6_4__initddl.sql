ALTER table flow_metric_local on cluster cluster_1x_copy ADD COLUMN `read_status` Nullable(UInt8);

ALTER table flow_metric_local on cluster cluster_1x_copy ADD COLUMN `read_size` Nullable(UInt32);

ALTER table flow_metric_local on cluster cluster_1x_copy ADD COLUMN `write_size` Nullable(UInt32);

ALTER table flow_metric_local on cluster cluster_1x_copy ADD COLUMN `queue_size` Nullable(UInt32);

ALTER table flow_metric_local on cluster cluster_1x_copy ADD COLUMN `write_status` Nullable(String);

ALTER table flow_metric on cluster cluster_1x_copy ADD COLUMN `read_status` Nullable(UInt8);

ALTER table flow_metric on cluster cluster_1x_copy ADD COLUMN `read_size` Nullable(UInt32);

ALTER table flow_metric on cluster cluster_1x_copy ADD COLUMN `write_size` Nullable(UInt32);

ALTER table flow_metric on cluster cluster_1x_copy ADD COLUMN `queue_size` Nullable(UInt32);

ALTER table flow_metric on cluster cluster_1x_copy ADD COLUMN `write_status` Nullable(String);