CREATE TABLE mt_base_dept_local ON CLUSTER cluster_nx_copy (
    `dept_id` UUID DEFAULT generateUUIDv4() COMMENT '唯一标志',
    `dept_name` Nullable(String) COMMENT '部门名称',
    `parent_id` Nullable(String) COMMENT '上级部门ID',
    `dept_desc` Nullable(String) COMMENT '部门描述',
    `create_time` DateTime64(3) DEFAULT now64() COMMENT '创建时间',
    `create_user` Nullable(String) COMMENT '创建人',
    `update_time` DateTime64(3) DEFAULT now64() COMMENT '更新日期',
    `update_user` Nullable(String) COMMENT '更新人',
    `flag` Nullable(String) DEFAULT CAST('1', 'Nullable(String)')  COMMENT '删除标志(0-删除 1-正常)'
    ) ENGINE = ReplicatedMergeTree('/clickhouse/%s/tables/{layer}-{shard_for_nx_copy}/mt_base_dept_local', '{replica}')
    PARTITION BY toYYYYMM(create_time) ORDER BY create_time;

CREATE TABLE mt_base_dept ON CLUSTER cluster_nx_copy (
    `dept_id` UUID DEFAULT generateUUIDv4() COMMENT '唯一标志',
    `dept_name` Nullable(String) COMMENT '部门名称',
    `parent_id` Nullable(String) COMMENT '上级部门ID',
    `dept_desc` Nullable(String) COMMENT '部门描述',
    `create_time` DateTime64(3) DEFAULT now64() COMMENT '创建时间',
    `create_user` Nullable(String) COMMENT '创建人',
    `update_time` DateTime64(3) DEFAULT now64() COMMENT '更新日期',
    `update_user` Nullable(String) COMMENT '更新人',
    `flag` Nullable(String) DEFAULT CAST('1', 'Nullable(String)')  COMMENT '删除标志(0-删除 1-正常)'
    ) ENGINE = Distributed(cluster_nx_copy, '%s', mt_base_dept_local, rand());