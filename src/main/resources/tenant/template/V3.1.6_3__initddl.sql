CREATE TABLE IF NOT EXISTS asset_safe_domain ON CLUSTER cluster_nx_copy (
    id Nullable(Int64) comment '主键',
    name Nullable(String) comment '安全域名称',
    physics_location Nullable(String) comment '物理位置',
    ip_type Nullable(Int8) comment 'ip类型,1ipv4,2ipv6',
    domain_type Nullable(Int8) comment '域类型,1安全域,2非安全域',
    remark Nullable(String) comment '描述',
    member Nullable(String) comment 'ip集合',
    safe_domain_type Nullable(String) comment '类型,json格式',
    origin_param Nullable(String) comment '原始数据类型',
    is_del Nullable(Int8) comment '是否删除,1是,0否',
    create_time Nullable(DateTime) comment '创建时间',
    create_user Nullable(String) comment '创建人',
    update_time Nullable(DateTime) comment '更新时间',
    update_user Nullable(String) comment '更新人'
) ENGINE = PostgreSQL('%s','%s', 'asset_safe_domain', '%s','%s','%s');