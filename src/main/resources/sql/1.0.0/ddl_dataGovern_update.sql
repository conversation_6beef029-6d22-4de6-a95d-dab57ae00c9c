ALTER TABLE qua_web_hive_element_detail_column ADD COLUMN business_type bigint(20) DEFAULT NULL COMMENT '业务系统';
ALTER TABLE qua_web_ch_element_detail_column ADD COLUMN business_type bigint(20) DEFAULT NULL COMMENT '业务系统';
ALTER TABLE qua_web_mysql_element_detail_column ADD COLUMN business_type bigint(20) DEFAULT NULL COMMENT '业务系统';
ALTER TABLE qua_web_es_element_detail_field ADD COLUMN business_type bigint(20) DEFAULT NULL COMMENT '业务系统';

alter table qua_monitor_rule_type add column type_desc varchar(1024) comment '规则描述';
update qua_monitor_rule_type set type_desc = '用于检查字段是否为空' where type_code = 'NULLVALUE';
update qua_monitor_rule_type set type_desc = '用于检查关键指标取值范围' where type_code = 'RANGEVALUE';
update qua_monitor_rule_type set type_desc = '检查字符型字段的类型是否规范' where type_code = 'NORM';
update qua_monitor_rule_type set type_desc = '检查一张表内的重复数据' where type_code = 'REPEAT';
update qua_monitor_rule_type set type_desc = '用于检查数据上报的及时性' where type_code = 'TIMELY';
update qua_monitor_rule_type set type_desc = '用于检查想对比照表数据是否有效' where type_code = 'QUOTE';
update qua_monitor_rule_type set type_desc = '用于检查指标值的波动范围' where type_code = 'FLUCTUATION';

update ums_sys_menus set  menu_name = '稽核数据源管理',default_name='稽核数据源管理' where menu_code = 'data-lake-governance-quality-model';
update ums_sys_menus set menu_level=3,menu_name = '模型管理',default_name='模型管理',parent_name='data-lake-governance-quality',default_parent='data-lake-governance-quality' where menu_code = 'data-lake-governance-quality-rule';
update ums_sys_menus set menu_level=3,menu_name = '任务管理',default_name='任务管理',parent_name='data-lake-governance-quality',default_parent='data-lake-governance-quality' where menu_code = 'data-lake-governance-quality-task';
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `root_parent`) VALUES ('builtIn', '1', '3', '规则管理', 'data-lake-governance-rule-manage', NULL, '0', '0', 'data-lake-governance-quality', '1', '40', '40', '规则管理', '1', 'data-lake-governance-quality', NULL, NULL, NULL, NULL, 'data-lake-governance');
INSERT INTO `ums_sys_role_function` (`role_id`, `function_id`, `value`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('140d6cf17d244e74855ecdd56c40335c', 'data-lake-governance-rule-manage', '15', '系统管理员', '1645422238000', '系统管理员', '1645422238000');


ALTER TABLE ums_sys_menus ADD application varchar(100) NULL ;
ALTER TABLE ums_sys_menus ADD level1 varchar(100) NULL ;
alter table tb_tenant modify FUNCTIONS varchar(200) null comment '功能配置';

-- 新增字段
ALTER TABLE tb_dic ADD unique_check_code varchar(100) NULL COMMENT '页面复选框互斥编码';
ALTER TABLE tb_dic ADD hidden INT DEFAULT 0 NULL COMMENT '是否隐藏，0：不隐藏，1：隐藏';

-- 更新值
UPDATE tb_dic SET hidden=1 WHERE alias in ('data-lake-home-tenant','system');

-- 插入数据
INSERT INTO tb_dic (NAME, ALIAS,UNIQUE_CHECK_CODE, PARENT_ID) VALUES('数据开发(自研)', 'data-development-inner','data-development', 2);
INSERT INTO tb_dic (NAME, ALIAS,UNIQUE_CHECK_CODE, PARENT_ID) VALUES('数据开发(第三方)', 'data-development-outer','data-development', 2);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID) VALUES('数据集市', 'data-mart', 2);
