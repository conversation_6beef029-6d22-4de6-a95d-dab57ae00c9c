INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(1, '租户关联区域', 'area', 0);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(2, '配置功能', 'function', 0);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(21, '杭州', 'HZ', 1);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(22, '宁波', 'NB', 1);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(23, '温州', 'WZ', 1);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(24, '嘉兴', 'JX', 1);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(25, '湖州', 'HZ', 1);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(26, '绍兴', 'SX', 1);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(27, '金华', 'JH', 1);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(28, '舟山', 'ZS', 1);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(29, '台州', 'TZ', 1);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(30, '衢州', 'QZ', 1);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(31, '丽水', 'LS', 1);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(32, '数据采集', 'insight', 2);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(34, '数据治理', 'data-lake-governance', 2);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(36, '数据共享', 'data', 2);
INSERT INTO tb_dic (ID, NAME, ALIAS, PARENT_ID) VALUES(37, '系统管理', 'system', 2);

INSERT INTO `tb_tenant` (`TENANT_CODE`, `TENANT_NAME`, `ACCOUNT_TYPE`, `CONTACT_MOBILE`, `DEL_FLAG`, `CONTACT_AREA`, `FUNCTIONS`, `CREATE_USER`, `CREATE_TIME`, `UPDATE_USER`, `UPDATE_TIME`, `ACCOUNT_NAME`, `ACCOUNT_PWD`, `TENANT_ID`) VALUES ('tenantadmin', '超级管理员', '0', '***********', '0', '22', '', '', '2022-07-22 13:39:23', '', '2022-07-22 13:39:23', 'tenantadmin', '4c5c6205bb2c5de0fffac65865b83594', NULL);

INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '1', 'system', '系统管理', 'system', NULL, '0', '0', NULL, '1', '70', '70', '系统', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '2', 'system', '租户管理', 'tenant-manage', NULL, '0', '0', 'system', '1', '1', '70', '租户管理', '1', 'system', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '2', 'system', '资源管理', 'cluster-resource-manage', NULL, '0', '0', 'system', '1', '1', '80', '资源管理', '1', 'system', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'system', '集群管理', 'cluster-manage', NULL, '0', '0', 'cluster-resource-manage', '1', '1', '10', '集群管理', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'system', '数仓管理', 'clickhouse-manage', NULL, '0', '0', 'cluster-resource-manage', '1', '1', '20', '数仓管理', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'system', '数仓详情', 'clickhouse-detail', NULL, '0', '0', 'cluster-resource-manage', '1', '1', '30', '数仓详情', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'system', 'ES管理', 'elasticsearch-manage', NULL, '0', '0', 'cluster-resource-manage', '1', '1', '40', 'ES管理', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'system', 'ES详情', 'elasticsearch-detail', NULL, '0', '0', 'cluster-resource-manage', '1', '1', '50', 'ES详情', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'system', 'Kafka管理', 'kafka-manage', NULL, '0', '0', 'cluster-resource-manage', '1', '1', '60', 'Kafka管理', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'system', 'Kafka详情', 'kafka-detail', NULL, '0', '0', 'cluster-resource-manage', '1', '1', '70', 'Kafka详情', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'system', 'MySQL管理', 'mysql-manage', NULL, '0', '0', 'cluster-resource-manage', '1', '1', '80', 'MySQL管理', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'system', 'MySQL详情', 'mysql-detail', NULL, '0', '0', 'cluster-resource-manage', '1', '1', '90', 'MySQL详情', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'system', 'HDFS管理', 'hdfs-manage', NULL, '0', '0', 'cluster-resource-manage', '1', '1', '100', 'HDFS管理', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '3', 'system', 'HDFS详情', 'hdfs-detail', NULL, '0', '0', 'cluster-resource-manage', '1', '1', '110', 'HDFS详情', '1', 'resource-manage', NULL, NULL, NULL, NULL);
INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '2', 'system', '授权管理', 'license-manage', NULL, '0', '0', 'system', '1', '90', '90', '授权管理', '1', 'system', NULL, NULL, NULL, NULL);

INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`) VALUES ('builtIn', '1', '1', 'data-lake-home', '首页', 'data-lake-home', NULL, '0', '0', NULL, '1', '10', '10', '系统', '1', NULL, NULL, NULL, NULL, NULL);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1) VALUES( 'builtIn', '1', 1, '4a-user-manage', '4A用户管理', '4a-user-manage', NULL, '0', '0', NULL, '1', 11, 11, '4A用户管理', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ums_sys_menus` (`menu_type`, `menu_property`, `menu_level`, `root_parent`, `menu_name`, `menu_code`, `menu_path`, `manage_free`, `hidden`, `parent_name`, `status`, `menu_order`, `default_order`, `default_name`, `default_status`, `default_parent`, `create_user`, `create_date`, `update_user`, `update_date`, `application`, `level1`) VALUES ('builtIn', '1', '2', NULL, 'EasyGo', 'easygo', '', '0', '0', 'system', '1', '80', '80', 'EasyGo', '1', 'system', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO qua_monitor_rule_type (`type_code`,`type_name`,`config_key`,`config_name`,`config_type`,`is_required`,`reg_pattern`,`interface_name`,`multi_select`,`sort_no`) VALUES
('NULLVALUE','空值检查','unionNull','勾选表示所有的字段不能同时为空，不勾选表示每个字段都不能为空','checkbox','1','','','0',1),
('RANGEVALUE','值域检查','rangeValue','值域范围(字符)','input','1','','','0',1),
('NORM','规范检查','normType','规则类型','select','1','','norm','0',1),
('LOGIC','逻辑检查','logicMath','检查公式','input','1','','','0',1),
('REPEAT','重复数据检查','','','','','','','0',1),
('TIMELY','及时性检查','days','最大误差天数','input','1','','','0',1),
('QUOTE','引用完整性检查','quoteTable','比对表','select','1','','','0',1),
('QUOTE','引用完整性检查','quoteColumn','比对字段','select','1','','','1',1),
('FLUCTUATION','波动检查','baseLine','波动基线值','input','1','','','0',1),
('FLUCTUATION','波动检查','fluctuationRange','波动范围','input','1','','','0',1),
('STANDARD','数据标准检查','standards','选择规范','select','1','','','0',1),
('STANDARD','数据标准检查','checkType','检查类型','select','1','','/lake/api/dataQuality/rule/normType','0',1);

INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '申诉管理', 'appeal-manage', null, '0', '0', 'system', '1', 56, 56, '申诉管理', '1', 'system', null, null, null, null, 'application', null, 'application', '0');
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, root_parent, application_code, application, level1) VALUES ('builtIn', '1', 3, '我的申诉', 'appeal-self', null, '0', '0', 'system', '1', 57, 57, '我的申诉', '1', 'system', null, null, null, null, 'application', null, 'application', '0');


