-- 数据共享相关的表

CREATE TABLE `tb_cluster` (
  `ID` bigint NOT NULL AUTO_INCREMENT,
  `CLUSTER_NAME` varchar(100) DEFAULT NULL,
  `CLUSTER_TYPE` varchar(100) DEFAULT NULL,
  `CLUSTER_IP` varchar(100) DEFAULT NULL,
  `CLUSTER_PORT` varchar(100) DEFAULT NULL,
  `CLUSTER_USERNAME` varchar(100) DEFAULT NULL,
  `CLUSTER_PASSWORD` varchar(100) DEFAULT NULL,
  `CLUSTER_VERSION` varchar(100) DEFAULT NULL,
  `JMX_USERNAME` varchar(100) DEFAULT NULL,
  `JMX_PASSWORD` varchar(100) DEFAULT NULL,
  `ZOOKEEPER` varchar(100) DEFAULT NULL,
  `CLUSTER_AUTH_TYPE` varchar(100) DEFAULT NULL,
  `DEL_FLAG` varchar(1) CHARACTER SET utf8mb4  DEFAULT '0',
  `STATUS` varchar(1) CHARACTER SET utf8mb4  DEFAULT '0',
  `CREATE_TIME` timestamp NULL DEFAULT NULL,
  `CREATE_USER` varchar(100) DEFAULT NULL,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `UPDATE_USER` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- tb_datasource_info definition

CREATE TABLE `tb_datasource_info` (
  `ID` bigint NOT NULL AUTO_INCREMENT,
  `TENANT_ID` bigint DEFAULT NULL,
  `DATASOURCE_URL` varchar(200) DEFAULT NULL,
  `DATASOURCE_DRIVER` varchar(128) CHARACTER SET utf8mb4  DEFAULT NULL,
  `DATASOURCE_USERNAME` varchar(128) CHARACTER SET utf8mb4  DEFAULT NULL,
  `DATASOURCE_PASSWORD` varchar(128) DEFAULT NULL,
  `DATASOURCE_TYPE` varchar(128) CHARACTER SET utf8mb4  DEFAULT NULL,
  `DEL_FALG` varchar(1) DEFAULT '0',
  `STATUS` varchar(1) CHARACTER SET utf8mb4 DEFAULT '0',
  `CREATE_TIME` timestamp NULL DEFAULT NULL,
  `CREATE_USER` varchar(100) DEFAULT NULL,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `UPDATE_USER` varchar(100) DEFAULT NULL,
  `CLUSTER_ID`   bigint    null,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- tb_dic definition

CREATE TABLE `tb_dic` (
  `ID` bigint NOT NULL AUTO_INCREMENT,
  `NAME` varchar(100) CHARACTER SET utf8mb4  DEFAULT NULL,
  `ALIAS` varchar(100) CHARACTER SET utf8mb4  DEFAULT NULL,
  `PARENT_ID` bigint DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;;


-- tb_tenant definition
CREATE TABLE `tb_tenant` (
  `ID` bigint NOT NULL AUTO_INCREMENT,
  `TENANT_CODE` varchar(200) CHARACTER SET utf8mb4  DEFAULT NULL COMMENT '租户编码',
  `TENANT_NAME` varchar(255) CHARACTER SET utf8mb4  DEFAULT NULL COMMENT '租户名称',
  `ACCOUNT_TYPE` varchar(1) CHARACTER SET utf8mb4  DEFAULT NULL COMMENT '租户用户类型',
  `CONTACT_MOBILE` varchar(30) CHARACTER SET utf8mb4  DEFAULT NULL COMMENT '联系电话',
  `DEL_FLAG` varchar(1) CHARACTER SET utf8mb4  DEFAULT '0' COMMENT '删除标识',
  `CONTACT_AREA` varchar(100) CHARACTER SET utf8mb4  DEFAULT NULL COMMENT '关联区域',
  `FUNCTIONS` varchar(100) CHARACTER SET utf8mb4  DEFAULT NULL COMMENT '功能配置',
  `RESOURCE_STATUS` varchar(2) CHARACTER SET utf8mb4  DEFAULT '0' COMMENT '租户资源状态 0-未分配 1-分配中 2-分配成功 3-分配失败',
  `CREATE_USER` varchar(50) CHARACTER SET utf8mb4  DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT NULL,
  `UPDATE_USER` varchar(50) CHARACTER SET utf8mb4  DEFAULT NULL,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `ACCOUNT_NAME` varchar(100) CHARACTER SET utf8mb4  DEFAULT NULL COMMENT '用户账户',
  `ACCOUNT_PWD` varchar(100) CHARACTER SET utf8mb4  DEFAULT NULL COMMENT '用户密码',
  `TENANT_ID` bigint DEFAULT NULL,
  `DB_NAME`   varchar(100)  null,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ums_sys_log
create table ums_sys_log
(
    id           int(11) unsigned auto_increment
        primary key,
    user_name    varchar(16)   default '' not null comment '用户名',
    real_name    varchar(16)   default '' not null comment '真实姓名',
    login_ip     varchar(32)   default '' not null comment '登录ip',
    user_agent   varchar(256)  default '' not null comment 'UA',
    request_path varchar(64)   default '' not null comment '请求路径',
    log_name     varchar(2048) default '' not null comment '操作名称',
    log_result   varchar(32)   default '' not null comment '操作结果',
    opt_type     varchar(32)   default '' not null comment '操作类型',
    opt_module   varchar(64)   default '' not null comment '操作模块',
    http_method  varchar(16)   default '' not null comment '请求方式',
    create_time  datetime                 not null comment '创建时间'
)
    comment '日志审计表' ;

create table tb_statistics_cluster_instance_disk_sync
(
    ID           bigint auto_increment comment '主键id'
        primary key,
    TENANT_ID    bigint       null comment '租户id',
    CLUSTER_ID   bigint       null comment '集群id',
    CLUSTER_TYPE varchar(100) null comment '集群类型',
    INSTANCE     varchar(100) null comment '实例',
    used         bigint       null comment '使用量，单位M',
    create_time  datetime     null comment '入库时间',
    free         bigint       null comment '剩余空间，单位M'
)
    comment '租户关联集群实例的磁盘使用量同步（mysql,ch是库，hdfs是目录）' charset = utf8mb4;

-- auto-generated definition
create table tb_statistics_disk
(
    id            int auto_increment
        primary key,
    resource_type varchar(100)  null comment '资源类型，clickhouse，mysql，hive，elasticsearch',
    used          bigint        null comment '已用，单位m',
    free          bigint        null comment '空闲，单位m',
    create_time   datetime      null comment '入库时间',
    flag          int default 1 null comment '1有效，0无效'
)
    comment '租户使用磁盘空间统计表' charset = utf8mb4;

-- auto-generated definition
create table tb_statistics_disk_sync
(
    id          int auto_increment
        primary key,
    ip          varchar(100)  not null comment '所属服务器IP',
    file_system varchar(100)  null comment '文件系统',
    total       bigint        null comment '总量',
    used        bigint        null comment '已用',
    free        bigint        null comment '空闲',
    used_rate   varchar(100)  null comment '使用率',
    mount_point varchar(100)  null comment '挂载点',
    create_time datetime      null comment '入库时间',
    flag        int default 1 null comment '1有效，0无效'
)
    comment '磁盘空间同步表' charset = utf8mb4;

create table tb_statistics_disk_tenant
(
    id                 int auto_increment
        primary key,
    tenant_id          bigint        null comment '租户id',
    tenant_name        varchar(100)  null comment '租户名称',
    clickhouse_used    bigint        null comment '已用，单位m',
    clickhouse_free    bigint        null comment '空闲，单位m',
    mysql_used         bigint        null comment '已用，单位m',
    mysql_free         bigint        null comment '空闲，单位m',
    hdfs_used          bigint        null comment '已用，单位m',
    hdfs_free          bigint        null comment '空闲，单位m',
    elasticsearch_used bigint        null comment '已用，单位m',
    elasticsearch_free bigint        null comment '空闲，单位m',
    create_time        datetime      null comment '入库时间',
    flag               int default 1 null comment '1有效，0无效'
)
    comment '租户使用磁盘空间统计表' charset = utf8mb4;

create table tb_statistics_etl_data
(
    id          int auto_increment
        primary key,
    etl_name    varchar(100)  not null comment '采集日志名称',
    etl_count   bigint        null comment '采集数量',
    create_time datetime      null comment '入库时间',
    flag        int default 1 null comment '1有效，0无效'
)
    comment '数据采集量统计表' charset = utf8mb4;

create table tb_statistics_etl_task
(
    id          int auto_increment
        primary key,
    etl_type    varchar(100)  not null comment '采集类型：信安，网安，数安',
    total       bigint        null comment '总量',
    create_time datetime      null comment '入库时间',
    flag        int default 1 null comment '1有效，0无效'
)
    comment '采集任务统计表' charset = utf8mb4;

create table tb_statistics_etl_task_day
(
    id          int auto_increment
        primary key,
    day         varchar(100)  not null comment '日期',
    task_count  bigint        null comment '采集任务数量',
    create_time datetime      null comment '入库时间',
    flag        int default 1 null comment '1有效，0无效'
)
    comment '数据采集任务趋势日统计表' charset = utf8mb4;

create table tb_statistics_hdfs_sync
(
    id          int auto_increment
        primary key,
    ip          varchar(100)  not null comment '所属服务器IP',
    dir         varchar(100)  null comment '文件系统目录',
    size        bigint        null comment '大小，单位b',
    create_time datetime      null comment '入库时间',
    flag        int default 1 null comment '1有效，0无效'
)
    comment 'hdfs磁盘空间同步表' charset = utf8mb4;

create table tb_statistics_subscription
(
    id                   int auto_increment
        primary key,
    tenant_id            int           not null comment '租户id',
    subscription_service varchar(100)  null comment '订阅服务',
    create_time          datetime      null comment '入库时间',
    flag                 int default 1 null comment '1有效，0无效'
)
    comment '租户订阅服务统计表' charset = utf8mb4;


-- tb_tenant_cluster definition

CREATE TABLE `tb_tenant_cluster` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `TENANT_ID` bigint DEFAULT NULL COMMENT '租户id、',
  `CLUSTER_ID` bigint DEFAULT NULL COMMENT '集群id',
  `CLUSTER_TYPE` varchar(100) DEFAULT NULL COMMENT '集群类型',
  `INSTANCE` varchar(100) DEFAULT NULL COMMENT '实例',
  `TOPIC` varchar(100) DEFAULT NULL COMMENT 'topic',
  `QUOTA` varchar(100) DEFAULT NULL COMMENT '配额',
  `SHARDS` varchar(100) DEFAULT NULL COMMENT '分片',
  `REPLICAS` varchar(100) DEFAULT NULL COMMENT '副本',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `ums_sys_menus`;
CREATE TABLE `ums_sys_menus` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `menu_type` varchar(32) NOT NULL COMMENT '菜单类型(builtIn-内置菜单 custom-自定义菜单)',
  `menu_property` varchar(2) NOT NULL COMMENT '菜单属性 0-不允许有子菜单 1-允许有子菜单',
  `menu_level` int(11) NOT NULL COMMENT '菜单层级',
  `root_parent` varchar(64) DEFAULT NULL COMMENT '菜单对应的一级菜单',
  `menu_name` varchar(64) NOT NULL COMMENT '菜单名称',
  `menu_code` varchar(64) NOT NULL COMMENT '菜单唯一标识',
  `menu_path` varchar(512) DEFAULT NULL COMMENT '菜单路径',
  `manage_free` varchar(2) DEFAULT '0',
  `hidden` varchar(2) DEFAULT '0' COMMENT '是否隐藏',
  `parent_name` varchar(64) DEFAULT NULL COMMENT '父级菜单',
  `status` varchar(2) DEFAULT '1' COMMENT '状态（0 表示禁用，1表示启用）',
  `menu_order` int(11) DEFAULT NULL COMMENT '菜单顺序',
  `default_order` int(11) NOT NULL COMMENT '默认顺序',
  `default_name` varchar(64) NOT NULL COMMENT '默认名称',
  `default_status` varchar(2) NOT NULL COMMENT '默认状态',
  `default_parent` varchar(64) DEFAULT NULL COMMENT '默认父级',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统菜单表';

DROP TABLE IF EXISTS `zeppelin_interpreter`;
CREATE TABLE `zeppelin_interpreter`
(
    `id`                    int(11) NOT NULL AUTO_INCREMENT,
    `tenant_id`             varchar(255) NOT NULL COMMENT '租户id',
    `interpreter_remote_id` varchar(255) NOT NULL COMMENT 'interpreter 服务器上id',
    `interpreter_alias`     varchar(255) DEFAULT '' COMMENT 'interpreter别名，用于页面展示',
    `create_time`           datetime     DEFAULT NULL COMMENT '创建时间',
    `create_user`           varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     DEFAULT NULL COMMENT '更新时间',
    `update_user`           varchar(255) DEFAULT NULL COMMENT '更新人',
    `interpreter_type`      varchar(100) DEFAULT NULL COMMENT '类型',
    `interpreter_name`      varchar(100) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `zeppelin_notebook`;
CREATE TABLE `zeppelin_notebook`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT,
    `tenant_id`      varchar(255) NOT NULL COMMENT '租户id',
    `note_remote_id` varchar(255) NOT NULL COMMENT 'notebook 服务器上id',
    `note_alias`     varchar(255) DEFAULT '' COMMENT 'notebook别名，用于页面展示',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `create_user`    varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_time`    datetime     DEFAULT NULL COMMENT '更新时间',
    `update_user`    varchar(255) DEFAULT NULL COMMENT '更新人',
    `interpreter_id` int(11) DEFAULT NULL COMMENT 'zeppelin_interpreter 主键',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `tb_script_history`;
CREATE TABLE `tb_script_history`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT,
    `tenant_id`      varchar(255) NOT NULL COMMENT '租户id',
    `cluster_id`      varchar(255)  DEFAULT NULL COMMENT '集群id',
    `del_flag`      varchar(255) DEFAULT '0' NOT NULL COMMENT '删除标识',
    `script_file`      varchar(255) NOT NULL COMMENT '脚本文件',
    `script_type`      varchar(32) NOT NULL COMMENT '文件类型 template sql',
    `db_type`      varchar(32) NOT NULL COMMENT '数据库类型 mysql clickhouse',
    `cluster_id` varchar(100) DEFAULT NULL,
    `del_flag` varchar(100) DEFAULT '0',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `create_user`    varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_time`    datetime     DEFAULT NULL COMMENT '更新时间',
    `update_user`    varchar(255) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='脚本执行历史';

CREATE TABLE `tb_monitor_topic` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `topic` varchar(100) DEFAULT NULL,
  `lags` varchar(100) DEFAULT NULL,
  `time` timestamp NULL DEFAULT NULL,
  `cluster_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;


CREATE TABLE `ums_sys_license` (
  `license` varbinary(2048) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

create table ums_sys_datasource_config
(
    id              int(11) unsigned auto_increment
        primary key,
    moudle_name     varchar(32)  null comment '配置模块 dashboard-仪表盘  schedule-模型调度',
    datasource_type varchar(16)  null comment '数据源类型',
    ip              varchar(64)  null comment 'IP地址',
    db_name         varchar(64)  null comment '数据库名',
    port            varchar(16)  null comment '端口',
    user_name       varchar(128) null comment '用户名',
    password        varchar(128) null comment '密码',
    show_name       varchar(128) null comment '展示名',
    time_out        int          null comment '超时时间',
    encrypt         varchar(2)   null comment '是否加密 1-加密 0-不加密',
    status          varchar(2)   null comment '1-正常 0-删除',
    copy_cnt        int          null comment '复制次数',
    db_url          varchar(512) null comment '数据库连接字符串',
    create_user     varchar(16)  null comment '创建账号',
    create_time     datetime     null comment '创建时间',
    update_user     varchar(16)  null comment '更新账号',
    update_time     datetime     null comment '更新时间'
)
    comment '数据源配置' charset = utf8;

-- data_lake_test.tb_cluster_kafka_topic definition

CREATE TABLE `tb_cluster_kafka_topic`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `topic`       varchar(100) DEFAULT NULL COMMENT 'topic名称',
    `tenant_name` varchar(100) DEFAULT NULL COMMENT '所属租户',
    `partitions`  int(11) DEFAULT NULL COMMENT '分区数',
    `brokers`     int(11) DEFAULT NULL COMMENT '节点数',
    `lags`        bigint(20) DEFAULT NULL,
    `data_name`   varchar(100) DEFAULT NULL,
    `flow_id`     varchar(100) DEFAULT NULL,
    `groups`      int(11) DEFAULT NULL,
    `replicas`    int(11) DEFAULT NULL,
    `spread`      varchar(100) DEFAULT NULL,
    `create_time` datetime     DEFAULT NULL,
    `is_deleted`  int(11) DEFAULT '0' COMMENT '是否删除，0否1是',
    `cluster_id`  bigint(20) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=151 DEFAULT CHARSET=utf8 COMMENT='集群管理-kafka详情-定时统计表';