-- 数据治理相关的表
SET FOREIGN_KEY_CHECKS=0;

DROP TABLE IF EXISTS `qua_scan_ch_column`;
CREATE TABLE `qua_scan_ch_column` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_no` varchar(20) NOT NULL COMMENT '任务编号',
  `table_id` bigint(20) NOT NULL COMMENT '表ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `db_name` varchar(128) NOT NULL COMMENT '数据库名称',
  `table_name` varchar(128) NOT NULL COMMENT '数据表名称',
  `column_name` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `type` varchar(255) DEFAULT NULL COMMENT '字段类型',
  `position` bigint(20) DEFAULT NULL COMMENT '字段序号，取值从1开始',
  `default_kind` varchar(255) DEFAULT NULL COMMENT '字段默认值类型，取值：DEFAULT、ALIAS',
  `default_expression` varchar(255) DEFAULT NULL COMMENT '字段默认值表达式，如：now()、CAST(1, ''Int32'')',
  `data_compressed_bytes` bigint(20) DEFAULT NULL COMMENT '数据压缩后字节数',
  `data_uncompressed_bytes` bigint(20) DEFAULT NULL COMMENT '数据未压缩字节数',
  `marks_bytes` bigint(20) DEFAULT NULL COMMENT '标记的大小',
  `comment` varchar(1000) DEFAULT NULL COMMENT '字段描述',
  `is_in_partition_key` tinyint(1) DEFAULT NULL COMMENT '是否属于分区字段',
  `is_in_sorting_key` tinyint(1) DEFAULT NULL COMMENT '是否属于排序字段',
  `is_in_primary_key` tinyint(1) DEFAULT NULL COMMENT '是否属于主键字段',
  `is_in_sampling_key` tinyint(1) DEFAULT NULL COMMENT '是否属于抽样字段',
  `compression_codec` varchar(255) DEFAULT NULL COMMENT '压缩编码器名称',
  `is_nullable` tinyint(1) DEFAULT NULL COMMENT '可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串',
  `column_length` int(11) DEFAULT NULL COMMENT '字段长度（bit），根据字段类型不同而不同，目前仅支持数值型字段',
  `column_precision` int(11) DEFAULT NULL COMMENT '字段精度（bit），仅针对浮点数值型字段',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  PRIMARY KEY (`id`),
  KEY `qua_scan_ch_column_table_id_fk` (`table_id`),
  CONSTRAINT `qua_scan_ch_column_table_id_fk` FOREIGN KEY (`table_id`) REFERENCES `qua_scan_ch_table` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描CH字段结果表';

-- ----------------------------
-- Table structure for qua_scan_ch_db
-- ----------------------------
DROP TABLE IF EXISTS `qua_scan_ch_db`;
CREATE TABLE `qua_scan_ch_db` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_no` varchar(20) NOT NULL COMMENT '任务编号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `db_name` varchar(64) NOT NULL COMMENT '数据库名称',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描CH库结果表';

-- ----------------------------
-- Table structure for qua_scan_ch_table
-- ----------------------------
DROP TABLE IF EXISTS `qua_scan_ch_table`;
CREATE TABLE `qua_scan_ch_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_no` varchar(20) NOT NULL COMMENT '任务编号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `db_id` bigint(20) NOT NULL COMMENT '扫描的数据库ID',
  `db_name` varchar(30) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(128) NOT NULL COMMENT '数据表名称',
  `uuid` char(36) DEFAULT NULL COMMENT 'CH表系统ID',
  `engine` varchar(200) DEFAULT NULL COMMENT '引擎类型',
  `is_temporary` tinyint(1) DEFAULT NULL COMMENT '是否临时表',
  `data_paths` varchar(200) DEFAULT NULL COMMENT '数据地址',
  `metadata_path` varchar(200) DEFAULT NULL COMMENT '元数据地址',
  `metadata_modification_time` varchar(30) DEFAULT NULL COMMENT '元数据修改时间',
  `dependencies_database` varchar(200) DEFAULT NULL COMMENT '依赖数据库',
  `dependencies_table` varchar(500) DEFAULT NULL COMMENT '依赖数据表',
  `create_table_query` text COMMENT '建表语句',
  `engine_full` varchar(500) DEFAULT NULL COMMENT '引擎参数',
  `partition_key` varchar(200) DEFAULT NULL COMMENT '分区字段',
  `sorting_key` varchar(200) DEFAULT NULL COMMENT '排序字段',
  `primary_key` varchar(200) DEFAULT NULL COMMENT '主键字段',
  `sampling_key` varchar(200) DEFAULT NULL COMMENT '抽样字段',
  `storage_policy` varchar(200) DEFAULT NULL COMMENT '存储策略',
  `total_rows` bigint(20) DEFAULT NULL COMMENT '总行数',
  `total_bytes` bigint(20) DEFAULT NULL COMMENT '总字节数',
  `lifetime_rows` bigint(20) DEFAULT NULL COMMENT '插入总行数',
  `lifetime_bytes` bigint(20) DEFAULT NULL COMMENT '插入总字节数',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  PRIMARY KEY (`id`),
  KEY `qua_scan_ch_table_db_id_fk` (`db_id`),
  CONSTRAINT `qua_scan_ch_table_db_id_fk` FOREIGN KEY (`db_id`) REFERENCES `qua_scan_ch_db` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描CH表结果表';

-- ----------------------------
-- Table structure for qua_scan_es_field
-- ----------------------------
DROP TABLE IF EXISTS `qua_scan_es_field`;
CREATE TABLE `qua_scan_es_field` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_no` varchar(20) NOT NULL COMMENT '任务号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `index_id` bigint(20) NOT NULL COMMENT '扫描的索引ID',
  `index_name` varchar(255) DEFAULT NULL COMMENT '索引名称',
  `field_name` varchar(100) DEFAULT NULL COMMENT '字段名称',
  `field_data_type` varchar(255) DEFAULT NULL COMMENT '字段数据类型',
  `analyzer` varchar(500) DEFAULT NULL COMMENT '用于文本分析的分析器',
  `boost` varchar(500) DEFAULT NULL COMMENT '相关性得分计算参数',
  `coerce` varchar(500) DEFAULT NULL COMMENT '试图清除脏值以适应字段的数据类型',
  `copy_to` varchar(500) DEFAULT NULL COMMENT '将多个字段的值复制到组字段中，然后可以将组字段作为单个字段进行查询',
  `doc_values` varchar(500) DEFAULT NULL COMMENT '文档索引时构建的磁盘数据结构',
  `dynamic` varchar(500) DEFAULT NULL COMMENT '对包含新字段的文档进行索引时，动态地添加到文档或文档内部对象中',
  `eager_global_ordinals` varchar(500) DEFAULT NULL COMMENT '字段类型使用序号映射来存储文档值，以获得更紧凑的表示',
  `enabled` varchar(500) DEFAULT NULL COMMENT '可配置跳过对字段内容的解析和索引',
  `fielddata` varchar(500) DEFAULT NULL COMMENT '可配置文本字段在默认情况下用于聚合、排序或脚本编写',
  `fields` varchar(500) DEFAULT NULL COMMENT '以不同的方式为同一个字段建立索引通',
  `format` varchar(500) DEFAULT NULL COMMENT '配置识别日期字符串',
  `ignore_above` varchar(500) DEFAULT NULL COMMENT '超过ignore_above设置的字符串将不会被索引或存储',
  `ignore_malformed` varchar(500) DEFAULT NULL COMMENT '允许忽略异常。不正确的字段不会被索引，但是文档中的其他字段可被正常处理',
  `index_options` varchar(500) DEFAULT NULL COMMENT '控制向反向索引可添加哪些信息以进行搜索和高亮显示',
  `index_phrases` varchar(500) DEFAULT NULL COMMENT '可配置允许精确短语查询更有效地运行，以牺牲更大的索引为代价',
  `index_prefixes` varchar(500) DEFAULT NULL COMMENT '可允许对词汇前缀进行索引，从而加快前缀搜索',
  `index` varchar(500) DEFAULT NULL COMMENT 'index选项控制是否对字段值进行索引，没有索引的字段是不可查询的',
  `meta` varchar(500) DEFAULT NULL COMMENT '附加到字段的元数据，用于共享关于字段的元信息',
  `normalizer` varchar(500) DEFAULT NULL COMMENT '自定义规范化器可产生单个token',
  `norms` varchar(500) DEFAULT NULL COMMENT 'Norms存储了以后在查询时使用的各种规范化因子，以便计算文档相对于查询的得分',
  `null_value` varchar(500) DEFAULT NULL COMMENT '允许使用指定的值替换显式的空值，这样就可以对它进行索引和搜索',
  `position_increment_gap` varchar(500) DEFAULT NULL COMMENT '用以防止多个短语查询跨值匹配',
  `properties` text COMMENT '属性可以是任何数据类型，包括对象和嵌套',
  `search_analyzer` varchar(500) DEFAULT NULL COMMENT '查询可配置为使用在字段映射中定义的分析器',
  `similarity` varchar(500) DEFAULT NULL COMMENT '可配置每个字段的评分算法或相似度算法',
  `store` varchar(500) DEFAULT NULL COMMENT '配置存储此字段',
  `term_vector` varchar(500) DEFAULT NULL COMMENT '配置分析过程产生的terms的信息',
  `field_length` int(11) DEFAULT NULL COMMENT '字段长度，根据字段类型不同而不同，目前支持数值型字段',
  `field_precision` int(11) DEFAULT NULL COMMENT '字段精度，仅针对浮点数值型字段',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  PRIMARY KEY (`id`),
  KEY `qua_scan_es_field_index_id_fk` (`index_id`),
  CONSTRAINT `qua_scan_es_field_index_id_fk` FOREIGN KEY (`index_id`) REFERENCES `qua_scan_es_index` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描ES字段结果表';

-- ----------------------------
-- Table structure for qua_scan_es_index
-- ----------------------------
DROP TABLE IF EXISTS `qua_scan_es_index`;
CREATE TABLE `qua_scan_es_index` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_no` varchar(20) NOT NULL COMMENT '任务编号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `cluster_name` varchar(255) DEFAULT NULL COMMENT '集群名称',
  `cluster_uuid` varchar(255) DEFAULT NULL COMMENT '集群UUID',
  `index_name` varchar(255) DEFAULT NULL COMMENT '索引名称',
  `type_name` varchar(100) DEFAULT NULL COMMENT '类型名称',
  `index_state` varchar(30) DEFAULT NULL COMMENT '索引状态',
  `index_settings` varchar(4000) DEFAULT NULL COMMENT '索引配置',
  `aliases` varchar(255) DEFAULT NULL COMMENT '索引别名',
  `primary_terms` varchar(5000) DEFAULT NULL COMMENT '主分片更新版本',
  `in_sync_allocations` varchar(5000) DEFAULT NULL COMMENT '分片分配标识',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描ES索引结果表';

-- ----------------------------
-- Table structure for qua_scan_list
-- ----------------------------
DROP TABLE IF EXISTS `qua_scan_list`;
CREATE TABLE `qua_scan_list` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_no` varchar(20) NOT NULL COMMENT '任务编号',
  `scan_type` varchar(20) DEFAULT NULL COMMENT '扫描类型：ES 或CH',
  `es_ip_port` varchar(1024) DEFAULT NULL COMMENT 'ES集群地址',
  `es_auth_type` tinyint(1) DEFAULT NULL COMMENT 'ES认证类型：1账密认证，2自上传文件认证，3选择文件认证',
  `es_user_name` varchar(60) DEFAULT NULL COMMENT 'ES用户名',
  `es_user_password` varchar(100) DEFAULT NULL COMMENT 'ES密码',
  `es_kbs_account` varchar(100) DEFAULT NULL COMMENT 'ES KBS账号',
  `es_keytab_file_path` varchar(255) DEFAULT NULL COMMENT 'ES keytab文件路径',
  `es_krb5_file_path` varchar(255) DEFAULT NULL COMMENT 'ES krb5文件路径',
  `es_jaas_file_path` varchar(255) DEFAULT NULL COMMENT 'ES jaas文件路径',
  `es_prop_file_path` varchar(255) DEFAULT NULL COMMENT 'ES认证配置文件路径',
  `es_kbs_template_id` bigint(20) DEFAULT NULL COMMENT 'ES 选择的模板ID',
  `ch_ip` varchar(20) DEFAULT NULL COMMENT 'CH IP',
  `ch_port` int(8) DEFAULT NULL COMMENT 'CH port',
  `ch_user_name` varchar(60) DEFAULT NULL COMMENT 'CH 用户名',
  `ch_user_password` varchar(100) DEFAULT NULL COMMENT 'CH 密码',
  `ch_is_ssl` tinyint(1) DEFAULT NULL COMMENT 'CH 是否SSL：0否，1是',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `scan_state` tinyint(1) DEFAULT '1' COMMENT '扫描状态：1扫描中，2扫描完成，3扫描失败',
  `scan_process` varchar(8) DEFAULT NULL COMMENT '扫描进度百分比  如：100%',
  `scan_fail_reason` varchar(1000) DEFAULT NULL COMMENT '扫描失败原因',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描任务表';

create table qua_server_used_statistic
(
    id          int auto_increment comment '主键id'
        primary key,
    tenant_id   int          null comment '租户id',
    server_type varchar(100) null comment '服务类型',
    create_time datetime     null comment '入库时间',
    create_date varchar(100) null comment '入库日期，yyyy-MM-dd'
)
    comment '服务使用频率统计' charset = latin1;

-- ----------------------------
-- Table structure for qua_wab_element
-- ----------------------------
DROP TABLE IF EXISTS `qua_wab_element`;
CREATE TABLE `qua_wab_element` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_type` varchar(20) DEFAULT NULL COMMENT '元数据类型 ：ES 或 CH',
  `element_name` varchar(100) NOT NULL COMMENT '元数据名称',
  `es_ip_port` varchar(1024) DEFAULT NULL COMMENT 'ES集群地址',
  `es_auth_type` tinyint(1) DEFAULT NULL COMMENT 'ES认证类型：1账密认证，2自上传文件认证，3选择文件认证',
  `es_user_name` varchar(60) DEFAULT NULL COMMENT 'ES用户名',
  `es_user_password` varchar(100) DEFAULT NULL COMMENT 'ES密码',
  `es_kbs_account` varchar(100) DEFAULT NULL COMMENT 'ES KBS账号',
  `es_keytab_file_path` varchar(255) DEFAULT NULL COMMENT 'ES keytab文件路径',
  `es_krb5_file_path` varchar(255) DEFAULT NULL COMMENT 'ES krb5文件路径',
  `es_jaas_file_path` varchar(255) DEFAULT NULL COMMENT 'ES jaas文件路径',
  `es_kbs_template_id` bigint(20) DEFAULT NULL COMMENT 'ES 选择的模板ID',
  `ch_ip` varchar(20) DEFAULT NULL COMMENT 'CH IP',
  `ch_port` int(8) DEFAULT NULL COMMENT 'CH port',
  `ch_user_name` varchar(60) DEFAULT NULL COMMENT 'CH 用户名',
  `ch_user_password` varchar(100) DEFAULT NULL COMMENT 'CH 密码',
  `ch_is_ssl` tinyint(4) DEFAULT '1' COMMENT 'CH是否SSL: 0否，1是',
  `is_connect` tinyint(1) NOT NULL COMMENT '是否连通，1：连通，2：不连通',
  `fail_connect_reason` varchar(100) DEFAULT NULL COMMENT '失败连通原因',
  `execute_type` varchar(20) NOT NULL COMMENT '执行类型',
  `config_json` varchar(200) NOT NULL COMMENT '执行频率配置JSON',
  `late_scan_time` timestamp NULL DEFAULT NULL COMMENT '最近抽取时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) NOT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) NOT NULL,
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `flag` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否有效，0无效，1有效，默认1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='元数据管理';

-- ----------------------------
-- Table structure for qua_web_ch_element_detail_column
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_ch_element_detail_column`;
CREATE TABLE `qua_web_ch_element_detail_column` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `db_name` varchar(64) DEFAULT NULL COMMENT '库名称',
  `table_name` varchar(255) DEFAULT NULL COMMENT '表名称',
  `column_name` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `column_name_cn` varchar(255) DEFAULT NULL COMMENT '字段中文名，默认为空字符串',
  `is_sensitive` tinyint(1) DEFAULT '0' COMMENT '是否为敏感表，默认不是',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `is_available` tinyint(1) DEFAULT '1' COMMENT '是否可用，默认为1，可用',
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
  business_type           bigint                               null comment '业务系统',
  PRIMARY KEY (`id`),
  KEY `idx_6` (`element_id`,`db_name`,`table_name`,`column_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CH清单Column表';


-- ----------------------------
-- Table structure for qua_web_ch_element_detail_db
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_ch_element_detail_db`;
CREATE TABLE `qua_web_ch_element_detail_db` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL,
  `db_name` varchar(64) DEFAULT NULL COMMENT '库名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ch_element_id_fk` (`element_id`),
  CONSTRAINT `ch_element_id_fk` FOREIGN KEY (`element_id`) REFERENCES `qua_wab_element` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CH清单DB表';

-- ----------------------------
-- Table structure for qua_web_ch_element_detail_table
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_ch_element_detail_table`;
CREATE TABLE `qua_web_ch_element_detail_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `db_name` varchar(64) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(255) DEFAULT NULL COMMENT '数据表名称',
  `table_name_cn` varchar(255) DEFAULT NULL COMMENT '表中文名',
  `table_dscribe` varchar(255) DEFAULT NULL COMMENT '表业务描述',
  `table_owner` varchar(255) DEFAULT NULL COMMENT '表业务负责人',
  `is_sensitive` tinyint(1) DEFAULT '0' COMMENT '是否为敏感表，默认不是',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `is_available` tinyint(1) DEFAULT '1' COMMENT '是否可用，默认为1，可用',
  `ext_attrs` varchar(1024) DEFAULT NULL COMMENT '自定义属性',
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_8` (`element_id`,`db_name`,`table_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CH清单Table表';

-- ----------------------------
-- Table structure for qua_web_ch_task_result_column
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_ch_task_result_column`;
CREATE TABLE `qua_web_ch_task_result_column` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `table_id` bigint(20) NOT NULL COMMENT '表ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建人',
  `db_name` varchar(128) NOT NULL COMMENT '数据库名称',
  `table_name` varchar(128) NOT NULL COMMENT '数据表名称',
  `column_name` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `type` varchar(255) DEFAULT NULL COMMENT '字段类型',
  `position` bigint(20) DEFAULT NULL COMMENT '字段序号，取值从1开始',
  `default_kind` varchar(255) DEFAULT NULL COMMENT '字段默认值类型，取值：DEFAULT、ALIAS',
  `default_expression` varchar(255) DEFAULT NULL COMMENT '字段默认值表达式，如：now()、CAST(1, ''Int32'')',
  `data_compressed_bytes` bigint(20) DEFAULT NULL COMMENT '数据压缩后字节数',
  `data_uncompressed_bytes` bigint(20) DEFAULT NULL COMMENT '数据未压缩字节数',
  `marks_bytes` bigint(20) DEFAULT NULL COMMENT '标记的大小',
  `comment` varchar(1000) DEFAULT NULL COMMENT '字段描述',
  `is_in_partition_key` tinyint(1) DEFAULT NULL COMMENT '是否属于分区字段',
  `is_in_sorting_key` tinyint(1) DEFAULT NULL COMMENT '是否属于排序字段',
  `is_in_primary_key` tinyint(1) DEFAULT NULL COMMENT '是否属于主键字段',
  `is_in_sampling_key` tinyint(1) DEFAULT NULL COMMENT '是否属于抽样字段',
  `compression_codec` varchar(255) DEFAULT NULL COMMENT '压缩编码器名称',
  `is_nullable` tinyint(1) DEFAULT NULL COMMENT '可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串',
  `column_length` int(11) DEFAULT NULL COMMENT '字段长度（bit），根据字段类型不同而不同，目前仅支持数值型字段',
  `column_precision` int(255) DEFAULT NULL COMMENT '字段精度（bit），仅针对浮点数值型字段',
  `bussiness_type` varchar(64) DEFAULT NULL COMMENT '业务类型',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `qua_web_ch_task_result_column_table_id_fk` (`table_id`),
  KEY `idx_5` (`element_id`,`db_name`,`table_name`,`column_name`,`snapshoot_version`),
  CONSTRAINT `qua_web_ch_task_result_column_table_id_fk` FOREIGN KEY (`table_id`) REFERENCES `qua_web_ch_task_result_table` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ch扫描Column记录表';

-- ----------------------------
-- Table structure for qua_web_ch_task_result_db
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_ch_task_result_db`;
CREATE TABLE `qua_web_ch_task_result_db` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建人',
  `db_name` varchar(64) NOT NULL COMMENT '数据库名称',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ch_task_id_fk` (`task_id`),
  KEY `idx_9` (`element_id`,`db_name`,`snapshoot_version`),
  CONSTRAINT `ch_task_id_fk` FOREIGN KEY (`task_id`) REFERENCES `qua_web_task` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ch扫描DB记录表';

-- ----------------------------
-- Table structure for qua_web_ch_task_result_table
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_ch_task_result_table`;
CREATE TABLE `qua_web_ch_task_result_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建人',
  `db_id` bigint(20) NOT NULL COMMENT '扫描的数据库ID',
  `db_name` varchar(30) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(128) NOT NULL COMMENT '数据表名称',
  `uuid` char(36) DEFAULT NULL COMMENT 'CH表系统ID',
  `engine` varchar(200) DEFAULT NULL COMMENT '引擎类型',
  `is_temporary` tinyint(1) DEFAULT NULL COMMENT '是否临时表',
  `data_paths` varchar(200) DEFAULT NULL COMMENT '数据地址',
  `metadata_path` varchar(200) DEFAULT NULL COMMENT '元数据地址',
  `metadata_modification_time` varchar(30) DEFAULT NULL COMMENT '元数据修改时间',
  `dependencies_database` varchar(200) DEFAULT NULL COMMENT '依赖数据库',
  `dependencies_table` varchar(500) DEFAULT NULL COMMENT '依赖数据表',
  `create_table_query` text COMMENT '建表语句',
  `engine_full` varchar(500) DEFAULT NULL COMMENT '引擎参数',
  `partition_key` varchar(200) DEFAULT NULL COMMENT '分区字段',
  `sorting_key` varchar(200) DEFAULT NULL COMMENT '排序字段',
  `primary_key` varchar(200) DEFAULT NULL COMMENT '主键字段',
  `sampling_key` varchar(200) DEFAULT NULL COMMENT '抽样字段',
  `storage_policy` varchar(200) DEFAULT NULL COMMENT '存储策略',
  `total_rows` bigint(20) DEFAULT NULL COMMENT '总行数',
  `total_bytes` bigint(20) DEFAULT NULL COMMENT '总字节数',
  `lifetime_rows` bigint(20) DEFAULT NULL COMMENT '插入总行数',
  `lifetime_bytes` bigint(20) DEFAULT NULL COMMENT '插入总字节数',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `qua_web_ch_task_result_table_db_id_fk` (`db_id`),
  KEY `idx_7` (`element_id`,`db_name`,`table_name`,`snapshoot_version`),
  CONSTRAINT `qua_web_ch_task_result_table_db_id_fk` FOREIGN KEY (`db_id`) REFERENCES `qua_web_ch_task_result_db` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ch扫描Table记录表';

-- ----------------------------
-- Table structure for qua_web_es_element_detail_field
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_es_element_detail_field`;
CREATE TABLE `qua_web_es_element_detail_field` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `index_name` varchar(255) DEFAULT NULL COMMENT '索引名称',
  `field_name` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `field_name_cn` varchar(255) DEFAULT NULL COMMENT '字段中文名，默认为空字符串',
  `is_sensitive` tinyint(1) DEFAULT '0' COMMENT '是否为敏感表，默认不是',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `is_available` tinyint(1) DEFAULT '1' COMMENT '是否可用，默认为1，可用',
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
  `business_type` BIGINT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_es_detail_field_ele_ver` (`element_id`),
  KEY `idx_2` (`index_name`,`field_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ES清单Field表';

-- ----------------------------
-- Table structure for qua_web_es_element_detail_index
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_es_element_detail_index`;
CREATE TABLE `qua_web_es_element_detail_index` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL,
  `host_address` varchar(255) DEFAULT NULL COMMENT '节点IP',
  `port` int(11) DEFAULT NULL COMMENT '端口',
  `index_name` varchar(255) DEFAULT NULL COMMENT '索引名称',
  `index_name_cn` varchar(255) DEFAULT NULL COMMENT '索引中文名',
  `index_dscribe` varchar(255) DEFAULT NULL COMMENT '索引业务描述',
  `index_owner` varchar(255) DEFAULT NULL COMMENT '索引业务负责人',
  `type_name` varchar(255) DEFAULT NULL COMMENT '类型名称，如有多个，逗号分隔',
  `is_sensitive` tinyint(1) DEFAULT '0' COMMENT '是否为敏感，0非，1是，默认不是',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `is_available` tinyint(1) DEFAULT '1' COMMENT '是否可用，0不可用，默认为1，可用',
  `ext_attrs` varchar(1024) DEFAULT NULL COMMENT '自定义属性',
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `detail_index` (`element_id`,`index_name`),
  KEY `idx_es_detail_index_ele` (`element_id`),
  KEY `idx_4` (`index_name`),
  CONSTRAINT `es_element_id_fk` FOREIGN KEY (`element_id`) REFERENCES `qua_wab_element` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ES清单Index表';

-- ----------------------------
-- Table structure for qua_web_es_task_result_field
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_es_task_result_field`;
CREATE TABLE `qua_web_es_task_result_field` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `index_id` bigint(20) NOT NULL COMMENT '索引ID',
  `index_name` varchar(255) DEFAULT NULL COMMENT '索引名称',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建人',
  `field_name` varchar(100) DEFAULT NULL,
  `field_data_type` varchar(255) DEFAULT NULL COMMENT '字段数据类型',
  `analyzer` varchar(500) DEFAULT NULL COMMENT '用于文本分析的分析器',
  `boost` varchar(500) DEFAULT NULL COMMENT '相关性得分计算参数',
  `coerce` varchar(500) DEFAULT NULL COMMENT '试图清除脏值以适应字段的数据类型',
  `copy_to` varchar(500) DEFAULT NULL COMMENT '将多个字段的值复制到组字段中，然后可以将组字段作为单个字段进行查询',
  `doc_values` varchar(500) DEFAULT NULL COMMENT '文档索引时构建的磁盘数据结构',
  `dynamic` varchar(500) DEFAULT NULL COMMENT '对包含新字段的文档进行索引时，动态地添加到文档或文档内部对象中',
  `eager_global_ordinals` varchar(500) DEFAULT NULL COMMENT '字段类型使用序号映射来存储文档值，以获得更紧凑的表示',
  `enabled` varchar(500) DEFAULT NULL COMMENT '可配置跳过对字段内容的解析和索引',
  `fielddata` varchar(500) DEFAULT NULL COMMENT '可配置文本字段在默认情况下用于聚合、排序或脚本编写',
  `fields` varchar(500) DEFAULT NULL COMMENT '以不同的方式为同一个字段建立索引通',
  `format` varchar(500) DEFAULT NULL COMMENT '配置识别日期字符串',
  `ignore_above` varchar(500) DEFAULT NULL COMMENT '超过ignore_above设置的字符串将不会被索引或存储',
  `ignore_malformed` varchar(500) DEFAULT NULL COMMENT '允许忽略异常。不正确的字段不会被索引，但是文档中的其他字段可被正常处理',
  `index_options` varchar(500) DEFAULT NULL COMMENT '控制向反向索引可添加哪些信息以进行搜索和高亮显示',
  `index_phrases` varchar(500) DEFAULT NULL COMMENT '可配置允许精确短语查询更有效地运行，以牺牲更大的索引为代价',
  `index_prefixes` varchar(500) DEFAULT NULL COMMENT '可允许对词汇前缀进行索引，从而加快前缀搜索',
  `index` varchar(500) DEFAULT NULL COMMENT 'index选项控制是否对字段值进行索引，没有索引的字段是不可查询的',
  `meta` varchar(500) DEFAULT NULL COMMENT '附加到字段的元数据，用于共享关于字段的元信息',
  `normalizer` varchar(500) DEFAULT NULL COMMENT '自定义规范化器可产生单个token',
  `norms` varchar(500) DEFAULT NULL COMMENT 'Norms存储了以后在查询时使用的各种规范化因子，以便计算文档相对于查询的得分',
  `null_value` varchar(500) DEFAULT NULL COMMENT '允许使用指定的值替换显式的空值，这样就可以对它进行索引和搜索',
  `position_increment_gap` varchar(500) DEFAULT NULL COMMENT '用以防止多个短语查询跨值匹配',
  `properties` text COMMENT '属性可以是任何数据类型，包括对象和嵌套',
  `search_analyzer` varchar(500) DEFAULT NULL COMMENT '查询可配置为使用在字段映射中定义的分析器',
  `similarity` varchar(500) DEFAULT NULL COMMENT '可配置每个字段的评分算法或相似度算法',
  `store` varchar(500) DEFAULT NULL COMMENT '配置存储此字段',
  `term_vector` varchar(500) DEFAULT NULL COMMENT '配置分析过程产生的terms的信息',
  `field_length` int(11) DEFAULT NULL COMMENT '字段长度，根据字段类型不同而不同，目前支持数值型字段',
  `field_precision` int(11) DEFAULT NULL COMMENT '字段精度，仅针对浮点数值型字段',
  `bussiness_type` varchar(64) DEFAULT NULL COMMENT '业务类型',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `es_index_id_fk` (`index_id`),
  KEY `idx_es_task_field_ele_ver` (`element_id`,`snapshoot_version`),
  KEY `idx_1` (`index_name`,`field_name`,`index_id`),
  CONSTRAINT `es_index_id_fk` FOREIGN KEY (`index_id`) REFERENCES `qua_web_es_task_result_index` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ES扫描field记录表';

-- ----------------------------
-- Table structure for qua_web_es_task_result_index
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_es_task_result_index`;
CREATE TABLE `qua_web_es_task_result_index` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建人',
  `cluster_name` varchar(255) DEFAULT NULL COMMENT '集群名',
  `cluster_uuid` varchar(255) DEFAULT NULL COMMENT '集群UUID',
  `index_name` varchar(255) DEFAULT NULL COMMENT '索引名称',
  `type_name` varchar(100) DEFAULT NULL COMMENT '类型名称',
  `index_state` varchar(30) DEFAULT NULL COMMENT '索引状态',
  `index_settings` varchar(4000) DEFAULT NULL COMMENT '索引配置',
  `aliases` varchar(255) DEFAULT NULL COMMENT '索引别名',
  `primary_terms` varchar(255) DEFAULT NULL COMMENT '主分片更新版本',
  `in_sync_allocations` varchar(1000) DEFAULT NULL COMMENT '分片分配标识',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `es_task_id_fk` (`task_id`),
  KEY `idx_es_task_index_ele_ver_taskid` (`element_id`,`snapshoot_version`,`task_id`),
  KEY `idx_3` (`index_name`),
  CONSTRAINT `es_task_id_fk` FOREIGN KEY (`task_id`) REFERENCES `qua_web_task` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ES扫描index记录表';

-- ----------------------------
-- Table structure for qua_web_job
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_job`;
CREATE TABLE `qua_web_job` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) NOT NULL COMMENT '元数据ID',
  `is_map_to_job` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否与Job对应的，0非，1是，默认1',
  `job_group` varchar(30) DEFAULT NULL COMMENT '作业分组',
  `job_name` varchar(100) NOT NULL COMMENT '作业名称',
  `job_type` tinyint(1) NOT NULL COMMENT '作业类型 1:周期，2:立即执行，3:指定时间',
  `job_cron` varchar(255) DEFAULT NULL COMMENT 'cron表达式',
  `job_cron_expression` varchar(255) NOT NULL COMMENT 'cron表达式 译文',
  `config_date_time` timestamp NULL DEFAULT NULL COMMENT '立即执行和指定时间配置的时间',
  `job_state` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Job状态，1：已开始，2已暂停',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) NOT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) NOT NULL,
  `late_start_time` timestamp NULL DEFAULT NULL COMMENT '最近开始时间  每个task执行时更新',
  `late_ent_time` timestamp NULL DEFAULT NULL COMMENT '最近结束时间  每个task执行时更新',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `flag` tinyint(1) DEFAULT NULL COMMENT '是否删除：0非，1是',
  PRIMARY KEY (`id`),
  KEY `qua_web_job_es_element_id_fk` (`element_id`),
  CONSTRAINT `qua_web_job_es_element_id_fk` FOREIGN KEY (`element_id`) REFERENCES `qua_wab_element` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描Job记录表';

-- ----------------------------
-- Table structure for qua_web_kbs_file_config
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_kbs_file_config`;
CREATE TABLE `qua_web_kbs_file_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) NOT NULL COMMENT '模板名称',
  `kbs_account` varchar(255) NOT NULL COMMENT 'KBS账号',
  `keytab_file_path` varchar(255) DEFAULT NULL,
  `krb5_file_path` varchar(255) DEFAULT NULL,
  `jaas_file_path` varchar(255) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `flag` tinyint(1) DEFAULT '1' COMMENT '是否有效，0无效，1有效，默认1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Kbs认证文件配置';

-- ----------------------------
-- Table structure for qua_web_task
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_task`;
CREATE TABLE `qua_web_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `job_id` bigint(20) NOT NULL COMMENT 'jobId',
  `task_no` varchar(20) NOT NULL COMMENT '任务编号',
  `start_time` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `task_progress` varchar(8) NOT NULL COMMENT '任务进度: 10%',
  `check_num` tinyint(1) DEFAULT '0' COMMENT '获取结果，检查次数，超过三次为异常',
  `status` tinyint(1) DEFAULT NULL COMMENT '任务状态：1执行中，2已完成，有差异，保存扫描结果，3已完成，无差异，不保存扫描结果 4执行异常',
  `result` varchar(1000) DEFAULT NULL COMMENT '执行成功或失败的结果',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tenant_id` bigint(20) NOT NULL,
  `snapshoot_version` varchar(100) DEFAULT NULL COMMENT '扫描版本',
  PRIMARY KEY (`id`),
  KEY `es_job_id_fk` (`job_id`),
  CONSTRAINT `es_job_id_fk` FOREIGN KEY (`job_id`) REFERENCES `qua_web_job` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=' 扫描task记录表';

-- ----------------------------
-- Table structure for qua_web_user_operate_log
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_user_operate_log`;
CREATE TABLE `qua_web_user_operate_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户名',
  `request_ip` varchar(255) NOT NULL COMMENT '登录IP',
  `operate_model` varchar(100) DEFAULT NULL COMMENT '操作模块',
  `operate_type` varchar(255) NOT NULL COMMENT '操作类型',
  `operate_desc` text NOT NULL COMMENT '操作描述',
  `result` varchar(20) DEFAULT NULL COMMENT '成功或失败',
  `create_user` varchar(60) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户操作记录表';


DROP TABLE IF EXISTS `qua_scan_mysql_column`;
CREATE TABLE `qua_scan_mysql_column` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_no` varchar(20) NOT NULL COMMENT '任务编号',
  `table_id` bigint(20) NOT NULL COMMENT '表ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `db_name` varchar(128) NOT NULL COMMENT '数据库名称',
  `table_name` varchar(128) NOT NULL COMMENT '数据表名称',
  `column_name` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `type` varchar(255) DEFAULT NULL COMMENT '字段类型',
  `comment` varchar(1000) DEFAULT NULL COMMENT '字段描述',
  `column_key` varchar(16) DEFAULT NULL COMMENT '索引 PRI-主键 UNI-唯一索引 MUL-非唯一索引',
  `is_nullable` tinyint(1) DEFAULT NULL COMMENT '可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串',
  `column_length` bigint(20) DEFAULT NULL COMMENT '字段长度',
  `column_precision` int(11) DEFAULT NULL COMMENT '字段精度',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  PRIMARY KEY (`id`),
  KEY `qua_scan_mysql_column_table_id_fk` (`table_id`),
  CONSTRAINT `qua_scan_mysql_column_table_id_fk` FOREIGN KEY (`table_id`) REFERENCES `qua_scan_mysql_table` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描mysql字段结果表';

-- ----------------------------
-- Table structure for qua_scan_mysql_db
-- ----------------------------
DROP TABLE IF EXISTS `qua_scan_mysql_db`;
CREATE TABLE `qua_scan_mysql_db` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_no` varchar(20) NOT NULL COMMENT '任务编号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `db_name` varchar(64) NOT NULL COMMENT '数据库名称',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描mysql库结果表';

-- ----------------------------
-- Table structure for qua_scan_mysql_table
-- ----------------------------
DROP TABLE IF EXISTS `qua_scan_mysql_table`;
CREATE TABLE `qua_scan_mysql_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_no` varchar(20) NOT NULL COMMENT '任务编号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `db_id` bigint(20) NOT NULL COMMENT '扫描的数据库ID',
  `db_name` varchar(30) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(128) NOT NULL COMMENT '数据表名称',
  `comment` varchar(1000) DEFAULT NULL COMMENT '表描述',
  `engine` varchar(200) DEFAULT NULL COMMENT '引擎类型',
  `verison` varchar(16) DEFAULT NULL COMMENT '版本',
  `row_format` varchar(32) DEFAULT NULL COMMENT '行格式',
  `table_type` varchar(200) DEFAULT NULL COMMENT '表类型',
  `table_charset` varchar(200) DEFAULT NULL COMMENT '表字符集',
  `total_rows` bigint(20) DEFAULT NULL COMMENT '总行数',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  PRIMARY KEY (`id`),
  KEY `qua_scan_mysql_table_db_id_fk` (`db_id`),
  CONSTRAINT `qua_scan_mysql_table_db_id_fk` FOREIGN KEY (`db_id`) REFERENCES `qua_scan_mysql_db` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描mysql表结果表';


-- ----------------------------
-- Table structure for qua_web_mysql_element_detail_column
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_mysql_element_detail_column`;
CREATE TABLE `qua_web_mysql_element_detail_column` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `db_name` varchar(64) DEFAULT NULL COMMENT '库名称',
  `table_name` varchar(255) DEFAULT NULL COMMENT '表名称',
  `column_name` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `column_name_cn` varchar(255) DEFAULT NULL COMMENT '字段中文名，默认为空字符串',
  `is_sensitive` tinyint(1) DEFAULT '0' COMMENT '是否为敏感表，默认不是',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `is_available` tinyint(1) DEFAULT '1' COMMENT '是否可用，默认为1，可用',
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
  business_type bigint null  comment '业务系统',
  PRIMARY KEY (`id`),
  KEY `idx_6` (`element_id`,`db_name`,`table_name`,`column_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='mysql清单Column表';

-- ----------------------------
-- Table structure for qua_web_mysql_element_detail_db
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_mysql_element_detail_db`;
CREATE TABLE `qua_web_mysql_element_detail_db` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL,
  `db_name` varchar(64) DEFAULT NULL COMMENT '库名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `mysql_element_id_fk` (`element_id`),
  CONSTRAINT `mysql_element_id_fk` FOREIGN KEY (`element_id`) REFERENCES `qua_wab_element` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='mysql清单DB表';

-- ----------------------------
-- Table structure for qua_web_mysql_element_detail_table
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_mysql_element_detail_table`;
CREATE TABLE `qua_web_mysql_element_detail_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `db_name` varchar(64) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(255) DEFAULT NULL COMMENT '数据表名称',
  `table_name_cn` varchar(255) DEFAULT NULL COMMENT '表中文名',
  `table_dscribe` varchar(255) DEFAULT NULL COMMENT '表业务描述',
  `table_owner` varchar(255) DEFAULT NULL COMMENT '表业务负责人',
  `is_sensitive` tinyint(1) DEFAULT '0' COMMENT '是否为敏感表，默认不是',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `is_available` tinyint(1) DEFAULT '1' COMMENT '是否可用，默认为1，可用',
  `ext_attrs` varchar(1024) DEFAULT NULL COMMENT '自定义属性',
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_8` (`element_id`,`db_name`,`table_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='mysql清单Table表';

-- ----------------------------
-- Table structure for qua_web_mysql_task_result_column
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_mysql_task_result_column`;
CREATE TABLE `qua_web_mysql_task_result_column` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `table_id` bigint(20) NOT NULL COMMENT '表ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建人',
  `db_name` varchar(128) NOT NULL COMMENT '数据库名称',
  `table_name` varchar(128) NOT NULL COMMENT '数据表名称',
  `column_name` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `type` varchar(255) DEFAULT NULL COMMENT '字段类型',
  `comment` varchar(1000) DEFAULT NULL COMMENT '字段描述',
  `column_key` varchar(16) DEFAULT NULL COMMENT '索引 PRI-主键 UNI-唯一索引 MUL-非唯一索引',
  `is_nullable` tinyint(1) DEFAULT NULL COMMENT '可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串',
  `column_length` int(11) DEFAULT NULL COMMENT '字段长度',
  `column_precision` int(11) DEFAULT NULL COMMENT '字段精度',
  `bussiness_type` varchar(64) DEFAULT NULL COMMENT '业务类型',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `qua_web_mysql_task_result_column_table_id_fk` (`table_id`),
  KEY `idx_5` (`element_id`,`db_name`,`table_name`,`column_name`,`snapshoot_version`),
  CONSTRAINT `qua_web_mysql_task_result_column_table_id_fk` FOREIGN KEY (`table_id`) REFERENCES `qua_web_mysql_task_result_table` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='mysql扫描Column记录表';

-- ----------------------------
-- Table structure for qua_web_mysql_task_result_db
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_mysql_task_result_db`;
CREATE TABLE `qua_web_mysql_task_result_db` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建人',
  `db_name` varchar(64) NOT NULL COMMENT '数据库名称',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `mysql_task_id_fk` (`task_id`),
  KEY `idx_9` (`element_id`,`db_name`,`snapshoot_version`),
  CONSTRAINT `mysql_task_id_fk` FOREIGN KEY (`task_id`) REFERENCES `qua_web_task` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='mysql扫描DB记录表';

-- ----------------------------
-- Table structure for qua_web_mysql_task_result_table
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_mysql_task_result_table`;
CREATE TABLE `qua_web_mysql_task_result_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建人',
  `db_id` bigint(20) NOT NULL COMMENT '扫描的数据库ID',
  `db_name` varchar(30) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(128) NOT NULL COMMENT '数据表名称',
  `comment` varchar(1000) DEFAULT NULL COMMENT '表描述',
  `engine` varchar(200) DEFAULT NULL COMMENT '引擎类型',
  `verison` tinyint(1) DEFAULT NULL COMMENT '版本',
  `row_format` tinyint(1) DEFAULT NULL COMMENT '行格式',
  `table_type` varchar(200) DEFAULT NULL COMMENT '表类型',
  `table_charset` varchar(200) DEFAULT NULL COMMENT '表字符集',
  `total_rows` bigint(20) DEFAULT NULL COMMENT '总行数',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `qua_web_mysql_task_result_table_db_id_fk` (`db_id`),
  KEY `idx_7` (`element_id`,`db_name`,`table_name`,`snapshoot_version`),
  CONSTRAINT `qua_web_mysql_task_result_table_db_id_fk` FOREIGN KEY (`db_id`) REFERENCES `qua_web_mysql_task_result_db` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='mysql扫描Table记录表';

-- ----------------------------
-- Table structure for qua_scan_hive_column
-- ----------------------------
DROP TABLE IF EXISTS `qua_scan_hive_column`;
CREATE TABLE `qua_scan_hive_column` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_no` varchar(20) NOT NULL COMMENT '任务编号',
  `table_id` bigint(20) NOT NULL COMMENT '表ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `db_name` varchar(128) NOT NULL COMMENT '数据库名称',
  `table_name` varchar(128) NOT NULL COMMENT '数据表名称',
  `column_name` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `type` varchar(255) DEFAULT NULL COMMENT '字段类型',
  `comment` varchar(1000) DEFAULT NULL COMMENT '字段描述',
  `is_nullable` tinyint(1) DEFAULT NULL COMMENT '可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串',
  `column_length` int(11) DEFAULT NULL COMMENT '字段长度',
  `column_precision` int(11) DEFAULT NULL COMMENT '字段精度',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  PRIMARY KEY (`id`),
  KEY `qua_scan_hive_column_table_id_fk` (`table_id`),
  CONSTRAINT `qua_scan_hive_column_table_id_fk` FOREIGN KEY (`table_id`) REFERENCES `qua_scan_hive_table` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描hive字段结果表';

-- ----------------------------
-- Table structure for qua_scan_hive_db
-- ----------------------------
DROP TABLE IF EXISTS `qua_scan_hive_db`;
CREATE TABLE `qua_scan_hive_db` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_no` varchar(20) NOT NULL COMMENT '任务编号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `db_name` varchar(64) NOT NULL COMMENT '数据库名称',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描hive库结果表';

-- ----------------------------
-- Table structure for qua_scan_hive_table
-- ----------------------------
DROP TABLE IF EXISTS `qua_scan_hive_table`;
CREATE TABLE `qua_scan_hive_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_no` varchar(20) NOT NULL COMMENT '任务编号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `db_id` bigint(20) NOT NULL COMMENT '扫描的数据库ID',
  `db_name` varchar(30) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(128) NOT NULL COMMENT '数据表名称',
  `comment` varchar(1000) DEFAULT NULL COMMENT '表描述',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  PRIMARY KEY (`id`),
  KEY `qua_scan_hive_table_db_id_fk` (`db_id`),
  CONSTRAINT `qua_scan_hive_table_db_id_fk` FOREIGN KEY (`db_id`) REFERENCES `qua_scan_hive_db` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='扫描hive表结果表';


-- ----------------------------
-- Table structure for qua_web_hive_element_detail_column
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_hive_element_detail_column`;
CREATE TABLE `qua_web_hive_element_detail_column` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `db_name` varchar(64) DEFAULT NULL COMMENT '库名称',
  `table_name` varchar(255) DEFAULT NULL COMMENT '表名称',
  `column_name` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `column_name_cn` varchar(255) DEFAULT NULL COMMENT '字段中文名，默认为空字符串',
  `is_sensitive` tinyint(1) DEFAULT '0' COMMENT '是否为敏感表，默认不是',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `is_available` tinyint(1) DEFAULT '1' COMMENT '是否可用，默认为1，可用',
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
   business_type bigint null  comment '业务系统',
  PRIMARY KEY (`id`),
  KEY `idx_6` (`element_id`,`db_name`,`table_name`,`column_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='hive清单Column表';

-- ----------------------------
-- Table structure for qua_web_hive_element_detail_db
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_hive_element_detail_db`;
CREATE TABLE `qua_web_hive_element_detail_db` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL,
  `db_name` varchar(64) DEFAULT NULL COMMENT '库名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `hive_element_id_fk` (`element_id`),
  CONSTRAINT `hive_element_id_fk` FOREIGN KEY (`element_id`) REFERENCES `qua_wab_element` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='hive清单DB表';

-- ----------------------------
-- Table structure for qua_web_hive_element_detail_table
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_hive_element_detail_table`;
CREATE TABLE `qua_web_hive_element_detail_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `db_name` varchar(64) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(255) DEFAULT NULL COMMENT '数据表名称',
  `table_name_cn` varchar(255) DEFAULT NULL COMMENT '表中文名',
  `table_dscribe` varchar(255) DEFAULT NULL COMMENT '表业务描述',
  `table_owner` varchar(255) DEFAULT NULL COMMENT '表业务负责人',
  `is_sensitive` tinyint(1) DEFAULT '0' COMMENT '是否为敏感表，默认不是',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `is_available` tinyint(1) DEFAULT '1' COMMENT '是否可用，默认为1，可用',
  `ext_attrs` varchar(1024) DEFAULT NULL COMMENT '自定义属性',
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_8` (`element_id`,`db_name`,`table_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='hive清单Table表';

-- ----------------------------
-- Table structure for qua_web_hive_task_result_column
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_hive_task_result_column`;
CREATE TABLE `qua_web_hive_task_result_column` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `table_id` bigint(20) NOT NULL COMMENT '表ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建人',
  `db_name` varchar(128) NOT NULL COMMENT '数据库名称',
  `table_name` varchar(128) NOT NULL COMMENT '数据表名称',
  `column_name` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `type` varchar(255) DEFAULT NULL COMMENT '字段类型',
  `comment` varchar(1000) DEFAULT NULL COMMENT '字段描述',
  `is_nullable` tinyint(1) DEFAULT NULL COMMENT '可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串',
  `column_length` int(11) DEFAULT NULL COMMENT '字段长度',
  `column_precision` int(11) DEFAULT NULL COMMENT '字段精度',
  `bussiness_type` varchar(64) DEFAULT NULL COMMENT '业务类型',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `qua_web_hive_task_result_column_table_id_fk` (`table_id`),
  KEY `idx_5` (`element_id`,`db_name`,`table_name`,`column_name`,`snapshoot_version`),
  CONSTRAINT `qua_web_hive_task_result_column_table_id_fk` FOREIGN KEY (`table_id`) REFERENCES `qua_web_hive_task_result_table` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='hive扫描Column记录表';

-- ----------------------------
-- Table structure for qua_web_hive_task_result_db
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_hive_task_result_db`;
CREATE TABLE `qua_web_hive_task_result_db` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建人',
  `db_name` varchar(64) NOT NULL COMMENT '数据库名称',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `hive_task_id_fk` (`task_id`),
  KEY `idx_9` (`element_id`,`db_name`,`snapshoot_version`),
  CONSTRAINT `hive_task_id_fk` FOREIGN KEY (`task_id`) REFERENCES `qua_web_task` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='hive扫描DB记录表';

-- ----------------------------
-- Table structure for qua_web_hive_task_result_table
-- ----------------------------
DROP TABLE IF EXISTS `qua_web_hive_task_result_table`;
CREATE TABLE `qua_web_hive_task_result_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL COMMENT '元数据ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建人',
  `db_id` bigint(20) NOT NULL COMMENT '扫描的数据库ID',
  `db_name` varchar(30) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(128) NOT NULL COMMENT '数据表名称',
  `comment` varchar(1000) DEFAULT NULL COMMENT '表描述',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  `tenant_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `qua_web_hive_task_result_table_db_id_fk` (`db_id`),
  KEY `idx_7` (`element_id`,`db_name`,`table_name`,`snapshoot_version`),
  CONSTRAINT `qua_web_hive_task_result_table_db_id_fk` FOREIGN KEY (`db_id`) REFERENCES `qua_web_hive_task_result_db` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='hive扫描Table记录表';


DROP TABLE IF EXISTS `qua_monitor_model`;
CREATE TABLE `qua_monitor_model` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `model_name` varchar(128) NOT NULL COMMENT '模型名称',
  `model_desc` varchar(1024) DEFAULT NULL COMMENT '模型描述',
  `element_id` bigint(20) NOT NULL COMMENT '资源ID',
  `element_type` varchar(16) NOT NULL COMMENT '资源类型 CH、ES、MYSQL、HIVE',
  `snapshoot_version` varchar(100) NOT NULL COMMENT '快照版本',
  `database_id` bigint(20) DEFAULT NULL COMMENT '数据库ID',
  `database_name` varchar(128) DEFAULT NULL COMMENT '数据库名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建用户',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(60) DEFAULT NULL COMMENT '更新时间',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识:1-有效；0-无效',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质量监测模型';

DROP TABLE IF EXISTS `qua_monitor_model_resource`;
CREATE TABLE `qua_monitor_model_resource` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `model_id` bigint(20) NOT NULL COMMENT '模型ID',
  `table_id` bigint(20) DEFAULT NULL COMMENT '表ID',
  `table_name` varchar(128) DEFAULT NULL COMMENT '表名/索引名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建用户',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(60) DEFAULT NULL COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质量监测模型包含的资源';

DROP TABLE IF EXISTS `qua_monitor_rule`;
CREATE TABLE `qua_monitor_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `model_id` bigint(20) NOT NULL COMMENT '模型ID',
  `rule_name` varchar(128) NOT NULL COMMENT '规则名称',
  `rule_code` varchar(128) DEFAULT NULL COMMENT '规则代码',
  `rule_type` varchar(64) NOT NULL COMMENT '规则类型',
  `table_name` varchar(128) DEFAULT NULL COMMENT '表名/索引名',
  `column_name` varchar(128) DEFAULT NULL COMMENT '字段名 多个字段使用逗号分隔',
  `rule_detail` varchar(1024) DEFAULT NULL COMMENT '规则明细 {config_key:config_value}',
  `rule_level` varchar(128) DEFAULT NULL COMMENT '问题级别',
  `rule_desc` varchar(512) DEFAULT NULL COMMENT '规则名称',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建用户',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(60) DEFAULT NULL COMMENT '更新时间',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识:1-有效；0-无效',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质量监测规则';

DROP TABLE IF EXISTS `qua_monitor_rule_type`;
CREATE TABLE `qua_monitor_rule_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type_code` varchar(128) NOT NULL COMMENT '类别编码',
  `type_name` varchar(128) NOT NULL COMMENT '类别名称',
  `config_key` varchar(128) DEFAULT NULL COMMENT '配置项Key',
  `config_name` varchar(64) NOT NULL COMMENT '配置项名称',
  `config_type` varchar(128) DEFAULT NULL COMMENT '配置项类型input、select、date、checkbox',
  `is_required` varchar(2) DEFAULT NULL COMMENT '是否必填 0-否 1-是',
  `reg_pattern` varchar(512) DEFAULT NULL COMMENT '正则表达式 如果该值不为空则配置的值需要满足该正则',
  `interface_name` varchar(128) DEFAULT NULL COMMENT '接口名 如果类型为下拉框，则需要根据该配置调用接口获取下拉框的值',
  `multi_select` varchar(2) DEFAULT 0 COMMENT '是否多选 0-否 1-是 (判断下来框是否多选)',
  `sort_no` int(11) DEFAULT NULL COMMENT '排序',
  type_desc      varchar(1024)          null comment '规则描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质量监测规则类别';


DROP TABLE IF EXISTS `qua_monitor_job`;
CREATE TABLE `qua_monitor_job` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `model_id` bigint(20) NOT NULL COMMENT '模型ID',
  `name` varchar(128) NOT NULL COMMENT '任务名称',
  `execute_auto` varchar(5) DEFAULT NULL COMMENT '01-人工执行；02-自动执行；03:定时执行;04:循环执行',
  `execute_cycle` varchar(11) DEFAULT NULL COMMENT '01:一次性任务;02:周期任务',
  `execute_cycle_desc` varchar(512) DEFAULT NULL COMMENT '执行周期描述',
  `execute_type` varchar(11) DEFAULT NULL COMMENT '执行方式:day,week,month,year,cron',
  `execute_config` varchar(1000) DEFAULT NULL COMMENT '执行配置，json格式',
  `execute_cron` varchar(50) DEFAULT NULL COMMENT 'quartz表达式',
  `define_time` datetime DEFAULT NULL COMMENT '定时执行指定时间',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `valid_time` datetime DEFAULT NULL COMMENT '起始时间',
  `invalid_time` datetime DEFAULT NULL COMMENT '截止时间',
  `execute_current_time` datetime DEFAULT NULL COMMENT '本次执行时间',
  `execute_next_time` datetime DEFAULT NULL COMMENT '下次执行时间',
  `job_desc` varchar(1024) DEFAULT NULL COMMENT '任务描述',
  `execute_status` int(11) DEFAULT NULL COMMENT '执行状态',
  `execute_result` varchar(1024) DEFAULT NULL COMMENT '执行结果说明',
  `audit_remark` varchar(1024) DEFAULT NULL COMMENT '核查结果说明',
  `job_rules` varchar(1024) DEFAULT NULL COMMENT '规则 多个规则使用逗号分隔',
  `rule_weight` varchar(1024) DEFAULT NULL COMMENT '规则权重 权重和规则一一对应',
  `sample_cnt` int DEFAULT NULL COMMENT '样例数据条数',
  `tenant_id` bigint(30) DEFAULT NULL COMMENT '租户ID',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `status` int(11) DEFAULT NULL COMMENT '状态 2:执行中 3:暂停',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识:1-有效；0-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质量监测JOB';

DROP TABLE IF EXISTS `qua_monitor_task`;
CREATE TABLE `qua_monitor_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `model_id` bigint(20) NOT NULL COMMENT '模型ID',
  `task_no` varchar(50) DEFAULT NULL COMMENT '任务编号',
  `job_id` bigint(20) NOT NULL COMMENT '任务ID',
  `job_rules` varchar(1024) DEFAULT NULL COMMENT '规则 多个规则使用逗号分隔',
  `rule_weight` varchar(1024) DEFAULT NULL COMMENT '规则权重 权重和规则一一对应',
  `sample_cnt` int DEFAULT NULL COMMENT '样例数据条数',
  `start_time` datetime NOT NULL COMMENT '任务待执行日期',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `end_time` datetime DEFAULT NULL COMMENT '执行完成时间',
  `task_result` int(11) DEFAULT NULL COMMENT '执行结果。0：失败；1：成功',
  `fail_reason` varchar(100) DEFAULT NULL COMMENT '失败原因',
  `run_process` double DEFAULT NULL COMMENT '执行进度百分比0-100',
  `status` int(11) DEFAULT NULL COMMENT '任务状态 1:待执行 2:执行中 3:暂停 4:扫描完成 5:停止 6:任务结束',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质量监测任务表';

DROP TABLE IF EXISTS `qua_monitor_result`;
CREATE TABLE `qua_monitor_result` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `job_id` bigint(20) DEFAULT NULL COMMENT '任务ID',
  `task_id` bigint(20) DEFAULT NULL COMMENT '任务ID',
  `element_id` varchar(128) DEFAULT NULL COMMENT '资产ID',
  `monitor_score` varchar(128) DEFAULT NULL COMMENT '监测分数',
  `level_cnt` varchar(128) DEFAULT NULL COMMENT '级别数量统计{level:cnt}',
  `success_match_rules` varchar(512) DEFAULT NULL COMMENT '执行成功匹配规则 多个规则使用逗号分隔',
  `success_not_match_rules` varchar(512) DEFAULT NULL COMMENT '执行成功不匹配规则 多个规则使用逗号分隔',
  `fail_rules` varchar(512) DEFAULT NULL COMMENT '执行失败规则 多个规则使用逗号分隔',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `tenant_id` bigint(30) DEFAULT NULL COMMENT '租户id',
  `is_new` varchar(8) DEFAULT NULL COMMENT '是否新增',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质量监测结果表';

DROP TABLE IF EXISTS `qua_monitor_result_detail`;
CREATE TABLE `qua_monitor_result_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `job_id` bigint(20) DEFAULT NULL COMMENT '任务ID',
  `task_id` bigint(20) DEFAULT NULL COMMENT '任务ID',
  `table_name` varchar(128) DEFAULT NULL COMMENT '表名',
  `column_name` varchar(128) DEFAULT NULL COMMENT '字段名',
  `rule_type` varchar(128) DEFAULT NULL COMMENT '触发规则',
  `rule_weight` varchar(256) DEFAULT NULL COMMENT '规则权重',
  `rule_level` varchar(128) DEFAULT NULL COMMENT '问题级别',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `tenant_id` bigint(30) DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质量监测结果明细';


DROP TABLE IF EXISTS `qua_config_business_category`;
CREATE TABLE `qua_config_business_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(128) NOT NULL COMMENT '分组名',
  `category_desc` varchar(1024) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建用户',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(60) DEFAULT NULL COMMENT '更新时间',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识:1-有效；0-无效',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务类型分组';


DROP TABLE IF EXISTS `qua_config_business`;
CREATE TABLE `qua_config_business` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `category_id` bigint(20) NOT NULL COMMENT '所属分组',
  `business_name` varchar(128) NOT NULL COMMENT '类型名称',
  `business_desc` varchar(1024) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建用户',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(60) DEFAULT NULL COMMENT '更新时间',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识:1-有效；0-无效',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务类型';

DROP TABLE IF EXISTS `qua_config_master_data_type`;
CREATE TABLE `qua_config_master_data_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type_name` varchar(128) NOT NULL COMMENT '主数据类型名称',
  `type_desc` varchar(128) NOT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建用户',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(60) DEFAULT NULL COMMENT '更新时间',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识:1-有效；0-无效',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主数据类型';

DROP TABLE IF EXISTS `qua_master_data_model`;
CREATE TABLE `qua_master_data_model` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `model_name` varchar(128) NOT NULL COMMENT '主数据模型名称',
  `model_code` varchar(128) NOT NULL COMMENT '编码规则',
  `model_type_id` bigint(20) NOT NULL COMMENT '主数据类型id',
  `model_status` varchar(2) DEFAULT '1' COMMENT '模型状态:1-正常；0-冻结',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建用户',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(60) DEFAULT NULL COMMENT '更新时间',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识:1-有效；0-无效',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主数据模型';


DROP TABLE IF EXISTS `qua_master_data`;
CREATE TABLE `qua_master_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `model_id` bigint(20) NOT NULL COMMENT '主数据模型ID',
  `field_name` varchar(128) NOT NULL COMMENT '字段名称',
  `field_code` varchar(128) NOT NULL COMMENT '字段编码',
  `field_desc` varchar(128) DEFAULT NULL COMMENT '字段描述',
  `data_type` varchar(128) DEFAULT NULL COMMENT '主数据类型',
  `field_length` int(11) DEFAULT NULL COMMENT '字段长度',
  `field_precision` int(11) DEFAULT NULL COMMENT '字段精度',
  `is_null` varchar(2) DEFAULT NULL COMMENT '是否可以为空 1-是 0-否',
  `is_unique` varchar(2) DEFAULT NULL COMMENT '是否可以唯一 1-是 0-否',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(60) DEFAULT NULL COMMENT '创建用户',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(60) DEFAULT NULL COMMENT '更新时间',
  `flag` varchar(1) DEFAULT '1' COMMENT '有效标识:1-有效；0-无效',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主数据';


CREATE TABLE `qua_data_standard_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cn_name` varchar(100) NOT NULL COMMENT '标准中文名',
  `en_name` varchar(100) NOT NULL COMMENT '标准英文名',
  `code` varchar(100) NOT NULL COMMENT '标准编号',
  `business_meaning` varchar(100) DEFAULT '' COMMENT '业务含义',
  `accordance` varchar(100) DEFAULT '' COMMENT '制定依据',
  `standard_type` int(11) NOT NULL COMMENT '类型，枚举值：1数据长度、2数据类型、3数据精度、4中英文、5自定义',
  `data_length` int(11) DEFAULT NULL COMMENT '数据长度',
  `data_column_type` varchar(100) DEFAULT NULL COMMENT '数据类型',
  `data_precision` int(11) DEFAULT NULL COMMENT '小数长度，1、2、3、4、5',
  `cn_en_verification` int(11) DEFAULT NULL COMMENT '中英文校验,1：仅包括中文 2：仅包含英文 3：包含中英文',
  `custom_content` varchar(1000) DEFAULT NULL COMMENT '自定义编辑',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT NULL,
  `create_user` varchar(60) NOT NULL,
  `update_time` datetime DEFAULT NULL,
  `update_user` varchar(60) NOT NULL,
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `set_id` bigint(20) DEFAULT NULL COMMENT '标准集id',
  `status` int(11) DEFAULT '1' COMMENT '启停，1启用，0停用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据标准配置';


CREATE TABLE `qua_data_standard_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `parent_id` bigint(20) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '入库时间',
  `create_user` varchar(60) NOT NULL,
  `update_time` datetime DEFAULT NULL,
  `update_user` varchar(60) NOT NULL,
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据标准组';


CREATE TABLE `qua_data_standard_set` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `create_time` datetime DEFAULT NULL COMMENT '入库时间',
  `create_user` varchar(60) NOT NULL,
  `update_time` datetime DEFAULT NULL,
  `update_user` varchar(60) NOT NULL,
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `group_id` bigint(20) DEFAULT NULL COMMENT '分组id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据标准集';

CREATE TABLE `ums_sys_user_4a`
(
    `user_id`                   bigint(32)   NOT NULL AUTO_INCREMENT COMMENT '用户编号',
    `dept_id`                   varchar(100) DEFAULT NULL COMMENT '部门编号',
    `user_name`                 varchar(300) DEFAULT NULL,
    `password`                  varchar(100) NOT NULL COMMENT '密码',
    `password_old`              varchar(32)  DEFAULT NULL COMMENT '旧密码',
    `period_from`               bigint(20)   NOT NULL COMMENT '账号使用开始日',
    `period_to`                 bigint(20)   NOT NULL COMMENT '账号使用结束日',
    `status`                    tinyint(1)   NOT NULL COMMENT '状态\r\n0:正常\r\n1:锁定',
    `real_name`                 varchar(300) DEFAULT NULL,
    `code`                      varchar(100) DEFAULT NULL COMMENT '员工工号',
    `sex`                       varchar(1)   DEFAULT NULL COMMENT '员工性别\r\n0:男\r\n1:女',
    `telephone`                 varchar(20)  DEFAULT NULL,
    `email`                     varchar(100) NOT NULL COMMENT '电子邮箱',
    `leader`                    varchar(100) DEFAULT NULL COMMENT '直属领导',
    `login_voucher`             varchar(100) DEFAULT NULL COMMENT '单点登录凭证',
    `fail_count`                int(11)      DEFAULT NULL COMMENT '失败次数',
    `lock_date`                 datetime     DEFAULT NULL COMMENT '锁定日期',
    `final_login_ip`            varchar(64)  DEFAULT NULL COMMENT '最后登录ip',
    `final_login_date`          bigint(20)   DEFAULT NULL COMMENT '最后登录时间',
    `builtin`                   tinyint(1)   NOT NULL COMMENT '是否预置\r\n0：预置\r\n1:未预置',
    `security_code`             varchar(128) DEFAULT NULL,
    `remark`                    varchar(300) DEFAULT NULL COMMENT '备注',
    `del_flag`                  tinyint(1)   NOT NULL COMMENT '删除标识\r\n0:未删除\r\n1:已删除',
    `tenant_id`                 int(11)      DEFAULT NULL COMMENT '租户ID',
    `queue`                     varchar(64)  DEFAULT NULL COMMENT '队列名称',
    `create_user`               varchar(100) DEFAULT NULL COMMENT '创建者',
    `create_date`               bigint(20)   DEFAULT NULL COMMENT '创建日期',
    `update_user`               varchar(100) DEFAULT NULL COMMENT '更新者',
    `update_date`               bigint(20)   DEFAULT NULL COMMENT '更新日期',
    `social_account`            varchar(100) DEFAULT NULL COMMENT '社交账号',
    `first_login_fail_time`     datetime     DEFAULT NULL COMMENT '连续登陆失败时第一次登录时间',
    `is_first_login`            varchar(1)   DEFAULT NULL COMMENT '是否第一次登录',
    `is_need_update_password`   varchar(1)   DEFAULT NULL COMMENT '第一次登录时是否需要修改密码',
    `default_router_id`         varchar(100) DEFAULT NULL COMMENT '默认应用id',
    `default_router_name`       varchar(100) DEFAULT NULL COMMENT '默认应用路由',
    `default_dashboard`         int(20)      DEFAULT NULL COMMENT '默认仪表盘id',
    `mobile`                    varchar(11)  DEFAULT NULL COMMENT '手机号码',
    `dept_name`                 varchar(100) DEFAULT NULL COMMENT '部门名称',
    `last_update_password_time` datetime     DEFAULT NULL,
    `tenant_name`               varchar(100) DEFAULT NULL,
    `role_id`                   varchar(100) DEFAULT NULL,
    `role_name`                 varchar(100) DEFAULT NULL,
    `allocate_status`           int(11)      DEFAULT '0' COMMENT '是否分配租户，0未分配，1已分配',
    PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='同步过来的4A用户存储表';

CREATE TABLE `qua_master_data_model_relation`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `model_id`         bigint(20) NOT NULL COMMENT '主数据模型id',
    `related_model_id` bigint(20) NOT NULL COMMENT '被关联主数据模型id',
    `related_type`     int(11) NOT NULL COMMENT '关联关系，1调用、2属于、3包含',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='主数据模型关系';
