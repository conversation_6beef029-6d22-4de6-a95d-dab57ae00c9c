ALTER TABLE ums_sys_auth_config ADD COLUMN fa_vendor varchar(64) DEFAULT 'asiaInfoSec' COMMENT '4A厂商 默认亚信安全';

INSERT INTO `qua_monitor_rule_type` (`type_code`, `type_name`, `config_key`, `config_name`, `config_type`, `is_required`, `reg_pattern`, `interface_name`, `multi_select`, `sort_no`, `type_desc`) VALUES ('SQL', '自定义SQL', '', '', '', '', '', '', '0', '1', '自定义SQL');

alter table `qua_master_data` add column `data_status` varchar(1) default 1 comment ' 状态 0-冻结 1-正常';
alter table `qua_master_data` add column `open_status` varchar(1) default 1 comment ' 状态 0-失效 1-正常';

alter table `qua_master_data_model` add column `open_status` varchar(1) default 1 comment ' 状态 0-失效 1-正常';

alter table `qua_data_standard_config` add column `encrypt` varchar(1) default 0 comment '是否启用加密，1启用，0停用';
alter table `qua_data_standard_config` add column `dept`  varchar(255) default null comment '所属部门';
alter table `qua_data_standard_config` add column `business_system`  varchar(255) default null comment '所属系统';
alter table `qua_data_standard_config` add column `store_cycle`  varchar(255) default null comment '存储周期';
alter table `qua_data_standard_config` add column `database_name`  varchar(255) default null comment '所属数据库';
alter table `qua_data_standard_config` add column `sensitive_type`  varchar(255) default null comment '敏感类型';
alter table `qua_data_standard_config` add column `specification`  varchar(255) default null comment '权限说明';


alter table `tb_cluster` add column `CLUSTER_STATUS` varchar(1) default 1 comment '集群状态 默认为白名单，1为白名单，0为黑名单';

create table `qua_monitor_result_appeal`
(
    `id` bigint auto_increment primary key,
    `result_detail_id` bigint               null,
    `appeal_user`      varchar(50)          null comment '申诉人',
    `appeal_reason`    varchar(200)         null comment '申诉原因',
    `appeal_time`      timestamp            null comment '申诉时间',
    `handle_user`      varchar(50)          null comment '处理人',
    `handle_time`      timestamp            null comment '处理时间',
    `handle_status`    tinyint(1) default 0 null comment '处理状态：0未处理，1已处理',
    `tenant_id`        bigint               null
) comment '质量检测结果申诉';


INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID) VALUES('AI数据分析', 'ai-data-analysis', 2);

ALTER TABLE t_ds_project ADD is_fuse_modeling int DEFAULT 0 NULL COMMENT '是否融合建模，0否1是';

ALTER TABLE tb_tenant ADD COLUMN FA_EXTEND_INFO varchar(2048) COMMENT '4A单点扩展信息';

ALTER TABLE ums_sys_user_4a ADD is_super_admin int DEFAULT 0 NULL COMMENT '是否超级管理员，1是0否';

ALTER TABLE ums_sys_license ADD COLUMN sn_data varchar(1024) COMMENT '机器码';
ALTER TABLE ums_sys_license ADD COLUMN sn_ip varchar(512) COMMENT '机器码所属IP';

ALTER TABLE qua_data_standard_config ADD version_no varchar(100) NULL COMMENT '版本号';
ALTER TABLE qua_data_standard_config ADD publish_status smallint DEFAULT 0 NULL COMMENT '发布状态，0未发布，1已发布';
ALTER TABLE qua_data_standard_config ADD publish_name varchar(100) NULL COMMENT '发布名称';
ALTER TABLE qua_data_standard_config ADD publish_desc varchar(255) NULL COMMENT '发布描述';
ALTER TABLE qua_data_standard_config ADD publish_time datetime NULL COMMENT '发布时间';
ALTER TABLE qua_data_standard_config ADD publish_user varchar(100) NULL COMMENT '发布人';
ALTER TABLE qua_data_standard_config ADD approval_status smallint DEFAULT NULL COMMENT '审批状态，0待审批 1同意 2拒绝';
ALTER TABLE qua_data_standard_config ADD approval_desc varchar(255) NULL COMMENT '审批说明';
ALTER TABLE qua_data_standard_config ADD approval_time datetime NULL COMMENT '审批时间';
ALTER TABLE qua_data_standard_config ADD approval_user varchar(100) NULL COMMENT '审批人';

CREATE TABLE `qua_data_standard_config_restore_log`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `config_id`          bigint(20) DEFAULT NULL COMMENT '标准主键id',
    `restore_version_no` varchar(100) DEFAULT NULL COMMENT '恢复后版本号',
    `restore_desc`       varchar(255) DEFAULT NULL COMMENT '恢复描述',
    `restore_user`       varchar(100) DEFAULT NULL COMMENT '操作人',
    `restore_time`       datetime     DEFAULT NULL COMMENT '恢复时间',
    `restore_result`     smallint(6) DEFAULT NULL COMMENT '恢复结果，0成功，1失败',
    `from_version_no`    varchar(100) DEFAULT NULL COMMENT '选择恢复的版本号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COMMENT='数据标准版本恢复记录';

CREATE TABLE `qua_data_standard_config_version_snapshot`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT,
    `cn_name`            varchar(100) NOT NULL COMMENT '标准中文名',
    `en_name`            varchar(100) NOT NULL COMMENT '标准英文名',
    `code`               varchar(100) NOT NULL COMMENT '标准编号',
    `business_meaning`   varchar(100)  DEFAULT '' COMMENT '业务含义',
    `accordance`         varchar(100)  DEFAULT '' COMMENT '制定依据',
    `standard_type`      int(11) NOT NULL COMMENT '类型，枚举值：1数据长度、2数据类型、3数据精度、4中英文、5自定义',
    `data_length`        int(11) DEFAULT NULL COMMENT '数据长度',
    `data_column_type`   varchar(100)  DEFAULT NULL COMMENT '数据类型',
    `data_precision`     int(11) DEFAULT NULL COMMENT '小数长度，1、2、3、4、5',
    `cn_en_verification` int(11) DEFAULT NULL COMMENT '中英文校验,1：仅包括中文 2：仅包含英文 3：包含中英文',
    `custom_content`     varchar(1000) DEFAULT NULL COMMENT '自定义编辑',
    `remark`             varchar(1000) DEFAULT NULL COMMENT '备注',
    `create_time`        datetime      DEFAULT NULL,
    `create_user`        varchar(60)  NOT NULL,
    `update_time`        datetime      DEFAULT NULL,
    `update_user`        varchar(60)  NOT NULL,
    `tenant_id`          bigint(20) NOT NULL COMMENT '租户ID',
    `set_id`             bigint(20) DEFAULT NULL COMMENT '标准集id',
    `status`             int(11) DEFAULT '1' COMMENT '启停，1启用，0停用',
    `encrypt`            varchar(1)    DEFAULT NULL,
    `dept`               varchar(255)  DEFAULT NULL,
    `business_system`    varchar(255)  DEFAULT NULL,
    `store_cycle`        varchar(255)  DEFAULT NULL,
    `database_name`      varchar(255)  DEFAULT NULL,
    `sensitive_type`     varchar(255)  DEFAULT NULL,
    `specification`      varchar(255)  DEFAULT NULL,
    `version_no`         varchar(100)  DEFAULT NULL COMMENT '版本号',
    `config_id`          bigint(20) DEFAULT NULL COMMENT '数据标准id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='数据标准配置多版本记录';
