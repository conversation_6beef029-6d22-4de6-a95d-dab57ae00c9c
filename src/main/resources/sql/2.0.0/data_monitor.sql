CREATE TABLE `qua_monitor_rule_exec_record` (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  rule_id bigint(20) DEFAULT NULL COMMENT '模型id',
  exec_result varchar(2) DEFAULT NULL COMMENT '试跑结果 1-触发规则  0-未触发规则 -1-执行失败',
  create_time datetime DEFAULT NULL COMMENT '执行时间',
  create_user varchar(64) DEFAULT NULL COMMENT '创建人',
  tenant_id bigint(20) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='模型试跑记录表';

ALTER TABLE qua_monitor_job ADD COLUMN execute_rule_type varchar(2) DEFAULT NULL COMMENT '01-弱规则执行  02-强规则执行';
ALTER TABLE qua_monitor_job ADD COLUMN flow_id bigint(20) DEFAULT NULL COMMENT '数据源ID';

alter table qua_monitor_model ADD COLUMN open_status varchar(2) DEFAULT '1' COMMENT '0-关闭数据源 1-启用数据源';