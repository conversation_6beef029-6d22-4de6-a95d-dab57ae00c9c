-- 数据字典相关的表
CREATE TABLE `data_dictionary_base` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dict_name` varchar(128) DEFAULT NULL COMMENT '字典名称',
  `dict_desc` varchar(1024) DEFAULT NULL COMMENT '字典描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COMMENT='数据字典基础信息';

CREATE TABLE `data_dictionary_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dict_id` bigint(20) NOT NULL COMMENT '字典ID',
  `category_name` varchar(128) DEFAULT NULL COMMENT '分类名称',
  `category_desc` varchar(1024) DEFAULT NULL COMMENT '分类描述',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父级分类',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8 COMMENT='数据字典分类';

CREATE TABLE `data_dictionary_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dict_id` bigint(20) NOT NULL COMMENT '字典ID',
  `category_id` bigint(20) NOT NULL COMMENT '字典分类ID',
  `item_id` bigint(20) NOT NULL COMMENT '字典项ID',
  `item_name` varchar(128) DEFAULT NULL COMMENT '字典项名称',
  `item_desc` varchar(1024) DEFAULT NULL COMMENT '字典项描述',
  `item_type` varchar(64) DEFAULT NULL COMMENT '字典项类型 table-表  column-字段',
  `item_constraint` varchar(128) DEFAULT NULL COMMENT '字典项约束',
  `datasource_type` varchar(64) DEFAULT NULL COMMENT '数据源类型',
  `database_id` bigint(20) DEFAULT NULL COMMENT '数据库id',
  `database_name` varchar(128) DEFAULT NULL COMMENT '数据库名称',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `column_name_cn` varchar(255) DEFAULT NULL COMMENT '字典项中文名',
  `table_name` varchar(255) DEFAULT NULL COMMENT '表名称',
  `field_type` varchar(100) DEFAULT NULL COMMENT '字段类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8 COMMENT='数据字典项';