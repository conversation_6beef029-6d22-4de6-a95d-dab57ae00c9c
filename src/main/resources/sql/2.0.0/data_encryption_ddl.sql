create table data_encryption_algorithm
(
    id              int auto_increment
        primary key,
    key_name        varchar(200)                        null comment '密钥名称',
    key_algorithm   varchar(100)                        null comment '加密算法:SM2,SM3,SM4',
    level_id        int                                 null comment '分级ID',
    type_id         int                                 null comment '分类ID',
    remark          varchar(200)                        null,
    sm2_public_key  varchar(1000)                       null comment 'sm2算法公钥',
    sm2_private_key varchar(1000)                       null comment 'sm2算法私钥',
    sm4_key_base64  varchar(1000)                       null comment 'sm4算法key',
    sm4_iv_base64   varchar(1000)                       null comment 'sm4算法偏移量',
    create_user     varchar(100)                        null,
    create_time     timestamp default CURRENT_TIMESTAMP null,
    update_user     varchar(100)                        null,
    update_time     timestamp default CURRENT_TIMESTAMP null,
    tenant_id       int                                 null,
    is_enable       int                                 null comment '是否启用，0停止，1启用'
)
    comment '数据加密算法';

create table data_encryption_algorithm_back_up
(
    id                      int auto_increment
        primary key,
    encryption_algorithm_id int                                 null comment '密钥ID',
    key_name                varchar(200)                        null comment '密钥名称',
    key_algorithm           varchar(100)                        null comment '加密算法:SM2,SM3,SM4',
    level_id                int                                 null comment '分级ID',
    level_name              varchar(100)                        null comment '分级',
    type_id                 int                                 null comment '分类ID',
    type_name               varchar(100)                        null comment '分级',
    remark                  varchar(200)                        null,
    sm2_public_key          varchar(1000)                       null comment 'sm2算法公钥',
    sm2_private_key         varchar(1000)                       null comment 'sm2算法私钥',
    sm4_key_base64          varchar(1000)                       null comment 'sm4算法key',
    sm4_iv_base64           varchar(1000)                       null comment 'sm4算法偏移量',
    back_up_user            varchar(100)                        null,
    back_up_time            timestamp default CURRENT_TIMESTAMP null,
    tenant_id               int                                 null
)
    comment '数据加密算法备份';

create table data_encryption_algorithm_job
(
    id                int auto_increment
        primary key,
    job_name          varchar(200)                        null comment '任务名称',
    datasource_type   varchar(100)                        null comment '数据源类型：Clickhouse,Elasticsearch,Hive,Mysql',
    encryption_object varchar(100)                        null comment '加密对象',
    encryption_type   varchar(100)                        null comment '加密类型',
    target_object     varchar(100)                        null comment '目标对象',
    remark            varchar(200)                        null,
    state             varchar(500)                        null comment '加密状态：1，执行中，2执行完成',
    create_user       varchar(100)                        null,
    create_time       timestamp default CURRENT_TIMESTAMP null,
    update_user       varchar(100)                        null,
    update_time       timestamp default CURRENT_TIMESTAMP null,
    tenant_id         int                                 null,
    finish_time       timestamp                           null comment '加密完成时间',
    result            text                                null comment '加密结果'
)
    comment '数据加密算法任务';

create table data_encryption_algorithm_job_field
(
    id                      int auto_increment
        primary key,
    job_id                  int          null comment '任务ID',
    field_name              varchar(200) null comment '字段名称，多个字段逗号分割',
    encryption_algorithm_id int          null comment '算法ID'
)
    comment '数据加密算法任务字段';

create table data_encryption_algorithm_job_record
(
    id           int auto_increment
        primary key,
    job_id       int                         null comment '任务ID',
    field_name   varchar(200)                null comment '加密字段',
    key_name     varchar(200)                null comment '加密密钥',
    before_text  varchar(200)                null comment '加密前样本',
    after_text   varchar(200)                null comment '加密后样本',
    state        varchar(200) default '成功' null comment '加密状态',
    should_count bigint                      null comment '应加密字段数',
    actual_count bigint                      null comment '实际加密字段数',
    success_rate varchar(200)                null comment '加密成功率'
)
    comment '数据加密算法任务记录表';

