ALTER TABLE  data_mart_asset ADD COLUMN `data_name` varchar(256) DEFAULT NULL COMMENT '数据名';
ALTER TABLE  data_mart_asset ADD COLUMN `asset_desc` varchar(512) DEFAULT NULL COMMENT '描述';
ALTER TABLE  data_mart_asset ADD COLUMN `start_time` varchar(32) DEFAULT NULL COMMENT '开始时间';
ALTER TABLE  data_mart_asset ADD COLUMN `end_time` varchar(32) DEFAULT NULL COMMENT '结束时间';


-- network_sdc.data_mart_subscribe definition
CREATE TABLE `data_mart_subscribe` (
   `id` bigint(20) NOT NULL AUTO_INCREMENT,
   `asset_id` bigint(20) DEFAULT NULL COMMENT '数据集市资产id',
   `tenant_id` int(11) DEFAULT NULL COMMENT '租户id',
   `user_id` varchar(100) DEFAULT NULL COMMENT '订阅用户id',
   `subscription_channel` int(11) DEFAULT NULL COMMENT '订阅方式，1 数据API，2 Kafka，3 Elasticsearch，4 ClickHouse',
   `subscription_time` datetime DEFAULT NULL COMMENT '订阅时间',
   `subscription_user` varchar(100) DEFAULT NULL COMMENT '订阅人',
   `subscription_remark` varchar(500) DEFAULT NULL COMMENT '申请说明',
   `approval_progress` int(11) DEFAULT NULL COMMENT '审批进度,1 审批中，2 同意 3 拒绝',
   `approval_time` datetime DEFAULT NULL COMMENT '审批时间',
   `approval_remark` varchar(500) DEFAULT NULL COMMENT '审批说明',
   `approval_user` varchar(100) DEFAULT NULL COMMENT '审批人',
   `configuration_progress` int(11) DEFAULT NULL COMMENT '配置进度，1 配置中 2 配置成功 3 配置失败',
   `configuration_user` varchar(100) DEFAULT NULL COMMENT '配置人',
   `configuration_time` datetime DEFAULT NULL COMMENT '配置时间',
   `configuration_remark` varchar(500) DEFAULT NULL COMMENT '配置说明',
   `call_num` bigint(20) DEFAULT '0' COMMENT '调用次数',
   `call_num_limit` int(11) DEFAULT '0' COMMENT '是否限制调用次数，0限制，1不限制',
   `begin_time` datetime DEFAULT NULL COMMENT '使用开始时间',
   `end_time` datetime DEFAULT NULL COMMENT '使用结束时间',
   `time_limit` int(11) DEFAULT '0' COMMENT '是否限制使用时间，0限制，1不限制',
   `kafka_topic` varchar(100) DEFAULT NULL COMMENT 'Kafka topic',
   `kafka_address` varchar(255) DEFAULT NULL COMMENT 'Kafka集群地址',
   `kafka_port` int(11) DEFAULT NULL COMMENT 'kafka端口',
   `elasticsearch_index` varchar(100) DEFAULT NULL COMMENT 'elasticsearch索引',
   `clickhouse_table` varchar(100) DEFAULT NULL COMMENT 'ClickHouse表名',
   `api_id` bigint(20) DEFAULT NULL COMMENT '数据API订阅通过后的api主键id',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据集市订阅表';

CREATE TABLE `data_mart_subscribe_notice` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `subscribe_id` bigint(20) DEFAULT NULL COMMENT '订阅id',
  `content` text COMMENT '消息内容',
  `create_time` datetime DEFAULT NULL COMMENT '消息入库时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='数据集市订阅通知表';


ALTER TABLE data_mart_subscribe ADD data_usage int NULL COMMENT '数据用途';
ALTER TABLE data_mart_subscribe ADD network_domain int NULL COMMENT '网络域';
ALTER TABLE data_mart_subscribe ADD business_ip varchar(100) NULL COMMENT '服务器业务IP';
ALTER TABLE data_mart_subscribe ADD hosting_network_ip varchar(100) NULL COMMENT '服务器承载网IP';
ALTER TABLE data_mart_subscribe ADD vpc_name varchar(100) NULL COMMENT 'VPC名称';
ALTER TABLE data_mart_subscribe ADD contact_phone varchar(100) NULL COMMENT '业务联系人电话';
ALTER TABLE data_mart_subscribe ADD contact_email varchar(100) NULL COMMENT '联系人邮箱';
ALTER TABLE data_mart_subscribe ADD resource_pool_id int NULL COMMENT '所属资源池id';
ALTER TABLE data_mart_subscribe ADD subscribe_rule_id varchar(100) NULL COMMENT '订阅id，按规则生成';
ALTER TABLE data_mart_subscribe ADD configuration_resource_pool_id int NULL;
ALTER TABLE data_mart_subscribe ADD configuration_business_ip varchar(100) NULL;
ALTER TABLE data_mart_subscribe ADD configuration_hosting_network_ip varchar(100) NULL;
ALTER TABLE data_mart_subscribe ADD configuration_vpc_name varchar(100) NULL;
ALTER TABLE data_mart_subscribe ADD configuration_network_domain int NULL;
ALTER TABLE data_mart_subscribe ADD configuration_kafka_topic varchar(100) NULL;
ALTER TABLE data_mart_subscribe ADD configuration_kafka_consumer_group varchar(100) NULL;
ALTER TABLE data_mart_subscribe ADD configuration_host_name varchar(100) NULL;

CREATE TABLE `data_mart_resource_pool` (
     `id` int(11) NOT NULL AUTO_INCREMENT,
     `name` varchar(100) NOT NULL COMMENT '资源池名称',
     `parent_id` int(11) DEFAULT NULL COMMENT '上级资源池id',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

ALTER TABLE data_mart_asset ADD asset_json text NULL COMMENT '资产集合';
ALTER TABLE data_mart_asset ADD update_time datetime NULL COMMENT '更新时间';


INSERT INTO data_mart_resource_pool (id, name, parent_id) VALUES (1, 'IT云区域中心', null);
INSERT INTO data_mart_resource_pool (id, name, parent_id) VALUES (2, '哈尔滨资源池', 1);
INSERT INTO data_mart_resource_pool (id, name, parent_id) VALUES (3, '航空港资源池', 1);
INSERT INTO data_mart_resource_pool (id, name, parent_id) VALUES (4, '苏州资源池', 1);
INSERT INTO data_mart_resource_pool (id, name, parent_id) VALUES (5, 'IT云省节点', null);
INSERT INTO data_mart_resource_pool (id, name, parent_id) VALUES (6, '北京资源池', 5);
INSERT INTO data_mart_resource_pool (id, name, parent_id) VALUES (7, '上海资源池', 5);
INSERT INTO data_mart_resource_pool (id, name, parent_id) VALUES (8, '贵州资源池', 5);

CREATE TABLE `data_mart_tag` (
   id bigint(20) NOT NULL AUTO_INCREMENT,
   tag_name varchar(128) DEFAULT NULL COMMENT '标签名称',
   tag_desc varchar(512) DEFAULT NULL COMMENT '标签备注',
   create_time datetime NULL DEFAULT NULL COMMENT '创建时间',
   create_user varchar(100) DEFAULT NULL COMMENT '创建人',
   update_time datetime NULL DEFAULT NULL COMMENT '更新时间',
   update_user varchar(100) DEFAULT NULL COMMENT '更新人',
   tenant_id bigint(20) NOT NULL COMMENT '租户ID',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据集市标签';

ALTER TABLE data_mart_asset_subscribe ADD COLUMN tag_id bigint(20) DEFAULT NULL COMMENT '标签';
