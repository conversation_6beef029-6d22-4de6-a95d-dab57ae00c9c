ALTER TABLE tb_cluster ADD COLUMN key_tab_path varchar(512) DEFAULT NULL COMMENT 'keytab文件路径';
ALTER TABLE tb_cluster ADD COLUMN krb5_conf_path varchar(512) DEFAULT NULL COMMENT 'krb5conf文件路径';
ALTER TABLE tb_cluster ADD COLUMN jaas_conf_path varchar(512) DEFAULT NULL COMMENT 'jass文件路径';
ALTER TABLE tb_cluster ADD COLUMN kbs_account varchar(128) DEFAULT NULL COMMENT 'kbs账号';
ALTER TABLE tb_cluster ADD COLUMN enable_kbs varchar(8) DEFAULT '0' COMMENT '是否开启kerberos认证 0-关闭 1-开启';
