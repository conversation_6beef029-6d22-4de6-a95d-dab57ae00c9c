-- 添加 data_time 列
ALTER TABLE qua_monitor_rule_template ADD COLUMN data_time varchar(10) DEFAULT '0';
COMMENT ON COLUMN qua_monitor_rule_template.data_time IS '取数日期: 0昨天，1上周，2上月';

-- 添加 filter_by_datetime 列
ALTER TABLE qua_monitor_rule_template ADD COLUMN filter_by_datetime INT DEFAULT 0;
COMMENT ON COLUMN qua_monitor_rule_template.filter_by_datetime IS '是否基于时间字段过滤，0-是，1-否';

-- 添加 datetime_function 列
ALTER TABLE qua_monitor_rule_template ADD COLUMN datetime_function VARCHAR(100);
COMMENT ON COLUMN qua_monitor_rule_template.datetime_function IS '时间转换函数';
