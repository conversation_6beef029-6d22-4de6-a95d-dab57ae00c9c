-- 创建数据源表
CREATE TABLE tenant_define_data_sources
(
    id                     SERIAL PRIMARY KEY,
    name                   VA<PERSON>HAR(100) NOT NULL,
    type                   VARCHAR(20)  NOT NULL CHECK (type IN
                                                        ('Mysql', 'Oracle', 'PostgreSql', 'Clickhouse', 'Elasticsearch',
                                                         'Kafka', 'Hive')),
    host                   VARCHAR(255) NOT NULL,
    port                   INTEGER      NOT NULL,
    db_name                VA<PERSON>HAR(100) ,
    schema_name            VARCHAR(255),
    es_index_prefix        VARCHAR(255),
    kfk_topic_prefix       VARCHAR(255),
    kfk_zk_host            VARCHAR(255),
    kfk_zk_port            INTEGER,
    ora_service_or_sid     varchar(20),
    description            TEXT,
    status                 VARCHAR(20)  NOT NULL CHECK (status IN ('connected', 'disconnected', 'error')),
--       cluster_enabled BOOLEAN NOT NULL DEFAULT FALSE,
--       cluster_id VARCHAR(36),
    kerberos_enabled       BOOLEAN      NOT NULL DEFAULT FALSE,
    kerberos_config_id     INTEGER,
    credentials_enabled    BOOLEAN      NOT NULL DEFAULT FALSE,
    credentials_config_id  INTEGER,
    certificates_enabled   BOOLEAN      NOT NULL DEFAULT FALSE,
    certificates_config_id INTEGER,
    create_time            TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user            VARCHAR(128) NOT NULL,
    update_time            TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user            VARCHAR(128) NOT NULL,
    tenant_id              INTEGER,
    tenant_name            VARCHAR(100) NOT NULL
);

-- 为数据源表添加字段注释
COMMENT ON COLUMN tenant_define_data_sources.id IS '数据源唯一标识符';
COMMENT ON COLUMN tenant_define_data_sources.name IS '数据源名称';
COMMENT ON COLUMN tenant_define_data_sources.type IS '数据源类型';
COMMENT ON COLUMN tenant_define_data_sources.host IS '数据源主机地址';
COMMENT ON COLUMN tenant_define_data_sources.port IS '数据源端口号';
COMMENT ON COLUMN tenant_define_data_sources.db_name IS '数据库名称';
COMMENT ON COLUMN tenant_define_data_sources.schema_name IS '数据库模式';
COMMENT ON COLUMN tenant_define_data_sources.es_index_prefix IS 'Elasticsearch索引前缀';
COMMENT ON COLUMN tenant_define_data_sources.kfk_topic_prefix IS 'Kafka主题前缀';
COMMENT ON COLUMN tenant_define_data_sources.kfk_zk_host IS 'Kafka ZK主机地址';
COMMENT ON COLUMN tenant_define_data_sources.kfk_zk_port IS 'Kafka ZK端口号';
COMMENT ON COLUMN tenant_define_data_sources.ora_service_or_sid IS 'Oracle服务名或SID';
COMMENT ON COLUMN tenant_define_data_sources.description IS '数据源描述信息';
COMMENT ON COLUMN tenant_define_data_sources.status IS '连接状态';
-- COMMENT ON COLUMN tenant_define_data_sources.cluster_enabled IS '是否启用集群';
-- COMMENT ON COLUMN tenant_define_data_sources.cluster_id IS '关联的集群ID';
COMMENT ON COLUMN tenant_define_data_sources.kerberos_enabled IS '是否启用Kerberos认证';
COMMENT ON COLUMN tenant_define_data_sources.kerberos_config_id IS '关联的Kerberos配置ID';
COMMENT ON COLUMN tenant_define_data_sources.credentials_enabled IS '是否启用凭证';
COMMENT ON COLUMN tenant_define_data_sources.credentials_config_id IS '关联的凭证ID';
COMMENT ON COLUMN tenant_define_data_sources.certificates_enabled IS '是否启用证书';
COMMENT ON COLUMN tenant_define_data_sources.certificates_config_id IS '关联的证书ID';
COMMENT ON COLUMN tenant_define_data_sources.create_time IS '创建时间';
COMMENT ON COLUMN tenant_define_data_sources.create_user IS '创建用户';
COMMENT ON COLUMN tenant_define_data_sources.update_time IS '更新时间';
COMMENT ON COLUMN tenant_define_data_sources.update_user IS '更新用户';
COMMENT ON COLUMN tenant_define_data_sources.tenant_id IS '租户ID';
CREATE TABLE tenant_define_kerberos_configs
(
    id              SERIAL PRIMARY KEY,
    name            VARCHAR(100) NOT NULL,
    principal       VARCHAR(255) NOT NULL,
    keytab_path     VARCHAR(255) NOT NULL,
    krb5_path       varchar(255) not null,
    keytab_uploaded BOOLEAN      NOT NULL DEFAULT FALSE,
    enabled         BOOLEAN      NOT NULL DEFAULT TRUE,
    create_time     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user     VARCHAR(128) NOT NULL,
    update_time     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user     VARCHAR(128) NOT NULL,
    tenant_id       INTEGER,
    tenant_name     VARCHAR(100) not null
);
--
-- -- 创建集群表
-- CREATE TABLE unify_clusters (
--       id SERIAL PRIMARY KEY,
--       name VARCHAR(100) NOT NULL,
--       type VARCHAR(20) NOT NULL CHECK (type IN ('Mysql', 'Oracle', 'PostgreSql', 'Clickhouse', 'Elasticsearch', 'Kafka','Hive')),
--       description TEXT,
--       node_count INTEGER NOT NULL DEFAULT 0,
--       status VARCHAR(20) NOT NULL CHECK (status IN ('running', 'stopped', 'degraded')),
--       create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
--       create_user VARCHAR(128) NOT NULL,
--       update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
--       update_user VARCHAR(128) NOT NULL
-- );
--
-- -- 为集群表添加字段注释
-- COMMENT ON COLUMN unify_clusters.id IS '集群唯一标识符';
-- COMMENT ON COLUMN unify_clusters.name IS '集群名称';
-- COMMENT ON COLUMN unify_clusters.type IS '集群类型';
-- COMMENT ON COLUMN unify_clusters.description IS '集群描述信息';
-- COMMENT ON COLUMN unify_clusters.node_count IS '节点数量';
-- COMMENT ON COLUMN unify_clusters.status IS '集群状态';
-- COMMENT ON COLUMN unify_clusters.create_time IS '创建时间';
-- COMMENT ON COLUMN unify_clusters.create_user IS '创建用户';
-- COMMENT ON COLUMN unify_clusters.update_time IS '更新时间';
-- COMMENT ON COLUMN unify_clusters.update_user IS '更新用户';
--
-- -- 创建集群节点表
-- CREATE TABLE unify_cluster_nodes (
--        id SERIAL PRIMARY KEY,
--        cluster_id VARCHAR(36) NOT NULL,
--        host VARCHAR(255) NOT NULL,
--        port INTEGER NOT NULL,
--        role VARCHAR(20) NOT NULL CHECK (role IN ('master', 'slave', 'worker')),
--        status VARCHAR(20) NOT NULL CHECK (status IN ('online', 'offline', 'warning')),
--        kerberos_enabled BOOLEAN NOT NULL DEFAULT FALSE,
--        kerberos_config_id VARCHAR(36),
--        create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
--        update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
--        FOREIGN KEY (cluster_id) REFERENCES unify_clusters(id) ON DELETE CASCADE
-- );
--
-- -- 为集群节点表添加字段注释
-- COMMENT ON COLUMN unify_cluster_nodes.id IS '节点唯一标识符';
-- COMMENT ON COLUMN unify_cluster_nodes.cluster_id IS '所属集群ID';
-- COMMENT ON COLUMN unify_cluster_nodes.host IS '节点主机地址';
-- COMMENT ON COLUMN unify_cluster_nodes.port IS '节点端口号';
-- COMMENT ON COLUMN unify_cluster_nodes.role IS '节点角色';
-- COMMENT ON COLUMN unify_cluster_nodes.status IS '节点状态';
-- COMMENT ON COLUMN unify_cluster_nodes.kerberos_enabled IS '是否启用Kerberos认证';
-- COMMENT ON COLUMN unify_cluster_nodes.kerberos_config_id IS '关联的Kerberos配置ID';
-- COMMENT ON COLUMN unify_cluster_nodes.create_time IS '创建时间';
-- COMMENT ON COLUMN unify_cluster_nodes.update_time IS '更新时间';

-- 创建Kerberos配置表
COMMENT ON COLUMN tenant_define_data_sources.tenant_name IS '租户名称';

-- 为Kerberos配置表添加字段注释
COMMENT ON COLUMN tenant_define_kerberos_configs.id IS 'Kerberos配置唯一标识符';
COMMENT ON COLUMN tenant_define_kerberos_configs.name IS '配置名称';
COMMENT ON COLUMN tenant_define_kerberos_configs.principal IS 'Kerberos主体';
COMMENT ON COLUMN tenant_define_kerberos_configs.keytab_path IS 'Keytab文件';
COMMENT ON COLUMN tenant_define_kerberos_configs.krb5_path IS 'krb5文件';
COMMENT ON COLUMN tenant_define_kerberos_configs.keytab_uploaded IS 'Keytab是否已上传';
COMMENT ON COLUMN tenant_define_kerberos_configs.enabled IS '是否启用';
COMMENT ON COLUMN tenant_define_kerberos_configs.create_time IS '创建时间';
COMMENT ON COLUMN tenant_define_kerberos_configs.create_user IS '创建用户';
COMMENT ON COLUMN tenant_define_kerberos_configs.update_time IS '更新时间';
COMMENT ON COLUMN tenant_define_kerberos_configs.update_user IS '更新用户';
COMMENT ON COLUMN tenant_define_kerberos_configs.tenant_id IS '租户ID';
COMMENT ON COLUMN tenant_define_kerberos_configs.tenant_name IS '租户名称';


-- 创建用户凭证表
CREATE TABLE tenant_define_credentials
(
    id          SERIAL PRIMARY KEY,
    name        VARCHAR(100) NOT NULL,
    username    VARCHAR(100) NOT NULL,
    password    VARCHAR(255) NOT NULL,
    description TEXT,
    last_used   TIMESTAMP,
    create_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user VARCHAR(128) NOT NULL,
    update_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user VARCHAR(128) NOT NULL,
    tenant_id   INTEGER       NULL,
    tenant_name VARCHAR(100) not null
);

-- 为用户凭证表添加字段注释
COMMENT ON COLUMN tenant_define_credentials.id IS '凭证唯一标识符';
COMMENT ON COLUMN tenant_define_credentials.name IS '凭证名称';
COMMENT ON COLUMN tenant_define_credentials.username IS '用户名';
COMMENT ON COLUMN tenant_define_credentials.password IS '密码';
COMMENT ON COLUMN tenant_define_credentials.description IS '凭证描述';
COMMENT ON COLUMN tenant_define_credentials.last_used IS '最后使用时间';
COMMENT ON COLUMN tenant_define_credentials.create_time IS '创建时间';
COMMENT ON COLUMN tenant_define_credentials.create_user IS '创建用户';
COMMENT ON COLUMN tenant_define_credentials.update_time IS '更新时间';
COMMENT ON COLUMN tenant_define_credentials.update_user IS '更新用户';
COMMENT ON COLUMN tenant_define_credentials.tenant_id IS '租户ID';
COMMENT ON COLUMN tenant_define_credentials.tenant_name IS '租户名称';

-- 创建SSL证书表
CREATE TABLE tenant_define_certificates
(
    id               SERIAL PRIMARY KEY,
    name             VARCHAR(100) NOT NULL,
    type             VARCHAR(20)  NOT NULL CHECK (type IN ('client', 'server', 'ca')),
    subject          VARCHAR(255) NOT NULL,
    issuer           VARCHAR(255) NOT NULL,
    valid_from       DATE         NOT NULL,
    valid_to         DATE         NOT NULL,
    fingerprint      VARCHAR(255) NOT NULL,
    enabled          BOOLEAN      NOT NULL DEFAULT TRUE,
    certificate_data BYTEA,
    create_time      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user      VARCHAR(128) NOT NULL,
    update_time      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user      VARCHAR(128) NOT NULL,
    tenant_id        INTEGER       NULL,
    tenant_name      VARCHAR(100) not null
);

-- 为SSL证书表添加字段注释
COMMENT ON COLUMN tenant_define_certificates.id IS '证书唯一标识符';
COMMENT ON COLUMN tenant_define_certificates.name IS '证书名称';
COMMENT ON COLUMN tenant_define_certificates.type IS '证书类型';
COMMENT ON COLUMN tenant_define_certificates.subject IS '证书主体';
COMMENT ON COLUMN tenant_define_certificates.issuer IS '证书颁发者';
COMMENT ON COLUMN tenant_define_certificates.valid_from IS '证书生效日期';
COMMENT ON COLUMN tenant_define_certificates.valid_to IS '证书过期日期';
COMMENT ON COLUMN tenant_define_certificates.fingerprint IS '证书指纹';
COMMENT ON COLUMN tenant_define_certificates.enabled IS '是否启用';
COMMENT ON COLUMN tenant_define_certificates.certificate_data IS '证书内容数据';
COMMENT ON COLUMN tenant_define_certificates.create_time IS '创建时间';
COMMENT ON COLUMN tenant_define_certificates.create_user IS '创建用户';
COMMENT ON COLUMN tenant_define_certificates.update_time IS '更新时间';
COMMENT ON COLUMN tenant_define_certificates.update_user IS '更新用户';
COMMENT ON COLUMN tenant_define_certificates.tenant_id IS '租户ID';
COMMENT ON COLUMN tenant_define_certificates.tenant_name IS '租户名称';


-- 添加表注释
COMMENT ON TABLE tenant_define_data_sources IS '数据源管理表';
-- COMMENT ON TABLE unify_clusters IS '集群管理表';
-- COMMENT ON TABLE unify_cluster_nodes IS '集群节点表';
COMMENT ON TABLE tenant_define_kerberos_configs IS 'Kerberos认证配置表';
COMMENT ON TABLE tenant_define_credentials IS '用户凭证管理表';
COMMENT ON TABLE tenant_define_certificates IS 'SSL证书管理表';

-- 添加外键约束
-- ALTER TABLE tenant_define_data_sources
--     ADD CONSTRAINT fk_tenant_define_data_sources_cluster
--         FOREIGN KEY (cluster_id) REFERENCES unify_clusters(id);

ALTER TABLE tenant_define_data_sources
    ADD CONSTRAINT fk_tenant_define_data_sources_kerberos
        FOREIGN KEY (kerberos_config_id) REFERENCES tenant_define_kerberos_configs (id);

-- ALTER TABLE unify_cluster_nodes
--     ADD CONSTRAINT fk_unify_cluster_nodes_kerberos
--         FOREIGN KEY (kerberos_config_id) REFERENCES tenant_define_kerberos_configs(id);

-- 创建索引以提高查询效率
CREATE INDEX idx_tenant_define_data_sources_type ON tenant_define_data_sources (type);
CREATE INDEX idx_tenant_define_data_sources_status ON tenant_define_data_sources (status);
-- CREATE INDEX idx_unify_clusters_type ON unify_clusters(type);
-- CREATE INDEX idx_unify_clusters_status ON unify_clusters(status);
-- CREATE INDEX idx_unify_cluster_nodes_status ON unify_cluster_nodes(status);
-- CREATE INDEX idx_unify_cluster_nodes_cluster_id ON unify_cluster_nodes(cluster_id);
CREATE INDEX idx_kerberos_enabled ON tenant_define_kerberos_configs (enabled);
CREATE INDEX idx_tenant_define_certificates_type ON tenant_define_certificates (type);
CREATE INDEX idx_tenant_define_certificates_enabled ON tenant_define_certificates (enabled);
CREATE INDEX idx_tenant_define_certificates_valid_to ON tenant_define_certificates (valid_to);


INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path,
                           manage_free, hidden, parent_name, status, menu_order, default_order, default_name,
                           default_status, default_parent, create_user, create_date, update_user, update_date,
                           application, level1, menu_category, new_open_window)
VALUES ('builtIn', '1', 3, 'system', '认证管理', 'certification-management', null, '0', '0', 'cluster-resource-manage', '1', 2,
        2, '认证管理', '1', 'resource-manage', null, null, null, null, null, null, '0', 0);



alter table t_ds_datasource add tenant_define_datasource_id integer;
comment on column t_ds_datasource.tenant_define_datasource_id is '租户自定义数据源 ID';

alter table qua_wab_element add tenant_datasource_id integer;
comment on column qua_wab_element.tenant_datasource_id is '统一数据源 id';

ALTER TABLE tb_cluster ADD node_address TEXT;
ALTER TABLE tb_cluster ADD connection_params TEXT;


alter table tenant_define_certificates drop constraint tenant_define_certificates_type_check;

ALTER TABLE tenant_define_kerberos_configs ADD service_name varchar(255);

