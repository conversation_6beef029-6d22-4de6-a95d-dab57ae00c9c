CREATE TABLE IF NOT EXISTS ai_mirror
(
	id SERIAL  PRIMARY KEY,
	function_classify VARCHAR(200) NOT NULL ,
	repository_id INTEGER NOT NULL ,
	mirror_name VARCHAR(200) NOT NULL ,
	tag VARCHAR(200),
	mirror_desc VARCHAR(200),
	mirror_use VARCHAR(100) NOT NULL ,
	mirror_file_path VARCHAR(500) NOT NULL ,
	mirror_file_name VARCHAR(200) NOT NULL ,
	status INTEGER DEFAULT 0,
	error_message VARCHAR(500),
	tenant_id INTEGER,
	create_user VARCHAR(50) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(50) NOT NULL ,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	mirror_addr VARCHAR(1024),
	add_type VARCHAR(2) DEFAULT '1',
	publish_status VARCHAR(2) DEFAULT '0'
);

CREATE TABLE IF NOT EXISTS ai_mirror_repository
(
	id SERIAL  PRIMARY KEY,
	repository_name VARCHAR(200) NOT NULL ,
	repository_addr VARCHAR(500) NOT NULL ,
	user_name VARCHAR(200) NOT NULL ,
	password VARCHAR(200) NOT NULL ,
	k8s_hubsecret VARCHAR(500),
	tenant_id INTEGER NOT NULL ,
	create_user VARCHAR(50) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(50) NOT NULL ,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS ai_model
(
	id SERIAL  PRIMARY KEY,
	project_id INTEGER NOT NULL ,
	model_name VARCHAR(200) NOT NULL ,
	training_framework VARCHAR(200) NOT NULL ,
	service_type VARCHAR(200) NOT NULL ,
	desc VARCHAR(200),
	tenant_id INTEGER NOT NULL ,
	create_user VARCHAR(50) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(50) NOT NULL ,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	framework_name VARCHAR(512)
);

CREATE TABLE IF NOT EXISTS ai_model_push
(
	id SERIAL  PRIMARY KEY,
	model_id INTEGER NOT NULL ,
	version_id INTEGER NOT NULL ,
	mirror_id INTEGER NOT NULL ,
	resource_cpu INTEGER NOT NULL ,
	resource_gpu INTEGER NOT NULL ,
	resource_memory INTEGER NOT NULL ,
	min_replica INTEGER NOT NULL ,
	max_replica INTEGER NOT NULL ,
	hpa VARCHAR(300) NOT NULL ,
	canary INTEGER NOT NULL ,
	canary_weight INTEGER,
	model_config_list VARCHAR(2000) NOT NULL ,
	command VARCHAR(2000) NOT NULL ,
	health_check VARCHAR(2000) NOT NULL ,
	push_param VARCHAR(5000),
	deploy_status INTEGER DEFAULT 0,
	deploy_error TEXT,
	deploy_url VARCHAR(2000),
	tenant_id INTEGER NOT NULL ,
	create_user VARCHAR(50) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(50) NOT NULL ,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	cfg_file TEXT,
	work_dir TEXT,
	env_var TEXT,
	service_port VARCHAR(32),
	url_prefix VARCHAR(32)
);

CREATE TABLE IF NOT EXISTS ai_model_version
(
	id SERIAL  PRIMARY KEY,
	model_id INTEGER NOT NULL ,
	model_version VARCHAR(200) NOT NULL ,
	model_type INTEGER NOT NULL ,
	model_file VARCHAR(500) NOT NULL ,
	desc VARCHAR(200),
	push_status INTEGER DEFAULT 0,
	tenant_id INTEGER NOT NULL ,
	create_user VARCHAR(50) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(50) NOT NULL ,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS ai_notebook
(
	id SERIAL  PRIMARY KEY,
	project_id INTEGER NOT NULL ,
	name VARCHAR(200) NOT NULL ,
	desc VARCHAR(200) NOT NULL ,
	repository_id INTEGER NOT NULL ,
	mirror_id INTEGER NOT NULL ,
	need_mem INTEGER NOT NULL ,
	need_cpu INTEGER NOT NULL ,
	need_gpu INTEGER NOT NULL ,
	status INTEGER DEFAULT 0,
	error_message VARCHAR(500),
	notebook_url VARCHAR(500),
	tenant_id INTEGER NOT NULL ,
	create_user VARCHAR(50) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(50) NOT NULL ,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS ai_project_model
(
	id SERIAL  PRIMARY KEY,
	name_space VARCHAR(200) NOT NULL ,
	en_name VARCHAR(200) NOT NULL ,
	tenant_id INTEGER NOT NULL ,
	cpu_limit INTEGER NOT NULL ,
	need_cpu_limit INTEGER NOT NULL ,
	mem_limit INTEGER NOT NULL ,
	need_mem_limit INTEGER NOT NULL ,
	gpu_limit INTEGER NOT NULL ,
	storage_limit INTEGER NOT NULL ,
	pvc_storage INTEGER,
	pvc_archive INTEGER,
	status INTEGER DEFAULT 0,
	error_message TEXT,
	create_user VARCHAR(50) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(50) NOT NULL ,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);



COMMENT ON COLUMN ai_mirror.function_classify IS '功能分类';
COMMENT ON COLUMN ai_mirror.repository_id IS '仓库ID';
COMMENT ON COLUMN ai_mirror.mirror_name IS '镜像名称';
COMMENT ON COLUMN ai_mirror.tag IS '标签';
COMMENT ON COLUMN ai_mirror.mirror_desc IS '镜像描述';
COMMENT ON COLUMN ai_mirror.mirror_use IS '镜像用途：notebook，模型开发';
COMMENT ON COLUMN ai_mirror.mirror_file_path IS '镜像文件路径';
COMMENT ON COLUMN ai_mirror.mirror_file_name IS '镜像文件名称';
COMMENT ON COLUMN ai_mirror.status IS '推送状态，0推送中，1推送成功，2推送失败';
COMMENT ON COLUMN ai_mirror.error_message IS '失败原因';
COMMENT ON COLUMN ai_mirror.mirror_addr IS '镜像地址';
COMMENT ON COLUMN ai_mirror.add_type IS '添加方式 1-上传镜像 2-镜像引入';
COMMENT ON COLUMN ai_mirror.publish_status IS '发布状态 0-未发布 1-已发布';
COMMENT ON COLUMN ai_mirror_repository.repository_name IS '仓库名称';
COMMENT ON COLUMN ai_mirror_repository.repository_addr IS '仓库地址';
COMMENT ON COLUMN ai_mirror_repository.user_name IS '用户名';
COMMENT ON COLUMN ai_mirror_repository.password IS '密码';
COMMENT ON COLUMN ai_mirror_repository.k8s_hubsecret IS 'k8s密钥';
COMMENT ON COLUMN ai_mirror_repository.tenant_id IS '租户';
COMMENT ON COLUMN ai_model.project_id IS '项目ID';
COMMENT ON COLUMN ai_model.model_name IS '模型名称';
COMMENT ON COLUMN ai_model.training_framework IS '训练框架';
COMMENT ON COLUMN ai_model.service_type IS '服务类型';
COMMENT ON COLUMN ai_model.desc IS '描述';
COMMENT ON COLUMN ai_model.framework_name IS '框架名称';
COMMENT ON COLUMN ai_model_push.model_id IS '模型ID';
COMMENT ON COLUMN ai_model_push.version_id IS '版本ID';
COMMENT ON COLUMN ai_model_push.mirror_id IS '镜像ID';
COMMENT ON COLUMN ai_model_push.resource_cpu IS '内存申请';
COMMENT ON COLUMN ai_model_push.resource_gpu IS 'GPU申请';
COMMENT ON COLUMN ai_model_push.resource_memory IS '内存申请';
COMMENT ON COLUMN ai_model_push.min_replica IS '最小副本';
COMMENT ON COLUMN ai_model_push.max_replica IS '内存申请';
COMMENT ON COLUMN ai_model_push.hpa IS '弹性缩容';
COMMENT ON COLUMN ai_model_push.canary IS '是否灰度发布：0否，1是';
COMMENT ON COLUMN ai_model_push.canary_weight IS '灰度流量';
COMMENT ON COLUMN ai_model_push.model_config_list IS '推理配置';
COMMENT ON COLUMN ai_model_push.command IS '启动命令';
COMMENT ON COLUMN ai_model_push.health_check IS '健康检查';
COMMENT ON COLUMN ai_model_push.push_param IS '推送参数';
COMMENT ON COLUMN ai_model_push.deploy_status IS '部署状态:0未部署，1成功，2失败';
COMMENT ON COLUMN ai_model_push.deploy_error IS '部署失败原因';
COMMENT ON COLUMN ai_model_push.deploy_url IS '部署地址';
COMMENT ON COLUMN ai_model_push.cfg_file IS '配置文件';
COMMENT ON COLUMN ai_model_push.work_dir IS '工作目录';
COMMENT ON COLUMN ai_model_push.env_var IS '环境变量';
COMMENT ON COLUMN ai_model_push.service_port IS '服务端口';
COMMENT ON COLUMN ai_model_push.url_prefix IS 'URL前缀';
COMMENT ON COLUMN ai_model_version.model_id IS '模型ID';
COMMENT ON COLUMN ai_model_version.model_version IS '模型名称';
COMMENT ON COLUMN ai_model_version.model_type IS '模型类型: 0本地，1离线';
COMMENT ON COLUMN ai_model_version.model_file IS '模型文件，本地和离线都是一个文件';
COMMENT ON COLUMN ai_model_version.desc IS '描述';
COMMENT ON COLUMN ai_model_version.push_status IS '发布状态：0未发布，1发布，2灰度发布';
COMMENT ON COLUMN ai_notebook.project_id IS '项目ID';
COMMENT ON COLUMN ai_notebook.name IS '名称';
COMMENT ON COLUMN ai_notebook.desc IS '描述';
COMMENT ON COLUMN ai_notebook.repository_id IS '仓库ID';
COMMENT ON COLUMN ai_notebook.mirror_id IS '镜像ID';
COMMENT ON COLUMN ai_notebook.need_mem IS '申请内存';
COMMENT ON COLUMN ai_notebook.need_cpu IS '申请CPU';
COMMENT ON COLUMN ai_notebook.need_gpu IS '申请GPU';
COMMENT ON COLUMN ai_notebook.status IS '状态：0分配中，1分配成功，1分配失败';
COMMENT ON COLUMN ai_notebook.error_message IS '失败原因';
COMMENT ON COLUMN ai_notebook.notebook_url IS '分配的url';
COMMENT ON COLUMN ai_project_model.name_space IS '命名空间';
COMMENT ON COLUMN ai_project_model.en_name IS '英文名称';
COMMENT ON COLUMN ai_project_model.tenant_id IS '所属租户';
COMMENT ON COLUMN ai_project_model.cpu_limit IS 'CPU限额';
COMMENT ON COLUMN ai_project_model.need_cpu_limit IS '需求CPU限额';
COMMENT ON COLUMN ai_project_model.mem_limit IS '内存限额';
COMMENT ON COLUMN ai_project_model.need_mem_limit IS '需求内存限额';
COMMENT ON COLUMN ai_project_model.gpu_limit IS 'GPU限额';
COMMENT ON COLUMN ai_project_model.storage_limit IS '存储限额';
COMMENT ON COLUMN ai_project_model.status IS '分配状态，0分配中，1分配成功，2分配失败';
COMMENT ON COLUMN ai_project_model.error_message IS '失败原因';


