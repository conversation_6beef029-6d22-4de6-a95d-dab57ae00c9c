alter table qua_web_task alter column task_no type varchar(50) using task_no::varchar(50);
alter table qua_scan_list alter column task_no type varchar(50) using task_no::varchar(50);
alter table qua_scan_mysql_db alter column task_no type varchar(50) using task_no::varchar(50);
alter table qua_scan_mysql_table alter column task_no type varchar(50) using task_no::varchar(50);
alter table qua_scan_mysql_column alter column task_no type varchar(50) using task_no::varchar(50);
alter table qua_scan_hive_db alter column task_no type varchar(50) using task_no::varchar(50);
alter table qua_scan_hive_table alter column task_no type varchar(50) using task_no::varchar(50);
alter table qua_scan_hive_column alter column task_no type varchar(50) using task_no::varchar(50);
alter table qua_scan_ch_db alter column task_no type varchar(50) using task_no::varchar(50);
alter table qua_scan_ch_table alter column task_no type varchar(50) using task_no::varchar(50);
alter table qua_scan_ch_column alter column task_no type varchar(50) using task_no::varchar(50);
alter table qua_scan_es_index alter column task_no type varchar(50) using task_no::varchar(50);
alter table qua_scan_es_field alter column task_no type varchar(50) using task_no::varchar(50);

alter table qua_config_master_data_type alter column type_desc drop not null;

