CREATE TABLE IF NOT EXISTS ds_security_verification_configuration
(
	id SERIAL  PRIMARY KEY,
	password_enable INTEGER DEFAULT 0,
	verify_password VARCHAR(100),
	sm4_enable INTEGER DEFAULT 0,
	tenant_id BIGINT NOT NULL ,
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP
);



COMMENT ON COLUMN ds_security_verification_configuration.password_enable IS '密码验证是否开启，0-否，1-是';
COMMENT ON COLUMN ds_security_verification_configuration.verify_password IS '验证密码';
COMMENT ON COLUMN ds_security_verification_configuration.sm4_enable IS 'sm4加密是否开启，0-否，1-是';
COMMENT ON COLUMN ds_security_verification_configuration.tenant_id IS '租户id';
COMMENT ON COLUMN ds_security_verification_configuration.create_user IS '创建人';
COMMENT ON COLUMN ds_security_verification_configuration.create_time IS '创建时间';
COMMENT ON COLUMN ds_security_verification_configuration.update_user IS '修改人';
COMMENT ON COLUMN ds_security_verification_configuration.update_time IS '修改时间';

