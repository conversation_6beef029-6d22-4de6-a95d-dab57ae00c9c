-- 添加字段语句已添加到ddl.sql


update qua_internal_model set
                              "name" = '表大小日变化波动率检查',
                              model_desc = '统计表磁盘空间大小日变化波动率，检查波动率是否异常。',
                              model_calc = '
①统计所选表的磁盘空间大小日变化(单位是MB)，得到“表大小日变化值”
②将“表大小日变化值”和“表大小日变化标准值”计算波动率：表大小日变化波动率=|表大小日变化值-表大小日变化标准值|/|表大小日变化标准值|。
③ 表大小日变化标准值可来自“自定义”或“动态阈值”。
④)将“表大小日变化波动率” 与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。
⑤基于“杈重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。
⑥基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。
' where model_code = 'tableSize';
update qua_internal_model set
                              "name" = '表行数日变化波动率检查',
                              model_desc = '统计表磁盘空间大小日变化波动率，检查波动率是否异常。',
                              model_calc = '
①统计所选表的行数日变化，得到“表行数日变化值”
②将“表行数日变化值”和“表行数日变化标准值”计算波动率：表行数日变化波动率=|表行数日变化值-表行数日变化标准值|/|表行数标准日变化量|。
③ 将“表行数日变化波动率” 与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。
④基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。
⑤基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。
' where model_code = 'tableLine';