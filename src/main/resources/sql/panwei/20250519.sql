create table qua_scan_kafka
(
    id                 serial,
    topic_name         varchar(100),
    partitions         integer,
    replication_factor integer,
    snapshoot_version  varchar(50),
    task_no            varchar(100)
);

comment on table qua_scan_kafka is '扫描kafka topic结果表';
comment on column qua_scan_kafka.topic_name is 'topic名称';
comment on column qua_scan_kafka.partitions is '分区数';
comment on column qua_scan_kafka.replication_factor is '副本数';
comment on column qua_scan_kafka.snapshoot_version is '快照版本';
comment on column qua_scan_kafka.task_no is '任务编号';

-- Kafka任务结果表
CREATE TABLE qua_web_kafka_task_result
(
    id                 SERIAL PRIMARY KEY,
    task_id            BIGINT       NOT NULL,
    element_id         BIGINT       NOT NULL,
    tenant_id          BIGINT       NOT NULL,
    topic_name         VARCHAR(100) NOT NULL,
    partitions         INTEGER      NOT NULL,
    replication_factor INTEGER      NOT NULL,
    snapshoot_version  VARCHAR(50)  NOT NULL,
    create_user        VARCHAR(50),
    create_time        TIMESTAMP default current_timestamp

);

COMMENT ON TABLE qua_web_kafka_task_result IS 'Kafka任务结果表';
COMMENT ON COLUMN qua_web_kafka_task_result.task_id IS '任务ID';
COMMENT ON COLUMN qua_web_kafka_task_result.element_id IS '元素ID';
COMMENT ON COLUMN qua_web_kafka_task_result.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_web_kafka_task_result.topic_name IS 'topic名称';
COMMENT ON COLUMN qua_web_kafka_task_result.partitions IS '分区数';
COMMENT ON COLUMN qua_web_kafka_task_result.replication_factor IS '副本数';
COMMENT ON COLUMN qua_web_kafka_task_result.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_kafka_task_result.create_user IS '创建人';
COMMENT ON COLUMN qua_web_kafka_task_result.create_time IS '创建时间';

-- Kafka元素详情表
CREATE TABLE qua_web_kafka_element_detail
(
    id                      SERIAL PRIMARY KEY,
    element_id              BIGINT       NOT NULL,
    tenant_id               BIGINT       NOT NULL,
    topic_name              VARCHAR(100) NOT NULL,
    partitions              INTEGER      NOT NULL,
    replication_factor      INTEGER      NOT NULL,
    first_snapshoot_version VARCHAR(50)  NOT NULL,
    last_snapshoot_version  VARCHAR(50)  NOT NULL,
    create_user             VARCHAR(50),
    create_time             TIMESTAMP default current_timestamp,
    update_user             VARCHAR(50),
    update_time             TIMESTAMP default current_timestamp
);

COMMENT ON TABLE qua_web_kafka_element_detail IS 'Kafka元素详情表';
COMMENT ON COLUMN qua_web_kafka_element_detail.element_id IS '元素ID';
COMMENT ON COLUMN qua_web_kafka_element_detail.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_web_kafka_element_detail.topic_name IS 'topic名称';
COMMENT ON COLUMN qua_web_kafka_element_detail.partitions IS '分区数';
COMMENT ON COLUMN qua_web_kafka_element_detail.replication_factor IS '副本数';
COMMENT ON COLUMN qua_web_kafka_element_detail.first_snapshoot_version IS '首次快照版本';
COMMENT ON COLUMN qua_web_kafka_element_detail.last_snapshoot_version IS '最新快照版本';
COMMENT ON COLUMN qua_web_kafka_element_detail.create_user IS '创建人';
COMMENT ON COLUMN qua_web_kafka_element_detail.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_kafka_element_detail.update_user IS '更新人';
COMMENT ON COLUMN qua_web_kafka_element_detail.update_time IS '更新时间';


alter table qua_scan_list add kafka_brokers varchar(100);
comment on column qua_scan_list.kafka_brokers is 'Kafka broker地址';
alter table qua_scan_list add kafka_user_name varchar(50);
comment on column qua_scan_list.kafka_user_name is 'Kafka用户名';
alter table qua_scan_list add kafka_user_password varchar(50);
comment on column qua_scan_list.kafka_user_password is 'Kafka密码';
alter table qua_scan_list add keytab_file_path varchar(200);
comment on column qua_scan_list.keytab_file_path is 'keytab配置文件路径';
alter table qua_scan_list add krb5_file_path varchar(200);
comment on column qua_scan_list.krb5_file_path is 'krb5配置文件路径';
alter table qua_scan_list add principal varchar(100);
comment on column qua_scan_list.principal is 'principal';

alter table qua_wab_element add kafka_brokers varchar(100);
comment on column qua_wab_element.kafka_brokers is 'broker地址';
alter table qua_wab_element add kafka_user_name varchar(50);
comment on column qua_wab_element.kafka_user_name is 'Kafka用户名';
alter table qua_wab_element add kafka_user_password varchar(50);
comment on column qua_wab_element.kafka_user_password is 'Kafka密码';
alter table qua_wab_element add keytab_file_path varchar(200);
comment on column qua_wab_element.keytab_file_path is 'keytab配置文件路径';
alter table qua_wab_element add krb5_file_path varchar(200);
comment on column qua_wab_element.krb5_file_path is 'krb5配置文件路径';
alter table qua_wab_element add principal varchar(100);
comment on column qua_wab_element.principal is 'principal';


CREATE TABLE IF NOT EXISTS qua_scan_panwei_column
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(50) NOT NULL ,
	table_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	db_name VARCHAR(255) NOT NULL ,
	schema_name VARCHAR(255) NOT NULL ,
	table_name VARCHAR(255) NOT NULL ,
	column_name VARCHAR(255) NOT NULL ,
	column_type VARCHAR(100) NOT NULL ,
	column_length BIGINT,
	is_not_null SMALLINT DEFAULT 0,
	default_value VARCHAR(1000),
	column_comment TEXT,
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE IF NOT EXISTS qua_scan_panwei_db
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(50) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	db_name VARCHAR(100) NOT NULL ,
	schema_name VARCHAR(255) NOT NULL ,
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE IF NOT EXISTS qua_scan_panwei_table
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(50) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	db_id BIGINT NOT NULL ,
	db_name VARCHAR(255) NOT NULL ,
	schema_name VARCHAR(255) NOT NULL ,
	table_name VARCHAR(255) NOT NULL ,
	table_comment TEXT,
	tablespace_name VARCHAR(255),
	server_version VARCHAR(50),
	access_method VARCHAR(50),
	object_type VARCHAR(50) NOT NULL ,
	database_collation VARCHAR(100),
	total_rows BIGINT,
	column_count INTEGER,
	has_primary_key INTEGER,
	snapshoot_version VARCHAR(100) NOT NULL
);


COMMENT ON COLUMN qua_scan_panwei_column.id IS '主键ID';
COMMENT ON COLUMN qua_scan_panwei_column.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_panwei_column.table_id IS '表ID';
COMMENT ON COLUMN qua_scan_panwei_column.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_panwei_column.db_name IS '数据库名称';
COMMENT ON COLUMN qua_scan_panwei_column.schema_name IS '模式名称';
COMMENT ON COLUMN qua_scan_panwei_column.table_name IS '表名称';
COMMENT ON COLUMN qua_scan_panwei_column.column_name IS '列名称';
COMMENT ON COLUMN qua_scan_panwei_column.column_type IS '列数据类型';
COMMENT ON COLUMN qua_scan_panwei_column.column_length IS '列长度';
COMMENT ON COLUMN qua_scan_panwei_column.is_not_null IS '是否不可为空(1=不可为空,0=可为空)';
COMMENT ON COLUMN qua_scan_panwei_column.default_value IS '默认值';
COMMENT ON COLUMN qua_scan_panwei_column.column_comment IS '列注释';
COMMENT ON COLUMN qua_scan_panwei_column.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_scan_panwei_db.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_panwei_db.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_panwei_db.db_name IS '数据库名称';
COMMENT ON COLUMN qua_scan_panwei_db.schema_name IS '模式名称';
COMMENT ON COLUMN qua_scan_panwei_db.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_scan_panwei_table.id IS '主键ID';
COMMENT ON COLUMN qua_scan_panwei_table.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_panwei_table.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_panwei_table.db_id IS '扫描的数据库ID';
COMMENT ON COLUMN qua_scan_panwei_table.db_name IS '数据库名称';
COMMENT ON COLUMN qua_scan_panwei_table.schema_name IS '模式名称';
COMMENT ON COLUMN qua_scan_panwei_table.table_name IS '表名称';
COMMENT ON COLUMN qua_scan_panwei_table.table_comment IS '表注释';
COMMENT ON COLUMN qua_scan_panwei_table.tablespace_name IS '表空间名称';
COMMENT ON COLUMN qua_scan_panwei_table.server_version IS '磐维服务器版本';
COMMENT ON COLUMN qua_scan_panwei_table.access_method IS '表的访问方法';
COMMENT ON COLUMN qua_scan_panwei_table.object_type IS '对象类型(TABLE, VIEW等)';
COMMENT ON COLUMN qua_scan_panwei_table.database_collation IS '数据库排序规则';
COMMENT ON COLUMN qua_scan_panwei_table.total_rows IS '表中的行数(近似值)';
COMMENT ON COLUMN qua_scan_panwei_table.column_count IS '表中的列数';
COMMENT ON COLUMN qua_scan_panwei_table.has_primary_key IS '是否有主键';
COMMENT ON COLUMN qua_scan_panwei_table.snapshoot_version IS '快照版本';


CREATE INDEX IDX_PANWEI_TABLE_ID_REFERENCE_IDX ON qua_scan_panwei_column(table_id);
CREATE INDEX IDX_PANWEI_TABLE_REFERENCE_IDX ON qua_scan_panwei_column(db_name,schema_name,table_name);
CREATE INDEX UK_PANWEI_TABLE_METADATA_IDX ON qua_scan_panwei_table(db_name,schema_name,table_name);

ALTER TABLE qua_scan_list ADD db_name varchar(255);
ALTER TABLE qua_scan_list ADD schema_name varchar(255);

alter table qua_wab_element add db_name varchar(50);
comment on column qua_wab_element.db_name is '数据库名称';
alter table qua_wab_element add schema_name varchar(50);
comment on column qua_wab_element.schema_name is 'schema名称';

CREATE TABLE IF NOT EXISTS qua_web_panwei_element_detail_column
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT NOT NULL ,
	tenant_id BIGINT NOT NULL ,
	db_name VARCHAR(255) NOT NULL ,
	schema_name VARCHAR(255) NOT NULL ,
	table_name VARCHAR(255) NOT NULL ,
	column_name VARCHAR(255) NOT NULL ,
	column_name_cn VARCHAR(255),
	is_sensitive SMALLINT DEFAULT 0,
	create_time TIMESTAMP NOT NULL ,
	create_user VARCHAR(60),
	update_time TIMESTAMP NOT NULL ,
	update_user VARCHAR(60),
	is_available SMALLINT DEFAULT 1,
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	business_type BIGINT,
	sen_level_id INTEGER,
	sen_level_name VARCHAR(200),
	sen_type_id INTEGER,
	sen_type_name VARCHAR(200),
	desensitization_id INTEGER,
	is_required INTEGER DEFAULT 0,
	is_encrypted INTEGER DEFAULT 0,
	cn_desc VARCHAR(200),
	enum_value VARCHAR(200),
	mapping_fields VARCHAR(200),
	sort INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS qua_web_panwei_element_detail_db
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT NOT NULL ,
	tenant_id BIGINT NOT NULL ,
	db_name VARCHAR(255) NOT NULL ,
	schema_name VARCHAR(255) NOT NULL ,
	create_time TIMESTAMP NOT NULL ,
	create_user VARCHAR(60),
	update_time TIMESTAMP NOT NULL ,
	update_user VARCHAR(60),
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100)
);

CREATE TABLE IF NOT EXISTS qua_web_panwei_element_detail_table
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT NOT NULL ,
	tenant_id BIGINT NOT NULL ,
	db_name VARCHAR(255) NOT NULL ,
	schema_name VARCHAR(255) NOT NULL ,
	table_name VARCHAR(255) NOT NULL ,
	table_name_cn VARCHAR(255),
	table_dscribe VARCHAR(255),
	table_owner VARCHAR(255),
	key_words TEXT,
	is_sensitive SMALLINT DEFAULT 0,
	create_time TIMESTAMP NOT NULL ,
	create_user VARCHAR(60),
	update_time TIMESTAMP NOT NULL ,
	update_user VARCHAR(60),
	is_available SMALLINT DEFAULT 1,
	ext_attrs VARCHAR(1024),
	meta_source int4 DEFAULT 1,
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	ddl_last_change_time VARCHAR(50),
	data_last_change_time VARCHAR(50),
	last_viewed_time VARCHAR(50)
);

CREATE TABLE IF NOT EXISTS qua_web_panwei_task_result_column
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT NOT NULL ,
	tenant_id BIGINT NOT NULL ,
	table_id BIGINT NOT NULL ,
	db_name VARCHAR(100) NOT NULL ,
	schema_name VARCHAR(100) NOT NULL ,
	table_name VARCHAR(100) NOT NULL ,
	column_name VARCHAR(100) NOT NULL ,
	column_type VARCHAR(100) NOT NULL ,
	column_length BIGINT,
	is_not_null SMALLINT DEFAULT 0,
	default_value VARCHAR(1000),
	column_comment TEXT,
	snapshoot_version VARCHAR(100) NOT NULL ,
	bussiness_type VARCHAR(64),
	create_time TIMESTAMP NOT NULL ,
	create_user VARCHAR(60)
);

CREATE TABLE IF NOT EXISTS qua_web_panwei_task_result_db
(
	id SERIAL  PRIMARY KEY,
	task_id BIGINT NOT NULL ,
	element_id BIGINT NOT NULL ,
	tenant_id BIGINT NOT NULL ,
	db_name VARCHAR(255) NOT NULL ,
	schema_name VARCHAR(255) NOT NULL ,
	create_time TIMESTAMP NOT NULL ,
	create_user VARCHAR(60),
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE IF NOT EXISTS qua_web_panwei_task_result_table
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	db_id BIGINT NOT NULL ,
	db_name VARCHAR(255) NOT NULL ,
	schema_name VARCHAR(255) NOT NULL ,
	table_name VARCHAR(255) NOT NULL ,
	table_comment TEXT,
	tablespace_name VARCHAR(255),
	server_version VARCHAR(50),
	access_method VARCHAR(50),
	object_type VARCHAR(50) NOT NULL ,
	database_collation VARCHAR(100),
	total_rows BIGINT,
	column_count INTEGER,
	has_primary_key INTEGER,
	snapshoot_version VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);


COMMENT ON COLUMN qua_web_panwei_element_detail_column.id IS '主键ID';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.element_id IS '元素ID';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.schema_name IS 'Schema名称';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.table_name IS '表名称';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.column_name IS '字段名称';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.column_name_cn IS '字段中文名，默认为空字符串';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.is_sensitive IS '是否为敏感表，默认不是';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.is_available IS '是否可用，默认为1，可用';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.business_type IS '业务系统';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.sen_level_id IS '敏感分级ID';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.sen_level_name IS '敏感分级名称';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.sen_type_id IS '敏感分类ID';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.sen_type_name IS '敏感分级名称';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.desensitization_id IS '脱敏规则 ID';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.is_required IS '是否必填，0-否，1-是';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.is_encrypted IS '是否加密，0-否，1-是';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.cn_desc IS '中文描述';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.enum_value IS '枚举值';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.mapping_fields IS '映射字段';
COMMENT ON COLUMN qua_web_panwei_element_detail_column.sort IS '排序字段';
COMMENT ON COLUMN qua_web_panwei_element_detail_db.id IS '主键ID';
COMMENT ON COLUMN qua_web_panwei_element_detail_db.element_id IS '元素ID';
COMMENT ON COLUMN qua_web_panwei_element_detail_db.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_web_panwei_element_detail_db.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_panwei_element_detail_db.schema_name IS 'Schema名称';
COMMENT ON COLUMN qua_web_panwei_element_detail_db.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_panwei_element_detail_db.update_time IS '更新时间';
COMMENT ON COLUMN qua_web_panwei_element_detail_db.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_panwei_element_detail_db.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.id IS '主键ID';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.element_id IS '元素ID';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.schema_name IS 'Schema名称';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.table_name IS '表名称';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.table_name_cn IS '表中文名';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.table_dscribe IS '表业务描述';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.table_owner IS '表业务负责人';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.key_words IS '关键词，["a","b"]';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.is_sensitive IS '是否为敏感表，默认不是';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.is_available IS '是否可用，默认为1，可用';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.ext_attrs IS '自定义属性';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.meta_source IS '元数据来源，1-自建，2-数据建模';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.ddl_last_change_time IS 'DDL最后变更时间';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.data_last_change_time IS '最后数据变更时间';
COMMENT ON COLUMN qua_web_panwei_element_detail_table.last_viewed_time IS '最后查看时间';
COMMENT ON COLUMN qua_web_panwei_task_result_column.id IS '主键ID';
COMMENT ON COLUMN qua_web_panwei_task_result_column.element_id IS '元素ID';
COMMENT ON COLUMN qua_web_panwei_task_result_column.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_web_panwei_task_result_column.table_id IS '表ID';
COMMENT ON COLUMN qua_web_panwei_task_result_column.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_panwei_task_result_column.schema_name IS '模式名称';
COMMENT ON COLUMN qua_web_panwei_task_result_column.table_name IS '表名称';
COMMENT ON COLUMN qua_web_panwei_task_result_column.column_name IS '列名称';
COMMENT ON COLUMN qua_web_panwei_task_result_column.column_type IS '列数据类型';
COMMENT ON COLUMN qua_web_panwei_task_result_column.column_length IS '列长度';
COMMENT ON COLUMN qua_web_panwei_task_result_column.is_not_null IS '是否不可为空(1=不可为空,0=可为空)';
COMMENT ON COLUMN qua_web_panwei_task_result_column.default_value IS '默认值';
COMMENT ON COLUMN qua_web_panwei_task_result_column.column_comment IS '列注释';
COMMENT ON COLUMN qua_web_panwei_task_result_column.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_panwei_task_result_column.bussiness_type IS '业务类型';
COMMENT ON COLUMN qua_web_panwei_task_result_column.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_panwei_task_result_column.create_user IS '创建人';
COMMENT ON COLUMN qua_web_panwei_task_result_db.id IS '主键ID';
COMMENT ON COLUMN qua_web_panwei_task_result_db.task_id IS '任务ID';
COMMENT ON COLUMN qua_web_panwei_task_result_db.element_id IS '元素ID';
COMMENT ON COLUMN qua_web_panwei_task_result_db.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_web_panwei_task_result_db.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_panwei_task_result_db.schema_name IS 'Schema名称';
COMMENT ON COLUMN qua_web_panwei_task_result_db.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_panwei_task_result_db.create_user IS '创建人';
COMMENT ON COLUMN qua_web_panwei_task_result_db.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_panwei_task_result_table.id IS '主键ID';
COMMENT ON COLUMN qua_web_panwei_task_result_table.element_id IS '元素ID';
COMMENT ON COLUMN qua_web_panwei_task_result_table.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_panwei_task_result_table.create_user IS '创建人';
COMMENT ON COLUMN qua_web_panwei_task_result_table.db_id IS '扫描的数据库ID';
COMMENT ON COLUMN qua_web_panwei_task_result_table.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_panwei_task_result_table.schema_name IS '模式名称';
COMMENT ON COLUMN qua_web_panwei_task_result_table.table_name IS '表名称';
COMMENT ON COLUMN qua_web_panwei_task_result_table.table_comment IS '表注释';
COMMENT ON COLUMN qua_web_panwei_task_result_table.tablespace_name IS '表空间名称';
COMMENT ON COLUMN qua_web_panwei_task_result_table.server_version IS 'PostgreSQL服务器版本';
COMMENT ON COLUMN qua_web_panwei_task_result_table.access_method IS '表的访问方法';
COMMENT ON COLUMN qua_web_panwei_task_result_table.object_type IS '对象类型(TABLE, VIEW等)';
COMMENT ON COLUMN qua_web_panwei_task_result_table.database_collation IS '数据库排序规则';
COMMENT ON COLUMN qua_web_panwei_task_result_table.total_rows IS '表中的行数(近似值)';
COMMENT ON COLUMN qua_web_panwei_task_result_table.column_count IS '表中的列数';
COMMENT ON COLUMN qua_web_panwei_task_result_table.has_primary_key IS '是否有主键';
COMMENT ON COLUMN qua_web_panwei_task_result_table.snapshoot_version IS '快照版本';


CREATE INDEX QUA_WEB_PANWEI_ELEMENT_DETAIL_COLUMN_ELEMENT_ID ON qua_web_panwei_element_detail_column(element_id);
CREATE INDEX QUA_WEB_PANWEI_ELEMENT_DETAIL_COLUMN_TENANT_ID ON qua_web_panwei_element_detail_column(tenant_id);
CREATE INDEX QUA_WEB_PANWEI_ELEMENT_DETAIL_COLUMN_EDTC ON qua_web_panwei_element_detail_column(element_id,db_name,table_name,column_name);
CREATE INDEX QUA_WEB_PANWEI_ELEMENT_DETAIL_DB_ELEMENT_ID ON qua_web_panwei_element_detail_db(element_id);
CREATE INDEX QUA_WEB_PANWEI_ELEMENT_DETAIL_DB_TENANT_ID ON qua_web_panwei_element_detail_db(tenant_id);
CREATE INDEX QUA_WEB_PANWEI_ELEMENT_DETAIL_TABLE_ELEMENT_ID ON qua_web_panwei_element_detail_table(element_id);
CREATE INDEX QUA_WEB_PANWEI_ELEMENT_DETAIL_TABLE_TENANT_ID ON qua_web_panwei_element_detail_table(tenant_id);
CREATE INDEX QUA_WEB_PANWEI_ELEMENT_DETAIL_TABLE_TDT ON qua_web_panwei_element_detail_table(tenant_id,db_name,table_name);
CREATE INDEX QUA_WEB_PANWEI_TASK_RESULT_COLUMN_ELEMENT_ID ON qua_web_panwei_task_result_column(element_id);
CREATE INDEX QUA_WEB_PANWEI_TASK_RESULT_COLUMN_TENANT_ID ON qua_web_panwei_task_result_column(tenant_id);
CREATE INDEX QUA_WEB_PANWEI_TASK_RESULT_COLUMN_EDTCS ON qua_web_panwei_task_result_column(element_id,db_name,table_name,column_name,snapshoot_version);
CREATE INDEX QUA_WEB_PANWEI_TASK_RESULT_DB_ELEMENT_ID ON qua_web_panwei_task_result_db(element_id);
CREATE INDEX QUA_WEB_PANWEI_TASK_RESULT_DB_TASK_ID ON qua_web_panwei_task_result_db(task_id);
CREATE INDEX QUA_WEB_PANWEI_TASK_RESULT_DB_TENANT_ID ON qua_web_panwei_task_result_db(tenant_id);
CREATE INDEX QUA_WEB_PANWEI_TASK_RESULT_DB_EDS ON qua_web_panwei_task_result_db(element_id,db_name,snapshoot_version);
CREATE INDEX QUA_WEB_PANWEI_TASK_RESULT_TABLE_ELEMENT_ID ON qua_web_panwei_task_result_table(element_id);
CREATE INDEX QUA_WEB_PANWEI_TASK_RESULT_TABLE_TENANT_ID ON qua_web_panwei_task_result_table(tenant_id);
CREATE INDEX QUA_WEB_PANWEI_TASK_RESULT_TABLE_EDTS ON qua_web_panwei_task_result_table(element_id,db_name,table_name,snapshoot_version);



alter table qua_web_kafka_element_detail add topic_name_cn varchar(50);
comment on column qua_web_kafka_element_detail.topic_name_cn is 'topic 中文名';

alter table qua_web_kafka_element_detail add topic_describe varchar(100);
comment on column qua_web_kafka_element_detail.topic_describe is '描述';

alter table qua_web_kafka_element_detail add topic_owner varchar(50);
comment on column qua_web_kafka_element_detail.topic_owner is '责任人';

alter table qua_web_kafka_element_detail add is_sensitive smallint default 0;
comment on column qua_web_kafka_element_detail.is_sensitive is '是否为敏感，0非，1是，默认不是';

alter table qua_web_kafka_element_detail add meta_source smallint default 1;
comment on column qua_web_kafka_element_detail.meta_source is '元数据来源，1-自建，2-数据建模';

alter table qua_web_kafka_element_detail add key_words varchar(2048) ;
comment on column qua_web_kafka_element_detail.key_words is '关键词，["a","b"]';
