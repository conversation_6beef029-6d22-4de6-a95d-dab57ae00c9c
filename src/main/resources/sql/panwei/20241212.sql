ALTER TABLE data_dictionary_item ALTER COLUMN dict_id TYPE bigint;
ALTER TABLE data_dictionary_item ALTER COLUMN dict_id DROP NOT NULL;
COMMENT ON COLUMN data_dictionary_item.dict_id IS '字典ID';

ALTER TABLE data_dictionary_item ALTER COLUMN category_id TYPE bigint;
ALTER TABLE data_dictionary_item ALTER COLUMN category_id DROP NOT NULL;
COMMENT ON COLUMN data_dictionary_item.category_id IS '字典分类ID';

ALTER TABLE data_dictionary_item ALTER COLUMN sort TYPE int;
ALTER TABLE data_dictionary_item ALTER COLUMN sort SET DEFAULT 0;
COMMENT ON COLUMN data_dictionary_item.sort IS '排序字段';

-- 创建索引
CREATE INDEX qua_asset_table_data_statistics_element_id_idx ON qua_asset_table_data_statistics USING btree (element_id, db_name, table_name, datasource_type);

