DROP TABLE IF EXISTS QRTZ_FIRED_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_PAUSED_TRIGGER_GRPS;
DROP TABLE IF EXISTS QRTZ_SCHEDULER_STATE;
DROP TABLE IF EXISTS QRTZ_LOCKS;
DROP TABLE IF EXISTS QRTZ_SIMPLE_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_SIMPROP_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_CRON_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_BLOB_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_JOB_DETAILS;
DROP TABLE IF EXISTS QRTZ_CALENDARS;

DROP TABLE IF EXISTS t_ds_access_token;
DROP TABLE IF EXISTS t_ds_alert;
DROP TABLE IF EXISTS t_ds_alert_plugin_instance;
DROP TABLE IF EXISTS t_ds_alert_send_status;
DROP TABLE IF EXISTS t_ds_alertgroup;
DROP TABLE IF EXISTS t_ds_audit_log;
DROP TABLE IF EXISTS t_ds_cluster;
DROP TABLE IF EXISTS t_ds_command;
DROP TABLE IF EXISTS t_ds_datasource;
DROP TABLE IF EXISTS t_ds_datasource_kbs;
DROP TABLE IF EXISTS t_ds_dq_comparison_type;
DROP TABLE IF EXISTS t_ds_dq_execute_result;
DROP TABLE IF EXISTS t_ds_dq_rule;
DROP TABLE IF EXISTS t_ds_dq_rule_execute_sql;
DROP TABLE IF EXISTS t_ds_dq_rule_input_entry;
DROP TABLE IF EXISTS t_ds_dq_task_statistics_value;
DROP TABLE IF EXISTS t_ds_environment;
DROP TABLE IF EXISTS t_ds_environment_worker_group_relation;
DROP TABLE IF EXISTS t_ds_error_command;
DROP TABLE IF EXISTS t_ds_fav_task;
DROP TABLE IF EXISTS t_ds_k8s;
DROP TABLE IF EXISTS t_ds_k8s_namespace;
DROP TABLE IF EXISTS t_ds_plugin_define;
DROP TABLE IF EXISTS t_ds_process_definition;
DROP TABLE IF EXISTS t_ds_process_definition_lock;
DROP TABLE IF EXISTS t_ds_process_definition_log;
DROP TABLE IF EXISTS t_ds_process_instance;
DROP TABLE IF EXISTS t_ds_process_task_relation;
DROP TABLE IF EXISTS t_ds_process_task_relation_log;
DROP TABLE IF EXISTS t_ds_project;
DROP TABLE IF EXISTS t_ds_queue;
DROP TABLE IF EXISTS t_ds_relation_datasource_user;
DROP TABLE IF EXISTS t_ds_relation_namespace_user;
DROP TABLE IF EXISTS t_ds_relation_process_instance;
DROP TABLE IF EXISTS t_ds_relation_project_user;
DROP TABLE IF EXISTS t_ds_relation_resources_user;
DROP TABLE IF EXISTS t_ds_relation_rule_execute_sql;
DROP TABLE IF EXISTS t_ds_relation_rule_input_entry;
DROP TABLE IF EXISTS t_ds_relation_udfs_user;
DROP TABLE IF EXISTS t_ds_resources;
DROP TABLE IF EXISTS t_ds_schedules;
DROP TABLE IF EXISTS t_ds_session;
DROP TABLE IF EXISTS t_ds_task_definition;
DROP TABLE IF EXISTS t_ds_task_definition_log;
DROP TABLE IF EXISTS t_ds_task_group;
DROP TABLE IF EXISTS t_ds_task_group_queue;
DROP TABLE IF EXISTS t_ds_task_instance;
DROP TABLE IF EXISTS t_ds_tenant;
DROP TABLE IF EXISTS t_ds_udfs;
DROP TABLE IF EXISTS t_ds_user;
DROP TABLE IF EXISTS t_ds_version;
DROP TABLE IF EXISTS t_ds_worker_group;

CREATE TABLE QRTZ_JOB_DETAILS (
  SCHED_NAME character varying(120) NOT NULL,
  JOB_NAME character varying(200) NOT NULL,
  JOB_GROUP character varying(200) NOT NULL,
  DESCRIPTION character varying(250) NULL,
  JOB_CLASS_NAME character varying(250) NOT NULL,
  IS_DURABLE boolean NOT NULL,
  IS_NONCONCURRENT boolean NOT NULL,
  IS_UPDATE_DATA boolean NOT NULL,
  REQUESTS_RECOVERY boolean NOT NULL,
  JOB_DATA bytea NULL
);

alter table QRTZ_JOB_DETAILS add primary key(SCHED_NAME,JOB_NAME,JOB_GROUP);

CREATE TABLE QRTZ_TRIGGERS (
  SCHED_NAME character varying(120) NOT NULL,
  TRIGGER_NAME character varying(200) NOT NULL,
  TRIGGER_GROUP character varying(200) NOT NULL,
  JOB_NAME character varying(200) NOT NULL,
  JOB_GROUP character varying(200) NOT NULL,
  DESCRIPTION character varying(250) NULL,
  NEXT_FIRE_TIME BIGINT NULL,
  PREV_FIRE_TIME BIGINT NULL,
  PRIORITY INTEGER NULL,
  TRIGGER_STATE character varying(16) NOT NULL,
  TRIGGER_TYPE character varying(8) NOT NULL,
  START_TIME BIGINT NOT NULL,
  END_TIME BIGINT NULL,
  CALENDAR_NAME character varying(200) NULL,
  MISFIRE_INSTR SMALLINT NULL,
  JOB_DATA bytea NULL
) ;

alter table QRTZ_TRIGGERS add primary key(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP);

CREATE TABLE QRTZ_SIMPLE_TRIGGERS (
    SCHED_NAME character varying(120) NOT NULL,
    TRIGGER_NAME character varying(200) NOT NULL,
    TRIGGER_GROUP character varying(200) NOT NULL,
    REPEAT_COUNT BIGINT NOT NULL,
    REPEAT_INTERVAL BIGINT NOT NULL,
    TIMES_TRIGGERED BIGINT NOT NULL
) ;

alter table QRTZ_SIMPLE_TRIGGERS add primary key(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP);

CREATE TABLE QRTZ_CRON_TRIGGERS (
    SCHED_NAME character varying(120) NOT NULL,
    TRIGGER_NAME character varying(200) NOT NULL,
    TRIGGER_GROUP character varying(200) NOT NULL,
    CRON_EXPRESSION character varying(120) NOT NULL,
    TIME_ZONE_ID character varying(80)
) ;

alter table QRTZ_CRON_TRIGGERS add primary key(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP);

CREATE TABLE QRTZ_SIMPROP_TRIGGERS (
    SCHED_NAME character varying(120) NOT NULL,
    TRIGGER_NAME character varying(200) NOT NULL,
    TRIGGER_GROUP character varying(200) NOT NULL,
    STR_PROP_1 character varying(512) NULL,
    STR_PROP_2 character varying(512) NULL,
    STR_PROP_3 character varying(512) NULL,
    INT_PROP_1 INT NULL,
    INT_PROP_2 INT NULL,
    LONG_PROP_1 BIGINT NULL,
    LONG_PROP_2 BIGINT NULL,
    DEC_PROP_1 NUMERIC(13,4) NULL,
    DEC_PROP_2 NUMERIC(13,4) NULL,
    BOOL_PROP_1 boolean NULL,
    BOOL_PROP_2 boolean NULL
) ;

alter table QRTZ_SIMPROP_TRIGGERS add primary key(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP);

CREATE TABLE QRTZ_BLOB_TRIGGERS (
    SCHED_NAME character varying(120) NOT NULL,
    TRIGGER_NAME character varying(200) NOT NULL,
    TRIGGER_GROUP character varying(200) NOT NULL,
    BLOB_DATA bytea NULL
) ;

alter table QRTZ_BLOB_TRIGGERS add primary key(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP);

CREATE TABLE QRTZ_CALENDARS (
    SCHED_NAME character varying(120) NOT NULL,
    CALENDAR_NAME character varying(200) NOT NULL,
    CALENDAR bytea NOT NULL
) ;

alter table QRTZ_CALENDARS add primary key(SCHED_NAME,CALENDAR_NAME);

CREATE TABLE QRTZ_PAUSED_TRIGGER_GRPS (
    SCHED_NAME character varying(120) NOT NULL,
    TRIGGER_GROUP character varying(200) NOT NULL
) ;

alter table QRTZ_PAUSED_TRIGGER_GRPS add primary key(SCHED_NAME,TRIGGER_GROUP);

CREATE TABLE QRTZ_FIRED_TRIGGERS (
    SCHED_NAME character varying(120) NOT NULL,
    ENTRY_ID character varying(200) NOT NULL,
    TRIGGER_NAME character varying(200) NOT NULL,
    TRIGGER_GROUP character varying(200) NOT NULL,
    INSTANCE_NAME character varying(200) NOT NULL,
    FIRED_TIME BIGINT NOT NULL,
    SCHED_TIME BIGINT NOT NULL,
    PRIORITY INTEGER NOT NULL,
    STATE character varying(16) NOT NULL,
    JOB_NAME character varying(200) NULL,
    JOB_GROUP character varying(200) NULL,
    IS_NONCONCURRENT boolean NULL,
    REQUESTS_RECOVERY boolean NULL
) ;

alter table QRTZ_FIRED_TRIGGERS add primary key(SCHED_NAME,ENTRY_ID);

CREATE TABLE QRTZ_SCHEDULER_STATE (
    SCHED_NAME character varying(120) NOT NULL,
    INSTANCE_NAME character varying(200) NOT NULL,
    LAST_CHECKIN_TIME BIGINT NOT NULL,
    CHECKIN_INTERVAL BIGINT NOT NULL
) ;

alter table QRTZ_SCHEDULER_STATE add primary key(SCHED_NAME,INSTANCE_NAME);

CREATE TABLE QRTZ_LOCKS (
    SCHED_NAME character varying(120) NOT NULL,
    LOCK_NAME character varying(40) NOT NULL
) ;

alter table QRTZ_LOCKS add primary key(SCHED_NAME,LOCK_NAME);

CREATE INDEX IDX_QRTZ_J_REQ_RECOVERY ON QRTZ_JOB_DETAILS(SCHED_NAME,REQUESTS_RECOVERY);
CREATE INDEX IDX_QRTZ_J_GRP ON QRTZ_JOB_DETAILS(SCHED_NAME,JOB_GROUP);

CREATE INDEX IDX_QRTZ_T_J ON QRTZ_TRIGGERS(SCHED_NAME,JOB_NAME,JOB_GROUP);
CREATE INDEX IDX_QRTZ_T_JG ON QRTZ_TRIGGERS(SCHED_NAME,JOB_GROUP);
CREATE INDEX IDX_QRTZ_T_C ON QRTZ_TRIGGERS(SCHED_NAME,CALENDAR_NAME);
CREATE INDEX IDX_QRTZ_T_G ON QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_GROUP);
CREATE INDEX IDX_QRTZ_T_STATE ON QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_STATE);
CREATE INDEX IDX_QRTZ_T_N_STATE ON QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP,TRIGGER_STATE);
CREATE INDEX IDX_QRTZ_T_N_G_STATE ON QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_GROUP,TRIGGER_STATE);
CREATE INDEX IDX_QRTZ_T_NEXT_FIRE_TIME ON QRTZ_TRIGGERS(SCHED_NAME,NEXT_FIRE_TIME);
CREATE INDEX IDX_QRTZ_T_NFT_ST ON QRTZ_TRIGGERS(SCHED_NAME,TRIGGER_STATE,NEXT_FIRE_TIME);
CREATE INDEX IDX_QRTZ_T_NFT_MISFIRE ON QRTZ_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME);
CREATE INDEX IDX_QRTZ_T_NFT_ST_MISFIRE ON QRTZ_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME,TRIGGER_STATE);
CREATE INDEX IDX_QRTZ_T_NFT_ST_MISFIRE_GRP ON QRTZ_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME,TRIGGER_GROUP,TRIGGER_STATE);

CREATE INDEX IDX_QRTZ_FT_TRIG_INST_NAME ON QRTZ_FIRED_TRIGGERS(SCHED_NAME,INSTANCE_NAME);
CREATE INDEX IDX_QRTZ_FT_INST_JOB_REQ_RCVRY ON QRTZ_FIRED_TRIGGERS(SCHED_NAME,INSTANCE_NAME,REQUESTS_RECOVERY);
CREATE INDEX IDX_QRTZ_FT_J_G ON QRTZ_FIRED_TRIGGERS(SCHED_NAME,JOB_NAME,JOB_GROUP);
CREATE INDEX IDX_QRTZ_FT_JG ON QRTZ_FIRED_TRIGGERS(SCHED_NAME,JOB_GROUP);
CREATE INDEX IDX_QRTZ_FT_T_G ON QRTZ_FIRED_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP);
CREATE INDEX IDX_QRTZ_FT_TG ON QRTZ_FIRED_TRIGGERS(SCHED_NAME,TRIGGER_GROUP);


CREATE TABLE t_ds_access_token
(
	id SERIAL  PRIMARY KEY,
	user_id INTEGER,
	token VARCHAR(64),
	expire_time TIMESTAMP,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_alert
(
	id SERIAL  PRIMARY KEY,
	title VARCHAR(64),
	sign CHARACTER(40) DEFAULT '',
	content TEXT,
	alert_status SMALLINT DEFAULT 0,
	warning_type SMALLINT DEFAULT 2,
	log TEXT,
	alertgroup_id INTEGER,
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	project_code BIGINT,
	process_definition_code BIGINT,
	process_instance_id INTEGER,
	alert_type INTEGER
);

CREATE TABLE t_ds_alert_plugin_instance
(
	id SERIAL  PRIMARY KEY,
	plugin_define_id INTEGER NOT NULL ,
	plugin_instance_params TEXT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	instance_name VARCHAR(200)
);

CREATE TABLE t_ds_alert_send_status
(
	id SERIAL  PRIMARY KEY,
	alert_id INTEGER NOT NULL ,
	alert_plugin_instance_id INTEGER NOT NULL ,
	send_status SMALLINT DEFAULT 0,
	log TEXT,
	create_time TIMESTAMP
);

CREATE TABLE t_ds_alertgroup
(
	id SERIAL  PRIMARY KEY,
	alert_instance_ids VARCHAR(255),
	create_user_id INTEGER,
	group_name VARCHAR(255),
	description VARCHAR(255),
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_audit_log
(
	id SERIAL  PRIMARY KEY,
	user_id INTEGER NOT NULL ,
	resource_type INTEGER NOT NULL ,
	operation INTEGER NOT NULL ,
	time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	resource_id INTEGER,
	resource_name VARCHAR(255)
);

CREATE TABLE t_ds_cluster
(
	id SERIAL  PRIMARY KEY,
	code BIGINT,
	name VARCHAR(100) NOT NULL ,
	config TEXT,
	description TEXT,
	operator INTEGER,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE t_ds_command
(
	id SERIAL  PRIMARY KEY,
	command_type SMALLINT,
	process_definition_code BIGINT NOT NULL ,
	process_definition_version INTEGER DEFAULT 0,
	process_instance_id INTEGER DEFAULT 0,
	command_param TEXT,
	task_depend_type SMALLINT,
	failure_strategy SMALLINT DEFAULT 0,
	warning_type SMALLINT DEFAULT 0,
	warning_group_id INTEGER,
	schedule_time TIMESTAMP,
	start_time TIMESTAMP,
	executor_id INTEGER,
	update_time TIMESTAMP,
	process_instance_priority INTEGER DEFAULT 2,
	worker_group VARCHAR(64),
	environment_code BIGINT DEFAULT -1,
	dry_run SMALLINT DEFAULT 0
);

CREATE TABLE t_ds_datasource
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(64) NOT NULL ,
	note VARCHAR(255),
	type SMALLINT NOT NULL ,
	user_id INTEGER NOT NULL ,
	connection_params TEXT NOT NULL ,
	create_time TIMESTAMP NOT NULL ,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_datasource_kbs
(
	id SERIAL  PRIMARY KEY,
	datasource_id INTEGER NOT NULL ,
	kbs_version VARCHAR(255),
	kbs_principal VARCHAR(255),
	kbs_keytab_origin_file_name VARCHAR(255),
	kbs_keytab_file_path VARCHAR(255),
	kbs_krb_origin_file_name VARCHAR(255),
	kbs_krb_file_path VARCHAR(255),
	kbs_jaas_origin_file_name VARCHAR(255),
	kbs_jaas_file_path VARCHAR(255)
);

CREATE TABLE t_ds_dq_comparison_type
(
	id SERIAL  PRIMARY KEY,
	type VARCHAR(100) NOT NULL ,
	execute_sql TEXT,
	output_table VARCHAR(100),
	name VARCHAR(100),
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	is_inner_source SMALLINT DEFAULT 0
);

CREATE TABLE t_ds_dq_execute_result
(
	id SERIAL  PRIMARY KEY,
	process_definition_id INTEGER,
	process_instance_id INTEGER,
	task_instance_id INTEGER,
	rule_type INTEGER,
	rule_name VARCHAR(255),
	statistics_value DOUBLE PRECISION,
	comparison_value DOUBLE PRECISION,
	check_type INTEGER,
	threshold DOUBLE PRECISION,
	operator INTEGER,
	failure_strategy INTEGER,
	state INTEGER,
	user_id INTEGER,
	comparison_type INTEGER,
	error_output_path TEXT,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_dq_rule
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(100),
	type INTEGER,
	user_id INTEGER,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_dq_rule_execute_sql
(
	id SERIAL  PRIMARY KEY,
	index INTEGER,
	sql TEXT,
	table_alias VARCHAR(255),
	type INTEGER,
	is_error_output_sql SMALLINT DEFAULT 0,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_dq_rule_input_entry
(
	id SERIAL  PRIMARY KEY,
	field VARCHAR(255),
	type VARCHAR(255),
	title VARCHAR(255),
	value VARCHAR(255),
	options TEXT,
	placeholder VARCHAR(255),
	option_source_type INTEGER,
	value_type INTEGER,
	input_type INTEGER,
	is_show SMALLINT DEFAULT 1,
	can_edit SMALLINT DEFAULT 1,
	is_emit SMALLINT DEFAULT 0,
	is_validate SMALLINT DEFAULT 1,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_dq_task_statistics_value
(
	id SERIAL  PRIMARY KEY,
	process_definition_id INTEGER,
	task_instance_id INTEGER,
	rule_id INTEGER NOT NULL ,
	unique_code VARCHAR(255),
	statistics_name VARCHAR(255),
	statistics_value DOUBLE PRECISION,
	data_time TIMESTAMP,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_environment
(
	id SERIAL  PRIMARY KEY,
	code BIGINT,
	name VARCHAR(100) NOT NULL ,
	config TEXT,
	description TEXT,
	operator INTEGER,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE t_ds_environment_worker_group_relation
(
	id SERIAL  PRIMARY KEY,
	environment_code BIGINT NOT NULL ,
	worker_group VARCHAR(255) NOT NULL ,
	operator INTEGER,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE t_ds_error_command
(
	id INTEGER PRIMARY KEY,
	command_type SMALLINT,
	executor_id INTEGER,
	process_definition_code BIGINT NOT NULL ,
	process_definition_version INTEGER DEFAULT 0,
	process_instance_id INTEGER DEFAULT 0,
	command_param TEXT,
	task_depend_type SMALLINT,
	failure_strategy SMALLINT DEFAULT 0,
	warning_type SMALLINT DEFAULT 0,
	warning_group_id INTEGER,
	schedule_time TIMESTAMP,
	start_time TIMESTAMP,
	update_time TIMESTAMP,
	process_instance_priority INTEGER DEFAULT 2,
	worker_group VARCHAR(64),
	environment_code BIGINT DEFAULT -1,
	message TEXT,
	dry_run SMALLINT DEFAULT 0
);

CREATE TABLE t_ds_fav_task
(
	id SERIAL  PRIMARY KEY,
	task_name VARCHAR(64) NOT NULL ,
	user_id INTEGER NOT NULL
);

CREATE TABLE t_ds_k8s
(
	id SERIAL  PRIMARY KEY,
	k8s_name VARCHAR(100),
	k8s_config TEXT,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_k8s_namespace
(
	id SERIAL  PRIMARY KEY,
	code BIGINT DEFAULT 0,
	limits_memory INTEGER,
	namespace VARCHAR(100),
	user_id INTEGER,
	pod_replicas INTEGER,
	pod_request_cpu NUMERIC,
	pod_request_memory INTEGER,
	limits_cpu NUMERIC,
	cluster_code BIGINT DEFAULT 0,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_plugin_define
(
	id SERIAL  PRIMARY KEY,
	plugin_name VARCHAR(100) NOT NULL ,
	plugin_type VARCHAR(100) NOT NULL ,
	plugin_params TEXT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE t_ds_process_definition
(
	id SERIAL  PRIMARY KEY,
	code BIGINT ,
	name VARCHAR(255),
	version INTEGER DEFAULT 0,
	description TEXT,
	project_code BIGINT NOT NULL ,
	release_state SMALLINT,
	user_id INTEGER,
	global_params TEXT,
	flag SMALLINT,
	locations TEXT,
	warning_group_id INTEGER,
	timeout INTEGER DEFAULT 0,
	tenant_id INTEGER DEFAULT -1,
	execution_type SMALLINT DEFAULT 0,
	create_time TIMESTAMP NOT NULL ,
	update_time TIMESTAMP NOT NULL,
	CONSTRAINT process_definition_unique UNIQUE (name, project_code)
);

CREATE TABLE t_ds_process_definition_lock
(
	id SERIAL  PRIMARY KEY,
	process_definition_code BIGINT NOT NULL ,
	user_id INTEGER,
	create_time TIMESTAMP NOT NULL
);

CREATE TABLE t_ds_process_definition_log
(
	id SERIAL  PRIMARY KEY,
	code BIGINT NOT NULL ,
	name VARCHAR(200),
	version INTEGER DEFAULT 0,
	description TEXT,
	project_code BIGINT NOT NULL ,
	release_state SMALLINT,
	user_id INTEGER,
	global_params TEXT,
	flag SMALLINT,
	locations TEXT,
	warning_group_id INTEGER,
	timeout INTEGER DEFAULT 0,
	tenant_id INTEGER DEFAULT -1,
	execution_type SMALLINT DEFAULT 0,
	operator INTEGER,
	operate_time TIMESTAMP,
	create_time TIMESTAMP NOT NULL ,
	update_time TIMESTAMP NOT NULL
);

CREATE TABLE t_ds_process_instance
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(255),
	process_definition_code BIGINT NOT NULL ,
	process_definition_version INTEGER DEFAULT 0,
	state SMALLINT,
	state_history TEXT,
	recovery SMALLINT,
	start_time TIMESTAMP,
	end_time TIMESTAMP,
	run_times INTEGER,
	host VARCHAR(135),
	command_type SMALLINT,
	command_param TEXT,
	task_depend_type SMALLINT,
	max_try_times SMALLINT DEFAULT 0,
	failure_strategy SMALLINT DEFAULT 0,
	warning_type SMALLINT DEFAULT 0,
	warning_group_id INTEGER,
	schedule_time TIMESTAMP,
	command_start_time TIMESTAMP,
	global_params TEXT,
	flag SMALLINT DEFAULT 1,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	is_sub_process INTEGER DEFAULT 0,
	executor_id INTEGER NOT NULL ,
	history_cmd TEXT,
	process_instance_priority INTEGER DEFAULT 2,
	worker_group VARCHAR(64),
	environment_code BIGINT DEFAULT -1,
	timeout INTEGER DEFAULT 0,
	tenant_id INTEGER DEFAULT -1,
	var_pool TEXT,
	dry_run SMALLINT DEFAULT 0,
	next_process_instance_id INTEGER DEFAULT 0,
	restart_time TIMESTAMP
);

CREATE TABLE t_ds_process_task_relation
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(200),
	project_code BIGINT NOT NULL ,
	process_definition_code BIGINT NOT NULL ,
	process_definition_version INTEGER NOT NULL ,
	pre_task_code BIGINT NOT NULL ,
	pre_task_version INTEGER NOT NULL ,
	post_task_code BIGINT NOT NULL ,
	post_task_version INTEGER NOT NULL ,
	condition_type SMALLINT,
	condition_params TEXT,
	create_time TIMESTAMP NOT NULL ,
	update_time TIMESTAMP NOT NULL
);

CREATE TABLE t_ds_process_task_relation_log
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(200),
	project_code BIGINT NOT NULL ,
	process_definition_code BIGINT NOT NULL ,
	process_definition_version INTEGER NOT NULL ,
	pre_task_code BIGINT NOT NULL ,
	pre_task_version INTEGER NOT NULL ,
	post_task_code BIGINT NOT NULL ,
	post_task_version INTEGER NOT NULL ,
	condition_type SMALLINT,
	condition_params TEXT,
	operator INTEGER,
	operate_time TIMESTAMP,
	create_time TIMESTAMP NOT NULL ,
	update_time TIMESTAMP NOT NULL
);

CREATE TABLE t_ds_project
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(100),
	code BIGINT NOT NULL ,
	description VARCHAR(255),
	user_id INTEGER,
	flag SMALLINT DEFAULT 1,
	create_time TIMESTAMP NOT NULL ,
	update_time TIMESTAMP,
	is_fuse_modeling INTEGER DEFAULT 0
);

CREATE TABLE t_ds_queue
(
	id SERIAL  PRIMARY KEY,
	queue_name VARCHAR(64),
	queue VARCHAR(64),
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_relation_datasource_user
(
	id SERIAL  PRIMARY KEY,
	user_id INTEGER NOT NULL ,
	datasource_id INTEGER,
	perm INTEGER DEFAULT 1,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_relation_namespace_user
(
	id SERIAL  PRIMARY KEY,
	user_id INTEGER NOT NULL ,
	namespace_id INTEGER,
	perm INTEGER DEFAULT 1,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_relation_process_instance
(
	id SERIAL  PRIMARY KEY,
	parent_process_instance_id INTEGER,
	parent_task_instance_id INTEGER,
	process_instance_id INTEGER
);

CREATE TABLE t_ds_relation_project_user
(
	id SERIAL  PRIMARY KEY,
	user_id INTEGER NOT NULL ,
	project_id INTEGER,
	perm INTEGER DEFAULT 1,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_relation_resources_user
(
	id SERIAL  PRIMARY KEY,
	user_id INTEGER NOT NULL ,
	resources_id INTEGER,
	perm INTEGER DEFAULT 1,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_relation_rule_execute_sql
(
	id SERIAL  PRIMARY KEY,
	rule_id INTEGER,
	execute_sql_id INTEGER,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_relation_rule_input_entry
(
	id SERIAL  PRIMARY KEY,
	rule_id INTEGER,
	rule_input_entry_id INTEGER,
	values_map TEXT,
	index INTEGER,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_relation_udfs_user
(
	id SERIAL  PRIMARY KEY,
	user_id INTEGER NOT NULL ,
	udf_id INTEGER,
	perm INTEGER DEFAULT 1,
	create_time TIMESTAMP,
	update_time TIMESTAMP
);

CREATE TABLE t_ds_resources
(
	id SERIAL  PRIMARY KEY,
	alias VARCHAR(64),
	file_name VARCHAR(64),
	description VARCHAR(255),
	user_id INTEGER,
	type SMALLINT,
	size BIGINT,
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	pid INTEGER,
	full_name VARCHAR(128),
	is_directory SMALLINT
);

CREATE TABLE t_ds_schedules
(
	id SERIAL  PRIMARY KEY,
	process_definition_code BIGINT NOT NULL ,
	start_time TIMESTAMP NOT NULL ,
	end_time TIMESTAMP NOT NULL ,
	timezone_id VARCHAR(40),
	crontab VARCHAR(255) NOT NULL ,
	failure_strategy SMALLINT NOT NULL ,
	user_id INTEGER NOT NULL ,
	release_state SMALLINT NOT NULL ,
	warning_type SMALLINT NOT NULL ,
	warning_group_id INTEGER,
	process_instance_priority INTEGER DEFAULT 2,
	worker_group VARCHAR(64) DEFAULT '',
	environment_code BIGINT DEFAULT -1,
	create_time TIMESTAMP NOT NULL ,
	update_time TIMESTAMP NOT NULL
);

CREATE TABLE t_ds_session
(
	id VARCHAR PRIMARY KEY,
	user_id INTEGER,
	ip VARCHAR(45),
	last_login_time TIMESTAMP
);

CREATE TABLE t_ds_task_definition
(
	id SERIAL  PRIMARY KEY,
	code BIGINT ,
	name VARCHAR(200),
	version INTEGER DEFAULT 0,
	description TEXT,
	project_code BIGINT NOT NULL ,
	user_id INTEGER,
	task_type VARCHAR(50) NOT NULL ,
	task_execute_type INTEGER DEFAULT 0,
	task_params TEXT,
	flag SMALLINT,
	task_priority SMALLINT DEFAULT 2,
	worker_group VARCHAR(200),
	environment_code BIGINT DEFAULT -1,
	fail_retry_times INTEGER,
	fail_retry_interval INTEGER,
	timeout_flag SMALLINT DEFAULT 0,
	timeout_notify_strategy SMALLINT,
	timeout INTEGER DEFAULT 0,
	delay_time INTEGER DEFAULT 0,
	resource_ids TEXT,
	task_group_id INTEGER,
	task_group_priority SMALLINT DEFAULT 0,
	cpu_quota INTEGER DEFAULT -1,
	memory_max INTEGER DEFAULT -1,
	create_time TIMESTAMP NOT NULL ,
	update_time TIMESTAMP NOT NULL
);

CREATE TABLE t_ds_task_definition_log
(
	id SERIAL  PRIMARY KEY,
	code BIGINT NOT NULL ,
	name VARCHAR(200),
	version INTEGER DEFAULT 0,
	description TEXT,
	project_code BIGINT NOT NULL ,
	user_id INTEGER,
	task_type VARCHAR(50) NOT NULL ,
	task_execute_type INTEGER DEFAULT 0,
	task_params TEXT,
	flag SMALLINT,
	task_priority SMALLINT DEFAULT 2,
	worker_group VARCHAR(200),
	environment_code BIGINT DEFAULT -1,
	fail_retry_times INTEGER,
	fail_retry_interval INTEGER,
	timeout_flag SMALLINT DEFAULT 0,
	timeout_notify_strategy SMALLINT,
	timeout INTEGER DEFAULT 0,
	delay_time INTEGER DEFAULT 0,
	resource_ids TEXT,
	operator INTEGER,
	task_group_id INTEGER,
	task_group_priority SMALLINT DEFAULT 0,
	operate_time TIMESTAMP,
	cpu_quota INTEGER DEFAULT -1,
	memory_max INTEGER DEFAULT -1,
	create_time TIMESTAMP NOT NULL ,
	update_time TIMESTAMP NOT NULL
);

CREATE TABLE t_ds_task_group
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(100),
	description VARCHAR(255),
	group_size INTEGER NOT NULL ,
	use_size INTEGER DEFAULT 0,
	user_id INTEGER,
	project_code BIGINT DEFAULT 0,
	status SMALLINT DEFAULT 1,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE t_ds_task_group_queue
(
	id SERIAL  PRIMARY KEY,
	task_id INTEGER,
	task_name VARCHAR(100),
	group_id INTEGER,
	process_id INTEGER,
	priority INTEGER DEFAULT 0,
	status SMALLINT DEFAULT -1,
	force_start SMALLINT DEFAULT 0,
	in_queue SMALLINT DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE t_ds_task_instance
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(255),
	task_type VARCHAR(50) NOT NULL ,
	task_execute_type INTEGER DEFAULT 0,
	task_code BIGINT NOT NULL ,
	task_definition_version INTEGER DEFAULT 0,
	process_instance_id INTEGER,
	state SMALLINT,
	submit_time TIMESTAMP,
	start_time TIMESTAMP,
	end_time TIMESTAMP,
	host VARCHAR(135),
	execute_path VARCHAR(200),
	log_path TEXT,
	alert_flag SMALLINT,
	retry_times INTEGER DEFAULT 0,
	pid INTEGER,
	app_link TEXT,
	task_params TEXT,
	flag SMALLINT DEFAULT 1,
	retry_interval INTEGER,
	max_retry_times INTEGER,
	task_instance_priority INTEGER,
	worker_group VARCHAR(64),
	environment_code BIGINT DEFAULT -1,
	environment_config TEXT,
	executor_id INTEGER,
	first_submit_time TIMESTAMP,
	delay_time INTEGER DEFAULT 0,
	var_pool TEXT,
	task_group_id INTEGER,
	dry_run SMALLINT DEFAULT 0,
	cpu_quota INTEGER DEFAULT -1,
	memory_max INTEGER DEFAULT -1
);

CREATE TABLE t_ds_tenant
(
	id SERIAL  PRIMARY KEY,
	tenant_code VARCHAR(64),
	description VARCHAR(255),
	queue_id INTEGER,
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	origin_tenant_id INTEGER
);

CREATE TABLE t_ds_udfs
(
	id SERIAL  PRIMARY KEY,
	user_id INTEGER NOT NULL ,
	func_name VARCHAR(100) NOT NULL ,
	class_name VARCHAR(255) NOT NULL ,
	type SMALLINT NOT NULL ,
	arg_types VARCHAR(255),
	database VARCHAR(255),
	description VARCHAR(255),
	resource_id INTEGER NOT NULL ,
	resource_name VARCHAR(255) NOT NULL ,
	create_time TIMESTAMP NOT NULL ,
	update_time TIMESTAMP NOT NULL
);

CREATE TABLE t_ds_user
(
	id SERIAL  PRIMARY KEY,
	user_name VARCHAR(64),
	user_password VARCHAR(64),
	user_type SMALLINT,
	email VARCHAR(64),
	phone VARCHAR(11),
	tenant_id INTEGER,
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	queue VARCHAR(64),
	state SMALLINT DEFAULT 1,
	time_zone VARCHAR(32)
);

CREATE TABLE t_ds_version
(
	id SERIAL  PRIMARY KEY,
	version VARCHAR(200) NOT NULL
);

CREATE TABLE t_ds_worker_group
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(255) NOT NULL ,
	addr_list TEXT,
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	description TEXT,
	other_params_json TEXT
);



COMMENT ON COLUMN t_ds_access_token.id IS 'key';
COMMENT ON COLUMN t_ds_access_token.user_id IS 'user id';
COMMENT ON COLUMN t_ds_access_token.token IS 'token';
COMMENT ON COLUMN t_ds_access_token.expire_time IS 'end time of token ';
COMMENT ON COLUMN t_ds_access_token.create_time IS 'create time';
COMMENT ON COLUMN t_ds_access_token.update_time IS 'update time';
COMMENT ON COLUMN t_ds_alert.id IS 'key';
COMMENT ON COLUMN t_ds_alert.title IS 'title';
COMMENT ON COLUMN t_ds_alert.sign IS 'sign=sha1(content)';
COMMENT ON COLUMN t_ds_alert.content IS 'Message content (can be email, can be SMS. Mail is stored in JSON map, and SMS is string)';
COMMENT ON COLUMN t_ds_alert.alert_status IS '0:wait running,1:success,2:failed';
COMMENT ON COLUMN t_ds_alert.warning_type IS '1 process is successfully, 2 process/task is failed';
COMMENT ON COLUMN t_ds_alert.log IS 'log';
COMMENT ON COLUMN t_ds_alert.alertgroup_id IS 'alert group id';
COMMENT ON COLUMN t_ds_alert.create_time IS 'create time';
COMMENT ON COLUMN t_ds_alert.update_time IS 'update time';
COMMENT ON COLUMN t_ds_alert.project_code IS 'project_code';
COMMENT ON COLUMN t_ds_alert.process_definition_code IS 'process_definition_code';
COMMENT ON COLUMN t_ds_alert.process_instance_id IS 'process_instance_id';
COMMENT ON COLUMN t_ds_alert.alert_type IS 'alert_type';
COMMENT ON COLUMN t_ds_alert_plugin_instance.plugin_instance_params IS 'plugin instance params. Also contain the params value which user input in web ui.';
COMMENT ON COLUMN t_ds_alert_plugin_instance.instance_name IS 'alert instance name';
COMMENT ON COLUMN t_ds_alert_send_status.create_time IS 'create time';
COMMENT ON COLUMN t_ds_alertgroup.id IS 'key';
COMMENT ON COLUMN t_ds_alertgroup.alert_instance_ids IS 'alert instance ids';
COMMENT ON COLUMN t_ds_alertgroup.create_user_id IS 'create user id';
COMMENT ON COLUMN t_ds_alertgroup.group_name IS 'group name';
COMMENT ON COLUMN t_ds_alertgroup.create_time IS 'create time';
COMMENT ON COLUMN t_ds_alertgroup.update_time IS 'update time';
COMMENT ON COLUMN t_ds_audit_log.id IS 'key';
COMMENT ON COLUMN t_ds_audit_log.user_id IS 'user id';
COMMENT ON COLUMN t_ds_audit_log.resource_type IS 'resource type';
COMMENT ON COLUMN t_ds_audit_log.operation IS 'operation';
COMMENT ON COLUMN t_ds_audit_log.time IS 'create time';
COMMENT ON COLUMN t_ds_audit_log.resource_id IS 'resource id';
COMMENT ON COLUMN t_ds_cluster.id IS 'id';
COMMENT ON COLUMN t_ds_cluster.code IS 'encoding';
COMMENT ON COLUMN t_ds_cluster.name IS 'cluster name';
COMMENT ON COLUMN t_ds_cluster.config IS 'this config contains many cluster variables config';
COMMENT ON COLUMN t_ds_cluster.description IS 'the details';
COMMENT ON COLUMN t_ds_cluster.operator IS 'operator user id';
COMMENT ON COLUMN t_ds_command.id IS 'key';
COMMENT ON COLUMN t_ds_command.command_type IS 'Command type: 0 start workflow, 1 start execution from current node, 2 resume fault-tolerant workflow, 3 resume pause process, 4 start execution from failed node, 5 complement, 6 schedule, 7 rerun, 8 pause, 9 stop, 10 resume waiting thread';
COMMENT ON COLUMN t_ds_command.process_definition_code IS 'process definition code';
COMMENT ON COLUMN t_ds_command.process_definition_version IS 'process definition version';
COMMENT ON COLUMN t_ds_command.process_instance_id IS 'process instance id';
COMMENT ON COLUMN t_ds_command.command_param IS 'json command parameters';
COMMENT ON COLUMN t_ds_command.task_depend_type IS 'Node dependency type: 0 current node, 1 forward, 2 backward';
COMMENT ON COLUMN t_ds_command.failure_strategy IS 'Failed policy: 0 end, 1 continue';
COMMENT ON COLUMN t_ds_command.warning_type IS 'Alarm type: 0 is not sent, 1 process is sent successfully, 2 process is sent failed, 3 process is sent successfully and all failures are sent';
COMMENT ON COLUMN t_ds_command.warning_group_id IS 'warning group';
COMMENT ON COLUMN t_ds_command.schedule_time IS 'schedule time';
COMMENT ON COLUMN t_ds_command.start_time IS 'start time';
COMMENT ON COLUMN t_ds_command.executor_id IS 'executor id';
COMMENT ON COLUMN t_ds_command.update_time IS 'update time';
COMMENT ON COLUMN t_ds_command.process_instance_priority IS 'process instance priority: 0 Highest,1 High,2 Medium,3 Low,4 Lowest';
COMMENT ON COLUMN t_ds_command.worker_group IS 'worker group';
COMMENT ON COLUMN t_ds_command.environment_code IS 'environment code';
COMMENT ON COLUMN t_ds_command.dry_run IS 'dry run flag：0 normal, 1 dry run';
COMMENT ON COLUMN t_ds_datasource.id IS 'key';
COMMENT ON COLUMN t_ds_datasource.name IS 'data source name';
COMMENT ON COLUMN t_ds_datasource.note IS 'description';
COMMENT ON COLUMN t_ds_datasource.type IS 'data source type: 0:mysql,1:postgresql,2:hive,3:spark';
COMMENT ON COLUMN t_ds_datasource.user_id IS 'the creator id';
COMMENT ON COLUMN t_ds_datasource.connection_params IS 'json connection params';
COMMENT ON COLUMN t_ds_datasource.create_time IS 'create time';
COMMENT ON COLUMN t_ds_datasource.update_time IS 'update time';
COMMENT ON COLUMN t_ds_datasource_kbs.id IS 'key';
COMMENT ON COLUMN t_ds_datasource_kbs.datasource_id IS '数据源 id';
COMMENT ON COLUMN t_ds_datasource_kbs.kbs_version IS '版本:华为(HW),开源(KY),亚信(YX)';
COMMENT ON COLUMN t_ds_datasource_kbs.kbs_principal IS '主体';
COMMENT ON COLUMN t_ds_datasource_kbs.kbs_keytab_origin_file_name IS 'keytab源文件名称';
COMMENT ON COLUMN t_ds_datasource_kbs.kbs_keytab_file_path IS 'keytab文件路径';
COMMENT ON COLUMN t_ds_datasource_kbs.kbs_krb_origin_file_name IS 'krb源文件名称';
COMMENT ON COLUMN t_ds_datasource_kbs.kbs_krb_file_path IS 'krb文件路径';
COMMENT ON COLUMN t_ds_datasource_kbs.kbs_jaas_origin_file_name IS 'jaas源文件名称';
COMMENT ON COLUMN t_ds_datasource_kbs.kbs_jaas_file_path IS 'jaas文件路径';
COMMENT ON COLUMN t_ds_environment.id IS 'id';
COMMENT ON COLUMN t_ds_environment.code IS 'encoding';
COMMENT ON COLUMN t_ds_environment.name IS 'environment name';
COMMENT ON COLUMN t_ds_environment.config IS 'this config contains many environment variables config';
COMMENT ON COLUMN t_ds_environment.description IS 'the details';
COMMENT ON COLUMN t_ds_environment.operator IS 'operator user id';
COMMENT ON COLUMN t_ds_environment_worker_group_relation.id IS 'id';
COMMENT ON COLUMN t_ds_environment_worker_group_relation.environment_code IS 'environment code';
COMMENT ON COLUMN t_ds_environment_worker_group_relation.worker_group IS 'worker group id';
COMMENT ON COLUMN t_ds_environment_worker_group_relation.operator IS 'operator user id';
COMMENT ON COLUMN t_ds_error_command.id IS 'key';
COMMENT ON COLUMN t_ds_error_command.command_type IS 'command type';
COMMENT ON COLUMN t_ds_error_command.executor_id IS 'executor id';
COMMENT ON COLUMN t_ds_error_command.process_definition_code IS 'process definition code';
COMMENT ON COLUMN t_ds_error_command.process_definition_version IS 'process definition version';
COMMENT ON COLUMN t_ds_error_command.process_instance_id IS 'process instance id: 0';
COMMENT ON COLUMN t_ds_error_command.command_param IS 'json command parameters';
COMMENT ON COLUMN t_ds_error_command.task_depend_type IS 'task depend type';
COMMENT ON COLUMN t_ds_error_command.failure_strategy IS 'failure strategy';
COMMENT ON COLUMN t_ds_error_command.warning_type IS 'warning type';
COMMENT ON COLUMN t_ds_error_command.warning_group_id IS 'warning group id';
COMMENT ON COLUMN t_ds_error_command.schedule_time IS 'scheduler time';
COMMENT ON COLUMN t_ds_error_command.start_time IS 'start time';
COMMENT ON COLUMN t_ds_error_command.update_time IS 'update time';
COMMENT ON COLUMN t_ds_error_command.process_instance_priority IS 'process instance priority, 0 Highest,1 High,2 Medium,3 Low,4 Lowest';
COMMENT ON COLUMN t_ds_error_command.worker_group IS 'worker group';
COMMENT ON COLUMN t_ds_error_command.environment_code IS 'environment code';
COMMENT ON COLUMN t_ds_error_command.message IS 'message';
COMMENT ON COLUMN t_ds_error_command.dry_run IS 'dry run flag: 0 normal, 1 dry run';
COMMENT ON COLUMN t_ds_fav_task.id IS 'favorite task id';
COMMENT ON COLUMN t_ds_fav_task.task_name IS 'favorite task name';
COMMENT ON COLUMN t_ds_fav_task.user_id IS 'user id';
COMMENT ON COLUMN t_ds_k8s.create_time IS 'create time';
COMMENT ON COLUMN t_ds_k8s.update_time IS 'update time';
COMMENT ON COLUMN t_ds_k8s_namespace.create_time IS 'create time';
COMMENT ON COLUMN t_ds_k8s_namespace.update_time IS 'update time';
COMMENT ON COLUMN t_ds_plugin_define.plugin_name IS 'the name of plugin eg: email';
COMMENT ON COLUMN t_ds_plugin_define.plugin_type IS 'plugin type . alert=alert plugin, job=job plugin';
COMMENT ON COLUMN t_ds_plugin_define.plugin_params IS 'plugin params';
COMMENT ON COLUMN t_ds_process_definition.id IS 'self-increasing id';
COMMENT ON COLUMN t_ds_process_definition.code IS 'encoding';
COMMENT ON COLUMN t_ds_process_definition.name IS 'process definition name';
COMMENT ON COLUMN t_ds_process_definition.version IS 'process definition version';
COMMENT ON COLUMN t_ds_process_definition.description IS 'description';
COMMENT ON COLUMN t_ds_process_definition.project_code IS 'project code';
COMMENT ON COLUMN t_ds_process_definition.release_state IS 'process definition release state：0:offline,1:online';
COMMENT ON COLUMN t_ds_process_definition.user_id IS 'process definition creator id';
COMMENT ON COLUMN t_ds_process_definition.global_params IS 'global parameters';
COMMENT ON COLUMN t_ds_process_definition.flag IS '0 not available, 1 available';
COMMENT ON COLUMN t_ds_process_definition.locations IS 'Node location information';
COMMENT ON COLUMN t_ds_process_definition.warning_group_id IS 'alert group id';
COMMENT ON COLUMN t_ds_process_definition.timeout IS 'time out, unit: minute';
COMMENT ON COLUMN t_ds_process_definition.tenant_id IS 'tenant id';
COMMENT ON COLUMN t_ds_process_definition.execution_type IS 'execution_type 0:parallel,1:serial wait,2:serial discard,3:serial priority';
COMMENT ON COLUMN t_ds_process_definition.create_time IS 'create time';
COMMENT ON COLUMN t_ds_process_definition.update_time IS 'update time';
COMMENT ON COLUMN t_ds_process_definition_lock.id IS 'key';
COMMENT ON COLUMN t_ds_process_definition_lock.process_definition_code IS 'encoding';
COMMENT ON COLUMN t_ds_process_definition_lock.user_id IS 'process definition creator id';
COMMENT ON COLUMN t_ds_process_definition_lock.create_time IS 'create time';
COMMENT ON COLUMN t_ds_process_definition_log.id IS 'self-increasing id';
COMMENT ON COLUMN t_ds_process_definition_log.code IS 'encoding';
COMMENT ON COLUMN t_ds_process_definition_log.name IS 'process definition name';
COMMENT ON COLUMN t_ds_process_definition_log.version IS 'process definition version';
COMMENT ON COLUMN t_ds_process_definition_log.description IS 'description';
COMMENT ON COLUMN t_ds_process_definition_log.project_code IS 'project code';
COMMENT ON COLUMN t_ds_process_definition_log.release_state IS 'process definition release state：0:offline,1:online';
COMMENT ON COLUMN t_ds_process_definition_log.user_id IS 'process definition creator id';
COMMENT ON COLUMN t_ds_process_definition_log.global_params IS 'global parameters';
COMMENT ON COLUMN t_ds_process_definition_log.flag IS '0 not available, 1 available';
COMMENT ON COLUMN t_ds_process_definition_log.locations IS 'Node location information';
COMMENT ON COLUMN t_ds_process_definition_log.warning_group_id IS 'alert group id';
COMMENT ON COLUMN t_ds_process_definition_log.timeout IS 'time out,unit: minute';
COMMENT ON COLUMN t_ds_process_definition_log.tenant_id IS 'tenant id';
COMMENT ON COLUMN t_ds_process_definition_log.execution_type IS 'execution_type 0:parallel,1:serial wait,2:serial discard,3:serial priority';
COMMENT ON COLUMN t_ds_process_definition_log.operator IS 'operator user id';
COMMENT ON COLUMN t_ds_process_definition_log.operate_time IS 'operate time';
COMMENT ON COLUMN t_ds_process_definition_log.create_time IS 'create time';
COMMENT ON COLUMN t_ds_process_definition_log.update_time IS 'update time';
COMMENT ON COLUMN t_ds_process_instance.id IS 'key';
COMMENT ON COLUMN t_ds_process_instance.name IS 'process instance name';
COMMENT ON COLUMN t_ds_process_instance.process_definition_code IS 'process definition code';
COMMENT ON COLUMN t_ds_process_instance.process_definition_version IS 'process definition version';
COMMENT ON COLUMN t_ds_process_instance.state IS 'process instance Status: 0 commit succeeded, 1 running, 2 prepare to pause, 3 pause, 4 prepare to stop, 5 stop, 6 fail, 7 succeed, 8 need fault tolerance, 9 kill, 10 wait for thread, 11 wait for dependency to complete';
COMMENT ON COLUMN t_ds_process_instance.state_history IS 'state history desc';
COMMENT ON COLUMN t_ds_process_instance.recovery IS 'process instance failover flag：0:normal,1:failover instance';
COMMENT ON COLUMN t_ds_process_instance.start_time IS 'process instance start time';
COMMENT ON COLUMN t_ds_process_instance.end_time IS 'process instance end time';
COMMENT ON COLUMN t_ds_process_instance.run_times IS 'process instance run times';
COMMENT ON COLUMN t_ds_process_instance.host IS 'process instance host';
COMMENT ON COLUMN t_ds_process_instance.command_type IS 'command type';
COMMENT ON COLUMN t_ds_process_instance.command_param IS 'json command parameters';
COMMENT ON COLUMN t_ds_process_instance.task_depend_type IS 'task depend type. 0: only current node,1:before the node,2:later nodes';
COMMENT ON COLUMN t_ds_process_instance.max_try_times IS 'max try times';
COMMENT ON COLUMN t_ds_process_instance.failure_strategy IS 'failure strategy. 0:end the process when node failed,1:continue running the other nodes when node failed';
COMMENT ON COLUMN t_ds_process_instance.warning_type IS 'warning type. 0:no warning,1:warning if process success,2:warning if process failed,3:warning if success';
COMMENT ON COLUMN t_ds_process_instance.warning_group_id IS 'warning group id';
COMMENT ON COLUMN t_ds_process_instance.schedule_time IS 'schedule time';
COMMENT ON COLUMN t_ds_process_instance.command_start_time IS 'command start time';
COMMENT ON COLUMN t_ds_process_instance.global_params IS 'global parameters';
COMMENT ON COLUMN t_ds_process_instance.flag IS 'flag';
COMMENT ON COLUMN t_ds_process_instance.is_sub_process IS 'flag, whether the process is sub process';
COMMENT ON COLUMN t_ds_process_instance.executor_id IS 'executor id';
COMMENT ON COLUMN t_ds_process_instance.history_cmd IS 'history commands of process instance operation';
COMMENT ON COLUMN t_ds_process_instance.process_instance_priority IS 'process instance priority. 0 Highest,1 High,2 Medium,3 Low,4 Lowest';
COMMENT ON COLUMN t_ds_process_instance.worker_group IS 'worker group id';
COMMENT ON COLUMN t_ds_process_instance.environment_code IS 'environment code';
COMMENT ON COLUMN t_ds_process_instance.timeout IS 'time out';
COMMENT ON COLUMN t_ds_process_instance.tenant_id IS 'tenant id';
COMMENT ON COLUMN t_ds_process_instance.var_pool IS 'var_pool';
COMMENT ON COLUMN t_ds_process_instance.dry_run IS 'dry run flag：0 normal, 1 dry run';
COMMENT ON COLUMN t_ds_process_instance.next_process_instance_id IS 'serial queue next processInstanceId';
COMMENT ON COLUMN t_ds_process_instance.restart_time IS 'process instance restart time';
COMMENT ON COLUMN t_ds_process_task_relation.id IS 'self-increasing id';
COMMENT ON COLUMN t_ds_process_task_relation.name IS 'relation name';
COMMENT ON COLUMN t_ds_process_task_relation.project_code IS 'project code';
COMMENT ON COLUMN t_ds_process_task_relation.process_definition_code IS 'process code';
COMMENT ON COLUMN t_ds_process_task_relation.process_definition_version IS 'process version';
COMMENT ON COLUMN t_ds_process_task_relation.pre_task_code IS 'pre task code';
COMMENT ON COLUMN t_ds_process_task_relation.pre_task_version IS 'pre task version';
COMMENT ON COLUMN t_ds_process_task_relation.post_task_code IS 'post task code';
COMMENT ON COLUMN t_ds_process_task_relation.post_task_version IS 'post task version';
COMMENT ON COLUMN t_ds_process_task_relation.condition_type IS 'condition type : 0 none, 1 judge 2 delay';
COMMENT ON COLUMN t_ds_process_task_relation.condition_params IS 'condition params(json)';
COMMENT ON COLUMN t_ds_process_task_relation.create_time IS 'create time';
COMMENT ON COLUMN t_ds_process_task_relation.update_time IS 'update time';
COMMENT ON COLUMN t_ds_process_task_relation_log.id IS 'self-increasing id';
COMMENT ON COLUMN t_ds_process_task_relation_log.name IS 'relation name';
COMMENT ON COLUMN t_ds_process_task_relation_log.project_code IS 'project code';
COMMENT ON COLUMN t_ds_process_task_relation_log.process_definition_code IS 'process code';
COMMENT ON COLUMN t_ds_process_task_relation_log.process_definition_version IS 'process version';
COMMENT ON COLUMN t_ds_process_task_relation_log.pre_task_code IS 'pre task code';
COMMENT ON COLUMN t_ds_process_task_relation_log.pre_task_version IS 'pre task version';
COMMENT ON COLUMN t_ds_process_task_relation_log.post_task_code IS 'post task code';
COMMENT ON COLUMN t_ds_process_task_relation_log.post_task_version IS 'post task version';
COMMENT ON COLUMN t_ds_process_task_relation_log.condition_type IS 'condition type : 0 none, 1 judge 2 delay';
COMMENT ON COLUMN t_ds_process_task_relation_log.condition_params IS 'condition params(json)';
COMMENT ON COLUMN t_ds_process_task_relation_log.operator IS 'operator user id';
COMMENT ON COLUMN t_ds_process_task_relation_log.operate_time IS 'operate time';
COMMENT ON COLUMN t_ds_process_task_relation_log.create_time IS 'create time';
COMMENT ON COLUMN t_ds_process_task_relation_log.update_time IS 'update time';
COMMENT ON COLUMN t_ds_project.id IS 'key';
COMMENT ON COLUMN t_ds_project.name IS 'project name';
COMMENT ON COLUMN t_ds_project.code IS 'encoding';
COMMENT ON COLUMN t_ds_project.user_id IS 'creator id';
COMMENT ON COLUMN t_ds_project.flag IS '0 not available, 1 available';
COMMENT ON COLUMN t_ds_project.create_time IS 'create time';
COMMENT ON COLUMN t_ds_project.update_time IS 'update time';
COMMENT ON COLUMN t_ds_project.is_fuse_modeling IS '是否融合建模，0否1是';
COMMENT ON COLUMN t_ds_queue.id IS 'key';
COMMENT ON COLUMN t_ds_queue.queue_name IS 'queue name';
COMMENT ON COLUMN t_ds_queue.queue IS 'yarn queue name';
COMMENT ON COLUMN t_ds_queue.create_time IS 'create time';
COMMENT ON COLUMN t_ds_queue.update_time IS 'update time';
COMMENT ON COLUMN t_ds_relation_datasource_user.id IS 'key';
COMMENT ON COLUMN t_ds_relation_datasource_user.user_id IS 'user id';
COMMENT ON COLUMN t_ds_relation_datasource_user.datasource_id IS 'data source id';
COMMENT ON COLUMN t_ds_relation_datasource_user.perm IS 'limits of authority';
COMMENT ON COLUMN t_ds_relation_datasource_user.create_time IS 'create time';
COMMENT ON COLUMN t_ds_relation_datasource_user.update_time IS 'update time';
COMMENT ON COLUMN t_ds_relation_namespace_user.id IS 'key';
COMMENT ON COLUMN t_ds_relation_namespace_user.user_id IS 'user id';
COMMENT ON COLUMN t_ds_relation_namespace_user.namespace_id IS 'namespace id';
COMMENT ON COLUMN t_ds_relation_namespace_user.perm IS 'limits of authority';
COMMENT ON COLUMN t_ds_relation_namespace_user.create_time IS 'create time';
COMMENT ON COLUMN t_ds_relation_namespace_user.update_time IS 'update time';
COMMENT ON COLUMN t_ds_relation_process_instance.id IS 'key';
COMMENT ON COLUMN t_ds_relation_process_instance.parent_process_instance_id IS 'parent process instance id';
COMMENT ON COLUMN t_ds_relation_process_instance.parent_task_instance_id IS 'parent process instance id';
COMMENT ON COLUMN t_ds_relation_process_instance.process_instance_id IS 'child process instance id';
COMMENT ON COLUMN t_ds_relation_project_user.id IS 'key';
COMMENT ON COLUMN t_ds_relation_project_user.user_id IS 'user id';
COMMENT ON COLUMN t_ds_relation_project_user.project_id IS 'project id';
COMMENT ON COLUMN t_ds_relation_project_user.perm IS 'limits of authority';
COMMENT ON COLUMN t_ds_relation_project_user.create_time IS 'create time';
COMMENT ON COLUMN t_ds_relation_project_user.update_time IS 'update time';
COMMENT ON COLUMN t_ds_relation_resources_user.user_id IS 'user id';
COMMENT ON COLUMN t_ds_relation_resources_user.resources_id IS 'resource id';
COMMENT ON COLUMN t_ds_relation_resources_user.perm IS 'limits of authority';
COMMENT ON COLUMN t_ds_relation_resources_user.create_time IS 'create time';
COMMENT ON COLUMN t_ds_relation_resources_user.update_time IS 'update time';
COMMENT ON COLUMN t_ds_relation_udfs_user.id IS 'key';
COMMENT ON COLUMN t_ds_relation_udfs_user.user_id IS 'userid';
COMMENT ON COLUMN t_ds_relation_udfs_user.udf_id IS 'udf id';
COMMENT ON COLUMN t_ds_relation_udfs_user.perm IS 'limits of authority';
COMMENT ON COLUMN t_ds_relation_udfs_user.create_time IS 'create time';
COMMENT ON COLUMN t_ds_relation_udfs_user.update_time IS 'update time';
COMMENT ON COLUMN t_ds_resources.id IS 'key';
COMMENT ON COLUMN t_ds_resources.alias IS 'alias';
COMMENT ON COLUMN t_ds_resources.file_name IS 'file name';
COMMENT ON COLUMN t_ds_resources.user_id IS 'user id';
COMMENT ON COLUMN t_ds_resources.type IS 'resource type,0:FILE，1:UDF';
COMMENT ON COLUMN t_ds_resources.size IS 'resource size';
COMMENT ON COLUMN t_ds_resources.create_time IS 'create time';
COMMENT ON COLUMN t_ds_resources.update_time IS 'update time';
COMMENT ON COLUMN t_ds_schedules.id IS 'key';
COMMENT ON COLUMN t_ds_schedules.process_definition_code IS 'process definition code';
COMMENT ON COLUMN t_ds_schedules.start_time IS 'start time';
COMMENT ON COLUMN t_ds_schedules.end_time IS 'end time';
COMMENT ON COLUMN t_ds_schedules.timezone_id IS 'schedule timezone id';
COMMENT ON COLUMN t_ds_schedules.crontab IS 'crontab description';
COMMENT ON COLUMN t_ds_schedules.failure_strategy IS 'failure strategy. 0:end,1:continue';
COMMENT ON COLUMN t_ds_schedules.user_id IS 'user id';
COMMENT ON COLUMN t_ds_schedules.release_state IS 'release state. 0:offline,1:online ';
COMMENT ON COLUMN t_ds_schedules.warning_type IS 'Alarm type: 0 is not sent, 1 process is sent successfully, 2 process is sent failed, 3 process is sent successfully and all failures are sent';
COMMENT ON COLUMN t_ds_schedules.warning_group_id IS 'alert group id';
COMMENT ON COLUMN t_ds_schedules.process_instance_priority IS 'process instance priority：0 Highest,1 High,2 Medium,3 Low,4 Lowest';
COMMENT ON COLUMN t_ds_schedules.worker_group IS 'worker group id';
COMMENT ON COLUMN t_ds_schedules.environment_code IS 'environment code';
COMMENT ON COLUMN t_ds_schedules.create_time IS 'create time';
COMMENT ON COLUMN t_ds_schedules.update_time IS 'update time';
COMMENT ON COLUMN t_ds_session.id IS 'key';
COMMENT ON COLUMN t_ds_session.user_id IS 'user id';
COMMENT ON COLUMN t_ds_session.ip IS 'ip';
COMMENT ON COLUMN t_ds_session.last_login_time IS 'last login time';
COMMENT ON COLUMN t_ds_task_definition.id IS 'self-increasing id';
COMMENT ON COLUMN t_ds_task_definition.code IS 'encoding';
COMMENT ON COLUMN t_ds_task_definition.name IS 'task definition name';
COMMENT ON COLUMN t_ds_task_definition.version IS 'task definition version';
COMMENT ON COLUMN t_ds_task_definition.description IS 'description';
COMMENT ON COLUMN t_ds_task_definition.project_code IS 'project code';
COMMENT ON COLUMN t_ds_task_definition.user_id IS 'task definition creator id';
COMMENT ON COLUMN t_ds_task_definition.task_type IS 'task type';
COMMENT ON COLUMN t_ds_task_definition.task_execute_type IS 'task execute type: 0-batch, 1-stream';
COMMENT ON COLUMN t_ds_task_definition.task_params IS 'job custom parameters';
COMMENT ON COLUMN t_ds_task_definition.flag IS '0 not available, 1 available';
COMMENT ON COLUMN t_ds_task_definition.task_priority IS 'job priority';
COMMENT ON COLUMN t_ds_task_definition.worker_group IS 'worker grouping';
COMMENT ON COLUMN t_ds_task_definition.environment_code IS 'environment code';
COMMENT ON COLUMN t_ds_task_definition.fail_retry_times IS 'number of failed retries';
COMMENT ON COLUMN t_ds_task_definition.fail_retry_interval IS 'failed retry interval';
COMMENT ON COLUMN t_ds_task_definition.timeout_flag IS 'timeout flag:0 close, 1 open';
COMMENT ON COLUMN t_ds_task_definition.timeout_notify_strategy IS 'timeout notification policy: 0 warning, 1 fail';
COMMENT ON COLUMN t_ds_task_definition.timeout IS 'timeout length,unit: minute';
COMMENT ON COLUMN t_ds_task_definition.delay_time IS 'delay execution time,unit: minute';
COMMENT ON COLUMN t_ds_task_definition.resource_ids IS 'resource id, separated by comma';
COMMENT ON COLUMN t_ds_task_definition.task_group_id IS 'task group id';
COMMENT ON COLUMN t_ds_task_definition.task_group_priority IS 'task group priority';
COMMENT ON COLUMN t_ds_task_definition.cpu_quota IS 'cpuQuota(%): -1:Infinity';
COMMENT ON COLUMN t_ds_task_definition.memory_max IS 'MemoryMax(MB): -1:Infinity';
COMMENT ON COLUMN t_ds_task_definition.create_time IS 'create time';
COMMENT ON COLUMN t_ds_task_definition.update_time IS 'update time';
COMMENT ON COLUMN t_ds_task_definition_log.id IS 'self-increasing id';
COMMENT ON COLUMN t_ds_task_definition_log.code IS 'encoding';
COMMENT ON COLUMN t_ds_task_definition_log.name IS 'task definition name';
COMMENT ON COLUMN t_ds_task_definition_log.version IS 'task definition version';
COMMENT ON COLUMN t_ds_task_definition_log.description IS 'description';
COMMENT ON COLUMN t_ds_task_definition_log.project_code IS 'project code';
COMMENT ON COLUMN t_ds_task_definition_log.user_id IS 'task definition creator id';
COMMENT ON COLUMN t_ds_task_definition_log.task_type IS 'task type';
COMMENT ON COLUMN t_ds_task_definition_log.task_execute_type IS 'task execute type: 0-batch, 1-stream';
COMMENT ON COLUMN t_ds_task_definition_log.task_params IS 'job custom parameters';
COMMENT ON COLUMN t_ds_task_definition_log.flag IS '0 not available, 1 available';
COMMENT ON COLUMN t_ds_task_definition_log.task_priority IS 'job priority';
COMMENT ON COLUMN t_ds_task_definition_log.worker_group IS 'worker grouping';
COMMENT ON COLUMN t_ds_task_definition_log.environment_code IS 'environment code';
COMMENT ON COLUMN t_ds_task_definition_log.fail_retry_times IS 'number of failed retries';
COMMENT ON COLUMN t_ds_task_definition_log.fail_retry_interval IS 'failed retry interval';
COMMENT ON COLUMN t_ds_task_definition_log.timeout_flag IS 'timeout flag:0 close, 1 open';
COMMENT ON COLUMN t_ds_task_definition_log.timeout_notify_strategy IS 'timeout notification policy: 0 warning, 1 fail';
COMMENT ON COLUMN t_ds_task_definition_log.timeout IS 'timeout length,unit: minute';
COMMENT ON COLUMN t_ds_task_definition_log.delay_time IS 'delay execution time,unit: minute';
COMMENT ON COLUMN t_ds_task_definition_log.resource_ids IS 'resource id, separated by comma';
COMMENT ON COLUMN t_ds_task_definition_log.operator IS 'operator user id';
COMMENT ON COLUMN t_ds_task_definition_log.task_group_id IS 'task group id';
COMMENT ON COLUMN t_ds_task_definition_log.task_group_priority IS 'task group priority';
COMMENT ON COLUMN t_ds_task_definition_log.operate_time IS 'operate time';
COMMENT ON COLUMN t_ds_task_definition_log.cpu_quota IS 'cpuQuota(%): -1:Infinity';
COMMENT ON COLUMN t_ds_task_definition_log.memory_max IS 'MemoryMax(MB): -1:Infinity';
COMMENT ON COLUMN t_ds_task_definition_log.create_time IS 'create time';
COMMENT ON COLUMN t_ds_task_definition_log.update_time IS 'update time';
COMMENT ON COLUMN t_ds_task_group.id IS 'key';
COMMENT ON COLUMN t_ds_task_group.name IS 'task_group name';
COMMENT ON COLUMN t_ds_task_group.group_size IS 'group size';
COMMENT ON COLUMN t_ds_task_group.use_size IS 'used size';
COMMENT ON COLUMN t_ds_task_group.user_id IS 'creator id';
COMMENT ON COLUMN t_ds_task_group.project_code IS 'project code';
COMMENT ON COLUMN t_ds_task_group.status IS '0 not available, 1 available';
COMMENT ON COLUMN t_ds_task_group_queue.id IS 'key';
COMMENT ON COLUMN t_ds_task_group_queue.task_id IS 'taskintanceid';
COMMENT ON COLUMN t_ds_task_group_queue.task_name IS 'TaskInstance name';
COMMENT ON COLUMN t_ds_task_group_queue.group_id IS 'taskGroup id';
COMMENT ON COLUMN t_ds_task_group_queue.process_id IS 'processInstace id';
COMMENT ON COLUMN t_ds_task_group_queue.priority IS 'priority';
COMMENT ON COLUMN t_ds_task_group_queue.status IS '-1: waiting  1: running  2: finished';
COMMENT ON COLUMN t_ds_task_group_queue.force_start IS 'is force start 0 NO ,1 YES';
COMMENT ON COLUMN t_ds_task_group_queue.in_queue IS 'ready to get the queue by other task finish 0 NO ,1 YES';
COMMENT ON COLUMN t_ds_task_instance.id IS 'key';
COMMENT ON COLUMN t_ds_task_instance.name IS 'task name';
COMMENT ON COLUMN t_ds_task_instance.task_type IS 'task type';
COMMENT ON COLUMN t_ds_task_instance.task_execute_type IS 'task execute type: 0-batch, 1-stream';
COMMENT ON COLUMN t_ds_task_instance.task_code IS 'task definition code';
COMMENT ON COLUMN t_ds_task_instance.task_definition_version IS 'task definition version';
COMMENT ON COLUMN t_ds_task_instance.process_instance_id IS 'process instance id';
COMMENT ON COLUMN t_ds_task_instance.state IS 'Status: 0 commit succeeded, 1 running, 2 prepare to pause, 3 pause, 4 prepare to stop, 5 stop, 6 fail, 7 succeed, 8 need fault tolerance, 9 kill, 10 wait for thread, 11 wait for dependency to complete';
COMMENT ON COLUMN t_ds_task_instance.submit_time IS 'task submit time';
COMMENT ON COLUMN t_ds_task_instance.start_time IS 'task start time';
COMMENT ON COLUMN t_ds_task_instance.end_time IS 'task end time';
COMMENT ON COLUMN t_ds_task_instance.host IS 'host of task running on';
COMMENT ON COLUMN t_ds_task_instance.execute_path IS 'task execute path in the host';
COMMENT ON COLUMN t_ds_task_instance.log_path IS 'task log path';
COMMENT ON COLUMN t_ds_task_instance.alert_flag IS 'whether alert';
COMMENT ON COLUMN t_ds_task_instance.retry_times IS 'task retry times';
COMMENT ON COLUMN t_ds_task_instance.pid IS 'pid of task';
COMMENT ON COLUMN t_ds_task_instance.app_link IS 'yarn app id';
COMMENT ON COLUMN t_ds_task_instance.task_params IS 'job custom parameters';
COMMENT ON COLUMN t_ds_task_instance.flag IS '0 not available, 1 available';
COMMENT ON COLUMN t_ds_task_instance.retry_interval IS 'retry interval when task failed ';
COMMENT ON COLUMN t_ds_task_instance.max_retry_times IS 'max retry times';
COMMENT ON COLUMN t_ds_task_instance.task_instance_priority IS 'task instance priority:0 Highest,1 High,2 Medium,3 Low,4 Lowest';
COMMENT ON COLUMN t_ds_task_instance.worker_group IS 'worker group id';
COMMENT ON COLUMN t_ds_task_instance.environment_code IS 'environment code';
COMMENT ON COLUMN t_ds_task_instance.environment_config IS 'this config contains many environment variables config';
COMMENT ON COLUMN t_ds_task_instance.first_submit_time IS 'task first submit time';
COMMENT ON COLUMN t_ds_task_instance.delay_time IS 'task delay execution time';
COMMENT ON COLUMN t_ds_task_instance.var_pool IS 'var_pool';
COMMENT ON COLUMN t_ds_task_instance.task_group_id IS 'task group id';
COMMENT ON COLUMN t_ds_task_instance.dry_run IS 'dry run flag: 0 normal, 1 dry run';
COMMENT ON COLUMN t_ds_task_instance.cpu_quota IS 'cpuQuota(%): -1:Infinity';
COMMENT ON COLUMN t_ds_task_instance.memory_max IS 'MemoryMax(MB): -1:Infinity';
COMMENT ON COLUMN t_ds_tenant.id IS 'key';
COMMENT ON COLUMN t_ds_tenant.tenant_code IS 'tenant code';
COMMENT ON COLUMN t_ds_tenant.queue_id IS 'queue id';
COMMENT ON COLUMN t_ds_tenant.create_time IS 'create time';
COMMENT ON COLUMN t_ds_tenant.update_time IS 'update time';
COMMENT ON COLUMN t_ds_tenant.origin_tenant_id IS '第三方租户id';
COMMENT ON COLUMN t_ds_udfs.id IS 'key';
COMMENT ON COLUMN t_ds_udfs.user_id IS 'user id';
COMMENT ON COLUMN t_ds_udfs.func_name IS 'UDF function name';
COMMENT ON COLUMN t_ds_udfs.class_name IS 'class of udf';
COMMENT ON COLUMN t_ds_udfs.type IS 'Udf function type';
COMMENT ON COLUMN t_ds_udfs.arg_types IS 'arguments types';
COMMENT ON COLUMN t_ds_udfs.database IS 'data base';
COMMENT ON COLUMN t_ds_udfs.resource_id IS 'resource id';
COMMENT ON COLUMN t_ds_udfs.resource_name IS 'resource name';
COMMENT ON COLUMN t_ds_udfs.create_time IS 'create time';
COMMENT ON COLUMN t_ds_udfs.update_time IS 'update time';
COMMENT ON COLUMN t_ds_user.id IS 'user id';
COMMENT ON COLUMN t_ds_user.user_name IS 'user name';
COMMENT ON COLUMN t_ds_user.user_password IS 'user password';
COMMENT ON COLUMN t_ds_user.user_type IS 'user type, 0:administrator，1:ordinary user';
COMMENT ON COLUMN t_ds_user.email IS 'email';
COMMENT ON COLUMN t_ds_user.phone IS 'phone';
COMMENT ON COLUMN t_ds_user.tenant_id IS 'tenant id';
COMMENT ON COLUMN t_ds_user.create_time IS 'create time';
COMMENT ON COLUMN t_ds_user.update_time IS 'update time';
COMMENT ON COLUMN t_ds_user.queue IS 'queue';
COMMENT ON COLUMN t_ds_user.state IS 'state 0:disable 1:enable';
COMMENT ON COLUMN t_ds_user.time_zone IS 'time zone';
COMMENT ON COLUMN t_ds_worker_group.id IS 'id';
COMMENT ON COLUMN t_ds_worker_group.name IS 'worker group name';
COMMENT ON COLUMN t_ds_worker_group.addr_list IS 'worker addr list. split by [,]';
COMMENT ON COLUMN t_ds_worker_group.create_time IS 'create time';
COMMENT ON COLUMN t_ds_worker_group.update_time IS 'update time';
COMMENT ON COLUMN t_ds_worker_group.description IS 'description';
COMMENT ON COLUMN t_ds_worker_group.other_params_json IS 'other params json';


CREATE INDEX IDX_SIGN_IDX ON t_ds_alert(sign);
CREATE INDEX IDX_STATUS_IDX ON t_ds_alert(alert_status);
CREATE UNIQUE INDEX ALERT_SEND_STATUS_UNIQUE_IDX ON t_ds_alert_send_status(alert_id,alert_plugin_instance_id);
CREATE UNIQUE INDEX T_DS_ALERTGROUP_NAME_UN_IDX ON t_ds_alertgroup(group_name);
CREATE UNIQUE INDEX CLUSTER_CODE_UNIQUE_IDX ON t_ds_cluster(code);
CREATE UNIQUE INDEX CLUSTER_NAME_UNIQUE_IDX ON t_ds_cluster(name);
CREATE INDEX PRIORITY_ID_INDEX_IDX ON t_ds_command(process_instance_priority,id);
CREATE UNIQUE INDEX T_DS_DATASOURCE_NAME_UN_IDX ON t_ds_datasource(name,type);
CREATE UNIQUE INDEX ENVIRONMENT_CODE_UNIQUE_IDX ON t_ds_environment(code);
CREATE UNIQUE INDEX ENVIRONMENT_NAME_UNIQUE_IDX ON t_ds_environment(name);
CREATE UNIQUE INDEX ENVIRONMENT_WORKER_GROUP_UNIQUE_IDX ON t_ds_environment_worker_group_relation(environment_code,worker_group);
CREATE UNIQUE INDEX K8S_NAMESPACE_UNIQUE_IDX ON t_ds_k8s_namespace(namespace,cluster_code);
CREATE UNIQUE INDEX T_DS_PLUGIN_DEFINE_UN_IDX ON t_ds_plugin_define(plugin_name,plugin_type);
CREATE UNIQUE INDEX PROCESS_UNIQUE_IDX ON t_ds_process_definition(name,project_code);
CREATE INDEX PROCESS_INSTANCE_INDEX_IDX ON t_ds_process_instance(process_definition_code,id);
CREATE INDEX START_TIME_INDEX_IDX ON t_ds_process_instance(start_time,end_time);
CREATE INDEX IDX_CODE_IDX ON t_ds_process_task_relation(project_code,process_definition_code);
CREATE INDEX IDX_POST_TASK_CODE_VERSION_IDX ON t_ds_process_task_relation(post_task_code,post_task_version);
CREATE INDEX IDX_PRE_TASK_CODE_VERSION_IDX ON t_ds_process_task_relation(pre_task_code,pre_task_version);
CREATE INDEX IDX_PROCESS_CODE_VERSION_IDX ON t_ds_process_task_relation_log(process_definition_code,process_definition_version);
CREATE UNIQUE INDEX UNIQUE_CODE_IDX ON t_ds_project(code);
CREATE UNIQUE INDEX UNIQUE_NAME_IDX ON t_ds_project(name);
CREATE INDEX USER_ID_INDEX_IDX ON t_ds_project(user_id);
CREATE UNIQUE INDEX UNIQUE_QUEUE_NAME_IDX ON t_ds_queue(queue_name);
CREATE UNIQUE INDEX NAMESPACE_USER_UNIQUE_IDX ON t_ds_relation_namespace_user(user_id,namespace_id);
CREATE INDEX IDX_PARENT_PROCESS_TASK_IDX ON t_ds_relation_process_instance(parent_process_instance_id,parent_task_instance_id);
CREATE INDEX IDX_PROCESS_INSTANCE_ID_IDX ON t_ds_relation_process_instance(process_instance_id);
CREATE UNIQUE INDEX UNIQ_UID_PID_IDX ON t_ds_relation_project_user(user_id,project_id);
CREATE UNIQUE INDEX T_DS_RESOURCES_UN_IDX ON t_ds_resources(full_name,type);
CREATE INDEX IDX_CODE_VERSION_IDX ON t_ds_task_definition_log(code,version);
CREATE INDEX IDX_PROJECT_CODE_IDX ON t_ds_task_definition_log(project_code);
CREATE INDEX IDX_CODE_VERSION_1_IDX ON t_ds_task_instance(task_code,task_definition_version);
CREATE INDEX PROCESS_INSTANCE_ID_IDX ON t_ds_task_instance(process_instance_id);
CREATE UNIQUE INDEX UNIQUE_TENANT_CODE_IDX ON t_ds_tenant(tenant_code);
CREATE UNIQUE INDEX UNIQUE_FUNC_NAME_IDX ON t_ds_udfs(func_name);
CREATE UNIQUE INDEX USER_NAME_UNIQUE_IDX ON t_ds_user(user_name);
CREATE UNIQUE INDEX VERSION_UNIQUE_IDX ON t_ds_version(version);
CREATE UNIQUE INDEX NAME_UNIQUE_IDX ON t_ds_worker_group(name);

-- Records of t_ds_user?user : admin , password : dolphinscheduler123
INSERT INTO t_ds_user(user_name, user_password, user_type, email, phone, tenant_id, state, create_time, update_time, time_zone)
VALUES ('admin', '7ad2410b2f4c074479a8937a28a22b8f', '0', '<EMAIL>', '', '0', 1, '2018-03-27 15:48:50', '2018-10-24 17:40:22', null);

-- Records of t_ds_alertgroup, default admin warning group
INSERT INTO t_ds_alertgroup(alert_instance_ids, create_user_id, group_name, description, create_time, update_time)
VALUES ('1,2', 1, 'default admin warning group', 'default admin warning group', '2018-11-29 10:20:39', '2018-11-29 10:20:39');

-- Records of t_ds_queue,default queue name : default
INSERT INTO t_ds_queue(queue_name, queue, create_time, update_time)
VALUES ('default', 'default', '2018-11-29 10:22:33', '2018-11-29 10:22:33');

-- Records of t_ds_queue,default queue name : default
INSERT INTO t_ds_version(version) VALUES ('3.1.1');

INSERT INTO t_ds_dq_comparison_type
(id, "type", execute_sql, output_table, "name", create_time, update_time, is_inner_source)
VALUES(1, 'FixValue', NULL, NULL, NULL, '2021-06-30 00:00:00.000', '2021-06-30 00:00:00.000', false);
INSERT INTO t_ds_dq_comparison_type
(id, "type", execute_sql, output_table, "name", create_time, update_time, is_inner_source)
VALUES(2, 'DailyAvg', 'select round(avg(statistics_value),2) as day_avg from t_ds_dq_task_statistics_value where data_time >=date_trunc(''DAY'', ${data_time}) and data_time < date_add(date_trunc(''day'', ${data_time}),1) and unique_code = ${unique_code} and statistics_name = ''${statistics_name}''', 'day_range', 'day_range.day_avg', '2021-06-30 00:00:00.000', '2021-06-30 00:00:00.000', true);
INSERT INTO t_ds_dq_comparison_type
(id, "type", execute_sql, output_table, "name", create_time, update_time, is_inner_source)
VALUES(3, 'WeeklyAvg', 'select round(avg(statistics_value),2) as week_avg from t_ds_dq_task_statistics_value where  data_time >= date_trunc(''WEEK'', ${data_time}) and data_time <date_trunc(''day'', ${data_time}) and unique_code = ${unique_code} and statistics_name = ''${statistics_name}''', 'week_range', 'week_range.week_avg', '2021-06-30 00:00:00.000', '2021-06-30 00:00:00.000', true);
INSERT INTO t_ds_dq_comparison_type
(id, "type", execute_sql, output_table, "name", create_time, update_time, is_inner_source)
VALUES(4, 'MonthlyAvg', 'select round(avg(statistics_value),2) as month_avg from t_ds_dq_task_statistics_value where  data_time >= date_trunc(''MONTH'', ${data_time}) and data_time <date_trunc(''day'', ${data_time}) and unique_code = ${unique_code} and statistics_name = ''${statistics_name}''', 'month_range', 'month_range.month_avg', '2021-06-30 00:00:00.000', '2021-06-30 00:00:00.000', true);
INSERT INTO t_ds_dq_comparison_type
(id, "type", execute_sql, output_table, "name", create_time, update_time, is_inner_source)
VALUES(5, 'Last7DayAvg', 'select round(avg(statistics_value),2) as last_7_avg from t_ds_dq_task_statistics_value where  data_time >= date_add(date_trunc(''day'', ${data_time}),-7) and  data_time <date_trunc(''day'', ${data_time}) and unique_code = ${unique_code} and statistics_name = ''${statistics_name}''', 'last_seven_days', 'last_seven_days.last_7_avg', '2021-06-30 00:00:00.000', '2021-06-30 00:00:00.000', true);
INSERT INTO t_ds_dq_comparison_type
(id, "type", execute_sql, output_table, "name", create_time, update_time, is_inner_source)
VALUES(6, 'Last30DayAvg', 'select round(avg(statistics_value),2) as last_30_avg from t_ds_dq_task_statistics_value where  data_time >= date_add(date_trunc(''day'', ${data_time}),-30) and  data_time < date_trunc(''day'', ${data_time}) and unique_code = ${unique_code} and statistics_name = ''${statistics_name}''', 'last_thirty_days', 'last_thirty_days.last_30_avg', '2021-06-30 00:00:00.000', '2021-06-30 00:00:00.000', true);
INSERT INTO t_ds_dq_comparison_type
(id, "type", execute_sql, output_table, "name", create_time, update_time, is_inner_source)
VALUES(7, 'SrcTableTotalRows', 'SELECT COUNT(*) AS total FROM ${src_table} WHERE (${src_filter})', 'total_count', 'total_count.total', '2021-06-30 00:00:00.000', '2021-06-30 00:00:00.000', false);
INSERT INTO t_ds_dq_comparison_type
(id, "type", execute_sql, output_table, "name", create_time, update_time, is_inner_source)
VALUES(8, 'TargetTableTotalRows', 'SELECT COUNT(*) AS total FROM ${target_table} WHERE (${target_filter})', 'total_count', 'total_count.total', '2021-06-30 00:00:00.000', '2021-06-30 00:00:00.000', false);

INSERT INTO t_ds_dq_rule
(id, "name", "type", user_id, create_time, update_time)
VALUES(1, '$t(null_check)', 0, 1, '2020-01-12 00:00:00.000', '2020-01-12 00:00:00.000');
INSERT INTO t_ds_dq_rule
(id, "name", "type", user_id, create_time, update_time)
VALUES(2, '$t(custom_sql)', 1, 1, '2020-01-12 00:00:00.000', '2020-01-12 00:00:00.000');
INSERT INTO t_ds_dq_rule
(id, "name", "type", user_id, create_time, update_time)
VALUES(3, '$t(multi_table_accuracy)', 2, 1, '2020-01-12 00:00:00.000', '2020-01-12 00:00:00.000');
INSERT INTO t_ds_dq_rule
(id, "name", "type", user_id, create_time, update_time)
VALUES(4, '$t(multi_table_value_comparison)', 3, 1, '2020-01-12 00:00:00.000', '2020-01-12 00:00:00.000');
INSERT INTO t_ds_dq_rule
(id, "name", "type", user_id, create_time, update_time)
VALUES(5, '$t(field_length_check)', 0, 1, '2020-01-12 00:00:00.000', '2020-01-12 00:00:00.000');
INSERT INTO t_ds_dq_rule
(id, "name", "type", user_id, create_time, update_time)
VALUES(6, '$t(uniqueness_check)', 0, 1, '2020-01-12 00:00:00.000', '2020-01-12 00:00:00.000');
INSERT INTO t_ds_dq_rule
(id, "name", "type", user_id, create_time, update_time)
VALUES(7, '$t(regexp_check)', 0, 1, '2020-01-12 00:00:00.000', '2020-01-12 00:00:00.000');
INSERT INTO t_ds_dq_rule
(id, "name", "type", user_id, create_time, update_time)
VALUES(8, '$t(timeliness_check)', 0, 1, '2020-01-12 00:00:00.000', '2020-01-12 00:00:00.000');
INSERT INTO t_ds_dq_rule
(id, "name", "type", user_id, create_time, update_time)
VALUES(9, '$t(enumeration_check)', 0, 1, '2020-01-12 00:00:00.000', '2020-01-12 00:00:00.000');
INSERT INTO t_ds_dq_rule
(id, "name", "type", user_id, create_time, update_time)
VALUES(10, '$t(table_count_check)', 0, 1, '2020-01-12 00:00:00.000', '2020-01-12 00:00:00.000');

INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(1, 1, 'SELECT COUNT(*) AS nulls FROM null_items', 'null_count', 1, false, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(2, 1, 'SELECT COUNT(*) AS total FROM ${src_table} WHERE (${src_filter})', 'total_count', 2, false, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(3, 1, 'SELECT COUNT(*) AS miss from miss_items', 'miss_count', 1, false, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(4, 1, 'SELECT COUNT(*) AS valids FROM invalid_length_items', 'invalid_length_count', 1, false, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(5, 1, 'SELECT COUNT(*) AS total FROM ${target_table} WHERE (${target_filter})', 'total_count', 2, false, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(6, 1, 'SELECT ${src_field} FROM ${src_table} group by ${src_field} having count(*) > 1', 'duplicate_items', 0, true, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(7, 1, 'SELECT COUNT(*) AS duplicates FROM duplicate_items', 'duplicate_count', 1, false, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(8, 1, 'SELECT ${src_table}.* FROM (SELECT * FROM ${src_table} WHERE (${src_filter})) ${src_table} LEFT JOIN (SELECT * FROM ${target_table} WHERE (${target_filter})) ${target_table} ON ${on_clause} WHERE ${where_clause}', 'miss_items', 0, true, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(9, 1, 'SELECT * FROM ${src_table} WHERE (${src_field} not regexp ''${regexp_pattern}'') AND (${src_filter}) ', 'regexp_items', 0, true, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(10, 1, 'SELECT COUNT(*) AS regexps FROM regexp_items', 'regexp_count', 1, false, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(11, 1, 'SELECT * FROM ${src_table} WHERE (to_unix_timestamp(${src_field}, ''${datetime_format}'')-to_unix_timestamp(''${deadline}'', ''${datetime_format}'') <= 0) AND (to_unix_timestamp(${src_field}, ''${datetime_format}'')-to_unix_timestamp(''${begin_time}'', ''${datetime_format}'') >= 0) AND (${src_filter}) ', 'timeliness_items', 0, true, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(12, 1, 'SELECT COUNT(*) AS timeliness FROM timeliness_items', 'timeliness_count', 1, false, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(13, 1, 'SELECT * FROM ${src_table} where (${src_field} not in ( ${enum_list} ) or ${src_field} is null) AND (${src_filter}) ', 'enum_items', 0, true, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(14, 1, 'SELECT COUNT(*) AS enums FROM enum_items', 'enum_count', 1, false, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(15, 1, 'SELECT COUNT(*) AS total FROM ${src_table} WHERE (${src_filter})', 'table_count', 1, false, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(16, 1, 'SELECT * FROM ${src_table} WHERE (${src_field} is null or ${src_field} = '''') AND (${src_filter})', 'null_items', 0, true, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_execute_sql
(id, "index", "sql", table_alias, "type", is_error_output_sql, create_time, update_time)
VALUES(17, 1, 'SELECT * FROM ${src_table} WHERE (length(${src_field}) ${logic_operator} ${field_length}) AND (${src_filter})', 'invalid_length_items', 0, true, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');

INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(1, 'src_connector_type', 'select', '$t(src_connector_type)', '', '[{"label":"HIVE","value":"HIVE"},{"label":"JDBC","value":"JDBC"}]', 'please select source connector type', 2, 2, 0, 1, 1, 1, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(2, 'src_datasource_id', 'select', '$t(src_datasource_id)', '', NULL, 'please select source datasource id', 1, 2, 0, 1, 1, 1, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(3, 'src_table', 'select', '$t(src_table)', NULL, NULL, 'Please enter source table name', 0, 0, 0, 1, 1, 1, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(4, 'src_filter', 'input', '$t(src_filter)', NULL, NULL, 'Please enter filter expression', 0, 3, 0, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(5, 'src_field', 'select', '$t(src_field)', NULL, NULL, 'Please enter column, only single column is supported', 0, 0, 0, 1, 1, 0, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(6, 'statistics_name', 'input', '$t(statistics_name)', NULL, NULL, 'Please enter statistics name, the alias in statistics execute sql', 0, 0, 1, 0, 0, 0, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(7, 'check_type', 'select', '$t(check_type)', '0', '[{"label":"Expected - Actual","value":"0"},{"label":"Actual - Expected","value":"1"},{"label":"Actual / Expected","value":"2"},{"label":"(Expected - Actual) / Expected","value":"3"}]', 'please select check type', 0, 0, 3, 1, 1, 1, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(8, 'operator', 'select', '$t(operator)', '0', '[{"label":"=","value":"0"},{"label":"<","value":"1"},{"label":"<=","value":"2"},{"label":">","value":"3"},{"label":">=","value":"4"},{"label":"!=","value":"5"}]', 'please select operator', 0, 0, 3, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(9, 'threshold', 'input', '$t(threshold)', NULL, NULL, 'Please enter threshold, number is needed', 0, 2, 3, 1, 1, 0, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(10, 'failure_strategy', 'select', '$t(failure_strategy)', '0', '[{"label":"Alert","value":"0"},{"label":"Block","value":"1"}]', 'please select failure strategy', 0, 0, 3, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(11, 'target_connector_type', 'select', '$t(target_connector_type)', '', '[{"label":"HIVE","value":"HIVE"},{"label":"JDBC","value":"JDBC"}]', 'Please select target connector type', 2, 0, 0, 1, 1, 1, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(12, 'target_datasource_id', 'select', '$t(target_datasource_id)', '', NULL, 'Please select target datasource', 1, 2, 0, 1, 1, 1, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(13, 'target_table', 'select', '$t(target_table)', NULL, NULL, 'Please enter target table', 0, 0, 0, 1, 1, 1, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(14, 'target_filter', 'input', '$t(target_filter)', NULL, NULL, 'Please enter target filter expression', 0, 3, 0, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(15, 'mapping_columns', 'group', '$t(mapping_columns)', NULL, '[{"field":"src_field","props":{"placeholder":"Please input src field","rows":0,"disabled":false,"size":"small"},"type":"input","title":"src_field"},{"field":"operator","props":{"placeholder":"Please input operator","rows":0,"disabled":false,"size":"small"},"type":"input","title":"operator"},{"field":"target_field","props":{"placeholder":"Please input target field","rows":0,"disabled":false,"size":"small"},"type":"input","title":"target_field"}]', 'please enter mapping columns', 0, 0, 0, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(16, 'statistics_execute_sql', 'textarea', '$t(statistics_execute_sql)', NULL, NULL, 'Please enter statistics execute sql', 0, 3, 0, 1, 1, 0, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(17, 'comparison_name', 'input', '$t(comparison_name)', NULL, NULL, 'Please enter comparison name, the alias in comparison execute sql', 0, 0, 0, 0, 0, 0, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(18, 'comparison_execute_sql', 'textarea', '$t(comparison_execute_sql)', NULL, NULL, 'Please enter comparison execute sql', 0, 3, 0, 1, 1, 0, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(19, 'comparison_type', 'select', '$t(comparison_type)', '', NULL, 'Please enter comparison title', 3, 0, 2, 1, 0, 1, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(20, 'writer_connector_type', 'select', '$t(writer_connector_type)', '', '[{"label":"MYSQL","value":"0"},{"label":"POSTGRESQL","value":"1"}]', 'please select writer connector type', 0, 2, 0, 1, 1, 1, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(21, 'writer_datasource_id', 'select', '$t(writer_datasource_id)', '', NULL, 'please select writer datasource id', 1, 2, 0, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(22, 'target_field', 'select', '$t(target_field)', NULL, NULL, 'Please enter column, only single column is supported', 0, 0, 0, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(23, 'field_length', 'input', '$t(field_length)', NULL, NULL, 'Please enter length limit', 0, 3, 0, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(24, 'logic_operator', 'select', '$t(logic_operator)', '=', '[{"label":"=","value":"="},{"label":"<","value":"<"},{"label":"<=","value":"<="},{"label":">","value":">"},{"label":">=","value":">="},{"label":"<>","value":"<>"}]', 'please select logic operator', 0, 0, 3, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(25, 'regexp_pattern', 'input', '$t(regexp_pattern)', NULL, NULL, 'Please enter regexp pattern', 0, 0, 0, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(26, 'deadline', 'input', '$t(deadline)', NULL, NULL, 'Please enter deadline', 0, 0, 0, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(27, 'datetime_format', 'input', '$t(datetime_format)', NULL, NULL, 'Please enter datetime format', 0, 0, 0, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(28, 'enum_list', 'input', '$t(enum_list)', NULL, NULL, 'Please enter enumeration', 0, 0, 0, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_dq_rule_input_entry
(id, field, "type", title, value, "options", placeholder, option_source_type, value_type, input_type, is_show, can_edit, is_emit, is_validate, create_time, update_time)
VALUES(29, 'begin_time', 'input', '$t(begin_time)', NULL, NULL, 'Please enter begin time', 0, 0, 0, 1, 1, 0, 0, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');

INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(1, 1, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(3, 5, 4, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(2, 3, 3, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(4, 3, 8, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(5, 6, 6, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(6, 6, 7, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(7, 7, 9, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(8, 7, 10, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(9, 8, 11, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(10, 8, 12, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(11, 9, 13, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(12, 9, 14, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(13, 10, 15, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(14, 1, 16, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_execute_sql
(id, rule_id, execute_sql_id, create_time, update_time)
VALUES(15, 5, 17, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');

INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(1, 1, 1, NULL, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(2, 1, 2, NULL, 2, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(3, 1, 3, NULL, 3, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(4, 1, 4, NULL, 4, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(5, 1, 5, NULL, 5, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(6, 1, 6, '{"statistics_name":"null_count.nulls"}', 6, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(7, 1, 7, NULL, 7, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(8, 1, 8, NULL, 8, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(9, 1, 9, NULL, 9, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(10, 1, 10, NULL, 10, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(11, 1, 17, '', 11, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(12, 1, 19, NULL, 12, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(13, 2, 1, NULL, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(14, 2, 2, NULL, 2, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(15, 2, 3, NULL, 3, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(16, 2, 6, '{"is_show":"true","can_edit":"true"}', 4, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(17, 2, 16, NULL, 5, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(18, 2, 4, NULL, 6, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(19, 2, 7, NULL, 7, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(20, 2, 8, NULL, 8, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(21, 2, 9, NULL, 9, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(22, 2, 10, NULL, 10, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(24, 2, 19, NULL, 12, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(25, 3, 1, NULL, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(26, 3, 2, NULL, 2, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(27, 3, 3, NULL, 3, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(28, 3, 4, NULL, 4, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(29, 3, 11, NULL, 5, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(30, 3, 12, NULL, 6, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(31, 3, 13, NULL, 7, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(32, 3, 14, NULL, 8, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(33, 3, 15, NULL, 9, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(34, 3, 7, NULL, 10, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(35, 3, 8, NULL, 11, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(36, 3, 9, NULL, 12, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(37, 3, 10, NULL, 13, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(38, 3, 17, '{"comparison_name":"total_count.total"}', 14, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(39, 3, 19, NULL, 15, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(40, 4, 1, NULL, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(41, 4, 2, NULL, 2, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(42, 4, 3, NULL, 3, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(43, 4, 6, '{"is_show":"true","can_edit":"true"}', 4, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(44, 4, 16, NULL, 5, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(45, 4, 11, NULL, 6, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(46, 4, 12, NULL, 7, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(47, 4, 13, NULL, 8, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(48, 4, 17, '{"is_show":"true","can_edit":"true"}', 9, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(49, 4, 18, NULL, 10, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(50, 4, 7, NULL, 11, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(51, 4, 8, NULL, 12, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(52, 4, 9, NULL, 13, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(53, 4, 10, NULL, 14, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(62, 3, 6, '{"statistics_name":"miss_count.miss"}', 18, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(63, 5, 1, NULL, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(64, 5, 2, NULL, 2, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(65, 5, 3, NULL, 3, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(66, 5, 4, NULL, 4, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(67, 5, 5, NULL, 5, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(68, 5, 6, '{"statistics_name":"invalid_length_count.valids"}', 6, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(69, 5, 24, NULL, 7, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(70, 5, 23, NULL, 8, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(71, 5, 7, NULL, 9, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(72, 5, 8, NULL, 10, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(73, 5, 9, NULL, 11, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(74, 5, 10, NULL, 12, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(75, 5, 17, '', 13, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(76, 5, 19, NULL, 14, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(79, 6, 1, NULL, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(80, 6, 2, NULL, 2, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(81, 6, 3, NULL, 3, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(82, 6, 4, NULL, 4, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(83, 6, 5, NULL, 5, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(84, 6, 6, '{"statistics_name":"duplicate_count.duplicates"}', 6, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(85, 6, 7, NULL, 7, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(86, 6, 8, NULL, 8, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(87, 6, 9, NULL, 9, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(88, 6, 10, NULL, 10, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(89, 6, 17, '', 11, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(90, 6, 19, NULL, 12, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(93, 7, 1, NULL, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(94, 7, 2, NULL, 2, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(95, 7, 3, NULL, 3, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(96, 7, 4, NULL, 4, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(97, 7, 5, NULL, 5, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(98, 7, 6, '{"statistics_name":"regexp_count.regexps"}', 6, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(99, 7, 25, NULL, 5, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(100, 7, 7, NULL, 7, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(101, 7, 8, NULL, 8, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(102, 7, 9, NULL, 9, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(103, 7, 10, NULL, 10, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(104, 7, 17, NULL, 11, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(105, 7, 19, NULL, 12, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(108, 8, 1, NULL, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(109, 8, 2, NULL, 2, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(110, 8, 3, NULL, 3, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(111, 8, 4, NULL, 4, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(112, 8, 5, NULL, 5, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(113, 8, 6, '{"statistics_name":"timeliness_count.timeliness"}', 6, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(114, 8, 26, NULL, 8, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(115, 8, 27, NULL, 9, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(116, 8, 7, NULL, 10, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(117, 8, 8, NULL, 11, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(118, 8, 9, NULL, 12, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(119, 8, 10, NULL, 13, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(120, 8, 17, NULL, 14, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(121, 8, 19, NULL, 15, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(124, 9, 1, NULL, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(125, 9, 2, NULL, 2, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(126, 9, 3, NULL, 3, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(127, 9, 4, NULL, 4, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(128, 9, 5, NULL, 5, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(129, 9, 6, '{"statistics_name":"enum_count.enums"}', 6, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(130, 9, 28, NULL, 7, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(131, 9, 7, NULL, 8, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(132, 9, 8, NULL, 9, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(133, 9, 9, NULL, 10, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(134, 9, 10, NULL, 11, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(135, 9, 17, NULL, 12, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(136, 9, 19, NULL, 13, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(139, 10, 1, NULL, 1, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(140, 10, 2, NULL, 2, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(141, 10, 3, NULL, 3, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(142, 10, 4, NULL, 4, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(143, 10, 6, '{"statistics_name":"table_count.total"}', 6, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(144, 10, 7, NULL, 7, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(145, 10, 8, NULL, 8, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(146, 10, 9, NULL, 9, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(147, 10, 10, NULL, 10, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(148, 10, 17, NULL, 11, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(149, 10, 19, NULL, 12, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
INSERT INTO t_ds_relation_rule_input_entry
(id, rule_id, rule_input_entry_id, values_map, "index", create_time, update_time)
VALUES(150, 8, 29, NULL, 7, '2021-03-03 11:31:24.000', '2021-03-03 11:31:24.000');
