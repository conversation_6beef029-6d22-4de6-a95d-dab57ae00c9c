CREATE TABLE IF NOT EXISTS data_lineage_field_mapping
(
	id SERIAL  PRIMARY KEY,
	source_element_id BIGINT NOT NULL ,
	source_datasource_type VA<PERSON>HAR(64),
	source_database_name VA<PERSON>HAR(128),
	source_table_name <PERSON><PERSON><PERSON><PERSON>(255),
	source_field_name VA<PERSON><PERSON><PERSON>(100),
	source_unique_id VARCHAR(100) DEFAULT '',
	target_element_id BIGINT NOT NULL ,
	target_datasource_type VA<PERSON>HA<PERSON>(64),
	target_database_name VA<PERSON>HAR(128),
	target_table_name VARCHAR(255),
	target_field_name VARCHAR(100),
	target_unique_id VARCHAR(100) DEFAULT '',
	association_type INTEGER DEFAULT 0,
	mapping_type INTEGER,
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	statistics_date VARCHAR(50),
	enable_state INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS data_lineage_relation_statistic
(
	id SERIAL  PRIMARY KEY,
	clickhouse_total BIGINT DEFAULT 0,
	mysql_total BIGINT DEFAULT 0,
	hive_total BIGINT DEFAULT 0,
	es_total BIGINT DEFAULT 0,
	clickhouse_high_value BIGINT DEFAULT 0,
	mysql_high_value BIGINT DEFAULT 0,
	hive_high_value BIGINT DEFAULT 0,
	es_high_value BIGINT DEFAULT 0,
	table_citation_count INTEGER DEFAULT 0,
	clickhouse_low_value BIGINT DEFAULT 0,
	mysql_low_value BIGINT DEFAULT 0,
	hive_low_value BIGINT DEFAULT 0,
	es_low_value BIGINT DEFAULT 0,
	tenant_id BIGINT NOT NULL ,
	statistics_date VARCHAR(50),
	hdfs_total BIGINT DEFAULT 0,
	hdfs_high_value BIGINT DEFAULT 0,
	hdfs_low_value BIGINT DEFAULT 0
);

CREATE TABLE IF NOT EXISTS data_lineage_relation_table
(
	id VARCHAR PRIMARY KEY,
	element_id BIGINT NOT NULL ,
	datasource_type VARCHAR(64),
	database_name VARCHAR(128),
	table_name VARCHAR(255),
	unique_id VARCHAR(100) DEFAULT '',
	cn_name VARCHAR(100) DEFAULT '',
	owner VARCHAR(100) DEFAULT '',
	table_modify_time VARCHAR(100) DEFAULT '',
	upstream_table_num INTEGER DEFAULT 0,
	downstream_table_num INTEGER DEFAULT 0,
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	statistics_date VARCHAR(50),
	enable_state INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS data_lineage_relation_table_column
(
	id SERIAL  PRIMARY KEY,
	source_table_id VARCHAR(50) NOT NULL ,
	source_column_name VARCHAR(255),
	target_table_id VARCHAR(50) NOT NULL ,
	target_column_name VARCHAR(255),
	statistics_date VARCHAR(50),
	enable_state INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS data_lineage_relation_table_island
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT NOT NULL ,
	datasource_type VARCHAR(64),
	database_name VARCHAR(128),
	table_name VARCHAR(255),
	unique_id VARCHAR(100) DEFAULT '',
	cn_name VARCHAR(100) DEFAULT '',
	owner VARCHAR(100) DEFAULT '',
	table_modify_time TIMESTAMP,
	tenant_id BIGINT NOT NULL ,
	statistics_date VARCHAR(50),
	enable_state INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS data_lineage_relation_table_node
(
	id VARCHAR PRIMARY KEY,
	pre_table_id VARCHAR(50) NOT NULL ,
	next_table_id VARCHAR(50),
	node_type INTEGER DEFAULT 0,
	task_name VARCHAR(128),
	access_address VARCHAR(100),
	access_method VARCHAR(100),
	execution_method VARCHAR(100),
	task_create_time VARCHAR(100),
	task_create_user VARCHAR(100),
	project_name VARCHAR(255),
	task_type VARCHAR(255),
	task_status VARCHAR(255),
	owner VARCHAR(100),
	execution_cycle VARCHAR(100),
	dataset_classification VARCHAR(100),
	execution_result VARCHAR(100),
	api_name VARCHAR(255),
	request_type VARCHAR(100),
	number_of_calls INTEGER DEFAULT 0,
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	statistics_date VARCHAR(50),
	enable_state INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS data_lineage_relation_table_reference
(
	id SERIAL  PRIMARY KEY,
	source_table_id VARCHAR(50) NOT NULL ,
	target_table_id VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS data_lineage_relation_table_source
(
	id VARCHAR PRIMARY KEY,
	table_id VARCHAR(50) NOT NULL ,
	datasource_type VARCHAR(64),
	database_name VARCHAR(128),
	table_name VARCHAR(128),
	ip VARCHAR(100),
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	statistics_date VARCHAR(50),
	enable_state INTEGER DEFAULT 0
);



COMMENT ON COLUMN data_lineage_field_mapping.source_element_id IS '来源元数据ID';
COMMENT ON COLUMN data_lineage_field_mapping.source_datasource_type IS '来源数据源类型';
COMMENT ON COLUMN data_lineage_field_mapping.source_database_name IS '来源数据库名称';
COMMENT ON COLUMN data_lineage_field_mapping.source_table_name IS '来源表名称';
COMMENT ON COLUMN data_lineage_field_mapping.source_field_name IS '来源字段名称';
COMMENT ON COLUMN data_lineage_field_mapping.source_unique_id IS '来源元数据唯一字段';
COMMENT ON COLUMN data_lineage_field_mapping.target_element_id IS '目标元数据ID';
COMMENT ON COLUMN data_lineage_field_mapping.target_datasource_type IS '目标数据源类型';
COMMENT ON COLUMN data_lineage_field_mapping.target_database_name IS '目标数据库名称';
COMMENT ON COLUMN data_lineage_field_mapping.target_table_name IS '目标表名称';
COMMENT ON COLUMN data_lineage_field_mapping.target_field_name IS '目标字段名称';
COMMENT ON COLUMN data_lineage_field_mapping.target_unique_id IS '目标元数据唯一字段';
COMMENT ON COLUMN data_lineage_field_mapping.association_type IS '关联类型，0-自动关联，1-手动关联';
COMMENT ON COLUMN data_lineage_field_mapping.mapping_type IS '映射类型，1-同库映射，2-同表映射，3-自定义映射';
COMMENT ON COLUMN data_lineage_field_mapping.create_time IS '创建时间';
COMMENT ON COLUMN data_lineage_field_mapping.create_user IS '创建人';
COMMENT ON COLUMN data_lineage_field_mapping.update_time IS '更新时间';
COMMENT ON COLUMN data_lineage_field_mapping.update_user IS '更新人';
COMMENT ON COLUMN data_lineage_field_mapping.tenant_id IS '租户ID';
COMMENT ON COLUMN data_lineage_field_mapping.statistics_date IS '统计日期';
COMMENT ON COLUMN data_lineage_field_mapping.enable_state IS '有效状态，0-有效，1-无效';
COMMENT ON COLUMN data_lineage_relation_statistic.clickhouse_total IS 'ClickHouse总数';
COMMENT ON COLUMN data_lineage_relation_statistic.mysql_total IS 'Mysql总数';
COMMENT ON COLUMN data_lineage_relation_statistic.hive_total IS 'Hive总数';
COMMENT ON COLUMN data_lineage_relation_statistic.es_total IS 'ES总数';
COMMENT ON COLUMN data_lineage_relation_statistic.clickhouse_high_value IS 'ClickHouse高价值资产总数';
COMMENT ON COLUMN data_lineage_relation_statistic.mysql_high_value IS 'Mysql高价值资产总数';
COMMENT ON COLUMN data_lineage_relation_statistic.hive_high_value IS 'Hive高价值资产总数';
COMMENT ON COLUMN data_lineage_relation_statistic.es_high_value IS 'ES高价值资产总数';
COMMENT ON COLUMN data_lineage_relation_statistic.table_citation_count IS '表引用次数，超过该次数的表，才会被统计';
COMMENT ON COLUMN data_lineage_relation_statistic.clickhouse_low_value IS 'ClickHouse孤岛资产总数';
COMMENT ON COLUMN data_lineage_relation_statistic.mysql_low_value IS 'Mysql孤岛资产总数';
COMMENT ON COLUMN data_lineage_relation_statistic.hive_low_value IS 'Hive孤岛资产总数';
COMMENT ON COLUMN data_lineage_relation_statistic.es_low_value IS 'ES孤岛资产总数';
COMMENT ON COLUMN data_lineage_relation_statistic.tenant_id IS '租户ID';
COMMENT ON COLUMN data_lineage_relation_statistic.statistics_date IS '统计日期';
COMMENT ON COLUMN data_lineage_relation_statistic.hdfs_total IS 'HDFS总数';
COMMENT ON COLUMN data_lineage_relation_statistic.hdfs_high_value IS 'HDFS高价值资产总数';
COMMENT ON COLUMN data_lineage_relation_statistic.hdfs_low_value IS 'hdfs孤岛资产总数';
COMMENT ON COLUMN data_lineage_relation_table.element_id IS '元数据ID';
COMMENT ON COLUMN data_lineage_relation_table.datasource_type IS '数据源类型';
COMMENT ON COLUMN data_lineage_relation_table.database_name IS '数据库名称';
COMMENT ON COLUMN data_lineage_relation_table.table_name IS '表名称';
COMMENT ON COLUMN data_lineage_relation_table.unique_id IS '元数据唯一字段';
COMMENT ON COLUMN data_lineage_relation_table.cn_name IS '表中文名';
COMMENT ON COLUMN data_lineage_relation_table.owner IS '负责人';
COMMENT ON COLUMN data_lineage_relation_table.table_modify_time IS '表更新时间';
COMMENT ON COLUMN data_lineage_relation_table.upstream_table_num IS '上游表数量';
COMMENT ON COLUMN data_lineage_relation_table.downstream_table_num IS '下游表数量';
COMMENT ON COLUMN data_lineage_relation_table.create_time IS '创建时间';
COMMENT ON COLUMN data_lineage_relation_table.create_user IS '创建人';
COMMENT ON COLUMN data_lineage_relation_table.update_time IS '更新时间';
COMMENT ON COLUMN data_lineage_relation_table.update_user IS '更新人';
COMMENT ON COLUMN data_lineage_relation_table.tenant_id IS '租户ID';
COMMENT ON COLUMN data_lineage_relation_table.statistics_date IS '统计日期';
COMMENT ON COLUMN data_lineage_relation_table.enable_state IS '有效状态，0-有效，1-无效';
COMMENT ON COLUMN data_lineage_relation_table_column.source_table_id IS '源表id';
COMMENT ON COLUMN data_lineage_relation_table_column.source_column_name IS '源字段名称';
COMMENT ON COLUMN data_lineage_relation_table_column.target_table_id IS '目标表id';
COMMENT ON COLUMN data_lineage_relation_table_column.target_column_name IS '目标字段名称';
COMMENT ON COLUMN data_lineage_relation_table_column.statistics_date IS '统计日期';
COMMENT ON COLUMN data_lineage_relation_table_column.enable_state IS '有效状态，0-有效，1-无效';
COMMENT ON COLUMN data_lineage_relation_table_island.element_id IS '元数据ID';
COMMENT ON COLUMN data_lineage_relation_table_island.datasource_type IS '数据源类型';
COMMENT ON COLUMN data_lineage_relation_table_island.database_name IS '数据库名称';
COMMENT ON COLUMN data_lineage_relation_table_island.table_name IS '表名称';
COMMENT ON COLUMN data_lineage_relation_table_island.unique_id IS '元数据唯一字段';
COMMENT ON COLUMN data_lineage_relation_table_island.cn_name IS '表中文名';
COMMENT ON COLUMN data_lineage_relation_table_island.owner IS '负责人';
COMMENT ON COLUMN data_lineage_relation_table_island.table_modify_time IS '表更新时间';
COMMENT ON COLUMN data_lineage_relation_table_island.tenant_id IS '租户ID';
COMMENT ON COLUMN data_lineage_relation_table_island.statistics_date IS '统计日期';
COMMENT ON COLUMN data_lineage_relation_table_island.enable_state IS '有效状态，0-有效，1-无效';
COMMENT ON COLUMN data_lineage_relation_table_node.pre_table_id IS '前一个节点表id';
COMMENT ON COLUMN data_lineage_relation_table_node.next_table_id IS '后一个节点表id';
COMMENT ON COLUMN data_lineage_relation_table_node.node_type IS '节点类型，1-数据采集，2-数据开发，3-GPL，4-api接口';
COMMENT ON COLUMN data_lineage_relation_table_node.task_name IS '任务名称';
COMMENT ON COLUMN data_lineage_relation_table_node.access_address IS '接入地址';
COMMENT ON COLUMN data_lineage_relation_table_node.access_method IS '接入方式';
COMMENT ON COLUMN data_lineage_relation_table_node.execution_method IS '执行方式';
COMMENT ON COLUMN data_lineage_relation_table_node.task_create_time IS '任务创建时间';
COMMENT ON COLUMN data_lineage_relation_table_node.task_create_user IS '任务创建人';
COMMENT ON COLUMN data_lineage_relation_table_node.project_name IS '所属项目';
COMMENT ON COLUMN data_lineage_relation_table_node.task_type IS '任务类型';
COMMENT ON COLUMN data_lineage_relation_table_node.task_status IS '任务状态';
COMMENT ON COLUMN data_lineage_relation_table_node.owner IS '负责人';
COMMENT ON COLUMN data_lineage_relation_table_node.execution_cycle IS '执行周期';
COMMENT ON COLUMN data_lineage_relation_table_node.dataset_classification IS '数据集分类';
COMMENT ON COLUMN data_lineage_relation_table_node.execution_result IS '运行结果';
COMMENT ON COLUMN data_lineage_relation_table_node.api_name IS 'api名称';
COMMENT ON COLUMN data_lineage_relation_table_node.request_type IS '请求类型';
COMMENT ON COLUMN data_lineage_relation_table_node.number_of_calls IS '调用次数';
COMMENT ON COLUMN data_lineage_relation_table_node.create_time IS '创建时间';
COMMENT ON COLUMN data_lineage_relation_table_node.create_user IS '创建人';
COMMENT ON COLUMN data_lineage_relation_table_node.update_time IS '更新时间';
COMMENT ON COLUMN data_lineage_relation_table_node.update_user IS '更新人';
COMMENT ON COLUMN data_lineage_relation_table_node.tenant_id IS '租户ID';
COMMENT ON COLUMN data_lineage_relation_table_node.statistics_date IS '统计日期';
COMMENT ON COLUMN data_lineage_relation_table_node.enable_state IS '有效状态，0-有效，1-无效';
COMMENT ON COLUMN data_lineage_relation_table_reference.source_table_id IS '起点表id';
COMMENT ON COLUMN data_lineage_relation_table_reference.target_table_id IS '终点表id';
COMMENT ON COLUMN data_lineage_relation_table_source.table_id IS '关联的表id';
COMMENT ON COLUMN data_lineage_relation_table_source.datasource_type IS '数据源类型';
COMMENT ON COLUMN data_lineage_relation_table_source.database_name IS '数据库名称';
COMMENT ON COLUMN data_lineage_relation_table_source.table_name IS '数据表名';
COMMENT ON COLUMN data_lineage_relation_table_source.ip IS 'IP';
COMMENT ON COLUMN data_lineage_relation_table_source.create_time IS '创建时间';
COMMENT ON COLUMN data_lineage_relation_table_source.create_user IS '创建人';
COMMENT ON COLUMN data_lineage_relation_table_source.update_time IS '更新时间';
COMMENT ON COLUMN data_lineage_relation_table_source.update_user IS '更新人';
COMMENT ON COLUMN data_lineage_relation_table_source.tenant_id IS '租户ID';
COMMENT ON COLUMN data_lineage_relation_table_source.statistics_date IS '统计日期';
COMMENT ON COLUMN data_lineage_relation_table_source.enable_state IS '有效状态，0-有效，1-无效';


CREATE INDEX data_lineage_relation_table_unique_id_idx ON data_lineage_relation_table (unique_id);
CREATE INDEX data_lineage_relation_table_tenant_id_idx ON data_lineage_relation_table (tenant_id);

CREATE INDEX idx_task_id ON qua_monitor_result (task_id);
CREATE INDEX idx_unique_id ON data_lineage_relation_table_island (unique_id);
CREATE INDEX idx_source_table_id ON data_lineage_relation_table_reference (source_table_id);
CREATE INDEX idx_source_unique_id ON data_meta_relation (source_unique_id);

CREATE INDEX data_lineage_relation_table_source_statistics_date_idx ON data_lineage_relation_table_source (statistics_date);
CREATE INDEX data_lineage_relation_table_statistics_date_idx ON data_lineage_relation_table (statistics_date);
CREATE INDEX data_lineage_relation_table_node_statistics_date_idx ON data_lineage_relation_table_node (statistics_date);
CREATE INDEX data_lineage_relation_table_island_statistics_date_idx ON data_lineage_relation_table_island (statistics_date);
CREATE INDEX data_lineage_relation_table_column_source_table_id_idx ON data_lineage_relation_table_column (source_table_id, source_column_name, target_table_id, target_column_name, statistics_date);


