delete from ueba_dictionary;
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('query', '查询', 1, null, 26, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('execute', '执行', 1, null, 27, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('freeze', '冻结和解冻', 1, null, 28, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('restore', '恢复', 1, null, 29, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('login', '登录', 1, null, 1, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('logout', '退出', 1, null, 2, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('search', '查询', 1, null, 3, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('preview', '预览', 1, null, 4, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('refresh', '刷新', 1, null, 5, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('add', '新增', 1, null, 6, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('copy', '复制', 1, null, 7, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('save', '保存', 1, null, 8, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('import', '导入', 1, null, 9, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('edit', '编辑', 1, null, 10, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('setting', '配置', 1, null, 11, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('move', '移动', 1, null, 12, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('start', '启用', 1, null, 13, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('stop', '停用', 1, null, 14, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('delete', '删除', 1, null, 15, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('upload', '上传', 1, null, 16, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('download', '下载', 1, null, 17, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('saveOrUpdate', '保存更新', 1, null, 18, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('detail', '查看', 1, null, 19, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('test', '测试', 1, null, 20, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('parse', '解析', 1, null, 21, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('match', '匹配', 1, null, 22, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('startOrStop', '启动和暂停', 1, null, 23, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('reset', '重置', 1, null, 24, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('export', '导出', 1, null, 9, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('send', '发送', 1, null, 25, 'SYSTEM_LOG_OPT_TYPE', null);
INSERT INTO ueba_dictionary (key_code, value, enable, remark, sortno, type, parent_id) VALUES ('success', '成功', 1, null, 1, 'SYSTEM_LOG_LOG_RESULT', null);

delete from tb_dic;
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('租户关联区域', 'area', 0, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('配置功能', 'function', 0, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('杭州', 'HZ', 1, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('宁波', 'NB', 1, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('温州', 'WZ', 1, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('嘉兴', 'JX', 1, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('湖州', 'HZ', 1, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('绍兴', 'SX', 1, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('金华', 'JH', 1, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('舟山', 'ZS', 1, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('台州', 'TZ', 1, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('衢州', 'QZ', 1, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('丽水', 'LS', 1, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('数据采集', 'insight', 2, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('数据治理', 'data-lake-governance', 2, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('数据共享', 'data', 2, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('系统管理', 'system', 2, null, 1);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('数据开发(自研)', 'data-development-inner', 2, 'data-development', 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('数据开发(第三方)', 'data-development-outer', 2, 'data-development', 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('数据集市', 'data-mart', 2, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('AI数据分析', 'ai-data-analysis', 2, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('威胁情报', 'high-value-analysis', 2, null, 0);
INSERT INTO tb_dic (NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('高价值分析', 'high-value-analysis-plus', 2, null, 0);

delete from tb_tenant;
INSERT INTO tb_tenant (TENANT_CODE, TENANT_NAME, ACCOUNT_TYPE, CONTACT_MOBILE, DEL_FLAG, CONTACT_AREA, FUNCTIONS, RESOURCE_STATUS, CREATE_USER, CREATE_TIME, UPDATE_USER, UPDATE_TIME, ACCOUNT_NAME, ACCOUNT_PWD, TENANT_ID, DB_NAME, FA_EXTEND_INFO) VALUES ('tenantadmin', '超级管理员', '0', '***********', '0', '22', '', '0', '', '2022-07-22 21:39:23.0', '', '2022-07-22 21:39:23.0', 'tenantadmin', '4c5c6205bb2c5de0fffac65865b83594', null, null, null);

delete from ums_sys_menus;
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, 'system', '系统管理', 'system', null, '0', '0', null, '1', 70, 70, '系统', '1', null, null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, 'system', '租户管理', 'tenant-manage', null, '0', '0', 'system', '1', 1, 70, '租户管理', '1', 'system', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, 'system', '资源管理', 'cluster-resource-manage', null, '0', '0', 'system', '1', 1, 80, '资源管理', '1', 'system', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'system', '集群管理', 'cluster-manage', null, '0', '0', 'cluster-resource-manage', '1', 1, 10, '集群管理', '1', 'resource-manage', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'system', '数仓管理', 'clickhouse-manage', null, '0', '0', 'cluster-resource-manage', '1', 1, 20, '数仓管理', '1', 'resource-manage', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'system', '数仓详情', 'clickhouse-detail', null, '0', '0', 'cluster-resource-manage', '1', 1, 30, '数仓详情', '1', 'resource-manage', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'system', 'ES管理', 'elasticsearch-manage', null, '0', '0', 'cluster-resource-manage', '1', 1, 40, 'ES管理', '1', 'resource-manage', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'system', 'ES详情', 'elasticsearch-detail', null, '0', '0', 'cluster-resource-manage', '1', 1, 50, 'ES详情', '1', 'resource-manage', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'system', 'Kafka管理', 'kafka-manage', null, '0', '0', 'cluster-resource-manage', '1', 1, 60, 'Kafka管理', '1', 'resource-manage', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'system', 'Kafka详情', 'kafka-detail', null, '0', '0', 'cluster-resource-manage', '1', 1, 70, 'Kafka详情', '1', 'resource-manage', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'system', 'MySQL管理', 'mysql-manage', null, '0', '0', 'cluster-resource-manage', '1', 1, 80, 'MySQL管理', '1', 'resource-manage', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'system', 'MySQL详情', 'mysql-detail', null, '0', '0', 'cluster-resource-manage', '1', 1, 90, 'MySQL详情', '1', 'resource-manage', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'system', 'HDFS管理', 'hdfs-manage', null, '0', '0', 'cluster-resource-manage', '1', 1, 100, 'HDFS管理', '1', 'resource-manage', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'system', 'HDFS详情', 'hdfs-detail', null, '0', '0', 'cluster-resource-manage', '1', 1, 110, 'HDFS详情', '1', 'resource-manage', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, 'system', '授权管理', 'license-manage', null, '0', '0', 'system', '1', 90, 90, '授权管理', '1', 'system', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, 'data-lake-home', '首页', 'data-lake-home', null, '0', '0', null, '1', 10, 10, '系统', '1', null, null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 3, 'data-lake-governance', '规则管理', 'data-lake-governance-rule-manage', null, '0', '0', 'data-lake-governance-quality', '1', 40, 40, '规则管理', '1', 'data-lake-governance-quality', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, '4a-user-manage', '4A用户管理', '4a-user-manage', null, '0', '0', null, '1', 20, 20, '4A用户管理', '1', null, null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, 'system', '配置管理', 'config-manage', null, '0', '0', 'system', '1', 30, 30, '配置管理', '1', 'system', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, 'system', '用户认证', 'user-auth-config', null, '0', '0', 'config-manage', '1', 10, 10, '用户认证', '1', 'config-manage', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, 'system', '用户认证配置', 'user-auth-detail', null, '0', '0', 'user-auth-config', '1', 10, 10, '用户认证配置', '1', 'user-auth-config', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, 'system', '系统日志', 'sys-log', null, '0', '0', 'system', '1', 50, 50, '系统日志', '1', 'system', null, null, null, null, null, null, '0', 0);
INSERT INTO ums_sys_menus (menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 1, 'system', '操作审计', 'operation-audit', null, '0', '0', 'sys-log', '1', 10, 10, '操作审计', '1', 'sys-log', null, null, null, null, null, null, '0', 0);

delete from qua_monitor_rule_type;
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('NULLVALUE', '空值检查', 'unionNull', '勾选表示所有的字段不能同时为空，不勾选表示每个字段都不能为空', 'checkbox', '1', '', '', '0', 1, '用于检查字段是否为空');
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('RANGEVALUE', '值域检查', 'rangeValue', '值域范围(字符)', 'input', '1', '', '', '0', 1, '用于检查关键指标取值范围');
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('NORM', '规范检查', 'normType', '规则类型', 'select', '1', '', 'norm', '0', 1, '检查字符型字段的类型是否规范');
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('LOGIC', '逻辑检查', 'logicMath', '检查公式', 'input', '1', '', '', '0', 1, null);
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('REPEAT', '重复数据检查', '', '', '', '', '', '', '0', 1, '检查一张表内的重复数据');
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('TIMELY', '及时性检查', 'days', '最大误差天数', 'input', '1', '', '', '0', 1, '用于检查数据上报的及时性');
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('QUOTE', '引用完整性检查', 'quoteTable', '比对表', 'select', '1', '', '', '0', 1, '用于检查想对比照表数据是否有效');
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('QUOTE', '引用完整性检查', 'quoteColumn', '比对字段', 'select', '1', '', '', '1', 1, '用于检查想对比照表数据是否有效');
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('FLUCTUATION', '波动检查', 'baseLine', '波动基线值', 'input', '1', '', '', '0', 1, '用于检查指标值的波动范围');
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('FLUCTUATION', '波动检查', 'fluctuationRange', '波动范围', 'input', '1', '', '', '0', 1, '用于检查指标值的波动范围');
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('STANDARD', '数据标准检查', 'standards', '选择规范', 'select', '1', '', '', '0', 1, null);
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('STANDARD', '数据标准检查', 'checkType', '检查类型', 'select', '1', '', '/lake/api/dataQuality/rule/normType', '0', 1, null);
INSERT INTO qua_monitor_rule_type (type_code, type_name, config_key, config_name, config_type, is_required, reg_pattern, interface_name, multi_select, sort_no, type_desc) VALUES ('SQL', '自定义SQL', '', '', '', '', '', '', '0', 1, '自定义SQL');

delete from qua_internal_model;
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('及时率检查', 'timely', '统计表中某两时间字段下数据的最大时差,检查其合格比率的是否异常', '①数据源筛选处理后，计算每一行数据中数据时差绝对值:数据时差绝对值=“时间字段一”一“时间字段二”。<br/>②将“数据时差”和“最大时差”比较，大于等于“最大时差”则不符合及时性要求，小于“最大时差”则符合足及时性要求。<br/>③计算总体数据的符合及时性要求的占比，即数据及时率:数据及时率=符合及时性的数据条数/统计数据总条数*100%。<br/>④将“数据及时率”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>⑤基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>⑥基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'timeliness', 'field', '被减时间字段,减去时间字段,时差阈值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('重复率检查', 'repeatRate', '统计表中某一字段下数据的重复比率,检查其重复比率是否异常', '①数据源筛选处理后，计算“统计字段”所有行数据中数据重复率:重复率=重复的数据行数/总的数据行数。<br/>②将“重复率”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>③基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>④基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'uniqueness', 'field', '被减时间字段,减去时间字段,时差阈值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('重复行数检查', 'repeatLine', '统计表中某一字段下数据的重复行数,检查其重复行数是否异常', '①数据源筛选处理后，计算“统计字段”所有行数据中数据重复行数。<br/>②将“重复行数”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>③基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>④基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'uniqueness', 'field', '被检查字段,阈值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('空值率检查', 'nullRate', '统计表中某一字段下数据空值情况,检查其空值比率是否异常', '①数据源筛选处理后，计算“统计字段”所有行数据中数据空值率:空值率=为空的数据行数/总数据行数。<br/>②将“空值率”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>③基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>④基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'integrality', 'field', '被检查字段,阈值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('空值行数检查', 'nullLine', '统计表中某一字段下数据空值情况,检查其空值行数是否异常', '①数据源筛选处理后，计算“统计字段”所有行数据中数据空值行数。<br/>②将“空值行数”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>③基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>④基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'integrality', 'field', '被检查字段,阈值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('逻辑通过数检查', 'logicLine', '统计表中某两字段下数据在指定条件下的逻辑判断(AND OR NOT),检查通过逻辑判断数量是否异常', '①数据源筛选处理后，将“字段(第一个)”并与“比较值(第一个)”按照“比较方法(第一个)”进行比较，判断是否通过。<br/>②将“字段(第二个)”并与“比较值(第二个)”按照“比较方法(第二个)”进行比较，判断是否通过。<br/>③上述两个步骤的结果进行“逻辑类型”(AND、OR、NOT)判断，满足则逻辑检查通过，否则逻辑检查不通过。<br/>④计算所有行数据的逻辑检查通过总数与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>⑤基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>⑥基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'uniformity', 'field', '被检查字段,阈值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('逻辑通过率检查', 'logicRate', '统计表中某两字段下数据在指定条件下的逻辑判断(AND OR NOT),检查通过逻辑判断比率是否异常', '①数据源筛选处理后，将“字段(第一个)”并与“比较值(第一个)”按照“比较方法(第一个)”进行比较，判断是否通过。<br/>②将“字段(第二个)”并与“比较值(第二个)”按照“比较方法(第二个)”进行比较，判断是否通过。<br/>③上述两个步骤的结果进行“逻辑类型”(AND、OR、NOT)判断，满足则逻辑检查通过，否则逻辑检查不通过。<br/>④计算所有行数据的逻辑检查通过率:逻辑检查通过率=通过逻辑检查的行数/总检查的行数*100%。<br/>⑤基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>⑥基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'uniformity', 'field', '被检查字段,阈值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('长度规范率检查', 'lengthRate', '统计表中某一字段下数据长度规范情况,检查其符合规范比率是否异常', '①数据源筛选处理后，检查“统计字段”的长度是否规范:等于“标准长度”则通过检查，否则不通过。<br/>②“标准长度”来源于“数据标准”中的数据长度或者自定义的“长度”。<br/>③计算“统计字段”所有行数据中长度规范率:长度规范率=通过检查的数据行数/总数据行数。<br/>④将“长度规范率”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>⑤基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>⑥基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'effectiveness', 'field', '被检查字段,阈值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('长度规范行数检查', 'lengthLine', '统计表中某一字段下数据长度规范情况,检查其符合规范行数是否异常', '①数据源筛选处理后，检查“统计字段”的长度是否规范:等于“标准长度”则通过检查，否则不通过。<br/>②“标准长度”来源于“数据标准”中的数据长度或者自定义的“长度”。<br/>③计算“统计字段”所有行数据中满足规范的行数,与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>④基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>⑤基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'effectiveness', 'field', '被检查字段,阈值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('格式规范率检查', 'formatRate', '统计表中某一字段下数据格式规范情况，检查其符合规范比率是否异常', '①数据源筛选处理后，检查“统计字段”的格式是否规范:等于“标准格式”要求则通过检查，否则不通过。<br/>②“标准格式”来源于“数据标准”中的数据格式或者“自定义格式”。<br/>③计算“统计字段”所有行数据的格式规范率:格式规范率=通过检查的数据行数/总数据行数。<br/>④将“格式规范率”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>⑤基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>⑥基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'effectiveness', 'field', '被检查字段,阈值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('格式规范行数检查', 'formatLine', '统计表中某一字段下数据格式规范情况,检查其符合规范行数是否异常', '①数据源筛选处理后，检查“统计字段”的格式是否规范:等于“标准格式”要求则通过检查，否则不通过。<br/>②“标准格式”来源于“数据标准”中的数据格式或者“自定义格式”。<br/>③计算“统计字段”所有行数据的满足规范总行数,与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>④基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>⑤基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'effectiveness', 'field', '被检查字段,阈值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('值域通过数检查', 'rangeLine', '统计表中某一字段下数据满足值域的情况,检查其合格的数量是否异常', '①数据源筛选处理后，将每一行数据与“值域”比较，满足则通过，否则不通过。<br/>②值域来自数据标准下的数据字典或者“自定义”。<br/>③计算所有数据的通过数量,与“阈值”按照“阈值比较方去”进行比较，满足则异常，否则正常。<br/>④基于“权重”计算本模板打分占比:模板打分占比=本板权重/所有模板权重和*100%。<br/>⑤基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'precision', 'field', '规范类型,被检查字段,统计结果类型,阈值值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('值域通过率检查', 'rangeRate', '统计表中某一字段下数据满足值域的情况,检查其合格比率是否异常', '①数据源筛选处理后，将每一行数据与“值域”比较，满足则通过，否则不通过。<br/>②值域来自数据标准下的数据字典或者“自定义”。<br/>③计算所有数据的通过率:通过率=通过的行数/数据检查总行数*100%。<br/>④将“通过率”与“阈值”按照“阈值比较方法”进行比较,满足则异常，否则正常。<br/>⑤基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>⑥基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'precision', 'field', '规范类型,被检查字段,统计结果类型,阈值值', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('表大小波动率检查', 'tableSize', '统计表磁盘空间大小波动率,检查波动率是否异常', '①统计所选表的实际磁盘空间大小(单位是MB)，得到表磁盘空间大小”。<br/>②将“表磁盘空间大小”和“磁盘空间标准值”计算波动率表大小波动率=(表的磁盘空间大小-表磁盘空间标准值)/表磁盘空间标准值。<br/>③表磁盘空间标准值可来自“自定义”或“动态阈值”。<br/>④将“表大小波动率”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>⑤基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>⑥基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'integrality', 'table', '被检查的表,波动类型,引用的动态阈值基线', null, '1', null, null, null, null, null);
INSERT INTO qua_internal_model (name, model_code, model_desc, model_calc, dimensions, audit_object, request_param, custom_sql, model_type, create_time, create_user, update_time, update_user, tenant_id) VALUES ('表行数波动率检查', 'tableLine', '统计表行数波动率,检查波动率是否异常', '①统计所选表的行数，得到“表行数”<br/>②将“表行数”和“表行数标准值”计算波动率:值。表行数波动率=(表行数-表行数标准值)/表行数标准<br/>③将“表行数波动率”与“阈值”按照“阈值比较方法”进行北较，:满足则异常，否则正常。<br/>④基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>⑤基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'integrality', 'table', '被检查的表,波动类型,引用的动态阈值基线', null, '1', null, null, null, null, null);

