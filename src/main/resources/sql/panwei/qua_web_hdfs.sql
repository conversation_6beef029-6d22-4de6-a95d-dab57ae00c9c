CREATE TABLE IF NOT EXISTS qua_wab_element_hdfs
(
	id SERIAL  PRIMARY KEY,
	element_type VARCHAR(20),
	element_name VARCHAR(100) NOT NULL ,
	host VARCHAR(100),
	port INTEGER,
	hdfs_dir VARCHAR(1000),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60) NOT NULL ,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60) NOT NULL ,
	tenant_id BIGINT NOT NULL ,
	flag SMALLINT DEFAULT 1,
	kbs_enable INTEGER DEFAULT 0,
	kbs_account VARCHAR(100),
	key_tab_path VARCHAR(512),
	krb5_conf_path VARCHAR(512),
	jaas_conf_path VARCHAR(512)
);

CREATE TABLE IF NOT EXISTS qua_web_hdfs_element_detail_dir
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	parent_dir_path VARCHAR(1000),
	dir_path VARCHAR(1000),
	dir_name <PERSON><PERSON><PERSON><PERSON>(255),
	dir_name_cn VARCHAR(255),
	dir_describe VARCHAR(255),
	dir_owner VARCHAR(255),
	dir_group VARCHAR(255),
	dir_authority VARCHAR(100),
	storage_size BIGINT,
	key_words VARCHAR(1000),
	is_sensitive SMALLINT DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	is_available SMALLINT DEFAULT 1,
	ext_attrs VARCHAR(1024),
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	last_viewed_time VARCHAR(50),
	meta_source INTEGER DEFAULT 1
);

CREATE TABLE IF NOT EXISTS qua_web_hdfs_element_detail_file
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	file_name VARCHAR(255),
	file_name_cn VARCHAR(255),
	file_describe VARCHAR(255),
	file_owner VARCHAR(255),
	file_group VARCHAR(255),
	file_authority VARCHAR(100),
	storage_size BIGINT,
	key_words VARCHAR(1000),
	is_sensitive SMALLINT DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	is_available SMALLINT DEFAULT 1,
	ext_attrs VARCHAR(1024),
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	last_viewed_time VARCHAR(50),
	meta_source INTEGER DEFAULT 1,
	dir_path VARCHAR(255)
);



COMMENT ON COLUMN qua_wab_element_hdfs.element_type IS '元数据类型 ：HDFS';
COMMENT ON COLUMN qua_wab_element_hdfs.element_name IS '元数据名称/目录名称';
COMMENT ON COLUMN qua_wab_element_hdfs.host IS 'host';
COMMENT ON COLUMN qua_wab_element_hdfs.port IS 'port';
COMMENT ON COLUMN qua_wab_element_hdfs.hdfs_dir IS 'hdfs目录地址';
COMMENT ON COLUMN qua_wab_element_hdfs.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_wab_element_hdfs.flag IS '是否有效，0无效，1有效，默认1';
COMMENT ON COLUMN qua_wab_element_hdfs.kbs_enable IS '是否开启kbs认证，0-不开启(默认)，1-开启';
COMMENT ON COLUMN qua_wab_element_hdfs.kbs_account IS 'KBS账号';
COMMENT ON COLUMN qua_wab_element_hdfs.key_tab_path IS 'keytab文件路径';
COMMENT ON COLUMN qua_wab_element_hdfs.krb5_conf_path IS 'krb5conf文件路径';
COMMENT ON COLUMN qua_wab_element_hdfs.jaas_conf_path IS 'jass文件路径';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.parent_dir_path IS '上级目录地址';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.dir_path IS '目录实际地址';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.dir_name IS '目录名称';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.dir_name_cn IS '目录中文名称';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.dir_describe IS '目录业务描述';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.dir_owner IS '目录业务负责人';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.dir_group IS '群组';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.dir_authority IS '权限';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.storage_size IS '存储大小，单位字节';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.key_words IS '关键词，["a","b"]';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.is_sensitive IS '是否为敏感，0非，1是，默认不是';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.is_available IS '是否可用，0不可用，默认为1，可用';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.ext_attrs IS '自定义属性';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.last_viewed_time IS '最后查看时间';
COMMENT ON COLUMN qua_web_hdfs_element_detail_dir.meta_source IS '元数据来源，1-自建，2-数据建模';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.file_name IS '文件名称';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.file_name_cn IS '中文名称';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.file_describe IS '业务描述';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.file_owner IS '业务负责人';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.file_group IS '群组';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.file_authority IS '权限';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.storage_size IS '存储大小，单位字节';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.key_words IS '关键词，["a","b"]';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.is_sensitive IS '是否为敏感，0非，1是，默认不是';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.is_available IS '是否可用，0不可用，默认为1，可用';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.ext_attrs IS '自定义属性';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.last_viewed_time IS '最后查看时间';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.meta_source IS '元数据来源，1-自建，2-数据建模';
COMMENT ON COLUMN qua_web_hdfs_element_detail_file.dir_path IS '目录地址';


CREATE INDEX hdfs_detail_index_idx ON qua_web_hdfs_element_detail_dir(element_id,dir_name);
CREATE INDEX hdfs_idx_4_idx ON qua_web_hdfs_element_detail_dir(dir_name);
CREATE INDEX idx_hdfs_detail_index_ele_idx ON qua_web_hdfs_element_detail_dir(element_id);
CREATE INDEX hdfs_detail_index_1_idx ON qua_web_hdfs_element_detail_file(element_id,file_name);
CREATE INDEX hdfs_idx_4_2_idx ON qua_web_hdfs_element_detail_file(file_name);
CREATE INDEX idx_hdfs_detail_index_ele_3_idx ON qua_web_hdfs_element_detail_file(element_id);
