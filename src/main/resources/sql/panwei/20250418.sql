ALTER TABLE tb_tenant_cluster ADD tenant_account varchar(100);
ALTER TABLE tb_tenant_cluster ADD tenant_password varchar(100);

COMMENT ON COLUMN tb_tenant_cluster.tenant_account IS '分配账号';
COMMENT ON COLUMN tb_tenant_cluster.tenant_password IS '分配密码';

ALTER TABLE tb_cluster ADD running_status int4 DEFAULT 0;
ALTER TABLE tb_cluster ADD auth_configuration_id int4 DEFAULT NULL;
ALTER TABLE tb_cluster ADD cluster_desc varchar(500);
ALTER TABLE tb_cluster ALTER COLUMN cluster_auth_type TYPE int4;

COMMENT ON COLUMN tb_cluster.running_status IS '运行状态 0:运行中 1:已停止';
COMMENT ON COLUMN tb_cluster.auth_configuration_id IS '认证配置ID';
COMMENT ON COLUMN tb_cluster.cluster_desc IS '集群描述';
COMMENT ON COLUMN tb_cluster.cluster_auth_type IS '认证方式 0:无认证 1:基本认证 2:Kbs认证 3:ssl认证';

CREATE TABLE IF NOT EXISTS tb_cluster_node
(
	id SERIAL PRIMARY KEY,
    cluster_id BIGINT NOT NULL ,
	host VARCHAR(100),
	port INTEGER,
	node_role VARCHAR(100),
	running_status int4 DEFAULT 0,
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP
);

COMMENT ON COLUMN tb_cluster_node.id IS '主键';
COMMENT ON COLUMN tb_cluster_node.cluster_id IS '集群ID';
COMMENT ON COLUMN tb_cluster_node.host IS '主机';
COMMENT ON COLUMN tb_cluster_node.port IS '端口';
COMMENT ON COLUMN tb_cluster_node.node_role IS '节点角色';
COMMENT ON COLUMN tb_cluster_node.running_status IS '运行状态 0:运行中 1:已停止';

-- 添加租户定义数据源ID
ALTER TABLE api_data_source ADD tenant_define_datasources_id int4 DEFAULT NULL;
