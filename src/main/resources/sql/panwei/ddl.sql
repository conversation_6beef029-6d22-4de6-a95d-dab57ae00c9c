DROP TABLE IF EXISTS api_apply;
DROP TABLE IF EXISTS api_catalog;
DROP TABLE IF EXISTS api_client;
DROP TABLE IF EXISTS api_client_auth;
DROP TABLE IF EXISTS api_crypto;
DROP TABLE IF EXISTS api_data_desensitization;
DROP TABLE IF EXISTS api_data_desensitization_log;
DROP TABLE IF EXISTS api_data_desensitization_log_detail;
DROP TABLE IF EXISTS api_data_source;
DROP TABLE IF EXISTS api_data_watermark;
DROP TABLE IF EXISTS api_data_watermark_log;
DROP TABLE IF EXISTS api_data_watermark_task;
DROP TABLE IF EXISTS api_data_watermark_task_result;
DROP TABLE IF EXISTS api_domain;
DROP TABLE IF EXISTS api_error_code;
DROP TABLE IF EXISTS api_info;
DROP TABLE IF EXISTS api_input_param;
DROP TABLE IF EXISTS api_invoke_log;
DROP TABLE IF EXISTS api_label;
DROP TABLE IF EXISTS api_label_info;
DROP TABLE IF EXISTS api_log;
DROP TABLE IF EXISTS api_output_param;
DROP TABLE IF EXISTS api_project;
DROP TABLE IF EXISTS api_security_group;
DROP TABLE IF EXISTS api_security_group_ref;
DROP TABLE IF EXISTS api_user;
DROP TABLE IF EXISTS backup_config;
DROP TABLE IF EXISTS backup_job;
DROP TABLE IF EXISTS backup_job_recover;
DROP TABLE IF EXISTS backup_job_task;
DROP TABLE IF EXISTS data_dev_fusion_model;
DROP TABLE IF EXISTS data_dev_fusion_task;
DROP TABLE IF EXISTS data_dev_model_field;
DROP TABLE IF EXISTS data_dev_source_field;
DROP TABLE IF EXISTS data_dictionary_base;
DROP TABLE IF EXISTS data_dictionary_category;
DROP TABLE IF EXISTS data_dictionary_item;
DROP TABLE IF EXISTS data_dictionary_item_table_trend;
DROP TABLE IF EXISTS data_encryption_algorithm;
DROP TABLE IF EXISTS data_encryption_algorithm_back_up;
DROP TABLE IF EXISTS data_encryption_algorithm_job;
DROP TABLE IF EXISTS data_encryption_algorithm_job_field;
DROP TABLE IF EXISTS data_encryption_algorithm_job_record;
DROP TABLE IF EXISTS data_mart_asset;
DROP TABLE IF EXISTS data_mart_asset_publish;
DROP TABLE IF EXISTS data_mart_asset_subscribe;
DROP TABLE IF EXISTS data_mart_asset_thumbs_up;
DROP TABLE IF EXISTS data_mart_discuss;
DROP TABLE IF EXISTS data_mart_group;
DROP TABLE IF EXISTS data_mart_hotword;
DROP TABLE IF EXISTS data_mart_resource_pool;
DROP TABLE IF EXISTS data_mart_score;
DROP TABLE IF EXISTS data_mart_subscribe;
DROP TABLE IF EXISTS data_mart_subscribe_notice;
DROP TABLE IF EXISTS data_mart_table_lines;
DROP TABLE IF EXISTS data_mart_tag;
DROP TABLE IF EXISTS data_mart_type;
DROP TABLE IF EXISTS data_meta_relation;
DROP TABLE IF EXISTS data_meta_tag;
DROP TABLE IF EXISTS data_meta_tag_master;
DROP TABLE IF EXISTS data_meta_tag_type;
DROP TABLE IF EXISTS data_model_business_process;
DROP TABLE IF EXISTS data_model_business_sector;
DROP TABLE IF EXISTS data_model_create_table_record;
DROP TABLE IF EXISTS data_model_data_domain;
DROP TABLE IF EXISTS data_model_dw_level;
DROP TABLE IF EXISTS data_model_export_record;
DROP TABLE IF EXISTS data_model_import_record;
DROP TABLE IF EXISTS data_model_logic_table;
DROP TABLE IF EXISTS data_model_logic_table_field;
DROP TABLE IF EXISTS data_model_logic_table_relation;
DROP TABLE IF EXISTS data_model_qua_monitor_rule;
DROP TABLE IF EXISTS eqpt_character_dic;
DROP TABLE IF EXISTS eqpt_character_report_list;
DROP TABLE IF EXISTS intelligence_query_history;
DROP TABLE IF EXISTS qua_config_business;
DROP TABLE IF EXISTS qua_config_business_category;
DROP TABLE IF EXISTS qua_config_master_data_type;
DROP TABLE IF EXISTS qua_data_standard_config;
DROP TABLE IF EXISTS qua_data_standard_config_master_data;
DROP TABLE IF EXISTS qua_data_standard_config_publish_log;
DROP TABLE IF EXISTS qua_data_standard_config_restore_log;
DROP TABLE IF EXISTS qua_data_standard_config_version_snapshot;
DROP TABLE IF EXISTS qua_data_standard_dic;
DROP TABLE IF EXISTS qua_data_standard_group;
DROP TABLE IF EXISTS qua_data_standard_set;
DROP TABLE IF EXISTS qua_dync_result;
DROP TABLE IF EXISTS qua_dync_threshold;
DROP TABLE IF EXISTS qua_internal_model;
DROP TABLE IF EXISTS qua_master_data;
DROP TABLE IF EXISTS qua_master_data_model;
DROP TABLE IF EXISTS qua_master_data_model_relation;
DROP TABLE IF EXISTS qua_monitor_job;
DROP TABLE IF EXISTS qua_monitor_model;
DROP TABLE IF EXISTS qua_monitor_model_resource;
DROP TABLE IF EXISTS qua_monitor_result;
DROP TABLE IF EXISTS qua_monitor_result_appeal;
DROP TABLE IF EXISTS qua_monitor_result_detail;
DROP TABLE IF EXISTS qua_monitor_rule;
DROP TABLE IF EXISTS qua_monitor_rule_exec_record;
DROP TABLE IF EXISTS qua_monitor_rule_template;
DROP TABLE IF EXISTS qua_monitor_rule_type;
DROP TABLE IF EXISTS qua_monitor_task;
DROP TABLE IF EXISTS qua_monitor_weight;
DROP TABLE IF EXISTS qua_scan_ch_column;
DROP TABLE IF EXISTS qua_scan_ch_db;
DROP TABLE IF EXISTS qua_scan_ch_table;
DROP TABLE IF EXISTS qua_scan_es_field;
DROP TABLE IF EXISTS qua_scan_es_index;
DROP TABLE IF EXISTS qua_scan_hive_column;
DROP TABLE IF EXISTS qua_scan_hive_db;
DROP TABLE IF EXISTS qua_scan_hive_table;
DROP TABLE IF EXISTS qua_scan_list;
DROP TABLE IF EXISTS qua_scan_mysql_column;
DROP TABLE IF EXISTS qua_scan_mysql_db;
DROP TABLE IF EXISTS qua_scan_mysql_table;
DROP TABLE IF EXISTS qua_server_used_statistic;
DROP TABLE IF EXISTS qua_wab_element;
DROP TABLE IF EXISTS qua_web_ch_element_detail_column;
DROP TABLE IF EXISTS qua_web_ch_element_detail_db;
DROP TABLE IF EXISTS qua_web_ch_element_detail_table;
DROP TABLE IF EXISTS qua_web_ch_task_result_column;
DROP TABLE IF EXISTS qua_web_ch_task_result_db;
DROP TABLE IF EXISTS qua_web_ch_task_result_table;
DROP TABLE IF EXISTS qua_web_es_element_detail_field;
DROP TABLE IF EXISTS qua_web_es_element_detail_index;
DROP TABLE IF EXISTS qua_web_es_task_result_field;
DROP TABLE IF EXISTS qua_web_es_task_result_index;
DROP TABLE IF EXISTS qua_web_hive_element_detail_column;
DROP TABLE IF EXISTS qua_web_hive_element_detail_db;
DROP TABLE IF EXISTS qua_web_hive_element_detail_table;
DROP TABLE IF EXISTS qua_web_hive_task_result_column;
DROP TABLE IF EXISTS qua_web_hive_task_result_db;
DROP TABLE IF EXISTS qua_web_hive_task_result_table;
DROP TABLE IF EXISTS qua_web_job;
DROP TABLE IF EXISTS qua_web_kbs_file_config;
DROP TABLE IF EXISTS qua_web_mysql_element_detail_column;
DROP TABLE IF EXISTS qua_web_mysql_element_detail_db;
DROP TABLE IF EXISTS qua_web_mysql_element_detail_table;
DROP TABLE IF EXISTS qua_web_mysql_task_result_column;
DROP TABLE IF EXISTS qua_web_mysql_task_result_db;
DROP TABLE IF EXISTS qua_web_mysql_task_result_table;
DROP TABLE IF EXISTS qua_web_task;
DROP TABLE IF EXISTS qua_web_user_operate_log;
DROP TABLE IF EXISTS sys_db_version_upgrade;
DROP TABLE IF EXISTS tb_cluster;
DROP TABLE IF EXISTS tb_cluster_kafka_topic;
DROP TABLE IF EXISTS tb_datasource_info;
DROP TABLE IF EXISTS tb_dic;
DROP TABLE IF EXISTS tb_monitor_topic;
DROP TABLE IF EXISTS tb_script_history;
DROP TABLE IF EXISTS tb_statistics_cluster_instance_disk_sync;
DROP TABLE IF EXISTS tb_statistics_disk;
DROP TABLE IF EXISTS tb_statistics_disk_sync;
DROP TABLE IF EXISTS tb_statistics_disk_tenant;
DROP TABLE IF EXISTS tb_statistics_etl_data;
DROP TABLE IF EXISTS tb_statistics_etl_task;
DROP TABLE IF EXISTS tb_statistics_etl_task_day;
DROP TABLE IF EXISTS tb_statistics_hdfs_sync;
DROP TABLE IF EXISTS tb_statistics_subscription;
DROP TABLE IF EXISTS tb_tenant;
DROP TABLE IF EXISTS tb_tenant_cluster;
DROP TABLE IF EXISTS ueba_dictionary;
DROP TABLE IF EXISTS ums_sys_auth_config;
DROP TABLE IF EXISTS ums_sys_datasource_config;
DROP TABLE IF EXISTS ums_sys_license;
DROP TABLE IF EXISTS ums_sys_log;
DROP TABLE IF EXISTS ums_sys_menus;
DROP TABLE IF EXISTS ums_sys_role;
DROP TABLE IF EXISTS ums_sys_user;
DROP TABLE IF EXISTS ums_sys_user_4a;
DROP TABLE IF EXISTS zeppelin_interpreter;
DROP TABLE IF EXISTS zeppelin_notebook;
DROP TABLE IF EXISTS application_list;


CREATE TABLE api_apply
(
	id VARCHAR PRIMARY KEY,
	api_id INTEGER NOT NULL ,
	client_id VARCHAR(50) NOT NULL ,
	call_limit INTEGER,
	begin_date TIMESTAMP,
	end_date TIMESTAMP,
	create_by VARCHAR(50),
	create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	modify_by VARCHAR(50),
	modify_date TIMESTAMP,
	apply_user VARCHAR(50),
	apply_time TIMESTAMP,
	audit_user VARCHAR(50),
	audit_time TIMESTAMP,
	apply_reason VARCHAR(500),
	audit_reason VARCHAR(500),
	audit_status INTEGER,
	audit_authorization_status INTEGER,
	tenant_id BIGINT,
	user_id VARCHAR(100)
);

CREATE TABLE api_catalog
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(100) NOT NULL ,
	parent_id BIGINT,
	create_user VARCHAR(50) DEFAULT '',
	create_time TIMESTAMP,
	update_user VARCHAR(50) DEFAULT '',
	update_time TIMESTAMP,
	tenant_id BIGINT DEFAULT 0
);

CREATE TABLE api_client
(
	id VARCHAR PRIMARY KEY,
	client_secret VARCHAR(200) NOT NULL ,
	client_name VARCHAR(200) NOT NULL ,
	notes VARCHAR(500),
	client_status SMALLINT DEFAULT 1,
	create_by VARCHAR(50) NOT NULL ,
	create_date TIMESTAMP NOT NULL ,
	modify_by VARCHAR(50),
	modify_date TIMESTAMP,
	domain_id INTEGER NOT NULL
);

CREATE TABLE api_client_auth
(
	id VARCHAR PRIMARY KEY,
	api_id INTEGER NOT NULL ,
	client_id VARCHAR(50) NOT NULL ,
	apply_id VARCHAR(100)
);

CREATE TABLE api_crypto
(
	id SERIAL  PRIMARY KEY,
	public_key VARCHAR(1000) DEFAULT '',
	private_key VARCHAR(1000) DEFAULT '',
	create_user VARCHAR(50) DEFAULT '',
	create_time TIMESTAMP,
	update_user VARCHAR(50) DEFAULT '',
	update_time TIMESTAMP,
	tenant_id BIGINT DEFAULT 0
);

CREATE TABLE api_data_desensitization
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(100) NOT NULL ,
	type INTEGER,
	create_user VARCHAR(50) DEFAULT '',
	create_time TIMESTAMP,
	update_user VARCHAR(50) DEFAULT '',
	update_time TIMESTAMP,
	tenant_id BIGINT DEFAULT 0,
	rule_content VARCHAR(1000),
	status INTEGER DEFAULT 0
);

CREATE TABLE api_data_desensitization_log
(
	id SERIAL  PRIMARY KEY,
	api_id BIGINT NOT NULL ,
	client_id VARCHAR(50),
	invoke_user VARCHAR(100),
	invoke_time TIMESTAMP,
	invoke_status INTEGER DEFAULT 0,
	desensitization_status INTEGER DEFAULT 0,
	api_name VARCHAR(100),
	api_status_name VARCHAR(50),
	desensitization_table VARCHAR(100),
	desensitization_db VARCHAR(100)
);

CREATE TABLE api_data_desensitization_log_detail
(
	id SERIAL  PRIMARY KEY,
	log_id BIGINT NOT NULL ,
	column_name VARCHAR(50),
	desensitization_before VARCHAR(500),
	desensitization_after VARCHAR(500),
	desensitization_name VARCHAR(255),
	desensitization_status INTEGER,
	desensitization_count INTEGER,
	create_time TIMESTAMP
);

CREATE TABLE api_data_source
(
	id SERIAL  PRIMARY KEY,
	data_source_name VARCHAR(255) DEFAULT '',
	data_source_type VARCHAR(255) DEFAULT '',
	data_source_version VARCHAR(255) DEFAULT '',
	data_source_desc VARCHAR(255) DEFAULT '',
	cluster_address VARCHAR(255) DEFAULT '',
	link_user VARCHAR(255) DEFAULT '',
	link_password VARCHAR(255) DEFAULT '',
	link_status INTEGER DEFAULT 0,
	create_user VARCHAR(50) DEFAULT '',
	create_time TIMESTAMP,
	update_user VARCHAR(50) DEFAULT '',
	update_time TIMESTAMP,
	tenant_id BIGINT DEFAULT 0,
	flag INTEGER DEFAULT 1,
	jdbc_url VARCHAR(255) DEFAULT '',
	cluster_name VARCHAR(255) DEFAULT '',
	database_name VARCHAR(255) DEFAULT '',
	key_tab_path varchar(512) DEFAULT NULL,
    krb5_conf_path varchar(512) DEFAULT NULL,
    jaas_conf_path varchar(512) DEFAULT NULL,
    key_tab_content text,
    krb5_conf_content text,
    jaas_conf_content text
);

CREATE TABLE api_data_watermark
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(100) NOT NULL ,
	content VARCHAR(100),
	create_user VARCHAR(50) DEFAULT '',
	create_time TIMESTAMP,
	update_user VARCHAR(50) DEFAULT '',
	update_time TIMESTAMP,
	tenant_id BIGINT DEFAULT 0,
	watermark_form INTEGER,
	custom_content VARCHAR(255),
	business_system VARCHAR(255),
	business_scenario VARCHAR(255),
	remark VARCHAR(255),
	status INTEGER DEFAULT 0,
	system_leader VARCHAR(100),
	department VARCHAR(100)
);

CREATE TABLE api_data_watermark_log
(
	id SERIAL  PRIMARY KEY,
	api_id BIGINT NOT NULL ,
	client_id VARCHAR(50),
	invoke_user VARCHAR(100),
	invoke_time TIMESTAMP,
	invoke_status INTEGER DEFAULT 0,
	watermark_status INTEGER DEFAULT 0,
	api_name VARCHAR(100),
	api_status INTEGER
);

CREATE TABLE api_data_watermark_task
(
	id SERIAL  PRIMARY KEY,
	task_name VARCHAR(100) NOT NULL ,
	file_name VARCHAR(100),
	create_user VARCHAR(50) DEFAULT '',
	create_time TIMESTAMP,
	update_user VARCHAR(50) DEFAULT '',
	update_time TIMESTAMP,
	tenant_id BIGINT DEFAULT 0,
	start_time TIMESTAMP,
	end_time TIMESTAMP,
	duration BIGINT,
	status INTEGER DEFAULT 0,
	file_path VARCHAR(255)
);

CREATE TABLE api_data_watermark_task_result
(
	id SERIAL  PRIMARY KEY,
	task_id BIGINT NOT NULL ,
	content VARCHAR(255),
	percentages VARCHAR(50) DEFAULT '',
	create_time TIMESTAMP
);

CREATE TABLE api_domain
(
	domain_id SERIAL  PRIMARY KEY,
	name VARCHAR(200) NOT NULL ,
	address VARCHAR(200) NOT NULL ,
	contact VARCHAR(200) NOT NULL ,
	phone_number VARCHAR(15) NOT NULL ,
	domain_account VARCHAR(20) NOT NULL ,
	domain_code VARCHAR(20) NOT NULL ,
	web_url VARCHAR(100),
	notes VARCHAR(500),
	create_by VARCHAR(50) NOT NULL ,
	create_date TIMESTAMP NOT NULL ,
	modify_by VARCHAR(50),
	modify_date TIMESTAMP,
	is_deleted SMALLINT DEFAULT 0
);

CREATE TABLE api_error_code
(
	id SERIAL  PRIMARY KEY,
	error_code VARCHAR(100),
	error_msg VARCHAR(255),
	solution TEXT,
	api_info_id INTEGER
);

CREATE TABLE api_info
(
	id SERIAL  PRIMARY KEY,
	cn_name VARCHAR(255) DEFAULT '',
	api_desc VARCHAR(255) DEFAULT '',
	api_path VARCHAR(255) DEFAULT '',
	protocol VARCHAR(255) DEFAULT '',
	req_type VARCHAR(50) DEFAULT '',
	response_type VARCHAR(255) DEFAULT '',
	query_timeout INTEGER,
	req_limit INTEGER DEFAULT 1000,
	transfer_encrypt_type INTEGER DEFAULT 0,
	data_source_id INTEGER,
	allow_paging INTEGER,
	contain_page INTEGER,
	contain_header INTEGER,
	api_type INTEGER,
	api_sql VARCHAR(1000) DEFAULT '',
	table_name VARCHAR(255) DEFAULT '',
	name VARCHAR(255) DEFAULT '',
	ignore_syntax_check INTEGER,
	submit_status INTEGER DEFAULT 0,
	publish_status INTEGER DEFAULT 0,
	api_create_type INTEGER,
	api_token VARCHAR(1000) DEFAULT '',
	target_host VARCHAR(1000) DEFAULT '',
	target_path VARCHAR(1000) DEFAULT '',
	target_port INTEGER,
	target_method VARCHAR(100),
	create_user VARCHAR(50) DEFAULT '',
	create_time TIMESTAMP,
	update_user VARCHAR(50) DEFAULT '',
	update_time TIMESTAMP,
	tenant_id BIGINT DEFAULT 0,
	content_type INTEGER DEFAULT 0,
	catalog_id BIGINT,
	project_id VARCHAR(50),
	result_sample_flag INTEGER,
	result_sample TEXT,
	body_desc_json TEXT,
	contain_original_status INTEGER,
	normal_return_example TEXT,
	error_return_example TEXT,
	is_watermark INTEGER,
	is_desensitization INTEGER,
	watermark_id BIGINT,
	publish_time TIMESTAMP,
	publish_desc VARCHAR(255),
	publish_user VARCHAR(100),
	approval_status INTEGER,
	approval_desc VARCHAR(255),
	approval_time TIMESTAMP,
	approval_user VARCHAR(100)
);

CREATE TABLE api_input_param
(
	id SERIAL  PRIMARY KEY,
	param_desc VARCHAR(255) DEFAULT '',
	field_name VARCHAR(255) DEFAULT '',
	operator VARCHAR(255) DEFAULT '',
	param_name VARCHAR(255) DEFAULT '',
	param_type VARCHAR(255) DEFAULT '',
	required INTEGER DEFAULT 0,
	row_permission INTEGER,
	api_info_id INTEGER,
	constant_param INTEGER,
	default_value VARCHAR(255) DEFAULT '',
	is_arrray INTEGER,
	param_location INTEGER,
	target_param_name VARCHAR(255)
);

CREATE TABLE api_invoke_log
(
	id VARCHAR PRIMARY KEY,
	api_id INTEGER NOT NULL ,
	client_id VARCHAR(50) NOT NULL ,
	invoke_time TIMESTAMP NOT NULL ,
	create_by VARCHAR(50),
	create_date TIMESTAMP,
	biz_type SMALLINT,
	content TEXT,
	invoke_flag VARCHAR(50),
	duration BIGINT,
	request_flag VARCHAR(50) DEFAULT 'true'
);

CREATE TABLE api_label
(
	id SERIAL  PRIMARY KEY,
	label_name VARCHAR(255) DEFAULT '',
	create_user VARCHAR(50) DEFAULT '',
	create_time TIMESTAMP,
	update_user VARCHAR(50) DEFAULT '',
	update_time TIMESTAMP,
	tenant_id BIGINT DEFAULT 0
);

CREATE TABLE api_label_info
(
	id SERIAL  PRIMARY KEY,
	api_label_id INTEGER,
	api_info_id INTEGER
);

CREATE TABLE api_log
(
	id SERIAL  PRIMARY KEY,
	api_info_id INTEGER,
	start_time TIMESTAMP,
	end_time TIMESTAMP,
	content TEXT,
	is_success INTEGER,
	create_user VARCHAR(50) DEFAULT '',
	create_time TIMESTAMP
);

CREATE TABLE api_output_param
(
	id SERIAL  PRIMARY KEY,
	param_desc VARCHAR(255) DEFAULT '',
	field_name VARCHAR(255) DEFAULT '',
	param_name VARCHAR(255) DEFAULT '',
	param_type VARCHAR(255) DEFAULT '',
	api_info_id INTEGER,
	desensitization_id BIGINT
);

CREATE TABLE api_project
(
	id VARCHAR PRIMARY KEY,
	project_mark VARCHAR(200) NOT NULL ,
	display_name VARCHAR(200) NOT NULL ,
	notes VARCHAR(500),
	create_by VARCHAR(50) NOT NULL ,
	create_date TIMESTAMP NOT NULL ,
	modify_by VARCHAR(50),
	modify_date TIMESTAMP,
	is_deleted SMALLINT DEFAULT 0,
	domain_id INTEGER NOT NULL
);

CREATE TABLE api_security_group
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(256) NOT NULL ,
	ip TEXT,
	simple_ip_text TEXT,
	type SMALLINT NOT NULL ,
	tenant_id INTEGER NOT NULL ,
	create_by VARCHAR(50),
	create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	modify_by VARCHAR(50),
	modify_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	is_deleted SMALLINT DEFAULT 0,
	project_id VARCHAR(50)
);

CREATE TABLE api_security_group_ref
(
	id SERIAL  PRIMARY KEY,
	group_id INTEGER NOT NULL ,
	api_id INTEGER NOT NULL ,
	tenant_id INTEGER NOT NULL ,
	create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	modify_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE api_user
(
	user_id VARCHAR PRIMARY KEY,
	user_name VARCHAR(70) NOT NULL ,
	display_name VARCHAR(50) NOT NULL ,
	password VARCHAR(50) NOT NULL ,
	phone_number VARCHAR(15),
	email VARCHAR(30),
	last_login_ip VARCHAR(20),
	notes VARCHAR(500),
	create_by VARCHAR(50) NOT NULL ,
	create_date TIMESTAMP NOT NULL ,
	modify_by VARCHAR(50),
	modify_date TIMESTAMP,
	is_deleted SMALLINT DEFAULT 0,
	domain_id INTEGER NOT NULL ,
	sex SMALLINT DEFAULT 1
);

CREATE TABLE application_list
(
	id SERIAL  PRIMARY KEY,
	app_name VARCHAR(255) NOT NULL ,
	app_code VARCHAR(255) NOT NULL ,
	app_id VARCHAR(255) NOT NULL ,
	account VARCHAR(255) NOT NULL ,
	user_name VARCHAR(255) NOT NULL ,
	user_phone VARCHAR(20) NOT NULL ,
	email VARCHAR(255) NOT NULL ,
	code_name VARCHAR(255) NOT NULL ,
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE backup_config
(
	id SERIAL  PRIMARY KEY,
	type VARCHAR(20) DEFAULT 'ch',
	table_name VARCHAR(50),
	backup_type VARCHAR(50),
	backup_policy VARCHAR(50) NOT NULL ,
	increment_field VARCHAR(50),
	storage VARCHAR(50),
	storage_volume INTEGER,
	volume_unit VARCHAR(10),
	storage_day INTEGER,
	execute_period VARCHAR(10),
	execute_plan INTEGER,
	execute_time VARCHAR(20),
	execute_cron VARCHAR(512),
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP,
	flag VARCHAR(1) DEFAULT '1',
	status VARCHAR(1),
	ip VARCHAR(20),
	port VARCHAR(20),
	data_base VARCHAR(20),
	time_type VARCHAR(20),
	time_config VARCHAR(256),
	is_recover VARCHAR(2),
	task_name VARCHAR(256),
	backup_method VARCHAR(1),
	recover_config VARCHAR(1024),
	last_finish_time TIMESTAMP,
	backup_result VARCHAR(1)
);

CREATE TABLE backup_job
(
	id SERIAL  PRIMARY KEY,
	job_name VARCHAR(200),
	data_source_type VARCHAR(50),
	backup_target VARCHAR(100),
	backup_type SMALLINT,
	store_type SMALLINT,
	increament_filed VARCHAR(100),
	run_type SMALLINT,
	data_value VARCHAR(100),
	cron VARCHAR(50),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(50),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(50),
	latest_state SMALLINT,
	latest_time TIMESTAMP,
	tenant_id INTEGER,
	job_state SMALLINT DEFAULT 1,
	flag SMALLINT DEFAULT 1,
	is_run SMALLINT DEFAULT 1
);

CREATE TABLE backup_job_recover
(
	id SERIAL  PRIMARY KEY,
	task_id INTEGER,
	create_time TIMESTAMP,
	backup_total BIGINT,
	recover_total BIGINT,
	recover_check SMALLINT,
	state SMALLINT,
	result TEXT,
	file_path VARCHAR(400),
	recover_target_name VARCHAR(400),
	tenant_id INTEGER
);

CREATE TABLE backup_job_task
(
	id SERIAL  PRIMARY KEY,
	job_id INTEGER,
	create_time TIMESTAMP,
	data_num BIGINT,
	file_sie BIGINT,
	state SMALLINT DEFAULT 0,
	result TEXT,
	file_path VARCHAR(400),
	sign_code VARCHAR(400)
);

CREATE TABLE data_dev_fusion_model
(
	id SERIAL  PRIMARY KEY,
	datasource VARCHAR(100),
	table_name VARCHAR(100),
	bus_desc VARCHAR(100),
	data_update_cycle SMALLINT,
	data_update_cron VARCHAR(30),
	data_save_type SMALLINT,
	data_save_num INTEGER,
	expiration_policy SMALLINT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(50),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(50),
	tenant_id BIGINT,
	status SMALLINT DEFAULT 0,
	is_first_sync SMALLINT DEFAULT 0,
	ch_cluster_name VARCHAR(100),
	ch_shard_name VARCHAR(100)
);

CREATE TABLE data_dev_fusion_task
(
	id SERIAL  PRIMARY KEY,
	fusion_model_id BIGINT,
	sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	sync_status SMALLINT DEFAULT 1,
	current_data_num VARCHAR(50),
	add_data_num VARCHAR(50),
	err_log TEXT
);

CREATE TABLE data_dev_model_field
(
	id SERIAL  PRIMARY KEY,
	fusion_model_id BIGINT,
	field_name VARCHAR(100),
	field_name_cn VARCHAR(200),
	field_type VARCHAR(100),
	field_length INTEGER,
	constraint_type VARCHAR(100),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE data_dev_source_field
(
	id SERIAL  PRIMARY KEY,
	fusion_model_id BIGINT,
	model_field_id BIGINT,
	model_field_name VARCHAR(100),
	ds_datasource_id BIGINT,
	source_table_name VARCHAR(100),
	source_field_name VARCHAR(100),
	source_field_name_cn VARCHAR(500),
	source_field_type VARCHAR(100),
	source_field_length INTEGER,
	source_sync_field VARCHAR(100),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE data_dictionary_base
(
	id SERIAL  PRIMARY KEY,
	dict_name VARCHAR(128),
	dict_desc VARCHAR(1024),
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	built_in INTEGER DEFAULT 1
);

CREATE TABLE data_dictionary_category
(
	id SERIAL  PRIMARY KEY,
	dict_id BIGINT NOT NULL ,
	category_name VARCHAR(128),
	category_desc VARCHAR(1024),
	parent_id BIGINT,
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE data_dictionary_item
(
	id SERIAL  PRIMARY KEY,
	dict_id BIGINT NOT NULL ,
	category_id BIGINT NOT NULL ,
	item_id BIGINT NOT NULL ,
	item_name VARCHAR(128),
	item_desc VARCHAR(1024),
	item_type VARCHAR(64),
	item_constraint VARCHAR(128),
	datasource_type VARCHAR(64),
	database_id BIGINT,
	database_name VARCHAR(128),
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	column_name_cn VARCHAR(1000),
	table_name VARCHAR(255),
	field_type VARCHAR(100),
	asset_path VARCHAR(255),
	asset_type VARCHAR(255),
	element_id BIGINT,
	asset_type_code VARCHAR(100),
	asset_data_type VARCHAR(100),
	data_mart_asset_id BIGINT,
	owner VARCHAR(200),
	sen_level_id BIGINT,
	sen_level_name VARCHAR(200),
	sen_type_id BIGINT,
	sen_type_name VARCHAR(200),
	is_sensitive INTEGER DEFAULT 0,
	desensitization_id INTEGER,
	is_required INTEGER DEFAULT 0,
	is_encrypted INTEGER DEFAULT 0
);

CREATE TABLE data_dictionary_item_table_trend
(
	id SERIAL  PRIMARY KEY,
	item_id BIGINT,
	count_day VARCHAR(100),
	table_line BIGINT DEFAULT 0,
	table_space NUMERIC,
	create_time TIMESTAMP
);

CREATE TABLE data_encryption_algorithm
(
	id SERIAL  PRIMARY KEY,
	key_name VARCHAR(200),
	key_algorithm VARCHAR(100),
	level_id INTEGER,
	type_id INTEGER,
	remark VARCHAR(200),
	sm2_public_key VARCHAR(1000),
	sm2_private_key VARCHAR(1000),
	sm4_key_base64 VARCHAR(1000),
	sm4_iv_base64 VARCHAR(1000),
	create_user VARCHAR(100),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(100),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	tenant_id INTEGER,
	is_enable INTEGER
);

CREATE TABLE data_encryption_algorithm_back_up
(
	id SERIAL  PRIMARY KEY,
	encryption_algorithm_id INTEGER,
	key_name VARCHAR(200),
	key_algorithm VARCHAR(100),
	level_id INTEGER,
	level_name VARCHAR(100),
	type_id INTEGER,
	type_name VARCHAR(100),
	remark VARCHAR(200),
	sm2_public_key VARCHAR(1000),
	sm2_private_key VARCHAR(1000),
	sm4_key_base64 VARCHAR(1000),
	sm4_iv_base64 VARCHAR(1000),
	back_up_user VARCHAR(100),
	back_up_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	tenant_id INTEGER
);

CREATE TABLE data_encryption_algorithm_job
(
	id SERIAL  PRIMARY KEY,
	job_name VARCHAR(200),
	datasource_type VARCHAR(100),
	encryption_object VARCHAR(100),
	encryption_type VARCHAR(100),
	target_object VARCHAR(100),
	remark VARCHAR(200),
	state VARCHAR(500),
	create_user VARCHAR(100),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(100),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	tenant_id INTEGER,
	finish_time TIMESTAMP,
	result TEXT
);

CREATE TABLE data_encryption_algorithm_job_field
(
	id SERIAL  PRIMARY KEY,
	job_id INTEGER,
	field_name VARCHAR(200),
	encryption_algorithm_id INTEGER
);

CREATE TABLE data_encryption_algorithm_job_record
(
	id SERIAL  PRIMARY KEY,
	job_id INTEGER,
	field_name VARCHAR(200),
	key_name VARCHAR(200),
	before_text VARCHAR(200),
	after_text VARCHAR(200),
	state VARCHAR(200) DEFAULT '成功',
	should_count BIGINT,
	actual_count BIGINT,
	success_rate VARCHAR(200)
);

CREATE TABLE IF NOT EXISTS data_mart_asset
(
	id SERIAL  PRIMARY KEY,
	type_id BIGINT,
	group_id BIGINT,
	element_id BIGINT,
	db_name VARCHAR(50),
	table_name VARCHAR(100),
	index_name VARCHAR(100),
	batch_no VARCHAR(100),
	asset_name VARCHAR(100),
	asset_path VARCHAR(500),
	asset_type VARCHAR(100),
	asset_type_code VARCHAR(100),
	subscribe_num INTEGER DEFAULT 0,
	thumbs_up_num INTEGER DEFAULT 0,
	release_status SMALLINT DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(100),
	tenant_id BIGINT,
	data_name VARCHAR(256),
	asset_desc VARCHAR(512),
	start_time VARCHAR(32),
	end_time VARCHAR(32),
	asset_json TEXT,
	update_time TIMESTAMP,
	publish_status INTEGER DEFAULT 0,
	publish_time TIMESTAMP,
	approval_status INTEGER,
	approval_desc VARCHAR(255),
	approval_time TIMESTAMP,
	data_mart_asset VARCHAR(100),
	browse_count INTEGER DEFAULT 0,
	approval_user VARCHAR(255),
	release_type SMALLINT DEFAULT 0,
	task_type SMALLINT DEFAULT 0,
	flow_id BIGINT DEFAULT 0,
	task_inputs TEXT,
	task_outs TEXT,
	is_manual SMALLINT DEFAULT 1
);

CREATE TABLE data_mart_asset_publish
(
	id SERIAL  PRIMARY KEY,
	asset_id BIGINT,
	label VARCHAR(100),
	url VARCHAR(500),
	tenant_id INTEGER,
	user_id VARCHAR(100),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE data_mart_asset_subscribe
(
	id SERIAL  PRIMARY KEY,
	asset_id BIGINT,
	tenant_id INTEGER,
	user_id VARCHAR(100),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	tag_id BIGINT
);

CREATE TABLE data_mart_asset_thumbs_up
(
	id SERIAL  PRIMARY KEY,
	asset_id BIGINT,
	tenant_id INTEGER,
	user_id VARCHAR(100),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE data_mart_discuss
(
	id SERIAL  PRIMARY KEY,
	asset_id BIGINT,
	tenant_id INTEGER,
	user_id INTEGER,
	user_name VARCHAR(200),
	discuss VARCHAR(10000),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE data_mart_group
(
	id SERIAL  PRIMARY KEY,
	group_name VARCHAR(100),
	data_mart_type_id BIGINT,
	group_desc VARCHAR(200),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(100),
	tenant_id BIGINT
);

CREATE TABLE data_mart_hotword
(
	id SERIAL  PRIMARY KEY,
	hotword VARCHAR(255),
	create_time TIMESTAMP
);

CREATE TABLE data_mart_resource_pool
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(100) NOT NULL ,
	parent_id INTEGER
);

CREATE TABLE data_mart_score
(
	id SERIAL  PRIMARY KEY,
	asset_id BIGINT,
	tenant_id INTEGER,
	user_name VARCHAR(200),
	available_score INTEGER DEFAULT 0,
	visible_score INTEGER DEFAULT 0,
	credible_score INTEGER DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE data_mart_subscribe
(
	id SERIAL  PRIMARY KEY,
	asset_id BIGINT,
	tenant_id INTEGER,
	user_id VARCHAR(100),
	subscription_channel INTEGER,
	subscription_time TIMESTAMP,
	subscription_user VARCHAR(100),
	subscription_remark VARCHAR(500),
	approval_progress INTEGER,
	approval_time TIMESTAMP,
	approval_remark VARCHAR(500),
	approval_user VARCHAR(100),
	configuration_progress INTEGER,
	configuration_user VARCHAR(100),
	configuration_time TIMESTAMP,
	configuration_remark VARCHAR(500),
	call_num BIGINT DEFAULT 0,
	call_num_limit INTEGER DEFAULT 0,
	begin_time TIMESTAMP,
	end_time TIMESTAMP,
	time_limit INTEGER DEFAULT 0,
	kafka_topic VARCHAR(100),
	kafka_address VARCHAR(255),
	kafka_port INTEGER,
	elasticsearch_index VARCHAR(100),
	clickhouse_table VARCHAR(100),
	api_id BIGINT,
	data_usage INTEGER,
	network_domain INTEGER,
	business_ip VARCHAR(100),
	hosting_network_ip VARCHAR(100),
	vpc_name VARCHAR(100),
	contact_phone VARCHAR(100),
	contact_email VARCHAR(100),
	resource_pool_id INTEGER,
	subscribe_rule_id VARCHAR(100),
	configuration_resource_pool_id INTEGER,
	configuration_business_ip VARCHAR(100),
	configuration_hosting_network_ip VARCHAR(100),
	configuration_vpc_name VARCHAR(100),
	configuration_network_domain INTEGER,
	configuration_kafka_topic VARCHAR(100),
	configuration_kafka_consumer_group VARCHAR(100),
	configuration_host_name VARCHAR(100)
);

CREATE TABLE data_mart_subscribe_notice
(
	id SERIAL  PRIMARY KEY,
	subscribe_id BIGINT,
	content TEXT,
	create_time TIMESTAMP
);

CREATE TABLE data_mart_table_lines
(
	id SERIAL  PRIMARY KEY,
	asset_id BIGINT,
	count_day VARCHAR(100),
	table_line VARCHAR(1000),
	table_line_all BIGINT,
	table_space_all NUMERIC,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE data_mart_tag
(
	id SERIAL  PRIMARY KEY,
	tag_name VARCHAR(128),
	tag_desc VARCHAR(512),
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE data_mart_type
(
	id SERIAL  PRIMARY KEY,
	type_name VARCHAR(100),
	type_desc VARCHAR(200),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(100),
	tenant_id INTEGER
);

CREATE TABLE data_meta_relation
(
	id SERIAL  PRIMARY KEY,
	source_element_id BIGINT NOT NULL ,
	source_item_type VARCHAR(64),
	source_datasource_type VARCHAR(64),
	source_database_name VARCHAR(128),
	source_table_name VARCHAR(255),
	source_field_name VARCHAR(100),
	source_unique_id VARCHAR(100) DEFAULT '',
	target_element_id BIGINT NOT NULL ,
	target_item_type VARCHAR(64),
	target_datasource_type VARCHAR(64),
	target_database_name VARCHAR(128),
	target_table_name VARCHAR(255),
	target_field_name VARCHAR(100),
	target_unique_id VARCHAR(100) DEFAULT '',
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE data_meta_tag
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(255) NOT NULL ,
	abbreviation VARCHAR(255) NOT NULL ,
	description TEXT,
	tag_type_id BIGINT NOT NULL ,
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE data_meta_tag_master
(
	id SERIAL  PRIMARY KEY,
	tag_id BIGINT NOT NULL ,
	element_id BIGINT NOT NULL ,
	item_type VARCHAR(64),
	datasource_type VARCHAR(64),
	database_name VARCHAR(128),
	table_name VARCHAR(255),
	field_name VARCHAR(100),
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	item_unique_id VARCHAR(100)
);

CREATE TABLE data_meta_tag_type
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(255) NOT NULL ,
	abbreviation VARCHAR(255) NOT NULL ,
	is_quick_tag INTEGER DEFAULT 0,
	description TEXT,
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE data_model_business_process
(
	id SERIAL  PRIMARY KEY,
	business_sector_id BIGINT NOT NULL ,
	data_domain_id BIGINT NOT NULL ,
	process_name_cn VARCHAR(128),
	process_name VARCHAR(128),
	process_desc VARCHAR(1024),
	process_status VARCHAR(2) DEFAULT '1',
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP,
	tenant_id BIGINT
);

CREATE TABLE data_model_business_sector
(
	id SERIAL  PRIMARY KEY,
	sector_name_cn VARCHAR(128),
	sector_name VARCHAR(128),
	sector_name_abbr VARCHAR(128),
	sector_desc VARCHAR(1024),
	sector_status VARCHAR(2) DEFAULT '1',
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP,
	tenant_id BIGINT
);

CREATE TABLE data_model_create_table_record
(
	id SERIAL  PRIMARY KEY,
	table_ids VARCHAR(3000) NOT NULL ,
	process_id BIGINT,
	create_sql TEXT,
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP,
	tenant_id BIGINT
);

CREATE TABLE data_model_data_domain
(
	id SERIAL  PRIMARY KEY,
	business_sector_id BIGINT NOT NULL ,
	domain_name_cn VARCHAR(128),
	domain_name VARCHAR(128),
	domain_name_abbr VARCHAR(128),
	domain_desc VARCHAR(1024),
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP,
	tenant_id BIGINT
);

CREATE TABLE data_model_dw_level
(
	id SERIAL  PRIMARY KEY,
	business_sector_id BIGINT NOT NULL ,
	level_name_cn VARCHAR(128),
	level_name VARCHAR(128),
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP,
	tenant_id BIGINT
);

CREATE TABLE data_model_export_record
(
	id SERIAL  PRIMARY KEY,
	record_name VARCHAR(200) NOT NULL ,
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	tenant_id BIGINT
);

CREATE TABLE data_model_import_record
(
	id SERIAL  PRIMARY KEY,
	business_sector_id BIGINT NOT NULL ,
	record_name VARCHAR(100) NOT NULL ,
	record_desc VARCHAR(200) NOT NULL ,
	table_type VARCHAR(100),
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	tenant_id BIGINT
);

CREATE TABLE data_model_logic_table
(
	id SERIAL  PRIMARY KEY,
	business_sector_id BIGINT NOT NULL ,
	dw_level_id BIGINT NOT NULL ,
	data_domain_id BIGINT NOT NULL ,
	process_id BIGINT,
	detail_type SMALLINT,
	table_name_cn VARCHAR(128),
	table_name VARCHAR(128),
	table_name_abbr VARCHAR(128),
	table_desc VARCHAR(1024),
	table_type VARCHAR(32) NOT NULL ,
	table_physical VARCHAR(2) DEFAULT '0',
	model_status VARCHAR(2) DEFAULT '1',
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP,
	tenant_id BIGINT,
	audit_status VARCHAR(2) DEFAULT '1',
	db_type VARCHAR(32) DEFAULT 'CLICKHOUSE',
	apply_user VARCHAR(64),
	apply_time TIMESTAMP,
	audit_user VARCHAR(64),
	audit_time TIMESTAMP,
	audit_desc VARCHAR(512)
);

CREATE TABLE data_model_logic_table_field
(
	id SERIAL  PRIMARY KEY,
	logic_table_id BIGINT NOT NULL ,
	field_name_cn VARCHAR(128),
	field_name VARCHAR(128) NOT NULL ,
	field_data_type VARCHAR(64) NOT NULL ,
	field_sort INTEGER DEFAULT 1,
	is_primary_key VARCHAR(2) DEFAULT '0',
	is_null VARCHAR(2) DEFAULT '0',
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP,
	tenant_id BIGINT
);

CREATE TABLE data_model_logic_table_relation
(
	id SERIAL  PRIMARY KEY,
	logic_table_id BIGINT NOT NULL ,
	logic_field_id BIGINT NOT NULL ,
	relation_table_id BIGINT NOT NULL ,
	relation_field_id BIGINT,
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP,
	tenant_id BIGINT
);

CREATE TABLE data_model_qua_monitor_rule
(
	id SERIAL  PRIMARY KEY,
	model_id BIGINT NOT NULL ,
	rule_name VARCHAR(128) NOT NULL ,
	rule_code VARCHAR(128),
	rule_type VARCHAR(64) NOT NULL ,
	table_name VARCHAR(128),
	column_name VARCHAR(128),
	rule_detail VARCHAR(1024),
	rule_level VARCHAR(128),
	rule_desc VARCHAR(512),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	flag VARCHAR(1) DEFAULT '1',
	tenant_id BIGINT NOT NULL ,
	logic_table_id BIGINT
);

CREATE TABLE eqpt_character_dic
(
	id SERIAL  PRIMARY KEY,
	group_key VARCHAR(200),
	name VARCHAR(200),
	code VARCHAR(200)
);

CREATE TABLE eqpt_character_report_list
(
	id SERIAL  PRIMARY KEY,
	branch_code VARCHAR(10) NOT NULL ,
	vendor_name VARCHAR(20) NOT NULL ,
	eqpt_type VARCHAR(20) NOT NULL ,
	eqpt_ip VARCHAR(64) NOT NULL ,
	sys_version VARCHAR(255) NOT NULL ,
	character_num VARCHAR(64) NOT NULL ,
	character_name VARCHAR(255) NOT NULL ,
	character_version VARCHAR(64) NOT NULL ,
	send_time BIGINT NOT NULL ,
	sys_update_time TIMESTAMP,
	character_update_time TIMESTAMP
);

CREATE TABLE intelligence_query_history
(
	id SERIAL  PRIMARY KEY,
	query_condition VARCHAR(512) NOT NULL ,
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_config_business
(
	id SERIAL  PRIMARY KEY,
	category_id BIGINT NOT NULL ,
	business_name VARCHAR(128) NOT NULL ,
	business_desc VARCHAR(1024),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	flag VARCHAR(1) DEFAULT '1',
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_config_business_category
(
	id SERIAL  PRIMARY KEY,
	category_name VARCHAR(128) NOT NULL ,
	category_desc VARCHAR(1024),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	flag VARCHAR(1) DEFAULT '1',
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_config_master_data_type
(
	id SERIAL  PRIMARY KEY,
	type_name VARCHAR(128) NOT NULL ,
	type_desc VARCHAR(128) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	flag VARCHAR(1) DEFAULT '1',
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_data_standard_config
(
	id SERIAL  PRIMARY KEY,
	cn_name VARCHAR(100) NOT NULL ,
	en_name VARCHAR(100) NOT NULL ,
	code VARCHAR(100) NOT NULL ,
	business_meaning VARCHAR(100) DEFAULT '',
	accordance VARCHAR(100) DEFAULT '',
	standard_type INTEGER NOT NULL ,
	data_length INTEGER,
	data_column_type VARCHAR(100),
	data_precision INTEGER,
	cn_en_verification INTEGER,
	custom_content VARCHAR(1000),
	remark VARCHAR(1000),
	create_time TIMESTAMP,
	create_user VARCHAR(60) NOT NULL ,
	update_time TIMESTAMP,
	update_user VARCHAR(60) NOT NULL ,
	tenant_id BIGINT NOT NULL ,
	set_id BIGINT,
	status INTEGER DEFAULT 1,
	encrypt VARCHAR(1) DEFAULT '0',
	dept VARCHAR(255),
	business_system VARCHAR(255),
	store_cycle VARCHAR(255),
	database_name VARCHAR(255),
	sensitive_type VARCHAR(255),
	specification VARCHAR(255),
	version_no VARCHAR(100),
	publish_status SMALLINT DEFAULT 0,
	publish_name VARCHAR(100),
	publish_desc VARCHAR(255),
	publish_time TIMESTAMP,
	publish_user VARCHAR(100),
	approval_status SMALLINT,
	approval_desc VARCHAR(255),
	approval_time TIMESTAMP,
	approval_user VARCHAR(100),
	type INTEGER DEFAULT 0
);

CREATE TABLE qua_data_standard_config_master_data
(
	id SERIAL  PRIMARY KEY,
	config_id BIGINT NOT NULL ,
	master_data_id BIGINT NOT NULL ,
	remark VARCHAR(1000),
	create_time TIMESTAMP,
	create_user VARCHAR(60) NOT NULL ,
	update_time TIMESTAMP,
	update_user VARCHAR(60) NOT NULL ,
	tenant_id BIGINT NOT NULL ,
	db_type VARCHAR(32),
	snapshoot_version VARCHAR(100),
	column_name VARCHAR(255),
	column_name_cn VARCHAR(1000)
);

CREATE TABLE qua_data_standard_config_publish_log
(
	id SERIAL  PRIMARY KEY,
	config_id BIGINT,
	flag SMALLINT DEFAULT 0,
	publish_status SMALLINT DEFAULT 0,
	publish_name VARCHAR(100),
	publish_desc VARCHAR(255),
	publish_time TIMESTAMP,
	publish_user VARCHAR(100),
	approval_status SMALLINT,
	approval_desc VARCHAR(255),
	approval_time TIMESTAMP,
	approval_user VARCHAR(100)
);

CREATE TABLE qua_data_standard_config_restore_log
(
	id SERIAL  PRIMARY KEY,
	config_id BIGINT,
	restore_version_no VARCHAR(100),
	restore_desc VARCHAR(255),
	restore_user VARCHAR(100),
	restore_time TIMESTAMP,
	restore_result SMALLINT,
	from_version_no VARCHAR(100)
);

CREATE TABLE qua_data_standard_config_version_snapshot
(
	id SERIAL  PRIMARY KEY,
	cn_name VARCHAR(100) NOT NULL ,
	en_name VARCHAR(100) NOT NULL ,
	code VARCHAR(100) NOT NULL ,
	business_meaning VARCHAR(100) DEFAULT '',
	accordance VARCHAR(100) DEFAULT '',
	standard_type INTEGER NOT NULL ,
	data_length INTEGER,
	data_column_type VARCHAR(100),
	data_precision INTEGER,
	cn_en_verification INTEGER,
	custom_content VARCHAR(1000),
	remark VARCHAR(1000),
	create_time TIMESTAMP,
	create_user VARCHAR(60) NOT NULL ,
	update_time TIMESTAMP,
	update_user VARCHAR(60) NOT NULL ,
	tenant_id BIGINT NOT NULL ,
	set_id BIGINT,
	status INTEGER DEFAULT 1,
	encrypt VARCHAR(1),
	dept VARCHAR(255),
	business_system VARCHAR(255),
	store_cycle VARCHAR(255),
	database_name VARCHAR(255),
	sensitive_type VARCHAR(255),
	specification VARCHAR(255),
	version_no VARCHAR(100),
	config_id BIGINT
);

CREATE TABLE qua_data_standard_dic
(
	id SERIAL  PRIMARY KEY,
	dic_name VARCHAR(100) NOT NULL ,
	dic_code VARCHAR(100) NOT NULL ,
	dic_type VARCHAR(100) ,
	remark VARCHAR(1000),
	dic_config VARCHAR(1000),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60) NOT NULL ,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_data_standard_group
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(100),
	parent_id BIGINT,
	create_time TIMESTAMP,
	create_user VARCHAR(60) NOT NULL ,
	update_time TIMESTAMP,
	update_user VARCHAR(60) NOT NULL ,
	tenant_id BIGINT NOT NULL ,
	type INTEGER DEFAULT 0
);

CREATE TABLE qua_data_standard_set
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(100),
	create_time TIMESTAMP,
	create_user VARCHAR(60) NOT NULL ,
	update_time TIMESTAMP,
	update_user VARCHAR(60) NOT NULL ,
	tenant_id BIGINT NOT NULL ,
	group_id BIGINT
);

CREATE TABLE IF NOT EXISTS qua_dync_result
(
	id SERIAL  PRIMARY KEY,
	dync_id INTEGER NOT NULL ,
	result INTEGER DEFAULT 1,
	fail_error TEXT,
	line BIGINT,
	forecast_line BIGINT,
	space BIGINT,
	forecast_space BIGINT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	day_line_change BIGINT DEFAULT 0,
	forecast_day_line_change BIGINT DEFAULT 0,
	day_space_change BIGINT DEFAULT 0,
	forecast_day_space_change BIGINT DEFAULT 0
);

CREATE TABLE qua_dync_threshold
(
	id SERIAL  PRIMARY KEY,
	job_name VARCHAR(200),
	source_type VARCHAR(50),
	element_id INTEGER,
	db_name VARCHAR(100),
	table_name VARCHAR(100),
	index_name VARCHAR(100),
	model_type VARCHAR(50),
	forecast_target VARCHAR(100),
	remark VARCHAR(200),
	forecast_value BIGINT,
	status INTEGER DEFAULT 1,
	tenant_id INTEGER,
	create_time TIMESTAMP,
	create_user VARCHAR(128),
	update_time TIMESTAMP,
	update_user VARCHAR(128)
);

CREATE TABLE qua_internal_model
(
	id SERIAL  PRIMARY KEY,
	name VARCHAR(200),
	model_code VARCHAR(64),
	model_desc VARCHAR(200),
	model_calc VARCHAR(1024),
	dimensions VARCHAR(100),
	audit_object VARCHAR(100),
	request_param VARCHAR(200),
	custom_sql TEXT,
	model_type VARCHAR(2) DEFAULT '1',
	create_time TIMESTAMP,
	create_user VARCHAR(128),
	update_time TIMESTAMP,
	update_user VARCHAR(128),
	tenant_id BIGINT
);

CREATE TABLE qua_master_data
(
	id SERIAL  PRIMARY KEY,
	model_id BIGINT NOT NULL ,
	field_name VARCHAR(128) NOT NULL ,
	field_code VARCHAR(128) NOT NULL ,
	field_desc VARCHAR(128),
	data_type VARCHAR(128),
	field_length INTEGER,
	field_precision INTEGER,
	is_null VARCHAR(2),
	is_unique VARCHAR(2),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	flag VARCHAR(1) DEFAULT '1',
	tenant_id BIGINT NOT NULL ,
	data_status VARCHAR(1) DEFAULT '1',
	open_status VARCHAR(1) DEFAULT '1'
);

CREATE TABLE qua_master_data_model
(
	id SERIAL  PRIMARY KEY,
	model_name VARCHAR(128) NOT NULL ,
	model_code VARCHAR(128) NOT NULL ,
	model_type_id BIGINT NOT NULL ,
	model_status VARCHAR(2) DEFAULT '1',
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	flag VARCHAR(1) DEFAULT '1',
	tenant_id BIGINT NOT NULL ,
	open_status VARCHAR(1) DEFAULT '1'
);

CREATE TABLE qua_master_data_model_relation
(
	id SERIAL  PRIMARY KEY,
	model_id BIGINT NOT NULL ,
	related_model_id BIGINT NOT NULL ,
	related_type INTEGER NOT NULL
);

CREATE TABLE qua_monitor_job
(
	id SERIAL  PRIMARY KEY,
	model_id BIGINT NOT NULL ,
	name VARCHAR(128) NOT NULL ,
	execute_auto VARCHAR(5),
	execute_cycle VARCHAR(11),
	execute_cycle_desc VARCHAR(512),
	execute_type VARCHAR(11),
	execute_config VARCHAR(1000),
	execute_cron VARCHAR(50),
	define_time TIMESTAMP,
	start_time TIMESTAMP,
	end_time TIMESTAMP,
	valid_time TIMESTAMP,
	invalid_time TIMESTAMP,
	execute_current_time TIMESTAMP,
	execute_next_time TIMESTAMP,
	job_desc VARCHAR(1024),
	execute_status INTEGER,
	execute_result VARCHAR(1024),
	audit_remark VARCHAR(1024),
	job_rules VARCHAR(1024),
	rule_weight VARCHAR(1024),
	sample_cnt INTEGER,
	tenant_id BIGINT,
	create_user VARCHAR(100),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(100),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	status INTEGER,
	flag VARCHAR(1) DEFAULT '1',
	execute_rule_type VARCHAR(2),
	flow_id BIGINT
);

CREATE TABLE qua_monitor_model
(
	id SERIAL  PRIMARY KEY,
	model_name VARCHAR(128) NOT NULL ,
	model_desc VARCHAR(1024),
	element_id BIGINT NOT NULL ,
	element_type VARCHAR(16) NOT NULL ,
	snapshoot_version VARCHAR(100) NOT NULL ,
	database_id BIGINT,
	database_name VARCHAR(128),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	flag VARCHAR(1) DEFAULT '1',
	tenant_id BIGINT NOT NULL ,
	open_status VARCHAR(2) DEFAULT '1'
);

CREATE TABLE qua_monitor_model_resource
(
	id SERIAL  PRIMARY KEY,
	model_id BIGINT NOT NULL ,
	table_id BIGINT,
	table_name VARCHAR(128),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE IF NOT EXISTS qua_monitor_result
(
	id SERIAL  PRIMARY KEY,
	job_id BIGINT,
	task_id BIGINT,
	element_id VARCHAR(128),
	monitor_score VARCHAR(128),
	level_cnt VARCHAR(128),
	success_match_rules VARCHAR(512),
	success_not_match_rules VARCHAR(512),
	fail_rules VARCHAR(512),
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	tenant_id BIGINT,
	is_new VARCHAR(8),
	del_flag INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS qua_monitor_result_appeal
(
	id SERIAL  PRIMARY KEY,
	result_detail_id BIGINT,
	appeal_user VARCHAR(50),
	appeal_reason VARCHAR(200),
	appeal_time TIMESTAMP,
	handle_user VARCHAR(50),
	handle_time TIMESTAMP,
	handle_status SMALLINT DEFAULT 0,
	tenant_id BIGINT,
	del_flag INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS qua_monitor_result_detail
(
	id SERIAL  PRIMARY KEY,
	job_id BIGINT,
	task_id BIGINT,
	table_name VARCHAR(128),
	column_name VARCHAR(128),
	rule_type VARCHAR(128),
	rule_weight VARCHAR(256),
	rule_level VARCHAR(128),
	create_time TIMESTAMP,
	update_time TIMESTAMP,
	tenant_id BIGINT,
	template_id VARCHAR(128),
	rule_type_id BIGINT,
	is_match VARCHAR(2),
	execute_status INTEGER DEFAULT 0,
	rule_score INTEGER,
	compare_value VARCHAR(128),
	threshold_value VARCHAR(128),
	del_flag INTEGER DEFAULT 0
);

CREATE TABLE qua_monitor_rule
(
	id SERIAL  PRIMARY KEY,
	model_id BIGINT,
	rule_name VARCHAR(128),
	rule_code VARCHAR(128),
	rule_type VARCHAR(64) NOT NULL ,
	table_name VARCHAR(128),
	column_name VARCHAR(128),
	rule_detail VARCHAR(1024),
	rule_level VARCHAR(128),
	rule_desc VARCHAR(512),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	flag VARCHAR(1) DEFAULT '1',
	tenant_id BIGINT NOT NULL ,
	threshold_operator VARCHAR(20),
	template_id VARCHAR(128),
	threshold_value VARCHAR(128),
	rule_type_id BIGINT,
	rule_weight INTEGER
);

CREATE TABLE qua_monitor_rule_exec_record
(
	id SERIAL  PRIMARY KEY,
	rule_id BIGINT,
	exec_result VARCHAR(2),
	create_time TIMESTAMP,
	create_user VARCHAR(64),
	tenant_id BIGINT
);

CREATE TABLE IF NOT EXISTS qua_monitor_rule_template
(
	id SERIAL  PRIMARY KEY,
	template_name VARCHAR(512) NOT NULL ,
	model_id BIGINT NOT NULL ,
	table_name VARCHAR(128),
	column_name VARCHAR(50),
	time_range_start VARCHAR(20),
	time_range_end VARCHAR(20),
	data_time VARCHAR(10) DEFAULT '0',
	delete_flag VARCHAR(2) DEFAULT '0',
	data_filter VARCHAR(10) DEFAULT '0',
	create_time TIMESTAMP,
	create_user VARCHAR(128),
	update_time TIMESTAMP,
	update_user VARCHAR(128),
	tenant_id BIGINT NOT NULL ,
	filter_by_datetime INTEGER DEFAULT 0,
	datetime_function VARCHAR(100)
);

CREATE TABLE qua_monitor_rule_type
(
	id SERIAL  PRIMARY KEY,
	type_code VARCHAR(128) NOT NULL ,
	type_name VARCHAR(128) NOT NULL ,
	config_key VARCHAR(128),
	config_name VARCHAR(255) ,
	config_type VARCHAR(128),
	is_required VARCHAR(2),
	reg_pattern VARCHAR(512),
	interface_name VARCHAR(128),
	multi_select VARCHAR(2) DEFAULT '0',
	sort_no INTEGER,
	type_desc VARCHAR(1024)
);

CREATE TABLE IF NOT EXISTS qua_monitor_task
(
	id SERIAL  PRIMARY KEY,
	model_id BIGINT NOT NULL ,
	task_no VARCHAR(50),
	job_id BIGINT NOT NULL ,
	job_rules VARCHAR(1024),
	rule_weight VARCHAR(1024),
	sample_cnt INTEGER,
	start_time TIMESTAMP NOT NULL ,
	create_time TIMESTAMP,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	end_time TIMESTAMP,
	task_result INTEGER,
	fail_reason VARCHAR(100),
	run_process DOUBLE PRECISION,
	status INTEGER,
	tenant_id BIGINT
);

CREATE TABLE qua_monitor_weight
(
	id SERIAL  PRIMARY KEY,
	dim_name VARCHAR(128) NOT NULL ,
	dim_code VARCHAR(128) NOT NULL ,
	weight_value INTEGER NOT NULL ,
	weight_desc VARCHAR(512) NOT NULL ,
	create_time TIMESTAMP,
	create_user VARCHAR(128),
	update_time TIMESTAMP,
	update_user VARCHAR(128)
);

CREATE TABLE qua_scan_ch_column
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(20) NOT NULL ,
	table_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	db_name VARCHAR(128) NOT NULL ,
	table_name VARCHAR(128) NOT NULL ,
	column_name VARCHAR(255),
	type VARCHAR(255),
	position BIGINT,
	default_kind VARCHAR(255),
	default_expression VARCHAR(255),
	data_compressed_bytes BIGINT,
	data_uncompressed_bytes BIGINT,
	marks_bytes BIGINT,
	comment VARCHAR(1000),
	is_in_partition_key SMALLINT,
	is_in_sorting_key SMALLINT,
	is_in_primary_key SMALLINT,
	is_in_sampling_key SMALLINT,
	compression_codec VARCHAR(255),
	is_nullable SMALLINT,
	column_length INTEGER,
	column_precision INTEGER,
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE qua_scan_ch_db
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(20) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	db_name VARCHAR(64) NOT NULL ,
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE qua_scan_ch_table
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(20) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	db_id BIGINT NOT NULL ,
	db_name VARCHAR(30),
	table_name VARCHAR(128) NOT NULL ,
	uuid CHARACTER(36),
	engine VARCHAR(200),
	is_temporary SMALLINT,
	data_paths VARCHAR(200),
	metadata_path VARCHAR(200),
	metadata_modification_time VARCHAR(30),
	dependencies_database TEXT,
	dependencies_table TEXT,
	create_table_query TEXT,
	engine_full VARCHAR(500),
	partition_key VARCHAR(200),
	sorting_key VARCHAR(200),
	primary_key VARCHAR(200),
	sampling_key VARCHAR(200),
	storage_policy VARCHAR(200),
	total_rows BIGINT,
	total_bytes BIGINT,
	lifetime_rows BIGINT,
	lifetime_bytes BIGINT,
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE qua_scan_es_field
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(20) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	index_id BIGINT NOT NULL ,
	index_name VARCHAR(255),
	field_name VARCHAR(100),
	field_data_type VARCHAR(255),
	analyzer VARCHAR(500),
	boost VARCHAR(500),
	coerce VARCHAR(500),
	copy_to VARCHAR(500),
	doc_values VARCHAR(500),
	dynamic VARCHAR(500),
	eager_global_ordinals VARCHAR(500),
	enabled VARCHAR(500),
	fielddata VARCHAR(500),
	fields VARCHAR(500),
	format VARCHAR(500),
	ignore_above VARCHAR(500),
	ignore_malformed VARCHAR(500),
	index_options VARCHAR(500),
	index_phrases VARCHAR(500),
	index_prefixes VARCHAR(500),
	index VARCHAR(500),
	meta VARCHAR(500),
	normalizer VARCHAR(500),
	norms VARCHAR(500),
	null_value VARCHAR(500),
	position_increment_gap VARCHAR(500),
	properties TEXT,
	search_analyzer VARCHAR(500),
	similarity VARCHAR(500),
	store VARCHAR(500),
	term_vector VARCHAR(500),
	field_length INTEGER,
	field_precision INTEGER,
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE qua_scan_es_index
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(20) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	cluster_name VARCHAR(255),
	cluster_uuid VARCHAR(255),
	index_name VARCHAR(255),
	type_name VARCHAR(100),
	index_state VARCHAR(30),
	index_settings TEXT,
	aliases VARCHAR(255),
	primary_terms VARCHAR(255),
	in_sync_allocations VARCHAR(1000),
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE qua_scan_hive_column
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(20) NOT NULL ,
	table_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	db_name VARCHAR(128) NOT NULL ,
	table_name VARCHAR(128) NOT NULL ,
	column_name VARCHAR(255),
	type VARCHAR(255),
	comment VARCHAR(1000),
	is_nullable SMALLINT,
	column_length INTEGER,
	column_precision INTEGER,
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE qua_scan_hive_db
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(20) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	db_name VARCHAR(64) NOT NULL ,
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE qua_scan_hive_table
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(20) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	db_id BIGINT NOT NULL ,
	db_name VARCHAR(30),
	table_name VARCHAR(128) NOT NULL ,
	comment VARCHAR(1000),
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE qua_scan_list
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(20) NOT NULL ,
	scan_type VARCHAR(20),
	es_ip_port VARCHAR(1024),
	es_auth_type SMALLINT,
	es_user_name VARCHAR(60),
	es_user_password VARCHAR(100),
	es_kbs_account VARCHAR(100),
	es_keytab_file_path VARCHAR(255),
	es_krb5_file_path VARCHAR(255),
	es_jaas_file_path VARCHAR(255),
	es_prop_file_path VARCHAR(255),
	es_kbs_template_id BIGINT,
	ch_ip VARCHAR(20),
	ch_port INTEGER,
	ch_user_name VARCHAR(60),
	ch_user_password VARCHAR(100),
	ch_is_ssl SMALLINT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	scan_state SMALLINT DEFAULT 1,
	scan_process VARCHAR(8),
	scan_fail_reason VARCHAR(1000),
	kbs_enable INTEGER DEFAULT 0,
	jdbc_url VARCHAR(255)
);

CREATE TABLE qua_scan_mysql_column
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(20) NOT NULL ,
	table_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	db_name VARCHAR(128) NOT NULL ,
	table_name VARCHAR(128) NOT NULL ,
	column_name VARCHAR(255),
	type VARCHAR(255),
	comment VARCHAR(1000),
	column_key VARCHAR(16),
	is_nullable SMALLINT,
	column_length BIGINT,
	column_precision INTEGER,
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE qua_scan_mysql_db
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(20) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	db_name VARCHAR(64) NOT NULL ,
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE qua_scan_mysql_table
(
	id SERIAL  PRIMARY KEY,
	task_no VARCHAR(20) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	db_id BIGINT NOT NULL ,
	db_name VARCHAR(30),
	table_name VARCHAR(128) NOT NULL ,
	comment VARCHAR(1000),
	engine VARCHAR(200),
	verison VARCHAR(16),
	row_format VARCHAR(32),
	table_type VARCHAR(200),
	table_charset VARCHAR(200),
	total_rows BIGINT,
	snapshoot_version VARCHAR(100) NOT NULL
);

CREATE TABLE qua_server_used_statistic
(
	id SERIAL  PRIMARY KEY,
	tenant_id INTEGER,
	server_type VARCHAR(100),
	create_time TIMESTAMP,
	create_date VARCHAR(100)
);

CREATE TABLE qua_wab_element
(
	id SERIAL  PRIMARY KEY,
	element_type VARCHAR(20),
	element_name VARCHAR(100) NOT NULL ,
	es_ip_port VARCHAR(1024),
	es_auth_type SMALLINT,
	es_user_name VARCHAR(60),
	es_user_password VARCHAR(100),
	es_kbs_account VARCHAR(100),
	es_keytab_file_path VARCHAR(255),
	es_krb5_file_path VARCHAR(255),
	es_jaas_file_path VARCHAR(255),
	es_kbs_template_id BIGINT,
	ch_ip VARCHAR(20),
	ch_port INTEGER,
	ch_user_name VARCHAR(60),
	ch_user_password VARCHAR(100),
	ch_is_ssl SMALLINT DEFAULT 1,
	is_connect SMALLINT NOT NULL ,
	fail_connect_reason VARCHAR(100),
	execute_type VARCHAR(20) NOT NULL ,
	config_json VARCHAR(200) NOT NULL ,
	late_scan_time TIMESTAMP,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60) NOT NULL ,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60) NOT NULL ,
	tenant_id BIGINT NOT NULL ,
	flag SMALLINT DEFAULT 1,
	key_tab_path VARCHAR(512),
	krb5_conf_path VARCHAR(512),
	jaas_conf_path VARCHAR(512),
	kbs_enable INTEGER DEFAULT 0,
	jdbc_url VARCHAR(255)
);

CREATE TABLE qua_web_ch_element_detail_column
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	table_name VARCHAR(255),
	column_name VARCHAR(255),
	column_name_cn VARCHAR(1000),
	is_sensitive SMALLINT DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	is_available SMALLINT DEFAULT 1,
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	business_type BIGINT,
	sen_level_id INTEGER,
	sen_level_name VARCHAR(200),
	sen_type_id INTEGER,
	sen_type_name VARCHAR(200),
	is_required INTEGER DEFAULT 0,
	is_encrypted INTEGER DEFAULT 0,
	desensitization_id INTEGER
);

CREATE TABLE qua_web_ch_element_detail_db
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_ch_element_detail_table
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	table_name VARCHAR(255),
	table_name_cn VARCHAR(255),
	table_dscribe VARCHAR(255),
	table_owner VARCHAR(255),
	is_sensitive SMALLINT DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	is_available SMALLINT DEFAULT 1,
	ext_attrs VARCHAR(1024),
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_ch_task_result_column
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	table_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	db_name VARCHAR(128) NOT NULL ,
	table_name VARCHAR(128) NOT NULL ,
	column_name VARCHAR(255),
	type VARCHAR(255),
	position BIGINT,
	default_kind VARCHAR(255),
	default_expression VARCHAR(255),
	data_compressed_bytes BIGINT,
	data_uncompressed_bytes BIGINT,
	marks_bytes BIGINT,
	comment VARCHAR(1000),
	is_in_partition_key SMALLINT,
	is_in_sorting_key SMALLINT,
	is_in_primary_key SMALLINT,
	is_in_sampling_key SMALLINT,
	compression_codec VARCHAR(255),
	is_nullable SMALLINT,
	column_length INTEGER,
	column_precision INTEGER,
	bussiness_type VARCHAR(64),
	snapshoot_version VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_ch_task_result_db
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	task_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	db_name VARCHAR(64) NOT NULL ,
	snapshoot_version VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_ch_task_result_table
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	db_id BIGINT NOT NULL ,
	db_name VARCHAR(30),
	table_name VARCHAR(128) NOT NULL ,
	uuid CHARACTER(36),
	engine VARCHAR(200),
	is_temporary SMALLINT,
	data_paths VARCHAR(200),
	metadata_path VARCHAR(200),
	metadata_modification_time VARCHAR(30),
	dependencies_database TEXT,
	dependencies_table TEXT,
	create_table_query TEXT,
	engine_full VARCHAR(500),
	partition_key VARCHAR(200),
	sorting_key VARCHAR(200),
	primary_key VARCHAR(200),
	sampling_key VARCHAR(200),
	storage_policy VARCHAR(200),
	total_rows BIGINT,
	total_bytes BIGINT,
	lifetime_rows BIGINT,
	lifetime_bytes BIGINT,
	snapshoot_version VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_es_element_detail_field
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	index_name VARCHAR(255),
	field_name VARCHAR(255),
	field_name_cn VARCHAR(255),
	is_sensitive SMALLINT DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	is_available SMALLINT DEFAULT 1,
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	business_type BIGINT,
	sen_level_id INTEGER,
	sen_level_name VARCHAR(200),
	sen_type_id INTEGER,
	sen_type_name VARCHAR(200),
	is_required INTEGER DEFAULT 0,
	is_encrypted INTEGER DEFAULT 0,
	desensitization_id INTEGER
);

CREATE TABLE qua_web_es_element_detail_index
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	host_address VARCHAR(255),
	port INTEGER,
	index_name VARCHAR(255),
	index_name_cn VARCHAR(255),
	index_dscribe VARCHAR(255),
	index_owner VARCHAR(255),
	type_name VARCHAR(255),
	is_sensitive SMALLINT DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	is_available SMALLINT DEFAULT 1,
	ext_attrs VARCHAR(1024),
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_es_task_result_field
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	index_id BIGINT NOT NULL ,
	index_name VARCHAR(255),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	field_name VARCHAR(100),
	field_data_type VARCHAR(255),
	analyzer VARCHAR(500),
	boost VARCHAR(500),
	coerce VARCHAR(500),
	copy_to VARCHAR(500),
	doc_values VARCHAR(500),
	dynamic VARCHAR(500),
	eager_global_ordinals VARCHAR(500),
	enabled VARCHAR(500),
	fielddata VARCHAR(500),
	fields VARCHAR(500),
	format VARCHAR(500),
	ignore_above VARCHAR(500),
	ignore_malformed VARCHAR(500),
	index_options VARCHAR(500),
	index_phrases VARCHAR(500),
	index_prefixes VARCHAR(500),
	index VARCHAR(500),
	meta VARCHAR(500),
	normalizer VARCHAR(500),
	norms VARCHAR(500),
	null_value VARCHAR(500),
	position_increment_gap VARCHAR(500),
	properties TEXT,
	search_analyzer VARCHAR(500),
	similarity VARCHAR(500),
	store VARCHAR(500),
	term_vector VARCHAR(500),
	field_length INTEGER,
	field_precision INTEGER,
	bussiness_type VARCHAR(64),
	snapshoot_version VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_es_task_result_index
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	task_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	cluster_name VARCHAR(255),
	cluster_uuid VARCHAR(255),
	index_name VARCHAR(255),
	type_name VARCHAR(100),
	index_state VARCHAR(30),
	index_settings TEXT,
	aliases VARCHAR(255),
	primary_terms VARCHAR(255),
	in_sync_allocations VARCHAR(1000),
	snapshoot_version VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_hive_element_detail_column
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	table_name VARCHAR(255),
	column_name VARCHAR(255),
	column_name_cn VARCHAR(1000),
	is_sensitive SMALLINT DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	is_available SMALLINT DEFAULT 1,
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	business_type BIGINT,
	sen_level_id INTEGER,
	sen_level_name VARCHAR(200),
	sen_type_id INTEGER,
	sen_type_name VARCHAR(200),
	is_required INTEGER DEFAULT 0,
	is_encrypted INTEGER DEFAULT 0,
	desensitization_id INTEGER
);

CREATE TABLE qua_web_hive_element_detail_db
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_hive_element_detail_table
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	table_name VARCHAR(255),
	table_name_cn VARCHAR(255),
	table_dscribe VARCHAR(255),
	table_owner VARCHAR(255),
	is_sensitive SMALLINT DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	is_available SMALLINT DEFAULT 1,
	ext_attrs VARCHAR(1024),
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_hive_task_result_column
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	table_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	db_name VARCHAR(128) NOT NULL ,
	table_name VARCHAR(128) NOT NULL ,
	column_name VARCHAR(255),
	type VARCHAR(255),
	comment VARCHAR(1000),
	is_nullable SMALLINT,
	column_length INTEGER,
	column_precision INTEGER,
	bussiness_type VARCHAR(64),
	snapshoot_version VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_hive_task_result_db
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	task_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	db_name VARCHAR(64) NOT NULL ,
	snapshoot_version VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_hive_task_result_table
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	db_id BIGINT NOT NULL ,
	db_name VARCHAR(30),
	table_name VARCHAR(128) NOT NULL ,
	comment VARCHAR(1000),
	snapshoot_version VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_job
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT NOT NULL ,
	is_map_to_job SMALLINT DEFAULT 1,
	job_group VARCHAR(30),
	job_name VARCHAR(100) NOT NULL ,
	job_type SMALLINT NOT NULL ,
	job_cron VARCHAR(255),
	job_cron_expression VARCHAR(255) NOT NULL ,
	config_date_time TIMESTAMP,
	job_state SMALLINT DEFAULT 1,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60) NOT NULL ,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60) NOT NULL ,
	late_start_time TIMESTAMP,
	late_ent_time TIMESTAMP,
	tenant_id BIGINT NOT NULL ,
	flag SMALLINT
);

CREATE TABLE qua_web_kbs_file_config
(
	id SERIAL  PRIMARY KEY,
	template_name VARCHAR(255) NOT NULL ,
	kbs_account VARCHAR(255) NOT NULL ,
	keytab_file_path VARCHAR(255),
	krb5_file_path VARCHAR(255),
	jaas_file_path VARCHAR(255),
	remark VARCHAR(255),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	tenant_id BIGINT,
	flag SMALLINT DEFAULT 1
);

CREATE TABLE qua_web_mysql_element_detail_column
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	table_name VARCHAR(255),
	column_name VARCHAR(255),
	column_name_cn VARCHAR(1000),
	is_sensitive SMALLINT DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	is_available SMALLINT DEFAULT 1,
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL ,
	business_type BIGINT,
	sen_level_id INTEGER,
	sen_level_name VARCHAR(200),
	sen_type_id INTEGER,
	sen_type_name VARCHAR(200),
	is_required INTEGER DEFAULT 0,
	is_encrypted INTEGER DEFAULT 0,
	desensitization_id INTEGER
);

CREATE TABLE qua_web_mysql_element_detail_db
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_mysql_element_detail_table
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	table_name VARCHAR(255),
	table_name_cn VARCHAR(255),
	table_dscribe VARCHAR(255),
	table_owner VARCHAR(255),
	is_sensitive SMALLINT DEFAULT 0,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(60),
	is_available SMALLINT DEFAULT 1,
	ext_attrs VARCHAR(1024),
	first_snapshoot_version VARCHAR(100),
	last_snapshoot_version VARCHAR(100),
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_mysql_task_result_column
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	table_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	db_name VARCHAR(128) NOT NULL ,
	table_name VARCHAR(128) NOT NULL ,
	column_name VARCHAR(255),
	type VARCHAR(255),
	comment VARCHAR(1000),
	column_key VARCHAR(16),
	is_nullable SMALLINT,
	column_length INTEGER,
	column_precision INTEGER,
	bussiness_type VARCHAR(64),
	snapshoot_version VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_mysql_task_result_db
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	task_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	db_name VARCHAR(64) NOT NULL ,
	snapshoot_version VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_mysql_task_result_table
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(60),
	db_id BIGINT NOT NULL ,
	db_name VARCHAR(30),
	table_name VARCHAR(128) NOT NULL ,
	comment VARCHAR(1000),
	engine VARCHAR(200),
	verison SMALLINT,
	row_format SMALLINT,
	table_type VARCHAR(200),
	table_charset VARCHAR(200),
	total_rows BIGINT,
	snapshoot_version VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE qua_web_task
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	job_id BIGINT NOT NULL ,
	task_no VARCHAR(20) NOT NULL ,
	start_time TIMESTAMP,
	end_time TIMESTAMP,
	task_progress VARCHAR(8) NOT NULL ,
	check_num SMALLINT DEFAULT 0,
	status SMALLINT,
	result text null ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	tenant_id BIGINT NOT NULL ,
	snapshoot_version VARCHAR(100)
);

CREATE TABLE qua_web_user_operate_log
(
	id SERIAL  PRIMARY KEY,
	tenant_id BIGINT NOT NULL ,
	user_name VARCHAR(100),
	request_ip VARCHAR(255) NOT NULL ,
	operate_model VARCHAR(100),
	operate_type VARCHAR(255) NOT NULL ,
	operate_desc TEXT NOT NULL ,
	result VARCHAR(20),
	create_user VARCHAR(60) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE sys_db_version_upgrade
(
	id VARCHAR PRIMARY KEY,
	version VARCHAR(50),
	module_name VARCHAR(255),
	description VARCHAR(255),
	content TEXT,
	error_content TEXT,
	version_code VARCHAR(255),
	status SMALLINT,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_time VARCHAR(30),
	success_content TEXT,
	error_msg TEXT,
	is_atomic SMALLINT,
	exclude_content TEXT
);

CREATE TABLE tb_cluster
(
	ID SERIAL  PRIMARY KEY,
	CLUSTER_NAME VARCHAR(100),
	CLUSTER_TYPE VARCHAR(100),
	CLUSTER_IP VARCHAR(100),
	CLUSTER_PORT VARCHAR(100),
	CLUSTER_USERNAME VARCHAR(100),
	CLUSTER_PASSWORD VARCHAR(100),
	CLUSTER_VERSION VARCHAR(100),
	JMX_USERNAME VARCHAR(100),
	JMX_PASSWORD VARCHAR(100),
	ZOOKEEPER VARCHAR(100),
	CLUSTER_AUTH_TYPE VARCHAR(100),
	DEL_FLAG VARCHAR(1) DEFAULT '0',
	STATUS VARCHAR(1) DEFAULT '0',
	CREATE_TIME TIMESTAMP,
	CREATE_USER VARCHAR(100),
	UPDATE_TIME TIMESTAMP,
	UPDATE_USER VARCHAR(100),
	manufacturer_type INTEGER DEFAULT 0,
	cluster_status VARCHAR(1) DEFAULT '1',
	key_tab_path VARCHAR(512),
	krb5_conf_path VARCHAR(512),
	jaas_conf_path VARCHAR(512),
	kbs_account VARCHAR(128),
	enable_kbs VARCHAR(8) DEFAULT '0',
	data_file_path VARCHAR(500),
	jdbc_url VARCHAR(255)
);

CREATE TABLE tb_cluster_kafka_topic
(
	id SERIAL  PRIMARY KEY,
	topic VARCHAR(100),
	tenant_name VARCHAR(100),
	PARTITIONS INTEGER,
	brokers INTEGER,
	lags BIGINT,
	data_name VARCHAR(100),
	flow_id VARCHAR(100),
	groups INTEGER,
	replicas INTEGER,
	spread VARCHAR(100),
	create_time TIMESTAMP,
	is_deleted INTEGER DEFAULT 0,
	cluster_id BIGINT
);

CREATE TABLE tb_datasource_info
(
	ID SERIAL  PRIMARY KEY,
	TENANT_ID BIGINT,
	DATASOURCE_URL VARCHAR(200),
	DATASOURCE_DRIVER VARCHAR(128),
	DATASOURCE_USERNAME VARCHAR(128),
	DATASOURCE_PASSWORD VARCHAR(128),
	DATASOURCE_TYPE VARCHAR(128),
	DEL_FALG VARCHAR(1) DEFAULT '0',
	STATUS VARCHAR(1) DEFAULT '0',
	CREATE_TIME TIMESTAMP,
	CREATE_USER VARCHAR(100),
	UPDATE_TIME TIMESTAMP,
	UPDATE_USER VARCHAR(100),
	CLUSTER_ID BIGINT
);

CREATE TABLE tb_dic
(
	ID SERIAL  PRIMARY KEY,
	NAME VARCHAR(100),
	ALIAS VARCHAR(100),
	PARENT_ID BIGINT,
	unique_check_code VARCHAR(100),
	hidden INTEGER DEFAULT 0
);

CREATE TABLE tb_monitor_topic
(
	id SERIAL  PRIMARY KEY,
	topic VARCHAR(100),
	lags VARCHAR(100),
	time TIMESTAMP,
	cluster_id BIGINT
);

CREATE TABLE tb_script_history
(
	id SERIAL  PRIMARY KEY,
	tenant_id VARCHAR(255) NOT NULL ,
	cluster_id VARCHAR(255),
	del_flag VARCHAR(255) DEFAULT '0',
	script_file VARCHAR(255) NOT NULL ,
	script_type VARCHAR(32) NOT NULL ,
	db_type VARCHAR(32) NOT NULL ,
	create_time TIMESTAMP,
	create_user VARCHAR(255),
	update_time TIMESTAMP,
	update_user VARCHAR(255),
	error_msg TEXT
);

CREATE TABLE tb_statistics_cluster_instance_disk_sync
(
	ID SERIAL  PRIMARY KEY,
	TENANT_ID BIGINT,
	CLUSTER_ID BIGINT,
	CLUSTER_TYPE VARCHAR(100),
	INSTANCE VARCHAR(100),
	used BIGINT,
	create_time TIMESTAMP,
	free BIGINT
);

CREATE TABLE tb_statistics_disk
(
	id SERIAL  PRIMARY KEY,
	resource_type VARCHAR(100),
	used BIGINT,
	free BIGINT,
	create_time TIMESTAMP,
	flag INTEGER DEFAULT 1
);

CREATE TABLE tb_statistics_disk_sync
(
	id SERIAL  PRIMARY KEY,
	ip VARCHAR(100) NOT NULL ,
	file_system VARCHAR(100),
	total BIGINT,
	used BIGINT,
	free BIGINT,
	used_rate VARCHAR(100),
	mount_point VARCHAR(100),
	create_time TIMESTAMP,
	flag INTEGER DEFAULT 1
);

CREATE TABLE tb_statistics_disk_tenant
(
	id SERIAL  PRIMARY KEY,
	tenant_id BIGINT,
	tenant_name VARCHAR(100),
	clickhouse_used BIGINT,
	clickhouse_free BIGINT,
	mysql_used BIGINT,
	mysql_free BIGINT,
	hdfs_used BIGINT,
	hdfs_free BIGINT,
	elasticsearch_used BIGINT,
	elasticsearch_free BIGINT,
	create_time TIMESTAMP,
	flag INTEGER DEFAULT 1
);

CREATE TABLE tb_statistics_etl_data
(
	id SERIAL  PRIMARY KEY,
	etl_name VARCHAR(100) NOT NULL ,
	etl_count BIGINT,
	create_time TIMESTAMP,
	flag INTEGER DEFAULT 1
);

CREATE TABLE tb_statistics_etl_task
(
	id SERIAL  PRIMARY KEY,
	etl_type VARCHAR(100) NOT NULL ,
	total BIGINT,
	create_time TIMESTAMP,
	flag INTEGER DEFAULT 1
);

CREATE TABLE tb_statistics_etl_task_day
(
	id SERIAL  PRIMARY KEY,
	day VARCHAR(100) NOT NULL ,
	task_count BIGINT,
	create_time TIMESTAMP,
	flag INTEGER DEFAULT 1
);

CREATE TABLE tb_statistics_hdfs_sync
(
	id SERIAL  PRIMARY KEY,
	ip VARCHAR(100) NOT NULL ,
	dir VARCHAR(100),
	size BIGINT,
	create_time TIMESTAMP,
	flag INTEGER DEFAULT 1
);

CREATE TABLE tb_statistics_subscription
(
	id SERIAL  PRIMARY KEY,
	tenant_id INTEGER NOT NULL ,
	subscription_service VARCHAR(100),
	create_time TIMESTAMP,
	flag INTEGER DEFAULT 1
);

CREATE TABLE tb_tenant
(
	ID SERIAL  PRIMARY KEY,
	TENANT_CODE VARCHAR(200),
	TENANT_NAME VARCHAR(255),
	ACCOUNT_TYPE VARCHAR(1),
	CONTACT_MOBILE VARCHAR(30),
	DEL_FLAG VARCHAR(1) DEFAULT '0',
	CONTACT_AREA VARCHAR(100),
	FUNCTIONS VARCHAR(200),
	RESOURCE_STATUS VARCHAR(2) DEFAULT '0',
	CREATE_USER VARCHAR(50),
	CREATE_TIME TIMESTAMP,
	UPDATE_USER VARCHAR(50),
	UPDATE_TIME TIMESTAMP,
	ACCOUNT_NAME VARCHAR(100),
	ACCOUNT_PWD VARCHAR(100),
	TENANT_ID BIGINT,
	DB_NAME VARCHAR(100),
	FA_EXTEND_INFO VARCHAR(2048)
);

CREATE TABLE tb_tenant_cluster
(
	ID SERIAL  PRIMARY KEY,
	TENANT_ID BIGINT,
	CLUSTER_ID BIGINT,
	CLUSTER_TYPE VARCHAR(100),
	INSTANCE VARCHAR(100),
	TOPIC VARCHAR(100),
	QUOTA VARCHAR(100),
	SHARDS VARCHAR(100),
	REPLICAS VARCHAR(100)
);

CREATE TABLE ueba_dictionary
(
	id SERIAL  PRIMARY KEY,
	key_code VARCHAR(255) NOT NULL ,
	value VARCHAR(255),
	enable INTEGER,
	remark VARCHAR(4000),
	sortno INTEGER,
	type VARCHAR(255),
	parent_id VARCHAR(255)
);

CREATE TABLE ums_sys_auth_config
(
	id SERIAL  PRIMARY KEY,
	auth_type SMALLINT NOT NULL ,
	enable_status SMALLINT NOT NULL ,
	default_status SMALLINT NOT NULL ,
	display_name VARCHAR(200) NOT NULL ,
	sync_status SMALLINT,
	enable_create_user SMALLINT NOT NULL ,
	role_id VARCHAR(50),
	grant_type SMALLINT,
	oauth_base_field_info TEXT,
	oauth_code_request_url VARCHAR(1000),
	oauth_code_request_way VARCHAR(20),
	oauth_code_resp_field VARCHAR(50),
	oauth_code_field_info TEXT,
	oauth_token_request_url VARCHAR(1000),
	oauth_token_request_way VARCHAR(20),
	oauth_token_resp_field VARCHAR(50),
	oauth_token_resp_format VARCHAR(20),
	oauth_token_field_info TEXT,
	oauth_user_request_url VARCHAR(1000),
	oauth_user_request_way VARCHAR(20),
	oauth_user_resp_field VARCHAR(50),
	oauth_user_resp_format VARCHAR(20),
	oauth_user_field_info TEXT,
	fa_app_field VARCHAR(50),
	fa_login_url VARCHAR(1000),
	fa_auth_url VARCHAR(1000),
	fa_user_resp_field VARCHAR(50),
	fa_request_protocol VARCHAR(20),
	fa_method_name VARCHAR(50),
	fa_request_way VARCHAR(20),
	fa_login_field_info TEXT,
	fa_check_field_info TEXT,
	create_user VARCHAR(100),
	create_time TIMESTAMP NOT NULL ,
	update_user VARCHAR(100),
	update_time TIMESTAMP NOT NULL ,
	fa_request_xml_template VARCHAR(1024),
	fa_vendor VARCHAR(64) DEFAULT 'asiaInfoSec'
);

CREATE TABLE ums_sys_datasource_config
(
	id SERIAL  PRIMARY KEY,
	moudle_name VARCHAR(32),
	datasource_type VARCHAR(16),
	ip VARCHAR(64),
	db_name VARCHAR(64),
	port VARCHAR(16),
	user_name VARCHAR(128),
	password VARCHAR(128),
	show_name VARCHAR(128),
	time_out INTEGER,
	encrypt VARCHAR(2),
	status VARCHAR(2),
	copy_cnt INTEGER,
	db_url VARCHAR(512),
	create_user VARCHAR(16),
	create_time TIMESTAMP,
	update_user VARCHAR(16),
	update_time TIMESTAMP
);

CREATE TABLE ums_sys_license
(
	license BYTEA,
	sn_data VARCHAR(1024),
	sn_ip VARCHAR(512)
);

CREATE TABLE ums_sys_log
(
	id SERIAL  PRIMARY KEY,
	user_name VARCHAR(16) DEFAULT '',
	real_name VARCHAR(16) DEFAULT '',
	login_ip VARCHAR(32) DEFAULT '',
	user_agent VARCHAR(256) DEFAULT '',
	request_path VARCHAR(64) DEFAULT '',
	log_name VARCHAR(2048) DEFAULT '',
	log_result VARCHAR(32) DEFAULT '',
	opt_type VARCHAR(32) DEFAULT '',
    opt_type_code VARCHAR(32) DEFAULT '',
	opt_module VARCHAR(64) DEFAULT '',
    opt_module_code VARCHAR(64) DEFAULT '',
	http_method VARCHAR(16) DEFAULT '',
	create_time TIMESTAMP NOT NULL
);

CREATE TABLE ums_sys_menus
(
	id SERIAL  PRIMARY KEY,
	menu_type VARCHAR(32) NOT NULL ,
	menu_property VARCHAR(2) NOT NULL ,
	menu_level INTEGER NOT NULL ,
	root_parent VARCHAR(64),
	menu_name VARCHAR(64) NOT NULL ,
	menu_code VARCHAR(64) NOT NULL ,
	menu_path VARCHAR(512),
	manage_free VARCHAR(2) DEFAULT '0',
	hidden VARCHAR(2) DEFAULT '0',
	parent_name VARCHAR(64),
	status VARCHAR(2) DEFAULT '1',
	menu_order INTEGER,
	default_order INTEGER NOT NULL ,
	default_name VARCHAR(64) NOT NULL ,
	default_status VARCHAR(2) NOT NULL ,
	default_parent VARCHAR(64),
	create_user VARCHAR(100),
	create_date TIMESTAMP,
	update_user VARCHAR(100),
	update_date TIMESTAMP,
	application VARCHAR(100),
	level1 VARCHAR(100),
	menu_category VARCHAR(2) DEFAULT '0',
	new_open_window INTEGER DEFAULT 0
);

CREATE TABLE ums_sys_role
(
	role_id VARCHAR PRIMARY KEY,
	role_name VARCHAR(100),
	role_describe VARCHAR(255),
	builtin SMALLINT NOT NULL ,
	del_flag SMALLINT NOT NULL ,
	create_user VARCHAR(100),
	create_date BIGINT,
	update_user VARCHAR(100),
	update_date BIGINT,
	is_admin VARCHAR(1)
);

CREATE TABLE ums_sys_user
(
	user_id SERIAL  PRIMARY KEY,
	dept_id VARCHAR(100),
	department_id VARCHAR(64),
	user_name VARCHAR(300),
	password VARCHAR(32) NOT NULL ,
	password_old VARCHAR(32),
	period_from BIGINT NOT NULL ,
	period_to BIGINT NOT NULL ,
	status SMALLINT NOT NULL ,
	real_name VARCHAR(300),
	code VARCHAR(100),
	sex VARCHAR(1),
	telephone VARCHAR(20),
	email VARCHAR(100) NOT NULL ,
	lant_id VARCHAR(32),
	leader VARCHAR(100),
	login_voucher VARCHAR(100),
	fail_count INTEGER,
	lock_date TIMESTAMP,
	final_login_ip VARCHAR(64),
	final_login_date BIGINT,
	builtin SMALLINT NOT NULL ,
	security_code VARCHAR(128),
	remark VARCHAR(300),
	del_flag SMALLINT NOT NULL ,
	tenant_id INTEGER,
	queue VARCHAR(64),
	create_user VARCHAR(100),
	create_date BIGINT,
	update_user VARCHAR(100),
	update_date BIGINT,
	social_account VARCHAR(100),
	first_login_fail_time TIMESTAMP,
	is_first_login VARCHAR(1),
	is_need_update_password VARCHAR(1),
	default_router_id VARCHAR(100),
	default_router_name VARCHAR(100),
	mobile VARCHAR(11),
	dept_name VARCHAR(100),
	last_update_password_time TIMESTAMP,
	default_dashboard INTEGER,
	data_limit_extend_role_id VARCHAR(64)
);

CREATE TABLE ums_sys_user_4a
(
	user_id SERIAL  PRIMARY KEY,
	dept_id VARCHAR(100),
	user_name VARCHAR(300),
	password VARCHAR(100) NOT NULL ,
	password_old VARCHAR(32),
	period_from BIGINT NOT NULL ,
	period_to BIGINT NOT NULL ,
	status SMALLINT NOT NULL ,
	real_name VARCHAR(300),
	code VARCHAR(100),
	sex VARCHAR(1),
	telephone VARCHAR(20),
	email VARCHAR(100) NOT NULL ,
	leader VARCHAR(100),
	login_voucher VARCHAR(100),
	fail_count INTEGER,
	lock_date TIMESTAMP,
	final_login_ip VARCHAR(64),
	final_login_date BIGINT,
	builtin SMALLINT NOT NULL ,
	security_code VARCHAR(128),
	remark VARCHAR(300),
	del_flag SMALLINT NOT NULL ,
	tenant_id INTEGER,
	queue VARCHAR(64),
	create_user VARCHAR(100),
	create_date BIGINT,
	update_user VARCHAR(100),
	update_date BIGINT,
	social_account VARCHAR(100),
	first_login_fail_time TIMESTAMP,
	is_first_login VARCHAR(1),
	is_need_update_password VARCHAR(1),
	default_router_id VARCHAR(100),
	default_router_name VARCHAR(100),
	default_dashboard INTEGER,
	mobile VARCHAR(11),
	dept_name VARCHAR(100),
	last_update_password_time TIMESTAMP,
	tenant_name VARCHAR(100),
	role_id VARCHAR(100),
	role_name VARCHAR(100),
	allocate_status INTEGER DEFAULT 0,
	is_super_admin INTEGER DEFAULT 0
);

CREATE TABLE zeppelin_interpreter
(
	id SERIAL  PRIMARY KEY,
	tenant_id VARCHAR(255) NOT NULL ,
	interpreter_remote_id VARCHAR(255) NOT NULL ,
	interpreter_alias VARCHAR(255) DEFAULT '',
	create_time TIMESTAMP,
	create_user VARCHAR(255),
	update_time TIMESTAMP,
	update_user VARCHAR(255),
	interpreter_type VARCHAR(100),
	interpreter_name VARCHAR(100)
);

CREATE TABLE zeppelin_notebook
(
	id SERIAL  PRIMARY KEY,
	tenant_id VARCHAR(255) NOT NULL ,
	note_remote_id VARCHAR(255) NOT NULL ,
	note_alias VARCHAR(255) DEFAULT '',
	create_time TIMESTAMP,
	create_user VARCHAR(255),
	update_time TIMESTAMP,
	update_user VARCHAR(255),
	interpreter_id INTEGER
);



COMMENT ON COLUMN api_apply.id IS '表主键';
COMMENT ON COLUMN api_apply.api_id IS 'api主键';
COMMENT ON COLUMN api_apply.client_id IS '客户端主键';
COMMENT ON COLUMN api_apply.call_limit IS '申请次数限制';
COMMENT ON COLUMN api_apply.begin_date IS '申请周期开始时间';
COMMENT ON COLUMN api_apply.end_date IS '申请周期结束时间';
COMMENT ON COLUMN api_apply.create_by IS '创建人';
COMMENT ON COLUMN api_apply.create_date IS '创建时间';
COMMENT ON COLUMN api_apply.modify_by IS '修改人';
COMMENT ON COLUMN api_apply.modify_date IS '修改时间';
COMMENT ON COLUMN api_apply.apply_user IS '申请人';
COMMENT ON COLUMN api_apply.apply_time IS '申请时间';
COMMENT ON COLUMN api_apply.audit_user IS '审核人';
COMMENT ON COLUMN api_apply.audit_time IS '审核时间';
COMMENT ON COLUMN api_apply.apply_reason IS '申请说明';
COMMENT ON COLUMN api_apply.audit_reason IS '审核说明';
COMMENT ON COLUMN api_apply.audit_status IS '审核状态,null：未申请，1：审批中，2：已审批，3：已停用';
COMMENT ON COLUMN api_apply.audit_authorization_status IS '审核授权状态,1:已通过,2:已拒绝,3:取消授权,4:已过期,5:已停用';
COMMENT ON COLUMN api_apply.tenant_id IS '租户id';
COMMENT ON COLUMN api_apply.user_id IS '用户id';
COMMENT ON COLUMN api_catalog.parent_id IS '父id';
COMMENT ON COLUMN api_catalog.create_user IS '创建人名称';
COMMENT ON COLUMN api_catalog.create_time IS '创建时间';
COMMENT ON COLUMN api_catalog.update_user IS '更新人名称';
COMMENT ON COLUMN api_catalog.update_time IS '更新时间';
COMMENT ON COLUMN api_catalog.tenant_id IS '租户id';
COMMENT ON COLUMN api_client.id IS '客户端ID';
COMMENT ON COLUMN api_client.client_secret IS '客户端秘钥';
COMMENT ON COLUMN api_client.client_name IS '客户端名称';
COMMENT ON COLUMN api_client.notes IS '备注';
COMMENT ON COLUMN api_client.client_status IS '应用状态，0禁用，1启用';
COMMENT ON COLUMN api_client.create_by IS '创建人';
COMMENT ON COLUMN api_client.create_date IS '创建日期';
COMMENT ON COLUMN api_client.modify_by IS '修改人';
COMMENT ON COLUMN api_client.modify_date IS '修改日期';
COMMENT ON COLUMN api_client.domain_id IS '租户ID';
COMMENT ON COLUMN api_client_auth.id IS '关联表ID';
COMMENT ON COLUMN api_client_auth.api_id IS 'api主键';
COMMENT ON COLUMN api_client_auth.client_id IS '客户端ID';
COMMENT ON COLUMN api_client_auth.apply_id IS '申请id';
COMMENT ON COLUMN api_crypto.public_key IS '公钥';
COMMENT ON COLUMN api_crypto.private_key IS '私钥';
COMMENT ON COLUMN api_crypto.create_user IS '创建人名称';
COMMENT ON COLUMN api_crypto.create_time IS '创建时间';
COMMENT ON COLUMN api_crypto.update_user IS '更新人名称';
COMMENT ON COLUMN api_crypto.update_time IS '更新时间';
COMMENT ON COLUMN api_crypto.tenant_id IS '租户id';
COMMENT ON COLUMN api_data_desensitization.name IS '规则名称';
COMMENT ON COLUMN api_data_desensitization.type IS '脱敏类型，1截断，2偏移，3规整，4替换，5重写，6加密';
COMMENT ON COLUMN api_data_desensitization.create_user IS '创建人名称';
COMMENT ON COLUMN api_data_desensitization.create_time IS '创建时间';
COMMENT ON COLUMN api_data_desensitization.update_user IS '更新人名称';
COMMENT ON COLUMN api_data_desensitization.update_time IS '更新时间';
COMMENT ON COLUMN api_data_desensitization.tenant_id IS '租户id';
COMMENT ON COLUMN api_data_desensitization.rule_content IS '截断规则';
COMMENT ON COLUMN api_data_desensitization.status IS '启用状态，0启用，1停用';
COMMENT ON COLUMN api_data_desensitization_log.api_id IS 'api id';
COMMENT ON COLUMN api_data_desensitization_log.client_id IS '客户端id，可用于查询到调用者';
COMMENT ON COLUMN api_data_desensitization_log.invoke_user IS '调用人';
COMMENT ON COLUMN api_data_desensitization_log.invoke_time IS '调用时间';
COMMENT ON COLUMN api_data_desensitization_log.invoke_status IS '调用状态，0成功，1失败';
COMMENT ON COLUMN api_data_desensitization_log.desensitization_status IS '脱敏状态，0成功，1失败';
COMMENT ON COLUMN api_data_desensitization_log.api_name IS 'api名称';
COMMENT ON COLUMN api_data_desensitization_log.api_status_name IS 'api状态';
COMMENT ON COLUMN api_data_desensitization_log.desensitization_table IS '脱敏表';
COMMENT ON COLUMN api_data_desensitization_log.desensitization_db IS '脱敏库';
COMMENT ON COLUMN api_data_desensitization_log_detail.log_id IS '脱敏记录id';
COMMENT ON COLUMN api_data_desensitization_log_detail.column_name IS '字段名';
COMMENT ON COLUMN api_data_desensitization_log_detail.desensitization_before IS '脱敏前样本';
COMMENT ON COLUMN api_data_desensitization_log_detail.desensitization_after IS '脱敏后样本';
COMMENT ON COLUMN api_data_desensitization_log_detail.desensitization_name IS '脱敏规则名称';
COMMENT ON COLUMN api_data_desensitization_log_detail.desensitization_status IS '脱敏状态，0成功，1失败';
COMMENT ON COLUMN api_data_desensitization_log_detail.desensitization_count IS '脱敏次数';
COMMENT ON COLUMN api_data_source.id IS '主键id';
COMMENT ON COLUMN api_data_source.data_source_name IS '数据源名称';
COMMENT ON COLUMN api_data_source.data_source_type IS '数据源类型';
COMMENT ON COLUMN api_data_source.data_source_version IS '数据源版本号';
COMMENT ON COLUMN api_data_source.data_source_desc IS '描述';
COMMENT ON COLUMN api_data_source.cluster_address IS '集群地址（多个用逗号分开）';
COMMENT ON COLUMN api_data_source.link_user IS '连接用户名';
COMMENT ON COLUMN api_data_source.link_password IS '连接密码';
COMMENT ON COLUMN api_data_source.link_status IS '连接状态，0：未连接，1：正常，2：失败';
COMMENT ON COLUMN api_data_source.create_user IS '创建人名称';
COMMENT ON COLUMN api_data_source.create_time IS '创建时间';
COMMENT ON COLUMN api_data_source.update_user IS '更新人名称';
COMMENT ON COLUMN api_data_source.update_time IS '更新时间';
COMMENT ON COLUMN api_data_source.tenant_id IS '租户id';
COMMENT ON COLUMN api_data_source.flag IS '状态，0：无效，1：有效';
COMMENT ON COLUMN api_data_source.jdbc_url IS 'jdbc地址';
COMMENT ON COLUMN api_data_source.cluster_name IS '集群名称';
COMMENT ON COLUMN api_data_source.database_name IS '数据库（mongo）';
COMMENT ON COLUMN api_data_watermark.name IS '规则名称';
COMMENT ON COLUMN api_data_watermark.content IS '水印内容，date日期，user用户名';
COMMENT ON COLUMN api_data_watermark.create_user IS '创建人名称';
COMMENT ON COLUMN api_data_watermark.create_time IS '创建时间';
COMMENT ON COLUMN api_data_watermark.update_user IS '更新人名称';
COMMENT ON COLUMN api_data_watermark.update_time IS '更新时间';
COMMENT ON COLUMN api_data_watermark.tenant_id IS '租户id';
COMMENT ON COLUMN api_data_watermark.watermark_form IS '水印形式，1 水印字段';
COMMENT ON COLUMN api_data_watermark.custom_content IS '自定义水印内容';
COMMENT ON COLUMN api_data_watermark.business_system IS '业务系统';
COMMENT ON COLUMN api_data_watermark.business_scenario IS '业务场景';
COMMENT ON COLUMN api_data_watermark.remark IS '备注信息';
COMMENT ON COLUMN api_data_watermark.status IS '启用状态，0启用，1停用';
COMMENT ON COLUMN api_data_watermark.system_leader IS '系统负责人';
COMMENT ON COLUMN api_data_watermark.department IS '所属部门';
COMMENT ON COLUMN api_data_watermark_log.api_id IS 'api id';
COMMENT ON COLUMN api_data_watermark_log.client_id IS '客户端id，可用于查询到调用者';
COMMENT ON COLUMN api_data_watermark_log.invoke_user IS '调用人';
COMMENT ON COLUMN api_data_watermark_log.invoke_time IS '调用时间';
COMMENT ON COLUMN api_data_watermark_log.invoke_status IS '调用状态，0成功，1失败';
COMMENT ON COLUMN api_data_watermark_log.watermark_status IS '水印状态，0成功，1失败';
COMMENT ON COLUMN api_data_watermark_log.api_name IS 'api名称';
COMMENT ON COLUMN api_data_watermark_log.api_status IS 'api状态';
COMMENT ON COLUMN api_data_watermark_task.task_name IS '任务名称';
COMMENT ON COLUMN api_data_watermark_task.file_name IS '溯源数据文件名称';
COMMENT ON COLUMN api_data_watermark_task.create_user IS '创建人名称';
COMMENT ON COLUMN api_data_watermark_task.create_time IS '创建时间';
COMMENT ON COLUMN api_data_watermark_task.update_user IS '更新人名称';
COMMENT ON COLUMN api_data_watermark_task.update_time IS '更新时间';
COMMENT ON COLUMN api_data_watermark_task.tenant_id IS '租户id';
COMMENT ON COLUMN api_data_watermark_task.start_time IS '开始时间';
COMMENT ON COLUMN api_data_watermark_task.end_time IS '结束时间';
COMMENT ON COLUMN api_data_watermark_task.duration IS '消耗时长,单位秒';
COMMENT ON COLUMN api_data_watermark_task.status IS '执行状态，0未开始，1进行中，2执行完成';
COMMENT ON COLUMN api_data_watermark_task.file_path IS '文件路径';
COMMENT ON COLUMN api_data_watermark_task_result.task_id IS '任务id';
COMMENT ON COLUMN api_data_watermark_task_result.content IS '水印内容';
COMMENT ON COLUMN api_data_watermark_task_result.percentages IS '命中率百分比';
COMMENT ON COLUMN api_domain.domain_id IS '租户标识';
COMMENT ON COLUMN api_domain.name IS '租户名称';
COMMENT ON COLUMN api_domain.address IS '租户地址';
COMMENT ON COLUMN api_domain.contact IS '联系人';
COMMENT ON COLUMN api_domain.phone_number IS '手机号码';
COMMENT ON COLUMN api_domain.domain_account IS '租户帐号';
COMMENT ON COLUMN api_domain.domain_code IS '租户编码';
COMMENT ON COLUMN api_domain.web_url IS '登录网址';
COMMENT ON COLUMN api_domain.notes IS '备注';
COMMENT ON COLUMN api_domain.create_by IS '创建人';
COMMENT ON COLUMN api_domain.create_date IS '创建日期';
COMMENT ON COLUMN api_domain.modify_by IS '修改人';
COMMENT ON COLUMN api_domain.modify_date IS '修改日期';
COMMENT ON COLUMN api_domain.is_deleted IS '是否删除';
COMMENT ON COLUMN api_error_code.id IS '主键id';
COMMENT ON COLUMN api_error_code.error_code IS '错误码';
COMMENT ON COLUMN api_error_code.error_msg IS '错误信息';
COMMENT ON COLUMN api_error_code.solution IS '解决方案';
COMMENT ON COLUMN api_error_code.api_info_id IS 'api主键';
COMMENT ON COLUMN api_info.id IS '主键id';
COMMENT ON COLUMN api_info.cn_name IS 'API中文名称';
COMMENT ON COLUMN api_info.api_desc IS '描述';
COMMENT ON COLUMN api_info.api_path IS 'api路径';
COMMENT ON COLUMN api_info.protocol IS '协议，HTTP/HTTPS';
COMMENT ON COLUMN api_info.req_type IS '请求类型，GET,POST,PUT';
COMMENT ON COLUMN api_info.response_type IS '返回类型，JSON';
COMMENT ON COLUMN api_info.query_timeout IS '查询超时时间，单位秒';
COMMENT ON COLUMN api_info.req_limit IS '单位时间内请求限制次数';
COMMENT ON COLUMN api_info.transfer_encrypt_type IS '传输加密类型，0：不加密 1：加密';
COMMENT ON COLUMN api_info.data_source_id IS '数据源id';
COMMENT ON COLUMN api_info.allow_paging IS '是否分页，0：否，1：是';
COMMENT ON COLUMN api_info.contain_page IS '返回内容是否包含分页信息，0：否，1：是';
COMMENT ON COLUMN api_info.contain_header IS '返回内容是否包含header信息，0：否，1：是';
COMMENT ON COLUMN api_info.api_type IS '0：生成api，1：注册api';
COMMENT ON COLUMN api_info.api_sql IS '执行的sql语句';
COMMENT ON COLUMN api_info.table_name IS '查询的表名';
COMMENT ON COLUMN api_info.name IS 'api名称';
COMMENT ON COLUMN api_info.ignore_syntax_check IS '是否忽略拼写检查，0：否，1：是';
COMMENT ON COLUMN api_info.submit_status IS '提交状态，0：未提交，1：已提交';
COMMENT ON COLUMN api_info.publish_status IS '发布状态，0未发布，1已发布';
COMMENT ON COLUMN api_info.api_create_type IS '生成api类型，0：简单模板，1：自定义';
COMMENT ON COLUMN api_info.api_token IS 'API-TOKEN值';
COMMENT ON COLUMN api_info.target_host IS '后端host，http(s)://host:port';
COMMENT ON COLUMN api_info.target_path IS '后端服务path，示例：/user/abc';
COMMENT ON COLUMN api_info.target_port IS '后端服务端口';
COMMENT ON COLUMN api_info.target_method IS '后端服务请求方式:POST,GET,PUT,DELETE';
COMMENT ON COLUMN api_info.create_user IS '创建人名称';
COMMENT ON COLUMN api_info.create_time IS '创建时间';
COMMENT ON COLUMN api_info.update_user IS '更新人名称';
COMMENT ON COLUMN api_info.update_time IS '更新时间';
COMMENT ON COLUMN api_info.tenant_id IS '租户id';
COMMENT ON COLUMN api_info.content_type IS '参数传输方式：0：json，1：form表单';
COMMENT ON COLUMN api_info.result_sample_flag IS '返回JSON样例是否保存 0不选中，1选中';
COMMENT ON COLUMN api_info.result_sample IS '返回JSON样例';
COMMENT ON COLUMN api_info.body_desc_json IS '请求Body描述';
COMMENT ON COLUMN api_info.contain_original_status IS '返回结果中携带第三方 API 状态码，0：不包含，1：包含';
COMMENT ON COLUMN api_info.normal_return_example IS '正常返回示例';
COMMENT ON COLUMN api_info.error_return_example IS '错误返回示例';
COMMENT ON COLUMN api_info.is_watermark IS '是否水印，0是1否';
COMMENT ON COLUMN api_info.is_desensitization IS '是否脱敏，0是1否';
COMMENT ON COLUMN api_info.watermark_id IS '水印规则id';
COMMENT ON COLUMN api_info.publish_time IS '发布时间';
COMMENT ON COLUMN api_info.publish_desc IS '发布说明';
COMMENT ON COLUMN api_info.publish_user IS '发布人';
COMMENT ON COLUMN api_info.approval_status IS '审批状态，0待审批 1已同意 2已拒绝 3已撤销';
COMMENT ON COLUMN api_info.approval_desc IS '审批说明';
COMMENT ON COLUMN api_info.approval_time IS '审批时间';
COMMENT ON COLUMN api_info.approval_user IS '审批人';
COMMENT ON COLUMN api_input_param.param_desc IS '描述';
COMMENT ON COLUMN api_input_param.field_name IS '字段名';
COMMENT ON COLUMN api_input_param.operator IS '操作符';
COMMENT ON COLUMN api_input_param.param_name IS '参数名';
COMMENT ON COLUMN api_input_param.param_type IS '参数类型';
COMMENT ON COLUMN api_input_param.required IS '是否必须，0：否，1：是';
COMMENT ON COLUMN api_input_param.row_permission IS '是否有行权限，0：否，1：是';
COMMENT ON COLUMN api_input_param.api_info_id IS 'api主键';
COMMENT ON COLUMN api_input_param.constant_param IS '是否常量参数，0：否，1：是';
COMMENT ON COLUMN api_input_param.default_value IS '默认值';
COMMENT ON COLUMN api_input_param.is_arrray IS '参数值是否是数组，0：否，1：是';
COMMENT ON COLUMN api_input_param.param_location IS '参数位置';
COMMENT ON COLUMN api_invoke_log.id IS '主键';
COMMENT ON COLUMN api_invoke_log.api_id IS 'api主键';
COMMENT ON COLUMN api_invoke_log.client_id IS '客户端主键';
COMMENT ON COLUMN api_invoke_log.invoke_time IS '调用时间';
COMMENT ON COLUMN api_invoke_log.create_by IS '创建人';
COMMENT ON COLUMN api_invoke_log.create_date IS '创建时间';
COMMENT ON COLUMN api_invoke_log.biz_type IS '日志类型';
COMMENT ON COLUMN api_invoke_log.content IS '日志内容';
COMMENT ON COLUMN api_invoke_log.invoke_flag IS '响应状态，true：成功，false：失败';
COMMENT ON COLUMN api_invoke_log.duration IS '响应持续时间';
COMMENT ON COLUMN api_invoke_log.request_flag IS '请求状态';
COMMENT ON COLUMN api_label.label_name IS '标签值';
COMMENT ON COLUMN api_label.create_user IS '创建人名称';
COMMENT ON COLUMN api_label.create_time IS '创建时间';
COMMENT ON COLUMN api_label.update_user IS '更新人名称';
COMMENT ON COLUMN api_label.update_time IS '更新时间';
COMMENT ON COLUMN api_label.tenant_id IS '租户id';
COMMENT ON COLUMN api_label_info.api_label_id IS 'apiLabel主键';
COMMENT ON COLUMN api_label_info.api_info_id IS 'api主键';
COMMENT ON COLUMN api_log.id IS '主键id';
COMMENT ON COLUMN api_log.api_info_id IS 'api主键';
COMMENT ON COLUMN api_log.start_time IS '开始时间';
COMMENT ON COLUMN api_log.end_time IS '结束时间';
COMMENT ON COLUMN api_log.content IS '返回内容';
COMMENT ON COLUMN api_log.is_success IS '是否成功，0：否，1：是';
COMMENT ON COLUMN api_log.create_user IS '创建人名称';
COMMENT ON COLUMN api_log.create_time IS '创建时间';
COMMENT ON COLUMN api_output_param.param_desc IS '参数描述';
COMMENT ON COLUMN api_output_param.field_name IS '字段名';
COMMENT ON COLUMN api_output_param.param_name IS '参数名';
COMMENT ON COLUMN api_output_param.param_type IS '参数类型';
COMMENT ON COLUMN api_output_param.api_info_id IS 'api主键';
COMMENT ON COLUMN api_output_param.desensitization_id IS '脱敏规则id';
COMMENT ON COLUMN api_project.id IS '项目id';
COMMENT ON COLUMN api_project.project_mark IS '项目标识';
COMMENT ON COLUMN api_project.display_name IS '用户名称';
COMMENT ON COLUMN api_project.notes IS '项目描述';
COMMENT ON COLUMN api_project.create_by IS '创建人';
COMMENT ON COLUMN api_project.create_date IS '创建日期';
COMMENT ON COLUMN api_project.modify_by IS '修改人';
COMMENT ON COLUMN api_project.modify_date IS '修改日期';
COMMENT ON COLUMN api_project.is_deleted IS '标记';
COMMENT ON COLUMN api_security_group.id IS '主键';
COMMENT ON COLUMN api_security_group.name IS '安全组名称';
COMMENT ON COLUMN api_security_group.ip IS 'ip列表';
COMMENT ON COLUMN api_security_group.simple_ip_text IS 'ip列表';
COMMENT ON COLUMN api_security_group.type IS '安全组类型 0：白名单，1：黑名单';
COMMENT ON COLUMN api_security_group.tenant_id IS '租户id';
COMMENT ON COLUMN api_security_group.create_by IS '创建人';
COMMENT ON COLUMN api_security_group.create_date IS '新增时间';
COMMENT ON COLUMN api_security_group.modify_by IS '修改人';
COMMENT ON COLUMN api_security_group.modify_date IS '修改时间';
COMMENT ON COLUMN api_security_group.is_deleted IS '0正常 1逻辑删除';
COMMENT ON COLUMN api_security_group.project_id IS '项目id';
COMMENT ON COLUMN api_security_group_ref.id IS '主键';
COMMENT ON COLUMN api_security_group_ref.group_id IS '组唯一编号';
COMMENT ON COLUMN api_security_group_ref.api_id IS 'api标识';
COMMENT ON COLUMN api_security_group_ref.tenant_id IS '租户id';
COMMENT ON COLUMN api_security_group_ref.create_date IS '新增时间';
COMMENT ON COLUMN api_security_group_ref.modify_date IS '修改时间';
COMMENT ON COLUMN api_user.user_id IS '用户id';
COMMENT ON COLUMN api_user.user_name IS '用户登录名';
COMMENT ON COLUMN api_user.display_name IS '用户名称';
COMMENT ON COLUMN api_user.password IS '密码';
COMMENT ON COLUMN api_user.phone_number IS '用户手机';
COMMENT ON COLUMN api_user.email IS '用户邮箱';
COMMENT ON COLUMN api_user.last_login_ip IS '最后一次登录ip';
COMMENT ON COLUMN api_user.notes IS '备注';
COMMENT ON COLUMN api_user.create_by IS '创建人';
COMMENT ON COLUMN api_user.create_date IS '创建日期';
COMMENT ON COLUMN api_user.modify_by IS '修改人';
COMMENT ON COLUMN api_user.modify_date IS '修改日期';
COMMENT ON COLUMN api_user.is_deleted IS '标记';
COMMENT ON COLUMN api_user.sex IS '性别，1男，0女';
COMMENT ON COLUMN backup_config.id IS 'id';
COMMENT ON COLUMN backup_config.type IS '备份数据库:ch,mysql';
COMMENT ON COLUMN backup_config.table_name IS '对象名称';
COMMENT ON COLUMN backup_config.backup_type IS '备份类型：备份/归档';
COMMENT ON COLUMN backup_config.backup_policy IS '策略：all-全量/increment-增量';
COMMENT ON COLUMN backup_config.increment_field IS '增量备份是依赖字段';
COMMENT ON COLUMN backup_config.storage IS '文件存储位置';
COMMENT ON COLUMN backup_config.storage_volume IS '存储容量';
COMMENT ON COLUMN backup_config.volume_unit IS '容量单位';
COMMENT ON COLUMN backup_config.storage_day IS '存储天数';
COMMENT ON COLUMN backup_config.execute_period IS '执行周期：cycle-周期/single-单次';
COMMENT ON COLUMN backup_config.execute_plan IS '执行计划：每隔多少天执行';
COMMENT ON COLUMN backup_config.execute_time IS '执行时间HH:mm:ss';
COMMENT ON COLUMN backup_config.create_user IS '创建者';
COMMENT ON COLUMN backup_config.create_date IS '创建日期';
COMMENT ON COLUMN backup_config.update_user IS '更新者';
COMMENT ON COLUMN backup_config.update_date IS '更新日期';
COMMENT ON COLUMN backup_config.flag IS '有效标识';
COMMENT ON COLUMN backup_config.time_type IS '归档/删除基准线';
COMMENT ON COLUMN backup_config.time_config IS '定时时间配置';
COMMENT ON COLUMN backup_config.is_recover IS '是否定时恢复，1-是；0-否';
COMMENT ON COLUMN backup_config.task_name IS '任务名';
COMMENT ON COLUMN backup_config.backup_method IS '备份方式:1-节点备份；2-业务备份';
COMMENT ON COLUMN backup_config.recover_config IS '恢复配置json';
COMMENT ON COLUMN backup_config.last_finish_time IS '最后一次执行时间';
COMMENT ON COLUMN backup_config.backup_result IS '最后一次执行结果';
COMMENT ON COLUMN backup_job.data_source_type IS '数据源类型:mysql,hive,elasticsearch';
COMMENT ON COLUMN backup_job.backup_target IS '备份对象：表，索引';
COMMENT ON COLUMN backup_job.backup_type IS '备份类型：0全量，1增量';
COMMENT ON COLUMN backup_job.store_type IS '存储类型：0本地,1HDFS';
COMMENT ON COLUMN backup_job.increament_filed IS '增量字段';
COMMENT ON COLUMN backup_job.run_type IS '执行方式：0单次执行，1周期执行';
COMMENT ON COLUMN backup_job.data_value IS '执行方式类型值';
COMMENT ON COLUMN backup_job.cron IS '执行表达式';
COMMENT ON COLUMN backup_job.latest_state IS '最近状态：0失败，1成功';
COMMENT ON COLUMN backup_job.latest_time IS '最近完成时间';
COMMENT ON COLUMN backup_job.job_state IS '运行状态：0新建，1运行，2暂停';
COMMENT ON COLUMN backup_job.flag IS '有效标识：0无效，1有效';
COMMENT ON COLUMN backup_job.is_run IS '是否可以被调度：0否，1是';
COMMENT ON COLUMN backup_job_recover.backup_total IS '数据条数';
COMMENT ON COLUMN backup_job_recover.recover_total IS '文件大小';
COMMENT ON COLUMN backup_job_recover.recover_check IS '恢复数据校验：0不一致，1一致';
COMMENT ON COLUMN backup_job_recover.state IS '恢复状态：0恢复中，1恢复成功，2恢复失败';
COMMENT ON COLUMN backup_job_recover.result IS '恢复结果';
COMMENT ON COLUMN backup_job_recover.file_path IS '备份文件路径';
COMMENT ON COLUMN backup_job_recover.recover_target_name IS '恢复目标名称';
COMMENT ON COLUMN backup_job_task.data_num IS '数据条数';
COMMENT ON COLUMN backup_job_task.file_sie IS '文件大小';
COMMENT ON COLUMN backup_job_task.state IS '备份状态：0备份中，1备份成功，2备份失败';
COMMENT ON COLUMN backup_job_task.result IS '备份结果';
COMMENT ON COLUMN backup_job_task.file_path IS '备份文件路径';
COMMENT ON COLUMN backup_job_task.sign_code IS '文件签名';
COMMENT ON COLUMN data_dev_fusion_model.datasource IS '数据源：mysql , clickhouse';
COMMENT ON COLUMN data_dev_fusion_model.table_name IS '表名';
COMMENT ON COLUMN data_dev_fusion_model.bus_desc IS '业务描述';
COMMENT ON COLUMN data_dev_fusion_model.data_update_cycle IS '数据更新周期: 0每小时更新，1每日更新，2每周更新，3每月更新，4每季更新，5每年更新';
COMMENT ON COLUMN data_dev_fusion_model.data_update_cron IS '数据更新周期cron表达式';
COMMENT ON COLUMN data_dev_fusion_model.data_save_type IS '数据存储类型: 0小时，1日，2周，3月，4季度，5年';
COMMENT ON COLUMN data_dev_fusion_model.data_save_num IS '数据存储类型时长';
COMMENT ON COLUMN data_dev_fusion_model.expiration_policy IS '到期策略： 0到期删除';
COMMENT ON COLUMN data_dev_fusion_model.status IS '运行状态：0新建，1运行，2暂停';
COMMENT ON COLUMN data_dev_fusion_model.is_first_sync IS '是否已首次同步:0否，1是';
COMMENT ON COLUMN data_dev_fusion_model.ch_cluster_name IS 'CH 集群名称';
COMMENT ON COLUMN data_dev_fusion_model.ch_shard_name IS 'CH shard变量名称';
COMMENT ON COLUMN data_dev_fusion_task.sync_status IS '更新状态:0失败，1成功';
COMMENT ON COLUMN data_dev_fusion_task.current_data_num IS '当前数据量';
COMMENT ON COLUMN data_dev_fusion_task.add_data_num IS '新增数据量';
COMMENT ON COLUMN data_dev_fusion_task.err_log IS '失败日志';
COMMENT ON COLUMN data_dev_model_field.fusion_model_id IS '模型ID';
COMMENT ON COLUMN data_dev_model_field.field_name IS '字段名';
COMMENT ON COLUMN data_dev_model_field.field_name_cn IS '字段注释';
COMMENT ON COLUMN data_dev_model_field.field_type IS '字段类型';
COMMENT ON COLUMN data_dev_model_field.field_length IS '类型长度';
COMMENT ON COLUMN data_dev_model_field.constraint_type IS '约束类型：不约束:none,非空约束:not_null,主键约束:primary,唯一约束:unique';
COMMENT ON COLUMN data_dev_source_field.fusion_model_id IS '模型ID';
COMMENT ON COLUMN data_dev_source_field.model_field_id IS '模型字段ID';
COMMENT ON COLUMN data_dev_source_field.model_field_name IS '字段名称';
COMMENT ON COLUMN data_dev_source_field.ds_datasource_id IS 'ds 数据源ID';
COMMENT ON COLUMN data_dev_source_field.source_table_name IS '来源表名';
COMMENT ON COLUMN data_dev_source_field.source_field_name IS '来源字段名';
COMMENT ON COLUMN data_dev_source_field.source_field_name_cn IS '字段备注';
COMMENT ON COLUMN data_dev_source_field.source_field_type IS '来源字段类型';
COMMENT ON COLUMN data_dev_source_field.source_field_length IS '来源字段类型长度';
COMMENT ON COLUMN data_dev_source_field.source_sync_field IS '同步周期字段名称，其在来源表的类型必须是时间类型';
COMMENT ON COLUMN data_dictionary_base.dict_name IS '字典名称';
COMMENT ON COLUMN data_dictionary_base.dict_desc IS '字典描述';
COMMENT ON COLUMN data_dictionary_base.create_time IS '创建时间';
COMMENT ON COLUMN data_dictionary_base.create_user IS '创建人';
COMMENT ON COLUMN data_dictionary_base.update_time IS '更新时间';
COMMENT ON COLUMN data_dictionary_base.update_user IS '更新人';
COMMENT ON COLUMN data_dictionary_base.tenant_id IS '租户ID';
COMMENT ON COLUMN data_dictionary_base.built_in IS '是否内置，0是 1否 1';
COMMENT ON COLUMN data_dictionary_category.dict_id IS '字典ID';
COMMENT ON COLUMN data_dictionary_category.category_name IS '分类名称';
COMMENT ON COLUMN data_dictionary_category.category_desc IS '分类描述';
COMMENT ON COLUMN data_dictionary_category.parent_id IS '父级分类';
COMMENT ON COLUMN data_dictionary_category.create_time IS '创建时间';
COMMENT ON COLUMN data_dictionary_category.create_user IS '创建人';
COMMENT ON COLUMN data_dictionary_category.update_time IS '更新时间';
COMMENT ON COLUMN data_dictionary_category.update_user IS '更新人';
COMMENT ON COLUMN data_dictionary_category.tenant_id IS '租户ID';
COMMENT ON COLUMN data_dictionary_item.dict_id IS '字典ID';
COMMENT ON COLUMN data_dictionary_item.category_id IS '字典分类ID';
COMMENT ON COLUMN data_dictionary_item.item_id IS '字典项ID';
COMMENT ON COLUMN data_dictionary_item.item_name IS '字典项名称';
COMMENT ON COLUMN data_dictionary_item.item_desc IS '字典项描述';
COMMENT ON COLUMN data_dictionary_item.item_type IS '字典项类型 table-表  column-字段';
COMMENT ON COLUMN data_dictionary_item.item_constraint IS '字典项约束';
COMMENT ON COLUMN data_dictionary_item.datasource_type IS '数据源类型';
COMMENT ON COLUMN data_dictionary_item.database_id IS '数据库id';
COMMENT ON COLUMN data_dictionary_item.database_name IS '数据库名称';
COMMENT ON COLUMN data_dictionary_item.create_time IS '创建时间';
COMMENT ON COLUMN data_dictionary_item.create_user IS '创建人';
COMMENT ON COLUMN data_dictionary_item.update_time IS '更新时间';
COMMENT ON COLUMN data_dictionary_item.update_user IS '更新人';
COMMENT ON COLUMN data_dictionary_item.tenant_id IS '租户ID';
COMMENT ON COLUMN data_dictionary_item.column_name_cn IS '字典项中文名';
COMMENT ON COLUMN data_dictionary_item.table_name IS '表名称';
COMMENT ON COLUMN data_dictionary_item.field_type IS '字段类型';
COMMENT ON COLUMN data_dictionary_item.data_mart_asset_id IS '发布到集市的资产id';
COMMENT ON COLUMN data_dictionary_item.owner IS '负责人';
COMMENT ON COLUMN data_dictionary_item.sen_level_id IS '敏感分级ID';
COMMENT ON COLUMN data_dictionary_item.sen_level_name IS '敏感分级名称';
COMMENT ON COLUMN data_dictionary_item.sen_type_id IS '敏感分类ID';
COMMENT ON COLUMN data_dictionary_item.sen_type_name IS '敏感分级名称';
COMMENT ON COLUMN data_dictionary_item.is_sensitive IS '是否敏感，默认不是，0-否，1-是';
COMMENT ON COLUMN data_dictionary_item.desensitization_id IS '脱敏规则 ID';
COMMENT ON COLUMN data_dictionary_item.is_required IS '是否必填，0-否，1-是';
COMMENT ON COLUMN data_dictionary_item.is_encrypted IS '是否加密，0-否，1-是';
COMMENT ON COLUMN data_dictionary_item_table_trend.count_day IS '统计日期';
COMMENT ON COLUMN data_dictionary_item_table_trend.table_line IS '数据条数';
COMMENT ON COLUMN data_dictionary_item_table_trend.table_space IS '占用空间 MB';
COMMENT ON COLUMN data_dictionary_item_table_trend.create_time IS '创建时间';
COMMENT ON COLUMN data_encryption_algorithm.key_name IS '密钥名称';
COMMENT ON COLUMN data_encryption_algorithm.key_algorithm IS '加密算法:SM2,SM3,SM4';
COMMENT ON COLUMN data_encryption_algorithm.level_id IS '分级ID';
COMMENT ON COLUMN data_encryption_algorithm.type_id IS '分类ID';
COMMENT ON COLUMN data_encryption_algorithm.sm2_public_key IS 'sm2算法公钥';
COMMENT ON COLUMN data_encryption_algorithm.sm2_private_key IS 'sm2算法私钥';
COMMENT ON COLUMN data_encryption_algorithm.sm4_key_base64 IS 'sm4算法key';
COMMENT ON COLUMN data_encryption_algorithm.sm4_iv_base64 IS 'sm4算法偏移量';
COMMENT ON COLUMN data_encryption_algorithm.is_enable IS '是否启用，0停止，1启用';
COMMENT ON COLUMN data_encryption_algorithm_back_up.encryption_algorithm_id IS '密钥ID';
COMMENT ON COLUMN data_encryption_algorithm_back_up.key_name IS '密钥名称';
COMMENT ON COLUMN data_encryption_algorithm_back_up.key_algorithm IS '加密算法:SM2,SM3,SM4';
COMMENT ON COLUMN data_encryption_algorithm_back_up.level_id IS '分级ID';
COMMENT ON COLUMN data_encryption_algorithm_back_up.level_name IS '分级';
COMMENT ON COLUMN data_encryption_algorithm_back_up.type_id IS '分类ID';
COMMENT ON COLUMN data_encryption_algorithm_back_up.type_name IS '分级';
COMMENT ON COLUMN data_encryption_algorithm_back_up.sm2_public_key IS 'sm2算法公钥';
COMMENT ON COLUMN data_encryption_algorithm_back_up.sm2_private_key IS 'sm2算法私钥';
COMMENT ON COLUMN data_encryption_algorithm_back_up.sm4_key_base64 IS 'sm4算法key';
COMMENT ON COLUMN data_encryption_algorithm_back_up.sm4_iv_base64 IS 'sm4算法偏移量';
COMMENT ON COLUMN data_encryption_algorithm_job.job_name IS '任务名称';
COMMENT ON COLUMN data_encryption_algorithm_job.datasource_type IS '数据源类型：Clickhouse,Elasticsearch,Hive,Mysql';
COMMENT ON COLUMN data_encryption_algorithm_job.encryption_object IS '加密对象';
COMMENT ON COLUMN data_encryption_algorithm_job.encryption_type IS '加密类型';
COMMENT ON COLUMN data_encryption_algorithm_job.target_object IS '目标对象';
COMMENT ON COLUMN data_encryption_algorithm_job.state IS '加密状态：1，执行中，2执行完成';
COMMENT ON COLUMN data_encryption_algorithm_job.finish_time IS '加密完成时间';
COMMENT ON COLUMN data_encryption_algorithm_job.result IS '加密结果';
COMMENT ON COLUMN data_encryption_algorithm_job_field.job_id IS '任务ID';
COMMENT ON COLUMN data_encryption_algorithm_job_field.field_name IS '字段名称，多个字段逗号分割';
COMMENT ON COLUMN data_encryption_algorithm_job_field.encryption_algorithm_id IS '算法ID';
COMMENT ON COLUMN data_encryption_algorithm_job_record.job_id IS '任务ID';
COMMENT ON COLUMN data_encryption_algorithm_job_record.field_name IS '加密字段';
COMMENT ON COLUMN data_encryption_algorithm_job_record.key_name IS '加密密钥';
COMMENT ON COLUMN data_encryption_algorithm_job_record.before_text IS '加密前样本';
COMMENT ON COLUMN data_encryption_algorithm_job_record.after_text IS '加密后样本';
COMMENT ON COLUMN data_encryption_algorithm_job_record.state IS '加密状态';
COMMENT ON COLUMN data_encryption_algorithm_job_record.should_count IS '应加密字段数';
COMMENT ON COLUMN data_encryption_algorithm_job_record.actual_count IS '实际加密字段数';
COMMENT ON COLUMN data_encryption_algorithm_job_record.success_rate IS '加密成功率';
COMMENT ON COLUMN data_mart_asset.type_id IS '分类ID';
COMMENT ON COLUMN data_mart_asset.group_id IS '分组ID';
COMMENT ON COLUMN data_mart_asset.element_id IS '元数据ID';
COMMENT ON COLUMN data_mart_asset.asset_name IS '(CH,Mysql,Hive)库名/表名/字段名，(ES)索引名/字段名';
COMMENT ON COLUMN data_mart_asset.asset_path IS '库表字段索引对应的全路劲';
COMMENT ON COLUMN data_mart_asset.asset_type IS '资产类型:clickhouse_db,clickhouse_table,clickhouse_field,hive_db,hive_table,hive_field,mysql_db,mysql_table,mysql_field,elasticsearch_index,elasticsearch_field';
COMMENT ON COLUMN data_mart_asset.asset_type_code IS '类型编码.DB,TABLE,FIELD,INDEX,INDEX_FIELD';
COMMENT ON COLUMN data_mart_asset.subscribe_num IS '订阅数';
COMMENT ON COLUMN data_mart_asset.thumbs_up_num IS '点赞数';
COMMENT ON COLUMN data_mart_asset.release_status IS '发布状态:0未发布，1已发布';
COMMENT ON COLUMN data_mart_asset.data_name IS '数据名';
COMMENT ON COLUMN data_mart_asset.asset_desc IS '描述';
COMMENT ON COLUMN data_mart_asset.start_time IS '开始时间';
COMMENT ON COLUMN data_mart_asset.end_time IS '结束时间';
COMMENT ON COLUMN data_mart_asset.asset_json IS '资产集合';
COMMENT ON COLUMN data_mart_asset.update_time IS '更新时间';
COMMENT ON COLUMN data_mart_asset.publish_status IS '发布状态，0未发布，1已发布';
COMMENT ON COLUMN data_mart_asset.publish_time IS '发布时间';
COMMENT ON COLUMN data_mart_asset.approval_status IS '审批状态，0待审批 1已发布 2已拒绝 3已撤销';
COMMENT ON COLUMN data_mart_asset.approval_desc IS '审批说明';
COMMENT ON COLUMN data_mart_asset.approval_time IS '审批时间';
COMMENT ON COLUMN data_mart_asset.data_mart_asset IS '审批人';
COMMENT ON COLUMN data_mart_asset.browse_count IS '浏览数';
COMMENT ON COLUMN data_mart_asset.approval_user IS '审批人';
COMMENT ON COLUMN data_mart_asset_publish.asset_id IS '资源ID';
COMMENT ON COLUMN data_mart_asset_publish.label IS '名称';
COMMENT ON COLUMN data_mart_asset_publish.url IS '上传图片地址';
COMMENT ON COLUMN data_mart_asset_subscribe.tag_id IS '标签';
COMMENT ON COLUMN data_mart_discuss.discuss IS '评论';
COMMENT ON COLUMN data_mart_group.group_name IS '分组名称';
COMMENT ON COLUMN data_mart_group.data_mart_type_id IS '数据集市分类ID';
COMMENT ON COLUMN data_mart_group.group_desc IS '描述';
COMMENT ON COLUMN data_mart_hotword.hotword IS '热词';
COMMENT ON COLUMN data_mart_hotword.create_time IS '入库时间';
COMMENT ON COLUMN data_mart_resource_pool.name IS '资源池名称';
COMMENT ON COLUMN data_mart_resource_pool.parent_id IS '上级资源池id';
COMMENT ON COLUMN data_mart_score.available_score IS '可用性分';
COMMENT ON COLUMN data_mart_score.visible_score IS '可见性分';
COMMENT ON COLUMN data_mart_score.credible_score IS '可信性分';
COMMENT ON COLUMN data_mart_subscribe.asset_id IS '数据集市资产id';
COMMENT ON COLUMN data_mart_subscribe.tenant_id IS '租户id';
COMMENT ON COLUMN data_mart_subscribe.user_id IS '订阅用户id';
COMMENT ON COLUMN data_mart_subscribe.subscription_channel IS '订阅方式，1 数据API，2 Kafka，3 Elasticsearch，4 ClickHouse';
COMMENT ON COLUMN data_mart_subscribe.subscription_time IS '订阅时间';
COMMENT ON COLUMN data_mart_subscribe.subscription_user IS '订阅人';
COMMENT ON COLUMN data_mart_subscribe.subscription_remark IS '申请说明';
COMMENT ON COLUMN data_mart_subscribe.approval_progress IS '审批进度,1 审批中，2 同意 3 拒绝';
COMMENT ON COLUMN data_mart_subscribe.approval_time IS '审批时间';
COMMENT ON COLUMN data_mart_subscribe.approval_remark IS '审批说明';
COMMENT ON COLUMN data_mart_subscribe.approval_user IS '审批人';
COMMENT ON COLUMN data_mart_subscribe.configuration_progress IS '配置进度，1 配置中 2 配置成功 3 配置失败';
COMMENT ON COLUMN data_mart_subscribe.configuration_user IS '配置人';
COMMENT ON COLUMN data_mart_subscribe.configuration_time IS '配置时间';
COMMENT ON COLUMN data_mart_subscribe.configuration_remark IS '配置说明';
COMMENT ON COLUMN data_mart_subscribe.call_num IS '调用次数';
COMMENT ON COLUMN data_mart_subscribe.call_num_limit IS '是否限制调用次数，0限制，1不限制';
COMMENT ON COLUMN data_mart_subscribe.begin_time IS '使用开始时间';
COMMENT ON COLUMN data_mart_subscribe.end_time IS '使用结束时间';
COMMENT ON COLUMN data_mart_subscribe.time_limit IS '是否限制使用时间，0限制，1不限制';
COMMENT ON COLUMN data_mart_subscribe.kafka_topic IS 'Kafka topic';
COMMENT ON COLUMN data_mart_subscribe.kafka_address IS 'Kafka集群地址';
COMMENT ON COLUMN data_mart_subscribe.kafka_port IS 'kafka端口';
COMMENT ON COLUMN data_mart_subscribe.elasticsearch_index IS 'elasticsearch索引';
COMMENT ON COLUMN data_mart_subscribe.clickhouse_table IS 'ClickHouse表名';
COMMENT ON COLUMN data_mart_subscribe.api_id IS '数据API订阅通过后的api主键id';
COMMENT ON COLUMN data_mart_subscribe.data_usage IS '数据用途';
COMMENT ON COLUMN data_mart_subscribe.network_domain IS '网络域';
COMMENT ON COLUMN data_mart_subscribe.business_ip IS '服务器业务IP';
COMMENT ON COLUMN data_mart_subscribe.hosting_network_ip IS '服务器承载网IP';
COMMENT ON COLUMN data_mart_subscribe.vpc_name IS 'VPC名称';
COMMENT ON COLUMN data_mart_subscribe.contact_phone IS '业务联系人电话';
COMMENT ON COLUMN data_mart_subscribe.contact_email IS '联系人邮箱';
COMMENT ON COLUMN data_mart_subscribe.resource_pool_id IS '所属资源池id';
COMMENT ON COLUMN data_mart_subscribe.subscribe_rule_id IS '订阅id，按规则生成';
COMMENT ON COLUMN data_mart_subscribe_notice.subscribe_id IS '订阅id';
COMMENT ON COLUMN data_mart_subscribe_notice.content IS '消息内容';
COMMENT ON COLUMN data_mart_subscribe_notice.create_time IS '消息入库时间';
COMMENT ON COLUMN data_mart_table_lines.count_day IS '统计日期';
COMMENT ON COLUMN data_mart_table_lines.table_line IS '每个表条数{"table_1":100,"table_2":100}';
COMMENT ON COLUMN data_mart_table_lines.table_line_all IS '总条数';
COMMENT ON COLUMN data_mart_table_lines.table_space_all IS '总占用空间 MB';
COMMENT ON COLUMN data_mart_tag.tag_name IS '标签名称';
COMMENT ON COLUMN data_mart_tag.tag_desc IS '标签备注';
COMMENT ON COLUMN data_mart_tag.create_time IS '创建时间';
COMMENT ON COLUMN data_mart_tag.create_user IS '创建人';
COMMENT ON COLUMN data_mart_tag.update_time IS '更新时间';
COMMENT ON COLUMN data_mart_tag.update_user IS '更新人';
COMMENT ON COLUMN data_mart_tag.tenant_id IS '租户ID';
COMMENT ON COLUMN data_mart_type.type_name IS '分类名称';
COMMENT ON COLUMN data_mart_type.type_desc IS '分类描述';
COMMENT ON COLUMN data_meta_relation.source_element_id IS '起始元数据ID';
COMMENT ON COLUMN data_meta_relation.source_item_type IS '起始类型 table-表  field-字段';
COMMENT ON COLUMN data_meta_relation.source_datasource_type IS '起始数据源类型';
COMMENT ON COLUMN data_meta_relation.source_database_name IS '起始数据库名称';
COMMENT ON COLUMN data_meta_relation.source_table_name IS '起始表名称';
COMMENT ON COLUMN data_meta_relation.source_field_name IS '起始字段名称';
COMMENT ON COLUMN data_meta_relation.source_unique_id IS '起始元数据唯一字段';
COMMENT ON COLUMN data_meta_relation.target_element_id IS '目标元数据ID';
COMMENT ON COLUMN data_meta_relation.target_item_type IS '目标类型 table-表  field-字段';
COMMENT ON COLUMN data_meta_relation.target_datasource_type IS '目标数据源类型';
COMMENT ON COLUMN data_meta_relation.target_database_name IS '目标数据库名称';
COMMENT ON COLUMN data_meta_relation.target_table_name IS '目标表名称';
COMMENT ON COLUMN data_meta_relation.target_field_name IS '目标字段名称';
COMMENT ON COLUMN data_meta_relation.target_unique_id IS '目标元数据唯一字段';
COMMENT ON COLUMN data_meta_relation.create_time IS '创建时间';
COMMENT ON COLUMN data_meta_relation.create_user IS '创建人';
COMMENT ON COLUMN data_meta_relation.update_time IS '更新时间';
COMMENT ON COLUMN data_meta_relation.update_user IS '更新人';
COMMENT ON COLUMN data_meta_relation.tenant_id IS '租户ID';
COMMENT ON COLUMN data_meta_tag.id IS '主键';
COMMENT ON COLUMN data_meta_tag.name IS '标签名称';
COMMENT ON COLUMN data_meta_tag.abbreviation IS '英文缩写';
COMMENT ON COLUMN data_meta_tag.description IS '描述';
COMMENT ON COLUMN data_meta_tag.tag_type_id IS '标签类型ID';
COMMENT ON COLUMN data_meta_tag.create_time IS '创建时间';
COMMENT ON COLUMN data_meta_tag.create_user IS '创建人';
COMMENT ON COLUMN data_meta_tag.update_time IS '更新时间';
COMMENT ON COLUMN data_meta_tag.update_user IS '更新人';
COMMENT ON COLUMN data_meta_tag.tenant_id IS '租户ID';
COMMENT ON COLUMN data_meta_tag_master.tag_id IS '标签值id';
COMMENT ON COLUMN data_meta_tag_master.element_id IS '元数据ID';
COMMENT ON COLUMN data_meta_tag_master.item_type IS '类型 table-表  field-字段';
COMMENT ON COLUMN data_meta_tag_master.datasource_type IS '数据源类型';
COMMENT ON COLUMN data_meta_tag_master.database_name IS '数据库名称';
COMMENT ON COLUMN data_meta_tag_master.table_name IS '表名称';
COMMENT ON COLUMN data_meta_tag_master.field_name IS '字段名称';
COMMENT ON COLUMN data_meta_tag_master.create_time IS '创建时间';
COMMENT ON COLUMN data_meta_tag_master.create_user IS '创建人';
COMMENT ON COLUMN data_meta_tag_master.update_time IS '更新时间';
COMMENT ON COLUMN data_meta_tag_master.update_user IS '更新人';
COMMENT ON COLUMN data_meta_tag_master.tenant_id IS '租户ID';
COMMENT ON COLUMN data_meta_tag_type.id IS '主键';
COMMENT ON COLUMN data_meta_tag_type.name IS '标签类型';
COMMENT ON COLUMN data_meta_tag_type.abbreviation IS '英文缩写';
COMMENT ON COLUMN data_meta_tag_type.is_quick_tag IS '是否为快捷标签（0表示否，1表示是）';
COMMENT ON COLUMN data_meta_tag_type.description IS '描述';
COMMENT ON COLUMN data_meta_tag_type.create_time IS '创建时间';
COMMENT ON COLUMN data_meta_tag_type.create_user IS '创建人';
COMMENT ON COLUMN data_meta_tag_type.update_time IS '更新时间';
COMMENT ON COLUMN data_meta_tag_type.update_user IS '更新人';
COMMENT ON COLUMN data_meta_tag_type.tenant_id IS '租户ID';
COMMENT ON COLUMN data_model_business_process.business_sector_id IS '所属业务板块';
COMMENT ON COLUMN data_model_business_process.data_domain_id IS '所属数据域';
COMMENT ON COLUMN data_model_business_process.process_name_cn IS '中文名称';
COMMENT ON COLUMN data_model_business_process.process_name IS '英文名称';
COMMENT ON COLUMN data_model_business_process.process_desc IS '描述';
COMMENT ON COLUMN data_model_business_process.process_status IS '状态（0 表示删除，1表示正常）';
COMMENT ON COLUMN data_model_business_process.create_user IS '创建人';
COMMENT ON COLUMN data_model_business_process.create_time IS '创建时间';
COMMENT ON COLUMN data_model_business_process.update_user IS '修改人';
COMMENT ON COLUMN data_model_business_process.update_time IS '修改时间';
COMMENT ON COLUMN data_model_business_process.tenant_id IS '租户ID';
COMMENT ON COLUMN data_model_business_sector.sector_name_cn IS '中文名称';
COMMENT ON COLUMN data_model_business_sector.sector_name IS '英文名称';
COMMENT ON COLUMN data_model_business_sector.sector_name_abbr IS '英文缩写';
COMMENT ON COLUMN data_model_business_sector.sector_desc IS '备注';
COMMENT ON COLUMN data_model_business_sector.sector_status IS '状态（0 表示删除，1表示正常）';
COMMENT ON COLUMN data_model_business_sector.create_user IS '创建人';
COMMENT ON COLUMN data_model_business_sector.create_time IS '创建时间';
COMMENT ON COLUMN data_model_business_sector.update_user IS '修改人';
COMMENT ON COLUMN data_model_business_sector.update_time IS '修改时间';
COMMENT ON COLUMN data_model_business_sector.tenant_id IS '租户ID';
COMMENT ON COLUMN data_model_create_table_record.table_ids IS '表ID，逗号分割';
COMMENT ON COLUMN data_model_create_table_record.process_id IS '层级ID';
COMMENT ON COLUMN data_model_create_table_record.create_sql IS '建表语句';
COMMENT ON COLUMN data_model_create_table_record.create_user IS '创建人';
COMMENT ON COLUMN data_model_create_table_record.create_time IS '创建时间';
COMMENT ON COLUMN data_model_create_table_record.update_user IS '修改人';
COMMENT ON COLUMN data_model_create_table_record.update_time IS '修改时间';
COMMENT ON COLUMN data_model_create_table_record.tenant_id IS '租户ID';
COMMENT ON COLUMN data_model_data_domain.business_sector_id IS '所属业务板块';
COMMENT ON COLUMN data_model_data_domain.domain_name_cn IS '中文名称';
COMMENT ON COLUMN data_model_data_domain.domain_name IS '英文名称';
COMMENT ON COLUMN data_model_data_domain.domain_name_abbr IS '英文缩写';
COMMENT ON COLUMN data_model_data_domain.domain_desc IS '描述';
COMMENT ON COLUMN data_model_data_domain.create_user IS '创建人';
COMMENT ON COLUMN data_model_data_domain.create_time IS '创建时间';
COMMENT ON COLUMN data_model_data_domain.update_user IS '修改人';
COMMENT ON COLUMN data_model_data_domain.update_time IS '修改时间';
COMMENT ON COLUMN data_model_data_domain.tenant_id IS '租户ID';
COMMENT ON COLUMN data_model_dw_level.business_sector_id IS '所属业务板块';
COMMENT ON COLUMN data_model_dw_level.level_name_cn IS '层级名称(中文)';
COMMENT ON COLUMN data_model_dw_level.level_name IS '层级名称';
COMMENT ON COLUMN data_model_dw_level.create_user IS '创建人';
COMMENT ON COLUMN data_model_dw_level.create_time IS '创建时间';
COMMENT ON COLUMN data_model_dw_level.update_user IS '修改人';
COMMENT ON COLUMN data_model_dw_level.update_time IS '修改时间';
COMMENT ON COLUMN data_model_dw_level.tenant_id IS '租户ID';
COMMENT ON COLUMN data_model_export_record.record_name IS '记录名称';
COMMENT ON COLUMN data_model_export_record.create_user IS '创建人';
COMMENT ON COLUMN data_model_export_record.create_time IS '创建时间';
COMMENT ON COLUMN data_model_export_record.tenant_id IS '租户ID';
COMMENT ON COLUMN data_model_import_record.business_sector_id IS '业务板块';
COMMENT ON COLUMN data_model_import_record.record_name IS '记录名称';
COMMENT ON COLUMN data_model_import_record.record_desc IS '记录描述';
COMMENT ON COLUMN data_model_import_record.table_type IS '类别 ods-贴源表 dwd-维度表 dws-实时表 ads-汇总表';
COMMENT ON COLUMN data_model_import_record.create_user IS '创建人';
COMMENT ON COLUMN data_model_import_record.create_time IS '创建时间';
COMMENT ON COLUMN data_model_import_record.tenant_id IS '租户ID';
COMMENT ON COLUMN data_model_logic_table.business_sector_id IS '所属业务板块';
COMMENT ON COLUMN data_model_logic_table.dw_level_id IS '所属属仓层级';
COMMENT ON COLUMN data_model_logic_table.data_domain_id IS '所属数据域';
COMMENT ON COLUMN data_model_logic_table.process_id IS '所属业务过程';
COMMENT ON COLUMN data_model_logic_table.detail_type IS '明细类型:1事务型，2周期快照型';
COMMENT ON COLUMN data_model_logic_table.table_name_cn IS '中文名称';
COMMENT ON COLUMN data_model_logic_table.table_name IS '英文名称';
COMMENT ON COLUMN data_model_logic_table.table_name_abbr IS '英文缩写';
COMMENT ON COLUMN data_model_logic_table.table_desc IS '描述';
COMMENT ON COLUMN data_model_logic_table.table_type IS '类别 ods-贴源表 dwd-维度表 dws-实时表 ads-汇总表';
COMMENT ON COLUMN data_model_logic_table.table_physical IS '是否物理化 0-否 1-是';
COMMENT ON COLUMN data_model_logic_table.model_status IS '状态（0 表示删除，1表示正常）';
COMMENT ON COLUMN data_model_logic_table.create_user IS '创建人';
COMMENT ON COLUMN data_model_logic_table.create_time IS '创建时间';
COMMENT ON COLUMN data_model_logic_table.update_user IS '修改人';
COMMENT ON COLUMN data_model_logic_table.update_time IS '修改时间';
COMMENT ON COLUMN data_model_logic_table.tenant_id IS '租户ID';
COMMENT ON COLUMN data_model_logic_table.audit_status IS '审批状态(1、待审批，2、审批中 3、审批通过 4、审批拒绝 5、待实施)';
COMMENT ON COLUMN data_model_logic_table.db_type IS '数据源类型(CLICKHOUSE、HIVE)';
COMMENT ON COLUMN data_model_logic_table.apply_user IS '申请人';
COMMENT ON COLUMN data_model_logic_table.apply_time IS '申请时间';
COMMENT ON COLUMN data_model_logic_table.audit_user IS '审核人';
COMMENT ON COLUMN data_model_logic_table.audit_time IS '审核时间';
COMMENT ON COLUMN data_model_logic_table.audit_desc IS '审核描述';
COMMENT ON COLUMN data_model_logic_table_field.logic_table_id IS '所属表';
COMMENT ON COLUMN data_model_logic_table_field.field_name_cn IS '中文名称';
COMMENT ON COLUMN data_model_logic_table_field.field_name IS '英文名称';
COMMENT ON COLUMN data_model_logic_table_field.field_data_type IS '数据类型';
COMMENT ON COLUMN data_model_logic_table_field.field_sort IS '排序';
COMMENT ON COLUMN data_model_logic_table_field.is_primary_key IS '是否是主键 0-否 1-是';
COMMENT ON COLUMN data_model_logic_table_field.is_null IS '是否非空  1-非空 0-可以为空';
COMMENT ON COLUMN data_model_logic_table_field.create_user IS '创建人';
COMMENT ON COLUMN data_model_logic_table_field.create_time IS '创建时间';
COMMENT ON COLUMN data_model_logic_table_field.update_user IS '修改人';
COMMENT ON COLUMN data_model_logic_table_field.update_time IS '修改时间';
COMMENT ON COLUMN data_model_logic_table_field.tenant_id IS '租户ID';
COMMENT ON COLUMN data_model_logic_table_relation.logic_table_id IS '表';
COMMENT ON COLUMN data_model_logic_table_relation.logic_field_id IS '字段';
COMMENT ON COLUMN data_model_logic_table_relation.relation_table_id IS '关联表';
COMMENT ON COLUMN data_model_logic_table_relation.relation_field_id IS '关联字段';
COMMENT ON COLUMN data_model_logic_table_relation.create_user IS '创建人';
COMMENT ON COLUMN data_model_logic_table_relation.create_time IS '创建时间';
COMMENT ON COLUMN data_model_logic_table_relation.update_user IS '修改人';
COMMENT ON COLUMN data_model_logic_table_relation.update_time IS '修改时间';
COMMENT ON COLUMN data_model_logic_table_relation.tenant_id IS '租户ID';
COMMENT ON COLUMN data_model_qua_monitor_rule.model_id IS '模型ID';
COMMENT ON COLUMN data_model_qua_monitor_rule.rule_name IS '规则名称';
COMMENT ON COLUMN data_model_qua_monitor_rule.rule_code IS '规则代码';
COMMENT ON COLUMN data_model_qua_monitor_rule.rule_type IS '规则类型';
COMMENT ON COLUMN data_model_qua_monitor_rule.table_name IS '表名/索引名';
COMMENT ON COLUMN data_model_qua_monitor_rule.column_name IS '字段名 多个字段使用逗号分隔';
COMMENT ON COLUMN data_model_qua_monitor_rule.rule_detail IS '规则明细 {config_key:config_value}';
COMMENT ON COLUMN data_model_qua_monitor_rule.rule_level IS '问题级别';
COMMENT ON COLUMN data_model_qua_monitor_rule.rule_desc IS '规则名称';
COMMENT ON COLUMN data_model_qua_monitor_rule.create_time IS '创建时间';
COMMENT ON COLUMN data_model_qua_monitor_rule.create_user IS '创建用户';
COMMENT ON COLUMN data_model_qua_monitor_rule.update_time IS '更新时间';
COMMENT ON COLUMN data_model_qua_monitor_rule.update_user IS '更新时间';
COMMENT ON COLUMN data_model_qua_monitor_rule.flag IS '有效标识:1-有效；0-无效';
COMMENT ON COLUMN data_model_qua_monitor_rule.tenant_id IS '租户ID';
COMMENT ON COLUMN eqpt_character_report_list.id IS '自增ID';
COMMENT ON COLUMN eqpt_character_report_list.branch_code IS '资源池';
COMMENT ON COLUMN eqpt_character_report_list.vendor_name IS '厂商';
COMMENT ON COLUMN eqpt_character_report_list.eqpt_type IS '设备类型';
COMMENT ON COLUMN eqpt_character_report_list.eqpt_ip IS '设备IP';
COMMENT ON COLUMN eqpt_character_report_list.sys_version IS '设备系统版本';
COMMENT ON COLUMN eqpt_character_report_list.character_num IS '特征库数量';
COMMENT ON COLUMN eqpt_character_report_list.character_name IS '特征库名称';
COMMENT ON COLUMN eqpt_character_report_list.character_version IS '特征库版本';
COMMENT ON COLUMN eqpt_character_report_list.send_time IS '发送时间';
COMMENT ON COLUMN intelligence_query_history.query_condition IS '查询条件';
COMMENT ON COLUMN intelligence_query_history.create_time IS '创建时间';
COMMENT ON COLUMN intelligence_query_history.create_user IS '创建人';
COMMENT ON COLUMN intelligence_query_history.update_time IS '更新时间';
COMMENT ON COLUMN intelligence_query_history.update_user IS '更新人';
COMMENT ON COLUMN intelligence_query_history.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_config_business.category_id IS '所属分组';
COMMENT ON COLUMN qua_config_business.business_name IS '类型名称';
COMMENT ON COLUMN qua_config_business.business_desc IS '备注';
COMMENT ON COLUMN qua_config_business.create_time IS '创建时间';
COMMENT ON COLUMN qua_config_business.create_user IS '创建用户';
COMMENT ON COLUMN qua_config_business.update_time IS '更新时间';
COMMENT ON COLUMN qua_config_business.update_user IS '更新时间';
COMMENT ON COLUMN qua_config_business.flag IS '有效标识:1-有效；0-无效';
COMMENT ON COLUMN qua_config_business.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_config_business_category.category_name IS '分组名';
COMMENT ON COLUMN qua_config_business_category.category_desc IS '备注';
COMMENT ON COLUMN qua_config_business_category.create_time IS '创建时间';
COMMENT ON COLUMN qua_config_business_category.create_user IS '创建用户';
COMMENT ON COLUMN qua_config_business_category.update_time IS '更新时间';
COMMENT ON COLUMN qua_config_business_category.update_user IS '更新时间';
COMMENT ON COLUMN qua_config_business_category.flag IS '有效标识:1-有效；0-无效';
COMMENT ON COLUMN qua_config_business_category.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_config_master_data_type.type_name IS '主数据类型名称';
COMMENT ON COLUMN qua_config_master_data_type.type_desc IS '备注';
COMMENT ON COLUMN qua_config_master_data_type.create_time IS '创建时间';
COMMENT ON COLUMN qua_config_master_data_type.create_user IS '创建用户';
COMMENT ON COLUMN qua_config_master_data_type.update_time IS '更新时间';
COMMENT ON COLUMN qua_config_master_data_type.update_user IS '更新时间';
COMMENT ON COLUMN qua_config_master_data_type.flag IS '有效标识:1-有效；0-无效';
COMMENT ON COLUMN qua_config_master_data_type.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_data_standard_config.cn_name IS '标准中文名';
COMMENT ON COLUMN qua_data_standard_config.en_name IS '标准英文名';
COMMENT ON COLUMN qua_data_standard_config.code IS '标准编号';
COMMENT ON COLUMN qua_data_standard_config.business_meaning IS '业务含义';
COMMENT ON COLUMN qua_data_standard_config.accordance IS '制定依据';
COMMENT ON COLUMN qua_data_standard_config.standard_type IS '类型，枚举值：1数据长度、2数据类型、3数据精度、4中英文、5自定义';
COMMENT ON COLUMN qua_data_standard_config.data_length IS '数据长度';
COMMENT ON COLUMN qua_data_standard_config.data_column_type IS '数据类型';
COMMENT ON COLUMN qua_data_standard_config.data_precision IS '小数长度，1、2、3、4、5';
COMMENT ON COLUMN qua_data_standard_config.cn_en_verification IS '中英文校验,1：仅包括中文 2：仅包含英文 3：包含中英文';
COMMENT ON COLUMN qua_data_standard_config.custom_content IS '自定义编辑';
COMMENT ON COLUMN qua_data_standard_config.remark IS '备注';
COMMENT ON COLUMN qua_data_standard_config.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_data_standard_config.set_id IS '标准集id';
COMMENT ON COLUMN qua_data_standard_config.status IS '启停，1启用，0停用';
COMMENT ON COLUMN qua_data_standard_config.encrypt IS '是否启用加密，1启用，0停用';
COMMENT ON COLUMN qua_data_standard_config.dept IS '所属部门';
COMMENT ON COLUMN qua_data_standard_config.business_system IS '所属系统';
COMMENT ON COLUMN qua_data_standard_config.store_cycle IS '存储周期';
COMMENT ON COLUMN qua_data_standard_config.database_name IS '所属数据库';
COMMENT ON COLUMN qua_data_standard_config.sensitive_type IS '敏感类型';
COMMENT ON COLUMN qua_data_standard_config.specification IS '权限说明';
COMMENT ON COLUMN qua_data_standard_config.version_no IS '版本号';
COMMENT ON COLUMN qua_data_standard_config.publish_status IS '发布状态，0未发布，1已发布';
COMMENT ON COLUMN qua_data_standard_config.publish_name IS '发布名称';
COMMENT ON COLUMN qua_data_standard_config.publish_desc IS '发布描述';
COMMENT ON COLUMN qua_data_standard_config.publish_time IS '发布时间';
COMMENT ON COLUMN qua_data_standard_config.publish_user IS '发布人';
COMMENT ON COLUMN qua_data_standard_config.approval_status IS '审批状态，0待审批 1同意 2拒绝';
COMMENT ON COLUMN qua_data_standard_config.approval_desc IS '审批说明';
COMMENT ON COLUMN qua_data_standard_config.approval_time IS '审批时间';
COMMENT ON COLUMN qua_data_standard_config.approval_user IS '审批人';
COMMENT ON COLUMN qua_data_standard_config.type IS '类型，0数据标准，1数据元';
COMMENT ON COLUMN qua_data_standard_config_master_data.config_id IS '标准id';
COMMENT ON COLUMN qua_data_standard_config_master_data.master_data_id IS '元数据id';
COMMENT ON COLUMN qua_data_standard_config_master_data.remark IS '备注';
COMMENT ON COLUMN qua_data_standard_config_master_data.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_data_standard_config_master_data.db_type IS '库类型';
COMMENT ON COLUMN qua_data_standard_config_master_data.snapshoot_version IS '快照版本号';
COMMENT ON COLUMN qua_data_standard_config_master_data.column_name IS '字段名称（字段英文名）';
COMMENT ON COLUMN qua_data_standard_config_master_data.column_name_cn IS '字段中文名，默认为空字符串';
COMMENT ON COLUMN qua_data_standard_config_publish_log.id IS '主键id';
COMMENT ON COLUMN qua_data_standard_config_publish_log.config_id IS '标准主键id';
COMMENT ON COLUMN qua_data_standard_config_publish_log.flag IS '0有效，1无效';
COMMENT ON COLUMN qua_data_standard_config_publish_log.publish_status IS '发布状态，0未发布，1发布成功，2发布失败';
COMMENT ON COLUMN qua_data_standard_config_publish_log.publish_name IS '发布名称';
COMMENT ON COLUMN qua_data_standard_config_publish_log.publish_desc IS '发布描述';
COMMENT ON COLUMN qua_data_standard_config_publish_log.publish_time IS '发布时间';
COMMENT ON COLUMN qua_data_standard_config_publish_log.publish_user IS '发布人';
COMMENT ON COLUMN qua_data_standard_config_publish_log.approval_status IS '审批状态，0待审批 1同意 2拒绝';
COMMENT ON COLUMN qua_data_standard_config_publish_log.approval_desc IS '审批说明';
COMMENT ON COLUMN qua_data_standard_config_publish_log.approval_time IS '审批时间';
COMMENT ON COLUMN qua_data_standard_config_publish_log.approval_user IS '审批人';
COMMENT ON COLUMN qua_data_standard_config_restore_log.id IS '主键id';
COMMENT ON COLUMN qua_data_standard_config_restore_log.config_id IS '标准主键id';
COMMENT ON COLUMN qua_data_standard_config_restore_log.restore_version_no IS '恢复后版本号';
COMMENT ON COLUMN qua_data_standard_config_restore_log.restore_desc IS '恢复描述';
COMMENT ON COLUMN qua_data_standard_config_restore_log.restore_user IS '操作人';
COMMENT ON COLUMN qua_data_standard_config_restore_log.restore_time IS '恢复时间';
COMMENT ON COLUMN qua_data_standard_config_restore_log.restore_result IS '恢复结果，0成功，1失败';
COMMENT ON COLUMN qua_data_standard_config_restore_log.from_version_no IS '选择恢复的版本号';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.cn_name IS '标准中文名';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.en_name IS '标准英文名';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.code IS '标准编号';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.business_meaning IS '业务含义';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.accordance IS '制定依据';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.standard_type IS '类型，枚举值：1数据长度、2数据类型、3数据精度、4中英文、5自定义';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.data_length IS '数据长度';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.data_column_type IS '数据类型';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.data_precision IS '小数长度，1、2、3、4、5';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.cn_en_verification IS '中英文校验,1：仅包括中文 2：仅包含英文 3：包含中英文';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.custom_content IS '自定义编辑';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.remark IS '备注';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.set_id IS '标准集id';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.status IS '启停，1启用，0停用';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.version_no IS '版本号';
COMMENT ON COLUMN qua_data_standard_config_version_snapshot.config_id IS '数据标准id';
COMMENT ON COLUMN qua_data_standard_dic.dic_name IS '字典名称';
COMMENT ON COLUMN qua_data_standard_dic.dic_code IS '字典编号';
COMMENT ON COLUMN qua_data_standard_dic.dic_type IS '类型';
COMMENT ON COLUMN qua_data_standard_dic.remark IS '备注';
COMMENT ON COLUMN qua_data_standard_dic.dic_config IS '字段配置: [{"value":"","cnName":"","remark":""}]';
COMMENT ON COLUMN qua_data_standard_dic.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_data_standard_group.name IS '名称';
COMMENT ON COLUMN qua_data_standard_group.create_time IS '入库时间';
COMMENT ON COLUMN qua_data_standard_group.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_data_standard_group.type IS '类型，0数据标准，1数据元';
COMMENT ON COLUMN qua_data_standard_set.name IS '名称';
COMMENT ON COLUMN qua_data_standard_set.create_time IS '入库时间';
COMMENT ON COLUMN qua_data_standard_set.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_data_standard_set.group_id IS '分组id';
COMMENT ON COLUMN qua_dync_threshold.source_type IS '数据源类型: mysql,clickhouse,elasticsearch,hive';
COMMENT ON COLUMN qua_dync_threshold.element_id IS '元数据ID';
COMMENT ON COLUMN qua_dync_threshold.model_type IS '计算模型: MA , ARIMA ,EWMA';
COMMENT ON COLUMN qua_dync_threshold.forecast_target IS '预测对象: 行数 line , 空间 space';
COMMENT ON COLUMN qua_dync_threshold.remark IS '备注';
COMMENT ON COLUMN qua_dync_threshold.forecast_value IS '最新预测值';
COMMENT ON COLUMN qua_dync_threshold.status IS '运行状态: 1运行，0停止';
COMMENT ON COLUMN qua_dync_threshold.create_time IS '创建时间';
COMMENT ON COLUMN qua_dync_threshold.create_user IS '创建人';
COMMENT ON COLUMN qua_dync_threshold.update_time IS '更新时间';
COMMENT ON COLUMN qua_dync_threshold.update_user IS '更新人';
COMMENT ON COLUMN qua_internal_model.name IS '模版名称';
COMMENT ON COLUMN qua_internal_model.model_code IS '模板编码';
COMMENT ON COLUMN qua_internal_model.model_desc IS '模版描述';
COMMENT ON COLUMN qua_internal_model.model_calc IS '模板计算逻辑';
COMMENT ON COLUMN qua_internal_model.dimensions IS '模版维度:及时性 timeliness、唯一性 uniqueness、完整性 integrality、一致性 uniformity、有效性 effectiveness、准确性 precision';
COMMENT ON COLUMN qua_internal_model.audit_object IS '稽核对象: 表table, 字段field';
COMMENT ON COLUMN qua_internal_model.request_param IS '输入参数';
COMMENT ON COLUMN qua_internal_model.custom_sql IS '自定义sql';
COMMENT ON COLUMN qua_internal_model.model_type IS '1-内置 2-自定义 模版类型';
COMMENT ON COLUMN qua_internal_model.create_time IS '创建时间';
COMMENT ON COLUMN qua_internal_model.create_user IS '创建人';
COMMENT ON COLUMN qua_internal_model.update_time IS '更新时间';
COMMENT ON COLUMN qua_internal_model.update_user IS '更新人';
COMMENT ON COLUMN qua_internal_model.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_master_data.model_id IS '主数据模型ID';
COMMENT ON COLUMN qua_master_data.field_name IS '字段名称';
COMMENT ON COLUMN qua_master_data.field_code IS '字段编码';
COMMENT ON COLUMN qua_master_data.field_desc IS '字段描述';
COMMENT ON COLUMN qua_master_data.data_type IS '主数据类型';
COMMENT ON COLUMN qua_master_data.field_length IS '字段长度';
COMMENT ON COLUMN qua_master_data.field_precision IS '字段精度';
COMMENT ON COLUMN qua_master_data.is_null IS '是否可以为空 1-是 0-否';
COMMENT ON COLUMN qua_master_data.is_unique IS '是否可以唯一 1-是 0-否';
COMMENT ON COLUMN qua_master_data.create_time IS '创建时间';
COMMENT ON COLUMN qua_master_data.create_user IS '创建用户';
COMMENT ON COLUMN qua_master_data.update_time IS '更新时间';
COMMENT ON COLUMN qua_master_data.update_user IS '更新时间';
COMMENT ON COLUMN qua_master_data.flag IS '有效标识:1-有效；0-无效';
COMMENT ON COLUMN qua_master_data.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_master_data.data_status IS ' 状态 0-冻结 1-正常';
COMMENT ON COLUMN qua_master_data.open_status IS ' 状态 0-失效 1-正常';
COMMENT ON COLUMN qua_master_data_model.model_name IS '主数据模型名称';
COMMENT ON COLUMN qua_master_data_model.model_code IS '编码规则';
COMMENT ON COLUMN qua_master_data_model.model_type_id IS '主数据类型id';
COMMENT ON COLUMN qua_master_data_model.model_status IS '模型状态:1-正常；0-冻结';
COMMENT ON COLUMN qua_master_data_model.create_time IS '创建时间';
COMMENT ON COLUMN qua_master_data_model.create_user IS '创建用户';
COMMENT ON COLUMN qua_master_data_model.update_time IS '更新时间';
COMMENT ON COLUMN qua_master_data_model.update_user IS '更新时间';
COMMENT ON COLUMN qua_master_data_model.flag IS '有效标识:1-有效；0-无效';
COMMENT ON COLUMN qua_master_data_model.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_master_data_model.open_status IS ' 状态 0-失效 1-正常';
COMMENT ON COLUMN qua_master_data_model_relation.model_id IS '主数据模型id';
COMMENT ON COLUMN qua_master_data_model_relation.related_model_id IS '被关联主数据模型id';
COMMENT ON COLUMN qua_master_data_model_relation.related_type IS '关联关系，1调用、2属于、3包含';
COMMENT ON COLUMN qua_monitor_job.id IS '主键';
COMMENT ON COLUMN qua_monitor_job.model_id IS '模型ID';
COMMENT ON COLUMN qua_monitor_job.name IS '任务名称';
COMMENT ON COLUMN qua_monitor_job.execute_auto IS '01-人工执行；02-自动执行；03:定时执行;04:循环执行';
COMMENT ON COLUMN qua_monitor_job.execute_cycle IS '01:一次性任务;02:周期任务';
COMMENT ON COLUMN qua_monitor_job.execute_cycle_desc IS '执行周期描述';
COMMENT ON COLUMN qua_monitor_job.execute_type IS '执行方式:day,week,month,year,cron';
COMMENT ON COLUMN qua_monitor_job.execute_config IS '执行配置，json格式';
COMMENT ON COLUMN qua_monitor_job.execute_cron IS 'quartz表达式';
COMMENT ON COLUMN qua_monitor_job.define_time IS '定时执行指定时间';
COMMENT ON COLUMN qua_monitor_job.start_time IS '开始时间';
COMMENT ON COLUMN qua_monitor_job.end_time IS '结束时间';
COMMENT ON COLUMN qua_monitor_job.valid_time IS '起始时间';
COMMENT ON COLUMN qua_monitor_job.invalid_time IS '截止时间';
COMMENT ON COLUMN qua_monitor_job.execute_current_time IS '本次执行时间';
COMMENT ON COLUMN qua_monitor_job.execute_next_time IS '下次执行时间';
COMMENT ON COLUMN qua_monitor_job.job_desc IS '任务描述';
COMMENT ON COLUMN qua_monitor_job.execute_status IS '执行状态';
COMMENT ON COLUMN qua_monitor_job.execute_result IS '执行结果说明';
COMMENT ON COLUMN qua_monitor_job.audit_remark IS '核查结果说明';
COMMENT ON COLUMN qua_monitor_job.job_rules IS '规则 多个规则使用逗号分隔';
COMMENT ON COLUMN qua_monitor_job.rule_weight IS '规则权重 权重和规则一一对应';
COMMENT ON COLUMN qua_monitor_job.sample_cnt IS '样例数据条数';
COMMENT ON COLUMN qua_monitor_job.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_monitor_job.create_user IS '创建者';
COMMENT ON COLUMN qua_monitor_job.create_time IS '创建日期';
COMMENT ON COLUMN qua_monitor_job.update_user IS '更新者';
COMMENT ON COLUMN qua_monitor_job.update_time IS '更新日期';
COMMENT ON COLUMN qua_monitor_job.status IS '状态 2:执行中 3:暂停';
COMMENT ON COLUMN qua_monitor_job.flag IS '有效标识:1-有效；0-无效';
COMMENT ON COLUMN qua_monitor_job.execute_rule_type IS '01-弱规则执行  02-强规则执行';
COMMENT ON COLUMN qua_monitor_job.flow_id IS '数据源ID';
COMMENT ON COLUMN qua_monitor_model.model_name IS '模型名称';
COMMENT ON COLUMN qua_monitor_model.model_desc IS '模型描述';
COMMENT ON COLUMN qua_monitor_model.element_id IS '资源ID';
COMMENT ON COLUMN qua_monitor_model.element_type IS '资源类型 CH、ES、MYSQL、HIVE';
COMMENT ON COLUMN qua_monitor_model.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_monitor_model.database_id IS '数据库ID';
COMMENT ON COLUMN qua_monitor_model.database_name IS '数据库名';
COMMENT ON COLUMN qua_monitor_model.create_time IS '创建时间';
COMMENT ON COLUMN qua_monitor_model.create_user IS '创建用户';
COMMENT ON COLUMN qua_monitor_model.update_time IS '更新时间';
COMMENT ON COLUMN qua_monitor_model.update_user IS '更新时间';
COMMENT ON COLUMN qua_monitor_model.flag IS '有效标识:1-有效；0-无效';
COMMENT ON COLUMN qua_monitor_model.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_monitor_model.open_status IS '0-关闭数据源 1-启用数据源';
COMMENT ON COLUMN qua_monitor_model_resource.model_id IS '模型ID';
COMMENT ON COLUMN qua_monitor_model_resource.table_id IS '表ID';
COMMENT ON COLUMN qua_monitor_model_resource.table_name IS '表名/索引名';
COMMENT ON COLUMN qua_monitor_model_resource.create_time IS '创建时间';
COMMENT ON COLUMN qua_monitor_model_resource.create_user IS '创建用户';
COMMENT ON COLUMN qua_monitor_model_resource.update_time IS '更新时间';
COMMENT ON COLUMN qua_monitor_model_resource.update_user IS '更新时间';
COMMENT ON COLUMN qua_monitor_model_resource.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_monitor_result.id IS '主键';
COMMENT ON COLUMN qua_monitor_result.job_id IS '任务ID';
COMMENT ON COLUMN qua_monitor_result.task_id IS '任务ID';
COMMENT ON COLUMN qua_monitor_result.element_id IS '资产ID';
COMMENT ON COLUMN qua_monitor_result.monitor_score IS '监测分数';
COMMENT ON COLUMN qua_monitor_result.level_cnt IS '级别数量统计{level:cnt}';
COMMENT ON COLUMN qua_monitor_result.success_match_rules IS '执行成功匹配规则 多个规则使用逗号分隔';
COMMENT ON COLUMN qua_monitor_result.success_not_match_rules IS '执行成功不匹配规则 多个规则使用逗号分隔';
COMMENT ON COLUMN qua_monitor_result.fail_rules IS '执行失败规则 多个规则使用逗号分隔';
COMMENT ON COLUMN qua_monitor_result.create_time IS '创建时间';
COMMENT ON COLUMN qua_monitor_result.update_time IS '修改时间';
COMMENT ON COLUMN qua_monitor_result.tenant_id IS '租户id';
COMMENT ON COLUMN qua_monitor_result.is_new IS '是否新增';
COMMENT ON COLUMN qua_monitor_result_appeal.appeal_user IS '申诉人';
COMMENT ON COLUMN qua_monitor_result_appeal.appeal_reason IS '申诉原因';
COMMENT ON COLUMN qua_monitor_result_appeal.appeal_time IS '申诉时间';
COMMENT ON COLUMN qua_monitor_result_appeal.handle_user IS '处理人';
COMMENT ON COLUMN qua_monitor_result_appeal.handle_time IS '处理时间';
COMMENT ON COLUMN qua_monitor_result_appeal.handle_status IS '处理状态：0未处理，1已处理';
COMMENT ON COLUMN qua_monitor_result_detail.id IS '主键';
COMMENT ON COLUMN qua_monitor_result_detail.job_id IS '任务ID';
COMMENT ON COLUMN qua_monitor_result_detail.task_id IS '任务ID';
COMMENT ON COLUMN qua_monitor_result_detail.table_name IS '表名';
COMMENT ON COLUMN qua_monitor_result_detail.column_name IS '字段名';
COMMENT ON COLUMN qua_monitor_result_detail.rule_type IS '触发规则';
COMMENT ON COLUMN qua_monitor_result_detail.rule_weight IS '规则权重';
COMMENT ON COLUMN qua_monitor_result_detail.rule_level IS '问题级别';
COMMENT ON COLUMN qua_monitor_result_detail.create_time IS '创建时间';
COMMENT ON COLUMN qua_monitor_result_detail.update_time IS '修改时间';
COMMENT ON COLUMN qua_monitor_result_detail.tenant_id IS '租户id';
COMMENT ON COLUMN qua_monitor_result_detail.template_id IS '模型模板ID';
COMMENT ON COLUMN qua_monitor_result_detail.rule_type_id IS '规则ID';
COMMENT ON COLUMN qua_monitor_result_detail.is_match IS '匹配结果 0-匹配失败 1-匹配成功';
COMMENT ON COLUMN qua_monitor_result_detail.rule_score IS '规则分数';
COMMENT ON COLUMN qua_monitor_result_detail.compare_value IS '稽核值';
COMMENT ON COLUMN qua_monitor_result_detail.threshold_value IS '阈值';
COMMENT ON COLUMN qua_monitor_rule.model_id IS '模型ID';
COMMENT ON COLUMN qua_monitor_rule.rule_name IS '规则名称';
COMMENT ON COLUMN qua_monitor_rule.rule_code IS '规则代码';
COMMENT ON COLUMN qua_monitor_rule.rule_type IS '规则类型';
COMMENT ON COLUMN qua_monitor_rule.table_name IS '表名/索引名';
COMMENT ON COLUMN qua_monitor_rule.column_name IS '字段名 多个字段使用逗号分隔';
COMMENT ON COLUMN qua_monitor_rule.rule_detail IS '规则明细 {config_key:config_value}';
COMMENT ON COLUMN qua_monitor_rule.rule_level IS '问题级别';
COMMENT ON COLUMN qua_monitor_rule.rule_desc IS '规则名称';
COMMENT ON COLUMN qua_monitor_rule.create_time IS '创建时间';
COMMENT ON COLUMN qua_monitor_rule.create_user IS '创建用户';
COMMENT ON COLUMN qua_monitor_rule.update_time IS '更新时间';
COMMENT ON COLUMN qua_monitor_rule.update_user IS '更新时间';
COMMENT ON COLUMN qua_monitor_rule.flag IS '有效标识:1-有效；0-无效';
COMMENT ON COLUMN qua_monitor_rule.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_monitor_rule.threshold_operator IS '阈值比较运算符';
COMMENT ON COLUMN qua_monitor_rule.template_id IS '模型模板ID';
COMMENT ON COLUMN qua_monitor_rule.threshold_value IS '阈值';
COMMENT ON COLUMN qua_monitor_rule.rule_type_id IS '模板类型ID';
COMMENT ON COLUMN qua_monitor_rule.rule_weight IS '权重';
COMMENT ON COLUMN qua_monitor_rule_exec_record.rule_id IS '模型id';
COMMENT ON COLUMN qua_monitor_rule_exec_record.exec_result IS '试跑结果 1-触发规则  0-未触发规则 -1-执行失败';
COMMENT ON COLUMN qua_monitor_rule_exec_record.create_time IS '执行时间';
COMMENT ON COLUMN qua_monitor_rule_exec_record.create_user IS '创建人';
COMMENT ON COLUMN qua_monitor_rule_exec_record.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_monitor_rule_template.template_name IS '模型名称';
COMMENT ON COLUMN qua_monitor_rule_template.model_id IS '数据源ID';
COMMENT ON COLUMN qua_monitor_rule_template.table_name IS '表名/索引名';
COMMENT ON COLUMN qua_monitor_rule_template.delete_flag IS '删除标识 0-未删除 1-已删除';
COMMENT ON COLUMN qua_monitor_rule_template.data_filter IS '筛选数据 0-前1000条 1-全部数据';
COMMENT ON COLUMN qua_monitor_rule_template.create_time IS '创建时间';
COMMENT ON COLUMN qua_monitor_rule_template.create_user IS '创建人';
COMMENT ON COLUMN qua_monitor_rule_template.update_time IS '更新时间';
COMMENT ON COLUMN qua_monitor_rule_template.update_user IS '更新人';
COMMENT ON COLUMN qua_monitor_rule_template.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_monitor_rule_type.type_code IS '类别编码';
COMMENT ON COLUMN qua_monitor_rule_type.type_name IS '类别名称';
COMMENT ON COLUMN qua_monitor_rule_type.config_key IS '配置项Key';
COMMENT ON COLUMN qua_monitor_rule_type.config_name IS '配置项名称';
COMMENT ON COLUMN qua_monitor_rule_type.config_type IS '配置项类型input、select、date、checkbox';
COMMENT ON COLUMN qua_monitor_rule_type.is_required IS '是否必填 0-否 1-是';
COMMENT ON COLUMN qua_monitor_rule_type.reg_pattern IS '正则表达式 如果该值不为空则配置的值需要满足该正则';
COMMENT ON COLUMN qua_monitor_rule_type.interface_name IS '接口名 如果类型为下拉框，则需要根据该配置调用接口获取下拉框的值';
COMMENT ON COLUMN qua_monitor_rule_type.multi_select IS '是否多选 0-否 1-是 (判断下来框是否多选)';
COMMENT ON COLUMN qua_monitor_rule_type.sort_no IS '排序';
COMMENT ON COLUMN qua_monitor_rule_type.type_desc IS '规则描述';
COMMENT ON COLUMN qua_monitor_task.id IS '主键';
COMMENT ON COLUMN qua_monitor_task.model_id IS '模型ID';
COMMENT ON COLUMN qua_monitor_task.task_no IS '任务编号';
COMMENT ON COLUMN qua_monitor_task.job_id IS '任务ID';
COMMENT ON COLUMN qua_monitor_task.job_rules IS '规则 多个规则使用逗号分隔';
COMMENT ON COLUMN qua_monitor_task.rule_weight IS '规则权重 权重和规则一一对应';
COMMENT ON COLUMN qua_monitor_task.sample_cnt IS '样例数据条数';
COMMENT ON COLUMN qua_monitor_task.start_time IS '任务待执行日期';
COMMENT ON COLUMN qua_monitor_task.create_time IS '创建时间';
COMMENT ON COLUMN qua_monitor_task.end_time IS '执行完成时间';
COMMENT ON COLUMN qua_monitor_task.task_result IS '执行结果。0：失败；1：成功';
COMMENT ON COLUMN qua_monitor_task.fail_reason IS '失败原因';
COMMENT ON COLUMN qua_monitor_task.run_process IS '执行进度百分比0-100';
COMMENT ON COLUMN qua_monitor_task.status IS '任务状态 1:待执行 2:执行中 3:暂停 4:扫描完成 5:停止 6:任务结束';
COMMENT ON COLUMN qua_monitor_task.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_monitor_weight.dim_name IS '维度名称';
COMMENT ON COLUMN qua_monitor_weight.dim_code IS '维度编码';
COMMENT ON COLUMN qua_monitor_weight.weight_value IS '权重';
COMMENT ON COLUMN qua_monitor_weight.weight_desc IS '备注';
COMMENT ON COLUMN qua_monitor_weight.create_time IS '创建时间';
COMMENT ON COLUMN qua_monitor_weight.create_user IS '创建人';
COMMENT ON COLUMN qua_monitor_weight.update_time IS '更新时间';
COMMENT ON COLUMN qua_monitor_weight.update_user IS '更新人';
COMMENT ON COLUMN qua_scan_ch_column.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_ch_column.table_id IS '表ID';
COMMENT ON COLUMN qua_scan_ch_column.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_ch_column.db_name IS '数据库名称';
COMMENT ON COLUMN qua_scan_ch_column.table_name IS '数据表名称';
COMMENT ON COLUMN qua_scan_ch_column.column_name IS '字段名称';
COMMENT ON COLUMN qua_scan_ch_column.type IS '字段类型';
COMMENT ON COLUMN qua_scan_ch_column.position IS '字段序号，取值从1开始';
COMMENT ON COLUMN qua_scan_ch_column.default_kind IS '字段默认值类型，取值：DEFAULT、ALIAS';
COMMENT ON COLUMN qua_scan_ch_column.default_expression IS '字段默认值表达式，如：now()、CAST(1, "Int32")';
COMMENT ON COLUMN qua_scan_ch_column.data_compressed_bytes IS '数据压缩后字节数';
COMMENT ON COLUMN qua_scan_ch_column.data_uncompressed_bytes IS '数据未压缩字节数';
COMMENT ON COLUMN qua_scan_ch_column.marks_bytes IS '标记的大小';
COMMENT ON COLUMN qua_scan_ch_column.comment IS '字段描述';
COMMENT ON COLUMN qua_scan_ch_column.is_in_partition_key IS '是否属于分区字段';
COMMENT ON COLUMN qua_scan_ch_column.is_in_sorting_key IS '是否属于排序字段';
COMMENT ON COLUMN qua_scan_ch_column.is_in_primary_key IS '是否属于主键字段';
COMMENT ON COLUMN qua_scan_ch_column.is_in_sampling_key IS '是否属于抽样字段';
COMMENT ON COLUMN qua_scan_ch_column.compression_codec IS '压缩编码器名称';
COMMENT ON COLUMN qua_scan_ch_column.is_nullable IS '可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串';
COMMENT ON COLUMN qua_scan_ch_column.column_length IS '字段长度（bit），根据字段类型不同而不同，目前仅支持数值型字段';
COMMENT ON COLUMN qua_scan_ch_column.column_precision IS '字段精度（bit），仅针对浮点数值型字段';
COMMENT ON COLUMN qua_scan_ch_column.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_scan_ch_db.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_ch_db.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_ch_db.db_name IS '数据库名称';
COMMENT ON COLUMN qua_scan_ch_db.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_scan_ch_table.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_ch_table.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_ch_table.db_id IS '扫描的数据库ID';
COMMENT ON COLUMN qua_scan_ch_table.db_name IS '数据库名称';
COMMENT ON COLUMN qua_scan_ch_table.table_name IS '数据表名称';
COMMENT ON COLUMN qua_scan_ch_table.uuid IS 'CH表系统ID';
COMMENT ON COLUMN qua_scan_ch_table.engine IS '引擎类型';
COMMENT ON COLUMN qua_scan_ch_table.is_temporary IS '是否临时表';
COMMENT ON COLUMN qua_scan_ch_table.data_paths IS '数据地址';
COMMENT ON COLUMN qua_scan_ch_table.metadata_path IS '元数据地址';
COMMENT ON COLUMN qua_scan_ch_table.metadata_modification_time IS '元数据修改时间';
COMMENT ON COLUMN qua_scan_ch_table.dependencies_database IS '依赖数据库';
COMMENT ON COLUMN qua_scan_ch_table.dependencies_table IS '依赖数据表';
COMMENT ON COLUMN qua_scan_ch_table.create_table_query IS '建表语句';
COMMENT ON COLUMN qua_scan_ch_table.engine_full IS '引擎参数';
COMMENT ON COLUMN qua_scan_ch_table.partition_key IS '分区字段';
COMMENT ON COLUMN qua_scan_ch_table.sorting_key IS '排序字段';
COMMENT ON COLUMN qua_scan_ch_table.primary_key IS '主键字段';
COMMENT ON COLUMN qua_scan_ch_table.sampling_key IS '抽样字段';
COMMENT ON COLUMN qua_scan_ch_table.storage_policy IS '存储策略';
COMMENT ON COLUMN qua_scan_ch_table.total_rows IS '总行数';
COMMENT ON COLUMN qua_scan_ch_table.total_bytes IS '总字节数';
COMMENT ON COLUMN qua_scan_ch_table.lifetime_rows IS '插入总行数';
COMMENT ON COLUMN qua_scan_ch_table.lifetime_bytes IS '插入总字节数';
COMMENT ON COLUMN qua_scan_ch_table.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_scan_es_field.task_no IS '任务号';
COMMENT ON COLUMN qua_scan_es_field.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_es_field.index_id IS '扫描的索引ID';
COMMENT ON COLUMN qua_scan_es_field.index_name IS '索引名称';
COMMENT ON COLUMN qua_scan_es_field.field_name IS '字段名称';
COMMENT ON COLUMN qua_scan_es_field.field_data_type IS '字段数据类型';
COMMENT ON COLUMN qua_scan_es_field.analyzer IS '用于文本分析的分析器';
COMMENT ON COLUMN qua_scan_es_field.boost IS '相关性得分计算参数';
COMMENT ON COLUMN qua_scan_es_field.coerce IS '试图清除脏值以适应字段的数据类型';
COMMENT ON COLUMN qua_scan_es_field.copy_to IS '将多个字段的值复制到组字段中，然后可以将组字段作为单个字段进行查询';
COMMENT ON COLUMN qua_scan_es_field.doc_values IS '文档索引时构建的磁盘数据结构';
COMMENT ON COLUMN qua_scan_es_field.dynamic IS '对包含新字段的文档进行索引时，动态地添加到文档或文档内部对象中';
COMMENT ON COLUMN qua_scan_es_field.eager_global_ordinals IS '字段类型使用序号映射来存储文档值，以获得更紧凑的表示';
COMMENT ON COLUMN qua_scan_es_field.enabled IS '可配置跳过对字段内容的解析和索引';
COMMENT ON COLUMN qua_scan_es_field.fielddata IS '可配置文本字段在默认情况下用于聚合、排序或脚本编写';
COMMENT ON COLUMN qua_scan_es_field.fields IS '以不同的方式为同一个字段建立索引通';
COMMENT ON COLUMN qua_scan_es_field.format IS '配置识别日期字符串';
COMMENT ON COLUMN qua_scan_es_field.ignore_above IS '超过ignore_above设置的字符串将不会被索引或存储';
COMMENT ON COLUMN qua_scan_es_field.ignore_malformed IS '允许忽略异常。不正确的字段不会被索引，但是文档中的其他字段可被正常处理';
COMMENT ON COLUMN qua_scan_es_field.index_options IS '控制向反向索引可添加哪些信息以进行搜索和高亮显示';
COMMENT ON COLUMN qua_scan_es_field.index_phrases IS '可配置允许精确短语查询更有效地运行，以牺牲更大的索引为代价';
COMMENT ON COLUMN qua_scan_es_field.index_prefixes IS '可允许对词汇前缀进行索引，从而加快前缀搜索';
COMMENT ON COLUMN qua_scan_es_field.index IS 'index选项控制是否对字段值进行索引，没有索引的字段是不可查询的';
COMMENT ON COLUMN qua_scan_es_field.meta IS '附加到字段的元数据，用于共享关于字段的元信息';
COMMENT ON COLUMN qua_scan_es_field.normalizer IS '自定义规范化器可产生单个token';
COMMENT ON COLUMN qua_scan_es_field.norms IS 'Norms存储了以后在查询时使用的各种规范化因子，以便计算文档相对于查询的得分';
COMMENT ON COLUMN qua_scan_es_field.null_value IS '允许使用指定的值替换显式的空值，这样就可以对它进行索引和搜索';
COMMENT ON COLUMN qua_scan_es_field.position_increment_gap IS '用以防止多个短语查询跨值匹配';
COMMENT ON COLUMN qua_scan_es_field.properties IS '属性可以是任何数据类型，包括对象和嵌套';
COMMENT ON COLUMN qua_scan_es_field.search_analyzer IS '查询可配置为使用在字段映射中定义的分析器';
COMMENT ON COLUMN qua_scan_es_field.similarity IS '可配置每个字段的评分算法或相似度算法';
COMMENT ON COLUMN qua_scan_es_field.store IS '配置存储此字段';
COMMENT ON COLUMN qua_scan_es_field.term_vector IS '配置分析过程产生的terms的信息';
COMMENT ON COLUMN qua_scan_es_field.field_length IS '字段长度，根据字段类型不同而不同，目前支持数值型字段';
COMMENT ON COLUMN qua_scan_es_field.field_precision IS '字段精度，仅针对浮点数值型字段';
COMMENT ON COLUMN qua_scan_es_field.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_scan_es_index.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_es_index.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_es_index.cluster_name IS '集群名称';
COMMENT ON COLUMN qua_scan_es_index.cluster_uuid IS '集群UUID';
COMMENT ON COLUMN qua_scan_es_index.index_name IS '索引名称';
COMMENT ON COLUMN qua_scan_es_index.type_name IS '类型名称';
COMMENT ON COLUMN qua_scan_es_index.index_state IS '索引状态';
COMMENT ON COLUMN qua_scan_es_index.index_settings IS '索引配置';
COMMENT ON COLUMN qua_scan_es_index.aliases IS '索引别名';
COMMENT ON COLUMN qua_scan_es_index.primary_terms IS '主分片更新版本';
COMMENT ON COLUMN qua_scan_es_index.in_sync_allocations IS '分片分配标识';
COMMENT ON COLUMN qua_scan_es_index.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_scan_hive_column.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_hive_column.table_id IS '表ID';
COMMENT ON COLUMN qua_scan_hive_column.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_hive_column.db_name IS '数据库名称';
COMMENT ON COLUMN qua_scan_hive_column.table_name IS '数据表名称';
COMMENT ON COLUMN qua_scan_hive_column.column_name IS '字段名称';
COMMENT ON COLUMN qua_scan_hive_column.type IS '字段类型';
COMMENT ON COLUMN qua_scan_hive_column.comment IS '字段描述';
COMMENT ON COLUMN qua_scan_hive_column.is_nullable IS '可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串';
COMMENT ON COLUMN qua_scan_hive_column.column_length IS '字段长度';
COMMENT ON COLUMN qua_scan_hive_column.column_precision IS '字段精度';
COMMENT ON COLUMN qua_scan_hive_column.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_scan_hive_db.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_hive_db.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_hive_db.db_name IS '数据库名称';
COMMENT ON COLUMN qua_scan_hive_db.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_scan_hive_table.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_hive_table.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_hive_table.db_id IS '扫描的数据库ID';
COMMENT ON COLUMN qua_scan_hive_table.db_name IS '数据库名称';
COMMENT ON COLUMN qua_scan_hive_table.table_name IS '数据表名称';
COMMENT ON COLUMN qua_scan_hive_table.comment IS '表描述';
COMMENT ON COLUMN qua_scan_hive_table.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_scan_list.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_list.scan_type IS '扫描类型：ES 或CH';
COMMENT ON COLUMN qua_scan_list.es_ip_port IS 'ES集群地址';
COMMENT ON COLUMN qua_scan_list.es_auth_type IS 'ES认证类型：1账密认证，2自上传文件认证，3选择文件认证';
COMMENT ON COLUMN qua_scan_list.es_user_name IS 'ES用户名';
COMMENT ON COLUMN qua_scan_list.es_user_password IS 'ES密码';
COMMENT ON COLUMN qua_scan_list.es_kbs_account IS 'ES KBS账号';
COMMENT ON COLUMN qua_scan_list.es_keytab_file_path IS 'ES keytab文件路径';
COMMENT ON COLUMN qua_scan_list.es_krb5_file_path IS 'ES krb5文件路径';
COMMENT ON COLUMN qua_scan_list.es_jaas_file_path IS 'ES jaas文件路径';
COMMENT ON COLUMN qua_scan_list.es_prop_file_path IS 'ES认证配置文件路径';
COMMENT ON COLUMN qua_scan_list.es_kbs_template_id IS 'ES 选择的模板ID';
COMMENT ON COLUMN qua_scan_list.ch_ip IS 'CH IP';
COMMENT ON COLUMN qua_scan_list.ch_port IS 'CH port';
COMMENT ON COLUMN qua_scan_list.ch_user_name IS 'CH 用户名';
COMMENT ON COLUMN qua_scan_list.ch_user_password IS 'CH 密码';
COMMENT ON COLUMN qua_scan_list.ch_is_ssl IS 'CH 是否SSL：0否，1是';
COMMENT ON COLUMN qua_scan_list.scan_state IS '扫描状态：1扫描中，2扫描完成，3扫描失败';
COMMENT ON COLUMN qua_scan_list.scan_process IS '扫描进度百分比  如：100%';
COMMENT ON COLUMN qua_scan_list.scan_fail_reason IS '扫描失败原因';
COMMENT ON COLUMN qua_scan_list.kbs_enable IS '是否开启kbs认证，0-不开启(默认)，1-开启';
COMMENT ON COLUMN qua_scan_list.jdbc_url IS 'jdbc地址，当开启kbs认证时，会包含principal';
COMMENT ON COLUMN qua_scan_mysql_column.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_mysql_column.table_id IS '表ID';
COMMENT ON COLUMN qua_scan_mysql_column.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_mysql_column.db_name IS '数据库名称';
COMMENT ON COLUMN qua_scan_mysql_column.table_name IS '数据表名称';
COMMENT ON COLUMN qua_scan_mysql_column.column_name IS '字段名称';
COMMENT ON COLUMN qua_scan_mysql_column.type IS '字段类型';
COMMENT ON COLUMN qua_scan_mysql_column.comment IS '字段描述';
COMMENT ON COLUMN qua_scan_mysql_column.column_key IS '索引 PRI-主键 UNI-唯一索引 MUL-非唯一索引';
COMMENT ON COLUMN qua_scan_mysql_column.is_nullable IS '可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串';
COMMENT ON COLUMN qua_scan_mysql_column.column_length IS '字段长度';
COMMENT ON COLUMN qua_scan_mysql_column.column_precision IS '字段精度';
COMMENT ON COLUMN qua_scan_mysql_column.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_scan_mysql_db.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_mysql_db.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_mysql_db.db_name IS '数据库名称';
COMMENT ON COLUMN qua_scan_mysql_db.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_scan_mysql_table.task_no IS '任务编号';
COMMENT ON COLUMN qua_scan_mysql_table.create_time IS '创建时间';
COMMENT ON COLUMN qua_scan_mysql_table.db_id IS '扫描的数据库ID';
COMMENT ON COLUMN qua_scan_mysql_table.db_name IS '数据库名称';
COMMENT ON COLUMN qua_scan_mysql_table.table_name IS '数据表名称';
COMMENT ON COLUMN qua_scan_mysql_table.comment IS '表描述';
COMMENT ON COLUMN qua_scan_mysql_table.engine IS '引擎类型';
COMMENT ON COLUMN qua_scan_mysql_table.verison IS '版本';
COMMENT ON COLUMN qua_scan_mysql_table.row_format IS '行格式';
COMMENT ON COLUMN qua_scan_mysql_table.table_type IS '表类型';
COMMENT ON COLUMN qua_scan_mysql_table.table_charset IS '表字符集';
COMMENT ON COLUMN qua_scan_mysql_table.total_rows IS '总行数';
COMMENT ON COLUMN qua_scan_mysql_table.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_server_used_statistic.id IS '主键id';
COMMENT ON COLUMN qua_server_used_statistic.tenant_id IS '租户id';
COMMENT ON COLUMN qua_server_used_statistic.server_type IS '服务类型';
COMMENT ON COLUMN qua_server_used_statistic.create_time IS '入库时间';
COMMENT ON COLUMN qua_server_used_statistic.create_date IS '入库日期，yyyy-MM-dd';
COMMENT ON COLUMN qua_wab_element.element_type IS '元数据类型 ：ES 或 CH';
COMMENT ON COLUMN qua_wab_element.element_name IS '元数据名称';
COMMENT ON COLUMN qua_wab_element.es_ip_port IS 'ES集群地址';
COMMENT ON COLUMN qua_wab_element.es_auth_type IS 'ES认证类型：1账密认证，2自上传文件认证，3选择文件认证';
COMMENT ON COLUMN qua_wab_element.es_user_name IS 'ES用户名';
COMMENT ON COLUMN qua_wab_element.es_user_password IS 'ES密码';
COMMENT ON COLUMN qua_wab_element.es_kbs_account IS 'ES KBS账号';
COMMENT ON COLUMN qua_wab_element.es_keytab_file_path IS 'ES keytab文件路径';
COMMENT ON COLUMN qua_wab_element.es_krb5_file_path IS 'ES krb5文件路径';
COMMENT ON COLUMN qua_wab_element.es_jaas_file_path IS 'ES jaas文件路径';
COMMENT ON COLUMN qua_wab_element.es_kbs_template_id IS 'ES 选择的模板ID';
COMMENT ON COLUMN qua_wab_element.ch_ip IS 'CH IP';
COMMENT ON COLUMN qua_wab_element.ch_port IS 'CH port';
COMMENT ON COLUMN qua_wab_element.ch_user_name IS 'CH 用户名';
COMMENT ON COLUMN qua_wab_element.ch_user_password IS 'CH 密码';
COMMENT ON COLUMN qua_wab_element.ch_is_ssl IS 'CH是否SSL: 0否，1是';
COMMENT ON COLUMN qua_wab_element.is_connect IS '是否连通，1：连通，2：不连通';
COMMENT ON COLUMN qua_wab_element.fail_connect_reason IS '失败连通原因';
COMMENT ON COLUMN qua_wab_element.execute_type IS '执行类型';
COMMENT ON COLUMN qua_wab_element.config_json IS '执行频率配置JSON';
COMMENT ON COLUMN qua_wab_element.late_scan_time IS '最近抽取时间';
COMMENT ON COLUMN qua_wab_element.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_wab_element.flag IS '是否有效，0无效，1有效，默认1';
COMMENT ON COLUMN qua_wab_element.key_tab_path IS 'keytab文件路径';
COMMENT ON COLUMN qua_wab_element.krb5_conf_path IS 'krb5conf文件路径';
COMMENT ON COLUMN qua_wab_element.jaas_conf_path IS 'jass文件路径';
COMMENT ON COLUMN qua_wab_element.kbs_enable IS '是否开启kbs认证，0-不开启(默认)，1-开启';
COMMENT ON COLUMN qua_wab_element.jdbc_url IS 'jdbc地址，当开启kbs认证时，会包含principal';
COMMENT ON COLUMN qua_web_ch_element_detail_column.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_ch_element_detail_column.db_name IS '库名称';
COMMENT ON COLUMN qua_web_ch_element_detail_column.table_name IS '表名称';
COMMENT ON COLUMN qua_web_ch_element_detail_column.column_name IS '字段名称';
COMMENT ON COLUMN qua_web_ch_element_detail_column.column_name_cn IS '字段中文名，默认为空字符串';
COMMENT ON COLUMN qua_web_ch_element_detail_column.is_sensitive IS '是否为敏感表，默认不是';
COMMENT ON COLUMN qua_web_ch_element_detail_column.is_available IS '是否可用，默认为1，可用';
COMMENT ON COLUMN qua_web_ch_element_detail_column.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_ch_element_detail_column.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_ch_element_detail_column.business_type IS '业务系统';
COMMENT ON COLUMN qua_web_ch_element_detail_column.sen_level_id IS '敏感分级ID';
COMMENT ON COLUMN qua_web_ch_element_detail_column.sen_level_name IS '敏感分级名称';
COMMENT ON COLUMN qua_web_ch_element_detail_column.sen_type_id IS '敏感分类ID';
COMMENT ON COLUMN qua_web_ch_element_detail_column.sen_type_name IS '敏感分级名称';
COMMENT ON COLUMN qua_web_ch_element_detail_column.is_required IS '是否必填，0-否，1-是';
COMMENT ON COLUMN qua_web_ch_element_detail_column.is_encrypted IS '是否加密，0-否，1-是';
COMMENT ON COLUMN qua_web_ch_element_detail_column.desensitization_id IS '脱敏规则 ID';
COMMENT ON COLUMN qua_web_ch_element_detail_db.db_name IS '库名';
COMMENT ON COLUMN qua_web_ch_element_detail_db.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_ch_element_detail_db.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_ch_element_detail_table.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_ch_element_detail_table.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_ch_element_detail_table.table_name IS '数据表名称';
COMMENT ON COLUMN qua_web_ch_element_detail_table.table_name_cn IS '表中文名';
COMMENT ON COLUMN qua_web_ch_element_detail_table.table_dscribe IS '表业务描述';
COMMENT ON COLUMN qua_web_ch_element_detail_table.table_owner IS '表业务负责人';
COMMENT ON COLUMN qua_web_ch_element_detail_table.is_sensitive IS '是否为敏感表，默认不是';
COMMENT ON COLUMN qua_web_ch_element_detail_table.is_available IS '是否可用，默认为1，可用';
COMMENT ON COLUMN qua_web_ch_element_detail_table.ext_attrs IS '自定义属性';
COMMENT ON COLUMN qua_web_ch_element_detail_table.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_ch_element_detail_table.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_ch_task_result_column.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_ch_task_result_column.table_id IS '表ID';
COMMENT ON COLUMN qua_web_ch_task_result_column.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_ch_task_result_column.create_user IS '创建人';
COMMENT ON COLUMN qua_web_ch_task_result_column.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_ch_task_result_column.table_name IS '数据表名称';
COMMENT ON COLUMN qua_web_ch_task_result_column.column_name IS '字段名称';
COMMENT ON COLUMN qua_web_ch_task_result_column.type IS '字段类型';
COMMENT ON COLUMN qua_web_ch_task_result_column.position IS '字段序号，取值从1开始';
COMMENT ON COLUMN qua_web_ch_task_result_column.default_kind IS '字段默认值类型，取值：DEFAULT、ALIAS';
COMMENT ON COLUMN qua_web_ch_task_result_column.default_expression IS '字段默认值表达式，如：now()、CAST(1, "Int32")';
COMMENT ON COLUMN qua_web_ch_task_result_column.data_compressed_bytes IS '数据压缩后字节数';
COMMENT ON COLUMN qua_web_ch_task_result_column.data_uncompressed_bytes IS '数据未压缩字节数';
COMMENT ON COLUMN qua_web_ch_task_result_column.marks_bytes IS '标记的大小';
COMMENT ON COLUMN qua_web_ch_task_result_column.comment IS '字段描述';
COMMENT ON COLUMN qua_web_ch_task_result_column.is_in_partition_key IS '是否属于分区字段';
COMMENT ON COLUMN qua_web_ch_task_result_column.is_in_sorting_key IS '是否属于排序字段';
COMMENT ON COLUMN qua_web_ch_task_result_column.is_in_primary_key IS '是否属于主键字段';
COMMENT ON COLUMN qua_web_ch_task_result_column.is_in_sampling_key IS '是否属于抽样字段';
COMMENT ON COLUMN qua_web_ch_task_result_column.compression_codec IS '压缩编码器名称';
COMMENT ON COLUMN qua_web_ch_task_result_column.is_nullable IS '可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串';
COMMENT ON COLUMN qua_web_ch_task_result_column.column_length IS '字段长度（bit），根据字段类型不同而不同，目前仅支持数值型字段';
COMMENT ON COLUMN qua_web_ch_task_result_column.column_precision IS '字段精度（bit），仅针对浮点数值型字段';
COMMENT ON COLUMN qua_web_ch_task_result_column.bussiness_type IS '业务类型';
COMMENT ON COLUMN qua_web_ch_task_result_column.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_ch_task_result_db.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_ch_task_result_db.task_id IS '任务ID';
COMMENT ON COLUMN qua_web_ch_task_result_db.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_ch_task_result_db.create_user IS '创建人';
COMMENT ON COLUMN qua_web_ch_task_result_db.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_ch_task_result_db.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_ch_task_result_table.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_ch_task_result_table.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_ch_task_result_table.create_user IS '创建人';
COMMENT ON COLUMN qua_web_ch_task_result_table.db_id IS '扫描的数据库ID';
COMMENT ON COLUMN qua_web_ch_task_result_table.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_ch_task_result_table.table_name IS '数据表名称';
COMMENT ON COLUMN qua_web_ch_task_result_table.uuid IS 'CH表系统ID';
COMMENT ON COLUMN qua_web_ch_task_result_table.engine IS '引擎类型';
COMMENT ON COLUMN qua_web_ch_task_result_table.is_temporary IS '是否临时表';
COMMENT ON COLUMN qua_web_ch_task_result_table.data_paths IS '数据地址';
COMMENT ON COLUMN qua_web_ch_task_result_table.metadata_path IS '元数据地址';
COMMENT ON COLUMN qua_web_ch_task_result_table.metadata_modification_time IS '元数据修改时间';
COMMENT ON COLUMN qua_web_ch_task_result_table.dependencies_database IS '依赖数据库';
COMMENT ON COLUMN qua_web_ch_task_result_table.dependencies_table IS '依赖数据表';
COMMENT ON COLUMN qua_web_ch_task_result_table.create_table_query IS '建表语句';
COMMENT ON COLUMN qua_web_ch_task_result_table.engine_full IS '引擎参数';
COMMENT ON COLUMN qua_web_ch_task_result_table.partition_key IS '分区字段';
COMMENT ON COLUMN qua_web_ch_task_result_table.sorting_key IS '排序字段';
COMMENT ON COLUMN qua_web_ch_task_result_table.primary_key IS '主键字段';
COMMENT ON COLUMN qua_web_ch_task_result_table.sampling_key IS '抽样字段';
COMMENT ON COLUMN qua_web_ch_task_result_table.storage_policy IS '存储策略';
COMMENT ON COLUMN qua_web_ch_task_result_table.total_rows IS '总行数';
COMMENT ON COLUMN qua_web_ch_task_result_table.total_bytes IS '总字节数';
COMMENT ON COLUMN qua_web_ch_task_result_table.lifetime_rows IS '插入总行数';
COMMENT ON COLUMN qua_web_ch_task_result_table.lifetime_bytes IS '插入总字节数';
COMMENT ON COLUMN qua_web_ch_task_result_table.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_es_element_detail_field.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_es_element_detail_field.index_name IS '索引名称';
COMMENT ON COLUMN qua_web_es_element_detail_field.field_name IS '字段名称';
COMMENT ON COLUMN qua_web_es_element_detail_field.field_name_cn IS '字段中文名，默认为空字符串';
COMMENT ON COLUMN qua_web_es_element_detail_field.is_sensitive IS '是否为敏感表，默认不是';
COMMENT ON COLUMN qua_web_es_element_detail_field.is_available IS '是否可用，默认为1，可用';
COMMENT ON COLUMN qua_web_es_element_detail_field.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_es_element_detail_field.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_es_element_detail_field.sen_level_id IS '敏感分级ID';
COMMENT ON COLUMN qua_web_es_element_detail_field.sen_level_name IS '敏感分级名称';
COMMENT ON COLUMN qua_web_es_element_detail_field.sen_type_id IS '敏感分类ID';
COMMENT ON COLUMN qua_web_es_element_detail_field.sen_type_name IS '敏感分级名称';
COMMENT ON COLUMN qua_web_es_element_detail_field.is_required IS '是否必填，0-否，1-是';
COMMENT ON COLUMN qua_web_es_element_detail_field.is_encrypted IS '是否加密，0-否，1-是';
COMMENT ON COLUMN qua_web_es_element_detail_field.desensitization_id IS '脱敏规则 ID';
COMMENT ON COLUMN qua_web_es_element_detail_index.host_address IS '节点IP';
COMMENT ON COLUMN qua_web_es_element_detail_index.port IS '端口';
COMMENT ON COLUMN qua_web_es_element_detail_index.index_name IS '索引名称';
COMMENT ON COLUMN qua_web_es_element_detail_index.index_name_cn IS '索引中文名';
COMMENT ON COLUMN qua_web_es_element_detail_index.index_dscribe IS '索引业务描述';
COMMENT ON COLUMN qua_web_es_element_detail_index.index_owner IS '索引业务负责人';
COMMENT ON COLUMN qua_web_es_element_detail_index.type_name IS '类型名称，如有多个，逗号分隔';
COMMENT ON COLUMN qua_web_es_element_detail_index.is_sensitive IS '是否为敏感，0非，1是，默认不是';
COMMENT ON COLUMN qua_web_es_element_detail_index.is_available IS '是否可用，0不可用，默认为1，可用';
COMMENT ON COLUMN qua_web_es_element_detail_index.ext_attrs IS '自定义属性';
COMMENT ON COLUMN qua_web_es_element_detail_index.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_es_element_detail_index.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_es_task_result_field.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_es_task_result_field.index_id IS '索引ID';
COMMENT ON COLUMN qua_web_es_task_result_field.index_name IS '索引名称';
COMMENT ON COLUMN qua_web_es_task_result_field.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_es_task_result_field.create_user IS '创建人';
COMMENT ON COLUMN qua_web_es_task_result_field.field_data_type IS '字段数据类型';
COMMENT ON COLUMN qua_web_es_task_result_field.analyzer IS '用于文本分析的分析器';
COMMENT ON COLUMN qua_web_es_task_result_field.boost IS '相关性得分计算参数';
COMMENT ON COLUMN qua_web_es_task_result_field.coerce IS '试图清除脏值以适应字段的数据类型';
COMMENT ON COLUMN qua_web_es_task_result_field.copy_to IS '将多个字段的值复制到组字段中，然后可以将组字段作为单个字段进行查询';
COMMENT ON COLUMN qua_web_es_task_result_field.doc_values IS '文档索引时构建的磁盘数据结构';
COMMENT ON COLUMN qua_web_es_task_result_field.dynamic IS '对包含新字段的文档进行索引时，动态地添加到文档或文档内部对象中';
COMMENT ON COLUMN qua_web_es_task_result_field.eager_global_ordinals IS '字段类型使用序号映射来存储文档值，以获得更紧凑的表示';
COMMENT ON COLUMN qua_web_es_task_result_field.enabled IS '可配置跳过对字段内容的解析和索引';
COMMENT ON COLUMN qua_web_es_task_result_field.fielddata IS '可配置文本字段在默认情况下用于聚合、排序或脚本编写';
COMMENT ON COLUMN qua_web_es_task_result_field.fields IS '以不同的方式为同一个字段建立索引通';
COMMENT ON COLUMN qua_web_es_task_result_field.format IS '配置识别日期字符串';
COMMENT ON COLUMN qua_web_es_task_result_field.ignore_above IS '超过ignore_above设置的字符串将不会被索引或存储';
COMMENT ON COLUMN qua_web_es_task_result_field.ignore_malformed IS '允许忽略异常。不正确的字段不会被索引，但是文档中的其他字段可被正常处理';
COMMENT ON COLUMN qua_web_es_task_result_field.index_options IS '控制向反向索引可添加哪些信息以进行搜索和高亮显示';
COMMENT ON COLUMN qua_web_es_task_result_field.index_phrases IS '可配置允许精确短语查询更有效地运行，以牺牲更大的索引为代价';
COMMENT ON COLUMN qua_web_es_task_result_field.index_prefixes IS '可允许对词汇前缀进行索引，从而加快前缀搜索';
COMMENT ON COLUMN qua_web_es_task_result_field.index IS 'index选项控制是否对字段值进行索引，没有索引的字段是不可查询的';
COMMENT ON COLUMN qua_web_es_task_result_field.meta IS '附加到字段的元数据，用于共享关于字段的元信息';
COMMENT ON COLUMN qua_web_es_task_result_field.normalizer IS '自定义规范化器可产生单个token';
COMMENT ON COLUMN qua_web_es_task_result_field.norms IS 'Norms存储了以后在查询时使用的各种规范化因子，以便计算文档相对于查询的得分';
COMMENT ON COLUMN qua_web_es_task_result_field.null_value IS '允许使用指定的值替换显式的空值，这样就可以对它进行索引和搜索';
COMMENT ON COLUMN qua_web_es_task_result_field.position_increment_gap IS '用以防止多个短语查询跨值匹配';
COMMENT ON COLUMN qua_web_es_task_result_field.properties IS '属性可以是任何数据类型，包括对象和嵌套';
COMMENT ON COLUMN qua_web_es_task_result_field.search_analyzer IS '查询可配置为使用在字段映射中定义的分析器';
COMMENT ON COLUMN qua_web_es_task_result_field.similarity IS '可配置每个字段的评分算法或相似度算法';
COMMENT ON COLUMN qua_web_es_task_result_field.store IS '配置存储此字段';
COMMENT ON COLUMN qua_web_es_task_result_field.term_vector IS '配置分析过程产生的terms的信息';
COMMENT ON COLUMN qua_web_es_task_result_field.field_length IS '字段长度，根据字段类型不同而不同，目前支持数值型字段';
COMMENT ON COLUMN qua_web_es_task_result_field.field_precision IS '字段精度，仅针对浮点数值型字段';
COMMENT ON COLUMN qua_web_es_task_result_field.bussiness_type IS '业务类型';
COMMENT ON COLUMN qua_web_es_task_result_field.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_es_task_result_index.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_es_task_result_index.task_id IS '任务ID';
COMMENT ON COLUMN qua_web_es_task_result_index.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_es_task_result_index.create_user IS '创建人';
COMMENT ON COLUMN qua_web_es_task_result_index.cluster_name IS '集群名';
COMMENT ON COLUMN qua_web_es_task_result_index.cluster_uuid IS '集群UUID';
COMMENT ON COLUMN qua_web_es_task_result_index.index_name IS '索引名称';
COMMENT ON COLUMN qua_web_es_task_result_index.type_name IS '类型名称';
COMMENT ON COLUMN qua_web_es_task_result_index.index_state IS '索引状态';
COMMENT ON COLUMN qua_web_es_task_result_index.index_settings IS '索引配置';
COMMENT ON COLUMN qua_web_es_task_result_index.aliases IS '索引别名';
COMMENT ON COLUMN qua_web_es_task_result_index.primary_terms IS '主分片更新版本';
COMMENT ON COLUMN qua_web_es_task_result_index.in_sync_allocations IS '分片分配标识';
COMMENT ON COLUMN qua_web_es_task_result_index.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_hive_element_detail_column.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_hive_element_detail_column.db_name IS '库名称';
COMMENT ON COLUMN qua_web_hive_element_detail_column.table_name IS '表名称';
COMMENT ON COLUMN qua_web_hive_element_detail_column.column_name IS '字段名称';
COMMENT ON COLUMN qua_web_hive_element_detail_column.column_name_cn IS '字段中文名，默认为空字符串';
COMMENT ON COLUMN qua_web_hive_element_detail_column.is_sensitive IS '是否为敏感表，默认不是';
COMMENT ON COLUMN qua_web_hive_element_detail_column.is_available IS '是否可用，默认为1，可用';
COMMENT ON COLUMN qua_web_hive_element_detail_column.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_hive_element_detail_column.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_hive_element_detail_column.business_type IS '业务系统';
COMMENT ON COLUMN qua_web_hive_element_detail_column.sen_level_id IS '敏感分级ID';
COMMENT ON COLUMN qua_web_hive_element_detail_column.sen_level_name IS '敏感分级名称';
COMMENT ON COLUMN qua_web_hive_element_detail_column.sen_type_id IS '敏感分类ID';
COMMENT ON COLUMN qua_web_hive_element_detail_column.sen_type_name IS '敏感分级名称';
COMMENT ON COLUMN qua_web_hive_element_detail_column.is_required IS '是否必填，0-否，1-是';
COMMENT ON COLUMN qua_web_hive_element_detail_column.is_encrypted IS '是否加密，0-否，1-是';
COMMENT ON COLUMN qua_web_hive_element_detail_column.desensitization_id IS '脱敏规则 ID';
COMMENT ON COLUMN qua_web_hive_element_detail_db.db_name IS '库名';
COMMENT ON COLUMN qua_web_hive_element_detail_db.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_hive_element_detail_db.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_hive_element_detail_table.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_hive_element_detail_table.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_hive_element_detail_table.table_name IS '数据表名称';
COMMENT ON COLUMN qua_web_hive_element_detail_table.table_name_cn IS '表中文名';
COMMENT ON COLUMN qua_web_hive_element_detail_table.table_dscribe IS '表业务描述';
COMMENT ON COLUMN qua_web_hive_element_detail_table.table_owner IS '表业务负责人';
COMMENT ON COLUMN qua_web_hive_element_detail_table.is_sensitive IS '是否为敏感表，默认不是';
COMMENT ON COLUMN qua_web_hive_element_detail_table.is_available IS '是否可用，默认为1，可用';
COMMENT ON COLUMN qua_web_hive_element_detail_table.ext_attrs IS '自定义属性';
COMMENT ON COLUMN qua_web_hive_element_detail_table.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_hive_element_detail_table.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_hive_task_result_column.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_hive_task_result_column.table_id IS '表ID';
COMMENT ON COLUMN qua_web_hive_task_result_column.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_hive_task_result_column.create_user IS '创建人';
COMMENT ON COLUMN qua_web_hive_task_result_column.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_hive_task_result_column.table_name IS '数据表名称';
COMMENT ON COLUMN qua_web_hive_task_result_column.column_name IS '字段名称';
COMMENT ON COLUMN qua_web_hive_task_result_column.type IS '字段类型';
COMMENT ON COLUMN qua_web_hive_task_result_column.comment IS '字段描述';
COMMENT ON COLUMN qua_web_hive_task_result_column.is_nullable IS '可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串';
COMMENT ON COLUMN qua_web_hive_task_result_column.column_length IS '字段长度';
COMMENT ON COLUMN qua_web_hive_task_result_column.column_precision IS '字段精度';
COMMENT ON COLUMN qua_web_hive_task_result_column.bussiness_type IS '业务类型';
COMMENT ON COLUMN qua_web_hive_task_result_column.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_hive_task_result_db.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_hive_task_result_db.task_id IS '任务ID';
COMMENT ON COLUMN qua_web_hive_task_result_db.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_hive_task_result_db.create_user IS '创建人';
COMMENT ON COLUMN qua_web_hive_task_result_db.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_hive_task_result_db.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_hive_task_result_table.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_hive_task_result_table.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_hive_task_result_table.create_user IS '创建人';
COMMENT ON COLUMN qua_web_hive_task_result_table.db_id IS '扫描的数据库ID';
COMMENT ON COLUMN qua_web_hive_task_result_table.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_hive_task_result_table.table_name IS '数据表名称';
COMMENT ON COLUMN qua_web_hive_task_result_table.comment IS '表描述';
COMMENT ON COLUMN qua_web_hive_task_result_table.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_job.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_job.is_map_to_job IS '是否与Job对应的，0非，1是，默认1';
COMMENT ON COLUMN qua_web_job.job_group IS '作业分组';
COMMENT ON COLUMN qua_web_job.job_name IS '作业名称';
COMMENT ON COLUMN qua_web_job.job_type IS '作业类型 1:周期，2:立即执行，3:指定时间';
COMMENT ON COLUMN qua_web_job.job_cron IS 'cron表达式';
COMMENT ON COLUMN qua_web_job.job_cron_expression IS 'cron表达式 译文';
COMMENT ON COLUMN qua_web_job.config_date_time IS '立即执行和指定时间配置的时间';
COMMENT ON COLUMN qua_web_job.job_state IS 'Job状态，1：已开始，2已暂停';
COMMENT ON COLUMN qua_web_job.late_start_time IS '最近开始时间  每个task执行时更新';
COMMENT ON COLUMN qua_web_job.late_ent_time IS '最近结束时间  每个task执行时更新';
COMMENT ON COLUMN qua_web_job.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_web_job.flag IS '是否删除：0非，1是';
COMMENT ON COLUMN qua_web_kbs_file_config.template_name IS '模板名称';
COMMENT ON COLUMN qua_web_kbs_file_config.kbs_account IS 'KBS账号';
COMMENT ON COLUMN qua_web_kbs_file_config.remark IS '备注';
COMMENT ON COLUMN qua_web_kbs_file_config.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_web_kbs_file_config.flag IS '是否有效，0无效，1有效，默认1';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.db_name IS '库名称';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.table_name IS '表名称';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.column_name IS '字段名称';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.column_name_cn IS '字段中文名，默认为空字符串';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.is_sensitive IS '是否为敏感表，默认不是';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.is_available IS '是否可用，默认为1，可用';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.business_type IS '业务系统';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.sen_level_id IS '敏感分级ID';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.sen_level_name IS '敏感分级名称';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.sen_type_id IS '敏感分类ID';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.sen_type_name IS '敏感分级名称';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.is_required IS '是否必填，0-否，1-是';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.is_encrypted IS '是否加密，0-否，1-是';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.desensitization_id IS '脱敏规则 ID';
COMMENT ON COLUMN qua_web_mysql_element_detail_db.db_name IS '库名';
COMMENT ON COLUMN qua_web_mysql_element_detail_db.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_mysql_element_detail_db.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.table_name IS '数据表名称';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.table_name_cn IS '表中文名';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.table_dscribe IS '表业务描述';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.table_owner IS '表业务负责人';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.is_sensitive IS '是否为敏感表，默认不是';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.is_available IS '是否可用，默认为1，可用';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.ext_attrs IS '自定义属性';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.first_snapshoot_version IS '最早快照版本号，新增记录时写入';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.last_snapshoot_version IS '最新快照版本号，更新记录时写入';
COMMENT ON COLUMN qua_web_mysql_task_result_column.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_mysql_task_result_column.table_id IS '表ID';
COMMENT ON COLUMN qua_web_mysql_task_result_column.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_mysql_task_result_column.create_user IS '创建人';
COMMENT ON COLUMN qua_web_mysql_task_result_column.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_mysql_task_result_column.table_name IS '数据表名称';
COMMENT ON COLUMN qua_web_mysql_task_result_column.column_name IS '字段名称';
COMMENT ON COLUMN qua_web_mysql_task_result_column.type IS '字段类型';
COMMENT ON COLUMN qua_web_mysql_task_result_column.comment IS '字段描述';
COMMENT ON COLUMN qua_web_mysql_task_result_column.column_key IS '索引 PRI-主键 UNI-唯一索引 MUL-非唯一索引';
COMMENT ON COLUMN qua_web_mysql_task_result_column.is_nullable IS '可否为空，1为可空，过字段类型（type）推断是否包含Nullable字符串';
COMMENT ON COLUMN qua_web_mysql_task_result_column.column_length IS '字段长度';
COMMENT ON COLUMN qua_web_mysql_task_result_column.column_precision IS '字段精度';
COMMENT ON COLUMN qua_web_mysql_task_result_column.bussiness_type IS '业务类型';
COMMENT ON COLUMN qua_web_mysql_task_result_column.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_mysql_task_result_db.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_mysql_task_result_db.task_id IS '任务ID';
COMMENT ON COLUMN qua_web_mysql_task_result_db.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_mysql_task_result_db.create_user IS '创建人';
COMMENT ON COLUMN qua_web_mysql_task_result_db.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_mysql_task_result_db.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_mysql_task_result_table.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_mysql_task_result_table.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_mysql_task_result_table.create_user IS '创建人';
COMMENT ON COLUMN qua_web_mysql_task_result_table.db_id IS '扫描的数据库ID';
COMMENT ON COLUMN qua_web_mysql_task_result_table.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_mysql_task_result_table.table_name IS '数据表名称';
COMMENT ON COLUMN qua_web_mysql_task_result_table.comment IS '表描述';
COMMENT ON COLUMN qua_web_mysql_task_result_table.engine IS '引擎类型';
COMMENT ON COLUMN qua_web_mysql_task_result_table.verison IS '版本';
COMMENT ON COLUMN qua_web_mysql_task_result_table.row_format IS '行格式';
COMMENT ON COLUMN qua_web_mysql_task_result_table.table_type IS '表类型';
COMMENT ON COLUMN qua_web_mysql_task_result_table.table_charset IS '表字符集';
COMMENT ON COLUMN qua_web_mysql_task_result_table.total_rows IS '总行数';
COMMENT ON COLUMN qua_web_mysql_task_result_table.snapshoot_version IS '快照版本';
COMMENT ON COLUMN qua_web_task.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_task.job_id IS 'jobId';
COMMENT ON COLUMN qua_web_task.task_no IS '任务编号';
COMMENT ON COLUMN qua_web_task.task_progress IS '任务进度: 10%';
COMMENT ON COLUMN qua_web_task.check_num IS '获取结果，检查次数，超过三次为异常';
COMMENT ON COLUMN qua_web_task.status IS '任务状态：1执行中，2已完成，有差异，保存扫描结果，3已完成，无差异，不保存扫描结果 4执行异常';
COMMENT ON COLUMN qua_web_task.result IS '执行成功或失败的结果';
COMMENT ON COLUMN qua_web_task.snapshoot_version IS '扫描版本';
COMMENT ON COLUMN qua_web_user_operate_log.tenant_id IS '租户ID';
COMMENT ON COLUMN qua_web_user_operate_log.user_name IS '用户名';
COMMENT ON COLUMN qua_web_user_operate_log.request_ip IS '登录IP';
COMMENT ON COLUMN qua_web_user_operate_log.operate_model IS '操作模块';
COMMENT ON COLUMN qua_web_user_operate_log.operate_type IS '操作类型';
COMMENT ON COLUMN qua_web_user_operate_log.operate_desc IS '操作描述';
COMMENT ON COLUMN qua_web_user_operate_log.result IS '成功或失败';
COMMENT ON COLUMN sys_db_version_upgrade.id IS ';';
COMMENT ON COLUMN sys_db_version_upgrade.version IS '版本描述';
COMMENT ON COLUMN sys_db_version_upgrade.module_name IS '模块名称';
COMMENT ON COLUMN sys_db_version_upgrade.description IS '版本描述';
COMMENT ON COLUMN sys_db_version_upgrade.content IS '执行脚本内容';
COMMENT ON COLUMN sys_db_version_upgrade.error_content IS '执行失败的脚本内容';
COMMENT ON COLUMN sys_db_version_upgrade.version_code IS '版本号+模块名的md5值作为防冲突依据';
COMMENT ON COLUMN sys_db_version_upgrade.status IS '成功=1; 0=未执行  2=部分成功、9=失败';
COMMENT ON COLUMN sys_db_version_upgrade.create_time IS '执行时间';
COMMENT ON COLUMN sys_db_version_upgrade.success_content IS '执行成功的脚本';
COMMENT ON COLUMN sys_db_version_upgrade.error_msg IS '错误信息';
COMMENT ON COLUMN sys_db_version_upgrade.is_atomic IS '是否原子执行';
COMMENT ON COLUMN sys_db_version_upgrade.exclude_content IS '被排除的脚本内容';
COMMENT ON COLUMN tb_cluster.manufacturer_type IS '厂商类型，0 开源版，1 华为版 2 亚信版';
COMMENT ON COLUMN tb_cluster.cluster_status IS '集群状态 默认为白名单，1为白名单，0为黑名单';
COMMENT ON COLUMN tb_cluster.key_tab_path IS 'keytab文件路径';
COMMENT ON COLUMN tb_cluster.krb5_conf_path IS 'krb5conf文件路径';
COMMENT ON COLUMN tb_cluster.jaas_conf_path IS 'jass文件路径';
COMMENT ON COLUMN tb_cluster.kbs_account IS 'kbs账号';
COMMENT ON COLUMN tb_cluster.enable_kbs IS '是否开启kerberos认证 0-关闭 1-开启';
COMMENT ON COLUMN tb_cluster.jdbc_url IS 'jdbc地址，当开启kbs认证时，会包含principal';
COMMENT ON COLUMN tb_cluster_kafka_topic.topic IS 'topic名称';
COMMENT ON COLUMN tb_cluster_kafka_topic.tenant_name IS '所属租户';
COMMENT ON COLUMN tb_cluster_kafka_topic.PARTITIONS IS '分区数';
COMMENT ON COLUMN tb_cluster_kafka_topic.brokers IS '节点数';
COMMENT ON COLUMN tb_cluster_kafka_topic.is_deleted IS '是否删除，0否1是';
COMMENT ON COLUMN tb_dic.unique_check_code IS '页面复选框互斥编码';
COMMENT ON COLUMN tb_dic.hidden IS '是否隐藏，0：不隐藏，1：隐藏';
COMMENT ON COLUMN tb_script_history.tenant_id IS '租户id';
COMMENT ON COLUMN tb_script_history.cluster_id IS '集群id';
COMMENT ON COLUMN tb_script_history.del_flag IS '删除标识';
COMMENT ON COLUMN tb_script_history.script_file IS '脚本文件';
COMMENT ON COLUMN tb_script_history.script_type IS '文件类型 template sql';
COMMENT ON COLUMN tb_script_history.db_type IS '数据库类型 mysql clickhouse';
COMMENT ON COLUMN tb_script_history.create_time IS '创建时间';
COMMENT ON COLUMN tb_script_history.create_user IS '创建人';
COMMENT ON COLUMN tb_script_history.update_time IS '更新时间';
COMMENT ON COLUMN tb_script_history.update_user IS '更新人';
COMMENT ON COLUMN tb_script_history.error_msg IS 'SQL执行错误信息';
COMMENT ON COLUMN tb_statistics_cluster_instance_disk_sync.ID IS '主键id';
COMMENT ON COLUMN tb_statistics_cluster_instance_disk_sync.TENANT_ID IS '租户id';
COMMENT ON COLUMN tb_statistics_cluster_instance_disk_sync.CLUSTER_ID IS '集群id';
COMMENT ON COLUMN tb_statistics_cluster_instance_disk_sync.CLUSTER_TYPE IS '集群类型';
COMMENT ON COLUMN tb_statistics_cluster_instance_disk_sync.INSTANCE IS '实例';
COMMENT ON COLUMN tb_statistics_cluster_instance_disk_sync.used IS '使用量，单位M';
COMMENT ON COLUMN tb_statistics_cluster_instance_disk_sync.create_time IS '入库时间';
COMMENT ON COLUMN tb_statistics_cluster_instance_disk_sync.free IS '剩余空间，单位M';
COMMENT ON COLUMN tb_statistics_disk.resource_type IS '资源类型，clickhouse，mysql，hive，elasticsearch';
COMMENT ON COLUMN tb_statistics_disk.used IS '已用，单位m';
COMMENT ON COLUMN tb_statistics_disk.free IS '空闲，单位m';
COMMENT ON COLUMN tb_statistics_disk.create_time IS '入库时间';
COMMENT ON COLUMN tb_statistics_disk.flag IS '1有效，0无效';
COMMENT ON COLUMN tb_statistics_disk_sync.ip IS '所属服务器IP';
COMMENT ON COLUMN tb_statistics_disk_sync.file_system IS '文件系统';
COMMENT ON COLUMN tb_statistics_disk_sync.total IS '总量';
COMMENT ON COLUMN tb_statistics_disk_sync.used IS '已用';
COMMENT ON COLUMN tb_statistics_disk_sync.free IS '空闲';
COMMENT ON COLUMN tb_statistics_disk_sync.used_rate IS '使用率';
COMMENT ON COLUMN tb_statistics_disk_sync.mount_point IS '挂载点';
COMMENT ON COLUMN tb_statistics_disk_sync.create_time IS '入库时间';
COMMENT ON COLUMN tb_statistics_disk_sync.flag IS '1有效，0无效';
COMMENT ON COLUMN tb_statistics_disk_tenant.tenant_id IS '租户id';
COMMENT ON COLUMN tb_statistics_disk_tenant.tenant_name IS '租户名称';
COMMENT ON COLUMN tb_statistics_disk_tenant.clickhouse_used IS '已用，单位m';
COMMENT ON COLUMN tb_statistics_disk_tenant.clickhouse_free IS '空闲，单位m';
COMMENT ON COLUMN tb_statistics_disk_tenant.mysql_used IS '已用，单位m';
COMMENT ON COLUMN tb_statistics_disk_tenant.mysql_free IS '空闲，单位m';
COMMENT ON COLUMN tb_statistics_disk_tenant.hdfs_used IS '已用，单位m';
COMMENT ON COLUMN tb_statistics_disk_tenant.hdfs_free IS '空闲，单位m';
COMMENT ON COLUMN tb_statistics_disk_tenant.elasticsearch_used IS '已用，单位m';
COMMENT ON COLUMN tb_statistics_disk_tenant.elasticsearch_free IS '空闲，单位m';
COMMENT ON COLUMN tb_statistics_disk_tenant.create_time IS '入库时间';
COMMENT ON COLUMN tb_statistics_disk_tenant.flag IS '1有效，0无效';
COMMENT ON COLUMN tb_statistics_etl_data.etl_name IS '采集日志名称';
COMMENT ON COLUMN tb_statistics_etl_data.etl_count IS '采集数量';
COMMENT ON COLUMN tb_statistics_etl_data.create_time IS '入库时间';
COMMENT ON COLUMN tb_statistics_etl_data.flag IS '1有效，0无效';
COMMENT ON COLUMN tb_statistics_etl_task.etl_type IS '采集类型：信安，网安，数安';
COMMENT ON COLUMN tb_statistics_etl_task.total IS '总量';
COMMENT ON COLUMN tb_statistics_etl_task.create_time IS '入库时间';
COMMENT ON COLUMN tb_statistics_etl_task.flag IS '1有效，0无效';
COMMENT ON COLUMN tb_statistics_etl_task_day.day IS '日期';
COMMENT ON COLUMN tb_statistics_etl_task_day.task_count IS '采集任务数量';
COMMENT ON COLUMN tb_statistics_etl_task_day.create_time IS '入库时间';
COMMENT ON COLUMN tb_statistics_etl_task_day.flag IS '1有效，0无效';
COMMENT ON COLUMN tb_statistics_hdfs_sync.ip IS '所属服务器IP';
COMMENT ON COLUMN tb_statistics_hdfs_sync.dir IS '文件系统目录';
COMMENT ON COLUMN tb_statistics_hdfs_sync.size IS '大小，单位b';
COMMENT ON COLUMN tb_statistics_hdfs_sync.create_time IS '入库时间';
COMMENT ON COLUMN tb_statistics_hdfs_sync.flag IS '1有效，0无效';
COMMENT ON COLUMN tb_statistics_subscription.tenant_id IS '租户id';
COMMENT ON COLUMN tb_statistics_subscription.subscription_service IS '订阅服务';
COMMENT ON COLUMN tb_statistics_subscription.create_time IS '入库时间';
COMMENT ON COLUMN tb_statistics_subscription.flag IS '1有效，0无效';
COMMENT ON COLUMN tb_tenant.TENANT_CODE IS '租户编码';
COMMENT ON COLUMN tb_tenant.TENANT_NAME IS '租户名称';
COMMENT ON COLUMN tb_tenant.ACCOUNT_TYPE IS '租户用户类型';
COMMENT ON COLUMN tb_tenant.CONTACT_MOBILE IS '联系电话';
COMMENT ON COLUMN tb_tenant.DEL_FLAG IS '删除标识';
COMMENT ON COLUMN tb_tenant.CONTACT_AREA IS '关联区域';
COMMENT ON COLUMN tb_tenant.FUNCTIONS IS '功能配置';
COMMENT ON COLUMN tb_tenant.RESOURCE_STATUS IS '租户资源状态 0-未分配 1-分配中 2-分配成功 3-分配失败';
COMMENT ON COLUMN tb_tenant.ACCOUNT_NAME IS '用户账户';
COMMENT ON COLUMN tb_tenant.ACCOUNT_PWD IS '用户密码';
COMMENT ON COLUMN tb_tenant.FA_EXTEND_INFO IS '4A单点扩展信息';
COMMENT ON COLUMN tb_tenant_cluster.ID IS '主键id';
COMMENT ON COLUMN tb_tenant_cluster.TENANT_ID IS '租户id、';
COMMENT ON COLUMN tb_tenant_cluster.CLUSTER_ID IS '集群id';
COMMENT ON COLUMN tb_tenant_cluster.CLUSTER_TYPE IS '集群类型';
COMMENT ON COLUMN tb_tenant_cluster.INSTANCE IS '实例';
COMMENT ON COLUMN tb_tenant_cluster.TOPIC IS 'topic';
COMMENT ON COLUMN tb_tenant_cluster.QUOTA IS '配额';
COMMENT ON COLUMN tb_tenant_cluster.SHARDS IS '分片';
COMMENT ON COLUMN tb_tenant_cluster.REPLICAS IS '副本';
COMMENT ON COLUMN ueba_dictionary.id IS '主键';
COMMENT ON COLUMN ueba_dictionary.key_code IS '字典编码';
COMMENT ON COLUMN ueba_dictionary.value IS '字典值';
COMMENT ON COLUMN ueba_dictionary.enable IS '是否可用 (0-不可用 1-可用)';
COMMENT ON COLUMN ueba_dictionary.remark IS '备注';
COMMENT ON COLUMN ueba_dictionary.sortno IS '排序';
COMMENT ON COLUMN ueba_dictionary.type IS '字典类型';
COMMENT ON COLUMN ums_sys_auth_config.id IS '登录配置标识';
COMMENT ON COLUMN ums_sys_auth_config.auth_type IS '认证类型, 0-原生体系，1-oauth2，2-4A';
COMMENT ON COLUMN ums_sys_auth_config.enable_status IS '启用状态，0-未启用，1-启用';
COMMENT ON COLUMN ums_sys_auth_config.default_status IS '默认状态，0-非默认，1-默认';
COMMENT ON COLUMN ums_sys_auth_config.display_name IS '显示名称';
COMMENT ON COLUMN ums_sys_auth_config.sync_status IS '同步状态';
COMMENT ON COLUMN ums_sys_auth_config.enable_create_user IS '是否创建新用户，0-否，1-是';
COMMENT ON COLUMN ums_sys_auth_config.role_id IS '用户角色';
COMMENT ON COLUMN ums_sys_auth_config.grant_type IS '授权类型，0-授权式，1-隐藏式';
COMMENT ON COLUMN ums_sys_auth_config.oauth_base_field_info IS '基础字段信息';
COMMENT ON COLUMN ums_sys_auth_config.oauth_code_request_url IS '请求code地址';
COMMENT ON COLUMN ums_sys_auth_config.oauth_code_request_way IS '请求code方式，get/post';
COMMENT ON COLUMN ums_sys_auth_config.oauth_code_resp_field IS '响应code字段';
COMMENT ON COLUMN ums_sys_auth_config.oauth_code_field_info IS '请求code参数信息';
COMMENT ON COLUMN ums_sys_auth_config.oauth_token_request_url IS '请求token地址';
COMMENT ON COLUMN ums_sys_auth_config.oauth_token_request_way IS '请求token方式，get/post';
COMMENT ON COLUMN ums_sys_auth_config.oauth_token_resp_field IS '响应token字段';
COMMENT ON COLUMN ums_sys_auth_config.oauth_token_resp_format IS '响应token的格式，json/xml';
COMMENT ON COLUMN ums_sys_auth_config.oauth_token_field_info IS '请求token参数信息';
COMMENT ON COLUMN ums_sys_auth_config.oauth_user_request_url IS '请求用户地址';
COMMENT ON COLUMN ums_sys_auth_config.oauth_user_request_way IS '请求user方式，get/post';
COMMENT ON COLUMN ums_sys_auth_config.oauth_user_resp_field IS '响应user字段';
COMMENT ON COLUMN ums_sys_auth_config.oauth_user_resp_format IS '响应user的格式，json/xml';
COMMENT ON COLUMN ums_sys_auth_config.oauth_user_field_info IS '请求user参数信息';
COMMENT ON COLUMN ums_sys_auth_config.fa_app_field IS '应用标识';
COMMENT ON COLUMN ums_sys_auth_config.fa_login_url IS '登录地址';
COMMENT ON COLUMN ums_sys_auth_config.fa_auth_url IS '认证地址';
COMMENT ON COLUMN ums_sys_auth_config.fa_user_resp_field IS '响应用户字段';
COMMENT ON COLUMN ums_sys_auth_config.fa_request_protocol IS '请求协议';
COMMENT ON COLUMN ums_sys_auth_config.fa_method_name IS 'webservice方法名';
COMMENT ON COLUMN ums_sys_auth_config.fa_request_way IS 'http请求方式，get/post';
COMMENT ON COLUMN ums_sys_auth_config.fa_login_field_info IS '登录参数信息';
COMMENT ON COLUMN ums_sys_auth_config.fa_check_field_info IS '认证参数信息';
COMMENT ON COLUMN ums_sys_auth_config.create_user IS '创建人';
COMMENT ON COLUMN ums_sys_auth_config.create_time IS '创建时间';
COMMENT ON COLUMN ums_sys_auth_config.update_user IS '更新人';
COMMENT ON COLUMN ums_sys_auth_config.update_time IS '更新时间';
COMMENT ON COLUMN ums_sys_auth_config.fa_vendor IS '4A厂商 默认亚信安全';
COMMENT ON COLUMN ums_sys_datasource_config.moudle_name IS '配置模块 dashboard-仪表盘  schedule-模型调度';
COMMENT ON COLUMN ums_sys_datasource_config.datasource_type IS '数据源类型';
COMMENT ON COLUMN ums_sys_datasource_config.ip IS 'IP地址';
COMMENT ON COLUMN ums_sys_datasource_config.db_name IS '数据库名';
COMMENT ON COLUMN ums_sys_datasource_config.port IS '端口';
COMMENT ON COLUMN ums_sys_datasource_config.user_name IS '用户名';
COMMENT ON COLUMN ums_sys_datasource_config.password IS '密码';
COMMENT ON COLUMN ums_sys_datasource_config.show_name IS '展示名';
COMMENT ON COLUMN ums_sys_datasource_config.time_out IS '超时时间';
COMMENT ON COLUMN ums_sys_datasource_config.encrypt IS '是否加密 1-加密 0-不加密';
COMMENT ON COLUMN ums_sys_datasource_config.status IS '1-正常 0-删除';
COMMENT ON COLUMN ums_sys_datasource_config.copy_cnt IS '复制次数';
COMMENT ON COLUMN ums_sys_datasource_config.db_url IS '数据库连接字符串';
COMMENT ON COLUMN ums_sys_datasource_config.create_user IS '创建账号';
COMMENT ON COLUMN ums_sys_datasource_config.create_time IS '创建时间';
COMMENT ON COLUMN ums_sys_datasource_config.update_user IS '更新账号';
COMMENT ON COLUMN ums_sys_datasource_config.update_time IS '更新时间';
COMMENT ON COLUMN ums_sys_license.sn_data IS '机器码';
COMMENT ON COLUMN ums_sys_license.sn_ip IS '机器码所属IP';
COMMENT ON COLUMN ums_sys_log.user_name IS '用户名';
COMMENT ON COLUMN ums_sys_log.real_name IS '真实姓名';
COMMENT ON COLUMN ums_sys_log.login_ip IS '登录ip';
COMMENT ON COLUMN ums_sys_log.user_agent IS 'UA';
COMMENT ON COLUMN ums_sys_log.request_path IS '请求路径';
COMMENT ON COLUMN ums_sys_log.log_name IS '操作名称';
COMMENT ON COLUMN ums_sys_log.log_result IS '操作结果';
COMMENT ON COLUMN ums_sys_log.opt_type IS '操作类型';
COMMENT ON COLUMN ums_sys_log.opt_module IS '操作模块';
COMMENT ON COLUMN ums_sys_log.http_method IS '请求方式';
COMMENT ON COLUMN ums_sys_log.create_time IS '创建时间';
COMMENT ON COLUMN ums_sys_menus.id IS 'ID';
COMMENT ON COLUMN ums_sys_menus.menu_type IS '菜单类型(builtIn-内置菜单 custom-自定义菜单)';
COMMENT ON COLUMN ums_sys_menus.menu_property IS '菜单属性 0-不允许有子菜单 1-允许有子菜单';
COMMENT ON COLUMN ums_sys_menus.menu_level IS '菜单层级';
COMMENT ON COLUMN ums_sys_menus.root_parent IS '菜单对应的一级菜单';
COMMENT ON COLUMN ums_sys_menus.menu_name IS '菜单名称';
COMMENT ON COLUMN ums_sys_menus.menu_code IS '菜单唯一标识';
COMMENT ON COLUMN ums_sys_menus.menu_path IS '菜单路径';
COMMENT ON COLUMN ums_sys_menus.hidden IS '是否隐藏';
COMMENT ON COLUMN ums_sys_menus.parent_name IS '父级菜单';
COMMENT ON COLUMN ums_sys_menus.status IS '状态（0 表示禁用，1表示启用）';
COMMENT ON COLUMN ums_sys_menus.menu_order IS '菜单顺序';
COMMENT ON COLUMN ums_sys_menus.default_order IS '默认顺序';
COMMENT ON COLUMN ums_sys_menus.default_name IS '默认名称';
COMMENT ON COLUMN ums_sys_menus.default_status IS '默认状态';
COMMENT ON COLUMN ums_sys_menus.default_parent IS '默认父级';
COMMENT ON COLUMN ums_sys_menus.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_menus.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_menus.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_menus.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_menus.menu_category IS '菜单类别 0-菜单 1-按钮';
COMMENT ON COLUMN ums_sys_menus.new_open_window IS '是否新开窗口(0-否 1-是)';
COMMENT ON COLUMN ums_sys_role.role_id IS '角色编号';
COMMENT ON COLUMN ums_sys_role.role_describe IS '角色描述';
COMMENT ON COLUMN ums_sys_role.builtin IS '是否预置 0:预置 1:未预置';
COMMENT ON COLUMN ums_sys_role.del_flag IS '删除标识 0:未删除 1:已删除';
COMMENT ON COLUMN ums_sys_role.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_role.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_role.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_role.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_role.is_admin IS '是否超级管理员';
COMMENT ON COLUMN ums_sys_user.user_id IS '用户编号';
COMMENT ON COLUMN ums_sys_user.dept_id IS '部门编号';
COMMENT ON COLUMN ums_sys_user.department_id IS '部门id';
COMMENT ON COLUMN ums_sys_user.password IS '密码';
COMMENT ON COLUMN ums_sys_user.password_old IS '旧密码';
COMMENT ON COLUMN ums_sys_user.period_from IS '账号使用开始日';
COMMENT ON COLUMN ums_sys_user.period_to IS '账号使用结束日';
COMMENT ON COLUMN ums_sys_user.status IS '状态 0:正常 1:锁定';
COMMENT ON COLUMN ums_sys_user.code IS '员工工号';
COMMENT ON COLUMN ums_sys_user.sex IS '员工性别 0:男 1:女';
COMMENT ON COLUMN ums_sys_user.email IS '电子邮箱';
COMMENT ON COLUMN ums_sys_user.leader IS '直属领导';
COMMENT ON COLUMN ums_sys_user.login_voucher IS '单点登录凭证';
COMMENT ON COLUMN ums_sys_user.fail_count IS '失败次数';
COMMENT ON COLUMN ums_sys_user.lock_date IS '锁定日期';
COMMENT ON COLUMN ums_sys_user.final_login_ip IS '最后登录ip';
COMMENT ON COLUMN ums_sys_user.final_login_date IS '最后登录时间';
COMMENT ON COLUMN ums_sys_user.builtin IS '是否预置 0：预置 1:未预置';
COMMENT ON COLUMN ums_sys_user.remark IS '备注';
COMMENT ON COLUMN ums_sys_user.del_flag IS '删除标识 0:未删除 1:已删除';
COMMENT ON COLUMN ums_sys_user.tenant_id IS '租户ID';
COMMENT ON COLUMN ums_sys_user.queue IS '队列名称';
COMMENT ON COLUMN ums_sys_user.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_user.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_user.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_user.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_user.social_account IS '社交账号';
COMMENT ON COLUMN ums_sys_user.first_login_fail_time IS '连续登陆失败时第一次登录时间';
COMMENT ON COLUMN ums_sys_user.is_first_login IS '是否第一次登录';
COMMENT ON COLUMN ums_sys_user.is_need_update_password IS '第一次登录时是否需要修改密码';
COMMENT ON COLUMN ums_sys_user.default_router_id IS '默认应用id';
COMMENT ON COLUMN ums_sys_user.default_router_name IS '默认应用路由';
COMMENT ON COLUMN ums_sys_user.mobile IS '手机号码';
COMMENT ON COLUMN ums_sys_user.dept_name IS '部门名称';
COMMENT ON COLUMN ums_sys_user.default_dashboard IS '默认仪表盘id';
COMMENT ON COLUMN ums_sys_user.data_limit_extend_role_id IS '限制继承的角色id';
COMMENT ON COLUMN ums_sys_user_4a.user_id IS '用户编号';
COMMENT ON COLUMN ums_sys_user_4a.dept_id IS '部门编号';
COMMENT ON COLUMN ums_sys_user_4a.password IS '密码';
COMMENT ON COLUMN ums_sys_user_4a.password_old IS '旧密码';
COMMENT ON COLUMN ums_sys_user_4a.period_from IS '账号使用开始日';
COMMENT ON COLUMN ums_sys_user_4a.period_to IS '账号使用结束日';
COMMENT ON COLUMN ums_sys_user_4a.status IS '状态 0:正常 1:锁定';
COMMENT ON COLUMN ums_sys_user_4a.code IS '员工工号';
COMMENT ON COLUMN ums_sys_user_4a.sex IS '员工性别 0:男 1:女';
COMMENT ON COLUMN ums_sys_user_4a.email IS '电子邮箱';
COMMENT ON COLUMN ums_sys_user_4a.leader IS '直属领导';
COMMENT ON COLUMN ums_sys_user_4a.login_voucher IS '单点登录凭证';
COMMENT ON COLUMN ums_sys_user_4a.fail_count IS '失败次数';
COMMENT ON COLUMN ums_sys_user_4a.lock_date IS '锁定日期';
COMMENT ON COLUMN ums_sys_user_4a.final_login_ip IS '最后登录ip';
COMMENT ON COLUMN ums_sys_user_4a.final_login_date IS '最后登录时间';
COMMENT ON COLUMN ums_sys_user_4a.builtin IS '是否预置 0：预置 1:未预置';
COMMENT ON COLUMN ums_sys_user_4a.remark IS '备注';
COMMENT ON COLUMN ums_sys_user_4a.del_flag IS '删除标识 0:未删除 1:已删除';
COMMENT ON COLUMN ums_sys_user_4a.tenant_id IS '租户ID';
COMMENT ON COLUMN ums_sys_user_4a.queue IS '队列名称';
COMMENT ON COLUMN ums_sys_user_4a.create_user IS '创建者';
COMMENT ON COLUMN ums_sys_user_4a.create_date IS '创建日期';
COMMENT ON COLUMN ums_sys_user_4a.update_user IS '更新者';
COMMENT ON COLUMN ums_sys_user_4a.update_date IS '更新日期';
COMMENT ON COLUMN ums_sys_user_4a.social_account IS '社交账号';
COMMENT ON COLUMN ums_sys_user_4a.first_login_fail_time IS '连续登陆失败时第一次登录时间';
COMMENT ON COLUMN ums_sys_user_4a.is_first_login IS '是否第一次登录';
COMMENT ON COLUMN ums_sys_user_4a.is_need_update_password IS '第一次登录时是否需要修改密码';
COMMENT ON COLUMN ums_sys_user_4a.default_router_id IS '默认应用id';
COMMENT ON COLUMN ums_sys_user_4a.default_router_name IS '默认应用路由';
COMMENT ON COLUMN ums_sys_user_4a.default_dashboard IS '默认仪表盘id';
COMMENT ON COLUMN ums_sys_user_4a.mobile IS '手机号码';
COMMENT ON COLUMN ums_sys_user_4a.dept_name IS '部门名称';
COMMENT ON COLUMN ums_sys_user_4a.allocate_status IS '是否分配租户，0未分配，1已分配';
COMMENT ON COLUMN ums_sys_user_4a.is_super_admin IS '是否超级管理员，1是0否';
COMMENT ON COLUMN zeppelin_interpreter.tenant_id IS '租户id';
COMMENT ON COLUMN zeppelin_interpreter.interpreter_remote_id IS 'interpreter 服务器上id';
COMMENT ON COLUMN zeppelin_interpreter.interpreter_alias IS 'interpreter别名，用于页面展示';
COMMENT ON COLUMN zeppelin_interpreter.create_time IS '创建时间';
COMMENT ON COLUMN zeppelin_interpreter.create_user IS '创建人';
COMMENT ON COLUMN zeppelin_interpreter.update_time IS '更新时间';
COMMENT ON COLUMN zeppelin_interpreter.update_user IS '更新人';
COMMENT ON COLUMN zeppelin_interpreter.interpreter_type IS '类型';
COMMENT ON COLUMN zeppelin_notebook.tenant_id IS '租户id';
COMMENT ON COLUMN zeppelin_notebook.note_remote_id IS 'notebook 服务器上id';
COMMENT ON COLUMN zeppelin_notebook.note_alias IS 'notebook别名，用于页面展示';
COMMENT ON COLUMN zeppelin_notebook.create_time IS '创建时间';
COMMENT ON COLUMN zeppelin_notebook.create_user IS '创建人';
COMMENT ON COLUMN zeppelin_notebook.update_time IS '更新时间';
COMMENT ON COLUMN zeppelin_notebook.update_user IS '更新人';
COMMENT ON COLUMN zeppelin_notebook.interpreter_id IS 'zeppelin_interpreter 主键';


CREATE INDEX IDX_TIME_IDX ON api_invoke_log(api_id,client_id,invoke_time);
CREATE INDEX QUA_DYNC_RESULT_QUA_DYNC_THRESHOLD_ID_FK_IDX ON qua_dync_result(dync_id);
CREATE INDEX QUA_SCAN_CH_COLUMN_TABLE_ID_FK_IDX ON qua_scan_ch_column(table_id);
CREATE INDEX QUA_SCAN_CH_TABLE_DB_ID_FK_IDX ON qua_scan_ch_table(db_id);
CREATE INDEX QUA_SCAN_ES_FIELD_INDEX_ID_FK_IDX ON qua_scan_es_field(index_id);
CREATE INDEX QUA_SCAN_HIVE_COLUMN_TABLE_ID_FK_IDX ON qua_scan_hive_column(table_id);
CREATE INDEX QUA_SCAN_HIVE_TABLE_DB_ID_FK_IDX ON qua_scan_hive_table(db_id);
CREATE INDEX QUA_SCAN_MYSQL_COLUMN_TABLE_ID_FK_IDX ON qua_scan_mysql_column(table_id);
CREATE INDEX QUA_SCAN_MYSQL_TABLE_DB_ID_FK_IDX ON qua_scan_mysql_table(db_id);
CREATE INDEX IDX_6_IDX ON qua_web_ch_element_detail_column(element_id,db_name,table_name,column_name);
CREATE INDEX CH_ELEMENT_ID_FK_IDX ON qua_web_ch_element_detail_db(element_id);
CREATE INDEX IDX_8_IDX ON qua_web_ch_element_detail_table(element_id,db_name,table_name);
CREATE INDEX IDX_5_IDX ON qua_web_ch_task_result_column(element_id,db_name,table_name,column_name,snapshoot_version);
CREATE INDEX QUA_WEB_CH_TASK_RESULT_COLUMN_TABLE_ID_FK_IDX ON qua_web_ch_task_result_column(table_id);
CREATE INDEX CH_TASK_ID_FK_IDX ON qua_web_ch_task_result_db(task_id);
CREATE INDEX IDX_9_IDX ON qua_web_ch_task_result_db(element_id,db_name,snapshoot_version);
CREATE INDEX IDX_7_IDX ON qua_web_ch_task_result_table(element_id,db_name,table_name,snapshoot_version);
CREATE INDEX QUA_WEB_CH_TASK_RESULT_TABLE_DB_ID_FK_IDX ON qua_web_ch_task_result_table(db_id);
CREATE INDEX IDX_2_IDX ON qua_web_es_element_detail_field(index_name,field_name);
CREATE INDEX IDX_ES_DETAIL_FIELD_ELE_VER_IDX ON qua_web_es_element_detail_field(element_id);
CREATE INDEX DETAIL_INDEX_IDX ON qua_web_es_element_detail_index(element_id,index_name);
CREATE INDEX IDX_4_IDX ON qua_web_es_element_detail_index(index_name);
CREATE INDEX IDX_ES_DETAIL_INDEX_ELE_IDX ON qua_web_es_element_detail_index(element_id);
CREATE INDEX ES_INDEX_ID_FK_IDX ON qua_web_es_task_result_field(index_id);
CREATE INDEX IDX_1_IDX ON qua_web_es_task_result_field(index_name,field_name,index_id);
CREATE INDEX IDX_ES_TASK_FIELD_ELE_VER_IDX ON qua_web_es_task_result_field(element_id,snapshoot_version);
CREATE INDEX ES_TASK_ID_FK_IDX ON qua_web_es_task_result_index(task_id);
CREATE INDEX IDX_3_IDX ON qua_web_es_task_result_index(index_name);
CREATE INDEX IDX_ES_TASK_INDEX_ELE_VER_TASKID_IDX ON qua_web_es_task_result_index(element_id,snapshoot_version,task_id);
CREATE INDEX IDX_6_1_IDX ON qua_web_hive_element_detail_column(element_id,db_name,table_name,column_name);
CREATE INDEX HIVE_ELEMENT_ID_FK_IDX ON qua_web_hive_element_detail_db(element_id);
CREATE INDEX IDX_8_2_IDX ON qua_web_hive_element_detail_table(element_id,db_name,table_name);
CREATE INDEX IDX_5_3_IDX ON qua_web_hive_task_result_column(element_id,db_name,table_name,column_name,snapshoot_version);
CREATE INDEX QUA_WEB_HIVE_TASK_RESULT_COLUMN_TABLE_ID_FK_IDX ON qua_web_hive_task_result_column(table_id);
CREATE INDEX HIVE_TASK_ID_FK_IDX ON qua_web_hive_task_result_db(task_id);
CREATE INDEX IDX_9_4_IDX ON qua_web_hive_task_result_db(element_id,db_name,snapshoot_version);
CREATE INDEX IDX_7_5_IDX ON qua_web_hive_task_result_table(element_id,db_name,table_name,snapshoot_version);
CREATE INDEX QUA_WEB_HIVE_TASK_RESULT_TABLE_DB_ID_FK_IDX ON qua_web_hive_task_result_table(db_id);
CREATE INDEX QUA_WEB_JOB_ES_ELEMENT_ID_FK_IDX ON qua_web_job(element_id);
CREATE INDEX IDX_6_6_IDX ON qua_web_mysql_element_detail_column(element_id,db_name,table_name,column_name);
CREATE INDEX MYSQL_ELEMENT_ID_FK_IDX ON qua_web_mysql_element_detail_db(element_id);
CREATE INDEX IDX_8_7_IDX ON qua_web_mysql_element_detail_table(element_id,db_name,table_name);
CREATE INDEX IDX_5_8_IDX ON qua_web_mysql_task_result_column(element_id,db_name,table_name,column_name,snapshoot_version);
CREATE INDEX QUA_WEB_MYSQL_TASK_RESULT_COLUMN_TABLE_ID_FK_IDX ON qua_web_mysql_task_result_column(table_id);
CREATE INDEX QUA_WEB_MYSQL_TASK_RESULT_COLUMN_TENANT_ID_IDX_IDX ON qua_web_mysql_task_result_column(tenant_id);
CREATE INDEX IDX_9_9_IDX ON qua_web_mysql_task_result_db(element_id,db_name,snapshoot_version);
CREATE INDEX MYSQL_TASK_ID_FK_IDX ON qua_web_mysql_task_result_db(task_id);
CREATE INDEX IDX_7_10_IDX ON qua_web_mysql_task_result_table(element_id,db_name,table_name,snapshoot_version);
CREATE INDEX QUA_WEB_MYSQL_TASK_RESULT_TABLE_DB_ID_FK_IDX ON qua_web_mysql_task_result_table(db_id);
CREATE INDEX ES_JOB_ID_FK_IDX ON qua_web_task(job_id);
CREATE INDEX QUA_MONITOR_RESULT_TASK_ID_IDX ON qua_monitor_result(task_id);
