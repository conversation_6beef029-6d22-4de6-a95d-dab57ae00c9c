-- 1. 对表 qua_web_ch_element_detail_column 的修改
ALTER TABLE qua_web_ch_element_detail_column ADD COLUMN cn_desc varchar(200) NULL;
ALTER TABLE qua_web_ch_element_detail_column ADD COLUMN enum_value varchar(200) NULL;
ALTER TABLE qua_web_ch_element_detail_column ADD COLUMN mapping_fields varchar(200) NULL;
ALTER TABLE qua_web_ch_element_detail_column ADD COLUMN sort int NOT NULL DEFAULT 0;

COMMENT ON COLUMN qua_web_ch_element_detail_column.cn_desc IS '中文描述';
COMMENT ON COLUMN qua_web_ch_element_detail_column.enum_value IS '枚举值';
COMMENT ON COLUMN qua_web_ch_element_detail_column.mapping_fields IS '映射字段';
COMMENT ON COLUMN qua_web_ch_element_detail_column.sort IS '排序字段';

-- 2. 对表 qua_web_es_element_detail_field 的修改
ALTER TABLE qua_web_es_element_detail_field ADD COLUMN cn_desc varchar(200) NULL;
ALTER TABLE qua_web_es_element_detail_field ADD COLUMN enum_value varchar(200) NULL;
ALTER TABLE qua_web_es_element_detail_field ADD COLUMN mapping_fields varchar(200) NULL;
ALTER TABLE qua_web_es_element_detail_field ADD COLUMN sort int NOT NULL DEFAULT 0;

COMMENT ON COLUMN qua_web_es_element_detail_field.cn_desc IS '中文描述';
COMMENT ON COLUMN qua_web_es_element_detail_field.enum_value IS '枚举值';
COMMENT ON COLUMN qua_web_es_element_detail_field.mapping_fields IS '映射字段';
COMMENT ON COLUMN qua_web_es_element_detail_field.sort IS '排序字段';

-- 3. 对表 qua_web_hive_element_detail_column 的修改
ALTER TABLE qua_web_hive_element_detail_column ADD COLUMN cn_desc varchar(200) NULL;
ALTER TABLE qua_web_hive_element_detail_column ADD COLUMN enum_value varchar(200) NULL;
ALTER TABLE qua_web_hive_element_detail_column ADD COLUMN mapping_fields varchar(200) NULL;
ALTER TABLE qua_web_hive_element_detail_column ADD COLUMN sort int NOT NULL DEFAULT 0;

COMMENT ON COLUMN qua_web_hive_element_detail_column.cn_desc IS '中文描述';
COMMENT ON COLUMN qua_web_hive_element_detail_column.enum_value IS '枚举值';
COMMENT ON COLUMN qua_web_hive_element_detail_column.mapping_fields IS '映射字段';
COMMENT ON COLUMN qua_web_hive_element_detail_column.sort IS '排序字段';

-- 4. 对表 qua_web_mysql_element_detail_column 的修改
ALTER TABLE qua_web_mysql_element_detail_column ADD COLUMN cn_desc varchar(200) NULL;
ALTER TABLE qua_web_mysql_element_detail_column ADD COLUMN enum_value varchar(200) NULL;
ALTER TABLE qua_web_mysql_element_detail_column ADD COLUMN mapping_fields varchar(200) NULL;
ALTER TABLE qua_web_mysql_element_detail_column ADD COLUMN sort int NOT NULL DEFAULT 0;

COMMENT ON COLUMN qua_web_mysql_element_detail_column.cn_desc IS '中文描述';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.enum_value IS '枚举值';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.mapping_fields IS '映射字段';
COMMENT ON COLUMN qua_web_mysql_element_detail_column.sort IS '排序字段';

-- 5. 对表 data_dictionary_item 的修改
ALTER TABLE data_dictionary_item ADD COLUMN cn_desc varchar(200) NULL;
ALTER TABLE data_dictionary_item ADD COLUMN enum_value varchar(200) NULL;
ALTER TABLE data_dictionary_item ADD COLUMN mapping_fields varchar(200) NULL;
ALTER TABLE data_dictionary_item ADD COLUMN sort int NOT NULL DEFAULT 0;

COMMENT ON COLUMN data_dictionary_item.cn_desc IS '中文描述';
COMMENT ON COLUMN data_dictionary_item.enum_value IS '枚举值';
COMMENT ON COLUMN data_dictionary_item.mapping_fields IS '映射字段';
COMMENT ON COLUMN data_dictionary_item.sort IS '排序字段';
