CREATE TABLE IF NOT EXISTS data_dictionary_item_view_record
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VA<PERSON><PERSON><PERSON>(64),
	table_name VARCHAR(255),
	datasource_type VARCHAR(64),
	viewer_tenant_id BIGINT NOT NULL ,
	create_time TIMESTAMP
);

CREATE TABLE IF NOT EXISTS data_mart_cart
(
	id SERIAL  PRIMARY KEY,
	asset_id BIGINT NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	create_user VARCHAR(100) NOT NULL ,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE IF NOT EXISTS data_score_calc_config
(
	id SERIAL  PRIMARY KEY,
	table_cn_name_wi DOUBLE PRECISION DEFAULT 0.000,
	table_cn_name_deduction DOUBLE PRECISION DEFAULT 0.000,
	table_desc_wi DOUBLE PRECISION DEFAULT 0.000,
	table_owner_wi DOUBLE PRECISION DEFAULT 0.000,
	table_dw_level_wi DOUBLE PRECISION DEFAULT 0.000,
	table_business_sector_wi DOUBLE PRECISION DEFAULT 0.000,
	table_data_domain_wi DOUBLE PRECISION DEFAULT 0.000,
	table_business_process_wi DOUBLE PRECISION DEFAULT 0.000,
	field_cn_name_wi DOUBLE PRECISION DEFAULT 0.000,
	field_cn_name_deduction DOUBLE PRECISION DEFAULT 0.000,
	field_desc_wi DOUBLE PRECISION DEFAULT 0.000,
	field_required_wi DOUBLE PRECISION DEFAULT 0.000,
	field_enum_wi DOUBLE PRECISION DEFAULT 0.000,
	low_maintenance_asset_score DOUBLE PRECISION DEFAULT 0.000,
	high_value_asset_score INTEGER DEFAULT 3,
	low_quality_asset_score DOUBLE PRECISION DEFAULT 0.000,
	tenant_id BIGINT,
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP
);

CREATE TABLE IF NOT EXISTS qua_asset_high_value_statistics
(
	id SERIAL  PRIMARY KEY,
	datasource_type VARCHAR(64) NOT NULL ,
	num BIGINT DEFAULT 0,
	statistic_date VARCHAR(64) NOT NULL ,
	create_time TIMESTAMP,
	status INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS qua_asset_isolated_island_statistics
(
	id SERIAL  PRIMARY KEY,
	datasource_type VARCHAR(64) NOT NULL ,
	num BIGINT DEFAULT 0,
	statistic_date VARCHAR(64) NOT NULL ,
	create_time TIMESTAMP,
	status INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS qua_asset_low_maintenance_statistics
(
	id SERIAL  PRIMARY KEY,
	datasource_type VARCHAR(64) NOT NULL ,
	num BIGINT DEFAULT 0,
	statistic_date VARCHAR(64) NOT NULL ,
	create_time TIMESTAMP,
	status INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS qua_asset_low_quality_statistics
(
	id SERIAL  PRIMARY KEY,
	datasource_type VARCHAR(64) NOT NULL ,
	num BIGINT DEFAULT 0,
	statistic_date VARCHAR(64) NOT NULL ,
	create_time TIMESTAMP,
	status INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS qua_asset_maintenance
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	table_name VARCHAR(255),
	datasource_type VARCHAR(64),
	score NUMERIC DEFAULT 0.00,
	tenant_id BIGINT NOT NULL ,
	create_time TIMESTAMP,
	status INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS qua_asset_quality
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	table_name VARCHAR(255),
	datasource_type VARCHAR(64),
	score NUMERIC DEFAULT 0.00,
	tenant_id BIGINT NOT NULL ,
	create_time TIMESTAMP,
	status INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS qua_asset_table_data_statistics
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	table_name VARCHAR(255),
	datasource_type VARCHAR(64),
	data_count BIGINT DEFAULT 0,
	data_storage_size BIGINT DEFAULT 0,
	statistic_date VARCHAR(100),
	create_time TIMESTAMP
);

CREATE TABLE IF NOT EXISTS qua_web_table_model
(
	id SERIAL  PRIMARY KEY,
	element_id BIGINT,
	db_name VARCHAR(64),
	table_name VARCHAR(255),
	datasource_type VARCHAR(64),
	business_sector_id BIGINT,
	dw_level_id BIGINT,
	data_domain_id BIGINT,
	business_process_id BIGINT,
	tenant_id BIGINT NOT NULL ,
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP
);

CREATE TABLE IF NOT EXISTS tb_dw_level
(
	id SERIAL  PRIMARY KEY,
	cn_name VARCHAR(128),
	en_name VARCHAR(128),
	level_desc VARCHAR(1000),
	built_in INTEGER DEFAULT 0,
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP
);

CREATE TABLE IF NOT EXISTS tb_dw_level_tenant
(
	id SERIAL  PRIMARY KEY,
	tenant_id BIGINT NOT NULL ,
	dw_level_id BIGINT NOT NULL
);

CREATE TABLE IF NOT EXISTS tb_select_field
(
	id SERIAL  PRIMARY KEY,
	module_name VARCHAR(50),
	field_name VARCHAR(255),
	field_desc VARCHAR(255),
	sort INTEGER DEFAULT 1,
	selected INTEGER DEFAULT 0,
	create_user VARCHAR(64),
	create_time TIMESTAMP,
	update_user VARCHAR(64),
	update_time TIMESTAMP
);



COMMENT ON COLUMN data_dictionary_item_view_record.element_id IS '元数据ID';
COMMENT ON COLUMN data_dictionary_item_view_record.db_name IS '数据库名称';
COMMENT ON COLUMN data_dictionary_item_view_record.table_name IS '数据表名称';
COMMENT ON COLUMN data_dictionary_item_view_record.datasource_type IS '数据源类型';
COMMENT ON COLUMN data_dictionary_item_view_record.viewer_tenant_id IS '浏览者租户id';
COMMENT ON COLUMN data_dictionary_item_view_record.create_time IS '入库时间';
COMMENT ON COLUMN data_mart_cart.asset_id IS '分类ID';
COMMENT ON COLUMN data_score_calc_config.table_cn_name_wi IS '表中文名权重';
COMMENT ON COLUMN data_score_calc_config.table_cn_name_deduction IS '表中文名扣分项';
COMMENT ON COLUMN data_score_calc_config.table_desc_wi IS '表描述权重';
COMMENT ON COLUMN data_score_calc_config.table_owner_wi IS '表责任人权重';
COMMENT ON COLUMN data_score_calc_config.table_dw_level_wi IS '所属分层权重';
COMMENT ON COLUMN data_score_calc_config.table_business_sector_wi IS '业务板块权重';
COMMENT ON COLUMN data_score_calc_config.table_data_domain_wi IS '业数据域权重';
COMMENT ON COLUMN data_score_calc_config.table_business_process_wi IS '业务过程权重';
COMMENT ON COLUMN data_score_calc_config.field_cn_name_wi IS '字段中文名称权重';
COMMENT ON COLUMN data_score_calc_config.field_cn_name_deduction IS '字段中文名称扣分项';
COMMENT ON COLUMN data_score_calc_config.field_desc_wi IS '字段中文描述权重';
COMMENT ON COLUMN data_score_calc_config.field_required_wi IS '字段是否必填权重';
COMMENT ON COLUMN data_score_calc_config.field_enum_wi IS '枚举值权重';
COMMENT ON COLUMN data_score_calc_config.low_maintenance_asset_score IS '低维护资产分值';
COMMENT ON COLUMN data_score_calc_config.high_value_asset_score IS '高价值资产分值';
COMMENT ON COLUMN data_score_calc_config.low_quality_asset_score IS '低质量资产分值';
COMMENT ON COLUMN data_score_calc_config.tenant_id IS '租户id';
COMMENT ON COLUMN data_score_calc_config.create_user IS '创建人';
COMMENT ON COLUMN data_score_calc_config.create_time IS '创建时间';
COMMENT ON COLUMN data_score_calc_config.update_user IS '修改人';
COMMENT ON COLUMN data_score_calc_config.update_time IS '修改时间';
COMMENT ON COLUMN qua_asset_high_value_statistics.datasource_type IS '数据源类型';
COMMENT ON COLUMN qua_asset_high_value_statistics.num IS '数量';
COMMENT ON COLUMN qua_asset_high_value_statistics.statistic_date IS '统计日期';
COMMENT ON COLUMN qua_asset_high_value_statistics.create_time IS '入库时间';
COMMENT ON COLUMN qua_asset_high_value_statistics.status IS '0-有效，1-无效';
COMMENT ON COLUMN qua_asset_isolated_island_statistics.datasource_type IS '数据源类型';
COMMENT ON COLUMN qua_asset_isolated_island_statistics.num IS '数量';
COMMENT ON COLUMN qua_asset_isolated_island_statistics.statistic_date IS '统计日期';
COMMENT ON COLUMN qua_asset_isolated_island_statistics.create_time IS '入库时间';
COMMENT ON COLUMN qua_asset_isolated_island_statistics.status IS '0-有效，1-无效';
COMMENT ON COLUMN qua_asset_low_maintenance_statistics.datasource_type IS '数据源类型';
COMMENT ON COLUMN qua_asset_low_maintenance_statistics.num IS '数量';
COMMENT ON COLUMN qua_asset_low_maintenance_statistics.statistic_date IS '统计日期';
COMMENT ON COLUMN qua_asset_low_maintenance_statistics.create_time IS '入库时间';
COMMENT ON COLUMN qua_asset_low_maintenance_statistics.status IS '0-有效，1-无效';
COMMENT ON COLUMN qua_asset_low_quality_statistics.datasource_type IS '数据源类型';
COMMENT ON COLUMN qua_asset_low_quality_statistics.num IS '数量';
COMMENT ON COLUMN qua_asset_low_quality_statistics.statistic_date IS '统计日期';
COMMENT ON COLUMN qua_asset_low_quality_statistics.create_time IS '入库时间';
COMMENT ON COLUMN qua_asset_low_quality_statistics.status IS '0-有效，1-无效';
COMMENT ON COLUMN qua_asset_maintenance.element_id IS '元数据ID';
COMMENT ON COLUMN qua_asset_maintenance.db_name IS '数据库名称';
COMMENT ON COLUMN qua_asset_maintenance.table_name IS '数据表名称';
COMMENT ON COLUMN qua_asset_maintenance.datasource_type IS '数据源类型';
COMMENT ON COLUMN qua_asset_maintenance.score IS '维护分，保留2位小数';
COMMENT ON COLUMN qua_asset_maintenance.tenant_id IS '租户id';
COMMENT ON COLUMN qua_asset_maintenance.create_time IS '创建时间';
COMMENT ON COLUMN qua_asset_maintenance.status IS '0-有效，1-无效';
COMMENT ON COLUMN qua_asset_quality.element_id IS '元数据ID';
COMMENT ON COLUMN qua_asset_quality.db_name IS '数据库名称';
COMMENT ON COLUMN qua_asset_quality.table_name IS '数据表名称';
COMMENT ON COLUMN qua_asset_quality.datasource_type IS '数据源类型';
COMMENT ON COLUMN qua_asset_quality.score IS '质量分，保留2位小数';
COMMENT ON COLUMN qua_asset_quality.tenant_id IS '租户id';
COMMENT ON COLUMN qua_asset_quality.create_time IS '创建时间';
COMMENT ON COLUMN qua_asset_quality.status IS '0-有效，1-无效';
COMMENT ON COLUMN qua_asset_table_data_statistics.element_id IS '元数据ID';
COMMENT ON COLUMN qua_asset_table_data_statistics.db_name IS '数据库名称';
COMMENT ON COLUMN qua_asset_table_data_statistics.table_name IS '数据表名称';
COMMENT ON COLUMN qua_asset_table_data_statistics.datasource_type IS '数据源类型';
COMMENT ON COLUMN qua_asset_table_data_statistics.data_count IS '数据条数';
COMMENT ON COLUMN qua_asset_table_data_statistics.data_storage_size IS '数据存储量（字节）';
COMMENT ON COLUMN qua_asset_table_data_statistics.statistic_date IS '统计日期';
COMMENT ON COLUMN qua_asset_table_data_statistics.create_time IS '入库时间';
COMMENT ON COLUMN qua_web_table_model.element_id IS '元数据ID';
COMMENT ON COLUMN qua_web_table_model.db_name IS '数据库名称';
COMMENT ON COLUMN qua_web_table_model.table_name IS '数据表名称';
COMMENT ON COLUMN qua_web_table_model.datasource_type IS '数据源类型';
COMMENT ON COLUMN qua_web_table_model.business_sector_id IS '所属业务板块';
COMMENT ON COLUMN qua_web_table_model.dw_level_id IS '所属层级';
COMMENT ON COLUMN qua_web_table_model.data_domain_id IS '所属数据域';
COMMENT ON COLUMN qua_web_table_model.business_process_id IS '所属业务过程';
COMMENT ON COLUMN qua_web_table_model.tenant_id IS '租户id';
COMMENT ON COLUMN qua_web_table_model.create_user IS '创建人';
COMMENT ON COLUMN qua_web_table_model.create_time IS '创建时间';
COMMENT ON COLUMN qua_web_table_model.update_user IS '修改人';
COMMENT ON COLUMN qua_web_table_model.update_time IS '修改时间';
COMMENT ON COLUMN tb_dw_level.cn_name IS '中文名称';
COMMENT ON COLUMN tb_dw_level.en_name IS '英文名称';
COMMENT ON COLUMN tb_dw_level.level_desc IS '层级描述';
COMMENT ON COLUMN tb_dw_level.built_in IS '是否内置，0-否，1-是';
COMMENT ON COLUMN tb_dw_level.create_user IS '创建人';
COMMENT ON COLUMN tb_dw_level.create_time IS '创建时间';
COMMENT ON COLUMN tb_dw_level.update_user IS '修改人';
COMMENT ON COLUMN tb_dw_level.update_time IS '修改时间';
COMMENT ON COLUMN tb_dw_level_tenant.tenant_id IS '租户id';
COMMENT ON COLUMN tb_dw_level_tenant.dw_level_id IS '层级id';
COMMENT ON COLUMN tb_select_field.module_name IS '所属模块';
COMMENT ON COLUMN tb_select_field.field_name IS '字段名';
COMMENT ON COLUMN tb_select_field.field_desc IS '字段描述';
COMMENT ON COLUMN tb_select_field.sort IS '排序';
COMMENT ON COLUMN tb_select_field.selected IS '是否选中，0-否，1-是';
COMMENT ON COLUMN tb_select_field.create_user IS '创建人';
COMMENT ON COLUMN tb_select_field.create_time IS '创建时间';
COMMENT ON COLUMN tb_select_field.update_user IS '修改人';
COMMENT ON COLUMN tb_select_field.update_time IS '修改时间';

CREATE INDEX QUA_ASSET_TABLE_DATA_STATISTICS_ELEMENT_ID_IDX_IDX ON qua_asset_table_data_statistics(element_id,db_name,table_name,datasource_type);

drop view if exists asset_map_view;
CREATE VIEW asset_map_view AS
SELECT t1.id,
       t1.data_name,
       CASE
           WHEN t1.asset_type = 'clickhouse_table' THEN 'clickhouse'
           WHEN t1.asset_type = 'mysql_table' THEN 'mysql'
           WHEN t1.asset_type = 'hive_table' THEN 'hive'
           WHEN t1.asset_type = 'elasticsearch_index' THEN 'elasticsearch'
           ELSE ''
           END                                                                                     AS asset_type,

       COALESCE(ch.table_owner, mysql.table_owner, hive.table_owner, es.index_owner, '')           AS table_owner,
       COALESCE(ch.db_name, mysql.db_name, hive.db_name, es.index_name, '')                        AS db_name,
       COALESCE(ch.key_words, mysql.key_words, hive.key_words, es.key_words, '')                   AS key_words,
       COALESCE(ch.table_name_cn, mysql.table_name_cn, hive.table_name_cn, es.index_name_cn, '')   AS table_name_cn,

       t2.id                                                                                       AS type_id,
       t2.type_name,
       t3.id                                                                                       AS group_id,
       t3.group_name,
       t1.asset_desc,

       (SELECT COUNT(1) FROM data_mart_subscribe WHERE asset_id = t1.id AND approval_progress = 2) AS subscribe_cnt
FROM data_mart_asset t1
         JOIN
     data_mart_type t2 ON t1.type_id = t2.id
         JOIN
     data_mart_group t3 ON t1.group_id = t3.id
         LEFT JOIN
     qua_web_ch_element_detail_table ch ON t1.asset_type = 'clickhouse_table'
         AND ch.element_id = t1.element_id
         AND ch.db_name = t1.db_name
         AND ch.table_name = t1.table_name
         LEFT JOIN
     qua_web_mysql_element_detail_table mysql ON t1.asset_type = 'mysql_table'
         AND mysql.element_id = t1.element_id
         AND mysql.db_name = t1.db_name
         AND mysql.table_name = t1.table_name
         LEFT JOIN
     qua_web_hive_element_detail_table hive ON t1.asset_type = 'hive_table'
         AND hive.element_id = t1.element_id
         AND hive.db_name = t1.db_name
         AND hive.table_name = t1.table_name
         LEFT JOIN
     qua_web_es_element_detail_index es ON t1.asset_type = 'elasticsearch_index'
         AND es.element_id = t1.element_id
         AND es.index_name = t1.index_name
WHERE t1.publish_status = 1
  AND t1.approval_status = 1;

INSERT INTO tb_dw_level (cn_name, en_name, level_desc, create_user, create_time, update_user, update_time, built_in) VALUES('原始数据层', 'ODS', '原始数据层', 'tenantadmin', '2024-11-04 00:00:00', 'tenantadmin', '2024-11-04 00:00:00', 1);
INSERT INTO tb_dw_level (cn_name, en_name, level_desc, create_user, create_time, update_user, update_time, built_in) VALUES('明细层', 'DWD', '明细层', 'tenantadmin', '2024-11-04 00:00:00', 'tenantadmin', '2024-11-04 00:00:00', 1);
INSERT INTO tb_dw_level (cn_name, en_name, level_desc, create_user, create_time, update_user, update_time, built_in) VALUES('中间层', 'DWM', '中间层', 'tenantadmin', '2024-11-04 00:00:00', 'tenantadmin', '2024-11-04 00:00:00', 1);
INSERT INTO tb_dw_level (cn_name, en_name, level_desc, create_user, create_time, update_user, update_time, built_in) VALUES('汇总数据层', 'DWS', '汇总数据层', 'tenantadmin', '2024-11-04 00:00:00', 'tenantadmin', '2024-11-04 00:00:00', 1);
INSERT INTO tb_dw_level (cn_name, en_name, level_desc, create_user, create_time, update_user, update_time, built_in) VALUES('数据应用层', 'ADS', '数据应用层', 'tenantadmin', '2024-11-04 00:00:00', 'tenantadmin', '2024-11-04 00:00:00', 1);
INSERT INTO tb_dw_level (cn_name, en_name, level_desc, create_user, create_time, update_user, update_time, built_in) VALUES('维度层', 'DIM', '维度层', 'tenantadmin', '2024-11-04 00:00:00', 'tenantadmin', '2024-11-04 00:00:00', 1);

ALTER TABLE qua_web_ch_element_detail_table ADD ddl_last_change_time varchar(50) NULL;
ALTER TABLE qua_web_ch_element_detail_table ADD data_last_change_time varchar(50) NULL;
ALTER TABLE qua_web_ch_element_detail_table ADD last_viewed_time varchar(50) NULL;
ALTER TABLE qua_web_ch_element_detail_table ADD meta_source int DEFAULT 1;
COMMENT ON COLUMN qua_web_ch_element_detail_table.ddl_last_change_time IS 'DDL最后变更时间';
COMMENT ON COLUMN qua_web_ch_element_detail_table.data_last_change_time IS '最后数据变更时间';
COMMENT ON COLUMN qua_web_ch_element_detail_table.last_viewed_time IS '最后查看时间';
COMMENT ON COLUMN qua_web_ch_element_detail_table.meta_source IS '元数据来源，1-自建，2-数据建模';

ALTER TABLE qua_web_es_element_detail_index ADD ddl_last_change_time varchar(50) NULL;
ALTER TABLE qua_web_es_element_detail_index ADD data_last_change_time varchar(50) NULL;
ALTER TABLE qua_web_es_element_detail_index ADD last_viewed_time varchar(50) NULL;
ALTER TABLE qua_web_es_element_detail_index ADD meta_source int DEFAULT 1;
COMMENT ON COLUMN qua_web_es_element_detail_index.ddl_last_change_time IS 'DDL最后变更时间';
COMMENT ON COLUMN qua_web_es_element_detail_index.data_last_change_time IS '最后数据变更时间';
COMMENT ON COLUMN qua_web_es_element_detail_index.last_viewed_time IS '最后查看时间';
COMMENT ON COLUMN qua_web_es_element_detail_index.meta_source IS '元数据来源，1-自建，2-数据建模';

ALTER TABLE qua_web_hive_element_detail_table ADD ddl_last_change_time varchar(50) NULL;
ALTER TABLE qua_web_hive_element_detail_table ADD data_last_change_time varchar(50) NULL;
ALTER TABLE qua_web_hive_element_detail_table ADD last_viewed_time varchar(50) NULL;
ALTER TABLE qua_web_hive_element_detail_table ADD meta_source int DEFAULT 1;
COMMENT ON COLUMN qua_web_hive_element_detail_table.ddl_last_change_time IS 'DDL最后变更时间';
COMMENT ON COLUMN qua_web_hive_element_detail_table.data_last_change_time IS '最后数据变更时间';
COMMENT ON COLUMN qua_web_hive_element_detail_table.last_viewed_time IS '最后查看时间';
COMMENT ON COLUMN qua_web_hive_element_detail_table.meta_source IS '元数据来源，1-自建，2-数据建模';

ALTER TABLE qua_web_mysql_element_detail_table ADD ddl_last_change_time varchar(50) NULL;
ALTER TABLE qua_web_mysql_element_detail_table ADD data_last_change_time varchar(50) NULL;
ALTER TABLE qua_web_mysql_element_detail_table ADD last_viewed_time varchar(50) NULL;
ALTER TABLE qua_web_mysql_element_detail_table ADD meta_source int DEFAULT 1;
COMMENT ON COLUMN qua_web_mysql_element_detail_table.ddl_last_change_time IS 'DDL最后变更时间';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.data_last_change_time IS '最后数据变更时间';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.last_viewed_time IS '最后查看时间';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.meta_source IS '元数据来源，1-自建，2-数据建模';

ALTER TABLE qua_web_ch_element_detail_table ADD key_words varchar(1000) NULL ;
ALTER TABLE qua_web_mysql_element_detail_table ADD key_words varchar(1000) NULL ;
ALTER TABLE qua_web_hive_element_detail_table ADD key_words varchar(1000) NULL ;
ALTER TABLE qua_web_es_element_detail_index ADD key_words varchar(1000) NULL ;
COMMENT ON COLUMN qua_web_ch_element_detail_table.key_words IS '关键词，["a","b"]';
COMMENT ON COLUMN qua_web_mysql_element_detail_table.key_words IS '关键词，["a","b"]';
COMMENT ON COLUMN qua_web_hive_element_detail_table.key_words IS '关键词，["a","b"]';
COMMENT ON COLUMN qua_web_es_element_detail_index.key_words IS '关键词，["a","b"]';


-- 数据字典添加字段
ALTER TABLE data_dictionary_item ADD business_sector_id bigint NULL;
ALTER TABLE data_dictionary_item ADD dw_level_id bigint NULL;
ALTER TABLE data_dictionary_item ADD data_domain_id bigint NULL;
ALTER TABLE data_dictionary_item ADD business_process_id bigint NULL;
ALTER TABLE data_dictionary_item ADD data_count bigint DEFAULT 0;
ALTER TABLE data_dictionary_item ADD data_storage_size bigint DEFAULT 0;
ALTER TABLE data_dictionary_item ADD meta_source int DEFAULT 1;
ALTER TABLE data_dictionary_item ADD is_primary_key int DEFAULT NULL;
COMMENT ON COLUMN data_dictionary_item.business_sector_id IS '所属业务板块';
COMMENT ON COLUMN data_dictionary_item.dw_level_id IS '所属层级';
COMMENT ON COLUMN data_dictionary_item.data_domain_id IS '所属数据域';
COMMENT ON COLUMN data_dictionary_item.business_process_id IS '所属业务过程';
COMMENT ON COLUMN data_dictionary_item.data_count IS '数据条数';
COMMENT ON COLUMN data_dictionary_item.data_storage_size IS '数据存储量（字节）';
COMMENT ON COLUMN data_dictionary_item.meta_source IS '元数据来源，1-自建，2-数据建模';
COMMENT ON COLUMN data_dictionary_item.is_primary_key IS '是否主键，0-否，1-是';

ALTER TABLE data_mart_asset ADD db_name varchar(50) NULL ;
ALTER TABLE data_mart_asset ADD table_name varchar(100) NULL ;
ALTER TABLE data_mart_asset ADD index_name varchar(100) NULL ;
ALTER TABLE data_mart_asset ADD batch_no varchar(100) NULL ;
COMMENT ON COLUMN data_mart_asset.db_name IS '库名';
COMMENT ON COLUMN data_mart_asset.table_name IS '表名称';
COMMENT ON COLUMN data_mart_asset.index_name IS '索引名称';
COMMENT ON COLUMN data_mart_asset.batch_no IS '批次号';

INSERT INTO tb_select_field (module_name, field_name, field_desc, sort, selected, create_user, create_time, update_user, update_time)
VALUES
('itemAssetPage', 'tableName', '表名', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'columnNameCn', '表中文名', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'datasourceType', '存储类型', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'metaSource', '元数据来源', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'assetTypeList', '资产类型', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'assetStatusList', '资产状态', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'dataCount', '数据量', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'storageSize', '数据大小', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'isSensitive', '是否涉敏', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'owner', '责任人', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'upstreamDependencyCount', '上游依赖', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'downstreamDependencyCount', '下游依赖', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'businessSectorName', '业务板块', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'dataDomainName', '数据域', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'dwLevelName', '数据层级', 1, 0, 'admin', NOW(), 'admin', NOW());

ALTER TABLE data_model_logic_table_field ADD type INTEGER NULL;
ALTER TABLE data_model_logic_table_field ADD data_standard varchar(200);
ALTER TABLE data_model_business_sector ADD COLUMN built_in INTEGER DEFAULT 0;
COMMENT ON COLUMN data_model_logic_table_field.type IS '类型: 0自定义 , 1数据源标准';
COMMENT ON COLUMN data_model_logic_table_field.data_standard IS '数据源标准ID 一级id_二级id_三级id';
COMMENT ON COLUMN data_model_business_sector.built_in IS '是否内置，0-否，1-是';

delete from tb_dic where ALIAS  ='model-manager';
INSERT INTO tb_dic ( NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ( '模型管理', 'model-manager', 2, null, 0);