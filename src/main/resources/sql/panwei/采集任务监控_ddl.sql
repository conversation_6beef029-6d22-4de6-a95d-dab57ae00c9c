CREATE TABLE IF NOT EXISTS etl_task_monitor
(
	id SERIAL  PRIMARY KEY,
	source_name <PERSON><PERSON><PERSON><PERSON>(512),
	flow_id BIGINT NOT NULL ,
	source_type VARCHA<PERSON>(255),
	cluster_name VA<PERSON>HA<PERSON>(255),
	task_create_time TIMESTAMP,
	task_status SMALLINT,
	task_monitor_time TIMESTAMP,
	disposal_recommendation VARCHAR(1000) DEFAULT '',
	client_read_status SMALLINT DEFAULT 0,
	gate_in_count BIGINT DEFAULT 0,
	gate_in_status SMALLINT DEFAULT 0,
	gate_read_size BIGINT DEFAULT 0,
	gate_write_size BIGINT DEFAULT 0,
	gate_read_write_status SMALLINT DEFAULT 0,
	gate_out_count BIGINT DEFAULT 0,
	gate_out_status SMALLINT DEFAULT 0,
	parser_kafka_lag BIGINT DEFAULT 0,
	parser_kafka_lag_status SMALLINT DEFAULT 0,
	parser_read_size BIGINT DEFAULT 0,
	parser_write_size BIGINT DEFAULT 0,
	parser_read_write_status SMALLINT DEFAULT 0,
	parser_write_status VARCHAR(5) DEFAULT '000',
	parser_ch_out_count BIGINT,
	parser_ch_rate BIGINT,
	parser_kafka_out_count BIGINT,
	parser_kafka_rate BIGINT,
	parser_es_out_count BIGINT,
	parser_es_rate BIGINT,
	tenant_id BIGINT NOT NULL ,
	create_time TIMESTAMP,
	create_user VARCHAR(128),
	update_time TIMESTAMP,
	update_user VARCHAR(128),
	parser_ch_out_status SMALLINT DEFAULT 0,
	parser_kafka_out_status SMALLINT DEFAULT 0,
	parser_es_out_status SMALLINT DEFAULT 0
);

CREATE TABLE IF NOT EXISTS etl_task_monitor_cluster_overview
(
	id SERIAL  PRIMARY KEY,
	cluster_cn_name VARCHAR(255),
	ch_count BIGINT DEFAULT 0,
	kafka_count BIGINT DEFAULT 0,
	es_count BIGINT DEFAULT 0,
	ch_length BIGINT DEFAULT 0,
	kafka_length BIGINT DEFAULT 0,
	es_length BIGINT DEFAULT 0,
	kafka_lag BIGINT DEFAULT 0,
	epm BIGINT DEFAULT 0,
	create_date TIMESTAMP,
	tenant_id BIGINT NOT NULL
);

CREATE TABLE IF NOT EXISTS etl_task_monitor_logmoudle
(
	id SERIAL  PRIMARY KEY,
	flow_id VARCHAR(100) NOT NULL ,
	work_ip VARCHAR(64) NOT NULL ,
	work_moudle VARCHAR(16) NOT NULL ,
	work_desc VARCHAR(512),
	open_port VARCHAR(32) DEFAULT '',
	cluster_name VARCHAR(255) DEFAULT '',
	cluster_cn_name VARCHAR(255),
	parser_topic VARCHAR(255),
	tenant_id BIGINT NOT NULL ,
	etl_task_monitor_id BIGINT NOT NULL ,
	logmodule_status SMALLINT DEFAULT 0,
	gate_in_status SMALLINT DEFAULT 0,
	gate_read_write_status SMALLINT DEFAULT 0,
	gate_out_status SMALLINT DEFAULT 0,
	parser_kafka_lag_status SMALLINT DEFAULT 0,
	parser_read_write_status SMALLINT DEFAULT 0,
	parser_write_status SMALLINT DEFAULT 0,
	parser_ch_out_status SMALLINT DEFAULT 0,
	parser_kafka_out_status SMALLINT DEFAULT 0,
	parser_es_out_status SMALLINT DEFAULT 0,
	create_time TIMESTAMP
);

CREATE TABLE IF NOT EXISTS etl_task_monitor_logmoudle_base
(
	id SERIAL  PRIMARY KEY,
	flow_id VARCHAR(100) NOT NULL ,
	source_ip VARCHAR(64) NOT NULL ,
	node_type VARCHAR(50) NOT NULL ,
	writer_type VARCHAR(50) NOT NULL ,
	in_out VARCHAR(50) NOT NULL ,
	base_count BIGINT DEFAULT 0,
	create_date TIMESTAMP,
	tenant_id BIGINT NOT NULL ,
	statistic_day VARCHAR(64) NOT NULL
);



COMMENT ON COLUMN etl_task_monitor.id IS '主键';
COMMENT ON COLUMN etl_task_monitor.source_name IS '任务名称';
COMMENT ON COLUMN etl_task_monitor.flow_id IS 'flowId';
COMMENT ON COLUMN etl_task_monitor.source_type IS '采集类型';
COMMENT ON COLUMN etl_task_monitor.cluster_name IS '集群名称';
COMMENT ON COLUMN etl_task_monitor.task_create_time IS '任务创建时间';
COMMENT ON COLUMN etl_task_monitor.task_status IS '任务总体状态，0-正常，1-警告，2-故障';
COMMENT ON COLUMN etl_task_monitor.task_monitor_time IS '任务监测时间';
COMMENT ON COLUMN etl_task_monitor.disposal_recommendation IS '处置建议';
COMMENT ON COLUMN etl_task_monitor.client_read_status IS 'client 对端连通性';
COMMENT ON COLUMN etl_task_monitor.gate_in_count IS 'gate采集数据量';
COMMENT ON COLUMN etl_task_monitor.gate_in_status IS 'gate采集数据量状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor.gate_read_size IS 'gate读缓存';
COMMENT ON COLUMN etl_task_monitor.gate_write_size IS 'gate写缓存';
COMMENT ON COLUMN etl_task_monitor.gate_read_write_status IS 'gate读写缓存状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor.gate_out_count IS 'gate写kafka数据量';
COMMENT ON COLUMN etl_task_monitor.gate_out_status IS 'gate写Kafka状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor.parser_kafka_lag IS 'parser消费Kafka积压';
COMMENT ON COLUMN etl_task_monitor.parser_kafka_lag_status IS 'parser消费Kafka积压正常/异常状态';
COMMENT ON COLUMN etl_task_monitor.parser_read_size IS 'parser读缓存';
COMMENT ON COLUMN etl_task_monitor.parser_write_size IS 'parser写缓存';
COMMENT ON COLUMN etl_task_monitor.parser_read_write_status IS 'parser读写缓存状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor.parser_write_status IS 'parser写入ch、es、kafka正常/异常状态';
COMMENT ON COLUMN etl_task_monitor.parser_ch_out_count IS '写入CH数据量';
COMMENT ON COLUMN etl_task_monitor.parser_ch_rate IS '写入CH速率，*条/秒';
COMMENT ON COLUMN etl_task_monitor.parser_kafka_out_count IS '写入kafka数据量';
COMMENT ON COLUMN etl_task_monitor.parser_kafka_rate IS '写入kafka速率，*条/秒';
COMMENT ON COLUMN etl_task_monitor.parser_es_out_count IS '写入es数据量';
COMMENT ON COLUMN etl_task_monitor.parser_es_rate IS '写入es速率，*条/秒';
COMMENT ON COLUMN etl_task_monitor.tenant_id IS '租户id';
COMMENT ON COLUMN etl_task_monitor.create_time IS '统计时间';
COMMENT ON COLUMN etl_task_monitor.create_user IS '创建人';
COMMENT ON COLUMN etl_task_monitor.update_time IS '更新时间';
COMMENT ON COLUMN etl_task_monitor.update_user IS '更新人';
COMMENT ON COLUMN etl_task_monitor.parser_ch_out_status IS '写入CH数据量状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor.parser_kafka_out_status IS '写入kafka数据量状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor.parser_es_out_status IS '写入es数据量状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor_cluster_overview.cluster_cn_name IS '集群中文名';
COMMENT ON COLUMN etl_task_monitor_cluster_overview.ch_count IS 'CH条数';
COMMENT ON COLUMN etl_task_monitor_cluster_overview.kafka_count IS 'kafka条数';
COMMENT ON COLUMN etl_task_monitor_cluster_overview.es_count IS 'es条数';
COMMENT ON COLUMN etl_task_monitor_cluster_overview.ch_length IS 'CH磁盘占用';
COMMENT ON COLUMN etl_task_monitor_cluster_overview.kafka_length IS 'kafka磁盘占用';
COMMENT ON COLUMN etl_task_monitor_cluster_overview.es_length IS 'es磁盘占用';
COMMENT ON COLUMN etl_task_monitor_cluster_overview.kafka_lag IS '近1分钟待处理数';
COMMENT ON COLUMN etl_task_monitor_cluster_overview.epm IS '每分钟事件数';
COMMENT ON COLUMN etl_task_monitor_cluster_overview.create_date IS '创建日期';
COMMENT ON COLUMN etl_task_monitor_cluster_overview.tenant_id IS '租户id';
COMMENT ON COLUMN etl_task_monitor_logmoudle.flow_id IS 'flowId';
COMMENT ON COLUMN etl_task_monitor_logmoudle.work_ip IS 'work部署的地址';
COMMENT ON COLUMN etl_task_monitor_logmoudle.work_moudle IS '类型single, client, relay, gate, parser';
COMMENT ON COLUMN etl_task_monitor_logmoudle.open_port IS '开放端口';
COMMENT ON COLUMN etl_task_monitor_logmoudle.cluster_name IS '集群名称';
COMMENT ON COLUMN etl_task_monitor_logmoudle.cluster_cn_name IS '集群中文名';
COMMENT ON COLUMN etl_task_monitor_logmoudle.parser_topic IS 'Parser消费的topic名';
COMMENT ON COLUMN etl_task_monitor_logmoudle.tenant_id IS '租户id';
COMMENT ON COLUMN etl_task_monitor_logmoudle.etl_task_monitor_id IS '关联的监控id';
COMMENT ON COLUMN etl_task_monitor_logmoudle.logmodule_status IS 'logmodule当前节点总体状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor_logmoudle.gate_in_status IS 'gate采集数据量状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor_logmoudle.gate_read_write_status IS 'gate读写缓存状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor_logmoudle.gate_out_status IS 'gate写Kafka状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor_logmoudle.parser_kafka_lag_status IS 'parser消费Kafka积压正常/异常状态';
COMMENT ON COLUMN etl_task_monitor_logmoudle.parser_read_write_status IS 'parser读写缓存状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor_logmoudle.parser_write_status IS 'parser写入ch、es、kafka正常/异常状态';
COMMENT ON COLUMN etl_task_monitor_logmoudle.parser_ch_out_status IS '写入CH数据量状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor_logmoudle.parser_kafka_out_status IS '写入kafka数据量状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor_logmoudle.parser_es_out_status IS '写入es数据量状态，0-正常,1-异常';
COMMENT ON COLUMN etl_task_monitor_logmoudle.create_time IS '创建日期';
COMMENT ON COLUMN etl_task_monitor_logmoudle_base.flow_id IS 'flowId';
COMMENT ON COLUMN etl_task_monitor_logmoudle_base.source_ip IS 'work部署的地址';
COMMENT ON COLUMN etl_task_monitor_logmoudle_base.node_type IS '类型single, client, relay, gate, parser';
COMMENT ON COLUMN etl_task_monitor_logmoudle_base.writer_type IS '写入类型';
COMMENT ON COLUMN etl_task_monitor_logmoudle_base.in_out IS 'in or out';
COMMENT ON COLUMN etl_task_monitor_logmoudle_base.base_count IS '基线数据量';
COMMENT ON COLUMN etl_task_monitor_logmoudle_base.create_date IS '创建日期';
COMMENT ON COLUMN etl_task_monitor_logmoudle_base.tenant_id IS '租户id';
COMMENT ON COLUMN etl_task_monitor_logmoudle_base.statistic_day IS '统计日期，yyyy-MM-dd';


CREATE INDEX ETL_TASK_MONITOR_CREATE_TIME_IDX_IDX ON etl_task_monitor(create_time);
CREATE INDEX ETL_TASK_MONITOR_FLOW_ID_IDX_IDX ON etl_task_monitor(flow_id);
CREATE INDEX ETL_TASK_MONITOR_SOURCE_NAME_IDX_IDX ON etl_task_monitor(source_name);
CREATE INDEX ETL_TASK_MONITOR_TASK_MONITOR_TIME_IDX_IDX ON etl_task_monitor(task_monitor_time);
CREATE INDEX ETL_TASK_MONITOR_TENANT_ID_IDX_IDX ON etl_task_monitor(tenant_id);
CREATE INDEX ETL_TASK_MONITOR_CLUSTER_OVERVIEW_CREATE_DATE_IDX_IDX ON etl_task_monitor_cluster_overview(create_date);
CREATE INDEX ETL_TASK_MONITOR_LOGMOUDLE_CREATE_DATE_IDX_IDX ON etl_task_monitor_logmoudle(create_time);
CREATE INDEX ETL_TASK_MONITOR_LOGMOUDLE_BASE_STATISTIC_DAY_IDX_IDX ON etl_task_monitor_logmoudle_base(statistic_day);

