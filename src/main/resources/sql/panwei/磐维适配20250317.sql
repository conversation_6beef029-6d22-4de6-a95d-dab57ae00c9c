drop view if exists asset_map_view;

ALTER TABLE qua_web_ch_element_detail_table ALTER COLUMN key_words TYPE VARCHAR(2048);
ALTER TABLE qua_web_mysql_element_detail_table ALTER COLUMN key_words TYPE VARCHAR(2048);
ALTER TABLE qua_web_hive_element_detail_table ALTER COLUMN key_words TYPE VARCHAR(2048);
ALTER TABLE qua_web_es_element_detail_index ALTER COLUMN key_words TYPE VARCHAR(2048);
ALTER TABLE qua_data_standard_dic ALTER COLUMN dic_config TYPE VARCHAR(2048);
ALTER TABLE data_mart_asset ALTER COLUMN task_inputs TYPE VARCHAR(2048);
ALTER TABLE data_mart_asset ALTER COLUMN task_outs TYPE VARCHAR(2048);
ALTER TABLE data_meta_tag ALTER COLUMN abbreviation DROP NOT NULL;
ALTER TABLE data_model_import_record ALTER COLUMN record_desc DROP NOT NULL;
ALTER TABLE qua_config_master_data_type ALTER COLUMN type_desc DROP NOT NULL;
ALTER TABLE ai_mirror ALTER COLUMN mirror_file_path DROP NOT NULL;
ALTER TABLE ai_mirror ALTER COLUMN mirror_file_name DROP NOT NULL;
ALTER TABLE ai_mirror ALTER COLUMN create_user DROP NOT NULL;
ALTER TABLE ai_mirror ALTER COLUMN create_time DROP NOT NULL;
ALTER TABLE ai_mirror ALTER COLUMN update_user DROP NOT NULL;
ALTER TABLE ai_mirror ALTER COLUMN update_time DROP NOT NULL;

ALTER TABLE ai_notebook ALTER COLUMN desc DROP NOT NULL;

ALTER TABLE ai_model_push ALTER COLUMN hpa DROP NOT NULL;
ALTER TABLE ai_model_push ALTER COLUMN model_config_list DROP NOT NULL;

ALTER TABLE data_meta_tag_type ALTER COLUMN abbreviation DROP NOT NULL;
ALTER TABLE data_meta_tag_type ALTER COLUMN is_quick_tag DROP NOT NULL;

ALTER TABLE qua_data_standard_dic ALTER COLUMN dic_type DROP NOT NULL;
ALTER TABLE qua_data_standard_dic ALTER COLUMN create_time DROP NOT NULL;
ALTER TABLE qua_data_standard_dic ALTER COLUMN create_user DROP NOT NULL;
ALTER TABLE qua_data_standard_dic ALTER COLUMN update_time DROP NOT NULL;
ALTER TABLE qua_data_standard_dic ALTER COLUMN update_user DROP NOT NULL;




CREATE VIEW asset_map_view AS
SELECT t1.id,
       t1.data_name,
       CASE
           WHEN t1.asset_type = 'clickhouse_table' THEN 'clickhouse'
           WHEN t1.asset_type = 'mysql_table' THEN 'mysql'
           WHEN t1.asset_type = 'hive_table' THEN 'hive'
           WHEN t1.asset_type = 'elasticsearch_index' THEN 'elasticsearch'
           ELSE ''
           END                                                                                     AS asset_type,

       COALESCE(ch.table_owner, mysql.table_owner, hive.table_owner, es.index_owner, '')           AS table_owner,
       COALESCE(ch.db_name, mysql.db_name, hive.db_name, es.index_name, '')                        AS db_name,
       COALESCE(ch.key_words, mysql.key_words, hive.key_words, es.key_words, '')                   AS key_words,
       COALESCE(ch.table_name_cn, mysql.table_name_cn, hive.table_name_cn, es.index_name_cn, '')   AS table_name_cn,

       t2.id                                                                                       AS type_id,
       t2.type_name,
       t3.id                                                                                       AS group_id,
       t3.group_name,
       t1.asset_desc,

       (SELECT COUNT(1) FROM data_mart_subscribe WHERE asset_id = t1.id AND approval_progress = 2) AS subscribe_cnt
FROM data_mart_asset t1
         JOIN
     data_mart_type t2 ON t1.type_id = t2.id
         JOIN
     data_mart_group t3 ON t1.group_id = t3.id
         LEFT JOIN
     qua_web_ch_element_detail_table ch ON t1.asset_type = 'clickhouse_table'
         AND ch.element_id = t1.element_id
         AND ch.db_name = t1.db_name
         AND ch.table_name = t1.table_name
         LEFT JOIN
     qua_web_mysql_element_detail_table mysql ON t1.asset_type = 'mysql_table'
         AND mysql.element_id = t1.element_id
         AND mysql.db_name = t1.db_name
         AND mysql.table_name = t1.table_name
         LEFT JOIN
     qua_web_hive_element_detail_table hive ON t1.asset_type = 'hive_table'
         AND hive.element_id = t1.element_id
         AND hive.db_name = t1.db_name
         AND hive.table_name = t1.table_name
         LEFT JOIN
     qua_web_es_element_detail_index es ON t1.asset_type = 'elasticsearch_index'
         AND es.element_id = t1.element_id
         AND es.index_name = t1.index_name
WHERE t1.publish_status = 1
  AND t1.approval_status = 1;