CREATE TABLE IF NOT EXISTS data_quota_task
(
	id SERIAL  PRIMARY KEY,
	task_name VARCHAR(200) NOT NULL ,
	task_type SMALLINT DEFAULT 0,
	flow_id BIGINT DEFAULT 0,
	task_desc VARCHAR(200),
	tenant_id INTEGER,
	create_user VARCHAR(50) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	update_user VARCHAR(50) NOT NULL ,
	update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS data_quota_task_input
(
	id SERIAL  PRIMARY KEY,
	task_id BIGINT NOT NULL ,
	asset_type VARCHAR(100) NOT NULL ,
	element_id BIGINT NOT NULL ,
	db_name VARCHAR(50) NOT NULL ,
	table_name VARCHAR(100) NOT NULL ,
	table_id BIGINT NOT NULL
);

CREATE TABLE IF NOT EXISTS data_quota_task_out
(
	id SERIAL  PRIMARY KEY,
	task_id BIGINT NOT NULL ,
	asset_type VARCHAR(100) NOT NULL ,
	element_id BIGINT NOT NULL ,
	db_name VARCHAR(50) NOT NULL ,
	table_name VARCHAR(100) NOT NULL ,
	create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	table_id BIGINT NOT NULL
);

COMMENT ON COLUMN data_quota_task.task_name IS '任务名称';
COMMENT ON COLUMN data_quota_task.task_type IS '任务类型：0离线，1实时';
COMMENT ON COLUMN data_quota_task.flow_id IS '数澜任务 ID';
COMMENT ON COLUMN data_quota_task.task_desc IS '任务描述';
COMMENT ON COLUMN data_quota_task_input.task_id IS '任务ID';
COMMENT ON COLUMN data_quota_task_input.asset_type IS '资产类型:clickhouse_table,hive_db,hive_table,mysql_table';
COMMENT ON COLUMN data_quota_task_input.element_id IS '元数据 ID';
COMMENT ON COLUMN data_quota_task_input.db_name IS '库名';
COMMENT ON COLUMN data_quota_task_input.table_name IS '表名称';
COMMENT ON COLUMN data_quota_task_input.table_id IS '表ID';
COMMENT ON COLUMN data_quota_task_out.task_id IS '任务ID';
COMMENT ON COLUMN data_quota_task_out.asset_type IS '资产类型:clickhouse_table,hive_db,hive_table,mysql_table';
COMMENT ON COLUMN data_quota_task_out.element_id IS '元数据 ID';
COMMENT ON COLUMN data_quota_task_out.db_name IS '库名';
COMMENT ON COLUMN data_quota_task_out.table_name IS '表名称';
COMMENT ON COLUMN data_quota_task_out.table_id IS '表ID';
