CREATE TABLE IF NOT EXISTS data_lifecycle_archived
(
	id SERIAL  PRIMARY KEY,
	tenant_id BIGINT NOT NULL ,
	column_info TEXT,
	offline_id BIGINT,
	asset_id BIGINT,
	partition_date VARCHAR(100) NOT NULL ,
	file_name VARCHAR(1024) NOT NULL ,
	file_path VARCHAR(1024) NOT NULL ,
	archived_time TIMESTAMP,
	data_size BIGINT,
	row_count BIGINT,
	status VARCHAR(50),
	days_to_destroy INTEGER,
	destroyed_time TIMESTAMP
);

CREATE TABLE IF NOT EXISTS data_lifecycle_archived_log
(
	id SERIAL  PRIMARY KEY,
	tenant_id BIGINT NOT NULL ,
	asset_id BIGINT NOT NULL ,
	archived_id BIGINT,
	log_type INTEGER,
	from_status INTEGER NOT NULL ,
	to_status INTEGER NOT NULL ,
	asset_type VARCHAR(255) NOT NULL ,
	partition_date VARCHAR(100) NOT NULL ,
	earliest_data_time VARCHAR(100),
	latest_data_time VARCHAR(100),
	create_time TIMESTAMP,
	execution_begin_time TIMESTAMP,
	execution_end_time TIMESTAMP,
	expected_data_volume BIGINT DEFAULT 0,
	processed_data_volume BIGINT DEFAULT 0,
	status VARCHAR(50) NOT NULL ,
	message TEXT
);

CREATE TABLE IF NOT EXISTS data_lifecycle_assets
(
	id SERIAL  PRIMARY KEY,
	tenant_id BIGINT NOT NULL ,
	asset_type VARCHAR(255) NOT NULL ,
	database_name VARCHAR(255) NOT NULL ,
	table_name VARCHAR(255) NOT NULL ,
	partition_by VARCHAR(255),
	partition_column VARCHAR(255),
	asset_desc VARCHAR(1000),
	data_size BIGINT,
	row_count BIGINT,
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100)
);

CREATE TABLE IF NOT EXISTS data_lifecycle_offline
(
	id SERIAL  PRIMARY KEY,
	tenant_id BIGINT NOT NULL ,
	asset_id BIGINT,
	column_info TEXT,
	partition_date VARCHAR(100) NOT NULL ,
	database_name VARCHAR(255) NOT NULL ,
	table_name VARCHAR(255) NOT NULL ,
	data_size BIGINT,
	row_count BIGINT,
	offline_time TIMESTAMP
);

CREATE TABLE IF NOT EXISTS data_lifecycle_offline_log
(
	id SERIAL  PRIMARY KEY,
	tenant_id BIGINT NOT NULL ,
	asset_id BIGINT NOT NULL ,
	offline_id BIGINT,
	from_status INTEGER NOT NULL ,
	to_status INTEGER NOT NULL ,
	asset_type VARCHAR(255) NOT NULL ,
	partition_date VARCHAR(100) NOT NULL ,
	earliest_data_time VARCHAR(100),
	latest_data_time VARCHAR(100),
	create_time TIMESTAMP,
	execution_begin_time TIMESTAMP,
	execution_end_time TIMESTAMP,
	expected_data_volume BIGINT DEFAULT 0,
	processed_data_volume BIGINT DEFAULT 0,
	status VARCHAR(50) NOT NULL ,
	message TEXT
);

CREATE TABLE IF NOT EXISTS data_lifecycle_policies
(
	id SERIAL  PRIMARY KEY,
	asset_id BIGINT NOT NULL ,
	policy_type VARCHAR(50) NOT NULL ,
	days_to_trigger INTEGER NOT NULL ,
	target_database VARCHAR(255),
	target_table VARCHAR(255),
	days_to_destroy INTEGER,
	permanent_storage INTEGER DEFAULT 0,
	create_time TIMESTAMP,
	create_user VARCHAR(100),
	update_time TIMESTAMP,
	update_user VARCHAR(100),
	hive_ddl text
);

CREATE TABLE IF NOT EXISTS data_lifecycle_recovery_log
(
	id SERIAL  PRIMARY KEY,
	tenant_id BIGINT NOT NULL ,
	asset_id BIGINT NOT NULL ,
	original_id BIGINT NOT NULL ,
	asset_type VARCHAR(255) NOT NULL ,
	from_status INTEGER NOT NULL ,
	to_status INTEGER NOT NULL ,
	recovery_to_asset_type VARCHAR(100) NOT NULL ,
	recovery_to_database varchar(100),
    recovery_to_table varchar(100),
	partition_date VARCHAR(100) NOT NULL ,
	create_time TIMESTAMP,
	execution_begin_time TIMESTAMP,
	execution_end_time TIMESTAMP,
	earliest_data_time VARCHAR(100),
	latest_data_time VARCHAR(100),
	expected_data_volume BIGINT DEFAULT 0,
	processed_data_volume BIGINT DEFAULT 0,
	status VARCHAR(50) NOT NULL ,
	message TEXT
);



COMMENT ON COLUMN data_lifecycle_archived.id IS '主键ID';
COMMENT ON COLUMN data_lifecycle_archived.tenant_id IS '租户ID';
COMMENT ON COLUMN data_lifecycle_archived.column_info IS '列信息';
COMMENT ON COLUMN data_lifecycle_archived.offline_id IS '离线ID，关联离线表';
COMMENT ON COLUMN data_lifecycle_archived.asset_id IS '原始资产ID，关联assets表';
COMMENT ON COLUMN data_lifecycle_archived.partition_date IS '分区日期';
COMMENT ON COLUMN data_lifecycle_archived.file_name IS '归档文件名称';
COMMENT ON COLUMN data_lifecycle_archived.file_path IS '归档文件路径';
COMMENT ON COLUMN data_lifecycle_archived.archived_time IS '归档时间';
COMMENT ON COLUMN data_lifecycle_archived.data_size IS '数据量（字节）';
COMMENT ON COLUMN data_lifecycle_archived.row_count IS '数据条数';
COMMENT ON COLUMN data_lifecycle_archived.status IS '状态：已归档或已销毁, 取值：ARCHIVED,DESTROYED';
COMMENT ON COLUMN data_lifecycle_archived.days_to_destroy IS '存储时限，归档后销毁天数';
COMMENT ON COLUMN data_lifecycle_archived.destroyed_time IS '销毁时间';
COMMENT ON COLUMN data_lifecycle_archived_log.id IS '主键ID';
COMMENT ON COLUMN data_lifecycle_archived_log.tenant_id IS '租户ID';
COMMENT ON COLUMN data_lifecycle_archived_log.asset_id IS '资产ID，关联assets表';
COMMENT ON COLUMN data_lifecycle_archived_log.archived_id IS '归档ID，关联归档表';
COMMENT ON COLUMN data_lifecycle_archived_log.log_type IS '日志类型，1-归档日志，2-销毁日志';
COMMENT ON COLUMN data_lifecycle_archived_log.from_status IS '来源状态：1-在线，2-离线';
COMMENT ON COLUMN data_lifecycle_archived_log.to_status IS '目标状态：3-归档，4-销毁';
COMMENT ON COLUMN data_lifecycle_archived_log.asset_type IS '资产类型：ClickHouse、ES、Hive';
COMMENT ON COLUMN data_lifecycle_archived_log.partition_date IS '分区日期';
COMMENT ON COLUMN data_lifecycle_archived_log.earliest_data_time IS '最早数据时间';
COMMENT ON COLUMN data_lifecycle_archived_log.latest_data_time IS '最晚数据时间';
COMMENT ON COLUMN data_lifecycle_archived_log.execution_begin_time IS '数据执行开始时间';
COMMENT ON COLUMN data_lifecycle_archived_log.execution_end_time IS '数据执行结束时间';
COMMENT ON COLUMN data_lifecycle_archived_log.expected_data_volume IS '应处理数据量';
COMMENT ON COLUMN data_lifecycle_archived_log.processed_data_volume IS '处理数据量';
COMMENT ON COLUMN data_lifecycle_archived_log.status IS '执行状态，取值：IN_PROGRESS、SUCCESS、FAILURE';
COMMENT ON COLUMN data_lifecycle_archived_log.message IS '日志消息，记录详情或错误信息';
COMMENT ON COLUMN data_lifecycle_assets.id IS '主键ID';
COMMENT ON COLUMN data_lifecycle_assets.tenant_id IS '租户ID';
COMMENT ON COLUMN data_lifecycle_assets.asset_type IS '资产类型：ClickHouse、ES、Hive、MySQL';
COMMENT ON COLUMN data_lifecycle_assets.database_name IS '库名';
COMMENT ON COLUMN data_lifecycle_assets.table_name IS '表名';
COMMENT ON COLUMN data_lifecycle_assets.partition_by IS '分区类型，toYear,toYYYYMM,toYYYYMMDD,toYYYYMMDDhh';
COMMENT ON COLUMN data_lifecycle_assets.partition_column IS '分区字段';
COMMENT ON COLUMN data_lifecycle_assets.asset_desc IS '资产描述';
COMMENT ON COLUMN data_lifecycle_assets.data_size IS '数据量（字节）';
COMMENT ON COLUMN data_lifecycle_assets.row_count IS '数据条数';
COMMENT ON COLUMN data_lifecycle_assets.create_time IS '创建时间';
COMMENT ON COLUMN data_lifecycle_assets.create_user IS '创建人';
COMMENT ON COLUMN data_lifecycle_assets.update_time IS '更新时间';
COMMENT ON COLUMN data_lifecycle_assets.update_user IS '更新人';
COMMENT ON COLUMN data_lifecycle_offline.id IS '主键ID';
COMMENT ON COLUMN data_lifecycle_offline.tenant_id IS '租户ID';
COMMENT ON COLUMN data_lifecycle_offline.asset_id IS '资产ID，关联assets表';
COMMENT ON COLUMN data_lifecycle_offline.column_info IS '列信息';
COMMENT ON COLUMN data_lifecycle_offline.partition_date IS '分区日期';
COMMENT ON COLUMN data_lifecycle_offline.database_name IS '库名';
COMMENT ON COLUMN data_lifecycle_offline.table_name IS '表名';
COMMENT ON COLUMN data_lifecycle_offline.data_size IS '数据量（字节）';
COMMENT ON COLUMN data_lifecycle_offline.row_count IS '数据条数';
COMMENT ON COLUMN data_lifecycle_offline.offline_time IS '离线时间';
COMMENT ON COLUMN data_lifecycle_offline_log.id IS '主键ID';
COMMENT ON COLUMN data_lifecycle_offline_log.tenant_id IS '租户ID';
COMMENT ON COLUMN data_lifecycle_offline_log.asset_id IS '资产ID，关联assets表';
COMMENT ON COLUMN data_lifecycle_offline_log.offline_id IS '离线ID，关联离线表';
COMMENT ON COLUMN data_lifecycle_offline_log.from_status IS '来源状态：1-在线，2-离线';
COMMENT ON COLUMN data_lifecycle_offline_log.to_status IS '目标状态：1-在线，2-离线';
COMMENT ON COLUMN data_lifecycle_offline_log.asset_type IS '资产类型：ClickHouse、ES、Hive';
COMMENT ON COLUMN data_lifecycle_offline_log.partition_date IS '分区日期';
COMMENT ON COLUMN data_lifecycle_offline_log.earliest_data_time IS '最早数据时间';
COMMENT ON COLUMN data_lifecycle_offline_log.latest_data_time IS '最晚数据时间';
COMMENT ON COLUMN data_lifecycle_offline_log.execution_begin_time IS '数据执行开始时间';
COMMENT ON COLUMN data_lifecycle_offline_log.execution_end_time IS '数据执行结束时间';
COMMENT ON COLUMN data_lifecycle_offline_log.expected_data_volume IS '应处理数据量';
COMMENT ON COLUMN data_lifecycle_offline_log.processed_data_volume IS '处理数据量';
COMMENT ON COLUMN data_lifecycle_offline_log.status IS '执行状态，取值：IN_PROGRESS,SUCCESS,FAILURE';
COMMENT ON COLUMN data_lifecycle_offline_log.message IS '日志消息，记录详情或错误信息';
COMMENT ON COLUMN data_lifecycle_policies.id IS '主键ID';
COMMENT ON COLUMN data_lifecycle_policies.asset_id IS '资产ID，关联assets表';
COMMENT ON COLUMN data_lifecycle_policies.policy_type IS '策略类型：转离线或转归档, 取值：TO_OFFLINE,TO_ARCHIVE';
COMMENT ON COLUMN data_lifecycle_policies.days_to_trigger IS '触发策略的天数';
COMMENT ON COLUMN data_lifecycle_policies.target_database IS '目标库名（转离线策略时为Hive库名）';
COMMENT ON COLUMN data_lifecycle_policies.target_table IS '目标表名（转离线策略时为Hive表名）';
COMMENT ON COLUMN data_lifecycle_policies.days_to_destroy IS '归档后销毁天数（转归档策略时有效）';
COMMENT ON COLUMN data_lifecycle_policies.permanent_storage IS '是否永久存储, 0-否，1-是';
COMMENT ON COLUMN data_lifecycle_policies.create_time IS '创建时间';
COMMENT ON COLUMN data_lifecycle_policies.create_user IS '创建人';
COMMENT ON COLUMN data_lifecycle_policies.update_time IS '更新时间';
COMMENT ON COLUMN data_lifecycle_policies.update_user IS '更新人';
COMMENT ON COLUMN data_lifecycle_recovery_log.id IS '主键ID';
COMMENT ON COLUMN data_lifecycle_recovery_log.tenant_id IS '租户ID';
COMMENT ON COLUMN data_lifecycle_recovery_log.asset_id IS '资产ID，关联assets表';
COMMENT ON COLUMN data_lifecycle_recovery_log.original_id IS '原始ID，比如离线ID、归档ID';
COMMENT ON COLUMN data_lifecycle_recovery_log.asset_type IS '资产类型：Hive、归档文件';
COMMENT ON COLUMN data_lifecycle_recovery_log.from_status IS '来源状态：1-在线，2-离线，3-归档，4-销毁';
COMMENT ON COLUMN data_lifecycle_recovery_log.to_status IS '目标状态：1-在线，2-离线，3-归档，4-销毁';
COMMENT ON COLUMN data_lifecycle_recovery_log.recovery_to_asset_type IS '恢复库类型';
COMMENT ON COLUMN data_lifecycle_recovery_log.partition_date IS '分区日期';
COMMENT ON COLUMN data_lifecycle_recovery_log.execution_begin_time IS '数据执行开始时间';
COMMENT ON COLUMN data_lifecycle_recovery_log.execution_end_time IS '数据执行结束时间';
COMMENT ON COLUMN data_lifecycle_recovery_log.earliest_data_time IS '最早数据时间';
COMMENT ON COLUMN data_lifecycle_recovery_log.latest_data_time IS '最晚数据时间';
COMMENT ON COLUMN data_lifecycle_recovery_log.expected_data_volume IS '应处理数据量';
COMMENT ON COLUMN data_lifecycle_recovery_log.processed_data_volume IS '处理数据量';
COMMENT ON COLUMN data_lifecycle_recovery_log.status IS '执行状态，取值：IN_PROGRESS、SUCCESS、FAILURE';
COMMENT ON COLUMN data_lifecycle_recovery_log.message IS '日志消息，记录详情或错误信息';


CREATE INDEX IDX_OFFLINE_ID_IDX ON data_lifecycle_archived(offline_id);
CREATE INDEX IDX_ORIGINAL_ASSET_ID_IDX ON data_lifecycle_archived(asset_id);
CREATE INDEX IDX_TENANT_STATUS_IDX ON data_lifecycle_archived(tenant_id,status);
CREATE INDEX IDX_ARCHIVED_ID_IDX ON data_lifecycle_archived_log(archived_id);
CREATE INDEX IDX_ASSET_ID_IDX ON data_lifecycle_archived_log(asset_id);
CREATE UNIQUE INDEX UK_TENANT_ASSET_IDX ON data_lifecycle_assets(tenant_id,asset_type,database_name,table_name);
CREATE INDEX IDX_TENANT_TYPE_IDX ON data_lifecycle_assets(tenant_id,asset_type);
CREATE INDEX IDX_ASSET_ID_1_IDX ON data_lifecycle_offline(asset_id);
CREATE INDEX IDX_TENANT_OFFLINE_IDX ON data_lifecycle_offline(tenant_id);
CREATE INDEX IDX_ASSET_ID_2_IDX ON data_lifecycle_offline_log(asset_id);
CREATE INDEX IDX_OFFLINE_ID_3_IDX ON data_lifecycle_offline_log(offline_id);
CREATE INDEX IDX_ASSET_ID_4_IDX ON data_lifecycle_policies(asset_id);
CREATE INDEX IDX_ASSET_ID_5_IDX ON data_lifecycle_recovery_log(asset_id);
CREATE INDEX IDX_ORIGINAL_ID_IDX ON data_lifecycle_recovery_log(original_id);

