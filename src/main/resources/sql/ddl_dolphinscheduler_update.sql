drop table if exists t_ds_datasource_kbs;
create table t_ds_datasource_kbs
(
    id                          int auto_increment comment 'key'
        primary key,
    datasource_id               int          not null comment '数据源 id',
    kbs_version                 varchar(255) null comment '版本:华为(HW),开源(KY),亚信(YX)',
    kbs_principal               varchar(255) null comment '主体',
    kbs_keytab_origin_file_name varchar(255) null comment 'keytab源文件名称',
    kbs_keytab_file_path        varchar(255) null comment 'keytab文件路径',
    kbs_krb_origin_file_name    varchar(255) null comment 'krb源文件名称',
    kbs_krb_file_path           varchar(255) null comment 'krb文件路径',
    kbs_jaas_origin_file_name   varchar(255) null comment 'jaas源文件名称',
    kbs_jaas_file_path          varchar(255) null comment 'jaas文件路径'
);

drop table if exists t_ds_process_definition_lock;
create table t_ds_process_definition_lock
(
    id                      int auto_increment comment 'key'
        primary key,
    process_definition_code bigint   not null comment 'encoding',
    user_id                 int      not null comment 'process definition creator id',
    create_time             datetime not null comment 'create time'
);

update t_ds_user set user_type = 0;