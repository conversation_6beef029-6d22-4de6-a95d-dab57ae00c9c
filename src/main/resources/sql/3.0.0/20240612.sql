alter table data_dictionary_item add column sen_level_id bigint(20) DEFAULT NULL comment '敏感分级ID';
alter table data_dictionary_item add column sen_level_name varchar(200) null comment '敏感分级名称';
alter table data_dictionary_item add column sen_type_id bigint(20) DEFAULT NULL comment '敏感分类ID';
alter table data_dictionary_item add column sen_type_name varchar(200) null comment '敏感分级名称';

alter table data_dictionary_item add column is_sensitive int DEFAULT '0' COMMENT '是否敏感，默认不是，0-否，1-是';
alter table data_dictionary_item add desensitization_id int null comment '脱敏规则 ID';
ALTER TABLE data_dictionary_item ADD COLUMN is_required INT DEFAULT 0 COMMENT '是否必填，0-否，1-是';
ALTER TABLE data_dictionary_item ADD COLUMN is_encrypted INT DEFAULT 0 COMMENT '是否加密，0-否，1-是';