-- 数据血缘关系相关的表
CREATE TABLE `data_lineage_field_mapping` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `source_element_id` bigint(20) NOT NULL COMMENT '来源元数据ID',
  `source_datasource_type` varchar(64) DEFAULT NULL COMMENT '来源数据源类型',
  `source_database_name` varchar(128) DEFAULT NULL COMMENT '来源数据库名称',
  `source_table_name` varchar(255) DEFAULT NULL COMMENT '来源表名称',
  `source_field_name` varchar(100) DEFAULT NULL COMMENT '来源字段名称',
  `source_unique_id` varchar(100) DEFAULT '' COMMENT '来源元数据唯一字段',
  `target_element_id` bigint(20) NOT NULL COMMENT '目标元数据ID',
  `target_datasource_type` varchar(64) DEFAULT NULL COMMENT '目标数据源类型',
  `target_database_name` varchar(128) DEFAULT NULL COMMENT '目标数据库名称',
  `target_table_name` varchar(255) DEFAULT NULL COMMENT '目标表名称',
  `target_field_name` varchar(100) DEFAULT NULL COMMENT '目标字段名称',
  `target_unique_id` varchar(100) DEFAULT '' COMMENT '目标元数据唯一字段',
  `association_type` int(11) DEFAULT 0 COMMENT '关联类型，0-自动关联，1-手动关联',
  `mapping_type` int(11) DEFAULT NULL COMMENT '映射类型，1-同库映射，2-同表映射，3-自定义映射',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `statistics_date` varchar(50) DEFAULT NULL COMMENT '统计日期',
  `enable_state` int(11) DEFAULT 0 COMMENT '有效状态，0-有效，1-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='元数据-字段映射关系表';


CREATE TABLE `data_lineage_relation_table` (
  `id` varchar(50) NOT NULL,
  `element_id` bigint(20) NOT NULL COMMENT '元数据ID',
  `datasource_type` varchar(64) DEFAULT NULL COMMENT '数据源类型',
  `database_name` varchar(128) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(255) DEFAULT NULL COMMENT '表名称',
  `unique_id` varchar(100) DEFAULT '' COMMENT '元数据唯一字段',
  `cn_name` varchar(100) DEFAULT '' COMMENT '表中文名',
  `owner` varchar(100) DEFAULT '' COMMENT '负责人',
  `table_modify_time` varchar(100) DEFAULT '' COMMENT '表更新时间',
  `upstream_table_num` int(11) DEFAULT 0 COMMENT '上游表数量',
  `downstream_table_num` int(11) DEFAULT 0 COMMENT '下游表数量',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `statistics_date` varchar(50) DEFAULT NULL COMMENT '统计日期',
  `enable_state` int(11) DEFAULT 0 COMMENT '有效状态，0-有效，1-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='元数据-血缘关系表基础信息';

CREATE TABLE `data_lineage_relation_table_column` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `source_table_id` varchar(50) NOT NULL COMMENT '源表id',
  `source_column_name` varchar(255) DEFAULT NULL COMMENT '源字段名称',
  `target_table_id` varchar(50) NOT NULL COMMENT '目标表id',
  `target_column_name` varchar(255) DEFAULT NULL COMMENT '目标字段名称',
  `statistics_date` varchar(50) DEFAULT NULL COMMENT '统计日期',
  `enable_state` int(11) DEFAULT 0 COMMENT '有效状态，0-有效，1-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='元数据-血缘关系表字段信息';

CREATE TABLE `data_lineage_relation_table_reference` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `source_table_id` varchar(50) NOT NULL COMMENT '起点表id',
  `target_table_id` varchar(50) NOT NULL COMMENT '终点表id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='元数据-血缘关系表关联';

CREATE TABLE `data_lineage_relation_table_node` (
  `id` varchar(50) NOT NULL ,
  `pre_table_id` varchar(50) NOT NULL COMMENT '前一个节点表id',
  `next_table_id` varchar(50) DEFAULT NULL COMMENT '后一个节点表id',
  `node_type` int(11) DEFAULT 0 COMMENT '节点类型，1-数据采集，2-数据开发，3-GPL，4-api接口',
  `task_name` varchar(128) DEFAULT NULL COMMENT '任务名称',
  `access_address` varchar(100) DEFAULT NULL COMMENT '接入地址',
  `access_method` varchar(100) DEFAULT NULL COMMENT '接入方式',
  `execution_method` varchar(100) DEFAULT NULL COMMENT '执行方式',
  `task_create_time` varchar(100) DEFAULT NULL COMMENT '任务创建时间',
  `task_create_user` varchar(100) DEFAULT NULL COMMENT '任务创建人',
  `project_name` varchar(255) DEFAULT NULL COMMENT '所属项目',
  `task_type` varchar(255) DEFAULT NULL COMMENT '任务类型',
  `task_status` varchar(255) DEFAULT NULL COMMENT '任务状态',
  `owner` varchar(100) DEFAULT NULL COMMENT '负责人',
  `execution_cycle` varchar(100) DEFAULT NULL COMMENT '执行周期',
  `dataset_classification` varchar(100) DEFAULT NULL COMMENT '数据集分类',
  `execution_result` varchar(100) DEFAULT NULL COMMENT '运行结果',
  `api_name` varchar(255) DEFAULT NULL COMMENT 'api名称',
  `request_type` varchar(100) DEFAULT NULL COMMENT '请求类型',
  `number_of_calls` int(11) DEFAULT 0 COMMENT '调用次数',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `statistics_date` varchar(50) DEFAULT NULL COMMENT '统计日期',
  `enable_state` int(11) DEFAULT 0 COMMENT '有效状态，0-有效，1-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='元数据-血缘关系节点信息';

CREATE TABLE `data_lineage_relation_table_source` (
  `id` varchar(50) NOT NULL,
  `table_id` varchar(50) NOT NULL COMMENT '关联的表id',
  `datasource_type` varchar(64) DEFAULT NULL COMMENT '数据源类型',
  `database_name` varchar(128) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(128) DEFAULT NULL COMMENT '数据表名',
  `ip` varchar(100) DEFAULT NULL COMMENT 'IP',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `statistics_date` varchar(50) DEFAULT NULL COMMENT '统计日期',
  `enable_state` int(11) DEFAULT 0 COMMENT '有效状态，0-有效，1-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='元数据-血缘关系源头表';

CREATE TABLE `data_lineage_relation_statistic` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `clickhouse_total` bigint(20) DEFAULT 0 COMMENT 'ClickHouse总数',
  `mysql_total` bigint(20) DEFAULT 0 COMMENT 'Mysql总数',
  `hive_total` bigint(20) DEFAULT 0 COMMENT 'Hive总数',
  `es_total` bigint(20) DEFAULT 0 COMMENT 'ES总数',
  `clickhouse_high_value` bigint(20) DEFAULT 0 COMMENT 'ClickHouse高价值资产总数',
  `mysql_high_value` bigint(20) DEFAULT 0 COMMENT 'Mysql高价值资产总数',
  `hive_high_value` bigint(20) DEFAULT 0 COMMENT 'Hive高价值资产总数',
  `es_high_value` bigint(20) DEFAULT 0 COMMENT 'ES高价值资产总数',
  `table_citation_count` int(11) DEFAULT 0 COMMENT '表引用次数，超过该次数的表，才会被统计',
  `clickhouse_low_value` bigint(20) DEFAULT 0 COMMENT 'ClickHouse孤岛资产总数',
  `mysql_low_value` bigint(20) DEFAULT 0 COMMENT 'Mysql孤岛资产总数',
  `hive_low_value` bigint(20) DEFAULT 0 COMMENT 'Hive孤岛资产总数',
  `es_low_value` bigint(20) DEFAULT 0 COMMENT 'ES孤岛资产总数',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `statistics_date` varchar(50) DEFAULT NULL COMMENT '统计日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='元数据-血缘关系资产统计表';

CREATE TABLE `data_lineage_relation_table_island` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) NOT NULL COMMENT '元数据ID',
  `datasource_type` varchar(64) DEFAULT NULL COMMENT '数据源类型',
  `database_name` varchar(128) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(255) DEFAULT NULL COMMENT '表名称',
  `unique_id` varchar(100) DEFAULT '' COMMENT '元数据唯一字段',
  `cn_name` varchar(100) DEFAULT '' COMMENT '表中文名',
  `owner` varchar(100) DEFAULT '' COMMENT '负责人',
  `table_modify_time` datetime DEFAULT NULL COMMENT '表更新时间',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `statistics_date` varchar(50) DEFAULT NULL COMMENT '统计日期',
  `enable_state` int(11) DEFAULT 0 COMMENT '有效状态，0-有效，1-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='元数据-血缘关系孤岛数据';

CREATE INDEX data_lineage_relation_table_unique_id_IDX USING BTREE ON data_lineage_relation_table (unique_id);
CREATE INDEX data_lineage_relation_table_tenant_id_IDX USING BTREE ON data_lineage_relation_table (tenant_id);

CREATE INDEX idx_task_id ON qua_monitor_result(task_id);
CREATE INDEX idx_unique_id ON data_lineage_relation_table_island(unique_id);
CREATE INDEX idx_source_table_id ON data_lineage_relation_table_reference(source_table_id);
CREATE INDEX idx_source_unique_id ON data_meta_relation(source_unique_id);

CREATE INDEX data_lineage_relation_table_source_statistics_date_IDX USING BTREE ON data_lineage_relation_table_source (statistics_date);
CREATE INDEX data_lineage_relation_table_statistics_date_IDX USING BTREE ON data_lineage_relation_table (statistics_date);
CREATE INDEX data_lineage_relation_table_node_statistics_date_IDX USING BTREE ON data_lineage_relation_table_node (statistics_date);
CREATE INDEX data_lineage_relation_table_island_statistics_date_IDX USING BTREE ON data_lineage_relation_table_island (statistics_date);
CREATE INDEX data_lineage_relation_table_column_source_table_id_IDX USING BTREE ON data_lineage_relation_table_column (source_table_id,source_column_name,target_table_id,target_column_name,statistics_date);

