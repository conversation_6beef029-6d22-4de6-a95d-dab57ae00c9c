CREATE TABLE `etl_task_monitor` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `source_name` varchar(512) DEFAULT NULL COMMENT '任务名称',
  `flow_id` bigint(20) NOT NULL COMMENT 'flowId',
  `source_type` varchar(255) DEFAULT NULL COMMENT '采集类型',
  `cluster_name` varchar(255) DEFAULT NULL COMMENT '集群名称',
  `task_create_time` datetime DEFAULT NULL COMMENT '任务创建时间',
  `task_status` SMALLINT DEFAULT NULL COMMENT '任务总体状态，0-正常，1-警告，2-故障',
  `task_monitor_time` datetime DEFAULT NULL COMMENT '任务监测时间',
  `disposal_recommendation` varchar(1000) DEFAULT '' COMMENT '处置建议',
  `client_read_status` SMALLINT DEFAULT 0 COMMENT 'client 对端连通性',
  `gate_in_count` bigint(20) DEFAULT 0 COMMENT 'gate采集数据量',
  `gate_in_status` SMALLINT DEFAULT 0 COMMENT 'gate采集数据量状态，0-正常,1-异常',
  `gate_read_size` bigint(20) DEFAULT 0 COMMENT 'gate读缓存',
  `gate_write_size` bigint(20) DEFAULT 0 COMMENT 'gate写缓存',
  `gate_read_write_status` SMALLINT DEFAULT 0 COMMENT 'gate读写缓存状态，0-正常,1-异常',
  `gate_out_count` bigint(20) DEFAULT 0 COMMENT 'gate写kafka数据量',
  `gate_out_status` SMALLINT DEFAULT 0 COMMENT 'gate写Kafka状态，0-正常,1-异常',
  `parser_kafka_lag` bigint(20) DEFAULT 0 COMMENT 'parser消费Kafka积压',
  `parser_kafka_lag_status` SMALLINT DEFAULT 0 COMMENT 'parser消费Kafka积压正常/异常状态',
  `parser_read_size` bigint(20) DEFAULT 0 COMMENT 'parser读缓存',
  `parser_write_size` bigint(20) DEFAULT 0 COMMENT 'parser写缓存',
  `parser_read_write_status` SMALLINT DEFAULT 0 COMMENT 'parser读写缓存状态，0-正常,1-异常',
  `parser_write_status` varchar(10) DEFAULT NULL COMMENT 'parser写入ch、es、kafka正常/异常状态,000,010',
  `parser_ch_out_count` bigint(20) DEFAULT NULL COMMENT '写入CH数据量',
  `parser_ch_out_status` SMALLINT DEFAULT 0 COMMENT '写入CH数据量状态，0-正常,1-异常',
  `parser_ch_rate` bigint(20) DEFAULT NULL COMMENT '写入CH速率，*条/秒',
  `parser_kafka_out_count` bigint(20) DEFAULT NULL COMMENT '写入kafka数据量',
  `parser_kafka_out_status` SMALLINT DEFAULT 0 COMMENT '写入kafka数据量状态，0-正常,1-异常',
  `parser_kafka_rate` bigint(20) DEFAULT NULL COMMENT '写入kafka速率，*条/秒',
  `parser_es_out_count` bigint(20) DEFAULT NULL COMMENT '写入es数据量',
  `parser_es_out_status` SMALLINT DEFAULT 0 COMMENT '写入es数据量状态，0-正常,1-异常',
  `parser_es_rate` bigint(20) DEFAULT NULL COMMENT '写入es速率，*条/秒',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
  `create_time` datetime DEFAULT NULL COMMENT '统计时间',
  `create_user` varchar(128) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(128) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT '采集任务监控统计';

CREATE INDEX etl_task_monitor_flow_id_IDX USING BTREE ON etl_task_monitor (flow_id);
CREATE INDEX etl_task_monitor_tenant_id_IDX USING BTREE ON etl_task_monitor (tenant_id);
CREATE INDEX etl_task_monitor_source_name_IDX USING BTREE ON etl_task_monitor (source_name);
CREATE INDEX etl_task_monitor_task_monitor_time_IDX USING BTREE ON etl_task_monitor (task_monitor_time);
CREATE INDEX etl_task_monitor_create_time_IDX USING BTREE ON etl_task_monitor (create_time);


CREATE TABLE `etl_task_monitor_logmoudle` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `flow_id` varchar(100) NOT NULL COMMENT 'flowId',
  `work_ip` varchar(64) NOT NULL COMMENT 'work部署的地址',
  `work_moudle` varchar(16) NOT NULL COMMENT '类型single, client, relay, gate, parser',
  `work_desc` varchar(512) DEFAULT NULL,
  `open_port` varchar(32) DEFAULT '' COMMENT '开放端口',
  `cluster_name` varchar(255) DEFAULT '' COMMENT '集群名称',
  `cluster_cn_name` varchar(255) DEFAULT NULL COMMENT '集群中文名',
  `parser_topic` varchar(255) DEFAULT NULL COMMENT 'Parser消费的topic名',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
  `etl_task_monitor_id` bigint(20) NOT NULL COMMENT '关联的监控id',
  `logmodule_status` SMALLINT DEFAULT 0 COMMENT 'logmodule当前节点总体状态，0-正常,1-异常',
  `gate_in_status` SMALLINT DEFAULT 0 COMMENT 'gate采集数据量状态，0-正常,1-异常',
  `gate_read_write_status` SMALLINT DEFAULT 0 COMMENT 'gate读写缓存状态，0-正常,1-异常',
  `gate_out_status` SMALLINT DEFAULT 0 COMMENT 'gate写Kafka状态，0-正常,1-异常',
  `parser_kafka_lag_status` SMALLINT DEFAULT 0 COMMENT 'parser消费Kafka积压正常/异常状态',
  `parser_read_write_status` SMALLINT DEFAULT 0 COMMENT 'parser读写缓存状态，0-正常,1-异常',
  `parser_write_status` SMALLINT DEFAULT 0 COMMENT 'parser写入ch、es、kafka正常/异常状态',
  `parser_ch_out_status` SMALLINT DEFAULT 0 COMMENT '写入CH数据量状态，0-正常,1-异常',
  `parser_kafka_out_status` SMALLINT DEFAULT 0 COMMENT '写入kafka数据量状态，0-正常,1-异常',
  `parser_es_out_status` SMALLINT DEFAULT 0 COMMENT '写入es数据量状态，0-正常,1-异常',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT '采集任务监控关联logmodule';

CREATE INDEX etl_task_monitor_logmoudle_create_date_IDX USING BTREE ON etl_task_monitor_logmoudle (create_time);

CREATE TABLE `etl_task_monitor_logmoudle_base` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `flow_id` varchar(100) NOT NULL COMMENT 'flowId',
  `source_ip` varchar(64) NOT NULL COMMENT 'work部署的地址',
  `node_type` varchar(50) NOT NULL COMMENT '类型single, client, relay, gate, parser',
  `writer_type` varchar(50) NOT NULL COMMENT '写入类型',
  `in_out` varchar(50) NOT NULL COMMENT 'in or out',
  `base_count` bigint(20) DEFAULT 0 COMMENT '基线数据量',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
  `statistic_day` varchar(64) NOT NULL COMMENT '统计日期，yyyy-MM-dd',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT '采集任务监控关联的logmodule的基线数据';

CREATE INDEX etl_task_monitor_logmoudle_base_create_date_IDX USING BTREE ON etl_task_monitor_logmoudle_base (create_date);
CREATE INDEX etl_task_monitor_logmoudle_base_statistic_day_IDX USING BTREE ON etl_task_monitor_logmoudle_base (statistic_day);

CREATE TABLE `etl_task_monitor_cluster_overview` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT ,
  `cluster_cn_name` varchar(255) DEFAULT NULL COMMENT '集群中文名',
  `ch_count` bigint(20) DEFAULT 0 COMMENT 'CH条数',
  `kafka_count` bigint(20) DEFAULT 0 COMMENT 'kafka条数',
  `es_count` bigint(20) DEFAULT 0 COMMENT 'es条数',
  `ch_length` bigint(20) DEFAULT 0 COMMENT 'CH磁盘占用',
  `kafka_length` bigint(20) DEFAULT 0 COMMENT 'kafka磁盘占用',
  `es_length` bigint(20) DEFAULT 0 COMMENT 'es磁盘占用',
  `kafka_lag` bigint(20) DEFAULT 0 COMMENT '近1分钟待处理数',
  `epm` bigint(20) DEFAULT 0 COMMENT '每分钟事件数',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT 'ETL集群概况';

CREATE INDEX etl_task_monitor_cluster_overview_create_date_IDX USING BTREE ON etl_task_monitor_cluster_overview (create_date);

