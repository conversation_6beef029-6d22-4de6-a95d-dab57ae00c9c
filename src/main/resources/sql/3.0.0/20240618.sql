CREATE TABLE `data_meta_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `source_element_id` bigint(20) NOT NULL COMMENT '起始元数据ID',
  `source_item_type` varchar(64) DEFAULT NULL COMMENT '起始类型 table-表  field-字段',
  `source_datasource_type` varchar(64) DEFAULT NULL COMMENT '起始数据源类型',
  `source_database_name` varchar(128) DEFAULT NULL COMMENT '起始数据库名称',
  `source_table_name` varchar(255) DEFAULT NULL COMMENT '起始表名称',
  `source_field_name` varchar(100) DEFAULT NULL COMMENT '起始字段名称',
  `source_unique_id` varchar(100) DEFAULT '' COMMENT '起始元数据唯一字段',
  `target_element_id` bigint(20) NOT NULL COMMENT '目标元数据ID',
  `target_item_type` varchar(64) DEFAULT NULL COMMENT '目标类型 table-表  field-字段',
  `target_datasource_type` varchar(64) DEFAULT NULL COMMENT '目标数据源类型',
  `target_database_name` varchar(128) DEFAULT NULL COMMENT '目标数据库名称',
  `target_table_name` varchar(255) DEFAULT NULL COMMENT '目标表名称',
  `target_field_name` varchar(100) DEFAULT NULL COMMENT '目标字段名称',
  `target_unique_id` varchar(100) DEFAULT '' COMMENT '目标元数据唯一字段',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='元数据-关系表';

create table data_dictionary_item_table_trend
(
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `item_id` bigint null,
    `count_day` varchar(100) null comment '统计日期',
    `table_line` bigint DEFAULT 0 comment '数据条数',
    `table_space` decimal(10,2) null comment '占用空间 MB',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='数据字典-数据条数趋势表';