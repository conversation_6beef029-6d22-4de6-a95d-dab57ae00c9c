alter table qua_monitor_task add update_time datetime default current_timestamp not null on update CURRENT_TIMESTAMP comment '更新时间' after create_time;

alter table qua_monitor_result add del_flag int default 0 not null comment '是否删除: 0否，1 是';
alter table qua_monitor_result_detail add del_flag int default 0 not null comment '是否删除: 0否，1 是';
alter table qua_monitor_result_appeal add del_flag int default 0 not null comment '是否删除: 0否，1 是';

alter table qua_monitor_rule_template add column_name varchar(50) null comment '字段名' after table_name;
alter table qua_monitor_rule_template add data_time varchar(10) not null default '0' comment '取数日期: 0昨天，1上周，2上月' after column_name;
alter table qua_monitor_rule_template add time_range_start varchar(20) null comment '时间范围开始 HH:mm:ss' after column_name;
alter table qua_monitor_rule_template add time_range_end varchar(20) null comment '时间范围结束 HH:mm:ss' after time_range_start;

alter table qua_monitor_result_detail add execute_status int default 0 not null comment '执行状态: 0正常，1异常' after is_match;

alter table qua_dync_result add day_line_change bigint default 0 not null comment '行数日变化';
alter table qua_dync_result add forecast_day_line_change bigint default 0 not null comment '预测行数日变化';
alter table qua_dync_result add day_space_change bigint default 0 not null comment '空间日变化';
alter table qua_dync_result add forecast_day_space_change bigint default 0 not null comment '预测空间日变化';

alter table qua_monitor_rule_template modify data_filter varchar(10) default '0' null comment '筛选数据 0-前1000条 1-全部数据';


update qua_internal_model set
                              `name` = '表大小日变化波动率检查',
                              model_desc = '统计表磁盘空间大小日变化波动率，检查波动率是否异常。',
                              model_calc = '
①统计所选表的磁盘空间大小日变化(单位是MB)，得到“表大小日变化值”
②将“表大小日变化值”和“表大小日变化标准值”计算波动率：表大小日变化波动率=|表大小日变化值-表大小日变化标准值|/|表大小日变化标准值|。
③ 表大小日变化标准值可来自“自定义”或“动态阈值”。
④)将“表大小日变化波动率” 与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。
⑤基于“杈重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。
⑥基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。
' where model_code = 'tableSize';
update qua_internal_model set
                              `name` = '表行数日变化波动率检查',
                              model_desc = '统计表磁盘空间大小日变化波动率，检查波动率是否异常。',
                              model_calc = '
①统计所选表的行数日变化，得到“表行数日变化值”
②将“表行数日变化值”和“表行数日变化标准值”计算波动率：表行数日变化波动率=|表行数日变化值-表行数日变化标准值|/|表行数标准日变化量|。
③ 将“表行数日变化波动率” 与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。
④基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。
⑤基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。
' where model_code = 'tableLine';