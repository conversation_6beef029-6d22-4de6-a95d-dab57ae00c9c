CREATE TABLE IF NOT EXISTS qua_internal_model(
    id bigint(20) NOT NULL AUTO_INCREMENT,
    name varchar(200) null comment '模版名称',
    model_code varchar(64) null comment '模板编码',
    model_desc varchar(200) null comment '模版描述',
    model_calc varchar(1024) null comment '模板计算逻辑',
    dimensions varchar(100) null comment '模版维度:及时性 timeliness、唯一性 uniqueness、完整性 integrality、一致性 uniformity、有效性 effectiveness、准确性 precision',
    audit_object varchar(100) null comment '稽核对象: 表table, 字段field',
    request_param varchar(200) null comment '输入参数',
    custom_sql text null comment '自定义sql',
    model_type varchar(2) DEFAULT '1' comment '1-内置 2-自定义 模版类型',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    create_user varchar(128) DEFAULT NULL COMMENT '创建人',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    update_user varchar(128) DEFAULT NULL COMMENT '更新人',
    tenant_id bigint(20) null comment '租户ID',
    PRIMARY KEY (id)
    ) ENGINE=InnoDB AUTO_INCREMENT = 20 DEFAULT CHARSET=utf8mb4 COMMENT='数据质量模版';

create table qua_data_standard_dic
(
    id          bigint auto_increment
        primary key,
    dic_name    varchar(100)  not null comment '字典名称',
    dic_code    varchar(100)  not null comment '字典编号',
    dic_type    int           not null comment '类型，枚举值：1数据长度、2数据类型、3数据精度、4中英文、5空值、6日期、7自然人信息、8自定义',
    remark      varchar(1000) null comment '备注',
    dic_config  json          null comment '字段配置: [{"value":"","cnName":"","remark":""}]',
    create_time timestamp default current_timestamp,
    create_user varchar(60)   not null,
    update_time timestamp default current_timestamp,
    update_user varchar(60)   not null,
    tenant_id   bigint        not null comment '租户ID'
) comment '数据标准字典' charset = utf8mb4;

alter table qua_data_standard_dic modify dic_type varchar(100) not null comment '类型';

alter table qua_data_standard_config add type int default 0 null comment '类型，0数据标准，1数据元';
alter table qua_data_standard_group add type int default 0 null comment '类型，0数据标准，1数据元';

create table qua_dync_threshold
(
    id              int auto_increment,
    job_name        varchar(200)  null,
    source_type     varchar(50)   null comment '数据源类型: mysql,clickhouse,elasticsearch,hive',
    element_id      int           null comment '元数据ID',
    db_name         varchar(100)  null,
    table_name      varchar(100)  null,
    index_name      varchar(100)  null,
    model_type      varchar(50)   null comment '计算模型: MA , ARIMA ,EWMA',
    forecast_target varchar(100)  null comment '预测对象: 行数 line , 空间 space',
    remark          varchar(200)  null comment '备注',
    forecast_value  bigint        null comment '最新预测值',
    status          int default 1 null comment '运行状态: 1运行，0停止',
    tenant_id       int           null,
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    create_user varchar(128) DEFAULT NULL COMMENT '创建人',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    update_user varchar(128) DEFAULT NULL COMMENT '更新人',
    constraint qua_dync_threshold_pk
    primary key (id)
) comment '数据质量动态阈值管理';

create table qua_dync_result
(
    id             int auto_increment,
    dync_id        int                                 not null comment '动态阈值ID',
    result         int       default 1                 not null comment '运行结果: 1成功，0失败',
    fail_error     text                                null comment '失败原因',
    line           bigint                              not null comment '时间行数',
    forecast_line  bigint                              not null comment '预测行数值',
    space          bigint                              not null comment '实际空间  byte单位',
    forecast_space bigint                              not null comment '预测空间值 byte单位',
    create_time    timestamp default current_timestamp not null,
    constraint qua_dync_result_pk
        primary key (id),
    constraint qua_dync_result_qua_dync_threshold_id_fk
        foreign key (dync_id) references qua_dync_threshold (id)
            on update cascade on delete cascade
) comment '动态阈值结果表';


CREATE TABLE IF NOT EXISTS qua_monitor_rule_template(
    id bigint(20) NOT NULL AUTO_INCREMENT,
    template_name varchar(512) NOT NULL COMMENT '模型名称',
    model_id bigint(20) NOT NULL COMMENT '数据源ID',
    table_name  varchar(128) DEFAULT NULL comment '表名/索引名',
    delete_flag varchar(2) DEFAULT '0' COMMENT '删除标识 0-未删除 1-已删除',
    data_filter varchar(2) DEFAULT '0' COMMENT '筛选数据 0-前1000条 1-全部数据',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    create_user varchar(128) DEFAULT NULL COMMENT '创建人',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    update_user varchar(128) DEFAULT NULL COMMENT '更新人',
    tenant_id bigint(20) not null comment '租户ID',
    PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据质量-模型管理';

CREATE TABLE IF NOT EXISTS qua_monitor_weight(
    id bigint(20) NOT NULL AUTO_INCREMENT,
    dim_name varchar(128) NOT NULL COMMENT '维度名称',
    dim_code varchar(128) NOT NULL COMMENT '维度编码',
    weight_value int(20) NOT NULL COMMENT '权重',
    weight_desc varchar(512) NOT NULL COMMENT '备注',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    create_user varchar(128) DEFAULT NULL COMMENT '创建人',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    update_user varchar(128) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据质量-权重管理';

ALTER TABLE qua_monitor_rule ADD COLUMN template_id varchar(128) comment '模型模板ID';
ALTER TABLE qua_monitor_rule ADD COLUMN threshold_value varchar(128) comment '阈值';
ALTER TABLE qua_monitor_rule ADD COLUMN rule_type_id bigint(20) comment '模板类型ID';
ALTER TABLE qua_monitor_rule ADD COLUMN rule_weight integer(20) null comment '权重';

ALTER TABLE qua_monitor_rule MODIFY COLUMN rule_name varchar(128) null comment '规则名称';
ALTER TABLE qua_monitor_rule MODIFY COLUMN model_id bigint(20) null comment '模型ID';

ALTER TABLE qua_monitor_result_detail ADD COLUMN template_id varchar(128) comment '模型模板ID';
ALTER TABLE qua_monitor_result_detail ADD COLUMN rule_type_id bigint(20) comment '规则ID';
ALTER TABLE qua_monitor_result_detail ADD COLUMN is_match varchar(2) comment '匹配结果 0-匹配失败 1-匹配成功';
ALTER TABLE qua_monitor_result_detail ADD COLUMN rule_score integer(20) comment '规则分数';
ALTER TABLE qua_monitor_result_detail ADD COLUMN compare_value varchar(128) comment '稽核值';
ALTER TABLE qua_monitor_result_detail ADD COLUMN threshold_value varchar(128) comment '阈值';

ALTER TABLE qua_monitor_rule ADD COLUMN threshold_operator varchar(20) null comment '阈值比较运算符';

alter table qua_dync_result modify forecast_line bigint null comment '预测行数值';
alter table qua_dync_result modify forecast_space bigint null comment '预测空间值 byte单位';
alter table qua_dync_result modify line bigint null comment '预测空间值 byte单位';
alter table qua_dync_result modify space bigint null comment '预测空间值 byte单位';

ALTER TABLE tb_script_history ADD COLUMN error_msg text COMMENT 'SQL执行错误信息';






