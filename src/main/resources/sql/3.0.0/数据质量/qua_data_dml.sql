DELETE FROM qua_internal_model;
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (1, '及时率检查', 'timely', '统计表中某两时间字段下数据的最大时差,检查其合格比率的是否异常', '①数据源筛选处理后，计算每一行数据中数据时差绝对值:数据时差绝对值=“时间字段一”一“时间字段二”。<br/>②将“数据时差”和“最大时差”比较，大于等于“最大时差”则不符合及时性要求，小于“最大时差”则符合足及时性要求。<br/>③计算总体数据的符合及时性要求的占比，即数据及时率:数据及时率=符合及时性的数据条数/统计数据总条数*100%。<br/>④将“数据及时率”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>⑤基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>⑥基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'timeliness', 'field', '被减时间字段,减去时间字段,时差阈值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (2, '重复率检查', 'repeatRate', '统计表中某一字段下数据的重复比率,检查其重复比率是否异常', '①数据源筛选处理后，计算“统计字段”所有行数据中数据重复率:重复率=重复的数据行数/总的数据行数。<br/>②将“重复率”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>③基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>④基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'uniqueness', 'field', '被减时间字段,减去时间字段,时差阈值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (3, '重复行数检查', 'repeatLine', '统计表中某一字段下数据的重复行数,检查其重复行数是否异常', '①数据源筛选处理后，计算“统计字段”所有行数据中数据重复行数。<br/>②将“重复行数”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>③基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>④基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'uniqueness', 'field', '被检查字段,阈值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (4, '空值率检查', 'nullRate', '统计表中某一字段下数据空值情况,检查其空值比率是否异常', '①数据源筛选处理后，计算“统计字段”所有行数据中数据空值率:空值率=为空的数据行数/总数据行数。<br/>②将“空值率”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>③基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>④基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'integrality', 'field', '被检查字段,阈值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (5, '空值行数检查', 'nullLine', '统计表中某一字段下数据空值情况,检查其空值行数是否异常', '①数据源筛选处理后，计算“统计字段”所有行数据中数据空值行数。<br/>②将“空值行数”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>③基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>④基于“模板打分占比”计算模板得分:模板得分=模板打分占比*100，得分取整数。<br/>', 'integrality', 'field', '被检查字段,阈值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (6, '逻辑通过数检查', 'logicLine', '统计表中某两字段下数据在指定条件下的逻辑判断(AND OR NOT),检查通过逻辑判断数量是否异常', '①数据源筛选处理后，将“字段(第一个)”并与“比较值(第一个)”按照“比较方法(第一个)”进行比较，判断是否通过。<br/>②将“字段(第二个)”并与“比较值(第二个)”按照“比较方法(第二个)”进行比较，判断是否通过。<br/>③上述两个步骤的结果进行“逻辑类型”(AND、OR、NOT)判断，满足则逻辑检查通过，否则逻辑检查不通过。<br/>④计算所有行数据的逻辑检查通过总数与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>', 'uniformity', 'field', '被检查字段,阈值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (7, '逻辑通过率检查', 'logicRate', '统计表中某两字段下数据在指定条件下的逻辑判断(AND OR NOT),检查通过逻辑判断比率是否异常', '①数据源筛选处理后，将“字段(第一个)”并与“比较值(第一个)”按照“比较方法(第一个)”进行比较，判断是否通过。<br/>②将“字段(第二个)”并与“比较值(第二个)”按照“比较方法(第二个)”进行比较，判断是否通过。<br/>③上述两个步骤的结果进行“逻辑类型”(AND、OR、NOT)判断，满足则逻辑检查通过，否则逻辑检查不通过。<br/>④计算所有行数据的逻辑检查通过率:逻辑检查通过率=通过逻辑检查的行数/总检查的行数*100%。<br/>', 'uniformity', 'field', '被检查字段,阈值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (8, '长度规范率检查', 'lengthRate', '统计表中某一字段下数据长度规范情况,检查其符合规范比率是否异常', '①数据源筛选处理后，检查“统计字段”的长度是否规范:等于“标准长度”则通过检查，否则不通过。<br/>②“标准长度”来源于“数据标准”中的数据长度或者自定义的“长度”。<br/>③计算“统计字段”所有行数据中长度规范率:长度规范率=通过检查的数据行数/总数据行数。<br/>④将“长度规范率”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>', 'effectiveness', 'field', '被检查字段,阈值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (9, '长度规范行数检查', 'lengthLine', '统计表中某一字段下数据长度规范情况,检查其符合规范行数是否异常', '①数据源筛选处理后，检查“统计字段”的长度是否规范:等于“标准长度”则通过检查，否则不通过。<br/>②“标准长度”来源于“数据标准”中的数据长度或者自定义的“长度”。<br/>③计算“统计字段”所有行数据中满足规范的行数,与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>④基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>', 'effectiveness', 'field', '被检查字段,阈值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (10, '格式规范率检查', 'formatRate', '统计表中某一字段下数据格式规范情况，检查其符合规范比率是否异常', '①数据源筛选处理后，检查“统计字段”的格式是否规范:等于“标准格式”要求则通过检查，否则不通过。<br/>②“标准格式”来源于“数据标准”中的数据格式或者“自定义格式”。<br/>③计算“统计字段”所有行数据的格式规范率:格式规范率=通过检查的数据行数/总数据行数。<br/>④将“格式规范率”与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>', 'effectiveness', 'field', '被检查字段,阈值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (11, '格式规范行数检查', 'formatLine', '统计表中某一字段下数据格式规范情况,检查其符合规范行数是否异常', '①数据源筛选处理后，检查“统计字段”的格式是否规范:等于“标准格式”要求则通过检查，否则不通过。<br/>②“标准格式”来源于“数据标准”中的数据格式或者“自定义格式”。<br/>③计算“统计字段”所有行数据的满足规范总行数,与“阈值”按照“阈值比较方法”进行比较，满足则异常，否则正常。<br/>④基于“权重”计算本模板打分占比:模板打分占比=本模板权重/所有模板权重和*100%。<br/>', 'effectiveness', 'field', '被检查字段,阈值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (12, '值域通过数检查', 'rangeLine', '统计表中某一字段下数据满足值域的情况,检查其合格的数量是否异常', '①数据源筛选处理后，将每一行数据与“值域”比较，满足则通过，否则不通过。<br/>②值域来自数据标准下的数据字典或者“自定义”。<br/>③计算所有数据的通过数量,与“阈值”按照“阈值比较方去”进行比较，满足则异常，否则正常。<br/>④基于“权重”计算本模板打分占比:模板打分占比=本板权重/所有模板权重和*100%。<br/>', 'precision', 'field', '规范类型,被检查字段,统计结果类型,阈值值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (13, '值域通过率检查', 'rangeRate', '统计表中某一字段下数据满足值域的情况,检查其合格比率是否异常', '①数据源筛选处理后，将每一行数据与“值域”比较，满足则通过，否则不通过。<br/>②值域来自数据标准下的数据字典或者“自定义”。<br/>③计算所有数据的通过率:通过率=通过的行数/数据检查总行数*100%。<br/>④将“通过率”与“阈值”按照“阈值比较方法”进行比较,满足则异常，否则正常。<br/>', 'precision', 'field', '规范类型,被检查字段,统计结果类型,阈值值');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (14, '表大小波动率检查', 'tableSize', '统计表磁盘空间大小波动率,检查波动率是否异常', '①统计所选表的实际磁盘空间大小(单位是MB)，得到表磁盘空间大小。<br/>②将“表磁盘空间大小”和“磁盘空间标准值”计算波动率表大小波动率=(表的磁盘空间大小-表磁盘空间标准值)/表磁盘空间标准值。<br/>③表磁盘空间标准值可来自“自定义”或“动态阈值”。<br/>', 'integrality', 'table', '被检查的表,波动类型,引用的动态阈值基线');
INSERT INTO qua_internal_model (id, name, model_code, model_desc, model_calc, dimensions, audit_object, request_param) VALUES (15, '表行数波动率检查', 'tableLine', '统计表行数波动率,检查波动率是否异常', '①统计所选表的行数，得到“表行数”。<br/>②将“表行数”和“表行数标准值”计算波动率:值。表行数波动率=(表行数-表行数标准值)/表行数标准。<br/>③将“表行数波动率”与“阈值”按照“阈值比较方法”进行北较，:满足则异常，否则正常。<br/>', 'integrality', 'table', '被检查的表,波动类型,引用的动态阈值基线');


DELETE FROM qua_monitor_weight;
INSERT INTO qua_monitor_weight (dim_name, dim_code, weight_value, weight_desc, create_time,update_time) VALUES ('一致性', 'uniformity', 100, '', now(), now());
INSERT INTO qua_monitor_weight (dim_name, dim_code, weight_value, weight_desc, create_time,update_time) VALUES ('及时性', 'timeliness', 100, '', now(), now());
INSERT INTO qua_monitor_weight (dim_name, dim_code, weight_value, weight_desc, create_time,update_time) VALUES ('唯一性', 'uniqueness', 100, '', now(), now());
INSERT INTO qua_monitor_weight (dim_name, dim_code, weight_value, weight_desc, create_time,update_time) VALUES ('有效性', 'effectiveness', 100, '', now(), now());
INSERT INTO qua_monitor_weight (dim_name, dim_code, weight_value, weight_desc, create_time,update_time) VALUES ('完整性', 'integrality', 100, '', now(), now());
INSERT INTO qua_monitor_weight (dim_name, dim_code, weight_value, weight_desc, create_time,update_time) VALUES ('准确性', 'precision', 100, '', now(), now());


