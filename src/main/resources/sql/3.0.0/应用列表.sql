CREATE TABLE application_list (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    app_name VARCHAR(255) NOT NULL COMMENT '应用名称',
    app_code VARCHAR(255) NOT NULL COMMENT '应用编码',
    app_id VARCHAR(255) NOT NULL COMMENT '应用 id',
    account VARCHAR(255) NOT NULL COMMENT '账号',
    user_name VARCHAR(255) NOT NULL COMMENT '用户名',
    user_phone VARCHAR(20) NOT NULL COMMENT '用户手机号',
    email VARCHAR(255) NOT NULL COMMENT '邮箱',
    code_name VARCHAR(255) NOT NULL COMMENT '代码名称',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    create_user varchar(100) DEFAULT NULL COMMENT '创建人',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    update_user varchar(100) DEFAULT NULL COMMENT '更新人',
    tenant_id bigint(20) NOT NULL COMMENT '租户ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用列表';