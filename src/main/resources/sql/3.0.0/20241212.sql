ALTER TABLE data_lake.data_dictionary_item MODIFY COLUMN dict_id bigint(20) NULL COMMENT '字典ID';
ALTER TABLE data_lake.data_dictionary_item MODIFY COLUMN category_id bigint(20) NULL COMMENT '字典分类ID';
ALTER TABLE data_lake.data_dictionary_item MODIFY COLUMN sort int(11) DEFAULT 0 NULL COMMENT '排序字段';

CREATE INDEX qua_asset_table_data_statistics_element_id_IDX USING BTREE ON qua_asset_table_data_statistics (element_id,db_name,table_name,datasource_type);
