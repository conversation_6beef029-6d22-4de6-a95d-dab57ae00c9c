CREATE TABLE `intelligence_query_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `query_condition` varchar(512) NOT NULL COMMENT '查询条件',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='ip信息查询历史';