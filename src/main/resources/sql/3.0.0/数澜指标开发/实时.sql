drop view if exists stream_task_view;
create view stream_task_view as
select a.workspace_id,
       g.name                as workspace_name,
       a.project_id,
       h.project_code,
       a.cata_id,
       b.cata_name,
       a.flow_id,
       c.name                as flow_name,
       a.task_id,
       a.task_name,
       a.ctime,
       j.user_name as create_user,
       a.task_type,
       FROM_BASE64(d.source) as source,
       e.storage_type,
       e.parameter           as storage_parameter,
       e.storage_id,
       f.parameter
from stream_backup.bas_task a
         left join uac_backup.uac_user j on a.create_user_id = j.user_id
         left join stream_backup.bas_cata b
                   on a.cata_id = b.cata_id
         left join project_backup.bas_business c
                   on a.flow_id = c.business_id
         left join (select *
                    from stream_backup.rel_task_config
                    where rid in (select t.max_rid
                                  from (select max(rid) max_rid, task_id
                                        from stream_backup.rel_task_config
                                        where source is not null
                                          and invalid = 0
                                        group by task_id) t)) d
                   on a.task_id = d.task_id
         left join (select *
                    from stream_backup.rel_task_storage
                    where rid in (select t.max_rid
                                  from (select max(rid) max_rid, task_id
                                        from stream_backup.rel_task_storage
                                        where invalid = 0
                                        group by task_id) t)) e
                   on a.task_id = e.task_id
         left join resource_backup.bas_storage f
                   on e.storage_id = f.storage_id
         left join project_backup.bas_workspace g
                   on a.workspace_id = g.workspace_id
         left join project_backup.bas_project h on a.project_id = h.project_id
         left join project_backup.rel_workspace_hadoop_cluster i on a.workspace_id = i.workspace_id
    and a.invalid = 0;
