drop view if exists batch_task_view;
create view batch_task_view as
select a.workspace_id,
       g.name                                                   as workspace_name,
       a.project_id,
       h.project_code,
       a.cata_id,
       b.cata_name,
       a.flow_id,
       c.name                                                   as flow_name,
       a.task_id,
       a.task_name,
       a.ctime,
       a.tasktype_code,
       FROM_BASE64(d.source)                                    as source,
       case
           when (a.tasktype_code = 'Hive' or a.tasktype_code = 'Hive-DDL')
               then '**************************************,xtsjzx-sdc-228-41-62:2181,xtsjzx-sdc-228-41-54:2181/sdc_ods;principal=hive/_HOST@XTSDCKDC;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2?tez.queue.name=root.bdoc.user_sdc'
           else JSON_UNQUOTE(f.parameter -> '$.connectUrl') end as connect_url,
       JSON_UNQUOTE(f.parameter -> '$.userName')                as user_name,
       JSON_UNQUOTE(f.parameter -> '$.password')                as password,
       case
           when (a.tasktype_code = 'Hive' or a.tasktype_code = 'Hive-DDL') then i.default_database
           else JSON_UNQUOTE(f.parameter -> '$.dbName') end     as db_name,
       case
           when (a.tasktype_code = 'Hive' or a.tasktype_code = 'Hive-DDL') then i.default_queue
           else '' end                                          as default_queue,
       case
           when a.tasktype_code = 'Hive-DDL' then
               JSON_UNQUOTE(f.parameter -> '$.clusterUserId')
           when a.tasktype_code = 'Hive' then
               (select prd_user_id
                from project_backup.rel_workspace_hadoop_cluster pr
                where pr.default_database = db_name
                  and pr.default_queue = i.default_queue
                limit 1)
           else '' end                                          as cluster_user_id,
       case
           when a.tasktype_code = 'Hive-DDL' then
               JSON_UNQUOTE(f.parameter -> '$.clusterId')
           when a.tasktype_code = 'Hive' then
               (select cluster_id
                from project_backup.rel_workspace_hadoop_cluster pr
                where pr.default_database = db_name
                  and pr.default_queue = i.default_queue
                limit 1)
           else '' end                                          as cluster_id
from batch_backup.bas_task a
# 任务数据源配置表
         left join batch_backup.bas_cata b
                   on a.cata_id = b.cata_id
         left join project_backup.bas_business c
                   on a.flow_id = c.business_id
         left join (select * from batch_backup.rel_task_config where rid in (select t.max_rid from (select max(rid) max_rid,task_id from batch_backup.rel_task_config where source is not null and invalid = 0 group by  task_id) t )) d
                   on a.task_id = d.task_id
         left join batch_backup.rel_task_storage e
                   on a.task_id = e.task_id
         left join resource_backup.bas_storage f
                   on e.storage_id = f.storage_id
         left join project_backup.bas_workspace g
                   on a.workspace_id = g.workspace_id
         left join project_backup.bas_project h on a.project_id = h.project_id
         left join project_backup.rel_workspace_hadoop_cluster i on a.workspace_id = i.workspace_id
where a.tasktype_code in ('Mysql', 'MySQL-DDL', 'Clickhouse', 'Clickhouse-DDL', 'Hive', 'Hive-DDL') and a.invalid = 0 ;

select FROM_BASE64(user_keytab) from resource_backup.bas_cluster_user where cluster_id = '0ae681deb5934e7cb3149706335b0036' and cluster_user_id = '22fef590dd9145cab7b1bc36a17940f3';









