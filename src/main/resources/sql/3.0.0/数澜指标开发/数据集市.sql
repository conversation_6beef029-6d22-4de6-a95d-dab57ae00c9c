create table data_quota_task
(
    id          int auto_increment
        primary key,
    task_name   varchar(200) not null comment '任务名称',
    task_type   tinyint(1)   not null default 0 comment '任务类型：0离线，1实时',
    flow_id     bigint       not null default 0 comment '数澜任务 ID',
    task_desc   varchar(200) null comment '任务描述',
    tenant_id   int          null,
    create_user varchar(50)  not null,
    create_time timestamp             default CURRENT_TIMESTAMP not null,
    update_user varchar(50)  not null,
    update_time timestamp             default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '指标开发任务';

create table data_quota_task_out
(
    id         int auto_increment
        primary key,
    task_id    bigint       not null comment '任务ID',
    asset_type varchar(100) not null comment '资产类型:clickhouse_table,hive_db,hive_table,mysql_table',
    element_id bigint       not null comment '元数据 ID',
    db_name    varchar(50)  not null comment '库名',
    table_name varchar(100) not null comment '表名称'
)
    comment '指标开发任务输出表';

create table data_quota_task_input
(
    id         int auto_increment
        primary key,
    task_id    bigint       not null comment '任务ID',
    asset_type varchar(100) not null comment '资产类型:clickhouse_table,hive_db,hive_table,mysql_table',
    element_id bigint       not null comment '元数据 ID',
    db_name    varchar(50)  not null comment '库名',
    table_name varchar(100) not null comment '表名称',
    table_id   bigint       not null comment '表ID'
)
    comment '指标开发任务输入表';

alter table data_mart_asset
    add release_type tinyint default 0 null comment '发布类型:0原始数据，1中间表';
alter table data_mart_asset
    add task_type tinyint(1) not null default 0 comment '任务类型：0离线，1实时';
alter table data_mart_asset
    add flow_id bigint not null default 0 comment '数澜任务 ID';

alter table data_mart_asset
    add task_inputs json null comment '实时或离线输入输出';

alter table data_mart_asset
    add task_outs json null comment '实时或离线输入输出';

alter table data_mart_asset
    add is_manual tinyint default 1 not null comment '是否手工维护: 0否，1是';

alter table data_quota_task_out
    add create_time timestamp default current_timestamp not null;




