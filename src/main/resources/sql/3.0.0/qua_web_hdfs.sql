
CREATE TABLE `qua_wab_element_hdfs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_type` varchar(20) DEFAULT NULL COMMENT '元数据类型 ：HDFS',
  `element_name` varchar(100) NOT NULL COMMENT '元数据名称/目录名称',
  `host` varchar(100) DEFAULT NULL COMMENT 'host',
  `port` int(8) DEFAULT NULL COMMENT 'port',
  `hdfs_dir` varchar(1000) DEFAULT NULL COMMENT 'hdfs目录地址',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) NOT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) NOT NULL,
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `flag` tinyint(1) DEFAULT '1' COMMENT '是否有效，0无效，1有效，默认1',
  `kbs_enable` int(11) DEFAULT '0' COMMENT '是否开启kbs认证，0-不开启(默认)，1-开启',
  `kbs_account` varchar(100) DEFAULT NULL COMMENT 'KBS账号',
  `key_tab_path` varchar(512) DEFAULT NULL COMMENT 'keytab文件路径',
  `krb5_conf_path` varchar(512) DEFAULT NULL COMMENT 'krb5conf文件路径',
  `jaas_conf_path` varchar(512) DEFAULT NULL COMMENT 'jass文件路径',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='hdfs元数据管理';

-- data_lake.qua_web_hdfs_element_detail_dir definition

CREATE TABLE `qua_web_hdfs_element_detail_dir` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL,
  `parent_dir_path` varchar(1000) DEFAULT NULL COMMENT '上级目录地址',
  `dir_name` varchar(255) DEFAULT NULL COMMENT '目录名称',
  `dir_name_cn` varchar(255) DEFAULT NULL COMMENT '目录中文名称',
  `dir_describe` varchar(255) DEFAULT NULL COMMENT '目录业务描述',
  `dir_owner` varchar(255) DEFAULT NULL COMMENT '目录业务负责人',
  `dir_group` varchar(255) DEFAULT NULL COMMENT '群组',
  `dir_authority` varchar(100) DEFAULT NULL COMMENT '权限',
  `storage_size` bigint(20) DEFAULT NULL COMMENT '存储大小，单位字节',
  `key_words` varchar(1000) DEFAULT NULL COMMENT '关键词，["a","b"]',
  `is_sensitive` smallint(1) DEFAULT '0' COMMENT '是否为敏感，0非，1是，默认不是',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `is_available` smallint(1) DEFAULT '1' COMMENT '是否可用，0不可用，默认为1，可用',
  `ext_attrs` varchar(1024) DEFAULT NULL COMMENT '自定义属性',
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
  `last_viewed_time` varchar(50) DEFAULT NULL COMMENT '最后查看时间',
  `meta_source` int(11) DEFAULT '1' COMMENT '元数据来源，1-自建，2-数据建模',
  `dir_path` varchar(255) DEFAULT NULL COMMENT '上级目录地址',
  PRIMARY KEY (`id`),
  KEY `detail_index` (`element_id`,`dir_name`) USING BTREE,
  KEY `idx_4` (`dir_name`) USING BTREE,
  KEY `idx_hdfs_detail_index_ele` (`element_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='hdfs清单目录表';

CREATE TABLE `qua_web_hdfs_element_detail_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `element_id` bigint(20) DEFAULT NULL,
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_name_cn` varchar(255) DEFAULT NULL COMMENT '中文名称',
  `file_describe` varchar(255) DEFAULT NULL COMMENT '业务描述',
  `file_owner` varchar(255) DEFAULT NULL COMMENT '业务负责人',
  `file_group` varchar(255) DEFAULT NULL COMMENT '群组',
  `file_authority` varchar(100) DEFAULT NULL COMMENT '权限',
  `storage_size` bigint(20) DEFAULT NULL COMMENT '存储大小，单位字节',
  `key_words` varchar(1000) DEFAULT NULL COMMENT '关键词，["a","b"]',
  `is_sensitive` smallint(1) DEFAULT '0' COMMENT '是否为敏感，0非，1是，默认不是',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_user` varchar(60) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` varchar(60) DEFAULT NULL,
  `is_available` smallint(1) DEFAULT '1' COMMENT '是否可用，0不可用，默认为1，可用',
  `ext_attrs` varchar(1024) DEFAULT NULL COMMENT '自定义属性',
  `first_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最早快照版本号，新增记录时写入',
  `last_snapshoot_version` varchar(100) DEFAULT NULL COMMENT '最新快照版本号，更新记录时写入',
  `tenant_id` bigint(20) NOT NULL,
  `last_viewed_time` varchar(50) DEFAULT NULL COMMENT '最后查看时间',
  `meta_source` int(11) DEFAULT '1' COMMENT '元数据来源，1-自建，2-数据建模',
  `dir_path` varchar(255) DEFAULT NULL COMMENT '目录地址',
  PRIMARY KEY (`id`),
  KEY `detail_index` (`element_id`,`file_name`) USING BTREE,
  KEY `idx_4` (`file_name`) USING BTREE,
  KEY `idx_hdfs_detail_index_ele` (`element_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='hdfs清单文件表';

ALTER TABLE data_lineage_relation_statistic ADD hdfs_total bigint(20) DEFAULT 0 NULL COMMENT 'HDFS总数';
ALTER TABLE data_lineage_relation_statistic ADD hdfs_high_value bigint(20) DEFAULT 0 NULL COMMENT 'HDFS高价值资产总数';
ALTER TABLE data_lineage_relation_statistic ADD hdfs_low_value bigint(20) DEFAULT 0 NULL COMMENT 'hdfs孤岛资产总数';
