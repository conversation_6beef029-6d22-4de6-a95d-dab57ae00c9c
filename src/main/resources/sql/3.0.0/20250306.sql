CREATE TABLE ds_security_verification_configuration (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  password_enable int(11) DEFAULT '0' COMMENT '密码验证是否开启，0-否，1-是',
  verify_password varchar(100) DEFAULT NULL COMMENT '验证密码',
  sm4_enable int(11) DEFAULT '0' COMMENT 'sm4加密是否开启，0-否，1-是',
  tenant_id bigint(20) NOT NULL COMMENT '租户id',
  create_user varchar(64) DEFAULT NULL COMMENT '创建人',
  create_time datetime DEFAULT NULL COMMENT '创建时间',
  update_user varchar(64) DEFAULT NULL COMMENT '修改人',
  update_time datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT '安全验证配置';