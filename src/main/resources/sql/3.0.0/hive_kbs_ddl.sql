ALTER TABLE qua_wab_element ADD kbs_enable INT DEFAULT 0 NULL COMMENT '是否开启kbs认证，0-不开启(默认)，1-开启';
ALTER TABLE qua_wab_element ADD jdbc_url varchar(255) NULL COMMENT 'jdbc地址，当开启kbs认证时，会包含principal';
ALTER TABLE tb_cluster ADD jdbc_url varchar(255) NULL COMMENT 'jdbc地址，当开启kbs认证时，会包含principal';

ALTER TABLE qua_scan_list ADD kbs_enable INT DEFAULT 0 NULL COMMENT '是否开启kbs认证，0-不开启(默认)，1-开启';
ALTER TABLE qua_scan_list ADD jdbc_url varchar(255) NULL COMMENT 'jdbc地址，当开启kbs认证时，会包含principal';