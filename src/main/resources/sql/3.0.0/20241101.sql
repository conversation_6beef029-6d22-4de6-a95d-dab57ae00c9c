CREATE TABLE tb_dw_level (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  cn_name varchar(128) DEFAULT NULL COMMENT '中文名称',
  en_name varchar(128) DEFAULT NULL COMMENT '英文名称',
  level_desc varchar(1000) DEFAULT NULL COMMENT '层级描述',
  built_in int(11) DEFAULT '0' COMMENT '是否内置，0-否，1-是',
  create_user varchar(64) DEFAULT NULL COMMENT '创建人',
  create_time datetime DEFAULT NULL COMMENT '创建时间',
  update_user varchar(64) DEFAULT NULL COMMENT '修改人',
  update_time datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT '层级管理';

INSERT INTO tb_dw_level (cn_name, en_name, level_desc, create_user, create_time, update_user, update_time, built_in) VALUES('原始数据层', 'ODS', '原始数据层', 'tenantadmin', '2024-11-04 00:00:00', 'tenantadmin', '2024-11-04 00:00:00', 1);
INSERT INTO tb_dw_level (cn_name, en_name, level_desc, create_user, create_time, update_user, update_time, built_in) VALUES('明细层', 'DWD', '明细层', 'tenantadmin', '2024-11-04 00:00:00', 'tenantadmin', '2024-11-04 00:00:00', 1);
INSERT INTO tb_dw_level (cn_name, en_name, level_desc, create_user, create_time, update_user, update_time, built_in) VALUES('中间层', 'DWM', '中间层', 'tenantadmin', '2024-11-04 00:00:00', 'tenantadmin', '2024-11-04 00:00:00', 1);
INSERT INTO tb_dw_level (cn_name, en_name, level_desc, create_user, create_time, update_user, update_time, built_in) VALUES('汇总数据层', 'DWS', '汇总数据层', 'tenantadmin', '2024-11-04 00:00:00', 'tenantadmin', '2024-11-04 00:00:00', 1);
INSERT INTO tb_dw_level (cn_name, en_name, level_desc, create_user, create_time, update_user, update_time, built_in) VALUES('数据应用层', 'ADS', '数据应用层', 'tenantadmin', '2024-11-04 00:00:00', 'tenantadmin', '2024-11-04 00:00:00', 1);
INSERT INTO tb_dw_level (cn_name, en_name, level_desc, create_user, create_time, update_user, update_time, built_in) VALUES('维度层', 'DIM', '维度层', 'tenantadmin', '2024-11-04 00:00:00', 'tenantadmin', '2024-11-04 00:00:00', 1);

CREATE TABLE tb_dw_level_tenant (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  tenant_id bigint(20) NOT NULL COMMENT '租户id',
  dw_level_id bigint(20) NOT NULL COMMENT '层级id',
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='租户-层级管理';

CREATE TABLE data_score_calc_config (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  table_cn_name_wi DOUBLE(10,3) DEFAULT 0 COMMENT '表中文名权重',
  table_cn_name_deduction DOUBLE(10,3) DEFAULT 0 COMMENT '表中文名扣分项',
  table_desc_wi DOUBLE(10,3) DEFAULT 0 COMMENT '表描述权重',
  table_owner_wi DOUBLE(10,3) DEFAULT 0 COMMENT '表责任人权重',
  table_dw_level_wi DOUBLE(10,3) DEFAULT 0 COMMENT '所属分层权重',
  table_business_sector_wi DOUBLE(10,3) DEFAULT 0 COMMENT '业务板块权重',
  table_data_domain_wi DOUBLE(10,3) DEFAULT 0 COMMENT '业数据域权重',
  table_business_process_wi DOUBLE(10,3) DEFAULT 0 COMMENT '业务过程权重',
  field_cn_name_wi DOUBLE(10,3) DEFAULT 0 COMMENT '字段中文名称权重',
  field_cn_name_deduction DOUBLE(10,3) DEFAULT 0 COMMENT '字段中文名称扣分项',
  field_desc_wi DOUBLE(10,3) DEFAULT 0 COMMENT '字段中文描述权重',
  field_required_wi DOUBLE(10,3) DEFAULT 0 COMMENT '字段是否必填权重',
  field_enum_wi DOUBLE(10,3) DEFAULT 0 COMMENT '枚举值权重',
  low_maintenance_asset_score DOUBLE(10,3) DEFAULT 0 COMMENT '低维护资产分值',
  high_value_asset_score int DEFAULT 3 COMMENT '高价值资产分值',
  low_quality_asset_score DOUBLE(10,3) DEFAULT 0 COMMENT '低质量资产分值',
  tenant_id bigint(20) DEFAULT NULL COMMENT '租户id',
  create_user varchar(64) DEFAULT NULL COMMENT '创建人',
  create_time datetime DEFAULT NULL COMMENT '创建时间',
  update_user varchar(64) DEFAULT NULL COMMENT '修改人',
  update_time datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT '元数据分值计算配置';


CREATE TABLE qua_web_table_model (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  element_id bigint(20) DEFAULT NULL COMMENT '元数据ID',
  db_name varchar(64) DEFAULT NULL COMMENT '数据库名称',
  table_name varchar(255) DEFAULT NULL COMMENT '数据表名称',
  datasource_type varchar(64) DEFAULT NULL COMMENT '数据源类型',
  business_sector_id bigint(20) DEFAULT NULL COMMENT '所属业务板块',
  dw_level_id bigint(20) DEFAULT NULL COMMENT '所属层级',
  data_domain_id bigint(20) DEFAULT NULL COMMENT '所属数据域',
  business_process_id bigint(20) DEFAULT NULL COMMENT '所属业务过程',
  tenant_id bigint(20) NOT NULL COMMENT '租户id',
  create_user varchar(64) DEFAULT NULL COMMENT '创建人',
  create_time datetime DEFAULT NULL COMMENT '创建时间',
  update_user varchar(64) DEFAULT NULL COMMENT '修改人',
  update_time datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=348 DEFAULT CHARSET=utf8mb4 COMMENT='元数据-table-模型信息表';

ALTER TABLE qua_web_ch_element_detail_table ADD ddl_last_change_time varchar(50) NULL COMMENT 'DDL最后变更时间';
ALTER TABLE qua_web_ch_element_detail_table ADD data_last_change_time varchar(50) NULL COMMENT '最后数据变更时间';
ALTER TABLE qua_web_ch_element_detail_table ADD last_viewed_time varchar(50) NULL COMMENT '最后查看时间';
ALTER TABLE qua_web_ch_element_detail_table ADD meta_source int DEFAULT 1 COMMENT '元数据来源，1-自建，2-数据建模';

ALTER TABLE qua_web_es_element_detail_index ADD ddl_last_change_time varchar(50) NULL COMMENT 'DDL最后变更时间';
ALTER TABLE qua_web_es_element_detail_index ADD data_last_change_time varchar(50) NULL COMMENT '最后数据变更时间';
ALTER TABLE qua_web_es_element_detail_index ADD last_viewed_time varchar(50) NULL COMMENT '最后查看时间';
ALTER TABLE qua_web_es_element_detail_index ADD meta_source int DEFAULT 1 COMMENT '元数据来源，1-自建，2-数据建模';

ALTER TABLE qua_web_hive_element_detail_table ADD ddl_last_change_time varchar(50) NULL COMMENT 'DDL最后变更时间';
ALTER TABLE qua_web_hive_element_detail_table ADD data_last_change_time varchar(50) NULL COMMENT '最后数据变更时间';
ALTER TABLE qua_web_hive_element_detail_table ADD last_viewed_time varchar(50) NULL COMMENT '最后查看时间';
ALTER TABLE qua_web_hive_element_detail_table ADD meta_source int DEFAULT 1 COMMENT '元数据来源，1-自建，2-数据建模';

ALTER TABLE qua_web_mysql_element_detail_table ADD ddl_last_change_time varchar(50) NULL COMMENT 'DDL最后变更时间';
ALTER TABLE qua_web_mysql_element_detail_table ADD data_last_change_time varchar(50) NULL COMMENT '最后数据变更时间';
ALTER TABLE qua_web_mysql_element_detail_table ADD last_viewed_time varchar(50) NULL COMMENT '最后查看时间';
ALTER TABLE qua_web_mysql_element_detail_table ADD meta_source int DEFAULT 1 COMMENT '元数据来源，1-自建，2-数据建模';

CREATE TABLE qua_asset_maintenance (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  element_id bigint(20) DEFAULT NULL COMMENT '元数据ID',
  db_name varchar(64) DEFAULT NULL COMMENT '数据库名称',
  table_name varchar(255) DEFAULT NULL COMMENT '数据表名称',
  datasource_type varchar(64) DEFAULT NULL COMMENT '数据源类型',
  score DECIMAL(5,2) DEFAULT 0 COMMENT '维护分，保留2位小数',
  tenant_id bigint(20) NOT NULL COMMENT '租户id',
  create_time datetime DEFAULT NULL COMMENT '创建时间',
  status INT DEFAULT 0 COMMENT '0-有效，1-无效',
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='元数据表维护分汇总';

CREATE TABLE qua_asset_low_maintenance_statistics (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  datasource_type varchar(64) NOT NULL COMMENT '数据源类型',
  num bigint(20) DEFAULT 0 COMMENT '数量',
  statistic_date varchar(64) NOT NULL COMMENT '统计日期',
  create_time datetime DEFAULT NULL COMMENT '入库时间',
  status INT DEFAULT 0 COMMENT '0-有效，1-无效',
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='元数据表低维护资产统计';

CREATE TABLE qua_asset_quality (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  element_id bigint(20) DEFAULT NULL COMMENT '元数据ID',
  db_name varchar(64) DEFAULT NULL COMMENT '数据库名称',
  table_name varchar(255) DEFAULT NULL COMMENT '数据表名称',
  datasource_type varchar(64) DEFAULT NULL COMMENT '数据源类型',
  score DECIMAL(5,2) DEFAULT 0 COMMENT '质量分，保留2位小数',
  tenant_id bigint(20) NOT NULL COMMENT '租户id',
  create_time datetime DEFAULT NULL COMMENT '创建时间',
  status INT DEFAULT 0 COMMENT '0-有效，1-无效',
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='元数据表质量分汇总';

CREATE TABLE qua_asset_low_quality_statistics (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  datasource_type varchar(64) NOT NULL COMMENT '数据源类型',
  num bigint(20) DEFAULT 0 COMMENT '数量',
  statistic_date varchar(64) NOT NULL COMMENT '统计日期',
  create_time datetime DEFAULT NULL COMMENT '入库时间',
  status INT DEFAULT 0 COMMENT '0-有效，1-无效',
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='元数据表低质量资产统计';

CREATE TABLE qua_asset_high_value_statistics (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  datasource_type varchar(64) NOT NULL COMMENT '数据源类型',
  num bigint(20) DEFAULT 0 COMMENT '数量',
  statistic_date varchar(64) NOT NULL COMMENT '统计日期',
  create_time datetime DEFAULT NULL COMMENT '入库时间',
  status INT DEFAULT 0 COMMENT '0-有效，1-无效',
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='元数据表高价值资产统计';

CREATE TABLE qua_asset_isolated_island_statistics (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  datasource_type varchar(64) NOT NULL COMMENT '数据源类型',
  num bigint(20) DEFAULT 0 COMMENT '数量',
  statistic_date varchar(64) NOT NULL COMMENT '统计日期',
  create_time datetime DEFAULT NULL COMMENT '入库时间',
  status INT DEFAULT 0 COMMENT '0-有效，1-无效',
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='元数据表孤岛资产统计';

alter table qua_web_ch_element_detail_table add key_words json null comment '关键词，["a","b"]' after table_owner;
alter table qua_web_mysql_element_detail_table add key_words json null comment '关键词，["a","b"]' after table_owner;
alter table qua_web_hive_element_detail_table add key_words json null comment '关键词，["a","b"]' after table_owner;
alter table qua_web_es_element_detail_index add key_words json null comment '关键词，["a","b"]' after index_owner;

-- 数据字典添加字段
ALTER TABLE data_dictionary_item ADD business_sector_id bigint NULL COMMENT '所属业务板块';
ALTER TABLE data_dictionary_item ADD dw_level_id bigint NULL COMMENT '所属层级';
ALTER TABLE data_dictionary_item ADD data_domain_id bigint NULL COMMENT '所属数据域';
ALTER TABLE data_dictionary_item ADD business_process_id bigint NULL COMMENT '所属业务过程';
ALTER TABLE data_dictionary_item ADD data_count bigint DEFAULT 0 COMMENT '数据条数';
ALTER TABLE data_dictionary_item ADD data_storage_size bigint DEFAULT 0 COMMENT '数据存储量（字节）';
ALTER TABLE data_dictionary_item ADD meta_source int DEFAULT 1 COMMENT '元数据来源，1-自建，2-数据建模';
ALTER TABLE data_dictionary_item ADD is_primary_key int DEFAULT NULL COMMENT '是否主键，0-否，1-是';

alter table data_mart_asset add db_name varchar(50) null comment '库名' after element_id;
alter table data_mart_asset add table_name varchar(100) null comment '表名称' after db_name;
alter table data_mart_asset add index_name varchar(100) null comment '索引名称' after table_name;
alter table data_mart_asset add batch_no varchar(100) null comment '批次号' after index_name;

CREATE TABLE qua_asset_table_data_statistics (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  element_id bigint(20) DEFAULT NULL COMMENT '元数据ID',
  db_name varchar(64) DEFAULT NULL COMMENT '数据库名称',
  table_name varchar(255) DEFAULT NULL COMMENT '数据表名称',
  datasource_type varchar(64) DEFAULT NULL COMMENT '数据源类型',
  data_count bigint(20) DEFAULT 0 COMMENT '数据条数',
  data_storage_size BIGINT DEFAULT 0 COMMENT '数据存储量（字节）',
  statistic_date varchar(100) DEFAULT NULL COMMENT '统计日期',
  create_time datetime DEFAULT NULL COMMENT '入库时间',
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='元数据表数据量和存储空间汇总';

CREATE TABLE data_dictionary_item_view_record (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  element_id bigint(20) DEFAULT NULL COMMENT '元数据ID',
  db_name varchar(64) DEFAULT NULL COMMENT '数据库名称',
  table_name varchar(255) DEFAULT NULL COMMENT '数据表名称',
  datasource_type varchar(64) DEFAULT NULL COMMENT '数据源类型',
  viewer_tenant_id bigint(20) NOT NULL COMMENT '浏览者租户id',
  create_time datetime DEFAULT NULL COMMENT '入库时间',
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='数据字典浏览记录';



drop view if exists asset_map_view;
CREATE VIEW asset_map_view AS
SELECT t1.id,
       t1.data_name,
       CASE
           WHEN t1.asset_type = 'clickhouse_table' THEN 'clickhouse'
           WHEN t1.asset_type = 'mysql_table' THEN 'mysql'
           WHEN t1.asset_type = 'hive_table' THEN 'hive'
           WHEN t1.asset_type = 'elasticsearch_index' THEN 'elasticsearch'
           ELSE ''
           END                                                                                     AS asset_type,

       COALESCE(ch.table_owner, mysql.table_owner, hive.table_owner, es.index_owner, '')           AS table_owner,
       COALESCE(ch.db_name, mysql.db_name, hive.db_name, es.index_name, '')                        AS db_name,
       COALESCE(ch.key_words, mysql.key_words, hive.key_words, es.key_words, '')                   AS key_words,
       COALESCE(ch.table_name_cn, mysql.table_name_cn, hive.table_name_cn, es.index_name_cn, '')   AS table_name_cn,

       t2.id                                                                                       AS type_id,
       t2.type_name,
       t3.id                                                                                       AS group_id,
       t3.group_name,
       t1.asset_desc,

       (SELECT COUNT(1) FROM data_mart_subscribe WHERE asset_id = t1.id AND approval_progress = 2) AS subscribe_cnt

FROM data_mart_asset t1
         JOIN
     data_mart_type t2 ON t1.type_id = t2.id
         JOIN
     data_mart_group t3 ON t1.group_id = t3.id
         LEFT JOIN
     qua_web_ch_element_detail_table ch ON t1.asset_type = 'clickhouse_table'
         AND ch.element_id = t1.element_id
         AND ch.db_name = t1.db_name
         AND ch.table_name = t1.table_name
         LEFT JOIN
     qua_web_mysql_element_detail_table mysql ON t1.asset_type = 'mysql_table'
         AND mysql.element_id = t1.element_id
         AND mysql.db_name = t1.db_name
         AND mysql.table_name = t1.table_name
         LEFT JOIN
     qua_web_hive_element_detail_table hive ON t1.asset_type = 'hive_table'
         AND hive.element_id = t1.element_id
         AND hive.db_name = t1.db_name
         AND hive.table_name = t1.table_name
         LEFT JOIN
     qua_web_es_element_detail_index es ON t1.asset_type = 'elasticsearch_index'
         AND es.element_id = t1.element_id
         AND es.index_name = t1.index_name
WHERE t1.publish_status = 1
  AND t1.approval_status = 1;


CREATE TABLE tb_select_field (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  module_name varchar(50) DEFAULT NULL COMMENT '所属模块',
  field_name varchar(255) DEFAULT NULL COMMENT '字段名',
  field_desc varchar(255) DEFAULT NULL COMMENT '字段描述',
  sort int DEFAULT 1 COMMENT '排序',
  selected int DEFAULT 0 COMMENT '是否选中，0-否，1-是',
  create_user varchar(64) DEFAULT NULL COMMENT '创建人',
  create_time datetime DEFAULT NULL COMMENT '创建时间',
  update_user varchar(64) DEFAULT NULL COMMENT '修改人',
  update_time datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='自定义列表显示';

INSERT INTO tb_select_field (module_name, field_name, field_desc, sort, selected, create_user, create_time, update_user, update_time)
VALUES
('itemAssetPage', 'tableName', '表名', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'columnNameCn', '表中文名', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'datasourceType', '存储类型', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'metaSource', '元数据来源', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'assetTypeList', '资产类型', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'assetStatusList', '资产状态', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'dataCount', '数据量', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'storageSize', '数据大小', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'isSensitive', '是否涉敏', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'owner', '责任人', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'upstreamDependencyCount', '上游依赖', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'downstreamDependencyCount', '下游依赖', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'businessSectorName', '业务板块', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'dataDomainName', '数据域', 1, 0, 'admin', NOW(), 'admin', NOW()),
('itemAssetPage', 'dwLevelName', '数据层级', 1, 0, 'admin', NOW(), 'admin', NOW());


ALTER TABLE data_model_logic_table_field ADD `type` int(11) NULL COMMENT '类型: 0自定义 , 1数据源标准';
ALTER TABLE data_model_logic_table_field ADD data_standard varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '数据源标准ID 一级id_二级id_三级id';

alter table data_model_business_sector add column built_in int DEFAULT 0 COMMENT '是否内置，0-否，1-是';


delete from tb_dic where ALIAS  ='model-manager';
INSERT INTO tb_dic ( NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ( '模型管理', 'model-manager', 2, null, 0);


create table if not exists data_mart_cart
(
    id          bigint auto_increment
        primary key,
    asset_id    bigint                              not null comment '分类ID',
    create_time timestamp default CURRENT_TIMESTAMP not null,
    create_user varchar(100)                        not null,
    tenant_id   bigint                              not null
)
    comment '资产订购表';