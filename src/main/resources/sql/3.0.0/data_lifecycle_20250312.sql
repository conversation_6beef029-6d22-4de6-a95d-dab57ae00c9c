-- 数据生命周期管理
CREATE TABLE data_lifecycle_assets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    asset_type VARCHAR(255) NOT NULL COMMENT '资产类型：ClickHouse、ES、Hive、MySQL',
    database_name VARCHAR(255) NOT NULL COMMENT '库名',
    table_name VARCHAR(255) NOT NULL COMMENT '表名',
    partition_by VARCHAR(255) NULL COMMENT '分区类型，toYear,toYYYYMM,toYYYYMMDD,toYYYYMMDDhh',
    partition_column VARCHAR(255) NULL COMMENT '分区字段',
    asset_desc VARCHAR(1000) NULL COMMENT '资产描述',
    data_size BIGINT COMMENT '数据量（字节）',
    row_count BIGINT COMMENT '数据条数',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    create_user varchar(100) DEFAULT NULL COMMENT '创建人',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    update_user varchar(100) DEFAULT NULL COMMENT '更新人',
    UNIQUE KEY uk_tenant_asset (tenant_id, asset_type, database_name, table_name) COMMENT '唯一约束：同一租户下资产唯一',
    INDEX idx_tenant_type (tenant_id, asset_type)
) COMMENT '数据资产表';

CREATE TABLE data_lifecycle_policies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    asset_id BIGINT NOT NULL COMMENT '资产ID，关联assets表',
    policy_type VARCHAR(50) NOT NULL COMMENT '策略类型：转离线或转归档, 取值：TO_OFFLINE,TO_ARCHIVE',
    days_to_trigger INT NOT NULL COMMENT '触发策略的天数',
    target_database VARCHAR(255) COMMENT '目标库名（转离线策略时为Hive库名）',
    target_table VARCHAR(255) COMMENT '目标表名（转离线策略时为Hive表名）',
    days_to_destroy INT COMMENT '归档后销毁天数（转归档策略时有效）',
    permanent_storage INT DEFAULT 0 COMMENT '是否永久存储, 0-否，1-是',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    create_user varchar(100) DEFAULT NULL COMMENT '创建人',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    update_user varchar(100) DEFAULT NULL COMMENT '更新人',
    INDEX idx_asset_id (asset_id)
) COMMENT '生命周期策略表';

CREATE TABLE data_lifecycle_offline (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    asset_id BIGINT COMMENT '资产ID，关联assets表',
    column_info TEXT COMMENT '列信息',
    partition_date VARCHAR(100) NOT NULL COMMENT '分区日期',
    database_name VARCHAR(255) NOT NULL COMMENT '库名',
    table_name VARCHAR(255) NOT NULL COMMENT '表名',
    data_size BIGINT COMMENT '数据量（字节）',
    row_count BIGINT COMMENT '数据条数',
    offline_time datetime DEFAULT NULL COMMENT '离线时间',
    INDEX idx_tenant_offline (tenant_id),
    INDEX idx_asset_id (asset_id)
) COMMENT '离线数据表';

CREATE TABLE data_lifecycle_offline_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    asset_id BIGINT NOT NULL COMMENT '资产ID，关联assets表',
    offline_id BIGINT COMMENT '离线ID，关联离线表',
    from_status INT NOT NULL COMMENT '来源状态：1-在线，2-离线',
    to_status INT NOT NULL COMMENT '目标状态：1-在线，2-离线',
    asset_type VARCHAR(255) NOT NULL COMMENT '资产类型：ClickHouse、ES、Hive',
    partition_date VARCHAR(100) NOT NULL COMMENT '分区日期',
    earliest_data_time VARCHAR(100) COMMENT '最早数据时间',
    latest_data_time VARCHAR(100) COMMENT '最晚数据时间',
    execution_begin_time datetime DEFAULT NULL COMMENT '数据执行开始时间',
    execution_end_time datetime DEFAULT NULL COMMENT '数据执行结束时间',
    create_time datetime DEFAULT NULL COMMENT '日志入库时间',
    expected_data_volume BIGINT DEFAULT 0 COMMENT '应处理数据量',
    processed_data_volume BIGINT DEFAULT 0 COMMENT '处理数据量',
    status VARCHAR(50) NOT NULL COMMENT '执行状态，取值：IN_PROGRESS,SUCCESS,FAILURE',
    message TEXT COMMENT '日志消息，记录详情或错误信息',
    INDEX idx_offline_id (offline_id),
    INDEX idx_asset_id (asset_id)
) COMMENT '离线日志表';

CREATE TABLE data_lifecycle_archived (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    column_info TEXT COMMENT '列信息',
    offline_id BIGINT COMMENT '离线ID，关联离线表',
    asset_id BIGINT COMMENT '原始资产ID，关联assets表',
    partition_date VARCHAR(100) NOT NULL COMMENT '分区日期',
    file_name VARCHAR(1024) NOT NULL COMMENT '归档文件名称',
    file_path VARCHAR(1024) NOT NULL COMMENT '归档文件路径',
    archived_time datetime DEFAULT NULL COMMENT '归档时间',
    data_size BIGINT COMMENT '数据量（字节）',
    row_count BIGINT COMMENT '数据条数',
    status VARCHAR(50) DEFAULT NULL COMMENT '状态：已归档或已销毁, 取值：ARCHIVED,DESTROYED',
    days_to_destroy INT COMMENT '存储时限，归档后销毁天数',
    destroyed_time datetime DEFAULT NULL COMMENT '销毁时间',
    INDEX idx_tenant_status (tenant_id, status),
    INDEX idx_original_asset_id (asset_id),
    INDEX idx_offline_id (offline_id)
) COMMENT '归档/销毁数据表';

CREATE TABLE data_lifecycle_archived_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    asset_id BIGINT NOT NULL COMMENT '资产ID，关联assets表',
    archived_id BIGINT COMMENT '归档ID，关联归档表',
    log_type INT COMMENT '日志类型，1-归档日志，2-销毁日志',
    from_status INT NOT NULL COMMENT '来源状态：1-在线，2-离线',
    to_status INT NOT NULL COMMENT '目标状态：3-归档，4-销毁',
    asset_type VARCHAR(255) NOT NULL COMMENT '资产类型：ClickHouse、ES、Hive',
    partition_date VARCHAR(100) NOT NULL COMMENT '分区日期',
    earliest_data_time VARCHAR(100) COMMENT '最早数据时间',
    latest_data_time VARCHAR(100) COMMENT '最晚数据时间',
    execution_begin_time datetime DEFAULT NULL COMMENT '数据执行开始时间',
    execution_end_time datetime DEFAULT NULL COMMENT '数据执行结束时间',
    create_time datetime DEFAULT NULL COMMENT '日志入库时间',
    expected_data_volume BIGINT DEFAULT 0 COMMENT '应处理数据量',
    processed_data_volume BIGINT DEFAULT 0 COMMENT '处理数据量',
    status VARCHAR(50) NOT NULL COMMENT '执行状态，取值：IN_PROGRESS、SUCCESS、FAILURE',
    message TEXT COMMENT '日志消息，记录详情或错误信息',
    INDEX idx_archived_id (archived_id),
    INDEX idx_asset_id (asset_id)
) COMMENT '归档/销毁日志表';

CREATE TABLE data_lifecycle_recovery_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    asset_id BIGINT NOT NULL COMMENT '资产ID，关联assets表',
    original_id BIGINT NOT NULL COMMENT '原始ID，比如离线ID、归档ID',
    asset_type VARCHAR(255) NOT NULL COMMENT '资产类型：Hive、归档文件',
    from_status INT NOT NULL COMMENT '来源状态：1-在线，2-离线，3-归档，4-销毁',
    to_status INT NOT NULL COMMENT '目标状态：1-在线，2-离线，3-归档，4-销毁',
    recovery_to_asset_type VARCHAR(255) COMMENT '恢复目标资产类型',
    recovery_to_database varchar(100)  COMMENT '恢复目标资产库名',
    recovery_to_table varchar(100)  COMMENT '恢复目标资产表名',
    partition_date VARCHAR(100) NOT NULL COMMENT '分区日期',
    execution_begin_time datetime DEFAULT NULL COMMENT '数据执行开始时间',
    execution_end_time datetime DEFAULT NULL COMMENT '数据执行结束时间',
    create_time datetime DEFAULT NULL COMMENT '日志入库时间',
    earliest_data_time VARCHAR(100) COMMENT '最早数据时间',
    latest_data_time VARCHAR(100) COMMENT '最晚数据时间',
    expected_data_volume BIGINT DEFAULT 0 COMMENT '应处理数据量',
    processed_data_volume BIGINT DEFAULT 0 COMMENT '处理数据量',
    status VARCHAR(50) NOT NULL COMMENT '执行状态，取值：IN_PROGRESS、SUCCESS、FAILURE',
    message TEXT COMMENT '日志消息，记录详情或错误信息',
    INDEX idx_original_id (original_id),
    INDEX idx_asset_id (asset_id)
) COMMENT '销毁日志表';

