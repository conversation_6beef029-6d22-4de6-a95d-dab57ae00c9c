alter table qua_web_ch_element_detail_column
    add desensitization_id int null comment '脱敏规则 ID';

alter table qua_web_es_element_detail_field
    add desensitization_id int null comment '脱敏规则 ID';

alter table qua_web_hive_element_detail_column
    add desensitization_id int null comment '脱敏规则 ID';

alter table qua_web_mysql_element_detail_column
    add desensitization_id int null comment '脱敏规则 ID';

create table data_mart_hotword
(
    id          bigint auto_increment
        primary key,
    hotword     varchar(255) null comment '热词',
    create_time datetime     null comment '入库时间'
)
    comment '数据集市热词';

alter table data_mart_asset
    add column
        publish_status int default 0 null comment '发布状态，0未发布，1已发布';
alter table data_mart_asset
    add column
        publish_time datetime null comment '发布时间';
alter table data_mart_asset
    add column
        approval_status int null comment '审批状态，0待审批 1已发布 2已拒绝 3已撤销';
alter table data_mart_asset
    add column
        approval_desc varchar(255) null comment '审批说明';
alter table data_mart_asset
    add column
        approval_time datetime null comment '审批时间';
alter table data_mart_asset
    add column
        approval_user varchar(100) null comment '审批人';
alter table data_mart_asset
    add column
        browse_count int default 0 null comment '浏览数';

create table data_mart_table_lines
(
    id              bigint auto_increment
        primary key,
    asset_id        bigint                              null,
    count_day       varchar(100)                        null comment '统计日期',
    table_line      json                                null comment '每个表条数{"table_1":100,"table_2":100}',
    table_line_all  bigint                              null comment '总条数',
    table_space_all decimal(10, 2)                      null comment '总占用空间 MB',
    create_time     timestamp default CURRENT_TIMESTAMP null
)
    comment '数据集市资产表数量统计';

create table data_mart_score
(
    id              bigint auto_increment
        primary key,
    asset_id        bigint                              null,
    tenant_id       int                                 null,
    user_name       varchar(200)                                 null,
    available_score int                                 default 0 comment '可用性分',
    visible_score   int                                 default 0 comment '可见性分',
    credible_score  int                                 default 0 comment '可信性分',
    create_time     timestamp default CURRENT_TIMESTAMP null
)
    comment '数据集市资产评分';


create table data_mart_discuss
(
    id          bigint auto_increment
        primary key,
    asset_id    bigint                              null,
    tenant_id   int                                 null,
    user_id     int                                 null,
    user_name   varchar(200)                        null,
    discuss     varchar(10000)                      null comment '评论',
    create_time timestamp default CURRENT_TIMESTAMP null
)
    comment '数据集市资产评论';
