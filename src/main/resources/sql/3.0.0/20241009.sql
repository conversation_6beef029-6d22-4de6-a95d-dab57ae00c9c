alter table qua_web_ch_element_detail_column add cn_desc varchar(200) null comment '中文描述';
alter table qua_web_ch_element_detail_column add enum_value varchar(200) null comment '枚举值';
alter table qua_web_ch_element_detail_column add mapping_fields varchar(200) null comment '映射字段';
alter table qua_web_ch_element_detail_column add sort int not null default 0 comment '排序字段';

alter table qua_web_es_element_detail_field add cn_desc varchar(200) null comment '中文描述';
alter table qua_web_es_element_detail_field add enum_value varchar(200) null comment '枚举值';
alter table qua_web_es_element_detail_field add mapping_fields varchar(200) null comment '映射字段';
alter table qua_web_es_element_detail_field add sort int not null default 0 comment '排序字段';

alter table qua_web_hive_element_detail_column add cn_desc varchar(200) null comment '中文描述';
alter table qua_web_hive_element_detail_column add enum_value varchar(200) null comment '枚举值';
alter table qua_web_hive_element_detail_column add mapping_fields varchar(200) null comment '映射字段';
alter table qua_web_hive_element_detail_column add sort int not null default 0 comment '排序字段';

alter table qua_web_mysql_element_detail_column add cn_desc varchar(200) null comment '中文描述';
alter table qua_web_mysql_element_detail_column add enum_value varchar(200) null comment '枚举值';
alter table qua_web_mysql_element_detail_column add mapping_fields varchar(200) null comment '映射字段';
alter table qua_web_mysql_element_detail_column add sort int not null default 0 comment '排序字段';

alter table data_dictionary_item add cn_desc varchar(200) null comment '中文描述';
alter table data_dictionary_item add enum_value varchar(200) null comment '枚举值';
alter table data_dictionary_item add mapping_fields varchar(200) null comment '映射字段';
alter table data_dictionary_item add sort int not null default 0 comment '排序字段';

update qua_web_ch_element_detail_column set column_name_cn='采集设备类型',cn_desc='采集设备类型' where column_name='generic_datasource_type';
update qua_web_ch_element_detail_column set column_name_cn='采集设备ip',cn_desc='采集设备ip' where column_name='generic_device_ip';
update qua_web_ch_element_detail_column set column_name_cn='解析字段总数',cn_desc='解析字段总数' where column_name='parser_total';
update qua_web_ch_element_detail_column set column_name_cn='解析字段计数器',cn_desc='解析字段计数器' where column_name='parser_count';
update qua_web_ch_element_detail_column set column_name_cn='数据采集任务id',cn_desc='对应采集任务的任务id' where column_name='ueba_flow_id';
update qua_web_ch_element_detail_column set column_name_cn='唯一标识',cn_desc='唯一标识，采集入库自动生成无业务含义' where column_name='uuid';
update qua_web_ch_element_detail_column set column_name_cn='入库时间',cn_desc='采集入库的时间' where column_name='generic_into_time';
update qua_web_ch_element_detail_column set column_name_cn='读取数据的时间',cn_desc='采集读取数据的时间' where column_name='generic_create_time';
update qua_web_ch_element_detail_column set column_name_cn='原始日志',cn_desc='源端发送的解析前的原始日志' where column_name='generic_raw_log';

update qua_web_es_element_detail_field set field_name_cn='采集设备类型',cn_desc='采集设备类型' where field_name='generic_datasource_type';
update qua_web_es_element_detail_field set field_name_cn='采集设备ip',cn_desc='采集设备ip' where field_name='generic_device_ip';
update qua_web_es_element_detail_field set field_name_cn='解析字段总数',cn_desc='解析字段总数' where field_name='parser_total';
update qua_web_es_element_detail_field set field_name_cn='解析字段计数器',cn_desc='解析字段计数器' where field_name='parser_count';
update qua_web_es_element_detail_field set field_name_cn='数据采集任务id',cn_desc='对应采集任务的任务id' where field_name='ueba_flow_id';
update qua_web_es_element_detail_field set field_name_cn='唯一标识',cn_desc='唯一标识，采集入库自动生成无业务含义' where field_name='uuid';
update qua_web_es_element_detail_field set field_name_cn='入库时间',cn_desc='采集入库的时间' where field_name='generic_into_time';
update qua_web_es_element_detail_field set field_name_cn='读取数据的时间',cn_desc='采集读取数据的时间' where field_name='generic_create_time';
update qua_web_es_element_detail_field set field_name_cn='原始日志',cn_desc='源端发送的解析前的原始日志' where field_name='generic_raw_log';

update qua_web_hive_element_detail_column set column_name_cn='采集设备类型',cn_desc='采集设备类型' where column_name='generic_datasource_type';
update qua_web_hive_element_detail_column set column_name_cn='采集设备ip',cn_desc='采集设备ip' where column_name='generic_device_ip';
update qua_web_hive_element_detail_column set column_name_cn='解析字段总数',cn_desc='解析字段总数' where column_name='parser_total';
update qua_web_hive_element_detail_column set column_name_cn='解析字段计数器',cn_desc='解析字段计数器' where column_name='parser_count';
update qua_web_hive_element_detail_column set column_name_cn='数据采集任务id',cn_desc='对应采集任务的任务id' where column_name='ueba_flow_id';
update qua_web_hive_element_detail_column set column_name_cn='唯一标识',cn_desc='唯一标识，采集入库自动生成无业务含义' where column_name='uuid';
update qua_web_hive_element_detail_column set column_name_cn='入库时间',cn_desc='采集入库的时间' where column_name='generic_into_time';
update qua_web_hive_element_detail_column set column_name_cn='读取数据的时间',cn_desc='采集读取数据的时间' where column_name='generic_create_time';
update qua_web_hive_element_detail_column set column_name_cn='原始日志',cn_desc='源端发送的解析前的原始日志' where column_name='generic_raw_log';

update qua_web_mysql_element_detail_column set column_name_cn='采集设备类型',cn_desc='采集设备类型' where column_name='generic_datasource_type';
update qua_web_mysql_element_detail_column set column_name_cn='采集设备ip',cn_desc='采集设备ip' where column_name='generic_device_ip';
update qua_web_mysql_element_detail_column set column_name_cn='解析字段总数',cn_desc='解析字段总数' where column_name='parser_total';
update qua_web_mysql_element_detail_column set column_name_cn='解析字段计数器',cn_desc='解析字段计数器' where column_name='parser_count';
update qua_web_mysql_element_detail_column set column_name_cn='数据采集任务id',cn_desc='对应采集任务的任务id' where column_name='ueba_flow_id';
update qua_web_mysql_element_detail_column set column_name_cn='唯一标识',cn_desc='唯一标识，采集入库自动生成无业务含义' where column_name='uuid';
update qua_web_mysql_element_detail_column set column_name_cn='入库时间',cn_desc='采集入库的时间' where column_name='generic_into_time';
update qua_web_mysql_element_detail_column set column_name_cn='读取数据的时间',cn_desc='采集读取数据的时间' where column_name='generic_create_time';
update qua_web_mysql_element_detail_column set column_name_cn='原始日志',cn_desc='源端发送的解析前的原始日志' where column_name='generic_raw_log';