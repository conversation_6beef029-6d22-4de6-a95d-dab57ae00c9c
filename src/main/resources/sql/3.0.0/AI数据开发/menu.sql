delete from ums_sys_menus where menu_code='ai-project-manage';
INSERT INTO ums_sys_menus(menu_type, menu_property, menu_level, root_parent, menu_name, menu_code, menu_path, manage_free, hidden, parent_name, status, menu_order, default_order, default_name, default_status, default_parent, create_user, create_date, update_user, update_date, application, level1, menu_category, new_open_window) VALUES ('builtIn', '1', 2, 'tenant-resource-auth', '项目管理', 'ai-project-manage', null, '0', '0', 'tenant-resource-auth', '1', 2, 2, '项目管理', '1', 'tenant-resource-auth', null, null, null, null, null, null, '0', 0);
delete from tb_dic where ALIAS = 'machine_learning';
INSERT INTO tb_dic ( NAME, ALIAS, PARENT_ID, unique_check_code, hidden) VALUES ('机器学习', 'machine_learning', 2, null, 0);

CREATE TABLE `ai_mirror`
(
    `id`                int(11)      NOT NULL AUTO_INCREMENT,
    `function_classify` varchar(200) NOT NULL COMMENT '功能分类',
    `repository_id`     int(11)      NOT NULL COMMENT '仓库ID',
    `mirror_name`       varchar(200) NOT NULL COMMENT '镜像名称',
    `tag`               varchar(200)          DEFAULT NULL COMMENT '标签',
    `mirror_desc`       varchar(200) NOT NULL COMMENT '镜像描述',
    `mirror_use`        varchar(100) NOT NULL COMMENT '镜像用途：notebook，模型开发',
    `mirror_file_path`  varchar(500) NOT NULL COMMENT '镜像文件路径',
    `mirror_file_name`  varchar(200) NOT NULL COMMENT '镜像文件名称',
    `status`            int(11)               DEFAULT '0' COMMENT '推送状态，0推送中，1推送成功，2推送失败',
    `error_message`     varchar(500)          DEFAULT NULL COMMENT '失败原因',
    `tenant_id`         int(11)               DEFAULT NULL,
    `create_user`       varchar(50)  NOT NULL,
    `create_time`       timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_user`       varchar(50)  NOT NULL,
    `update_time`       timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='ai镜像';

CREATE TABLE `ai_mirror_repository`
(
    `id`              int(11)      NOT NULL AUTO_INCREMENT,
    `repository_name` varchar(200) NOT NULL COMMENT '仓库名称',
    `repository_addr` varchar(500) NOT NULL COMMENT '仓库地址',
    `user_name`       varchar(200) NOT NULL COMMENT '用户名',
    `password`        varchar(200) NOT NULL COMMENT '密码',
    `k8s_hubsecret`   varchar(500) NOT NULL COMMENT 'k8s密钥',
    `tenant_id`       int(11)      NOT NULL COMMENT '租户',
    `create_user`     varchar(50)  NOT NULL,
    `create_time`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_user`     varchar(50)  NOT NULL,
    `update_time`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='ai镜像仓库';

CREATE TABLE `ai_notebook`
(
    `id`            int(11)      NOT NULL AUTO_INCREMENT,
    `project_id`    int(11)      NOT NULL COMMENT '项目ID',
    `name`          varchar(200) NOT NULL COMMENT '名称',
    `desc`          varchar(200) NOT NULL COMMENT '描述',
    `repository_id` int(11)      NOT NULL COMMENT '仓库ID',
    `mirror_id`     int(11)      NOT NULL COMMENT '镜像ID',
    `need_mem`      int(11)      NOT NULL COMMENT '申请内存',
    `need_cpu`      int(11)      NOT NULL COMMENT '申请CPU',
    `need_gpu`      int(11)      NOT NULL COMMENT '申请GPU',
    `status`        int(11)      NOT NULL DEFAULT '0' COMMENT '状态：0分配中，1分配成功，1分配失败',
    `error_message` varchar(500)          DEFAULT NULL COMMENT '失败原因',
    `notebook_url`  varchar(500)          DEFAULT NULL COMMENT '分配的url',
    `tenant_id`     int(11)      NOT NULL,
    `create_user`   varchar(50)  NOT NULL,
    `create_time`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_user`   varchar(50)  NOT NULL,
    `update_time`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='ai_notebook';

CREATE TABLE `ai_project_model`
(
    `id`             int(11)      NOT NULL AUTO_INCREMENT,
    `name_space`     varchar(200) NOT NULL COMMENT '命名空间',
    `en_name`        varchar(200) NOT NULL COMMENT '英文名称',
    `tenant_id`      int(11)      NOT NULL COMMENT '所属租户',
    `cpu_limit`      int(11)      NOT NULL COMMENT 'CPU限额',
    `need_cpu_limit` int(11)      NOT NULL COMMENT '需求CPU限额',
    `mem_limit`      int(11)      NOT NULL COMMENT '内存限额',
    `need_mem_limit` int(11)      NOT NULL COMMENT '需求内存限额',
    `gpu_limit`      int(11)      NOT NULL COMMENT 'GPU限额',
    `storage_limit`  int(11)      NOT NULL COMMENT '存储限额',
    `pvc_storage`    int(11)               DEFAULT NULL,
    `pvc_archive`    int(11)               DEFAULT NULL,
    `status`         int(11)               DEFAULT '0' COMMENT '分配状态，0分配中，1分配成功，2分配失败',
    `error_message`  text COMMENT '失败原因',
    `create_user`    varchar(50)  NOT NULL,
    `create_time`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_user`    varchar(50)  NOT NULL,
    `update_time`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='AI项目管理';

-- auto-generated definition
create table ai_model
(
    id                 int auto_increment
        primary key,
    project_id         int                                 not null comment '项目ID',
    model_name         varchar(200)                        not null comment '模型名称',
    training_framework varchar(200)                        not null comment '训练框架',
    service_type       varchar(200)                        not null comment '服务类型',
    `desc`             varchar(200)                        not null comment '描述',
    tenant_id          int                                 not null,
    create_user        varchar(50)                         not null,
    create_time        timestamp default CURRENT_TIMESTAMP not null,
    update_user        varchar(50)                         not null,
    update_time        timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment 'ai模型管理';

create table ai_model_version
(
    id            int auto_increment
        primary key,
    model_id      int                                 not null comment '模型ID',
    model_version varchar(200)                        not null comment '模型名称',
    model_type    int                                 not null comment '模型类型: 0本地，1离线',
    model_file    varchar(500)                        not null comment '模型文件，本地和离线都是一个文件',
    `desc`        varchar(200)                        not null comment '描述',
    tenant_id     int                                 not null,
    create_user   varchar(50)                         not null,
    create_time   timestamp default CURRENT_TIMESTAMP not null,
    update_user   varchar(50)                         not null,
    update_time   timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment 'ai模型版本管理';

create table ai_model_push
(
    id                int auto_increment
        primary key,
    model_id          int                                 not null comment '模型ID',
    version_id        int                                 not null comment '版本ID',
    mirror_id         int                                 not null comment '镜像ID',
    resource_cpu      int                                 not null comment '内存申请',
    resource_gpu      int                                 not null comment 'GPU申请',
    resource_memory   int                                 not null comment '内存申请',
    min_replica       int                                 not null comment '最小副本',
    max_replica       int                                 not null comment '内存申请',
    hpa               varchar(300)                        not null comment '弹性缩容',
    canary            int                                 not null comment '是否灰度发布：0否，1是',
    canary_weight     int                                 not null comment '灰度流量',
    model_config_list varchar(2000)                       not null comment '推理配置',
    command           varchar(2000)                       not null comment '启动命令',
    health_check      varchar(2000)                       not null comment '健康检查',
    tenant_id         int                                 not null,
    create_user       varchar(50)                         not null,
    create_time       timestamp default CURRENT_TIMESTAMP not null,
    update_user       varchar(50)                         not null,
    update_time       timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment 'ai模型推送';


