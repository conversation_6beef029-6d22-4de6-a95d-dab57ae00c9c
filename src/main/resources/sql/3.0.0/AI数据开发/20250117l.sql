alter table ai_mirror add column mirror_addr varchar(1024) comment '镜像地址';
alter table ai_mirror add column add_type varchar(2) default '1' comment '添加方式 1-上传镜像 2-镜像引入';
alter table ai_mirror add column publish_status varchar(2)  default '0' comment '发布状态 0-未发布 1-已发布';

alter table ai_model_push add column cfg_file text comment '配置文件';
alter table ai_model_push add column work_dir text comment '工作目录';
alter table ai_model_push add column env_var text comment '环境变量';
alter table ai_model_push add column service_port varchar(32) comment '服务端口';
alter table ai_model_push add column url_prefix varchar(32) comment 'URL前缀';

alter table ai_model add column framework_name varchar(512)  comment '框架名称';


alter table ai_model_version modify `desc` varchar(200) null comment '描述';
alter table ai_model_version add push_status tinyint default 0 not null comment '发布状态：0未发布，1发布，2灰度发布' after tenant_id;




