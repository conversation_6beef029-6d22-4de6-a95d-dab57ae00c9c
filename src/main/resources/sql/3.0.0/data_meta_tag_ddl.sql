CREATE TABLE data_meta_tag_type (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` VARCHAR(255) NOT NULL COMMENT '标签类型',
    `abbreviation` VARCHAR(255) NOT NULL COMMENT '英文缩写',
    `is_quick_tag` INT NOT NULL DEFAULT 0 COMMENT '是否为快捷标签（0表示否，1表示是）',
    `description` TEXT COMMENT '描述',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
    `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='元数据标签类型';

CREATE TABLE data_meta_tag (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` VARCHAR(255) NOT NULL COMMENT '标签名称',
    `abbreviation` VARCHAR(255) NOT NULL COMMENT '英文缩写',
    `description` TEXT COMMENT '描述',
    `tag_type_id` bigint(20) NOT NULL COMMENT '标签类型ID',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
    `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='元数据标签值';

CREATE TABLE `data_meta_tag_master` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tag_id` bigint(20) NOT NULL COMMENT '标签值id',
  `element_id` bigint(20) NOT NULL COMMENT '元数据ID',
  `item_type` varchar(64) DEFAULT NULL COMMENT '类型 table-表  field-字段',
  `datasource_type` varchar(64) DEFAULT NULL COMMENT '数据源类型',
  `database_name` varchar(128) DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(255) DEFAULT NULL COMMENT '表名称',
  `field_name` varchar(100) DEFAULT NULL COMMENT '字段名称',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8 COMMENT='元数据-标签关联表';