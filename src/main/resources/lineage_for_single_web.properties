# èç¹ip
NODE_IP=************
# èç¹id
NODE_ID=soar-logmodule
# æå¤§å¤ççº¿ç¨
max_process_thread=1
# æå¤§åçº¿ç¨ï¼å¦æè¾åºä¸ºæä»¶ï¼åçº¿ç¨å¿é¡»ä¸º1ï¼å ä¸ºå¤çº¿ç¨æåµä¸åºå±æä»¶IOæ¯åä¸ä¸ªã
max_write_thread=1
# è¯»åéåç¼å­å¤§å°
max_queue_size=5000
# mysqlçurl
MYSQL_URL=*******************************************************************************************************************************
# mysqlçç¨æ·å
MYSQL_USER=root
# mysqlå¯ç 
MYSQL_PASS=3hd2eV-^MNSrtQ06
# soarééåå¥è¡¨
# MYSQL_TABLE=soar_log
# soarééåå¥è¡¨å·ä½å­æ®µï¼å¿½ç¥id ä½¿ç¨ä¸»é®èªå¢ï¼ ç®ååºå® ä¸éè¦å
# MYSQL_TABLE_COLUMN=flow_id,src_device_type,data_source,occur_time,end_time,log_id,log_name,log_level,log_type,src_ip,src_mac,src_port,src_country,src_province,src_city,dst_ip,dst_mac,dst_port,dst_url,dst_domain,dst_country,dst_province,dst_city,threaten_type,agreement,method,status_code,email,username
# å¯å¨ç«¯å£
PORT=18860
# udpééç«¯å£
SYSLOG_UDP_PORT=9510

clickhouse_url=**********************************************
clickhouse_username=default
clickhouse_password=ugOZymrL54TawiV4

KAFKA_SERVER=************:9092